@import '~@uni/commons/src/style/variables.less';

.dmr-main-container .department-transfer-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  min-height: 30px;

  .tags-container {
    width: 100%;
    height: 100%;
    min-height: 30px;
    cursor: pointer;
    align-items: center;
    display: flex;

    .rc-overflow {
      width: 100%;
      align-items: center;
      display: flex;
      flex-direction: row;
    }

    .ant-tooltip {
      max-width: fit-content !important;
    }
  }

  .ant-space-item {
    white-space: nowrap;
  }

  .ant-select-selector {
    background: transparent !important;
    color: #000000d9 !important;
  }

  .ant-select-selection-item {
    color: #000000d9 !important;
  }

  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
    .ant-select-selector,
  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
    .ant-select-selector
    input {
    cursor: pointer;
  }
}

.dmr-main-container .department-transfer-modal-container {
  top: 150px;

  .ant-modal-close {
    display: none;
  }

  .ant-modal-body {
    padding: 12px 12px;
    height: 400px;
  }

  table {
    position: relative;
  }

  .ant-table-body {
    min-height: 330px;
  }

  .department-transfer-input {
    border: none;
    background: transparent;
    box-shadow: none;

    .ant-input-number-input {
      background-color: transparent;
      border: none;
      outline: none;
    }
  }

  .department-transfer-add-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    padding: 8px 8px;

    span {
      margin-right: 10px;
    }
  }

  .compact-date-container {
    border-bottom: none !important;
  }

  //.ant-modal-footer {
  //  display: none;
  //}

  .tr-border-active {
    background-color: #ffffff !important;

    td {
      background-color: #ffffff !important;
    }
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    border-bottom-color: #f0f0f0 !important;
  }

  .ant-table.ant-table-small .ant-table-tbody > tr > td .ant-form-item {
    margin-block: 2px !important;
  }
}
