import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import {
  Card,
  Col,
  Row,
  Tabs,
  Divider,
  Input,
  Space,
  Button,
  TableProps,
  Tooltip,
} from 'antd';
import _ from 'lodash';
import CustomGradientChart from '@/pages/drg/components/customGradientChart';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import UniTable from '@uni/components/src/table';
import CardWithBtns from '@uni/components/src/cardWithBtns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { IReducer, ITableState } from '@uni/reducers/src/interface';
import {
  InitTableState,
  TableAction,
  tableReducer,
} from '@uni/reducers/src/tableReducer';
import IconBtn from '@uni/components/src/iconBtn/index';
import ProFormContainer from '@uni/components/src/pro-form-container/index';
import { ProForm, ProFormInstance } from '@uni/components/src/pro-form/index';
import dayjs from 'dayjs';
import { ExportIconBtn } from '@uni/components/src/index';
import './index.less';
import { isEmptyValues } from '@uni/utils/src/utils';
import { PlusOutlined, RedoOutlined } from '@ant-design/icons';
import React, {
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from 'react';
import { Link, useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { useDebounce, useDeepCompareEffect } from 'ahooks';
import {
  TableColumns,
  BasePageProps,
  RespVO,
} from '@uni/commons/src/interfaces';
import { useModel } from '@@/plugin-model/useModel';
import { AmountStatisticsColumns } from './columns';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';

const canRefresh =
  (window as any).externalConfig?.['his']?.devtool?.refresh ?? false;
const canEditColumn =
  (window as any).externalConfig?.['his']?.devtool?.editColumn ?? false;

const AmountStatistics = () => {
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster');

  const [activeKey, setActiveKey] = useState('cliDept');

  // clidept table
  const [MainTableState, MainTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, { ...InitTableState });

  // clidept api
  const { data: cliDeptColumns, run: appealTaskStatsByCliDeptColumnsReq } =
    useRequest(
      () => {
        return uniCommonService(
          'Api/CenterSettle/CenterSettleAppealReport/GetSpecialCaseAppealTaskStatsByCliDept',
          {
            method: 'POST',
            headers: {
              'Retrieve-Column-Definitions': 1,
            },
          },
        );
      },
      {
        //   manual: true,
        formatResult: (res: RespVO<TableColumns>) => {
          if (res.code === 0) {
            return res.data.Columns;
          }
        },
      },
    );

  useEffect(() => {
    if (searchParams && dictData && cliDeptColumns?.length > 0) {
      MainTableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: tableColumnBaseProcessor(
            AmountStatisticsColumns(searchParams, dictData),
            cliDeptColumns,
          ),
        },
      });
    }
  }, [searchParams, dictData, cliDeptColumns]);

  const {
    loading: appealTaskStatsByCliDeptLoading,
    run: appealTaskStatsByCliDeptReq,
  } = useRequest(
    (data) => {
      return uniCommonService(
        'Api/CenterSettle/CenterSettleAppealReport/GetSpecialCaseAppealTaskStatsByCliDept',
        {
          method: 'POST',
          data: {
            ...data,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
        return null;
      },
      onSuccess: (data, params) => {
        if (data) {
          // data + pagi
          MainTableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: data ?? [],
            },
          });
          // sorter
          if (!_.isEqual(params?.at(2), MainTableState.sorter)) {
            MainTableDispatch({
              type: TableAction.sortChange,
              payload: { sorter: params?.at(2) },
            });
          }
        }
      },
    },
  );

  // drgcode table
  const [DrgCodeTableState, DrgCodeTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, { ...InitTableState });

  // drgcode api
  const { data: drgCodeColumns, run: appealTaskStatsByDrgCodeColumnsReq } =
    useRequest(
      () => {
        return uniCommonService(
          'Api/CenterSettle/CenterSettleAppealReport/GetSpecialCaseAppealTaskStatsByChsDrg',
          {
            method: 'POST',
            headers: {
              'Retrieve-Column-Definitions': 1,
            },
          },
        );
      },
      {
        //   manual: true,
        formatResult: (res: RespVO<TableColumns>) => {
          if (res.code === 0) {
            return res.data.Columns;
          }
        },
      },
    );

  useEffect(() => {
    if (searchParams && dictData && drgCodeColumns?.length > 0) {
      DrgCodeTableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: tableColumnBaseProcessor(
            AmountStatisticsColumns(searchParams, dictData),
            drgCodeColumns,
          ),
        },
      });
    }
  }, [searchParams, dictData, drgCodeColumns]);

  const {
    loading: appealTaskStatsByDrgCodeLoading,
    run: appealTaskStatsByDrgCodeReq,
  } = useRequest(
    (data) => {
      return uniCommonService(
        'Api/CenterSettle/CenterSettleAppealReport/GetSpecialCaseAppealTaskStatsByChsDrg',
        {
          method: 'POST',
          data: {
            ...data,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
        return null;
      },
      onSuccess: (data, params) => {
        if (data) {
          // data + pagi
          DrgCodeTableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: data ?? [],
            },
          });
          // sorter
          if (!_.isEqual(params?.at(2), DrgCodeTableState.sorter)) {
            DrgCodeTableDispatch({
              type: TableAction.sortChange,
              payload: { sorter: params?.at(2) },
            });
          }
        }
      },
    },
  );

  useDeepCompareEffect(() => {
    if (searchParams && searchParams?.dateRange?.length > 0) {
      console.log('inininininin');
      if (activeKey === 'cliDept') {
        appealTaskStatsByCliDeptReq({
          Sdate: searchParams?.dateRange[0],
          Edate: searchParams?.dateRange[1],
          HospCode: searchParams?.hospCodes,
          InsurTypes: searchParams?.insurType,
        });
      } else {
        appealTaskStatsByDrgCodeReq({
          Sdate: searchParams?.dateRange[0],
          Edate: searchParams?.dateRange[1],
          HospCode: searchParams?.hospCodes,
          InsurTypes: searchParams?.insurType,
        });
      }
    }
  }, [searchParams, activeKey]);

  const tabItems = [
    {
      label: '按科室统计',
      key: 'cliDept',
      children: (
        <>
          <UniTable
            id={'appeal-task-stats-by-cliDept'}
            rowKey={'CliDept'}
            dictionaryData={dictData}
            scroll={{ x: 'max-content' }}
            loading={appealTaskStatsByCliDeptLoading}
            columns={MainTableState.columns}
            dataSource={MainTableState.data}
            toolBarRender={null}
          />
        </>
      ),
    },
    {
      label: '按病组统计',
      key: 'drgCode',
      children: (
        <>
          <UniTable
            id={'appeal-task-stats-by-drgcode'}
            rowKey={'ChsDrgCode'}
            dictionaryData={dictData}
            scroll={{ x: 'max-content' }}
            loading={appealTaskStatsByDrgCodeLoading}
            columns={DrgCodeTableState.columns}
            dataSource={DrgCodeTableState.data}
            toolBarRender={null}
          />
        </>
      ),
    },
  ];

  return (
    <Card>
      <Tabs
        items={tabItems}
        activeKey={activeKey}
        onChange={(activeKeys) => {
          setActiveKey(activeKeys);
        }}
        tabBarExtraContent={
          <Space>
            <Divider type="vertical" />
            {canRefresh && (
              <Tooltip title="刷新">
                <Button
                  type={'text'}
                  shape={'default'}
                  icon={<RedoOutlined />}
                  onClick={() => {
                    if (activeKey === 'cliDept') {
                      appealTaskStatsByCliDeptReq({
                        Sdate: searchParams?.dateRange[0],
                        Edate: searchParams?.dateRange[1],
                        HospCode: searchParams?.hospCodes,
                        InsurTypes: searchParams?.insurType,
                      });
                    } else {
                      appealTaskStatsByDrgCodeReq({
                        Sdate: searchParams?.dateRange[0],
                        Edate: searchParams?.dateRange[1],
                        HospCode: searchParams?.hospCodes,
                        InsurTypes: searchParams?.insurType,
                      });
                    }
                  }}
                ></Button>
              </Tooltip>
            )}
            <TableColumnEditButton
              {...{
                columnInterfaceUrl:
                  activeKey === 'cliDept'
                    ? 'Api/CenterSettle/CenterSettleAppealReport/GetSpecialCaseAppealTaskStatsByCliDept'
                    : 'Api/CenterSettle/CenterSettleAppealReport/GetSpecialCaseAppealTaskStatsByChsDrg',
                onTableRowSaveSuccess: (newColumns) => {
                  if (activeKey === 'cliDept') {
                    MainTableDispatch({
                      type: TableAction.columnsChange,
                      payload: {
                        columns: tableColumnBaseProcessor(
                          AmountStatisticsColumns(searchParams, dictData),
                          newColumns,
                        ),
                      },
                    });
                  } else {
                    DrgCodeTableDispatch({
                      type: TableAction.columnsChange,
                      payload: {
                        columns: tableColumnBaseProcessor(
                          AmountStatisticsColumns(searchParams, dictData),
                          newColumns,
                        ),
                      },
                    });
                  }
                },
              }}
            />
          </Space>
        }
      />
      <DetailTableModal dictData={dictData} noDetailBtn />
    </Card>
  );
};

export default AmountStatistics;
