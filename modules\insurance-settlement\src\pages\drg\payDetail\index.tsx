import React, { useState } from 'react';
import { Tabs, Modal, Radio, message } from 'antd';
import { Emitter } from '@uni/utils/src/emitter';
import SearchResult from './components/searchResult';
import ImportRecord from './components/importRecord';
import { PayDetailEventConstants } from './constants';
import { uniCommonService } from '@uni/services/src/commonService';
import { downloadFile, UseDispostionEnum } from '@uni/utils/src/download';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';

import './index.less';

export interface IPayDetailProps {}

const PayDetail: React.FunctionComponent<IPayDetailProps> = (props) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  const items = [
    {
      key: 'searchResult',
      label: 'DRG支付明细查询',
      children: <SearchResult />,
    },
    {
      key: 'importRecord',
      label: '上报记录',
      children: <ImportRecord />,
    },
  ];

  return (
    <>
      <Tabs
        defaultActiveKey="searchResult"
        items={items}
        destroyInactiveTabPane
      />
    </>
  );
};

export default PayDetail;
