import React, {
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from 'react';
import { Link, useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { useDebounce } from 'ahooks';
import {
  TableColumns,
  BasePageProps,
  RespVO,
} from '@uni/commons/src/interfaces';
import { useModel } from '@@/plugin-model/useModel';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import {
  Card,
  Col,
  Row,
  Tabs,
  Divider,
  Input,
  Space,
  Button,
  TableProps,
  message,
  Dropdown,
  MenuProps,
  Modal,
  Radio,
} from 'antd';
import _ from 'lodash';
import CustomGradientChart from '@/pages/drg/components/customGradientChart';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import UniTable from '@uni/components/src/table';
import CardWithBtns from '@uni/components/src/cardWithBtns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { IReducer, ITableState } from '@uni/reducers/src/interface';
import {
  InitTableState,
  TableAction,
  tableReducer,
} from '@uni/reducers/src/tableReducer';
import IconBtn from '@uni/components/src/iconBtn/index';
import ProFormContainer from '@uni/components/src/pro-form-container/index';
import { ProForm, ProFormInstance } from '@uni/components/src/pro-form/index';
import { SpecialDiseaseDiscussionFormItems } from './formItems';
import dayjs from 'dayjs';
import { ExportIconBtn } from '@uni/components/src/index';
import './index.less';
import { isEmptyValues } from '@uni/utils/src/utils';
import DrawerSpecialDiseaseDiscussion from '../../components/drawerFormInsur/index';
import {
  DownloadOutlined,
  PlusOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { SpecialDiseaseDiscussionColumns } from './columns';
import AppealForm from '../../components/appealForm/index';
import { SpecialDiseaseDiscussionEventConstants } from './constants';
import { downloadFile, UseDispostionEnum } from '@uni/utils/src/download';

interface ISpecialDiseaseDiscussionProps {}

// hack 获取 sessionStorage
const getSessionStorage = (key: string) => {
  return JSON.parse(sessionStorage.getItem(key) as string);
};

const SpecialDiseaseManager = (props: ISpecialDiseaseDiscussionProps) => {
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster');

  const [searchedValue, setSearchedValue] = useState(
    !isEmptyValues(searchParams)
      ? {
          ..._.omit(searchParams, 'hospCode', 'hospCodes'),
          Sdate: searchParams?.dateRange
            ? dayjs(searchParams?.dateRange[0])
                .startOf('M')
                .format('YYYY-MM-DD')
            : undefined,
          Edate: searchParams?.dateRange
            ? dayjs(searchParams?.dateRange[1]).endOf('M').format('YYYY-MM-DD')
            : undefined,
          HospCode: searchParams?.hospCodes,
        }
      : {
          ..._.omit(getSessionStorage('searchOpts'), 'hospCode', 'hospCodes'),
          Sdate: getSessionStorage('searchOpts')?.dateRange
            ? dayjs(getSessionStorage('searchOpts')?.dateRange[0])
                .startOf('M')
                .format('YYYY-MM-DD')
            : undefined,
          Edate: getSessionStorage('searchOpts')?.dateRange
            ? dayjs(getSessionStorage('searchOpts')?.dateRange[1])
                .endOf('M')
                .format('YYYY-MM-DD')
            : undefined,
          HospCode: getSessionStorage('searchOpts')?.hospCodes,
        },
  );

  // left form items
  const SearchFormItems = useMemo(() => {
    console.log('useMemo', searchedValue, searchParams);
    return SpecialDiseaseDiscussionFormItems(searchedValue, dictData);
  }, [dictData, searchedValue]);

  const [proForm] = ProForm.useForm<ProFormInstance>();

  // drawer for insur
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [pageType, setPageType] = useState(undefined);

  // 添加状态来跟踪选中的通道类型
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);
  // 添加状态控制模态框显示
  const [modalVisible, setModalVisible] = useState(false);
  // 添加状态记录当前操作类型
  const [currentAction, setCurrentAction] = useState<'download' | 'import'>(
    'download',
  );

  // 主table
  const [MainTableState, MainTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, { ...InitTableState });

  const { run: appealTaskColumnsReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/CenterSettle/CenterSettleAppeal/GetSpecialCaseAppealTasks',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      //   manual: true,
      formatResult: (res: RespVO<TableColumns>) => {
        if (res.code === 0) {
          MainTableDispatch({
            type: TableAction.columnsChange,
            payload: {
              columns: tableColumnBaseProcessor(
                SpecialDiseaseDiscussionColumns,
                res?.data?.Columns,
              ),
            },
          });
        }
      },
    },
  );

  const { loading: appealTaskDataLoading, run: appealTaskDataReq } = useRequest(
    (data, pagi, sorter = null) => {
      return uniCommonService(
        'Api/CenterSettle/CenterSettleAppeal/GetSpecialCaseAppealTasks',
        {
          method: 'POST',
          data: {
            DtParam: {
              Draw: 1,
              Start: (pagi.cur - 1) * pagi.size,
              Length: pagi.size,
            },
            ...data,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
        return null;
      },
      onSuccess: (data, params) => {
        if (data) {
          // data + pagi
          MainTableDispatch({
            type: TableAction.dataPagiChange,
            payload: {
              data: data?.data ?? [],
              backPagination: {
                ...MainTableState.backPagination,
                current: params?.at(1)?.cur,
                pageSize: params?.at(1)?.size,
                total: data?.recordsFiltered ?? 0,
              },
            },
          });
          // sorter
          if (!_.isEqual(params?.at(2), MainTableState.sorter)) {
            MainTableDispatch({
              type: TableAction.sortChange,
              payload: { sorter: params?.at(2) },
            });
          }
        }
      },
    },
  );

  // 查询调接口
  useEffect(() => {
    if (searchedValue?.Sdate || searchedValue?.SettleSdate) {
      appealTaskDataReq(
        {
          ...searchedValue,
        },
        {
          cur: 1,
          size:
            MainTableState.backPagination?.pageSize ??
            MainTableState.backPagination?.defaultPageSize,
        },
      );
    }
  }, [searchedValue]);

  const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    appealTaskDataReq(
      {
        ...searchedValue,
      },
      { cur: pagi.current, size: pagi.pageSize },
    );
  };

  //
  const handleInsurDetailDrawerClk = (type) => {
    setDrawerVisible(true);
    setPageType(type);
  };

  // 删除api
  const { loading: appealTaskDeleteLoading, run: appealTaskDeleteReq } =
    useRequest(
      (taskId) => {
        return uniCommonService(
          'Api/CenterSettle/CenterSettleAppeal/DeleteSpecialCaseAppealTask',
          {
            method: 'POST',
            data: {
              TaskId: taskId,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (res) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            message.success('删除成功');
            appealTaskDataReq(
              {
                ...searchedValue,
              },
              {
                cur: MainTableState.backPagination?.current,
                size:
                  MainTableState.backPagination?.pageSize ??
                  MainTableState.backPagination?.defaultPageSize,
              },
            );
            return res.data;
          }
          return null;
        },
      },
    );
  // emitter
  useEffect(() => {
    // delete
    Emitter.on(SpecialDiseaseDiscussionEventConstants.DELETE_CLK, (record) => {
      appealTaskDeleteReq(record.TaskId);
    });

    Emitter.on(
      SpecialDiseaseDiscussionEventConstants.EDIT_APPEAL_CONFIRM_CLK,
      () => {
        appealTaskDataReq(
          {
            ...searchedValue,
          },
          {
            cur: MainTableState.backPagination?.current,
            size:
              MainTableState.backPagination?.pageSize ??
              MainTableState.backPagination?.defaultPageSize,
          },
        );
      },
    );

    return () => {
      Emitter.off(SpecialDiseaseDiscussionEventConstants.DELETE_CLK);
      Emitter.off(
        SpecialDiseaseDiscussionEventConstants.EDIT_APPEAL_CONFIRM_CLK,
      );
    };
  }, [searchedValue, MainTableState.backPagination]);

  // 下载导入模板
  const { run: downloadTemplateReq } = useRequest(
    (appealChannel) => {
      return uniCommonService(
        'Api/CenterSettle/AppealTaskImport/GetSpecialCaseImportTemplate',
        {
          method: 'POST',
          data: {
            AppealChannel: appealChannel,
          },
          responseType: 'blob',
        },
      );
    },
    {
      manual: true,
      formatResult: (response) => {
        return response;
      },
      onSuccess: (data, params) => {
        if (data.code === 0 && data.statusCode === 200) {
          message.success('Excel模板导出成功');
          console.log();
          let exportName =
            params?.[0] === 'NormalChannel' ? '特病单议-普通' : '特病单议-绿色';
          downloadFile(
            exportName,
            data?.response,
            UseDispostionEnum.combineWithNameFront,
          );
        } else {
          message.success('Excel模板导出失败，请联系管理员');
        }
      },
    },
  );

  const handleDownloadTemplate = (appealChannel) => {
    downloadTemplateReq(appealChannel);
  };

  // 导入文件
  const { run: importFileReq } = useRequest(
    (params: { file: File; appealChannel: string }) => {
      const formData = new FormData();
      formData.append('File', params.file);
      formData.append('AppealChannel', params.appealChannel);

      return uniCommonService(
        'Api/CenterSettle/AppealTaskImport/ImportSpecialCase',
        {
          method: 'POST',
          data: formData,
          requestType: 'form',
        },
      );
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res.code === 0 && res.statusCode === 200) {
          return 'success';
        } else {
          return 'failed';
        }
      },
      onSuccess: (data) => {
        if (data === 'success') {
          message.success('导入成功');
          // 刷新表格数据
          appealTaskDataReq(
            {
              ...searchedValue,
            },
            {
              cur: MainTableState.backPagination?.current,
              size:
                MainTableState.backPagination?.pageSize ??
                MainTableState.backPagination?.defaultPageSize,
            },
          );
        } else {
          message.error(data?.message || '导入失败');
        }
      },
    },
  );

  // 显示选择通道类型的模态框
  const showChannelSelectModal = (action: 'download' | 'import') => {
    // 重置选中的通道
    setSelectedChannel(null);
    setCurrentAction(action);
    setModalVisible(true);
  };

  // 处理确认按钮点击
  const handleModalOk = () => {
    if (!selectedChannel) {
      message.warning('请先选择通道类型');
      return;
    }

    if (currentAction === 'download') {
      handleDownloadTemplate(selectedChannel);
    } else {
      // 创建一个隐藏的上传组件并触发点击
      const uploadInput = document.createElement('input');
      uploadInput.type = 'file';
      uploadInput.accept = '.xlsx';
      uploadInput.style.display = 'none';
      uploadInput.onchange = (e) => {
        const file = e.target.files?.[0];
        if (file) {
          importFileReq({
            file,
            appealChannel: selectedChannel,
          });
        }
        // 移除临时创建的元素
        document.body.removeChild(uploadInput);
      };
      document.body.appendChild(uploadInput);
      uploadInput.click();
    }
    setModalVisible(false);
  };

  // 处理取消按钮点击
  const handleModalCancel = () => {
    setModalVisible(false);
  };

  return (
    <Row
      wrap={false}
      gutter={8}
      style={{ overflow: 'hidden' }}
      className="special_disease_discussion_container"
    >
      <Col flex="330px">
        <Card
          title="查询条件"
          className="special_disease_discussion_search_card"
        >
          <ProFormContainer
            className={'search_form noMarginBottom'}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            preserve={false}
            form={proForm}
            grid
            searchOpts={SearchFormItems}
            submitter={{
              render: (props, doms) => {
                return [
                  <Button
                    type="primary"
                    style={{
                      width: '100%',
                      marginTop: '8px',
                      borderRadius: 0,
                    }}
                    key="submit"
                    onClick={() => {
                      props.form?.submit?.();
                    }}
                  >
                    查询
                  </Button>,
                ];
              },
            }}
            onFinish={async (values) => {
              let result = {
                ..._.omit(values, ['customDateType', 'Sdate', 'Edate']),
                ...{
                  [values.customDateType === 'ByOutDate'
                    ? 'Sdate'
                    : 'SettleSdate']: values?.Sdate,
                  [values.customDateType === 'ByOutDate'
                    ? 'Edate'
                    : 'SettleEdate']: values?.Edate,
                },
              };
              console.log('onFinish', result);

              setSearchedValue(result);
              setQiankunGlobalState({
                dictData: dictData,
                searchParams: {
                  ...searchParams,
                  ..._.omit(values, ['customDateType', 'Sdate', 'Edate']),
                  dateRange: [values.Sdate, values.Edate],
                  hospCodes: values?.HospCode,
                },
              });
            }}
          />
        </Card>
      </Col>
      <Col flex="auto">
        <Card
          title="特病单议病例管理"
          extra={
            <Space>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'download',
                      label: (
                        // <div style={{ padding: '6px' }}>下载导入用模板</div>
                        <Button
                          key="getTemplate"
                          type="text"
                          icon={<DownloadOutlined />}
                          onClick={(e) => {
                            showChannelSelectModal('download');
                            // Emitter.emit(
                            //   SpecialDiseaseDiscussionEventConstants.SHOW_TEMPLATE_MODAL,
                            //   'download',
                            // );
                          }}
                        >
                          模板
                        </Button>
                      ),
                      // onClick: () => {
                      //   showChannelSelectModal('download');
                      // },
                    },
                    {
                      key: 'import',
                      label: (
                        <Button
                          key="getTemplate"
                          type="text"
                          icon={<UploadOutlined />}
                          onClick={(e) => {
                            showChannelSelectModal('import');
                            // Emitter.emit(
                            //   SpecialDiseaseDiscussionEventConstants.SHOW_TEMPLATE_MODAL,
                            //   'import',
                            // );
                          }}
                        >
                          导入
                        </Button>
                      ),
                      // onClick: () => {
                      //   showChannelSelectModal('import');
                      // },
                    },
                  ],
                }}
              >
                <Button>导入</Button>
              </Dropdown>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'center',
                      label: (
                        <div style={{ padding: '6px' }}>通过医保反馈明细</div>
                      ),
                      onClick: () => {
                        handleInsurDetailDrawerClk('center');
                      },
                    },
                    {
                      key: 'insur',
                      label: (
                        <div style={{ padding: '6px' }}>通过结算清单数据</div>
                      ),
                      onClick: () => {
                        handleInsurDetailDrawerClk('insur');
                      },
                    },
                  ],
                }}
              >
                <Button icon={<PlusOutlined />}>新增</Button>
              </Dropdown>
              <Divider type="vertical" />
              <ExportIconBtn
                isBackend={true}
                backendObj={{
                  url: 'Api/CenterSettle/CenterSettleAppeal/ExportGetSpecialCaseAppealTasks',
                  method: 'POST',
                  data: {
                    ...searchedValue,
                  },
                  fileName: '特病单议病例管理',
                }}
                btnDisabled={MainTableState?.data?.length < 1}
              />
              <TableColumnEditButton
                {...{
                  columnInterfaceUrl:
                    'Api/CenterSettle/CenterSettleAppeal/GetSpecialCaseAppealTasks',
                  onTableRowSaveSuccess: (newColumns) => {
                    MainTableDispatch({
                      type: TableAction.columnsChange,
                      payload: {
                        columns: tableColumnBaseProcessor(
                          SpecialDiseaseDiscussionColumns,
                          newColumns,
                        ),
                      },
                    });
                  },
                }}
              />
            </Space>
          }
        >
          <UniTable
            id={'special-disease-discussion-table'}
            rowKey={'IssueId'}
            dictionaryData={dictData}
            scroll={{ x: 'max-content' }}
            loading={appealTaskDataLoading}
            columns={MainTableState.columns}
            dataSource={MainTableState.data}
            pagination={MainTableState.backPagination}
            onChange={backTableOnChange}
            toolBarRender={null}
            isBackPagination
          />
        </Card>
        {/* 模板 */}
        <Modal
          title="模板内容"
          open={modalVisible}
          onOk={handleModalOk}
          onCancel={handleModalCancel}
        >
          <div>
            请选择模板类型：
            <Radio.Group
              onChange={(e) => setSelectedChannel(e.target.value)}
              value={selectedChannel}
            >
              {dictData?.CenterSettleAppealChannel?.map((item) => (
                <Radio key={item.Code} value={item.Code}>
                  {item.Name}
                </Radio>
              ))}
            </Radio.Group>
          </div>
        </Modal>
        {/* 通过医保反馈明细新增特病单议病例 */}
        {/* <DrawerSpecialDiseaseDiscussion
            visible={drawerVisibleCenter}
            pageType={'center'}
            onClose={() => {
              setDrawerVisibleCenter(false);
              appealTaskDataReq(
                {
                  ...searchedValue,
                },
                {
                  cur: 1,
                  size:
                    MainTableState.backPagination?.pageSize ??
                    MainTableState.backPagination?.defaultPageSize,
                },
              );
            }}
          /> */}
        {/* 通过结算清单数据新增特病单议病例 */}
        <DrawerSpecialDiseaseDiscussion
          visible={drawerVisible}
          pageType={pageType}
          onClose={() => {
            setDrawerVisible(false);
            setPageType(undefined);
            appealTaskDataReq(
              {
                ...searchedValue,
              },
              {
                cur: 1,
                size:
                  MainTableState.backPagination?.pageSize ??
                  MainTableState.backPagination?.defaultPageSize,
              },
            );
          }}
        />

        {/* 填写申诉内容 */}
        <AppealForm />
      </Col>
    </Row>
  );
};

export default SpecialDiseaseManager;
