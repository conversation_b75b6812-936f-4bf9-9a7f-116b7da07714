import {
  closestCenter,
  DndContext,
  MouseSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  horizontalListSortingStrategy,
  SortableContext,
  useSortable,
} from '@dnd-kit/sortable';
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers';
import { CSSProperties } from 'react';
import { Coordinates, CSS } from '@dnd-kit/utilities';
import { DragOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';

export const useDraggableTableHeader = (header: any) => {
  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useSortable({
      id: header.column.id,
    });

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: 'relative',
    transform: CSS.Translate.toString(transform), // translate instead of transform to avoid squishing
    transition: 'width transform 0.2s ease-in-out',
    whiteSpace: 'nowrap',
    width: header.column.getSize(),
    zIndex: isDragging ? 10000 : 0,
  };

  const dragHandler = () => {
    return (
      <div
        {...attributes}
        {...listeners}
        style={{ cursor: isDragging ? 'grabbing' : 'grab', margin: '0px 5px' }}
      >
        <Tooltip title={'明细列交换顺序'}>
          <DragOutlined />
        </Tooltip>
      </div>
    );
  };

  return {
    style: style,
    setNodeRef: setNodeRef,
    handler: dragHandler,
  };
};

export const useDraggableTableAlongCell = (cell: any) => {
  const { isDragging, setNodeRef, transform } = useSortable({
    id: cell.column.id,
  });

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: 'relative',
    transform: CSS.Translate.toString(transform), // translate instead of transform to avoid squishing
    transition: 'width transform 0.2s ease-in-out',
    width: cell.column.getSize(),
    zIndex: isDragging ? 1 : 0,
  };

  return {
    style: style,
    setNodeRef: setNodeRef,
  };
};

export const HeaderSorterContainer = (props: any) => {
  const sensors = useSensors(useSensor(MouseSensor, {}));

  return (
    <DndContext
      collisionDetection={closestCenter}
      modifiers={[restrictToHorizontalAxis]}
      onDragEnd={props?.onHeaderDragEnd}
      onDragMove={props?.onHeaderDragMove}
      onDragStart={props?.onHeaderDragStart}
      autoScroll={props?.autoScroll}
      sensors={sensors}
    >
      {props?.children}
    </DndContext>
  );
};

export const HeaderSortableContext = (props: any) => {
  return (
    <SortableContext
      items={props?.columnOrder}
      strategy={horizontalListSortingStrategy}
    >
      {props?.children}
    </SortableContext>
  );
};

export const CellSortableContext = (props: any) => {
  return (
    <SortableContext
      key={props?.cellId}
      items={props?.columnOrder}
      strategy={horizontalListSortingStrategy}
    >
      {props?.children}
    </SortableContext>
  );
};
