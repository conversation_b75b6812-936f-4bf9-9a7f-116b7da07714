import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import {
  arrayMoveImmutable,
  useRefFunction,
  RowEditableConfig,
} from '@ant-design/pro-utils';
import './UniDmrDragEditOnlyTable.less';
import {
  Dropdown,
  Form,
  Input,
  MenuProps,
  Table,
  TablePaginationConfig,
} from 'antd';
import {
  DndContext,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import omit from 'lodash/omit';
import { ProFormDependency } from '@ant-design/pro-components';
import { isEmptyValues } from '@uni/utils/src/utils';
import cloneDeep from 'lodash/cloneDeep';
import UniTableNG, { EmptyWrapper } from '../../tanstack-table';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import GridItemContext from '@uni/commons/src/grid-context';
import { PlusSquareTwoTone } from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';

/**
 * UniDmrDragEditOnlyTable
 * 仅用于 拖动编辑表格显示用
 * 后期可能会做修改
 * 这里有一个eventbus 监听
 * 用于后期可能存在的 保存功能 用
 */

const MainItemDataIndex = [
  'IcdeCode',
  'IcdeName',
  'OperCode',
  'OperName',
  'IcdeCond',
];

export interface FormTableItemBaseProps {
  value?: any;
  onChange?: any;
}

interface UniDmrDragEditOnlyTableProps {
  dmrTableContainerRef?: any;

  tableLayout?: 'auto' | 'fixed';
  itemRef?: any;
  form: any;
  id?: string;
  formKey?: string;
  loading?: boolean;
  dataSource?: any[];
  value?: any[];
  editable?: RowEditableConfig<any>;
  columns?: any[];
  tableId: string;
  scroll?: any;
  className?: string;
  rowKey: string;
  eventName?: string;
  controlled?: boolean;
  pagination?: false | TablePaginationConfig;
  onTableDataSourceOrderChange?: (
    tableData: any[],
    dataOrder?: any,
    focusId?: string,
  ) => void;
  recordCreatorProps?: boolean | any;
  onValuesChange?: (recordList: any[], changedValues?: any) => void;
  onChange?: (value: any[]) => void;
  rowClassName?: string | ((record, index) => string);
  bordered?: boolean;
  forceColumnsUpdate?: boolean;

  formItemContainerClassName?: string;
  // 允许拖动
  allowDragging?: boolean;

  onDragExtra?: (data: any[]) => void;
  onDragEndPre?: (active: any, over: any) => Promise<boolean>;

  // extra form item keys
  extraFormItemKeys?: string[];

  dictionaryData?: any;
  // row 能不能 被拖动 默认是ADD的不能拖
  canRowSortable?: (row: any) => boolean;

  enableRowSelection?: boolean;
}

export const getLineArrowUpDownEventKey = (formKey) => {
  return `${EventConstant.LINE_ARROW_UP_DOWN_PRESS}#${formKey}`;
};

export const FieldsetReadonly = (props: any) => {
  return <fieldset disabled={true}>{props?.children}</fieldset>;
};

const timescapeInputSizeToFormat = {
  3: 'YYYY-MM-DD',
  4: 'YYYY-MM-DD HH:00:00',
  5: 'YYYY-MM-DD HH:mm:00',
  6: 'YYYY-MM-DD HH:mm:ss',
};

export interface DmrDragEditOnlyTableContextItem {
  dmrTableContainerRef?: any;

  columnItemRefMapSetter?: (key: string, ref: any) => void;
  columnItemRefMapGetter?: (key: string) => any;
}

const DmrDragEditOnlyTableContext =
  createContext<DmrDragEditOnlyTableContextItem>({});

export const useDmrDragEditOnlyTableContext = () =>
  useContext(DmrDragEditOnlyTableContext);

const UniDmrDragEditOnlyTable = ({
  dmrTableContainerRef,

  itemRef,
  form,
  tableId,
  scroll,
  loading,
  pagination,
  className,
  columns,
  dataSource,
  rowKey,
  controlled,
  onTableDataSourceOrderChange,
  recordCreatorProps,
  onValuesChange,
  onChange,
  rowClassName,
  bordered,
  forceColumnsUpdate,
  tableLayout,
  editable,
  formItemContainerClassName,
  allowDragging = true,
  formKey,
  onDragExtra,
  onDragEndPre,
  extraFormItemKeys,
  dictionaryData,
  canRowSortable,
  enableRowSelection,
  ...restProps
}: UniDmrDragEditOnlyTableProps) => {
  const columnItemRefMap = useRef<any>({});

  const selfRef = React.useRef<any>();

  const gridItemContext = React.useContext(GridItemContext);

  console.log('GridItemContext', gridItemContext);

  const [tableAutoFixedClassName, setTableAutoFixedClassName] =
    useState<string>('');

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // https://docs.dndkit.com/api-documentation/sensors/pointer#activation-constraints
        distance: 1,
      },
    }),
  );

  const items: any[] = [
    {
      icon: <PlusSquareTwoTone />,
      label: '添加批注',
      key: 'COMMENT_ADD',
      enable: (extra: any) => {
        return !isEmptyValues(extra?.detailCommentRef);
      },
    },
    {
      icon: <PlusSquareTwoTone />,
      label: '添加批注',
      key: 'PRE_COMMENT_ADD',
      enable: (extra: any) => {
        return !isEmptyValues(extra?.dmrPreCardCommentContainerRef);
      },
    },
  ];

  const commentAddProps = (
    tableFormKey: string,
    dataIndex: string,
    index: number,
    title: string,
    record: any,
    isMissingItem = false,
  ) => {
    // TODO 费用？？？？ 表格？？？？？ 怎么来标识
    const menuItems =
      items
        ?.filter((item) => item?.enable(gridItemContext?.extra))
        ?.map((item) => {
          return omit(item, ['enable']);
        }) ?? [];
    return {
      key: `Dropdown-formItem#${tableFormKey}#${dataIndex}#${index}`,
      getPopupContainer: () => document.getElementById(tableFormKey),
      menu: {
        items: menuItems,
        onClick: (info: any) => {
          let formKey = `Table-${tableFormKey}-${dataIndex}-${
            record?.UniqueId ?? uuidv4()
          }`;
          if (
            // record?.IsMain === true &&
            index === 0 &&
            MainItemDataIndex?.includes(dataIndex)
          ) {
            formKey = `${formKey}-Main`;
          }

          if (isMissingItem === true) {
            formKey = `Table-${tableFormKey}-Missing`;
          }

          let currentDropdownOpenInputContainers = document.querySelectorAll(
            'div[class~=ant-dropdown-open] input',
          );

          // 时间输入
          let currentDropdownOpenIsTimescape = document.querySelector(
            'div[class~=ant-dropdown-open] .timescape',
          );

          // 只读
          let currentDropdownOpenIsIcdeOperReadonlyItem =
            document.querySelector(
              'div[class~=ant-dropdown-open] .icde-oper-readonly-item',
            );

          let currentItemValue = '';
          [].slice.call(currentDropdownOpenInputContainers)?.forEach((item) => {
            currentItemValue += item?.value;
          });

          if (!isEmptyValues(currentDropdownOpenIsTimescape)) {
            if (!isEmptyValues(currentItemValue)) {
              currentItemValue = dayjs(currentItemValue)?.format(
                timescapeInputSizeToFormat[
                  currentDropdownOpenInputContainers?.length
                ],
              );
            }
          }

          if (!isEmptyValues(currentDropdownOpenIsIcdeOperReadonlyItem)) {
            currentItemValue =
              currentDropdownOpenIsIcdeOperReadonlyItem?.textContent;
          }

          // 直接唤起 首页批注 右侧 菜单
          gridItemContext?.extra?.gridContainerRef?.current?.showDmrPreComment?.();

          setTimeout(() => {
            switch (info?.key) {
              case 'COMMENT_ADD':
                gridItemContext?.extra?.detailCommentRef?.current?.addComment({
                  formKey: formKey,
                  containerKey: formKey,
                  label: title,
                  OriginalInputValue:
                    isMissingItem === true
                      ? '数据遗漏'
                      : currentItemValue?.trim(),
                });
                break;
              case 'PRE_COMMENT_ADD':
                gridItemContext?.extra?.dmrPreCardCommentContainerRef?.current?.addComment(
                  {
                    formKey: formKey,
                    containerKey: formKey,
                    label: title,
                    OriginalInputValue:
                      isMissingItem === true
                        ? '数据遗漏'
                        : currentItemValue?.trim(),
                  },
                );
                break;
              default:
                break;
            }
          }, 200);
        },
      },
      trigger: ['contextMenu'],
      getPopupContainer: (triggerNode) =>
        document.getElementById('dmr-form-container'),
    };
  };

  useEffect(() => {
    let formValues = {};
    let dataOrder = {};
    dataSource
      ?.filter((item) => {
        if (item?.id === 'ADD') {
          return true;
        }

        if (gridItemContext?.needBlankLineInTable === false) {
          if (
            isEmptyValues(item?.['IcdeCode']) &&
            isEmptyValues(item?.['OperCode']) &&
            isEmptyValues(item?.['PathologyIcdeCode'])
          ) {
            return false;
          }
        }

        return true;
      })
      ?.forEach((item, index) => {
        if (item[rowKey] !== 'ADD') {
          formValues[item[rowKey]] = item;

          dataOrder[item[rowKey]] = index;
        }
      });

    formValues['dataOrder'] = dataOrder;

    console.log('dataSource form', formValues, dataSource);
    form.setFieldsValue(formValues);
  }, [dataSource]);

  const onDragStart = (event: any) => {
    // 从 ref Map 那里 拿出Column DataIndex 对应的所有ref
    Object.keys(columnItemRefMap?.current)?.forEach((key) => {
      if (columnItemRefMap?.current?.[key]?.current?.closeOption) {
        setTimeout(() => {
          columnItemRefMap?.current?.[key]?.current?.closeOption();
        }, 100);
        // columnItemRefMap?.current?.[key]?.current?.blur();
      }
    });
  };

  const onDragEnd = (event: any, newData: any, focusId: string) => {
    // 修改form dataOrder
    let newDataOrder = dataOrderProcessor(newData);
    form.setFieldValue('dataOrder', newDataOrder);

    // 同步 form value to newData
    let formValues = form?.getFieldsValue(true);
    newData = newData?.map((item) => {
      let formItemValue = formValues?.[item?.[rowKey]];
      if (formItemValue) {
        return {
          ...item,
          ...formItemValue,
        };
      }

      return item;
    });

    onTableDataSourceOrderChange &&
      onTableDataSourceOrderChange([...newData], newDataOrder, focusId);
  };

  const dataOrderProcessor = (data: any[]) => {
    let dataOrder = {};

    data?.forEach((item, index) => {
      dataOrder[item[rowKey]] = index;
    });

    return dataOrder;
  };

  const formCellProcessor = (columns: any[]) => {
    console.log('form values', form.getFieldsValue());

    return columns?.map((item: any) => {
      if (!isEmptyValues(item?.children)) {
        item?.children?.forEach((item) => {
          injectColumnFormItemRender(item);
        });
      } else {
        injectColumnFormItemRender(item);
      }

      return item;
    });
  };

  const injectColumnFormItemRender = (item) => {
    if (item?.onCell === undefined) {
      item['onCell'] = (record, rowIndex) => {
        return {
          record: record,
          rowIndex: rowIndex,
          rowKey: rowKey,
        };
      };
    }

    if (item?.renderColumnFormItem !== undefined) {
      let ItemWrapper = EmptyWrapper;
      let commentDropdown = false;
      if (
        gridItemContext?.extra?.enableGridItemComment === true &&
        item?.disableComment !== true
      ) {
        ItemWrapper = Dropdown;
        commentDropdown = true;
      }

      let ReadonlyWrapper = EmptyWrapper;
      if (item?.readonly === true && commentDropdown === false) {
        ReadonlyWrapper = FieldsetReadonly;
      }

      item['render'] = (text, record, rowIndex) => {
        let fieldDom = <></>;

        if (record[rowKey] === 'ADD') {
          fieldDom = (
            <ItemWrapper
              {...commentAddProps(
                formKey,
                item?.dataIndex,
                rowIndex,
                item?.title,
                record,
                true,
              )}
            >
              {item?.renderColumnFormItem(text, record, rowIndex)}
            </ItemWrapper>
          );
        } else {
          fieldDom = (
            <ReadonlyWrapper>
              <ItemWrapper
                {...commentAddProps(
                  formKey,
                  item?.dataIndex,
                  rowIndex,
                  item?.title,
                  record,
                )}
              >
                <Form.Item
                  name={[record[rowKey], item?.dataIndex]}
                  className={formItemContainerClassName ?? ''}
                  {...(item?.formFieldProps ?? {})}
                  itemId={`Table-${formKey}-${item?.dataIndex}-${
                    record?.UniqueId ?? uuidv4()
                  }`}
                >
                  {item?.renderColumnFormItem(
                    text,
                    record,
                    rowIndex,
                    item?.dataIndex,
                    form,
                    item,
                  )}
                </Form.Item>
              </ItemWrapper>
            </ReadonlyWrapper>
          );
        }

        return <>{fieldDom}</>;
      };
    }
  };

  const onFormValuesChange = (changedValues, values) => {
    console.log('onFormValuesChange', changedValues, values);

    let dataOrder = form.getFieldValue('dataOrder');
    let sortedValues = [];
    let dataValues = omit(values, [
      'ADD',
      'dataOrder',
      ...(extraFormItemKeys ?? []),
    ]);
    Object.keys(dataValues)?.map((key) => {
      let item = dataValues?.[key];
      item[rowKey] = key;
      item['sort'] = dataOrder[key] ?? Number.MAX_VALUE;
      sortedValues.push(item);
    });

    sortedValues = sortedValues?.sort((a, b) => a?.sort - b?.sort);

    onValuesChange && onValuesChange(sortedValues, changedValues);
  };

  const hiddenFormItem = () => {
    let hiddenDatIndex = columns
      ?.filter((item) => item?.visible === false)
      ?.map((item) => item?.dataIndex);
    return (
      <>
        {dataSource?.map((item) => {
          return hiddenDatIndex?.map((dataIndex) => {
            return (
              <Form.Item hidden={true} name={[item?.[rowKey], dataIndex]} />
            );
          });
        })}
      </>
    );
  };

  return (
    <DmrDragEditOnlyTableContext.Provider
      value={{
        dmrTableContainerRef: dmrTableContainerRef,
        columnItemRefMapSetter: (key: string, ref: any) => {
          columnItemRefMap.current[key] = ref;
        },
        columnItemRefMapGetter: (key: string) => {
          return columnItemRefMap?.current?.[key];
        },
      }}
    >
      <div
        ref={itemRef ?? selfRef}
        className={`uni-drag-edit-table-container ${
          gridItemContext?.extra?.enableGridItemComment === true
            ? 'grid-comment-custom'
            : ''
        }`}
      >
        <Form form={form} onValuesChange={onFormValuesChange}>
          <Form.Item hidden={true} name={'dataOrder'} />
          {extraFormItemKeys &&
            extraFormItemKeys?.map((key) => {
              return <Form.Item hidden={true} name={key} />;
            })}
          {hiddenFormItem()}
          <UniTableNG
            id={tableId || restProps?.id}
            formKey={formKey}
            size={'small'}
            rowClassName={rowClassName ?? ''}
            scroll={scroll}
            loading={loading || false}
            pagination={pagination || false}
            className={`${className} ${tableAutoFixedClassName}`}
            columns={formCellProcessor(columns)}
            dataSource={dataSource?.filter((item) => {
              if (item?.id === 'ADD') {
                return true;
              }

              if (gridItemContext?.needBlankLineInTable === false) {
                if (
                  isEmptyValues(item?.['IcdeCode']) &&
                  isEmptyValues(item?.['OperCode']) &&
                  isEmptyValues(item?.['PathologyIcdeCode'])
                ) {
                  return false;
                }
              }

              return true;
            })}
            rowKey={rowKey}
            forceColumnsUpdate={forceColumnsUpdate ?? false}
            enableDragging={true}
            bordered={bordered ?? true}
            clickable={false}
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
            onDragExtra={onDragExtra}
            onDragEndPre={onDragEndPre}
            dictionaryData={dictionaryData}
            canRowSortable={canRowSortable}
            rowBaseSort={true}
            enableRowSelection={enableRowSelection}
            autoScroll={{
              canScroll: (element) => {
                return (
                  element === document?.getElementById('dmr-content-container')
                );
              },
            }}
          />
        </Form>
      </div>
    </DmrDragEditOnlyTableContext.Provider>
  );
};

export default UniDmrDragEditOnlyTable;
