import { Tag } from 'antd';

const transferDictFormat = (dict) => {
  if (dict?.length) {
    return dict.reduce((acc, { Code, Name }) => {
      acc[Code] = { text: Name };
      return acc;
    }, {});
  } else return [];
};

export const BaseInfoColumns = (dict) => {
  return [
    {
      title: '病案号',
      key: 'text',
      dataIndex: 'PatNo',
    },
    {
      title: '姓名',
      key: 'text',
      dataIndex: 'PatName',
    },
    {
      title: '性别',
      key: 'text',
      dataIndex: 'PatSex',
      valueType: 'select',
      valueEnum: dict?.Dmr?.XB?.map((a) => a?.Name) || [],
    },
    {
      title: '年龄',
      key: 'text',
      dataIndex: 'PatAge',
    },
    {
      title: '医疗付费方式',
      span: 2,
      key: 'text',
      dataIndex: 'InsurType',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.InsurType),
    },
    {
      title: '离院方式',
      key: 'text',
      dataIndex: 'OutType',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.Dmr?.LYFS),
    },
    {
      title: '出院科室',
      key: 'text',
      dataIndex: 'OutDept',
      valueType: 'select',
      // 特殊处理
      valueEnum: transferDictFormat(
        dict?.CliDeptAndCaty?.length > 0
          ? dict?.CliDeptAndCaty
          : dict?.CliDepts,
      ),
    },
    {
      title: '出生日期',
      key: 'text',
      dataIndex: 'PatBirth',
      valueType: 'date',
    },
    {
      title: '（年龄不足1周岁）的年龄',
      key: 'text',
      dataIndex: 'BabyAge',
    },
    {
      title: '新生儿出生体重',
      key: 'text',
      dataIndex: 'BabyBw',
    },
    {
      title: '新生儿入院体重',
      key: 'text',
      dataIndex: 'BabyIw',
    },
    {
      title: '入院日期',
      key: 'text',
      dataIndex: 'InDate',
      valueType: 'date',
    },
    {
      title: '出院日期',
      key: 'text',
      dataIndex: 'OutDate',
      valueType: 'date',
    },
    {
      title: '住院天数',
      key: 'text',
      dataIndex: 'InPeriod',
    },
    {
      title: '科主任',
      key: 'text',
      dataIndex: 'Director',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.Employee),
    },
    {
      title: '主任(副主任)医师',
      key: 'text',
      dataIndex: 'Chief',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.Employee),
    },
    {
      title: '主治医师',
      key: 'text',
      dataIndex: 'Attending',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.Employee),
    },
  ];
};

export const ChsDrgResultColumns = (dict) => {
  return [
    // {
    //   title: '病例类型',
    //   key: 'text',
    //   dataIndex: 'AbnFeeType',
    //   valueType: 'select',
    //   valueEnum: dict?.AbnFeeType?.length ? dict?.AbnFeeType.map(a => a.Name) : [],
    // },
    {
      key: 'Cw',
      title: 'RW',
      dataIndex: 'Cw',
    },
    {
      key: 'CwRate',
      title: '费率',
      dataIndex: 'CwRate',
    },
    {
      title: '支付标准',
      key: 'CwValue',
      dataIndex: 'CwValue',
    },
    {
      title: '当前花费',
      key: 'TotalFee',
      dataIndex: 'TotalFee',
    },
    {
      title: '盈亏',
      key: 'Profit',
      dataIndex: 'Profit',
    },
    {
      title: '历史均费',
      key: 'AvgFee',
      dataIndex: 'AvgFee',
    },
    // {
    //   title: '地区平均值',
    //   key: 'text',
    //   dataIndex: 'AreaAvgFee',
    // },
  ];
};

export const ChsResultColumns = (type) => {
  return [
    {
      key: 'BaseCwPoint',
      title: '病种基准分值',
      dataIndex: 'BaseCwPoint',
    },
    {
      key: 'CwPointCoefficient',
      title: '重点专科病种调节系数',
      dataIndex: 'CwPointCoefficient',
    },
    {
      key: 'HospCoefficient',
      title: '医疗机构系数',
      dataIndex: 'HospCoefficient',
    },
    {
      key: 'HospPerfCoefficient',
      title: '机构考核系数',
      dataIndex: 'HospPerfCoefficient',
    },
    {
      key: 'TooHighOrLowRatio',
      title: '超高超低系数',
      dataIndex: 'TooHighOrLowRatio',
    },
    // BaseCwPoint ? CwPointCoefficient  ? HospCoefficient  ? HospPerfCoefficient  ? TooHighOrLowRatio = CwPoint (中间值)
    {
      key: 'CwPoint',
      title: '分值',
      dataIndex: 'CwPoint',
    },
    {
      key: 'CwRate',
      title: '单价',
      dataIndex: 'CwRate',
    },
    // CwPoint * CwRate = StdInsurPayment
    {
      title: `${type}支付金额`,
      key: 'StdInsurPayment',
      dataIndex: 'StdInsurPayment',
    },
    {
      title: '当前花费',
      key: 'TotalFee',
      dataIndex: 'TotalFee',
    },
    {
      title: '超支结余',
      key: 'Profit',
      dataIndex: 'Profit',
    },
    {
      title: '历史数据',
      key: 'AvgFee',
      dataIndex: 'AvgFee',
    },
  ];
};

export const FeeTypesColumns = [
  // {
  //   dataIndex: 'Code',
  //   title: '编码',
  //   visible: false,
  // },
  {
    dataIndex: 'FeeTypeName',
    title: '费用类型',
    visible: true,
  },
  {
    dataIndex: 'TotalFee',
    title: '当前费用',
    align: 'right',
    visible: true,
  },
  {
    dataIndex: 'AvgFee',
    title: '历史费用',
    align: 'right',
    visible: true,
  },
];
