import { useState } from 'react';
import { useModel, useRequest } from 'umi';
import { Col, Row, Tabs } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import '../index.less';
import Stats from '@/components/stats/index';
import { DifficultCaseNormalStat } from '@/constants';
import { RespVO } from '@uni/commons/src/interfaces';
import SingleColumnTable from '@/components/SingleColumnTable/index';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';

const DifficultCase = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, MedTeams } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // 这边调bundleData
  const {
    data: BundledData,
    loading: getBundledDataLoading,
    run: getBundledDataReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/v2/Drgs/MedTeamHighRw/BundledHighRw', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
      MedTeams,
    };
    setTableParams(params);
    getBundledDataReq(params);
  }, [dateRange, hospCodes, MedTeams]);

  let tabItems = [
    {
      key: 'statistic',
      label: '医疗组综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Drgs/MedTeamHighRw/BundledHighRw`}
              trendApi={`Api/v2/Drgs/MedTeamHighRw/HighRwTrend`}
              columns={DifficultCaseNormalStat}
              defaultSelectItem={'PatCnt'}
              type="col-xl-6"
              chartHeight={310}
              tabKey={activeKey}
              useGlobalState
              tableParams={tableParams}
            />
          </Col>
          <SingleColumnTable
            title="医疗组疑难病例"
            args={{
              api: `Api/v2/Drgs/MedTeamHighRw/HighRwByMedTeam`,
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="MedTeamName"
            type="table"
            colSpan={{ span: 24 }}
            visibleValueKeys={[
              'MedTeamName',
              'PatCnt',
              'PatRatio',
              'DrgCnt',
              'AvgInPeriod',
              'AvgTotalFee',
              'AvgMedicineFee',
              'MedicineFeeRatio',
              'AvgMaterialFee',
              'MaterialFeeRatio',
            ]}
            chart={{
              api: `Api/v2/Drgs/MedTeamHighRw/HighRwADrgComposition`,
              type: 'bar',
              valueKeys: ['PatCnt'],
              category: 'ADrgName',
              yAxis: '出院人次',
              colSpan: { span: 24 },
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName,
                args: {
                  ...tableParams,
                  MedTeams: [record?.MedTeam],
                  HighRw: true,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                dictData: dictData, // 传入
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'medTeam_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <SingleColumnTable
          title="医生疑难病例分布"
          args={{
            api: 'Api/v2/Drgs/DoctorHighRw/HighRwByDoctor',
          }}
          tableParams={tableParams}
          dictData={dictData}
          category="DoctorName"
          type="table"
          visibleValueKeys={[
            'DoctorName',
            // 'TotalCnt',
            'PatCnt',
            'PatRatio',
            'DrgCnt',
            'AvgInPeriod',
            'AvgTotalFee',
            'AvgMedicineFee',
            'MedicineFeeRatio',
            'AvgMaterialFee',
            'MaterialFeeRatio',
          ]}
          colSpan={{ span: 24 }}
          select={{
            dataKey: 'DoctorType',
            valueKey: 'DoctorType',
            allowClear: false,
            defaultSelect: true,
          }}
          //   chart={{
          //     api: 'Api/Drgs/MedTeamHighRw/HighRwADrgComposition',
          //     type: 'bar',
          //     valueKeys: ['PatCnt'],
          //     category: 'ADrgName',
          //     yAxis: 'TotalCnt',
          //   }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.DoctorName,
              args: {
                ...tableParams,
                DoctorCodes: [record?.DoctorCode],
                DoctorType: (record?.DoctorType as string)?.toLocaleUpperCase(),
                HighRw: true,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default DifficultCase;
