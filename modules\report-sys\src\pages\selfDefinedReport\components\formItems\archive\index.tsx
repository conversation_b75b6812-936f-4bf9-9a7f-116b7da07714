import {
  ProForm,
  ProFormInstance,
  ProFormText,
  ProFormSwitch,
  StepsForm,
  ProFormDependency,
  ProFormSelect,
  ProFormTextArea,
} from '@uni/components/src/pro-form';
import _ from 'lodash';
import {
  Col,
  Divider,
  Form,
  Input,
  Select,
  Space,
  Spin,
  Typography,
  message,
} from 'antd';
import { ArchiveType } from '@/pages/selfDefinedReport/constants';
import { useEventEmitter, useSafeState } from 'ahooks';
import BlobFileHandler from '@/components/BlobFileHandler';
import {
  BlobFileContentType,
  BlobFileTypeAccept,
} from '@/components/BlobFileHandler/constants';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';

const ArchiveFormItems = ({ editValueObj, dictData }) => {
  const event$ = useEventEmitter<any>();
  const [fileList, setFileList] = useSafeState([]);

  const [udfList, setUdfList] = useSafeState([]);

  // udf 专门 api
  const { loading: UdfReqLoading, run: UdfReq } = useRequest(
    (data) => {
      return uniCommonService('Api/Sys/UdfConfigSys/GetTriggerUdfConfigs', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          console.log(response);
          setUdfList(response?.data);
        } else {
          message.error('Udf列表获取失败');
        }
      },
    },
  );

  return (
    <ProForm.Group
      title="归档配置"
      colProps={{
        span: 12,
      }}
    >
      <ProFormSwitch
        name={['MasterArgs', 'EnableArchive']} // 这个最终要保存到masterArgs
        label="归档配置"
        initialValue={!!editValueObj?.Master?.ArchiveSettingId || false}
        colProps={{
          span: 4,
        }}
      />
      <ProFormDependency name={[['MasterArgs', 'EnableArchive']]}>
        {({ MasterArgs: { EnableArchive } }) => {
          if (EnableArchive) {
            return (
              <>
                <ProFormSelect
                  name={['ArchiveArgs', 'ArchiveType']}
                  label="归档类型"
                  width="sm"
                  options={ArchiveType}
                  initialValue={
                    editValueObj?.ArchiveSetting?.ArchiveType ||
                    'Csv' ||
                    undefined
                  }
                  placeholder="请输入归档类型"
                  rules={[{ required: true }]}
                  colProps={{
                    span: 8,
                  }}
                />
                <ProFormDependency name={[['ArchiveArgs', 'ArchiveType']]}>
                  {({ ArchiveArgs: { ArchiveType } }) => {
                    if (ArchiveType && ArchiveType === 'Docx') {
                      return (
                        <ProFormSelect
                          name={['ArchiveArgs', 'PrintTemplate']}
                          label="导出模板"
                          initialValue={
                            editValueObj?.ExportSetting?.ExportTemplate ||
                            undefined
                          }
                          colProps={{
                            span: 8,
                          }}
                          fieldProps={{
                            placeholder: '请选择导出模板',
                            open: false,
                            dropdownRender: () => <></>,
                            options: fileList, //
                            fieldNames: {
                              label: 'Title',
                              value: 'BlobId',
                            },
                            onClick: (e) => {
                              event$.emit('selfDefinedReport_ArchiveArgs');
                            },
                          }}
                        />
                        // <ProFormText
                        //   name={['ArchiveArgs', 'PrintTemplate']}
                        //   label="归档模板"
                        //   width="sm"
                        //   initialValue={
                        //     editValueObj?.ArchiveSetting?.PrintTemplate ||
                        //     undefined
                        //   }
                        //   placeholder="请输入归档模板"
                        //   rules={[{ required: ArchiveType === 'Docx' }]}
                        //   colProps={{
                        //     span: 8,
                        //   }}
                        // />
                      );
                    } else if (ArchiveType && ArchiveType === 'Udf') {
                      return (
                        <>
                          <ProFormSelect
                            name={['ArchiveArgs', 'PrintTemplate']}
                            label="导出模板"
                            initialValue={
                              editValueObj?.ArchiveSetting?.PrintTemplate ||
                              undefined
                            }
                            colProps={{
                              span: 8,
                            }}
                            fieldProps={{
                              placeholder: '请选择导出模板',
                              options: udfList,
                              fieldNames: {
                                label: 'Title',
                                value: 'Id',
                              },
                              loading: UdfReqLoading,
                              onDropdownVisibleChange: (open) => {
                                if (open) {
                                  UdfReq({ trigger: 'ReportArchiveRequest' });
                                } else {
                                  setUdfList([]);
                                }
                              },
                            }}
                          />
                          <Col span={4} style={{ alignSelf: 'center' }}>
                            <a
                              style={{ textDecoration: 'underline' }}
                              onClick={(e) => {
                                window.open(`/reportSys/backendReport`);
                              }}
                            >
                              前往编辑
                            </a>
                          </Col>
                        </>
                      );
                    }
                  }}
                </ProFormDependency>
                <BlobFileHandler
                  event$={event$}
                  accepts={BlobFileTypeAccept.ArchiveArgs}
                  fileContentType={
                    BlobFileContentType['/selfDefinedReport_ArchiveArgs']
                  }
                  formNamePath={['ArchiveArgs', 'PrintTemplate']}
                  setParentFileList={setFileList}
                />

                <ProFormDependency name={[['ArchiveArgs', 'ArchiveType']]}>
                  {({ ArchiveArgs: { ArchiveType } }) => {
                    if (ArchiveType && ArchiveType !== 'Udf') {
                      return (
                        <ProFormSwitch
                          name={[
                            'ArchiveArgs',
                            'ExtraConfig',
                            'ExportColumnTitle',
                          ]}
                          label="归档时列名显示为中文"
                          initialValue={
                            // 特殊
                            editValueObj?.ArchiveSetting?.ExtraConfig
                              ?.ExportColumnTitle ?? true
                          }
                          colProps={{
                            // offset: 2,
                            span: 8,
                          }}
                        />
                      );
                    }
                  }}
                </ProFormDependency>

                <ProFormDependency name={[['ArchiveArgs', 'ArchiveType']]}>
                  {({ ArchiveArgs: { ArchiveType } }) => {
                    if (
                      ArchiveType &&
                      (ArchiveType === 'Csv' || ArchiveType === 'Xlsx')
                    ) {
                      return (
                        <>
                          <ProFormText
                            name={['ArchiveArgs', 'DateTimeFormatter']}
                            label={`${ArchiveType}时间类型`}
                            width="sm"
                            initialValue={
                              editValueObj?.ArchiveSetting?.DateTimeFormatter ||
                              'yyyy-MM-dd HH:mm:ss'
                            }
                            placeholder="请输入时间类型"
                            rules={[
                              {
                                required:
                                  ArchiveType === 'Csv' ||
                                  ArchiveType === 'Xlsx',
                              },
                            ]}
                            colProps={{
                              span: 8,
                            }}
                          />
                          <ProFormText
                            name={['ArchiveArgs', 'DateFormatter']}
                            label={`${ArchiveType}日期类型`}
                            width="sm"
                            initialValue={
                              editValueObj?.ArchiveSetting?.DateFormatter ||
                              'yyyy-MM-dd'
                            }
                            placeholder="请输入日期类型"
                            rules={[
                              {
                                required:
                                  ArchiveType === 'Csv' ||
                                  ArchiveType === 'Xlsx',
                              },
                            ]}
                            colProps={{
                              span: 7,
                            }}
                          />
                          <ProFormText
                            name={['ArchiveArgs', 'MonthFormatter']}
                            label={`${ArchiveType}月份类型`}
                            width="sm"
                            initialValue={
                              editValueObj?.ArchiveSetting?.MonthFormatter ||
                              'yyyy-MM'
                            }
                            placeholder="请输入月份类型"
                            rules={[
                              {
                                required:
                                  ArchiveType === 'Csv' ||
                                  ArchiveType === 'Xlsx',
                              },
                            ]}
                            colProps={{
                              span: 6,
                            }}
                          />
                        </>
                      );
                    }
                  }}
                </ProFormDependency>

                <ProFormText
                  name={['ArchiveArgs', 'Description']}
                  label="展示描述"
                  width="sm"
                  initialValue={
                    editValueObj?.ArchiveSetting?.Description || undefined
                  }
                  placeholder="请输入展示描述"
                  transform={(values, namePath) => {
                    return {
                      ArchiveArgs: {
                        [namePath]: values ? values : null,
                      },
                    };
                  }}
                  colProps={{
                    span: 8,
                  }}
                />

                <ProFormTextArea
                  name={['ArchiveArgs', 'UdfPostScript']}
                  label="后处理配置"
                  colProps={{
                    span: 11,
                  }}
                  initialValue={(() => {
                    const udfPostScript =
                      editValueObj?.ArchiveSetting?.UdfPostScript;
                    if (!udfPostScript) return undefined;

                    try {
                      // 如果是字符串，先尝试解析
                      const parsed =
                        typeof udfPostScript === 'string'
                          ? JSON.parse(udfPostScript)
                          : udfPostScript;
                      return JSON.stringify(parsed, null, 2);
                    } catch (error) {
                      // 如果解析失败，直接返回原始值
                      return typeof udfPostScript === 'string'
                        ? udfPostScript
                        : JSON.stringify(udfPostScript, null, 2);
                    }
                  })()}
                />
              </>
            );
          }
        }}
      </ProFormDependency>
    </ProForm.Group>
  );
};

export default ArchiveFormItems;
