import { Reducer, useEffect, useMemo, useReducer, useRef } from 'react';
import { Dispatch, Prompt, useDispatch, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { ExportIconBtn, UniTable } from '@uni/components/src';
import { Button, Card, Modal, Popconfirm, message, Space, Divider } from 'antd';
import { useState } from 'react';
import {
  IEditableState,
  IModalState,
  IReducer,
  ITableState,
} from '@uni/reducers/src/interface';
import { SwagHierarchyBedAmtItem } from './interface';
import { isRespErr, sortingHandler } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType, shortcuts } from '@/Constants';
import {
  tableEditPropsReducer,
  tableReducer,
  InitTableState,
  InitEditableState,
  InitModalState,
  TableAction,
  EditableTableAction,
  ModalAction,
  modalReducer,
} from '@uni/reducers/src';
import './index.less';
import IconBtn from '@uni/components/src/iconBtn';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { HierarchyBedColumns, hierarchyModalColumns } from './columns';
import { ModalForm, ProForm } from '@uni/components/src/pro-form';
import UniEditableTable from '@uni/components/src/table/edittable';
import dayjs from 'dayjs';
import { ITableReq } from '@/interface';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';
import { virtualConfig } from '@uni/components/src/table/virtual';
import { Emitter } from '@uni/utils/src/emitter';
import { HBedEventConstants } from './constants';
import { v4 as uuidv4 } from 'uuid';

const HierarchyBed = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  const tableActionRef = useRef<any>();

  const [editableTableForm] = ProForm.useForm();

  // 编辑模式状态，默认是viewing，点击修改/新建时变为editing
  const [editingMode, setEditingMode] = useState<'viewing' | 'editing'>(
    'viewing',
  );

  // 不做分页
  const [HierarchyBedAmtsTable, HierarchyBedAmtsTableDispatch] = useReducer<
    Reducer<ITableState<SwagHierarchyBedAmtItem>, IReducer>
  >(tableReducer, InitTableState);
  // 不做分页
  const [EditableState, EditableStateDispatch] = useReducer<
    Reducer<
      IEditableState<SwagHierarchyBedAmtItem>,
      IReducer<IEditableState<SwagHierarchyBedAmtItem>>
    >
  >(tableEditPropsReducer, InitEditableState);

  const [modalState, modalStateDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer>
  >(modalReducer, InitModalState);

  // 普通的tableReq
  const tableReq = async (
    params,
    cur = 1,
    size = 9999, // 不分页
    sorter = HierarchyBedAmtsTable.sorter,
  ) => {
    let res: ITableReq | string = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: 'HierarchyBedAmts',
        requestParams: [
          {
            url: 'Api/Dyn-ddr/HierarchyBedAmts/GetList',
            method: 'POST',
            data: {
              HospCode: params?.hospCodes, //[params?.hospCodes?.at(0)],
              HierarchyCode: params?.hierarchyCodes,
              Sdate: params?.dateRange[0],
              Edate: params?.dateRange[1],
              Status: params?.dynRegiStatus,
              current: cur,
              pageSize: size,
              sorting: sortingHandler(sorter),
              HierarchyType: 'DynDepts', // 写死 住院科室
            },
            dataType: 'dyn-ddr',
          },
        ],
      },
    });
    if (typeof res !== 'string') {
      HierarchyBedAmtsTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res?.datas[0]?.data ?? [],
        },
      });
      EditableStateDispatch({
        type: EditableTableAction.editableValuesChange,
        payload: {
          value: res?.datas[0]?.data ?? [],
        },
      });
      // 把 form 清空
      editableTableForm.setFieldsValue(res?.datas[0]?.data ?? []);
    }
  };

  // 操作类
  const reqActionReq = async (params: any, reqType: ReqActionType) => {
    if (!params || !reqType) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `HierarchyBedAmts/${reqType}`,
        requestParams: {
          url: `Api/Dyn-ddr/HierarchyBedAmts/${reqType}`,
          method:
            reqType === ReqActionType.delete
              ? 'DELETE'
              : reqType === ReqActionType.getInpatientBedAmts
              ? 'GET'
              : 'POST',
          params:
            typeof params === 'number'
              ? { id: params }
              : reqType === ReqActionType.update
              ? { id: params.Id }
              : undefined,
          data:
            reqType === ReqActionType?.delete ||
            reqType === ReqActionType?.create ||
            reqType === ReqActionType?.update
              ? {
                  ...params,
                  HierarchyType: 'DynDepts', // 写死
                }
              : reqType === ReqActionType?.updateInpatientBedAmts
              ? params
              : _.isObject(params)
              ? {
                  HospCode: params?.hospCodes,
                  HierarchyCode: params?.hierarchyCodes,
                  Sdate: params?.dateRange[0],
                  Edate: params?.dateRange[1],
                  Status: params?.dynRegiStatus,
                  HierarchyType: 'DynDepts', // 写死
                }
              : undefined,
          dataType: 'dyn-ddr',
          requestType: 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      if (reqType === ReqActionType.getInpatientBedAmts) {
        console.log(res);
        if (res?.data.length > 0) {
          modalStateDispatch({
            type: ModalAction.change,
            payload: {
              visible: true,
              record: params,
              dataSource: res?.data.sort((a, b) =>
                a.ExactDate.localeCompare(b.ExactDate),
              ), // 先排序
            },
          });
        }
        // res?.data.sort((a, b) => b.ExactDate.localeCompare(a.ExactDate));
      } else {
        message.success('操作成功');
        if (modalState.visible) {
          modalStateDispatch({
            type: ModalAction.init,
          });
        }
        tableReq(searchParams);
        return 'done';
      }
    }
    return 'fail';
  };

  useEffect(() => {
    if (searchParams) {
      if (editingMode === 'editing') {
        message.warning('请先完成当前编辑');
      } else {
        tableReq(searchParams);

        // if (
        //   (searchParams && searchParams?.triggerSource === 'btnClick') ||
        //   (isEmptyValues(requestParams) && searchParams?.singleDate)
        // ) {
        //   setModalInitValue({
        //     ExactDate: searchParams?.singleDate ?? searchParams?.dateRange?.at(1),
        //     HospCode:
        //       searchParams?.hospCodes ??
        //       (isEmptyValues(searchParams?.hospCode)
        //         ? []
        //         : Array.isArray(searchParams.hospCode)
        //         ? searchParams.hospCode
        //         : [searchParams.hospCode]),
        //   });
        // }
      }
    }
  }, [searchParams]);

  // 离开页面提示

  // treeChange时特殊操作的提示
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (editingMode === 'editing') {
        event.preventDefault();
        event.returnValue = '你确定要离开此页面？ 系统可能不会保存您所做的更改';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [editingMode]);

  // 必须在editable value set 之后再更新form
  useEffect(() => {
    if (EditableState?.value?.length > 0 && editingMode === 'viewing') {
      // 这边是为了让表单的值和table的值保持一致
      editableTableForm.resetFields();
    }
  }, [EditableState?.value, editingMode]);

  // modal form && table columns
  useEffect(() => {
    if (dictData?.Hospital && dictData?.DynDepts) {
      // columns处理
      if (
        columnsList?.['HierarchyBedAmts'] &&
        HierarchyBedAmtsTable.columns.length < 1
      ) {
        HierarchyBedAmtsTableDispatch({
          type: TableAction.columnsChange,
          payload: {
            columns: tableColumnBaseProcessor(
              HierarchyBedColumns(dictData?.DynDepts),
              columnsList['HierarchyBedAmts'],
            ),
          },
        });
      }
    }
  }, [
    dictData?.Hospital,
    dictData?.DynDepts,
    columnsList?.['HierarchyBedAmts'],
  ]);

  // 李惠利custom：批量upsert
  const lihuiliCustomActionReq = async (data: any) => {
    if (!data) return;
    // 处理数据，特别处理Id以new-开头的记录
    const processedData = data.map((item) => {
      // 创建一个新对象，避免直接修改原对象
      const newItem = { ...item };

      // 如果Id以new-开头，进行特殊处理
      if (newItem.Id && newItem.Id.toString().startsWith('new-')) {
        // 把HierarchyName的值给到HierarchyCode
        if (newItem.HierarchyName) {
          newItem.HierarchyCode = newItem.HierarchyName?.value;
          newItem.HierarchyName = newItem.HierarchyName?.label;

          // 尝试在dictData.DynDepts中查找匹配项
          if (dictData?.DynDepts) {
            const matchedDept = dictData.DynDepts.find(
              (dept) => dept.Code === newItem.HierarchyCode,
            );
            console.log('matchedDept', matchedDept);
            // 如果找到匹配项且有ExtraProperties.HospCode，将其赋值给hospCode
            if (
              matchedDept &&
              matchedDept.ExtraProperties &&
              matchedDept.ExtraProperties.HospCode
            ) {
              newItem.hospCode = matchedDept.ExtraProperties.HospCode;
            }
          }
        }
        // 完全删除Id属性
        delete newItem.Id;
      }

      return newItem;
    });

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `HierarchyBedAmts/BulkUpsert`,
        requestParams: {
          url: `Api/Dyn-ddr/HierarchyBedAmts/BulkUpsert`,
          method: 'POST',
          data: processedData, // 使用处理后的数据
          dataType: 'dyn-ddr',
          requestType: 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      message.success('批量保存成功');
      totalEditingData.current = [];
      EditableStateDispatch({
        type: EditableTableAction.editableKeysChange,
        payload: {
          editableKeys: [],
        },
      });
      // 保存成功后更新为查看模式
      setEditingMode('viewing');
      tableReq(searchParams);
    }
    return 'fail';
  };

  // virtual list
  const tableY = `calc(100vh - 470px)`;
  // 记录一份 全部数据的副本 实时根据用户的修改进行更新 ，在用户点击保存时，统一提交
  const totalEditingData = useRef([]);

  useEffect(() => {
    // end date change
    Emitter.on(HBedEventConstants.DATE_CHANGE, ({ id, formatDate }) => {
      // ref 会实时更新
      const index = totalEditingData.current?.findIndex(
        (data) => data.Id === id,
      );

      console.log('before Set edtae', totalEditingData.current);

      if (index !== -1) {
        // 找到匹配项，直接修改属性
        totalEditingData.current[index].Edate = formatDate;

        console.log('after Set edtae', totalEditingData.current);

        editableTableForm.setFieldValue([id, 'Edate'], formatDate);
      }
    });

    // start date change new only
    Emitter.on(HBedEventConstants.START_DATE_CHANGE, ({ id, formatDate }) => {
      // ref 会实时更新
      const index = totalEditingData.current?.findIndex(
        (data) => data.Id === id,
      );
      console.log('before Set start', totalEditingData.current);
      if (index !== -1) {
        // 找到匹配项，直接修改属性
        totalEditingData.current[index].Sdate = formatDate;
        console.log('after Set start', totalEditingData.current);
        editableTableForm.setFieldValue([id, 'Sdate'], formatDate);
      }
    });

    return () => {
      Emitter.off(HBedEventConstants.DATE_CHANGE);
      Emitter.off(HBedEventConstants.START_DATE_CHANGE);
    };
  }, []);

  // 自定义添加多行的功能
  const handleAddMultipleRows = () => {
    // BUG: 添加多行时，因为会重新setValue，这会导致之前如果有新建过的或者编辑过的内容会被清空，因为其他地方编辑的时候把编辑的结果值存在了
    let newData = [{ Id: 'new-' + uuidv4() }];
    EditableStateDispatch({
      type: EditableTableAction.editableValuesAddWithKeys,
      payload: {
        value: newData.concat([...totalEditingData.current]) as any,
        key: newData?.at(0)?.Id,
      },
    });
    console.log(totalEditingData.current);
    totalEditingData.current = newData.concat([...totalEditingData.current]);
  };

  return (
    <>
      <Prompt
        when={editingMode === 'editing'}
        message="你确定要离开此页面？ 系统可能不会保存您所做的更改"
      />
      <Card
        title="床位数管理"
        extra={
          <>
            <Space>
              {editingMode === 'viewing' && (
                <Button
                  disabled={!Array.isArray(HierarchyBedAmtsTable.data)}
                  onClick={() => {
                    EditableStateDispatch({
                      type: EditableTableAction.editableKeysChange,
                      payload: {
                        editableKeys: HierarchyBedAmtsTable?.data?.map(
                          (item) => item?.Id,
                        ),
                      },
                    });
                    // 数据开始记录
                    totalEditingData.current = _.cloneDeep(
                      HierarchyBedAmtsTable.data,
                    );
                    // 设置为编辑模式
                    setEditingMode('editing');
                  }}
                >
                  修改 / 新建
                </Button>
              )}
              {editingMode === 'editing' && (
                <>
                  <Button
                    loading={loadings[`HierarchyBedAmts/BulkUpsert`]}
                    type="primary"
                    onClick={() => {
                      console.log(
                        'EditableState save',
                        editableTableForm.getFieldsValue(),
                        totalEditingData.current,
                      );
                      lihuiliCustomActionReq(totalEditingData.current);
                      // 在保存成功后，lihuiliCustomActionReq 函数会调用 tableReq 并清空 editableKeys
                      // 所以这里不需要额外设置 setEditingMode('viewing')
                    }}
                  >
                    保存
                  </Button>
                  <Popconfirm
                    title="确认要取消编辑？"
                    onConfirm={() => {
                      EditableStateDispatch({
                        type: EditableTableAction.editableKeysChange,
                        payload: {
                          editableKeys: [],
                        },
                      });
                      EditableStateDispatch({
                        type: EditableTableAction.editableValuesChange,
                        payload: {
                          value: HierarchyBedAmtsTable.data,
                        },
                      });
                      totalEditingData.current = undefined;
                      // 设置为查看模式
                      setEditingMode('viewing');
                    }}
                  >
                    <Button>取消编辑</Button>
                  </Popconfirm>
                </>
              )}
              <Divider type="vertical" />
              <ExportIconBtn
                isBackend={true}
                backendObj={{
                  url: 'Api/Dyn-ddr/HierarchyBedAmts/ExportGetList',
                  method: 'POST',
                  data: {
                    HospCode: searchParams?.hospCodes, //[params?.hospCodes?.at(0)],
                    HierarchyCode: searchParams?.hierarchyCodes,
                    Sdate: searchParams?.dateRange?.[0],
                    Edate: searchParams?.dateRange?.[1],
                    HierarchyType: 'DynDepts', // 写死 观察科室
                  },
                  fileName: `${searchParams?.dateRange?.[0]}_${searchParams?.dateRange?.[1]} 住院动态床位数管理`,
                }}
                btnDisabled={EditableState.value?.length < 1}
              />
              <TableColumnEditButton
                {...{
                  columnInterfaceUrl: 'Api/Dyn-ddr/HierarchyBedAmts/GetList',
                  onTableRowSaveSuccess: (columns) => {
                    // 这个columns 存到dva
                    dispatch({
                      type: 'global/saveColumns',
                      payload: {
                        name: 'HierarchyBedAmts',
                        value: columns,
                      },
                    });
                    HierarchyBedAmtsTableDispatch({
                      type: TableAction.columnsChange,
                      payload: {
                        columns: tableColumnBaseProcessor(
                          HierarchyBedColumns(dictData?.DynDepts),
                          columns,
                        ),
                      },
                    });
                  },
                }}
              />
            </Space>
          </>
        }
      >
        <UniEditableTable
          id="odm_hierarchy_bed_amts"
          wrapperClassName="in_hosp_hierarchy_bed"
          rowKey="Id"
          actionRef={tableActionRef}
          size="small"
          showSorterTooltip={false}
          enableShortcuts={false}
          enableHotKeys
          style={{
            '--tableMinHeight': tableY,
          }}
          scroll={{ x: 'max-content', y: tableY }}
          // widthCalculate={true}
          widthDetectAfterDictionary
          forceColumnsUpdate
          columns={HierarchyBedAmtsTable.columns}
          value={EditableState.value}
          bordered
          loading={
            loadings[`HierarchyBedAmts/BulkUpsert`] ??
            loadings['HierarchyBedAmts'] ??
            false
          }
          pagination={false}
          dictionaryData={dictData}
          recordCreatorProps={
            editingMode === 'viewing'
              ? false
              : {
                  position: 'bottom',
                  onClick: () => {
                    handleAddMultipleRows();
                    return false;
                  },
                }
          }
          editable={{
            type: 'multiple',
            form: editableTableForm,
            editableKeys: EditableState.editableKeys,
            formProps: {
              onValuesChange: (changedValues, allValues) => {
                console.log(
                  'onValuesChange formProps',
                  changedValues,
                  allValues,
                );
              },
            },
            onChange: (editableKeys) => {
              EditableStateDispatch({
                type: EditableTableAction.editableKeysChange,
                payload: { editableKeys },
              });
              // 同步更新 editingMode 状态，确保与 editableKeys 保持一致
              setEditingMode(editableKeys.length > 0 ? 'editing' : 'viewing');
            },
            actionRender: (row, config, defaultDom) => {
              return row?.Id?.toString()?.indexOf('new-') !== -1 ? (
                <Space style={{ justifyContent: 'center', width: '100%' }}>
                  <IconBtn
                    key="delete"
                    type="delete"
                    openPop
                    popOnConfirm={() => {
                      EditableStateDispatch({
                        type: EditableTableAction.editableValuesDelete,
                        payload: {
                          value: [row?.Id],
                        },
                      });
                      totalEditingData.current =
                        totalEditingData.current?.filter(
                          (d) => d.Id !== row?.Id,
                        );
                    }}
                  />
                </Space>
              ) : null;
            },
            // row?.Id?.toString()?.includes('new') ? [defaultDom.delete] : null,
            onSave: async (rowKey, data, row) => {
              console.log(data);
            },
            onValuesChange: (record, recordList) => {
              // 这边实时更新ref值 用于保存传给后端
              // 使用 findIndex 找到匹配的元素索引
              const index = totalEditingData.current?.findIndex(
                (data) => data.Id === record.Id,
              );

              console.log(
                'onValuesChange before Set',
                record,
                totalEditingData.current,
              );
              if (index !== -1) {
                // 找到匹配项，直接修改属性
                // Edate不替换
                let edate = totalEditingData.current[index].Edate;
                console.log('edate check', edate);
                totalEditingData.current[index] = { ...record, Edate: edate };
              }
              // totalEditingData.current = recordList
              console.log('onValuesChange after Set', totalEditingData.current);
            },
          }}
          {..._.merge(
            {},
            virtualConfig(
              'odm_hierarchy_bed_amts',
              EditableState?.value?.length,
            ),
          )}
        />
      </Card>
      <Modal
        width={800}
        title={
          <>
            <span>更新床位数：</span>
            {modalState?.visible ? (
              <Space size={10}>
                <span>
                  {
                    EditableState?.value?.find(
                      (d) => d.Id === modalState?.record,
                    )?.HospName
                  }
                </span>
                <span>
                  {dayjs(
                    EditableState?.value?.find(
                      (d) => d.Id === modalState?.record,
                    )?.Sdate,
                  )?.format('YYYY-MM-DD')}
                  ~
                  {dayjs(
                    EditableState?.value?.find(
                      (d) => d.Id === modalState?.record,
                    )?.Edate,
                  )?.format('YYYY-MM-DD')}
                </span>
              </Space>
            ) : (
              ''
            )}
          </>
        }
        open={modalState.visible}
        maskClosable={false}
        onCancel={() => {
          modalStateDispatch({
            type: ModalAction.init,
          });
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              modalStateDispatch({
                type: ModalAction.init,
              });
            }}
          >
            取消
          </Button>,
          <Popconfirm
            key="submit"
            title="请注意，已锁定的数据将不会更新"
            onConfirm={() => {
              reqActionReq(
                modalState?.dataSource?.map((d) => ({
                  Id: d.Id,
                  ApprovedBedsNumber: d.ApprovedBedsNumber,
                  SuppliedBedsNumber: d.SuppliedBedsNumber,
                })),
                ReqActionType?.updateInpatientBedAmts,
              );
            }}
          >
            <Button
              loading={
                loadings[
                  `HierarchyBedAmts/${ReqActionType.updateInpatientBedAmts}`
                ]
              }
              key="submit"
              type="primary"
            >
              确认更新
            </Button>
          </Popconfirm>,
        ]}
      >
        <UniTable
          id="hierarchy_bed_modal_table"
          bordered
          rowKey="Id"
          showSorterTooltip={false}
          columns={hierarchyModalColumns}
          dataSource={modalState?.dataSource}
          scroll={{ x: 'max-content' }}
        />
      </Modal>

      {/* <ShortcutsHelpModal shortcuts={shortcuts} /> */}
    </>
  );
};

export default HierarchyBed;
