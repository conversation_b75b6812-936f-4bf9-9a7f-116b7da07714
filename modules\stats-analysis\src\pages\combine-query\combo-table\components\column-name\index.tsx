import {
  pinyinInitialSearch,
  searchFunctionGetter,
} from '@uni/utils/src/pinyin';
import fuzzysort from 'fuzzysort';
import FieldCascader, {
  removePrefixPath,
} from '@/pages/combine-query/components/field-cascader/FieldCascader';
import React, { useState } from 'react';
import { FormTableItemBaseProps } from '@uni/components/src/drag-edit-only-table/UniDragEditOnlyTable';
import { Button, Divider, Form, Select, Space } from 'antd';
import {
  filterAllItems,
  filterIsCommonItems,
  findSelectItemInDataSource,
  searchRecordParentFieldByGroupId,
} from '@/pages/combine-query/combo-table/utils';
import { UniSelect } from '@uni/components/src';
import { Emitter } from '@uni/utils/src/emitter';
import { CombineQueryFieldItem } from '@/pages/combine-query/interfaces';
import { StatsAnalysisEventConstant } from '@/constants';
import { isEmptyValues } from '@uni/utils/src/utils';
const { Option, OptGroup } = Select;
import './index.less';
import {
  filterFlagProcessor,
  filterFlagWithLeftStartProcessor,
} from '@/pages/combine-query/utils';

interface ColumnNameCascaderProps extends FormTableItemBaseProps {
  recordId: string;
  dataIndex: string;
  config: any;
  fields: any;
}

const leftStartSearch =
  (window as any)?.externalConfig?.['common']?.leftStartSearch ?? false;

const ColumnNameCommonSelector = (props: ColumnNameCascaderProps) => {
  const [keyword, setKeyword] = useState<string>('');

  const form = Form.useFormInstance();
  const groupId = Form.useWatch([props?.recordId, 'groupId'], form);
  let parentField = searchRecordParentFieldByGroupId(
    form?.getFieldsValue(true),
    groupId,
  );

  const fieldsProcessorWithParentField = () => {
    const commonItems = filterIsCommonItems(props?.fields)
      ?.map((item) => {
        return {
          ...item,
          name: item?.extra?.title2,
          value: item?.path,
        };
      })
      ?.sort(
        (a, b) =>
          (a?.extra?.commonSequence ?? 65535) -
          (b?.extra?.commonSequence ?? 65535),
      )
      ?.filter((item) => {
        return leftStartSearch
          ? filterFlagWithLeftStartProcessor(item, keyword)
          : filterFlagProcessor(item, keyword);
      });

    const commonItemKeys = commonItems?.map((item) => item?.path);

    const otherItems = filterAllItems(props?.fields)
      ?.filter((item) => !commonItemKeys?.includes(item?.path))
      ?.map((item) => {
        return {
          ...item,
          name: item?.extra?.title2,
          value: item?.path,
          className: keyword ? '' : 'hidden-option-item',
        };
      })
      ?.sort(
        (a, b) =>
          (a?.extra?.columnSequence ?? 65535) -
          (b?.extra?.columnSequence ?? 65535),
      )
      ?.filter((item) => {
        return leftStartSearch
          ? filterFlagWithLeftStartProcessor(item, keyword)
          : filterFlagProcessor(item, keyword);
      });

    let dataSources = [];

    if (parentField) {
      let fields = props?.fields?.slice();
      let parentFieldPath = parentField?.path?.split('.');

      parentFieldPath?.forEach((pathItem) => {
        let fieldWithThisPath = fields?.find((item) => item?.key === pathItem);
        if (fieldWithThisPath) {
          fields = fieldWithThisPath?.items;
        }
      });

      dataSources = fields
        ?.filter((item) => {
          return leftStartSearch
            ? filterFlagWithLeftStartProcessor(item, keyword)
            : filterFlagProcessor(item, keyword);
        })
        ?.map((item) => {
          return {
            ...item,
            name: item?.extra?.title2,
            value: item?.path,
          };
        })
        ?.sort(
          (a, b) =>
            (a?.extra?.columnSequence ?? 65535) -
            (b?.extra?.columnSequence ?? 65535),
        );
    } else {
      if (keyword) {
        dataSources = [
          {
            dataSourceType: 'group',
            label: '常用',
            options: commonItems,
          },
          {
            dataSourceType: 'group',
            label: '其他',
            options: otherItems,
          },
        ];
      } else {
        dataSources = commonItems?.concat(otherItems);
      }
    }

    return dataSources;
  };

  const renderOptions = (items: any) => {
    return (
      <>
        {items?.map((item) => {
          return (
            <Option className={item?.className ?? ''} value={item?.value}>
              {item?.label}
            </Option>
          );
        })}
      </>
    );
  };

  const renderOptionsWithGroup = (items: any) => {
    return (
      <>
        {items?.map((groupItem) => {
          return (
            <OptGroup label={groupItem?.label}>
              {renderOptions(groupItem?.options)}
            </OptGroup>
          );
        })}
      </>
    );
  };

  const dataSource = fieldsProcessorWithParentField();

  // console.log('props?.fields', props?.fields);

  return (
    <div className={'column-name-select-container'}>
      <UniSelect
        allowClear={true}
        placeholder={'请选择条件'}
        showSearch={true}
        value={props?.value?.at(0)}
        getPopupContainer={(trigger) => {
          return document.getElementById('combo-query-expression-table');
        }}
        onFilterOptionsCapture={(inputValue, option) => {
          return true;
        }}
        dropdownRender={(menu) => {
          // console.log('dropdownRender', menu);

          return (
            <>
              {menu}
              {isEmptyValues(parentField) && isEmptyValues(keyword) && (
                <>
                  <Divider style={{ margin: '0px 0' }} />
                  <Button
                    type="primary"
                    style={{ width: '100%', borderRadius: 0 }}
                    onClick={() => {
                      Emitter.emit(
                        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE,
                        {
                          visible: true,
                          formKey: [props?.recordId, props?.dataIndex],
                        },
                      );
                    }}
                  >
                    更多条件
                  </Button>
                </>
              )}
            </>
          );
        }}
        onSearch={(value: string) => {
          setKeyword(value);
        }}
        onClear={() => {
          Emitter.emit(
            StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_COLUMN_CLEAR,
          );
        }}
        onChange={(value) => {
          let option = findSelectItemInDataSource(dataSource, value);
          setKeyword('');

          if (value === undefined) {
            form.setFieldValue([props?.recordId, props?.dataIndex], undefined);
            form.setFieldValue([props?.recordId, 'columnValue'], undefined);
            form.setFieldValue([props?.recordId, 'operator'], undefined);
            form.setFieldValue([props?.recordId, 'expr'], undefined);
            form.setFieldValue([props?.recordId, 'extra'], undefined);
            form.setFieldValue([props?.recordId, 'dataType'], undefined);
            form.setFieldValue([props?.recordId, 'parentField'], undefined);
            form.setFieldValue([props?.recordId, 'parentFieldPath'], undefined);
            return;
          }

          let paths = value?.split('.');
          form.setFieldValue([props?.recordId, props?.dataIndex], [value]);
          form.setFieldValue([props?.recordId, 'operator'], undefined);
          form.setFieldValue([props?.recordId, 'columnValue'], undefined);
          form.setFieldValue([props?.recordId, 'expr'], option?.extra?.expr);
          form.setFieldValue([props?.recordId, 'extra'], option?.extra);

          form.setFieldValue(
            [props?.recordId, 'dataType'],
            option?.extra?.type,
          );

          // 表示当前第一层的是!group  | array 也要有上级 前提是当前这个只有2层 不然会出错
          if (paths?.length === 2) {
            let rootField = props?.fields?.find(
              (item) => item?.key === paths?.at(0),
            );

            if (
              rootField?.extra?.dataType === 'array' &&
              rootField?.extra?.type === '!group'
            ) {
              form.setFieldValue([props?.recordId, 'parentField'], rootField);
              form.setFieldValue(
                [props?.recordId, 'parentFieldPath'],
                rootField?.path,
              );
            }
          }

          // 表示是第三层
          if (paths?.length > 2) {
            let rootField = props?.fields?.find(
              (item) => item?.key === paths?.at(0),
            );
            let secondLevelField = rootField?.items?.find(
              (item) => item?.key === paths?.at(1),
            );
            form.setFieldValue(
              [props?.recordId, 'parentField'],
              secondLevelField,
            );
            form.setFieldValue(
              [props?.recordId, 'parentFieldPath'],
              secondLevelField?.path,
            );
          }
        }}
        dropdownClassName={'column-name-select-dropdown-container'}
        virtual={false}
      >
        <>
          {keyword
            ? parentField
              ? renderOptions(dataSource)
              : renderOptionsWithGroup(dataSource)
            : renderOptions(dataSource)}
        </>
      </UniSelect>
    </div>
  );
};

const ColumnNameCascader = (props: ColumnNameCascaderProps) => {
  const form = Form.useFormInstance();
  const groupId = Form.useWatch([props?.recordId, 'groupId'], form);
  let parentField = searchRecordParentFieldByGroupId(
    form?.getFieldsValue(true),
    groupId,
  );

  const fieldsProcessorWithParentField = () => {
    if (parentField) {
      let fields = props?.fields?.slice();
      let parentFieldPath = parentField?.path?.split('.');

      parentFieldPath?.forEach((pathItem) => {
        let fieldWithThisPath = fields?.find((item) => item?.key === pathItem);
        if (fieldWithThisPath) {
          fields = fieldWithThisPath?.items;
        }
      });

      return fields;
    }

    return props?.fields;
  };

  let fields = fieldsProcessorWithParentField();

  console.log('Fields', fields);

  const parentFieldPath = parentField ? parentField?.path.split('.') : [];

  return (
    <FieldCascader
      type={'table'}
      config={props?.config}
      value={removePrefixPath(props?.value, parentFieldPath)}
      setField={(paths, options) => {
        console.log('setField', paths, options);

        let nonSelectableKeys = options?.filter(
          (item) => item?.notSelectable !== true,
        );
        // 表示不能显示在 框里面
        // 这里的options 是文件夹
        if (nonSelectableKeys?.length === 0) {
          form.setFieldValue([props?.recordId, props?.dataIndex], []);
          form.setFieldValue(
            [props?.recordId, 'parentField'],
            options?.at(options?.length - 1),
          );
          form.setFieldValue(
            [props?.recordId, 'parentFieldPath'],
            options?.at(options?.length - 1)?.path,
          );
          form.setFieldValue([props?.recordId, 'columnValue'], undefined);
          return;
        }

        form.setFieldValue([props?.recordId, props?.dataIndex], paths);
        form.setFieldValue([props?.recordId, 'columnValue'], undefined);
        form.setFieldValue(
          [props?.recordId, 'dataType'],
          options?.at(options?.length - 1)?.extra?.type,
        );

        // 设定上级
        if (options?.length > 2) {
          form.setFieldValue(
            [props?.recordId, 'parentField'],
            options?.at(options?.length - 2),
          );
          form.setFieldValue(
            [props?.recordId, 'parentFieldPath'],
            options?.at(options?.length - 2)?.path,
          );
        }
      }}
      onVirtualKeysExist={(payload, virtualOptions) => {
        console.log('onVirtualKeysExist', payload, virtualOptions);
      }}
      items={fields}
      customProps={{
        enableFuzzySortHighlight: true,
        changeOnSelect: true,
        expandTrigger: 'hover',
        showSearch: {
          filter: (inputValue, path) => {
            console.log('showSearch', inputValue, path);
            const keysForFilter = ['label'];

            let likeWords = keysForFilter?.map((key) => {
              return path
                .map((option) => {
                  return option[key];
                })
                ?.join('/');
            });

            return path.some((option) => {
              // TODO hard code 0
              return (
                (likeWords?.at(0)?.indexOf(inputValue.toLowerCase()) > -1 ||
                  pinyinInitialSearch(
                    likeWords?.at(0),
                    inputValue.toLowerCase(),
                  ) ||
                  fuzzysort.go(inputValue?.toLowerCase(), likeWords)?.length >
                    0) &&
                option?.notSelectable !== true
              );
            });
          },
        },
        displayRender: (label, selectedOptions) => {
          return selectedOptions?.at(selectedOptions?.length - 1)?.label;
        },
      }}
    />
  );
};

export { ColumnNameCascader, ColumnNameCommonSelector };
