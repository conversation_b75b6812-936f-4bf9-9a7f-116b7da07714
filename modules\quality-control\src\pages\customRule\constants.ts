export const qualityControlRuleEventConstants = {
  EDIT_BTN_CLK: 'EDIT_BTN_CLK',
  TEST_BTN_CLK: 'TEST_BTN_CLK',
  ISVALID_STATUS_CHANGE: 'ISVALID_STATUS_CHANGE',
  ADD_BY_JSONLOGIC: 'ADD_BY_JSONLOGIC',
  RELOAD_BY_JSONLOGIC: 'RELOAD_BY_JSONLOGIC',

  ACTIVE_TAB_CHANGE: 'ACTIVE_TAB_CHANGE',

  // 返回给onFinish（调接口）
  RETURN_EXPR: 'RETURN_EXPR',
  // 返回给测试
  RETURN_TEST_EXPR: 'RETURN_TEST_EXPR',

  // step form second
  UPDATE_QUALITY_CHECK_PICK: 'UPDATE_QUALITY_CHECK_PICK',

  // step form test modal
  OPEN_TEST_MODAL: 'OPEN_TEST_MODAL',
  TEST_DATASOURCE_DETAIL_BTN_CLK: 'TEST_DATASOURCE_DETAIL_BTN_CLK',

  // 字段数据相关
  EXPR_FIELDS_UPDATED: 'EXPR_FIELDS_UPDATED', // 字段数据更新事件
  REQUEST_EXPR_FIELDS: 'REQUEST_EXPR_FIELDS', // 请求字段数据事件
};

export const defaultDatasourcePicks = [
  {
    AppCode: 'Dmr',
    RuleTemplate: 'Dmr-Review-Pre',
    // RuleCode: '8-CUST-1',
    Description: '病案首页提交前审核',
    ErrorLevel: '2',
    IsValid: false,
  },
  {
    AppCode: 'Dmr',
    RuleTemplate: 'Dmr-Review-Post',
    // RuleCode: '8-CUST-1',
    Description: '病案首页提交后审核',
    ErrorLevel: '2',
    IsValid: true,
  },
  {
    AppCode: 'Insur',
    RuleTemplate: 'Insur-Review-Pre',
    // RuleCode: '8-CUST-1',
    Description: '结算清单提交前审核',
    ErrorLevel: '2',
    IsValid: false,
  },
  {
    AppCode: 'Insur',
    RuleTemplate: 'Insur-Review-Post',
    // RuleCode: '8-CUST-1',
    Description: '结算清单提交后审核',
    ErrorLevel: '0',
    IsValid: false,
  },
];

import dayjs from 'dayjs';

export enum ReqActionType {
  GetJsonLogicSrcObject = 'Dmr/DmrQualityControl/GetJsonLogicSrcObject', // 根据HisId返回JsonLogic所用的Src Json
  GetExprRules = 'Sys/QualitySys/GetExprRules', // 获取Expr规则列表
  UpsertExprRule = 'Sys/QualitySys/UpsertExprRule', // 新增/更新 Expr规则列表
  CheckRule = 'logic/checkRule', // 测试 rule正确性，特殊接口

  DmrCardBundle = 'Dmr/DmrCardBundle/Get', // 展示用数据获取
  GetQueryableList = 'Sys/ColumnDefSys/GetQueryableList', // 'Sys/ColumnDefSys/GetQueryableList', // Expr data
}

export const EventConstant = {
  ADD_BY_RULESENGINE: 'ADD_BY_RULESENGINE', // 新增规则引擎触发事件
  ADD_BY_JSONLOGIC: 'ADD_BY_JSONLOGIC', // 新增JSONLOGIC触发事件

  RELOAD_BY_JSONLOGIC: 'RELOAD_BY_JSONLOGIC', // RELOAD
};

export const StatsAnalysisEventConstant = {
  STATS_ANALYSIS_RESET: 'STATS_ANALYSIS_RESET',

  STATS_ANALYSIS_REPORT_ITEM_QUERY: 'STATS_ANALYSIS_REPORT_ITEM_QUERY',
  STATS_ANALYSIS_REPORT_ITEM_QUERY_SUBMIT:
    'STATS_ANALYSIS_REPORT_ITEM_QUERY_SUBMIT',
  STATS_ANALYSIS_REPORT_ITEM_CLICK: 'STATS_ANALYSIS_REPORT_ITEM_CLICK',
  STATS_ANALYSIS_REPORT_ARCHIVE: 'STATS_ANALYSIS_REPORT_ARCHIVE',
  STATS_ANALYSIS_REPORT_DOWNLOAD: 'STATS_ANALYSIS_REPORT_DOWNLOAD',

  // 组合查询
  STATS_ANALYSIS_COMBINE_QUERY: 'STATS_ANALYSIS_COMBINE_QUERY',
  STATS_ANALYSIS_COMBINE_QUERY_METRICS: 'STATS_ANALYSIS_COMBINE_QUERY_METRICS',
  STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT:
    'STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT',

  STATS_ANALYSIS_COMBINE_QUERY_METRICS_STATE:
    'STATS_ANALYSIS_COMBINE_QUERY_METRICS_STATE',
  STATS_ANALYSIS_COMBINE_QUERY_COLUMNS_STATE:
    'STATS_ANALYSIS_COMBINE_QUERY_COLUMNS_STATE',

  STATS_ANALYSIS_COMBINE_QUERY_DETAIL: 'STATS_ANALYSIS_COMBINE_QUERY_DETAIL',
  STATS_ANALYSIS_COMBINE_QUERY_TITLE: 'STATS_ANALYSIS_COMBINE_QUERY_TITLE',
  STATS_ANALYSIS_STATS_COMBINE_QUERY_TITLE:
    'STATS_ANALYSIS_STATS_COMBINE_QUERY_TITLE',

  STATS_ANALYSIS_AGGREGATE_ITEM_EDIT: 'STATS_ANALYSIS_AGGREGATE_ITEM_EDIT',
  STATS_ANALYSIS_GROUP_ITEM_EDIT: 'STATS_ANALYSIS_GROUP_ITEM_EDIT',
  STATS_ANALYSIS_METRIC_ITEM_EDIT: 'STATS_ANALYSIS_METRIC_ITEM_EDIT',

  STATS_ANALYSIS_TEMPLATE_CLICK: 'STATS_ANALYSIS_TEMPLATE_CLICK',

  DETAIL_COLUMN_TEMPLATE_CLICK: 'DETAIL_COLUMN_TEMPLATE_CLICK',
  METRIC_COLUMN_TEMPLATE_CLICK: 'METRIC_COLUMN_TEMPLATE_CLICK',

  STATS_ANALYSIS_TEMPLATE_SAVE_SUCCESS: 'STATS_ANALYSIS_TEMPLATE_SAVE_SUCCESS',
  DETAIL_COLUMN_TEMPLATE_SAVE_SUCCESS: 'DETAIL_COLUMN_TEMPLATE_SAVE_SUCCESS',
  METRIC_COLUMN_TEMPLATE_SAVE_SUCCESS: 'METRIC_COLUMN_TEMPLATE_SAVE_SUCCESS',

  STATS_ANALYSIS_TEMPLATE_QUERY_SUCCESS:
    'STATS_ANALYSIS_TEMPLATE_QUERY_SUCCESS',

  DETAIL_COLUMN_TEMPLATE_EXPAND: 'DETAIL_COLUMN_TEMPLATE_EXPAND',
  METRIC_COLUMN_TEMPLATE_EXPAND: 'METRIC_COLUMN_TEMPLATE_EXPAND',
  STATS_ANALYSIS_TEMPLATE_EXPAND: 'STATS_ANALYSIS_TEMPLATE_EXPAND',

  DETAIL_COLUMN_TEMPLATE_OPEN: 'DETAIL_COLUMN_TEMPLATE_OPEN',
  METRIC_COLUMN_TEMPLATE_OPEN: 'METRIC_COLUMN_TEMPLATE_OPEN',

  STATS_ANALYSIS_EXCLUDE_CLICK: 'STATS_ANALYSIS_EXCLUDE_CLICK',

  STATS_ANALYSIS_VIRTUAL_ITEM: 'STATS_ANALYSIS_VIRTUAL_ITEM',

  STATS_ANALYSIS_TITLE_ADD: 'STATS_ANALYSIS_TITLE_ADD',
  STATS_ANALYSIS_TITLE_EDIT: 'STATS_ANALYSIS_TITLE_EDIT',
  STATS_ANALYSIS_TITLE_UPDATE: 'STATS_ANALYSIS_TITLE_UPDATE',
  STATS_ANALYSIS_TITLE_LIST_UPDATE: 'STATS_ANALYSIS_TITLE_LIST_UPDATE',

  STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_ADD:
    'STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_ADD',
  // 另存为列模板 明细
  STATS_ANALYSIS_DETAIL_TEMPLATE_SAVE_AS:
    'STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_SAVE_AS',

  STATS_ANALYSIS_METRIC_TEMPLATE_TITLE_ADD:
    'STATS_ANALYSIS_METRIC_TEMPLATE_TITLE_ADD',
  // 另存为列模板 统计
  STATS_ANALYSIS_METRICL_TEMPLATE_SAVE_AS:
    'STATS_ANALYSIS_METRIC_TEMPLATE_TITLE_SAVE_AS',

  DETAIL_COLUMN_TEMPLATE_DELETE_SUCCESS:
    'DETAIL_COLUMN_TEMPLATE_DELETE_SUCCESS',
  METRIC_COLUMN_TEMPLATE_DELETE_SUCCESS:
    'METRIC_COLUMN_TEMPLATE_DELETE_SUCCESS',
  STATS_ANALYSIS_TEMPLATE_DELETE_SUCCESS:
    'STATS_ANALYSIS_TEMPLATE_DELETE_SUCCESS',

  STATS_ANALYSIS_COLUMN_SELECT: 'STATS_ANALYSIS_COLUMN_SELECT',
  STATS_ANALYSIS_COLUMN_DESELECT: 'STATS_ANALYSIS_COLUMN_DESELECT',
  STATS_ANALYSIS_COLUMN_SELECT_RESET: 'STATS_ANALYSIS_COLUMN_SELECT_RESET',

  STATS_ANALYSIS_COLUMN_SELECT_DELETE: 'STATS_ANALYSIS_COLUMN_SELECT_DELETE',
  STATS_ANALYSIS_COLUMN_SELECT_TOP: 'STATS_ANALYSIS_COLUMN_SELECT_TOP',
  STATS_ANALYSIS_COLUMN_SELECT_BOTTOM: 'STATS_ANALYSIS_COLUMN_SELECT_BOTTOM',

  STATS_ANALYSIS_METRIC_COLUMN_SELECT: 'STATS_ANALYSIS_METRIC_COLUMN_SELECT',
  STATS_ANALYSIS_METRIC_COLUMN_SELECT_RESET:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_RESET',
  STATS_ANALYSIS_METRIC_COLUMN_SELECT_NEED_RESET:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_NEED_RESET',

  STATS_ANALYSIS_METRIC_COLUMN_ITEM_SELECT:
    'STATS_ANALYSIS_METRIC_COLUMN_ITEM_SELECT',

  STATS_ANALYSIS_METRIC_COLUMN_SELECT_COMPLETE:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_COMPLETE',

  STATS_ANALYSIS_METRIC_COLUMN_SELECT_DELETE:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_DELETE',
  STATS_ANALYSIS_METRIC_COLUMN_SELECT_TOP:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_TOP',
  STATS_ANALYSIS_METRIC_COLUMN_SELECT_BOTTOM:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_BOTTOM',

  // 病种分析
  STATS_ANALYSIS_DISEASE_DATA_SORT_CHANGE:
    'STATS_ANALYSIS_DISEASE_DATA_SORT_CHANGE',
  STATS_ANALYSIS_DISEASE_DATA_OPEN: 'STATS_ANALYSIS_DISEASE_DATA_OPEN',
  STATS_ANALYSIS_DISEASE_DATA_ADD: 'STATS_ANALYSIS_DISEASE_DATA_ADD',
  STATS_ANALYSIS_DISEASE_DATA_DELETE: 'STATS_ANALYSIS_DISEASE_DATA_DELETE',
  STATS_ANALYSIS_DISEASE_DATA_RELOAD: 'STATS_ANALYSIS_DISEASE_DATA_RELOAD',
  STATS_ANALYSIS_DISEASE_GET_EXPR: 'STATS_ANALYSIS_DISEASE_GET_EXPR',

  // 条件删除
  STATS_ANALYSIS_TABLE_CONDITION_DELETE:
    'STATS_ANALYSIS_TABLE_CONDITION_DELETE',
  // 条件新增
  STATS_ANALYSIS_TABLE_CONDITION_ADD: 'STATS_ANALYSIS_TABLE_CONDITION_ADD',

  // 条件组内 item新增
  STATS_ANALYSIS_TABLE_CONDITION_ITEM_ADD:
    'STATS_ANALYSIS_TABLE_CONDITION_ITEM_ADD',
  // 条件组内 item删除
  STATS_ANALYSIS_TABLE_CONDITION_ITEM_DELETE:
    'STATS_ANALYSIS_TABLE_CONDITION_ITEM_DELETE',

  STATS_ANALYSIS_COMBINE_QUERY_TABLE_TO_TREE:
    'STATS_ANALYSIS_COMBINE_QUERY_TABLE_TO_TREE',

  STATS_ANALYSIS_COMBINE_QUERY_TABLE_ITEM_ADD:
    'STATS_ANALYSIS_COMBINE_QUERY_TABLE_ITEM_ADD',

  STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE:
    'STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE',
  STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE_SELECT:
    'STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE_SELECT',

  STATS_ANALYSIS_COMBINE_QUERY_TABLE_RESET:
    'STATS_ANALYSIS_COMBINE_QUERY_TABLE_RESET',

  STATS_ANALYSIS_COMBINE_QUERY_TABLE_CONDITION_ADD:
    'STATS_ANALYSIS_COMBINE_QUERY_TABLE_CONDITION_ADD',

  STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE:
    'STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE',

  STATS_ANALYSIS_COMBINE_QUERY_COLUMN_CLEAR:
    'STATS_ANALYSIS_COMBINE_QUERY_COLUMN_CLEAR',

  STATS_ANALYSIS_COMBINE_QUERY_VIEW_TEXT:
    'STATS_ANALYSIS_COMBINE_QUERY_VIEW_TEXT',
};

export const linkDataSource = [
  {
    value: 'and',
    name: '并且',
  },
  {
    value: 'or',
    name: '或者',
  },
];

export const PeriodicPickerModes = {
  ByDay: {
    name: '按日',
    value: 'date',
  },
  ByMonth: {
    name: '按月',
    value: 'month',
  },
  ByQuarter: {
    name: '按季度',
    value: 'quarter',
  },
  ByYear: {
    name: '按年',
    value: 'year',
  },
};

export const PickerModes = {
  Date: {
    value: 'date',
    dateProcessor: (dates) => {
      return dates?.map((item) => {
        return dayjs(item).format('YYYY-MM-DD');
      });
    },
  },
  Month: {
    value: 'month',
    dateProcessor: (dates) => {
      return [
        dayjs(dates[0]).startOf('month').format('YYYY-MM-DD'),
        dayjs(dates[1]).endOf('month').format('YYYY-MM-DD'),
      ];
    },
  },
  Quarter: {
    value: 'quarter',
    dateProcessor: (dates) => {
      return [
        dayjs(dates[0]).startOf('quarter').format('YYYY-MM-DD'),
        dayjs(dates[1]).endOf('quarter').format('YYYY-MM-DD'),
      ];
    },
  },
  Year: {
    value: 'year',
    dateProcessor: (dates) => {
      return [
        dayjs(dates[0]).startOf('year').format('YYYY-MM-DD'),
        dayjs(dates[1]).endOf('year').format('YYYY-MM-DD'),
      ];
    },
  },
  DateTime: {
    value: 'date',
    dateProcessor: (dates) => {
      return dates?.map((item) => {
        return dayjs(item).format('YYYY-MM-DD HH:mm:ss');
      });
    },
  },
};

export const SurgeryTags = [
  { Name: '章节', Code: 'Initial' },
  { Name: '类目', Code: 'Category' },
  { Name: '亚目', Code: 'SubCategory' },
  { Name: '细目', Code: 'DetailCategory' },
];

export const DiseaseTags = [
  { Name: '章节', Code: 'Initial' },
  { Name: '类目', Code: 'Category' },
  { Name: '亚目', Code: 'SubCategory' },
  { Name: '细目', Code: 'DetailCategory' },
];
