import { useRef, useState, useEffect } from 'react';
import { useReactToPrint } from 'react-to-print';

/**
 * 处理打印相关逻辑的自定义Hook
 */
export const usePrintHandler = () => {
  // 打印组件引用
  const componentRef = useRef<any>();
  // 存储resolve Promise在`onBeforeGetContent`中使用
  const promiseResolveRef = useRef(null);
  // 打印状态
  const [isPrinting, setIsPrinting] = useState(false);

  // 监控状态变化，当Promise解析可用时
  useEffect(() => {
    if (isPrinting && promiseResolveRef.current) {
      // 解析Promise，让`react-to-print`知道DOM更新已完成
      promiseResolveRef.current();
    }
  }, [isPrinting]);

  // 处理打印动作
  const handlePrint = useReactToPrint({
    bodyClass: 'print-body',
    pageStyle: '#print_example { display: block !important; }',
    content: () => componentRef.current,
    onBeforeGetContent: () => {
      return new Promise((resolve) => {
        // 打印前的额外操作
        promiseResolveRef.current = resolve;
        setIsPrinting(true);
      });
    },
    onAfterPrint: () => {
      // 打印后的复原操作
      // 重置Promise解析，以便可以再次打印
      promiseResolveRef.current = null;
      setIsPrinting(false);
    },
  });

  return {
    componentRef,
    isPrinting,
    handlePrint,
  };
};
