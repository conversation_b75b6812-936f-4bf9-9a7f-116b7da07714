import { useEffect, useState } from 'react';
import { Col, Row, Tabs } from 'antd';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import './index.less';
import { BasePageProps, RespVO } from '@uni/commons/src/interfaces';
import _, { isNumber } from 'lodash';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { useModel } from '@@/plugin-model/useModel';
import Stats from '@/components/statsWithTrend';
import { TotalStatsColumns } from '../constants';
import SingleColumnTable from '@/components/singleColumnTable';
import PieTreeTrend from './components/pieTreeTrend/index';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import CliDeptTab from './components/cliDeptTab/index';

interface DmrManagementProps extends BasePageProps {}

const CheckResultAnalysis = (props: DmrManagementProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  let { dateRange, hospCodes, errorLevel } = globalState.searchParams;
  const [tableParams, setTableParams] = useState(undefined);

  const [pieTrendParams, setPieTrendParams] = useState(undefined);

  const [clickedRecord, setClickedRecord] = useState(undefined);

  useEffect(() => {
    if (dateRange?.length && hospCodes?.length) {
      let params = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        ErrorLevel: errorLevel,
        HospCode: hospCodes,
      };
      setTableParams(params);
    }
  }, [dateRange, hospCodes, errorLevel]);

  // useEffect(() => {
  //   if (tableParams) {
  //     getQcHospPassReq(tableParams);
  //     getQcHospScoreReq(tableParams);
  //     getQcStatsOfHospReq(tableParams);
  //     getQcStatsOfCliDeptReq(tableParams);
  //   }
  // }, [tableParams]);

  useEffect(() => {
    if (clickedRecord) {
      setPieTrendParams({
        ...tableParams,
        doctorTypes: [clickedRecord?.DoctorType],
        doctorCodes: [clickedRecord?.Doctor],
      });
    }
  }, [clickedRecord]);

  // 合格率
  const {
    data: qcPassData,
    loading: getQcPassLoading,
    run: getQcHospPassReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Emr/EmrQcReport/QcHospPassOfDept`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 合格率
  const {
    data: qcPassDataColumns,
    loading: getQcPassColumnsLoading,
    run: getQcHospPassColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Emr/EmrQcReport/QcHospPassOfDept`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor(
            [
              { dataIndex: 'HospCode', visible: false },
              { dataIndex: 'CliDept', visible: false },
            ],
            res.data?.Columns,
          );
        }
      },
    },
  );

  // 得分
  const {
    data: qcScoreData,
    loading: getQcScoreLoading,
    run: getQcHospScoreReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Emr/EmrQcReport/QcHospScoreOfDept`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data.length) {
            return res.data.map((r) => ({
              ...r,
              Score: r.Score.toFixed(2),
              AvgScore: isNumber(r.AvgScore) ? r.AvgScore.toFixed(2) : '-',
            }));
          } else return [];
        }
      },
    },
  );

  // 得分
  const {
    data: qcScoreDataColumns,
    loading: getQcScoreColumnsLoading,
    run: getQcHospScoreColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Emr/EmrQcReport/QcHospScoreOfDept`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor(
            [
              { dataIndex: 'HospCode', visible: false },
              { dataIndex: 'CliDept', visible: false },
            ],
            res.data?.Columns,
          );
        }
      },
    },
  );

  // // 全院质控
  // const {
  //   data: QcStatsOfHospData,
  //   loading: getQcStatsOfHospLoading,
  //   run: getQcStatsOfHospReq,
  // } = useRequest(
  //   (data) =>
  //     uniCommonService('Api/Emr/QcResultStats/QcStatsOfHosp', {
  //       method: 'POST',
  //       data: data,
  //     }),
  //   {
  //     manual: true,
  //     formatResult: (res: RespVO<any>) => {
  //       if (res?.code === 0 && res?.statusCode === 200) {
  //         return res.data;
  //       }
  //     },
  //   },
  // );

  // // 全院质控 Columns
  // const { data: QcStatsOfHospColumnsData, run: getQcStatsOfHospColumnsReq } =
  //   useRequest(
  //     () =>
  //       uniCommonService('Api/Emr/QcResultStats/QcStatsOfHosp', {
  //         method: 'POST',
  //         headers: {
  //           'Retrieve-Column-Definitions': 1,
  //         },
  //       }),
  //     {
  //       manual: true,
  //       formatResult: (res: RespVO<any>) => {
  //         if (res?.code === 0 && res?.statusCode === 200) {
  //           return tableColumnBaseProcessor([], res.data?.Columns);
  //         }
  //       },
  //     },
  //   );

  // // 科室质控
  // const {
  //   data: QcStatsOfCliDeptData,
  //   loading: getQcStatsOfCliDeptLoading,
  //   run: getQcStatsOfCliDeptReq,
  // } = useRequest(
  //   (data) =>
  //     uniCommonService('Api/Emr/QcResultStats/QcStatsOfCliDept', {
  //       method: 'POST',
  //       data: data,
  //     }),
  //   {
  //     manual: true,
  //     formatResult: (res: RespVO<any>) => {
  //       if (res?.code === 0 && res?.statusCode === 200) {
  //         return res.data;
  //       }
  //     },
  //   },
  // );

  // // 科室质控 Columns
  // const {
  //   data: QcStatsOfCliDeptColumnsData,
  //   run: getQcStatsOfCliDeptColumnsReq,
  // } = useRequest(
  //   () =>
  //     uniCommonService('Api/Emr/QcResultStats/QcStatsOfCliDept', {
  //       method: 'POST',
  //       headers: {
  //         'Retrieve-Column-Definitions': 1,
  //       },
  //     }),
  //   {
  //     manual: true,
  //     formatResult: (res: RespVO<any>) => {
  //       if (res?.code === 0 && res?.statusCode === 200) {
  //         return tableColumnBaseProcessor([], res.data?.Columns);
  //       }
  //     },
  //   },
  // );

  // useEffect(() => {
  // getQcHospPassColumnsReq();
  // getQcHospScoreColumnsReq();
  // getQcStatsOfHospColumnsReq();
  // getQcStatsOfCliDeptColumnsReq();
  // }, []);

  // tab
  const [activeKey, setActiveKey] = useState('Hosp');
  let tabItems = [
    {
      key: 'Hosp',
      label: '全院统计分析',
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <Stats
                api={`Api/Emr/QcResultStats/GetOverviewBundle`}
                trendApi={`Api/Emr/QcResultStats/GetOverviewTrends`}
                columns={TotalStatsColumns}
                type="col-xl-12"
                tabKey={activeKey}
                chartHeight={320}
                useGlobalState
                defaultSelectItem="RecordCnt"
              />
            </Col>
            <Col
              xs={24}
              sm={24}
              md={24}
              lg={24}
              xl={24}
              style={{ padding: '0' }}
            >
              <SingleColumnTable
                title="全院质控结果"
                args={{
                  api: 'Api/Emr/QcResultStats/QcStatsOfHosp',
                }}
                tableParams={tableParams}
                dictData={globalState?.dictData}
                category="HospName"
                type="table"
                visibleValueKeys={[
                  'HospName',
                  'RecordCnt',
                  'QcRecordCnt',
                  'RecordCheckCount',
                  'RecordCheckRate',
                  'CodeCheckCount',
                  'CodeCheckRate',
                  'ErrorCount',
                  'ErrorRate',
                  'ForceErrorCount',
                  'ForceErrorRate',
                  'IndicativeErrorCount',
                  'IndicativeErrorRate',
                ]}
                colSpan={{ span: 24 }}
                isShowDetails
                detailAction={(record) => {
                  Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                    title: '',
                    args: {
                      ...tableParams,
                      ...record?.args,
                      HospCode: record?.HospCode
                        ? [record?.HospCode]
                        : undefined,
                    },
                    detailsUrl: 'Emr/QcResultStats/GetQcCards',
                    dictData: globalState?.dictData,
                  });
                }}
              />
            </Col>
            {/* <Col span={8}>
              <CardWithBtns
                title={'全院质控结果'}
                content={
                  <UniTable
                    id={'emr-data-table'}
                    rowKey={'HospCode'}
                    scroll={{
                      x: 'max-content',
                    }}
                    columns={QcStatsOfHospColumnsData}
                    dataSource={QcStatsOfHospData}
                    loading={getQcStatsOfHospLoading}
                    clickable={true}
                    // pagination={false}
                    dictionaryData={globalState?.dictData}
                  />
                }
                needExport={true}
                exportTitle={'全院质控结果'}
                exportData={QcStatsOfHospData}
                exportColumns={QcStatsOfHospColumnsData}
                needModalDetails={true}
                onRefresh={() => {
                  getQcStatsOfHospReq(tableParams);
                }}
              />
            </Col> */}

            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <PieTreeTrend
                tableParams={tableParams}
                detailAction={(record) => {
                  Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                    title: '',
                    args: {
                      ...tableParams,
                      ...record?.args,
                    },
                    // detailsUrl: 'Dmr/DmrQcStats/GetQcCards',
                    detailsUrl: 'Emr/QcResultStats/GetQcCards',
                    dictData: globalState?.dictData,
                  });
                }}
              />
            </Col>

            {/* <QcRuleTypeAnalCard
              type={'hosp'}
              api={`Api/Emr/EmrQcReport/QcHospRuleTypeAnal`}
              hospitalCodes={hospCodes}
              Sdate={dateRange?.at(0)}
              Edate={dateRange?.at(1)}
            /> */}
          </Row>
        </>
      ),
    },
    {
      key: 'CliDept',
      label: '科室统计分析',
      children: (
        <>
          <CliDeptTab
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: '',
                args: {
                  ...tableParams,
                  cliDepts: [record?.CliDept],
                  ...record?.args,
                },
                detailsUrl: 'Emr/QcResultStats/GetQcCards',
                dictData: globalState?.dictData,
              });
            }}
            visibleValueKeys={[
              'CliDeptName',
              'RecordCnt',
              'QcRecordCnt',
              'RecordCheckCount',
              'RecordCheckRate',
              'CodeCheckCount',
              'CodeCheckRate',
              'ErrorCount',
              'ErrorRate',
              'ForceErrorCount',
              'ForceErrorRate',
              'IndicativeErrorCount',
              'IndicativeErrorRate',
            ]}
          />

          {/* <SingleColumnTable
              title="科室质控结果"
              args={{
                api: 'Api/Emr/QcResultStats/QcStatsOfCliDept',
              }}
              tableParams={tableParams}
              dictData={globalState?.dictData}
              category="CliDeptName"
              type="table"
              // TODO
              isShowDetails
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: '',
                  args: {
                    ...tableParams,
                    cliDepts: [record?.CliDept],
                    ...record?.args,
                  },
                  detailsUrl: 'Emr/QcResultStats/GetQcCards',
                  dictData: globalState?.dictData,
                });
              }}
              visibleValueKeys={[
                'CliDeptName',
                'RecordCnt',
                'QcRecordCnt',
                'RecordCheckCount',
                'RecordCheckRate',
                'CodeCheckCount',
                'CodeCheckRate',
                'ErrorCount',
                'ErrorRate',
                'ForceErrorCount',
                'ForceErrorRate',
                'IndicativeErrorCount',
                'IndicativeErrorRate',
              ]}
              colSpan={{ span: 24 }}
            />
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <PieTreeTrend
                tableParams={tableParams}
                detailAction={(record) => {
                  Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                    title: '',
                    args: {
                      ...tableParams,
                      ...record?.args,
                    },
                    // detailsUrl: 'Dmr/DmrQcStats/GetQcCards',
                    detailsUrl: 'Emr/QcResultStats/GetQcCards',
                    dictData: globalState?.dictData,
                  });
                }}
              />
            </Col> */}
          {/* <Col span={24}>
            <CardWithBtns
                title={'科室质控结果'}
                content={
                  <UniTable
                    id={'emr-data-table'}
                    rowKey={'HospCode'}
                    scroll={{
                      x: 'max-content',
                    }}
                    columns={QcStatsOfCliDeptColumnsData}
                    dataSource={QcStatsOfCliDeptData}
                    loading={getQcStatsOfHospLoading}
                    clickable={true}
                    // pagination={false}
                    dictionaryData={globalState?.dictData}
                  />
                }
                needExport={true}
                exportTitle={'科室质控结果'}
                exportData={QcStatsOfCliDeptData}
                exportColumns={QcStatsOfCliDeptColumnsData}
                needModalDetails={true}
                onRefresh={() => {
                  getQcStatsOfHospReq(tableParams);
                }}
              />
            </Col> */}
          {/* <Col span={14}>
              <CardWithBtns
                title={'各科室合格率'}
                content={
                  <UniEcharts
                    height={250}
                    elementId="Area"
                    loading={getQcPassLoading}
                    options={
                      (qcPassData !== 'apiErr' &&
                        qcPassData &&
                        qcPassByEntityLineBar(
                          _.orderBy(
                            qcPassData.map((r) => ({
                              ...r,
                              PassRate: isNumber(r.PassRate)
                                ? (r.PassRate * 100).toFixed(2)
                                : null,
                              AvgPassRate: isNumber(r.AvgPassRate)
                                ? (r.AvgPassRate * 100).toFixed(2)
                                : '-',
                            })),
                            ['PassRate'],
                            'desc',
                          ),
                          'CliDeptName',
                        )) ||
                      {}
                    }
                  />
                }
                needExport={true}
                exportTitle={'各科室合格率'}
                exportData={qcPassData}
                exportColumns={qcPassDataColumns}
                needModalDetails={true}
                onRefresh={() => {
                  getQcHospPassReq(tableParams);
                }}
              />
            </Col>
            <Col span={10}>
              <CardWithBtns
                title={'各科室合格率排名'}
                content={
                  <UniEcharts
                    height={250}
                    elementId="ListBar"
                    loading={getQcPassLoading}
                    options={
                      (qcPassData !== 'apiErr' &&
                        qcPassData &&
                        qcPassByEntityListBarOption(
                          _.orderBy(
                            qcPassData.map((r) => ({
                              ...r,
                              PassRate: isNumber(r.PassRate)
                                ? (r.PassRate * 100).toFixed(2)
                                : null,
                              AvgPassRate: isNumber(r.AvgPassRate)
                                ? (r.AvgPassRate * 100).toFixed(2)
                                : '-',
                            })),
                            ['Ranking'],
                            'asc',
                          ),
                          'CliDeptName',
                        )) ||
                      {}
                    }
                  />
                }
                needExport={true}
                exportTitle={'各科室合格率排名'}
                exportData={qcPassData}
                exportColumns={qcPassDataColumns}
                needModalDetails={true}
                onRefresh={() => {
                  getQcHospPassReq(tableParams);
                }}
              />
            </Col>
            <Col span={14}>
              <CardWithBtns
                title={'各科室平均得分'}
                content={
                  <UniEcharts
                    height={250}
                    elementId="Area"
                    loading={getQcScoreLoading}
                    options={
                      (qcScoreData !== 'apiErr' &&
                        qcScoreData &&
                        qcScoreByEntityArea(
                          _.orderBy(qcScoreData, ['Ranking'], 'asc'),
                          'CliDeptName',
                        )) ||
                      {}
                    }
                  />
                }
                needExport={true}
                exportTitle={'各科室平均得分'}
                exportData={qcScoreData}
                exportColumns={qcScoreDataColumns}
                dictData={globalState.dictData}
                needModalDetails={true}
                onRefresh={() => {
                  getQcHospScoreReq(tableParams);
                }}
              />
            </Col>
            <Col span={10}>
              <CardWithBtns
                title={'各科室平均得分排名'}
                content={
                  <UniEcharts
                    height={250}
                    elementId="ListBar"
                    loading={getQcScoreLoading}
                    options={
                      (qcScoreData !== 'apiErr' &&
                        qcScoreData &&
                        qcScoreByEntityListBarOption(
                          _.orderBy(qcScoreData, ['Ranking'], 'asc'),
                          'CliDeptName',
                        )) ||
                      {}
                    }
                  />
                }
                needExport={true}
                exportTitle={'各科室平均得分排名'}
                exportData={qcScoreData}
                exportColumns={qcScoreDataColumns}
                needModalDetails={true}
                onRefresh={() => {
                  getQcHospScoreReq(tableParams);
                }}
              />
            </Col> */}
        </>
      ),
    },
    {
      key: 'Doctor',
      label: '医生统计分析',
      children: (
        <Row gutter={[16, 16]}>
          <SingleColumnTable
            title="医生质控结果"
            args={{
              api: 'Api/Emr/QcResultStats/QcStatsOfDoctor',
            }}
            tableParams={tableParams}
            dictData={globalState?.dictData}
            category="Doctor"
            type="table"
            // TODO
            isShowDetails
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: '',
                args: {
                  ...tableParams,
                  doctorTypes: record?.DoctorType
                    ? [record?.DoctorType]
                    : undefined,
                  doctorCodes: record?.Doctor ? [record?.Doctor] : undefined,
                  ...record?.args,
                },
                detailsUrl: 'Emr/QcResultStats/GetQcCards',
                dictData: globalState?.dictData,
              });
            }}
            visibleValueKeys={[
              'DoctorName',
              'RecordCnt',
              'QcRecordCnt',
              'RecordCheckCount',
              'RecordCheckRate',
              'CodeCheckCount',
              'CodeCheckRate',
              'ErrorCount',
              'ErrorRate',
              'ForceErrorCount',
              'ForceErrorRate',
              'IndicativeErrorCount',
              'IndicativeErrorRate',
            ]}
            colSpan={{ span: 24 }}
            select={{
              dataKey: 'DoctorType',
              valueKey: 'DoctorType',
              allowClear: false,
              defaultSelect: true,
            }}
            // onRow={(record) => {
            //   setClickedRecord(record);
            // }}
            clickedRecord={clickedRecord}
            setClickedRecord={setClickedRecord}
          />
          <Col span={24}>
            <h3>
              <b>
                {clickedRecord?.Doctor
                  ? `${clickedRecord?.DoctorName} 统计分析`
                  : '请选择医生'}
              </b>
            </h3>
          </Col>
          <Col span={24}>
            <PieTreeTrend
              tableParams={pieTrendParams}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: '',
                  args: {
                    ...pieTrendParams,
                    ...record?.args,
                  },
                  detailsUrl: 'Emr/QcResultStats/GetQcCards',
                  dictData: dictData,
                });
              }}
            />
          </Col>
          {/* <CardWithBtns
              title={'医生质控结果'}
              content={
                <UniTable
                  id={'emr-data-table'}
                  rowKey={'HospCode'}
                  scroll={{
                    x: 'max-content',
                  }}
                  columns={qcHospQcResultColumnsData}
                  dataSource={qcHospQcResultData}
                  loading={getQcHospQcResultLoading}
                  clickable={true}
                  // pagination={false}
                  dictionaryData={globalState?.dictData}
                />
              }
              needExport={true}
              exportTitle={'医生质控结果'}
              exportData={qcHospQcResultData}
              exportColumns={qcHospQcResultColumnsData}
              needModalDetails={true}
              onRefresh={() => {
                getQcHospQcResultReq(tableParams);
              }}
            /> */}
        </Row>
      ),
    },
  ];

  return (
    <>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
    </>
  );
};

export default CheckResultAnalysis;
