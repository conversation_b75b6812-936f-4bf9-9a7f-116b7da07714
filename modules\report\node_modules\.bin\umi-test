#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+test@3.5.34/node_modules/@umijs/test/bin/node_modules:/mnt/c/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+test@3.5.34/node_modules/@umijs/test/node_modules:/mnt/c/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+test@3.5.34/node_modules/@umijs/node_modules:/mnt/c/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+test@3.5.34/node_modules:/mnt/c/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+test@3.5.34/node_modules/@umijs/test/bin/node_modules:/mnt/c/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+test@3.5.34/node_modules/@umijs/test/node_modules:/mnt/c/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+test@3.5.34/node_modules/@umijs/node_modules:/mnt/c/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+test@3.5.34/node_modules:/mnt/c/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/@umijs+test@3.5.34/node_modules/@umijs/test/bin/umi-test.js" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/@umijs+test@3.5.34/node_modules/@umijs/test/bin/umi-test.js" "$@"
fi
