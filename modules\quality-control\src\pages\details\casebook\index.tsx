import React, { useRef, useState } from 'react';
import { <PERSON>ton, Card, Tooltip, TableProps, Space, Divider } from 'antd';
import { useRequest } from 'umi';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { useModel } from '@@/plugin-model/useModel';
import UniTable from '@uni/components/src/table';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';
import { useUpdateEffect } from 'ahooks';
import SearchForm from '@/components/searchForm';
import { OptsProps, SearchItemsOpts } from '../formItems';
import { isEmptyObj } from '@/utils/widgets';
import { CasebookColumns } from './columns';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import cloneDeep from 'lodash/cloneDeep';
import { EventConstant } from '@uni/utils/src/emitter';
import ExportIconBtn from '@uni/components/src/backend-export';
import { isEmptyValues } from '@uni/utils/src/utils';
import pick from 'lodash/pick';
import { nextPreviousDmrInfoTableDataSetter } from '@uni/utils/src/next-dmr-utils';
import { dateRangeValueProcessor } from '@uni/components/src/date-range-with-type';
import { getSessionStorage } from '@/utils/utils';

const headerSearchForceModified =
  (window as any).externalConfig?.['common']?.headerSearchForceModified ??
  false;

const summarys = [
  {
    label: '总数',
    key: 'TotalCount',
    value: {},
  },
  {
    label: '强制性错误数',
    key: 'ForceErrorCount',
    value: {
      ForceErrorFilter: true,
    },
  },
  {
    label: '提示性错误数',
    key: 'IndicativeErrorCount',
    value: {
      IndicativeErrorFilter: true,
    },
  },
  {
    label: '首页问题数',
    key: 'RecordCheckCount',
    value: {
      RecordCheckFilter: true,
    },
  },
  {
    label: '编码问题数',
    key: 'CodeCheckCount',
    value: {
      CodeCheckFilter: true,
    },
  },
];

type SummaryItemProps = {
  className?: string;
  onClick: Function;
  item: {
    label: string;
    key: string;
  };
  count?: number;
};

const isNumberOrNonEmptyString = (value) => {
  return (
    typeof value === 'number' ||
    (typeof value === 'string' && value.trim() !== '')
  );
};

const SummaryItem = (props: SummaryItemProps) => {
  return (
    <div
      className={`summary-item-container ${props?.className}`}
      onClick={() => {
        props?.onClick && props?.onClick();
      }}
      key={props?.item?.key}
    >
      <span className={'label'}>{props?.item?.label}：</span>
      <span className={'value'}>
        {props?.count === null || props?.count === undefined
          ? '-'
          : props?.count}
      </span>
    </div>
  );
};

const RecordChechDetails = () => {
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster');

  const [searchedValue, setSearchedValue] = useState(
    () => {
      let curObj;
      if (isEmptyObj(searchParams)) {
        const obj = getSessionStorage('searchOpts');
        let currentObj = { ...obj };
        let startDate = obj?.['dateRange']?.[0];
        let endDate = obj?.['dateRange']?.[1];
        if (startDate && endDate) {
          let dates = dateRangeValueProcessor(obj, true, true);
          currentObj['Sdate'] = dates?.['Sdate'];
          currentObj['Edate'] = dates?.['Edate'];
          currentObj['dateRange'] = [dates?.['Sdate'], dates?.['Edate']];
        }
        curObj = {
          ..._.omit(currentObj),
        };
      } else {
        curObj = {
          ..._.omit(searchParams),
        };
      }
      return curObj;
    },

    //   {
    //   ..._.omit(searchParams, 'hospCode', 'hospCodes', 'OutType'),
    //   // HospCode: toArrayMode(searchParams?.hospCode ?? searchParams?.hospCodes),
    // }
  );

  const [columns, setColumns] = useState([]);

  const [summarySelectedKey, setSummarySelectedKey] = useState('TotalCount');

  const [selectedHisId, setSelectedHisId] = useState(undefined);

  const dmrInfoViewRef = useRef(null);

  const [DatasourceData, setDatasourceData] = useState([]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  // stats
  const {
    data: StatsData,
    loading: GetStatsLoading,
    run: GetStatsReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/Dmr/DmrQcReport/GetQcCardAndReviewStats', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          console.log(res);
          return res.data;
        }
      },
    },
  );

  // columns
  const {
    data: ColumnsData,
    loading: GetColumnsLoading,
    run: GetColumnsReq,
  } = useRequest(
    () =>
      uniCommonService('Api/Dmr/DmrQcReport/GetQcCardAndReviewResults', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      // manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          console.log(res.data.Columns);
          setColumns(
            tableColumnBaseProcessor(CasebookColumns, res.data.Columns),
          );
        }
      },
    },
  );

  // datasource
  const { loading: GetDatasourceLoading, run: GetDatasourceReq } = useRequest(
    (current, pageSize) =>
      uniCommonService('Api/Dmr/DmrQcReport/GetQcCardAndReviewResults', {
        method: 'POST',
        data: {
          DtParam: {
            Draw: 1,
            Start: (current - 1) * pageSize,
            Length: pageSize,
          },
          ...pick(searchedValue, Object.keys(new OptsProps())),
          ...summarySelectedKeyHandler(),
          // ...clickSubRule,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          nextPreviousDmrInfoTableDataSetter(
            res?.data,
            setDatasourceData,
            backPagination,
            setBackPagination,
          );
          return res.data.data;
        }
      },
    },
  );

  const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    GetDatasourceReq(pagi.current, pagi.pageSize);
  };

  // handle summary selected
  const summarySelectedKeyHandler = () => {
    if (summarySelectedKey) {
      let summaryItem = summarys?.find(
        (item) => item?.key === summarySelectedKey,
      );
      if (summaryItem) {
        return summaryItem.value;
      }
    }
    return {};
  };

  const flagRef = useRef(1);

  const flagRefPlus = () => {
    flagRef.current++;
  };

  useUpdateEffect(() => {
    if (!isEmptyObj(searchedValue)) {
      setBackPagination({
        ...backPagination,
        current: 1,
      });
      if (flagRef.current === 1) {
      } else {
        GetDatasourceReq(1, backPagination.pageSize);
        GetStatsReq(searchedValue);
      }
    }
  }, [searchedValue]);

  const onExtraSearchedHisIdLastOne = async () => {
    let current = backPagination.current + 1;
    setBackPagination({
      ...backPagination,
      current: current,
    });

    let nextPageData = await GetDatasourceReq(
      current,
      backPagination?.pageSize,
    );
    setSelectedHisId(undefined);

    return (
      nextPageData
        ?.map((item) => item?.HisId)
        ?.filter((item) => !isEmptyValues(item)) ?? []
    );
  };

  const onDmrInfoViewClick = (record: any) => {
    (global?.window as any)?.eventEmitter?.emit('DMR_AOD_STATUS', {
      status: true,
      hisId: record.HisId,
      instantAudit: true,
      operateNextPreviousExtraData: true,
      nextPageHisIdsReplace: true,
      extraSearchedHisIds: DatasourceData?.map((item) => item?.HisId)?.filter(
        (item) => !isEmptyValues(item),
      ),
      onExtraSearchedHisIdLastOne: onExtraSearchedHisIdLastOne,
      onExtraSearchedHisIdChange: (hisId: string) => {
        setSelectedHisId(hisId);
      },
    });
  };

  React.useImperativeHandle(dmrInfoViewRef, () => {
    return {
      onDmrInfoViewClick: (record) => {
        onDmrInfoViewClick(record);
      },
    };
  });

  return (
    <>
      <Card style={{ marginBottom: '20px' }}>
        <SearchForm
          className="casebook_flex_form"
          grid
          rowProps={{ gutter: [16, 16] }}
          searchOpts={[
            ...SearchItemsOpts(searchedValue, {
              hospOpts: dictData.Hospital,
              deptOpts: dictData.CliDepts,
              coderOpts: dictData?.['Dmr']?.Coder,
            }),
            {
              title: '离院方式',
              dataType: 'select',
              name: 'OutType',
              opts: dictData?.['Dmr']?.LYFS,
              initialValue: searchedValue?.OutType,
              fieldProps: {},
              colProps: { span: 6 },
            },
          ]}
          fetchCallback={(data) => {
            console.log('fetchCallback', data);
          }}
          submitter={{
            render: (props, doms) => {
              return [
                <Button
                  type="primary"
                  style={{
                    width: '150px',
                    float: 'right',
                  }}
                  key="reset"
                  onClick={() => {
                    props.form?.submit?.();
                  }}
                >
                  查询
                </Button>,
              ];
            },
          }}
          onFinish={async (values) => {
            flagRefPlus();
            let dates = dateRangeValueProcessor(values, true, true);
            values['Sdate'] = dates?.['Sdate'];
            values['Edate'] = dates?.['Edate'];
            values['dateRange'] = [dates?.['Sdate'], dates?.['Edate']];

            let searchValues = {
              ...values,
              ...dates,
              UseCodeTimeFilter:
                values.DateType === 'RegisterDate' ? true : false,
            };
            setSearchedValue(
              headerSearchForceModified === false
                ? cloneDeep(searchValues)
                : searchValues,
            );
            (global?.window as any)?.eventEmitter?.emit(
              EventConstant.HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY,
              {
                ...searchValues,
                searchNow: headerSearchForceModified === false,
              },
            );

            return true;
          }}
        />
      </Card>
      <Card
        title="病案质控明细"
        extra={
          <Space>
            <Divider type="vertical" />
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: 'Api/Dmr/DmrQcReport/ExportGetQcCardAndReviewResults',
                method: 'POST',
                data: {
                  ...searchedValue,
                  ...summarySelectedKeyHandler(),
                },
                fileName: `病案质控明细`,
              }}
              btnDisabled={DatasourceData?.length < 1}
            />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl:
                  'Api/Dmr/DmrQcReport/GetQcCardAndReviewResults',
                onTableRowSaveSuccess: (columns) => {
                  setColumns(
                    tableColumnBaseProcessor(CasebookColumns, columns),
                  );
                },
              }}
            />
          </Space>
        }
      >
        <Space>
          {summarys?.map((item) => {
            return (
              <SummaryItem
                className={
                  item?.key === summarySelectedKey ? 'card-selected' : ''
                }
                item={item}
                count={StatsData?.[item.key]}
                onClick={() => {
                  setSummarySelectedKey(item.key);

                  setTimeout(() => {
                    if (!isEmptyObj(searchedValue)) {
                      setBackPagination({
                        ...backPagination,
                        current: 1,
                      });
                      if (isNumberOrNonEmptyString(StatsData?.[item.key])) {
                        GetDatasourceReq(1, backPagination.pageSize);
                      }
                    }
                  }, 0);
                }}
              />
            );
          })}
        </Space>
        <UniTable
          id="casebook-table"
          className="casebook_table"
          rowKey="aguid"
          loading={GetDatasourceLoading}
          rowClassName={(record, index) => {
            return !isEmptyValues(record?.HisId) &&
              record?.HisId === selectedHisId
              ? 'table-hisId-row-selected'
              : '';
          }}
          columns={
            (columns.length && [
              {
                title: '',
                colSize: 0.5,
                width: 50,
                fixed: 'left',
                align: 'center',
                visible: true,
                valueType: 'option',
                render: (_, record) => {
                  return [
                    <Tooltip key="watch" title="查看病案明细">
                      <div
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          dmrInfoViewRef?.current?.onDmrInfoViewClick(record);
                        }}
                      >
                        <svg
                          width="22"
                          height="22"
                          viewBox="0 0 48 48"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <rect
                            x="6"
                            y="6"
                            width="36"
                            height="36"
                            rx="3"
                            fill="none"
                            stroke="#1464f8"
                            strokeWidth="4"
                            strokeLinejoin="round"
                          />
                          <rect
                            x="13"
                            y="13"
                            width="8"
                            height="8"
                            fill="none"
                            stroke="#1464f8"
                            strokeWidth="4"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M27 13L35 13"
                            stroke="#1464f8"
                            strokeWidth="4"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M27 20L35 20"
                            stroke="#1464f8"
                            strokeWidth="4"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M13 28L35 28"
                            stroke="#1464f8"
                            strokeWidth="4"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M13 35H35"
                            stroke="#1464f8"
                            strokeWidth="4"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </Tooltip>,
                  ];
                },
              },
              ...columns,
            ]) ||
            []
          }
          dictionaryData={dictData}
          dataSource={DatasourceData ?? []}
          pagination={DatasourceData?.length ? backPagination : false}
          onChange={backTableOnChange}
          // scroll={{ x: 'max-content' }}
        />
      </Card>
    </>
  );
};

export default RecordChechDetails;
