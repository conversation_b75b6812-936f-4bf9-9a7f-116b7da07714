import {
  UniDragEditTable,
  UniInput,
  UniInputNumber,
  UniInputNumberRange,
  UniSelect,
  UniTable,
  UniCheckbox,
  UniDateRadioPicker,
  Switch,
  UniSelectTextInput,
} from '@uni/components/src';
import { DatePicker, Input, InputNumber } from 'antd';
import UniNull from '@uni/components/src/empty';
import {
  UniHeaderFormDatePicker,
  UniHeaderFormRangePicker,
  UniHeaderFormSelect,
} from '@/layouts/form-datepicker';
import UniDependencySelect from '@uni/components/src/select/dependency-select';

export const dynamicComponentsMap = {
  UniSelect: UniSelect,
  UniConstrainedSelect: UniHeaderFormSelect,
  UniDatePicker: UniHeaderFormDatePicker,
  UniRangePicker: UniHeaderFormRangePicker,
  UniInput: UniInput,
  UniInputNumber: UniInputNumber,
  UniInputNumberRange,
  UniTable: UniTable,
  UniCheckbox,
  UniSwitch: Switch,
  // 空组件
  UniNull: UniNull,

  // 拖动table
  UniDragEditTable: UniDragEditTable,

  UniDateRadioPicker,

  UniDependencySelect: UniDependencySelect,
  UniConstrainedDependencySelect: UniDependencySelect,
  UniSelectTextInput,
};
