import './index.less';
import React, { useEffect, useState } from 'react';
import {
  pivotColumns,
  TableColumnFullProperties,
  tableColumnPropertiesToColumns,
  TableColumnsApiRedirectionColumns,
  tableDataSourceProcessor,
  tableEditColumns,
} from './columns';
import { UniDragEditTable, UniSelect, UniTable } from '../../index';
import cloneDeep from 'lodash/cloneDeep';
import { Button, Form, message, Modal, Space, Tooltip } from 'antd';
import { isEmptyValues } from '@uni/utils/src/utils';
import { useRequest } from 'ahooks';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { TableAction } from '@uni/reducers/src';
import { SettingOutlined } from '@ant-design/icons';
import mergeWith from 'lodash/mergeWith';
import pick from 'lodash/pick';
import unset from 'lodash/unset';
import { ActionType } from '@ant-design/pro-table';
import { v4 as uuidv4 } from 'uuid';

interface TableColumnEditModalProps {
  form?: any;
  columnInterfaceUrl?: string;
  fetchMethod?: string;
  saveMethod?: string;
  columnDataSource?: any[];
  onTableRowEdit?: (columns: any[]) => void;
  onTableRowEditConfirm?: (columns: any[]) => void;
  onTableRowSaveSuccess?: (columns?: any[]) => void;

  // 是否启用pivot
  enablePivot?: boolean;
}

interface TableRowConfigContainerProps extends TableColumnEditModalProps {
  actionRef: any;
  tableColumnsDataSource?: any[];
  tableColumnLoading?: boolean;

  columnDefinitionData?: any;

  columnEditTableType?: string;

  onDataSourceChange: (newDataSource: any[]) => void;
}

const TableColumnEditModal = (props: TableColumnEditModalProps) => {
  const [tableColumnsEditOpen, setTableColumnsEditOpen] =
    React.useState<boolean>(false);

  const [form] = Form.useForm();

  const [modalEventData, setModalEventData] = React.useState<any>({});

  useEffect(() => {
    (global?.window as any)?.eventEmitter?.on(
      EventConstant.TABLE_COLUMN_EDIT,
      (data) => {
        setModalEventData(data);
        setTableColumnsEditOpen(true);
      },
    );

    return () => {
      (global?.window as any)?.eventEmitter?.off(
        EventConstant.TABLE_COLUMN_EDIT,
      );
    };
  }, []);

  const onTableSaveCompleteSetTableParameters = (columns: any[]) => {
    let sortedInfo = {};
    columns?.forEach((columnItem) => {
      // 现在当且仅当一个
      if (
        columnItem?.orderPriority !== 0 &&
        !isEmptyValues(columnItem?.orderMode)
      ) {
        sortedInfo['columnKey'] = columnItem?.data ?? columnItem?.dataIndex;
        sortedInfo['field'] = columnItem?.data ?? columnItem?.dataIndex;
        sortedInfo['order'] = columnItem?.orderMode;
      }
    });

    // if(!isEmptyValues(sortedInfo)) {
    //   if (isEmptyValues(global['tableParameters'])) {
    //     global['tableParameters'] = {};
    //   }
    //   global['tableParameters']['sorter'] = sortedInfo;
    //
    //   Emitter.emit(EventConstant.TABLE_COLUMN_SORTER_EDIT, sortedInfo);
    // }
  };

  const {
    data: tableColumnData,
    loading: tableColumnSaveLoading,
    run: tableColumnSaveReq,
  } = useRequest(
    async (columns, extra) => {
      let headers = {
        'Modify-Column-Definitions': 1,
        'Modify-Api-Redirection': '',
      };

      if (extra?.columnEditTableType === 'REPORT_API_REDIRECTION') {
        headers['Modify-Api-Redirection'] = extra?.reportMasterSelectedItemId;
      }

      let params = {
        ColumnsJson: modalEventData?.saveMethod === 'GET' ? columns : undefined,
      };

      let data = {
        ColumnsJson: columns,
      };

      if (extra?.columnEditTableType === 'REPORT_API_REDIRECTION') {
        params = {};
        data = {};
      }

      const res: RespVO<any> = await uniCommonService(
        props?.columnInterfaceUrl ?? modalEventData?.columnInterfaceUrl,
        {
          method: modalEventData?.saveMethod ?? 'POST',
          headers: headers,
          params: params,
          data: data,
        },
      );
      return res;
    },
    {
      manual: true,
      onSuccess: (data) => {
        if (data?.code === 0 && data?.statusCode === 200) {
          setTableColumnsEditOpen(false);
          props?.onTableRowSaveSuccess &&
            props?.onTableRowSaveSuccess(data?.data?.Columns);
          modalEventData?.onTableRowSaveSuccess &&
            modalEventData?.onTableRowSaveSuccess(
              data?.data?.Columns?.map((d) => ({
                ...d,
                dataType:
                  d?.dataType ?? data?.data?.DictColumns?.[d.data]?.dataType,
                columnType:
                  d?.columnType ??
                  data?.data?.DictColumns?.[d.data]?.columnType,
              })),
            );

          // 设定 当前 的一个global tableParameters
          onTableSaveCompleteSetTableParameters(data?.data?.Columns);
          message.success('保存成功');
        } else {
          message.error('保存失败');
        }
      },
      // formatResult: (response: RespVO<TableColumns>) => {
      //   return response;
      // },
    },
  );

  return (
    <Modal
      zIndex={1001}
      title={
        <Space>
          <span>表格列属性修改</span>
          <Button onClick={(e) => tableColumnSaveReq('', {})}>重置列</Button>
        </Space>
      }
      open={tableColumnsEditOpen}
      width={1300}
      okText={'确定'}
      cancelText={'取消'}
      wrapClassName={'table-columns-configuration-container'}
      onOk={async (event) => {
        console.log('form items', (props?.form ?? form).getFieldsValue());

        let formValues = cloneDeep((props?.form ?? form).getFieldsValue());

        if (formValues?.['columnEditTableType'] === 'REPORT_API_REDIRECTION') {
          // 当且仅当 REPORT_API_REDIRECTION 并且没选的时候 要提示错误
          let reportApiRedirectionId =
            formValues?.['reportMasterSelectedItemId'];
          if (isEmptyValues(reportApiRedirectionId)) {
            message.error('请选择自定义报表用于表格配置');
            return;
          }
        }

        let columns = [];
        Object.keys(formValues).forEach((key) => {
          let item = cloneDeep(formValues?.[key]);
          let tempOriginal = item?.['sort'];
          if (item?.['sort']) {
            delete item?.['sort'];
          }

          // orderPriority 处理
          if (typeof item?.['orderPriority'] === 'boolean') {
            if (item?.['orderPriority'] === true) {
              item['orderPriority'] = 1;
            } else {
              item['orderPriority'] = 0;
            }
          }

          let extraProperties = tempOriginal?.extraProperties ?? {};
          extraProperties['columnTypeExtra'] = item?.columnTypeExtra;

          if (item?.exportable === true) {
            if (!tempOriginal?.className?.includes('exportable')) {
              tempOriginal['className'] += ' exportable';
            }
          } else if (item?.exportable === false) {
            tempOriginal?.className?.replace('exportable', '')?.trim();
          }

          // 后端需要那些没做/无法做修改的值
          columns.push({
            ...tempOriginal,
            ...item,
            extraProperties: extraProperties,
          });
        });
        props?.onTableRowEditConfirm && props?.onTableRowEditConfirm(columns);

        let saveExtra = {
          columnEditTableType:
            formValues?.['columnEditTableType'] ?? 'ORIGIN_API',
          reportMasterSelectedItemId:
            formValues?.['reportMasterSelectedItemId'] ?? '',
        };

        // 保存 列修改
        if (
          !isEmptyValues(
            props?.columnInterfaceUrl ?? modalEventData?.columnInterfaceUrl,
          )
        ) {
          tableColumnSaveReq(columns, saveExtra);
        } else {
          setTableColumnsEditOpen(false);
        }
      }}
      onCancel={(event) => {
        setTableColumnsEditOpen(false);
      }}
      confirmLoading={tableColumnSaveLoading}
      destroyOnClose={true}
      getContainer={document.getElementById('dmr-configuration-wrapper')}
    >
      <TableColumnEditContainer
        {...props}
        form={props?.form ?? form}
        {...modalEventData}
      />
    </Modal>
  );
};

const columnEditTableTypes = [
  {
    label: '使用原始API',
    value: 'ORIGIN_API',
  },
  {
    label: '使用自定义报表',
    value: 'REPORT_API_REDIRECTION',
  },
];

const TableColumnEditContainer = (props: TableRowConfigContainerProps) => {
  const [columnEditTableType, setColumnEditTableType] = useState(undefined);

  const [columnDefinitionData, setColumnDefinitionData] = useState(undefined);

  const [tableColumnsDataSource, setTableColumnsDataSource] = React.useState<
    any[]
  >([]);

  const tableColumnAddActionRef = React.useRef<ActionType>();

  useEffect(() => {
    if (!isEmptyValues(props?.columnDataSource)) {
      setTableColumnsDataSource(
        tableDataSourceProcessor(props?.columnDataSource ?? []),
      );
    } else if (!isEmptyValues(props?.columnInterfaceUrl)) {
      tableColumnReq();
    } else {
      setTableColumnsDataSource([]);
    }
  }, [props?.columnDataSource, props?.columnInterfaceUrl]);

  const { loading: tableColumnLoading, run: tableColumnReq } = useRequest(
    async () => {
      const response: RespVO<any> = await uniCommonService(
        props?.columnInterfaceUrl,
        {
          method: props?.fetchMethod ?? 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
            'Retrieve-Api-Redirection': 1,
          },
        },
      );
      if (response.code === 0) {
        let columnEditTableType = response?.data?.ApiRedirection
          ? 'REPORT_API_REDIRECTION'
          : 'ORIGIN_API';
        setColumnEditTableType(columnEditTableType);
        setTableColumnsDataSource(
          tableDataSourceProcessor(
            response?.data?.OriginColumns ?? response?.data?.Columns ?? [],
          ),
        );
        setColumnDefinitionData(response?.data);
      } else {
        setTableColumnsDataSource([]);
      }
    },
    {
      manual: true,
    },
  );

  return (
    <div className={'table-columns-edit-container'}>
      <div className={'table-column-type-container'}>
        <UniSelect
          dataSource={columnEditTableTypes}
          allowClear={false}
          placeholder={'请选择'}
          value={columnEditTableType}
          onChange={(value) => {
            setColumnEditTableType(value);
          }}
        />

        {columnEditTableType === 'ORIGIN_API' && (
          <Button
            className={'column-add'}
            type={'primary'}
            onClick={() => {
              let recordId = uuidv4();
              tableColumnAddActionRef?.current?.addEditRecord(
                {
                  id: recordId,
                  order: tableColumnsDataSource?.length + 1,
                  columnTypeExtra: 'CUSTOM_ADD',
                },
                { newRecordType: 'dataSource' },
              );

              // 滚动到底
              setTimeout(() => {
                let scrollHeight = document?.querySelector(
                  '#table-columns-edit-table .ant-table-body',
                )?.scrollHeight;
                if (!isEmptyValues(scrollHeight)) {
                  document
                    ?.querySelector('#table-columns-edit-table .ant-table-body')
                    ?.scrollTo({
                      top: scrollHeight,
                      behavior: 'smooth',
                    });

                  const dataIndexInput = document?.querySelector(
                    `#table-columns-edit-table tr[data-row-key='${recordId}'] td:nth-child(4) input`,
                  ) as any;
                  dataIndexInput?.focus();
                }
              }, 300);
            }}
          >
            新增表格列
          </Button>
        )}
      </div>

      {columnEditTableType === 'REPORT_API_REDIRECTION' && (
        <ApiRedirectionReportSelectTableContainer
          {...props}
          columnDefinitionData={columnDefinitionData}
          columnEditTableType={columnEditTableType}
        />
      )}
      {columnEditTableType === 'ORIGIN_API' && (
        <ApiColumnsEditTableContainer
          {...props}
          actionRef={tableColumnAddActionRef}
          tableColumnsDataSource={tableColumnsDataSource}
          tableColumnLoading={tableColumnLoading}
          onDataSourceChange={(newDataSource: any[]) => {
            setTableColumnsDataSource(newDataSource.slice());
          }}
        />
      )}
    </div>
  );
};

const ApiColumnsEditTableContainer = (props: TableRowConfigContainerProps) => {
  const [tableColumnsDataSource, setTableColumnsDataSource] = React.useState<
    any[]
  >([]);
  const tableOriginApiContainerRef = React.useRef(null);

  useEffect(() => {
    setTableColumnsDataSource(props?.tableColumnsDataSource);
  }, [props?.tableColumnsDataSource]);

  React.useImperativeHandle(tableOriginApiContainerRef, () => {
    return {
      deleteRecord: (recordId: string) => {
        let deleteIndex = tableColumnsDataSource?.findIndex(
          (item) => item?.id === recordId,
        );
        if (deleteIndex > -1) {
          tableColumnsDataSource.splice(deleteIndex, 1);
          setTableColumnsDataSource(tableColumnsDataSource.slice());
        }
      },
    };
  });

  return (
    <UniDragEditTable
      actionRef={props?.actionRef}
      overrideContainerClassName={'table-columns-edit-table-container'}
      className={'table-columns-edit-table'}
      bordered={false}
      form={props?.form}
      key={'table-columns-edit-table'}
      id={'table-columns-edit-table'}
      tableId={'table-columns-edit-table'}
      scroll={{
        y: 400,
        x: 'max-content',
      }}
      loading={props?.tableColumnLoading}
      pagination={false}
      forceColumnsUpdate={true}
      columns={[
        ...tableEditColumns(tableOriginApiContainerRef),
        ...tableColumnPropertiesToColumns(
          TableColumnFullProperties?.concat(
            props?.enablePivot === true ? pivotColumns : [],
          ),
        ),
      ]}
      value={tableColumnsDataSource}
      rowKey={'id'}
      onValuesChange={(recordList: any[]) => {
        props?.onTableRowEdit && props?.onTableRowEdit(recordList);
      }}
      onChange={(values: any[]) => {
        setTableColumnsDataSource(values?.slice());
      }}
      onTableDataSourceOrderChange={(tableData) => {
        let formValues = props?.form?.getFieldsValue();
        tableData?.forEach((item, index) => {
          formValues[item?.id]['order'] = index + 1;

          // 设定tableData 与 form Values 合并
          let currentFormItem = formValues[item?.id];
          mergeWith(
            item,
            pick(
              currentFormItem,
              TableColumnFullProperties?.map((item) => item?.key),
            ),
            (objValue, srcValue, key, object) => {
              if (srcValue == null) {
                unset(object, key);
              }
            },
          );
        });
        props?.form.setFieldsValue({
          ...formValues,
        });

        setTableColumnsDataSource(tableData);

        props?.onTableRowEdit && props?.onTableRowEdit(tableData);
      }}
    />
  );
};

const ApiRedirectionReportSelectTableContainer = (
  props: TableRowConfigContainerProps,
) => {
  const [columnReportMasterItems, setColumnReportMasterItems] = useState([]);

  const [columnReportMasterSelectedItems, setColumnReportMasterSelectedItems] =
    useState([]);

  useEffect(() => {
    if (props?.columnDefinitionData?.ApiRedirection) {
      setColumnReportMasterSelectedItems([
        props?.columnDefinitionData?.ApiRedirection,
      ]);
    }
  }, [props?.columnDefinitionData]);

  useEffect(() => {
    props?.form?.setFieldValue(
      'columnEditTableType',
      props?.columnEditTableType,
    );
  }, [props?.columnEditTableType]);

  useEffect(() => {
    props?.form?.setFieldValue(
      'reportMasterSelectedItemId',
      columnReportMasterSelectedItems?.at(0) ?? '',
    );
  }, [columnReportMasterSelectedItems]);

  useEffect(() => {
    reportMastersReq();
  }, []);

  const { loading: reportMastersLoading, run: reportMastersReq } = useRequest(
    () => {
      return uniCommonService('Api/Report/Report/GetRestrictedReportMasters', {
        params: {
          AppCode: 'ApiRedirection',
        },
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setColumnReportMasterItems(
            response?.data?.filter((item) => item?.IsHidden !== true),
          );
        } else {
          setColumnReportMasterItems([]);
        }
      },
    },
  );

  return (
    <>
      <Form form={props?.form} preserve={false}>
        <Form.Item hidden={true} name={'columnEditTableType'} />
        <Form.Item hidden={true} name={'reportMasterSelectedItemId'} />
      </Form>
      <UniTable
        id={'column-edit-report-select-table'}
        rowKey={'Id'}
        scroll={{
          x: 'max-content',
        }}
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys: columnReportMasterSelectedItems,
          hideSelectAll: true,
          onSelect: (record, selected, selectedRows, nativeEvent) => {
            if (selected) {
              setColumnReportMasterSelectedItems([record?.Id]);
            } else {
              setColumnReportMasterSelectedItems([]);
            }
          },
        }}
        loading={reportMastersLoading}
        columns={TableColumnsApiRedirectionColumns}
        dataSource={columnReportMasterItems}
        pagination={false}
        toolBarRender={null}
      />
    </>
  );
};

export interface TableColumnEditButtonProps extends TableColumnEditModalProps {
  title?: string;
}

const TableColumnEditButton = (props: TableColumnEditButtonProps) => {
  let userInfoString = sessionStorage.getItem('userInfo');
  let userInfo = JSON.parse(userInfoString);

  if (!userInfo?.Roles?.includes('Admin')) {
    return <></>;
  }

  return (
    <Tooltip title={props?.title ?? '表格配置'}>
      <Button
        type="text"
        shape="circle"
        icon={<SettingOutlined className="infinity_rotate" />}
        onClick={(e) => {
          e.stopPropagation();
          (global?.window as any)?.eventEmitter?.emit(
            EventConstant.TABLE_COLUMN_EDIT,
            { ...props },
          );
        }}
      />
      {/* <Button
        onClick={(e) => {
          (global?.window as any)?.eventEmitter?.emit(
            EventConstant.TABLE_COLUMN_EDIT,
            { ...props },
          );
        }}
      >
        {props?.title ?? '列配置'}
      </Button> */}
    </Tooltip>
  );
};

export {
  TableColumnEditModal,
  TableColumnEditContainer,
  TableColumnEditButton,
};
