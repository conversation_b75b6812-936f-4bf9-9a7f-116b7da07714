import { CheckCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { Space, Tooltip } from 'antd';

export const SingleSignInColumns = [
  {
    data: 'isCorrect',
    dataIndex: 'isCorrect',
    visible: true,
    render: (text: string, record: any) => {
      return (
        <>
          {record?.errMsg ? (
            <Tooltip title={record?.errMsg?.join('。/n')}>
              <WarningOutlined />
            </Tooltip>
          ) : (
            <CheckCircleOutlined />
          )}
        </>
      );
    },
    order: 1,
  },
  {
    data: 'xuhao',
    dataIndex: 'xuhao',
    title: '序号',
    visible: true,
    align: 'center',
    render: (text, record, index) => {
      return index + 1;
    },
    order: 2,
  },
  {
    dataIndex: 'BarCode',
    title: '条码号',
    visible: true,
    className: 'exportable',
    // render: (text: string, record: any) => {
    //   return (
    //     <Space>
    //       {record?.errMsg && <WarningOutlined />}
    //       <span>{text}</span>
    //     </Space>
    //   );
    // },
  },
  {
    dataIndex: 'IsWardSignedOut',
    fixed: 'right',
    order: 10,
    width: 88,
  },
];
