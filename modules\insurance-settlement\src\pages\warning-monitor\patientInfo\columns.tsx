import TableLikeSelect from '@/components/tableLikeSelect';
import { Emitter } from '@uni/utils/src/emitter';
import { Tag } from 'antd';
import { EventConstants } from './constant';

export const DiagColumns = [
  {
    dataIndex: 'IcdeSort',
    title: '诊断',
    visible: true,
    width: 80,
    align: 'center',
    render: (node, record, index) => {
      let labelNode = null;
      if (record?.Id === 'ADD') {
        labelNode = <Tag style={{ margin: 0 }}>{`新增`}</Tag>;
      } else if (index === 0) {
        labelNode = (
          <Tag style={{ margin: 0 }} color="blue">
            主诊
          </Tag>
        );
      } else {
        labelNode = (
          <Tag style={{ margin: 0 }} color="magenta">{`次诊${index}`}</Tag>
        );
      }
      return labelNode;
    },
  },
  {
    dataIndex: 'IcdeCode',
    title: '国临编码',
    visible: true,
    render: (node, record, index) => {
      return (
        <TableLikeSelect
          value={record?.IcdeCode}
          apiUrl={'Api/Dmr/DmrSearch/Icde'}
          apiParams={{
            HasInsurCompare: true,
            HasHqmsCompare: true,
            HasDrgsCompare: true,
            IsDscg: true,
          }}
          onChange={(value) => {
            Emitter.emit(EventConstants.ICDE_TABLE_LIKE_CLEAR, {
              record,
              index,
            });
          }}
          tableProps={{
            rowKey: 'Code',
            showHeader: false,
            id: 'sanit_icde_code_table',
            columns: IcdeTableLikeSelectColumns,
            onRow: (rowRecord) => {
              return {
                onClick: (e) => {
                  Emitter.emit(EventConstants.ICDE_TABLE_LIKE_ROW_CLICK, {
                    rowRecord,
                    record,
                    index,
                  });
                  console.log(rowRecord, index);
                },
              };
            },
          }}
        />
      );
    },
  },
  {
    dataIndex: 'IcdeName',
    title: '国临名称',
    visible: true,
  },
  {
    dataIndex: 'InsurCode',
    title: '医保编码',
    visible: true,
  },
  {
    dataIndex: 'InsurName',
    title: '医保名称',
    visible: false,
  },
];

export const OperColumns = [
  {
    dataIndex: 'OperSort',
    title: '手术',
    visible: true,
    width: 80,
    align: 'center',
    render: (node, record, index) => {
      let labelNode = null;
      if (record?.Id === 'ADD') {
        labelNode = <Tag style={{ margin: 0 }}>{`新增`}</Tag>;
      } else {
        labelNode = (
          <Tag style={{ margin: 0 }} color="cyan">{`手术${index + 1}`}</Tag>
        );
      }
      return labelNode;
    },
  },
  {
    dataIndex: 'OperCode',
    title: '国临编码',
    visible: true,
    render: (node, record, index) => {
      return (
        <TableLikeSelect
          value={record?.OperCode}
          apiUrl={'Api/Dmr/DmrSearch/Oper'}
          apiParams={{
            HasInsurCompare: true,
            HasHqmsCompare: true,
            HasDrgsCompare: true,
            IsDscg: true,
          }}
          onChange={(value) => {
            Emitter.emit(EventConstants.OPER_TABLE_LIKE_CLEAR, {
              record,
              index,
            });
          }}
          tableProps={{
            rowKey: 'Code',
            showHeader: false,
            id: 'sanit_oper_code_table',
            columns: OperTableLikeSelectColumns,
            onRow: (rowRecord) => {
              return {
                onClick: (e) => {
                  Emitter.emit(EventConstants.OPER_TABLE_LIKE_ROW_CLICK, {
                    rowRecord,
                    record,
                    index,
                  });
                  console.log(rowRecord, index);
                },
              };
            },
          }}
        />
      );
    },
  },
  {
    dataIndex: 'OperName',
    title: '国临名称',
    visible: true,
  },
  {
    dataIndex: 'InsurCode',
    title: '医保编码',
    visible: true,
  },
  {
    dataIndex: 'InsurName',
    title: '医保名称',
    visible: false,
  },
];

export const IcdeTableLikeSelectColumns = [
  {
    dataIndex: 'Code',
    title: '编码',
    visible: true,
  },
  {
    dataIndex: 'Name',
    title: '名称',
    visible: true,
  },
];

export const OperTableLikeSelectColumns = [
  {
    dataIndex: 'Code',
    title: '编码',
    visible: true,
  },
  {
    dataIndex: 'Name',
    title: '名称',
    visible: true,
  },
];

export const GroupFeeSummaryColumns = [
  {
    dataIndex: 'Code',
    title: '编码',
    visible: false,
  },
  {
    dataIndex: 'Name',
    title: '项目',
    visible: true,
  },
  {
    dataIndex: 'NowFeeSumamt',
    title: '当前',
    align: 'right',
    visible: true,
  },
  {
    dataIndex: 'HistoryFeeSumant',
    title: '历史',
    align: 'right',
    visible: true,
  },
];

export const GroupFeeNowItemColumns = [
  {
    dataIndex: 'ChargeCode',
    title: '编码',
    visible: false,
  },
  {
    dataIndex: 'ChargeName',
    title: '项目',
    visible: true,
  },
  {
    dataIndex: 'Cnt',
    title: '数量',
    visible: true,
  },
  {
    dataIndex: 'DetItemFeeSumamt',
    title: '金额',
    visible: true,
  },
  {
    dataIndex: 'HisMedChrgitmType',
    title: '项目类型',
    visible: false,
  },
];

export const GroupFeeHistoryItemColumns = [];
