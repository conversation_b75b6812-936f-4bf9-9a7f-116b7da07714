@import '~@uni/commons/src/style/variables.less';

.check-result-container .ant-card {
  .ant-pro-card-body {
    padding: 0;
  }

  .ant-pro-table-list-toolbar-title {
    font-size: 17px;
    font-weight: 600;
  }

  .ant-collapse {
    height: 578px;
    overflow: auto;

    .ant-collapse-header-text {
      font-size: 15px;
    }

    .detailBtn {
      width: 100%;
      display: flex;
      justify-content: space-between;

      &:active,
      &:visited,
      &:focus {
        color: #fff;
        background-color: @blue-color;
        box-shadow: 0 2px 0 rgb(5 145 255 / 10%);
      }
    }
  }
  .text-right {
    text-align: right;
  }
  .border-right {
    border-right: 1px solid @card-border-color;
  }
}
#matrix-table-医生质控结果 {
  .row-selected {
    background-color: #f3f5fe;
  }
}
.settle-comp-stats-by-adrg-table {
  .row-selected {
    background-color: #f3f5fe;
  }
}

#matrix-table-科室质控结果 {
  .row-selected {
    background-color: #f3f5fe;
  }
}
