import React, { useContext, useEffect, useState } from 'react';
import { Checkbox } from 'antd';
import { Emitter } from '@uni/utils/src/emitter';
import './index.less';

interface RowSelectionHeaderProps {
  tableId?: string;
  onSelectAll?: (checked: boolean) => void;
}

export const RowSelectionHeader = (props: RowSelectionHeaderProps) => {
  const [checked, setChecked] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);

  // 这个组件将作为 column title 使用，无法直接访问表格的内部 form
  // 我们使用一个简化的实现，主要依靠事件通信

  const handleSelectAll = (e: any) => {
    const isChecked = e.target.checked;
    setChecked(isChecked);
    setIndeterminate(false);

    const tableId = props.tableId || 'diagnosisTable';
    // 发送全选事件，让表格组件处理具体的选中逻辑
    Emitter.emit(`DMR_ROW_SELECTION_SELECT_ALL_${tableId}`, {
      tableId: tableId,
      checked: isChecked,
    });

    // 调用回调
    if (props.onSelectAll) {
      props.onSelectAll(isChecked);
    }
  };

  useEffect(() => {
    const tableId = props.tableId || 'diagnosisTable';
    const stateUpdateEvent = `DMR_ROW_SELECTION_STATE_UPDATE_${tableId}`;
    
    // 监听表格选中状态变化
    const updateSelectionState = (data: any) => {
      setChecked(data.allSelected);
      setIndeterminate(data.indeterminate);
    };

    Emitter.on(stateUpdateEvent, updateSelectionState);

    return () => {
      Emitter.off(stateUpdateEvent);
    };
  }, [props.tableId]);

  return (
    <Checkbox
      className="mr-left-4"
      checked={checked}
      indeterminate={indeterminate}
      onChange={handleSelectAll}
    />
  );
};
