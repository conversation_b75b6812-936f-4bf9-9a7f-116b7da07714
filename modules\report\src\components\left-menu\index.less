@import '~@uni/commons/src/style/variables.less';

.report-container {
  .ant-card-body {
    padding: 14px;
    height: calc(100% - 65px);
  }

  .ant-drawer-title {
    width: 100%;

    .master-item {
      padding: 0px 6px 10px 6px;
    }
  }
}

.report-menu-container {
  background: transparent;
  border: 1px solid transparent;

  //.ant-menu-sub {
  //  background: transparent !important;
  //}

  .ant-menu-sub.ant-menu-inline {
    background: #eeeeee !important;
  }

  .ant-menu-item,
  .ant-menu-submenu-title {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    padding-right: 12px;

    margin-top: 0px !important;
    margin-bottom: 0px !important;

    //font-size: 15px;
    //display: flex;
    //flex-direction: row;
    //align-items: center;
    //justify-content: space-between;
    //border-bottom: 1px solid #e9e9e9;
  }

  .ant-menu-submenu-title,
  .ant-menu-item {
    height: auto !important;
    line-height: 1.5715 !important;
  }

  //.ant-menu-submenu-title {
  //
  //}
  //
  .ant-menu-item-only-child,
  .menu-item {
    width: 100%;
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    padding-right: 12px !important;
    //font-size: 15px;
    //display: flex;
    //flex-direction: row;
    //align-items: center;
    //justify-content: space-between;
    //
    //margin-bottom: 0px !important;
    //cursor: pointer;
    //border-bottom: 1px solid #e9e9e9;
  }
  //
  //.ant-menu-item-selected::after,
  //.ant-menu-inline .ant-menu-item::after {
  //  border-right: none !important;
  //}
  //
  //
  //.ant-menu-item-selected {
  //   // color: @blue-color;
  //        color: #fff;
  //        background-color: #25a99c !important;
  //        border-radius: 5px;
  //        //margin: 5px;
  //}
  //
  //.ant-menu-item-selected:hover {
  //  color: #fff !important;
  //}
  //
  .ant-menu-title-content {
    min-width: 80% !important;
    overflow: unset !important;
    text-overflow: unset !important;

    white-space: normal;
    flex: 1;
    max-width: 80%;
    word-break: break-all;
    display: flex;
    align-items: center;
  }
}

.master-item {
  width: 100%;
  //padding: 10px 12px;
  padding: 10px 12px 10px 18px;
  font-size: 15px;
  display: flex;
  flex-direction: row;
  align-items: center;
  //justify-content: space-between;

  &:hover {
    color: #1890ff;
  }

  .title {
    //max-width: 80%;
    word-break: break-all;
    display: flex;
    align-items: center;
    // font-weight: 600;
  }

  .icon-left {
    min-width: 10%;
    font-size: 19px;
    margin-right: 10px;
    // color: rgba(0, 0, 0, 0.55);
    align-self: start;
  }

  .icon-right {
    font-size: 15px;
    margin-left: 10px;
    color: @blue-color;
  }
}

.report-left-menu-bg,
.report-left-menu-container .ant-drawer-content {
  //background: @menu-bg-color;
  background: #ffffff;
}

.report-left-menu-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 250px;
  width: 250px;
  // border: 1px solid #d9d9d9;

  .search-container {
    margin: 0px 0px 10px 0px;
  }

  // spin
  .ant-spin-nested-loading {
    height: 100%;
  }

  .ant-spin-container {
    height: 100%;
  }

  .left-menu-items-container {
    height: calc(100% - 32px - 10px);
    //height: 100%;
    overflow-y: auto;

    .master-item {
      cursor: pointer;
      border-bottom: 1px solid @card-border-color;

      border-right: 3px solid transparent;
    }

    .item-container-select {
      // color: @blue-color;
      color: #fff;
      background-color: #25a99c;
      border-radius: 5px;
      //margin: 5px;
    }

    .item-container-select-v2 {
      // color: @blue-color;
      color: #1890ff;
      background-color: #e6f7ff;
      border-right: 3px solid #1890ff;

      transition: transform 0.15s cubic-bezier(0.215, 0.61, 0.355, 1),
        opacity 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
      //border-radius: 5px;
      //margin: 5px;
    }
  }

  .report-left-menu-item-container {
    display: flex;
    flex-direction: column;

    .ant-collapse-content-box {
      padding: 0px !important;
    }

    .title-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 40px;
    }

    .item-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 10px 10px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.15);
      cursor: pointer;
    }

    .add-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 40px;
      border-bottom: none !important;

      span {
        margin-right: 10px;
      }
    }

    .content-container {
      display: flex;
      flex-direction: column;
    }
  }

  .report-base-drawer-container {
    .ant-drawer-header {
      padding: 0;
    }

    .ant-drawer-body {
      padding: 0px;
    }

    .ant-drawer-content-wrapper {
      box-shadow: none;
      // border: 1px solid #d9d9d9;
    }
  }
}
