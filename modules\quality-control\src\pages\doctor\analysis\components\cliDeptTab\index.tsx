import CardWithBtns from '@uni/components/src/cardWithBtns/index';
import { Col, Row } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src/commonService';
import { RespVO } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { UniTable } from '@uni/components/src/index';
import _ from 'lodash';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { isEmptyValues } from '@uni/utils/src/utils';
import IconBtn from '@uni/components/src/iconBtn/index';
import PieTreeTrend from '../pieTreeTrend/index';

const CliDeptTab = ({ detailAction, visibleValueKeys }) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, errorLevel, coders, qualityMonitorLevel } =
    globalState.searchParams;
  const [tableParams, setTableParams] = useState(undefined);
  const [pieTrendParams, setPieTrendParams] = useState(undefined);
  const [clickedRecord, setClickedRecord] = useState(undefined);

  useEffect(() => {
    if (dateRange?.length) {
      let params = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: isEmptyValues(hospCodes) ? undefined : hospCodes,
        ErrorLevel: errorLevel,
        Coders: coders,
        MonitorLevel: qualityMonitorLevel,
      };
      setTableParams(params);
    }
  }, [dateRange, hospCodes, errorLevel, coders, qualityMonitorLevel]);

  useEffect(() => {
    if (clickedRecord?.CliDept) {
      setPieTrendParams({
        ...tableParams,
        CliDepts: [clickedRecord?.CliDept],
      });
    }
  }, [clickedRecord, tableParams]);

  useEffect(() => {
    if (tableParams) {
      getQcStatsOfCliDeptReq(tableParams);
    }
  }, [tableParams]);

  useEffect(() => {
    if (!QcStatsOfCliDeptColumnsData?.length) {
      getQcStatsOfCliDeptColumnsReq();
    }
  }, []);

  const {
    data: qcStatsOfCliDeptData,
    loading: getQcStatsOfCliDeptLoading,
    run: getQcStatsOfCliDeptReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/Emr/QcResultStats/QcStatsOfCliDept', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.length) {
            let data = _.orderBy(res.data, visibleValueKeys[1], 'desc');
            setClickedRecord(data[0]);
            return data;
          } else {
            return [];
          }
        } else return [];
      },
    },
  );

  // Columns
  const {
    data: QcStatsOfCliDeptColumnsData,
    loading: getQcStatsOfCliDeptColumnsLoading,
    mutate: mutateQcStatsOfCliDeptColumns,
    run: getQcStatsOfCliDeptColumnsReq,
  } = useRequest(
    () =>
      uniCommonService('Api/Emr/QcResultStats/QcStatsOfCliDept', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          const ColumnsData = tableColumnBaseProcessor([], res.data?.Columns);
          const list = ColumnsData?.filter((col) =>
            visibleValueKeys?.includes(col?.dataIndex),
          )?.map((col) => ({ ...col, visible: true }));
          return list;
        }
      },
    },
  );

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <CardWithBtns
            title={'科室质控结果'}
            content={
              <UniTable
                id={'dmr-data-dept-table'}
                rowKey={'CliDept'}
                columns={[
                  {
                    dataIndex: 'options',
                    width: 40,
                    visible: true,
                    fixed: 'left',
                    render: (text, record) => (
                      <IconBtn
                        type="details"
                        style={{ margin: '0 10px' }}
                        onClick={(e) => {
                          detailAction && detailAction(record);
                        }}
                      />
                    ),
                  },
                ].concat(QcStatsOfCliDeptColumnsData)}
                scroll={{
                  x: 'max-content',
                }}
                dataSource={qcStatsOfCliDeptData}
                loading={getQcStatsOfCliDeptLoading}
                clickable={true}
                // pagination={false}
                dictionaryData={globalState?.dictData}
                onRow={(record) => {
                  return {
                    onClick: (event) => {
                      setClickedRecord(record);
                    },
                  };
                }}
                rowClassName={(record, index) => {
                  if (record?.CliDept)
                    return record.CliDept === clickedRecord?.CliDept
                      ? 'row-selected'
                      : '';
                }}
              />
            }
            needExport={true}
            exportTitle={'科室质控结果'}
            exportData={qcStatsOfCliDeptData}
            exportColumns={QcStatsOfCliDeptColumnsData}
            needModalDetails={true}
            onRefresh={() => {
              getQcStatsOfCliDeptReq(tableParams);
            }}
          />
        </Col>
        <Col span={24}>
          <h3>
            <b>
              {clickedRecord?.CliDept
                ? `${clickedRecord?.CliDeptName} 统计分析`
                : '请选择科室'}
            </b>
          </h3>
        </Col>
        <Col span={24}>
          <PieTreeTrend
            tableParams={pieTrendParams}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: '',
                args: {
                  ...pieTrendParams,
                  ...record?.args,
                },
                detailsUrl: 'Emr/QcResultStats/GetQcCards',
                dictData: dictData,
              });
            }}
          />
        </Col>
      </Row>
    </>
  );
};
export default CliDeptTab;
