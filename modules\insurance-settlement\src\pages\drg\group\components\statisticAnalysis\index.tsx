import { Input, Col, Row, Tabs, Alert } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import DetailsBtn from '@uni/components/src/details-btn';
import {
  GrpTotalStatsColumns,
  SettleCompStatsByGrpColumns,
} from '@/pages/drg/constants';
import Stats from '@/pages/drg/components/stats';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import IconBtn from '@uni/components/src/iconBtn';
import { UniSelect } from '@uni/components/src';
import SettleCompStatsByFeeRange from '../feeRange';
import PatCntRatioAnalysis from '../patCntRatioAnalysis';
import ChsBmFeeTable from '../chsBm';
import TrendAnalysis from '@/components/trendAnalysis';

export interface ISingleGroupAnalysisProps {
  selectedTableItem?: {
    VersionedChsDrgCode: string;
  };
}

const StatisticAnalysis = (props) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, insurType } = globalState?.searchParams;
  const [requestParams, setRequestParams] = useState<any>({});
  const [filterNumber, setFilterNumber] = useState<any>(0);
  const [statsHeight, setStatsHeight] = useState<number>(295);

  // 这个是stats 分析页面的左边card选择项
  const [selectedStatItem, setSelectedStatItem] = useState({
    contentData: 'Profit',
    dataType: 'Currency',
    title: '盈亏',
  });

  // 概况(各种人次数据 / 占比等)
  const {
    data: settleCompStatsOfHospData,
    loading: getSettleCompStatsOfHospLoading,
    run: getSettleCompStatsOfHospReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsOfGrp`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  useEffect(() => {
    const updateHeight = () => {
      const element = document.getElementById('group-stats-list');
      if (element) {
        const height = element.getBoundingClientRect().height;
        if (height > 0) {
          setStatsHeight(height - 50 - 24);
        }
      }
    };

    // 初始更新
    updateHeight();

    // 创建一个观察器实例
    const observer = new MutationObserver(updateHeight);

    // 配置观察选项
    const config = { attributes: true, childList: true, subtree: true };

    // 开始观察目标节点
    const targetNode = document.getElementById('group-stats-list');
    if (targetNode) {
      observer.observe(targetNode, config);
    }

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    if (
      dateRange?.length &&
      hospCodes?.length &&
      props?.selectedTableItem?.VersionedChsDrgCode
    ) {
      let tableParams: any = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        insurType,
        VersionedChsDrgCodes: [props?.selectedTableItem?.VersionedChsDrgCode],
        MdcCodes: props?.MdcCodes,
      };
      setRequestParams(tableParams);
    }
  }, [
    props?.selectedTableItem,
    dateRange,
    hospCodes,
    insurType,
    props?.MdcCodes,
  ]);

  // stat click
  useEffect(() => {
    Emitter.on(EventConstant.STAT_CLICK, (record) => {
      setSelectedStatItem(record);
    });
    return () => {
      Emitter.off(EventConstant.STAT_CLICK);
    };
  }, [selectedStatItem]);

  useEffect(() => {
    if (requestParams && Object.keys(requestParams)?.length) {
      getSettleCompStatsOfHospReq(requestParams);
    }
  }, [requestParams]);

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={24} md={24} lg={11} xl={11}>
        <Row gutter={[16, 16]} id="group-stats-list">
          <Stats
            level={props?.type}
            api={`Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsOfGrp`}
            selectedTableItem={props?.selectedTableItem}
            columns={GrpTotalStatsColumns}
            type="col-xl-6"
          />
        </Row>
      </Col>
      <Col xs={24} sm={24} md={24} lg={13} xl={13}>
        <TrendAnalysis
          title="月度变化趋势"
          height={statsHeight}
          selectedStatItem={selectedStatItem}
          requestParams={requestParams}
          dictData={globalState.dictData}
        />
      </Col>
      <Col span={24}>
        <PatCntRatioAnalysis
          requestParams={requestParams}
          loading={getSettleCompStatsOfHospLoading}
          api="Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsOfGrp"
          settleCompStatsOfHospData={settleCompStatsOfHospData}
        />
      </Col>
      <Col>
        <SettleCompStatsByFeeRange requestParams={requestParams} />
      </Col>
    </Row>
  );
};

export default StatisticAnalysis;
