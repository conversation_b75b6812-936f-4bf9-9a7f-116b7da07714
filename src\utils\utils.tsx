export function getDayjsEndOfByType(pickerType: string, dayjsInstance: any) {
  switch (pickerType) {
    case 'year':
      return dayjsInstance.endOf('year');
    case 'quarter':
      return dayjsInstance.endOf('quarter');
    case 'month':
      return dayjsInstance.endOf('month');
  }

  return dayjsInstance;
}

export function getDayjsStartOfByType(pickerType: string, dayjsInstance: any) {
  switch (pickerType) {
    case 'year':
      return dayjsInstance.startOf('year');
    case 'quarter':
      return dayjsInstance.startOf('quarter');
    case 'month':
      return dayjsInstance.startOf('month');
  }

  return dayjsInstance;
}

export function addGlobalUncaughtErrorHandler(
  errorHandler: OnErrorEventHandlerNonNull,
): void {
  window.addEventListener('error', errorHandler);
}

export function removeGlobalUncaughtErrorHandler(
  errorHandler: (...args: any[]) => any,
) {
  window.removeEventListener('error', errorHandler);
}
