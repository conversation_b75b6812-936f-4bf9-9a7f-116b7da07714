import React, { useEffect, useMemo, useState } from 'react';
import { Card, Button, Space } from 'antd';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { QueryParameterConfigurationEditButton } from '@uni/components/src/query-configuration';
import { ReqActionType } from '@/Constants';
import { isEmptyValues } from '@uni/utils/src/utils';
import { argDefToFormItemTransformer } from '@uni/components/src/query-configuration/processor';
import { BorrowFormItems } from '../../formItems';

interface BorrowFormProps {
  proFormRef: React.RefObject<any>;
  barCodeRef: React.RefObject<any>;
  lendSearchOpts: any[];
  setLendSearchOpts: any;
  defaultSearchOpts: any[];
  dictData: any;
  loadings: any;
  fetchReqActionReq: (opts: any[]) => void;
}

/**
 * 借阅表单组件
 */
const BorrowForm: React.FC<BorrowFormProps> = ({
  proFormRef,
  barCodeRef,
  lendSearchOpts,
  setLendSearchOpts,
  defaultSearchOpts,
  dictData,
  loadings,
  fetchReqActionReq,
}) => {
  // 使用useMemo优化formItems的计算
  // const currentFormItems = useMemo(() => {
  //   return isEmptyValues(lendSearchOpts) ? defaultSearchOpts : lendSearchOpts;
  // }, [lendSearchOpts, defaultSearchOpts]);

  // // 使用useMemo优化onFormItemsSaveSuccess回调
  // const handleFormItemsSaveSuccess = useMemo(() => {
  //   return (argDefs: any) => {
  //     if (!argDefs) return;

  //     let transformed = argDefToFormItemTransformer(argDefs, dictData)
  //       .concat(
  //         (defaultSearchOpts ?? [])?.filter(
  //           (item) => item?.custom === true,
  //         ) as any[],
  //       )
  //       .sort(
  //         (a: any, b: any) => (a?.ColumnSort ?? 999) - (b?.ColumnSort ?? 999),
  //       );

  //     setLendSearchOpts(transformed?.filter((item) => item?.visible === true));
  //   };
  // }, [defaultSearchOpts, dictData]);

  return (
    <Card
      title={<Space>借阅信息</Space>}
      style={{ marginBottom: '15px' }}
      extra={
        <QueryParameterConfigurationEditButton
          type={'LEFT_CONTAINER'}
          currentSearchOptsLength={lendSearchOpts?.length ?? 0}
          setSearchOpts={setLendSearchOpts}
          formItems={
            isEmptyValues(lendSearchOpts) ? defaultSearchOpts : lendSearchOpts
          }
          dictData={dictData}
          queryInterfaceUrl={'Api/Mr/Borrowing/Lend'}
          onFormItemsSaveSuccess={(argDefs) => {
            let transformed = argDefToFormItemTransformer(argDefs, dictData)
              .concat(
                (defaultSearchOpts ?? [])?.filter(
                  (item) => item?.custom === true,
                ) as any[],
              )
              .sort(
                (a: any, b: any) =>
                  (a?.ColumnSort ?? 999) - (b?.ColumnSort ?? 999),
              );

            setLendSearchOpts(
              transformed?.filter((item) => item?.visible === true),
            );
          }}
        />
      }
    >
      <ProFormContainer
        spinLoading={
          loadings['TraceRecord/GetList'] ||
          loadings[`Borrowing/${ReqActionType.lend}`] ||
          false
        }
        autoFocusFirstInput={false}
        className="borrow_register_form"
        id="borrowRegisterForm"
        formRef={proFormRef}
        grid
        labelCol={{ flex: '100px' }}
        wrapperCol={{ flex: 'auto' }}
        searchOpts={lendSearchOpts}
        submitter={{
          render: () => {
            return [
              <Button
                type="primary"
                style={{
                  width: 'calc(100% - 100px)',
                  float: 'right',
                  marginTop: '8px',
                }}
                key="submit"
                onClick={() => {
                  fetchReqActionReq(lendSearchOpts);
                }}
              >
                借阅(Enter)
              </Button>,
            ];
          },
        }}
      />
    </Card>
  );
};

export default BorrowForm;
