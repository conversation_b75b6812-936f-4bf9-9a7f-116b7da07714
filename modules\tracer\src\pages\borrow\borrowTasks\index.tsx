import React, { useEffect, useRef, useState } from 'react';
import { UniTable } from '@uni/components/src';
import { useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
// 导入PdfPrint组件的打印工具函数
import { handlePdfPrint } from '@uni/components/src/pdf-print';
import { useModel } from '@@/plugin-model/useModel';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { uniCommonService } from '@uni/services/src';
import type { ReactNode } from 'react';
import {
  CloseOutlined,
  PlusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Emitter } from '@uni/utils/src/emitter';
import { EVENT_BORROW_TASK } from './constants';
import { handleDocxPrintFromApi } from './utils/docxToPrint';
import { BorrowTaskColumns } from './columns';
import {
  Button,
  Card,
  Col,
  Modal,
  Drawer,
  Row,
  TableProps,
  message,
  Form,
  Input,
  Space,
  Divider,
} from 'antd';
import { ProFormInstance } from '@ant-design/pro-components';
import IconBtn from '@uni/components/src/iconBtn';
import DetailsTableModal from './components/detailsTable';
import PatTimeline from './components/PatTimeline';
import BorrowTasksCreate from './components/create/index';
import BorrowRegister from './components/register/register';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';
import _ from 'lodash';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';

const BorrowTasks = (props) => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster');
  const [candelRejectForm] = Form.useForm();
  let { dateRange, Borrower, ApplicationId } = searchParams;
  const [tableColumns, setTableColumns] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [rejectModalVisible, setRejectModalVisible] = useState({
    visible: false,
    record: { Id: null, BorrowerName: null },
    type: null,
  });
  const [selectedTableRow, setSelectedTableRow] = useState(null);
  const [selectedApplicationActions, setSelectedApplicationActions] =
    useState();

  const [createModalVisible, setCreateModalVisible] = useState(false);

  // 病案室：拒绝申请
  const { run: rejectReq } = useRequest(
    (values) => {
      return uniCommonService('Api/Mr/BorrowApplication/RejectApplication', {
        method: 'POST',
        requestType: 'json',
        data: values,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          getTableDataReq();
          setRejectModalVisible({
            visible: false,
            record: { Id: null, BorrowerName: null },
            type: null,
          });
        }
      },
    },
  );

  // 病案室：开始处理
  const { run: handleReq } = useRequest(
    (values) => {
      return uniCommonService('Api/Mr/BorrowApplication/HandleApplication', {
        method: 'POST',
        requestType: 'json',
        data: values,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          getTableDataReq();
        }
      },
    },
  );

  // 病案室：准备病案
  const { run: prepareReq } = useRequest(
    (values) => {
      return uniCommonService('Api/Mr/BorrowApplication/PrepareApplication', {
        method: 'POST',
        requestType: 'json',
        data: values,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('已准备完成');
          getTableDataReq();
        }
      },
    },
  );

  // 病案室：已取病案
  const { run: fetchReq } = useRequest(
    (values) => {
      return uniCommonService('Api/Mr/BorrowApplication/FetchApplication', {
        method: 'POST',
        requestType: 'json',
        data: values,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setRejectModalVisible({
            visible: false,
            record: { Id: null, BorrowerName: null },
            type: null,
          });
          getTableDataReq();
        }
      },
    },
  );

  // 病案室：新建批量list
  const { loading: createReqLoading, run: createReq } = useRequest(
    (values, cb) => {
      return uniCommonService('Api/Mr/BorrowApplication/CreateApplication', {
        method: 'POST',
        requestType: 'json',
        data: { ...values, isMr: true },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          // setModalVisible(false);
          message.success('新建成功');
          setCreateModalVisible(false);
          if (params?.at(1)) {
            params?.at(1)();
          }
          // 检查是否是通过打印按钮触发的提交
          if (params[0]?.isPrintTriggered) {
            // 如果是打印触发的，则调用打印接口
            // 从返回的数据中获取ApplicationId
            const applicationId = response?.data?.Data?.at(0)?.Id;
            if (applicationId) {
              // 调用打印接口
              printReq({ ApplicationId: applicationId });
            }
          }

          getTableDataReq();
        }
      },
    },
  );

  /**
   * 病案室：借阅打印信息
   *
   * 使用PdfPrint组件提供的打印功能
   * @see packages/components/src/pdf-print/index.tsx
   */
  const { loading: printReqLoading, run: printReq } = useRequest(
    (values) => {
      return uniCommonService(
        'Api/Mr/BorrowApplication/ExportBorrowApplicationManifest',
        {
          method: 'POST',
          params: values,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        // 使用PdfPrint组件提供的打印功能
        handlePdfPrint(response);
      },
    },
  );

  // 注意：printReq函数现在使用了PdfPrint组件提供的handlePdfPrint工具函数
  // 如果需要修改打印逻辑，只需要更新PdfPrint组件中的handlePdfPrint函数即可

  // 病案室：批量归还
  const { run: batchReturnReq } = useRequest(
    (values) => {
      return uniCommonService(
        'Api/Mr/BorrowApplication/BatchReturnByApplication',
        {
          method: 'POST',
          requestType: 'json',
          params: values,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('批量归还成功');
          getTableDataReq();
        }
      },
    },
  );

  // 时间轴
  const { loading: applicationActionsLoading, run: getApplicationActionsReq } =
    useRequest(
      (values) => {
        return uniCommonService(
          'Api/Mr/BorrowApplication/GetApplicationActions',
          {
            method: 'POST',
            requestType: 'json',
            data: values,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<any>) => {
          return response;
        },
        onSuccess: (response, params) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setSelectedApplicationActions(response?.data);
          }
        },
      },
    );

  useEffect(() => {
    if (selectedTableRow)
      getApplicationActionsReq({
        ApplicationId: selectedTableRow?.Id,
      });
  }, [selectedTableRow]);

  const { loading: getTableDataLoading, run: getTableDataReq } = useRequest(
    () => {
      return uniCommonService('Api/Mr/BorrowApplication/GetApplications', {
        method: 'POST',
        data: {
          Sdate: dateRange?.at(0),
          Edate: dateRange?.at(1),
          Borrower,
          ApplicationId,
        },
      });
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0) {
          if (!res.data || !Array.isArray(res.data) || res.data.length === 0) {
            setTableData([]);
            return;
          }
          setTableData(res.data);
        } else {
          setTableData([]);
        }
      },
    },
  );

  const { run: getTableColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Mr/BorrowApplication/GetApplications', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0) {
          setTableColumns(
            tableColumnBaseProcessor(
              BorrowTaskColumns,
              response?.data?.Columns,
            ),
          );
        } else {
          setTableColumns([]);
        }
      },
    },
  );

  useEffect(() => {
    if (tableColumns.length < 1) getTableColumnsReq();

    // 注册事件监听
    Emitter.on(EVENT_BORROW_TASK.HANDLE_APPLICATION, (record) => {
      handleReq({ ApplicationId: record?.Id });
    });

    Emitter.on(EVENT_BORROW_TASK.PREPARE_APPLICATION, (record) => {
      prepareReq({ ApplicationId: record?.Id });
    });

    Emitter.on(EVENT_BORROW_TASK.REJECT_APPLICATION, (record) => {
      setRejectModalVisible({
        visible: true,
        record,
        type: 'reject',
      });
    });

    Emitter.on(EVENT_BORROW_TASK.FETCH_APPLICATION, (record) => {
      setRejectModalVisible({
        visible: true,
        record,
        type: 'fetch',
      });
    });

    Emitter.on(EVENT_BORROW_TASK.PRINT_APPLICATION, (record) => {
      printReq({ ApplicationId: record?.Id });
    });

    Emitter.on(EVENT_BORROW_TASK.BATCH_RETURN, (record) => {
      batchReturnReq({ ApplicationId: record?.Id });
    });

    // 清理事件监听
    return () => {
      Emitter.off(EVENT_BORROW_TASK.HANDLE_APPLICATION);
      Emitter.off(EVENT_BORROW_TASK.PREPARE_APPLICATION);
      Emitter.off(EVENT_BORROW_TASK.REJECT_APPLICATION);
      Emitter.off(EVENT_BORROW_TASK.FETCH_APPLICATION);
      Emitter.off(EVENT_BORROW_TASK.PRINT_APPLICATION);
      Emitter.off(EVENT_BORROW_TASK.BATCH_RETURN);
    };
  }, []);

  useEffect(() => {
    if (dateRange?.length > 0) {
      console.log('getTableDataReq', dateRange);
      getTableDataReq();
      setSelectedTableRow(null);
      setSelectedApplicationActions(null);
    }
  }, [dateRange, Borrower]);

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={17}>
          <Card
            title="批量借阅管理"
            extra={
              <Space>
                <Button
                  key="add"
                  type="primary"
                  loading={false}
                  icon={<PlusOutlined />}
                  onClick={(e) => {
                    setCreateModalVisible(true);
                  }}
                >
                  新增借阅申请
                </Button>
                <Divider type="vertical" />
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl:
                      'Api/Mr/BorrowApplication/GetApplications',
                    onTableRowSaveSuccess: (newColumns) => {
                      setTableColumns(
                        tableColumnBaseProcessor(BorrowTaskColumns, newColumns),
                      );
                    },
                  }}
                />
              </Space>
            }
          >
            <UniTable
              id="trace_borrow_tasks"
              rowKey="Id"
              loading={getTableDataLoading || printReqLoading}
              columns={tableColumns}
              dataSource={tableData}
              dictionaryData={dictData}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    setSelectedTableRow(record);
                  },
                };
              }}
              rowClassName={(record, index) =>
                record?.Id === selectedTableRow?.Id ? 'row-selected' : ''
              }
              scroll={{ x: 'max-content' }}
            />
            <DetailsTableModal printReqLoading={printReqLoading} />
          </Card>
        </Col>
        <Col span={6}>
          <PatTimeline
            item={selectedTableRow}
            loading={applicationActionsLoading}
            timelineItems={selectedApplicationActions}
          />
        </Col>
      </Row>

      {/* 新建借阅申请 - 使用重构后的Register组件（内部包含Drawer） */}
      <BorrowRegister
        createReq={createReq}
        loading={createReqLoading}
        onClose={() => setCreateModalVisible(false)}
        visible={createModalVisible}
      />

      <Modal
        title={`${
          rejectModalVisible.type === 'fetch' ? '病案已取' : '拒绝申请'
        }`}
        open={rejectModalVisible.visible}
        onCancel={() =>
          setRejectModalVisible({
            visible: false,
            record: { Id: null, BorrowerName: null },
            type: null,
          })
        }
        footer={[
          <Button
            key="cancel"
            onClick={() =>
              setRejectModalVisible({
                visible: false,
                record: { Id: null, BorrowerName: null },
                type: null,
              })
            }
          >
            取消
          </Button>,
          <Button
            key="reject"
            type="primary"
            onClick={() => {
              if (rejectModalVisible.type === 'reject') {
                rejectReq({
                  Id: rejectModalVisible.record?.Id,
                  Remark: candelRejectForm.getFieldValue('rejectReason'),
                });
              }
              if (rejectModalVisible.type === 'fetch') {
                fetchReq({
                  Id: rejectModalVisible.record?.Id,
                  Remark: candelRejectForm.getFieldValue('rejectReason'),
                });
              }
            }}
          >
            确认
          </Button>,
        ]}
      >
        <Form form={candelRejectForm}>
          <Form.Item name="rejectReason">
            <Input.TextArea
              placeholder={`${
                rejectModalVisible.type === 'fetch'
                  ? '请输入'
                  : '请输入拒绝申请理由'
              }`}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default BorrowTasks;
