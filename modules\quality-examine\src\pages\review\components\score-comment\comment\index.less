@import '~@uni/commons/src/style/variables.less';

.right-item-comment-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .ant-tabs-nav {
    margin-bottom: 5px;
    padding-left: 10px;
  }

  .ant-tabs,
  .ant-tabs-content,
  .ant-tabs-content-holder,
  .ant-tabs-tabpane,
  .ant-spin-nested-loading {
    height: 100%;
  }

  .ant-input-disabled {
    color: rgba(0, 0, 0, 0.85) !important;
    background: #ffffff !important;
    cursor: default;
    border: none;
  }

  .group-comment-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow-y: auto;
    width: 100%;

    .group-comment-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 20px 10px;

      border-bottom: 1px solid #e6e6e6;

      .ant-form-item {
        margin-bottom: 0px;
      }

      .comment-content {
        display: flex;
        flex-direction: column;
        flex: 1;

        .component {
          font-weight: bold;
          margin-bottom: 8px;
        }
      }

      .comment-operator-container {
        display: flex;
        flex-direction: column;
      }

      .comment-operator {
        padding: 3px;
        color: #1464f8;
        cursor: pointer;
        margin: 5px 5px;
      }
    }
  }

  .comment-info {
    word-break: break-all;
  }

  .separator-comment-container {
    display: flex;
    flex-direction: column;
    margin: 5px;
    height: calc(100% - 10px);
    overflow-y: auto;
    width: calc(100% - 10px);
    position: relative;
    //-ms-overflow-style: none; /* IE and Edge */
    //scrollbar-width: none; /* Firefox */

    .ant-spin-spinning {
      background: #ffffff;
      max-height: 750px;
      min-height: 750px;
    }

    .separator-comment-relative-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
    }

    .separator-comment-line-container {
      display: flex;
      flex-direction: column;
      position: fixed;
      width: 100%;
    }

    .separator-comment-item-Appealed {
      border: 2px solid #f8961e !important;

      .status-tag-container {
        background: #f8961e;
      }

      &:hover {
        border: 2px solid #f8961e !important;
        background: rgba(248, 150, 30, 0.2) !important;
      }
    }

    .separator-comment-item-Resolved {
      border: 2px solid #80ed99 !important;

      .status-tag-container {
        background: #80ed99 !important;
      }

      &:hover {
        border: 2px solid #80ed99 !important;
        background: rgba(128, 237, 153, 0.2) !important;
      }
    }

    .separator-comment-item-InvalidError {
      border: 2px solid #0466c8 !important;

      .status-tag-container {
        background: #0466c8 !important;
      }

      &:hover {
        border: 2px solid #0466c8 !important;
        background: rgba(4, 102, 200, 0.2) !important;
      }
    }

    .separator-comment-item-ValidError {
      border: 2px solid #ffd60a !important;

      .status-tag-container {
        background: #ffd60a !important;
      }

      &:hover {
        border: 2px solid #ffd60a !important;
        background: rgba(255, 214, 10, 0.2) !important;
      }
    }

    .separator-comment-item-Ok {
      border: 2px solid #06d6a0 !important;

      .status-tag-container {
        background: #06d6a0 !important;
      }

      &:hover {
        border: 2px solid #06d6a0 !important;
        background: rgba(6, 214, 160, 0.2) !important;
      }
    }

    .separator-comment-item {
      padding: 5px 10px;
      width: 100%;
      background: #ffffff;
      border: 2px solid #bb2424;
      // border-top: 1px solid transparent;
      // border-bottom: 1px solid transparent;
      // border-right: 1px solid transparent;

      &:focus-within,
      &:hover {
        padding: 5px 10px 5px 10px !important;
        border: 2px solid #bb2424;
        background: #fff7f7;
      }

      .commenter-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
      }

      .commenter {
        font-size: 14px;
        font-weight: bold;
      }

      .comment-date-operation-container {
        display: flex;
        flex-direction: row;
        align-items: center;

        .date {
          margin: 0px 3px;
        }

        .category-name {
          padding: 0px 3px;
          border-left: 2px solid #d9d9d9;
          border-right: 2px solid #d9d9d9;
          text-align: center;
        }

        .fold-btn {
          margin: 0px 3px;
        }
      }
    }

    .status-tag-container {
      position: absolute;
      top: 0px;
      right: 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px;
      border-bottom-left-radius: 4px;
      background-color: #bb2424;

      .label {
        color: #ffffff;
      }
    }

    .rule-score-comment-item {
      display: flex;
      flex-direction: column;
      position: relative;
      cursor: pointer;

      .detail-operation-container {
        margin-top: 35px;
        display: flex;
        flex-direction: column;
        margin-left: 5px;

        button {
          margin-bottom: 8px;
        }
      }
    }

    .rule-score-item-container {
      display: flex;
      flex-direction: row;
      flex: 1;

      .rule-content-container {
        display: flex;
        flex-direction: column;
        flex: 1;
      }

      .rule-score-item {
        display: flex;
        flex-direction: row;
        margin-bottom: 8px;

        .rule-score-label {
          min-width: 70px;
        }

        .rule-score-content {
          display: flex;
          flex-direction: column;

          .ant-form-item {
            margin-bottom: 0px;
          }
        }

        .rule-score-item-input {
          border-radius: 4px;
        }
      }
    }

    .issue-items-container {
      display: flex;
      flex-direction: column;
      border-top: 1px solid @border-color;

      .container-label {
        font-size: 16px;
        font-weight: bold;
      }

      .issue-item {
        display: flex;
        flex-direction: column;
        border-bottom: 1px solid @border-color;
        padding: 5px;
        margin-top: 10px;
        background: #ffffff;
      }

      .issue-item-content {
        word-break: break-all;
      }

      .issue-operations {
        display: flex;
        flex-direction: row;
        align-self: flex-end;
        margin-top: 5px;
      }

      .issue-operator {
        margin: 0px 10px;
      }
    }

    .detail-comment-content-container {
      display: flex;
      flex-direction: column;

      .comment-input-container {
        display: flex;
        flex-direction: column;

        .ant-form-item {
          margin-bottom: 0px;
        }

        .comment-operation-container {
          margin-top: 10px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-end;
        }
      }

      .info {
        font-size: 14px;
      }
    }
  }

  .separator-comment-container::-webkit-scrollbar {
    display: none;
  }
}

.appeal-container {
  display: flex;
  flex-direction: column;

  .ant-card-head-title,
  .ant-card-extra {
    padding: 8px 0px;
  }

  .ant-card-body {
    padding: 12px 24px;
  }
}

.task-error-container {
  display: flex;
  flex-direction: column;

  .ant-modal-body {
    padding: 0px 0px 10px 0px !important;
  }

  .ant-modal-confirm-content {
    margin-top: 0px !important;
  }

  .ant-modal-confirm-btns {
    padding: 0px 10px !important;
  }

  .ant-card-head-title,
  .ant-card-extra {
    padding: 8px 0px;
  }

  .ant-card-body {
    padding: 12px 24px;
  }
}

.annotation-add-container {
  display: flex;
  flex-direction: column;

  .ant-modal-body {
    padding: 0px 0px 12px !important;
  }

  .ant-modal-confirm-content {
    margin-left: 0px !important;
  }

  .ant-card-head-title,
  .ant-card-extra {
    padding: 8px 0px !important;
  }

  .ant-card-body {
    padding: 0px !important;
  }

  .ant-card {
    border: none !important;
  }

  .ant-modal-confirm-title {
    border-bottom: 1px solid #cccccc !important;
    padding: 10px 12px !important;
  }

  .ant-modal-confirm-btns {
    padding: 0px 12px;
  }

  .annotation-add-content-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 0px 12px;
  }

  .annotation-add-item {
    display: flex;
    flex-direction: row;
    margin-bottom: 8px;

    .annotation-add-label {
      min-width: 70px;
    }

    .annotation-add-content {
      display: flex;
      flex-direction: column;

      .ant-form-item {
        margin-bottom: 0px;
      }
    }

    .annotation-add-item-input {
      //border: 1px solid @border-color;
      border-radius: 4px;

      textarea {
        width: 320px !important;
      }
    }
  }
}
