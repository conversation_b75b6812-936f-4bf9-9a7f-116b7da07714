import { useEffect, useState } from 'react';
import { Tabs, Col, Row } from 'antd';
import { useRequest } from 'umi';
import { UniSelect } from '@uni/components/src';
import { uniCommonService } from '@uni/services/src';
import { useDeepCompareEffect } from 'ahooks';
import './index.less';
import { RespVO } from '@uni/commons/src/interfaces';
import _ from 'lodash';
import { useModel } from '@@/plugin-model/useModel';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import DrillDownSdComposition from '../../components/drillDownSdComposition/index';
import Stats from '../../components/statsWithTrend';
import SingleColumnTable from '../../components/singleColumnTable/index';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '../../components/drawerCardInfo';
import { TotalStatsColumns } from '../constants';

const GradeSd = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, CliDepts } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('sdComposition');

  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedItem, setSelectedItem] = useState(undefined);

  const [selectOpts, setSelectOpts] = useState([]);

  // tab 使用下拉框数据
  const { loading: HqmsSdCompositionLoading, run: GradeSdCompositionReq } =
    useRequest(
      (data) =>
        uniCommonService(
          'Api/v2/Grade/GradeStats/GradeSdCompositionOfCliDept',
          {
            method: 'POST',
            data: data,
          },
        ),
      {
        manual: true,
        formatResult: (res: RespVO<any>) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            if (res?.data?.length) {
              setSelectOpts(
                res.data?.map((d) => ({
                  ...d,
                  label: `${d?.SdName}`,
                })),
              );
              // 默认把第一个设置为selected
              if (!selectedItem) {
                setSelectedItem(
                  res?.data?.at(0),
                  // _.maxBy(res?.data, function (o) {
                  //   return o.PatCnt;
                  // }),
                );
              }
            } else {
              setSelectOpts([]);
            }
          }
        },
      },
    );

  // 处理tableParams
  useDeepCompareEffect(() => {
    if (dateRange?.length) {
      let params: any = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        CliDepts,
      };
      setTableParams(params);
      GradeSdCompositionReq(params);
    }
  }, [dateRange, hospCodes, CliDepts]);

  useEffect(() => {
    Emitter.on(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK, (record) => {
      setSelectedItem(record);
      setActiveKey('hosp_deptAnalysis');
    });

    return () => {
      Emitter.off(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK);
    };
  }, []);

  let tabItems = [
    {
      key: 'sdComposition',
      label: '单病种质量控制',
      children: (
        <DrillDownSdComposition
          tableParams={tableParams}
          compositionApi="Api/v2/Grade/GradeStats/GradeSdCompositionOfCliDept"
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.SdName,
              args: {
                ...tableParams,
                SdCodes: [record?.SdCode],
              },
              detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
              dictData: dictData,
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_deptAnalysis',
      label: '科室综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Grade/GradeStats/GradeSdCompositionOfCliDept`}
              trendApi={`Api/v2/Grade/GradeStats/GradeSdCompositionTrend`}
              columns={
                selectedItem?.SdType !== '0'
                  ? [
                      ...TotalStatsColumns(selectedItem),
                      {
                        contentData: 'AvgPreOperPeriod',
                        clickable: true,
                        footerYoy: true,
                      },
                      {
                        contentData: 'AvgPostOperPeriod',
                        clickable: true,
                        footerYoy: true,
                      },
                    ]
                  : TotalStatsColumns(selectedItem)
              }
              type="col-xl-8"
              chartHeight={320}
              tabKey={activeKey}
              useGlobalState
              level="sd"
              tableParams={tableParams}
              selectedTableItem={selectedItem}
            />
          </Col>
          <SingleColumnTable
            title="该病种科室分布"
            args={{
              api: 'Api/v2/Grade/GradeStats/GradeSdCompositionByCliDept',
              extraApiArgs: {
                SdCodes: [selectedItem?.SdCode],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="CliDeptName"
            type="table"
            visibleValueKeys={[
              'CliDeptName',
              'PatCnt',
              'PatRatio',
              'AvgInPeriod',
              'AvgTotalFee',
              'MedicineFeeRatio',
              'MaterialFeeRatio',
              'DeathCnt',
              'DeathRatio',
              'AvgPreOperPeriod',
              'AvgPostOperPeriod',
            ]}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.CliDeptName,
                args: {
                  ...tableParams,
                  SdCodes: [selectedItem?.SdCode],
                  CliDepts: [record?.CliDept],
                },
                detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
                dictData: dictData,
              });
            }}
            colSpan={{ span: 24 }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_medTeamAnalysis',
      label: '医疗组综合分析',
      children: (
        <Row>
          <SingleColumnTable
            title="该病种医疗组分布"
            args={{
              api: 'Api/v2/Grade/GradeStats/GradeSdCompositionByMedTeam',
              extraApiArgs: {
                SdCodes: [selectedItem?.SdCode],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="MedTeamName"
            type="table"
            visibleValueKeys={[
              'MedTeamName',
              'PatCnt',
              'PatRatio',
              'AvgInPeriod',
              'AvgTotalFee',
              'MedicineFeeRatio',
              'MaterialFeeRatio',
              // 'DeathCnt',
              'DeathRatio',
            ]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName,
                args: {
                  ...tableParams,
                  SdCodes: [selectedItem?.SdCode],
                  MedTeams: [record?.MedTeam],
                },
                detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <Row>
          <SingleColumnTable
            title="该病种医生分布"
            args={{
              api: 'Api/v2/Grade/GradeStats/GradeSdCompositionByDoctor',
              extraApiArgs: {
                SdCodes: [selectedItem?.SdCode],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="DoctorName"
            type="table"
            visibleValueKeys={[
              'DoctorName',
              'PatCnt',
              'PatRatio',
              'AvgInPeriod',
              'AvgTotalFee',
              'MedicineFeeRatio',
              'MaterialFeeRatio',
              // 'DeathCnt',
              'DeathRatio',
            ]}
            colSpan={{ span: 24 }}
            select={{
              dataKey: 'DoctorType',
              valueKey: 'GroupByDoctor',
              allowClear: false,
              defaultSelect: true,
              capitalizeString: true,
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.DoctorName,
                args: {
                  ...tableParams,
                  SdCodes: [selectedItem?.SdCode],
                  DoctorCodes: [record?.DoctorCode],
                  DoctorType: (
                    record?.DoctorType as string
                  )?.toLocaleUpperCase(),
                },
                detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
  ];

  return (
    <>
      <div>
        <Tabs
          size="small"
          items={tabItems}
          activeKey={activeKey}
          onChange={(key) => setActiveKey(key)}
          tabBarExtraContent={{
            right: activeKey !== 'sdComposition' && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <label>当前单病种：</label>
                <UniSelect
                  width={300}
                  showSearch
                  dataSource={selectOpts}
                  value={selectedItem?.SdCode}
                  onChange={(value) => {
                    setSelectedItem(
                      selectOpts?.find((d) => d?.SdCode === value),
                    );
                  }}
                  allowClear={false}
                  optionNameKey={'label'}
                  optionValueKey={'SdCode'}
                  enablePinyinSearch={true}
                  fieldNames={{
                    // label: 'ChsDrgName',
                    value: 'SdCode',
                  }}
                />
              </div>
            ),
          }}
        />
        {/* 把modal与drawer的位置提高到这里，统一在这里处理 避免id太多导致匹配混乱 & tab切换后id失效 */}
        {/* TODO DetailTableModal内部的实现方式是destoryOnClose 导致每次打开都要重新获取columns */}
        <DetailTableModal
          dictData={dictData}
          detailAction={(record) => {
            // 这里替代内部 操作 onClick
            setDrawerVisible({
              hisId: record?.HisId,
              type: 'drg',
            });
          }}
        />
        {/* drawer 同理 这样保持复用 因为drawer 监听的是detailTableModal 或者一些table */}
        <DrawerCardInfo
          visible={drawerVisible}
          onClose={() => setDrawerVisible(undefined)}
        />
      </div>
    </>
  );
};

export default GradeSd;
