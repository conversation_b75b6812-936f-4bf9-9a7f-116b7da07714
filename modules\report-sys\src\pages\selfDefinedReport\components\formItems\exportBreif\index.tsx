import {
  ProForm,
  ProFormSwitch,
  ProFormDependency,
  ProFormSelect,
  ProFormTextArea,
} from '@uni/components/src/pro-form';
import _ from 'lodash';
import { useEventEmitter, useSafeState } from 'ahooks';
import Blob<PERSON>ileHandler from '@/components/BlobFileHandler';
import { BlobFileContentType } from '@/components/BlobFileHandler/constants';

const ExportBriefFormItems = ({ editValueObj }) => {
  const event$ = useEventEmitter<any>();
  const [fileList, setFileList] = useSafeState([]);

  return (
    <ProForm.Group
      title="导出简报配置"
      colProps={{
        span: 12,
      }}
    >
      {/* <ProFormSwitch
        name={['MasterArgs', 'EnableExportBrief']}
        label="允许导出简报"
        initialValue={editValueObj?.Master?.EnableExportBrief ?? true}
        colProps={{
          span: 4,
        }}
      /> */}

      <ProFormSwitch
        name={['MasterArgs', 'ExportBriefArgsShow']}
        label="导出简报配置"
        initialValue={
          Boolean(editValueObj?.Master?.ExportBriefSettingId) || false
        }
        colProps={{
          span: 4,
        }}
      />
      <ProFormDependency name={['MasterArgs', 'ExportBriefArgsShow']}>
        {({ MasterArgs: { ExportBriefArgsShow } }) => {
          if (ExportBriefArgsShow) {
            return (
              <>
                <ProFormSelect
                  name={['ExportBriefArgs', 'ExportTemplate']}
                  label="导出简报模板"
                  initialValue={
                    editValueObj?.ExportBriefSetting?.ExportTemplate ||
                    undefined
                  }
                  colProps={{
                    span: 8,
                  }}
                  fieldProps={{
                    placeholder: '请选择导出简报模板',
                    open: false,
                    dropdownRender: () => <></>,
                    options: fileList,
                    fieldNames: {
                      label: 'Title',
                      value: 'BlobId',
                    },
                    onClick: (e) => {
                      event$.emit('selfDefinedReport_ExportBriefArgs');
                    },
                  }}
                />
                <ProFormTextArea
                  name={['ExportBriefArgs', 'UdfPostScript']}
                  label="后处理配置"
                  colProps={{
                    span: 11,
                  }}
                />
              </>
            );
          }
        }}
      </ProFormDependency>
      <BlobFileHandler
        event$={event$}
        accepts=".docx"
        fileContentType={BlobFileContentType['/selfDefinedReport_ArchiveArgs']}
        formNamePath={['ExportBriefArgs', 'ExportTemplate']}
        setParentFileList={setFileList}
      />
    </ProForm.Group>
  );
};

export default ExportBriefFormItems;
