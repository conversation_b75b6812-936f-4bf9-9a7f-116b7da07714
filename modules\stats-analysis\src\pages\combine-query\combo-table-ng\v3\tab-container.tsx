import { Button, message, Tabs, Tooltip } from 'antd';

import React, { useRef, useState } from 'react';
import './index.less';
import DateRangeWithType from '@uni/components/src/date-range-with-type';
import { InfoCircleOutlined } from '@ant-design/icons';
import loadedConfig from '@/pages/combine-query/config';
import ComboTable from '@/pages/combine-query/combo-table';
import { QueryProps } from '@react-awesome-query-builder/ui';
import { StatsAnalysisEventConstant } from '@/constants';
import {
  CombineQueryContext,
  MetricDetailExtraContent,
  MetricDetailTitle,
} from '@/pages/combine-query/combo-table-ng';
import CombineQueryTable from '@/pages/combine-query/containers/table';
import CombineQueryMetrics from '@/pages/combine-query/containers/metric';
import { Emitter } from '@uni/utils/src/emitter';
import { useModel } from 'umi';
import { isEmptyValues } from '@uni/utils/src/utils';

const tableName = 'OmniCard';

interface ComboQueryV3TableProps {
  detailTableRef: any;
  metricTableRef: any;
  templateSelectorContainerRef: any;
}

interface ComboQueryV3TabContainerProps extends ComboQueryV3TableProps {
  comboTableRef: any;
  queryFields: any;
  state: any;

  basicArgForm: any;
  combineQueryDetail: any;
}

interface ComboQueryV3TabRightContainerProps {
  basicArgForm: any;
  comboTableRef: any;
  setActiveKey: any;
}

const expressionEmptySearch = true;

const TabRightContainer = (props: ComboQueryV3TabRightContainerProps) => {
  let initialValues = {
    DateType: 'OutDate',
    DateFormatType: 'date',
  };
  const dateTypeRangeItemString = localStorage.getItem(
    'comboQueryDateTypeRange',
  );
  if (!isEmptyValues(dateTypeRangeItemString)) {
    try {
      let dateTypeRangeItem = JSON.parse(dateTypeRangeItemString);
      if (!isEmptyValues(dateTypeRangeItem?.DateRangeWithType)) {
        initialValues['DateFormatType'] = dateTypeRangeItem?.DateRangeWithType;
      }
    } catch (error) {
      console.log('comboQueryDateTypeRange', error);
    }
  }

  return (
    <div className={'flex-row-center'} style={{ marginRight: 10 }}>
      <div className={'header-args-container'}>
        <DateRangeWithType
          initialValues={initialValues}
          form={props?.basicArgForm}
          needFormWrapper={true}
          enableDateFormatTypeSelector={true}
          onTypeChangeExtra={(value: string) => {
            let dateTypeRange = {
              DateRangeWithType: value,
            };
            localStorage.setItem(
              'comboQueryDateTypeRange',
              JSON.stringify(dateTypeRange),
            );
          }}
        />
      </div>

      <div className={'header-operations-container'}>
        <Button
          className={'operation-item'}
          style={{ marginRight: 10 }}
          onClick={() => {
            props?.comboTableRef?.current?.onOperationClick('RESET');
          }}
        >
          重置
        </Button>

        <Button
          className={'operation-item'}
          style={{ marginRight: 10 }}
          type={'primary'}
          onClick={async () => {
            // let dateRange = props?.basicArgForm?.getFieldValue('dateRange');
            // if (
            //   isEmptyValues(dateRange) ||
            //   isEmptyValues(dateRange?.at(0)) ||
            //   isEmptyValues(dateRange?.at(1)) ||
            //   props?.comboTableRef?.current?.getTableSize() > 0
            // ) {
            //   message.error('请选择时间或输入条件');
            //   return;
            // }

            let queryResult =
              await props?.comboTableRef?.current?.onOperationClick('QUERY');
            if (queryResult?.success !== false) {
              props?.setActiveKey('COMBO_DETAIL');
            }
          }}
        >
          查询
        </Button>
      </div>

      <div className={'expr-preview-separator'} />

      <Tooltip title={'查看语法'}>
        <InfoCircleOutlined
          style={{ margin: '0px 10px' }}
          onClick={() => {
            props?.comboTableRef?.current?.onOperationClick('VIEW_TEXT');
          }}
        />
      </Tooltip>
    </div>
  );
};

export const ComboQueryTabContainer = (
  props: ComboQueryV3TabContainerProps,
) => {
  const tabContainer = useRef(null);

  const internalTabContainerRef = React.useRef(null);
  const internalTabContainerActiveRef = React.useRef('COMBO_QUERY');

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const ngHeaderHeight =
    document?.getElementById('query-rule-ng-table')?.offsetHeight ?? 40;

  const comboQueryTabContentHeight =
    (
      document?.querySelector(
        '#combo-query-v3-tab-container .ant-tabs-content-holder',
      ) as any
    )?.offsetHeight -
    40 -
    16 * 2;

  const templateIdDetailSyncRef = useRef(null);
  const templateIdComboQuerySyncRef = useRef(null);

  const metricDetailExtraContentContainerRef = useRef(null);

  React.useImperativeHandle(tabContainer, () => {
    return {
      changeActiveKey: (activeKey: string) => {
        internalTabContainerActiveRef.current = activeKey;
        internalTabContainerRef?.current?.setActiveKeyWithInternalFunc(
          activeKey,
        );
      },
      getActiveKey: () => internalTabContainerActiveRef.current,
    };
  });

  return (
    <Tabs
      id={'combo-query-v3-tab-container'}
      tabContainerRef={internalTabContainerRef}
      style={{ height: `calc(100% - ${ngHeaderHeight}px)` }}
      tabPosition="top"
      className={'combo-query-tab-container combo-query-override-container'}
      tabBarExtraContent={{
        right: (
          <TabRightContainer
            basicArgForm={props?.basicArgForm}
            comboTableRef={props?.comboTableRef}
            setActiveKey={(activeKey) => {
              if (internalTabContainerActiveRef.current === 'COMBO_QUERY') {
                internalTabContainerActiveRef.current = activeKey;
                internalTabContainerRef?.current?.setActiveKeyWithInternalFunc(
                  activeKey,
                );
              }
            }}
          />
        ),
      }}
      onChange={(activeKey) => {
        internalTabContainerActiveRef.current = activeKey;
      }}
    >
      <Tabs.TabPane tab="复合条件" key="COMBO_QUERY">
        <CombineQueryContext.Provider
          value={{
            basicArgForm: props?.basicArgForm,
            combineQueryDetail: props?.combineQueryDetail,
          }}
        >
          <div className={'combo-query-outer-container'}>
            <MetricDetailExtraContent
              templateIdSyncRef={templateIdComboQuerySyncRef}
              templateSelectorContainerRef={props?.templateSelectorContainerRef}
              currentActiveKey={'DETAIL'}
              disableSpecialOperator={true}
              onTemplateSelect={(templateId) => {
                templateIdDetailSyncRef?.current?.syncSelectedItemId(
                  templateId,
                );
              }}
              onOperationClick={(type) => {
                let operatorRef = props?.detailTableRef;
                if (operatorRef) {
                  switch (type) {
                    case 'COLUMN_CUSTOMIZE':
                      operatorRef?.current?.openColumnCustomizer();
                      break;
                    case 'SAVE_COLUMN':
                      operatorRef?.current?.saveColumnTemplate();
                      break;
                    case 'SAVE_AS_COLUMN':
                      operatorRef?.current?.saveAsColumnTemplate();
                      break;
                    default:
                      break;
                  }
                }
              }}
            />
            <ComboTable
              tableHeight={comboQueryTabContentHeight ?? 660}
              inFullViewMode={true}
              tableRef={props?.comboTableRef}
              nextGeneration={true}
              {...loadedConfig}
              fields={props?.queryFields}
              value={props?.state.tree}
              onChange={() => {}}
              renderBuilder={() => {
                return null;
              }}
            />
          </div>
        </CombineQueryContext.Provider>
      </Tabs.TabPane>
      <Tabs.TabPane
        tab={
          <MetricDetailTitle
            type={'DETAIL'}
            label={'明细'}
            eventName={
              StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TAB_COUNT
            }
          />
        }
        key="COMBO_DETAIL"
        forceRender={true}
      >
        <CombineQueryContext.Provider
          value={{
            basicArgForm: props?.basicArgForm,
            combineQueryDetail: props?.combineQueryDetail,
          }}
        >
          <div className={'detail-data-container'}>
            <MetricDetailExtraContent
              comboTableRef={props?.comboTableRef}
              detailTableContainerRef={props?.detailTableRef}
              contentContainer={metricDetailExtraContentContainerRef}
              templateIdSyncRef={templateIdDetailSyncRef}
              templateSelectorContainerRef={props?.templateSelectorContainerRef}
              currentActiveKey={'DETAIL'}
              onTemplateSelect={(templateId) => {
                templateIdComboQuerySyncRef?.current?.syncSelectedItemId(
                  templateId,
                );
              }}
              onOperationClick={(type) => {
                let operatorRef = props?.detailTableRef;
                if (operatorRef) {
                  switch (type) {
                    case 'COLUMN_CUSTOMIZE':
                      operatorRef?.current?.openColumnCustomizer();
                      break;
                    case 'SAVE_COLUMN':
                      operatorRef?.current?.saveColumnTemplate();
                      break;
                    case 'SAVE_AS_COLUMN':
                      operatorRef?.current?.saveAsColumnTemplate();
                      break;
                    case 'EXPORT':
                      return operatorRef?.current?.getExportDataConfig();
                    case 'ICDE_EXPORT':
                    case 'OPER_EXPORT':
                    case 'OPER_GROUP_EXPORT':
                      return operatorRef?.current?.getExportDataConfig(type);
                    default:
                      break;
                  }
                }
              }}
            />

            <CombineQueryTable
              activeKey={tabContainer?.current?.getActiveKey()}
              tableRef={props?.detailTableRef}
              templateSelectorContainerRef={props?.templateSelectorContainerRef}
              tableName={tableName}
              needCardContainer={false}
              nextGeneration={true}
              tableHeight={comboQueryTabContentHeight ?? 660}
              metricContainerRef={props?.metricTableRef}
              tabContainer={tabContainer}
              metricDetailExtraContentContainerRef={
                metricDetailExtraContentContainerRef
              }
            />
          </div>
        </CombineQueryContext.Provider>
      </Tabs.TabPane>
      <Tabs.TabPane tab="统计" key="COMBO_METRIC" forceRender={true}>
        <CombineQueryContext.Provider
          value={{
            basicArgForm: props?.basicArgForm,
            combineQueryDetail: props?.combineQueryDetail,
          }}
        >
          <div className={'group-data-container'}>
            <MetricDetailExtraContent
              templateSelectorContainerRef={props?.templateSelectorContainerRef}
              currentActiveKey={'METRIC'}
              onGetExtraTableProps={() => {
                return {
                  dataSource:
                    props?.metricTableRef?.current?.getTableDataSource(),
                  columns: props?.metricTableRef?.current?.getTableColumns(),
                  dictionaryData: globalState?.dictData,
                } as any;
              }}
              onOperationClick={(type) => {
                let operatorRef = props?.metricTableRef;
                if (operatorRef) {
                  switch (type) {
                    case 'COLUMN_CUSTOMIZE':
                      operatorRef?.current?.openColumnCustomizer();
                      break;
                    case 'SAVE_COLUMN':
                      operatorRef?.current?.saveColumnTemplate();
                      break;
                    case 'SAVE_AS_COLUMN':
                      operatorRef?.current?.saveAsColumnTemplate();
                      break;
                    case 'EXPORT':
                      return operatorRef?.current?.getExportDataConfig();
                    default:
                      break;
                  }
                }
              }}
            />

            <CombineQueryMetrics
              detailContainerRef={props?.detailTableRef}
              tableRef={props?.metricTableRef}
              tableName={tableName}
              needCardContainer={false}
              nextGeneration={true}
              tableHeight={comboQueryTabContentHeight ?? 660}
              tabContainer={tabContainer}
            />
          </div>
        </CombineQueryContext.Provider>
      </Tabs.TabPane>
    </Tabs>
  );
};
