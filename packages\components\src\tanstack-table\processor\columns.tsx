import ColumnItemMinWidthProcessor from '../../table/processor/column/column-min-width';
import { ValueTypeProcessor } from '../../table/processor/column/value-type';
import SorterItemProcessor from './sorter';
import FilterItemProcessor from './filter';
import {
  processSearchFilter,
  SearchItemProcessor,
} from '../../table/processor/column/search-item';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import React from 'react';
import isEqual from 'lodash/isEqual';
import { DictionaryProcessor } from '../../table/processor/column/dictionary-processor';
import isNil from 'lodash/isNil';
import {
  TanstackDictionaryProcessor,
  multipleTranslateDelimiter,
} from './dictionary';
import { InfiniteScrollProps } from './infinite-scroll';
import { Tooltip } from 'antd';
import { EmptyWrapper } from '../index';
import { contentTextStyleProcessor } from './text-style';
import { getTooltipPopupContainer } from '../utils';

export interface ColumnProcessorProps {
  dictionaryData?: any;
  // 是否强制columns更新...对于那些title文字，visible等blahblah更新的，绕过tableColumns检查
  forceColumnsUpdate?: boolean;
  // 是否计算宽度
  widthCalculate?: boolean;
  // columnProcessor
  columnItemProcessor?: (columnItem: any, index: number) => any;

  // 桥接 antd columnsState来
  columnsState?: {
    value: any;
  };

  enableMaxCharNumberEllipses?: boolean;
  maxEllipseCharNumber?: number;
  enableColumnResizing?: boolean;

  noMetaProperty?: boolean;
  enableMultipleTranslate?: boolean;
}

export class TanstackColumnProcessor {
  property: ColumnProcessorProps = {};
  propertyColumns = [];
  tableColumns = [];

  constructor(
    property: ColumnProcessorProps,
    propertyColumns: any[],
    tableColumns: any[],
  ) {
    this.property = property;
    this.propertyColumns = propertyColumns;
    this.tableColumns = tableColumns;
  }
  processTableColumns = () => {
    let processors = [
      this.#commonColumnProcessor,
      ColumnItemMinWidthProcessor,
      ValueTypeProcessor,
    ];

    let processColumns = this.propertyColumns
      ? this.propertyColumns
          .slice()
          ?.sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0))
      : [];

    // 当处理过columns 保持处理同一份columns
    if (
      !this.property?.forceColumnsUpdate &&
      this.tableColumns &&
      this.tableColumns?.length > 0 &&
      this.tableColumns?.length === this.propertyColumns?.length &&
      // 严格意义上相等 包括顺序也要相等
      isEqual(this.tableColumns, this.propertyColumns)
    ) {
      processColumns = this.tableColumns?.slice();
    }

    // TODO 处理columns
    // tableColumns = tableColumns.slice().map((item, index) => {
    for (let index = 0; index < processColumns.length; index++) {
      let item = processColumns?.at(index);

      // 存在children的时候是不用过process的
      if (item.children && item.children?.length > 0) {
        item.children = this.#childItemProcessor(item, processors);
        continue;
      }

      item = this.#itemProcess(item, processors);
      if (this.property?.columnItemProcessor) {
        item = Object.assign(
          {},
          this.property?.columnItemProcessor(item, index),
        );
      }
      processColumns[index] = item;
    }

    // dictionary columns
    processColumns = TanstackDictionaryProcessor(
      processColumns,
      this.property?.dictionaryData,
      this.property?.enableMaxCharNumberEllipses ?? false,
      this.property?.noMetaProperty === true ? null : 'meta',
      this.property?.maxEllipseCharNumber,
      this.property?.enableColumnResizing,
      this.property?.enableMultipleTranslate,
    )?.slice();

    // sort
    processColumns = processColumns?.sort(
      (a, b) => (a?.order ?? 0) - (b?.order ?? 0),
    );

    return processColumns;
  };

  #dataItemWithColumnModuleProcessor = (module, dataItem) => {
    if (this.property?.dictionaryData?.[module]) {
      let dictionaryItem = this.property?.dictionaryData?.[module]?.find(
        (item) => item.Code === dataItem,
      );
      if (dictionaryItem) {
        return dictionaryItem?.Name || dataItem;
      }
    }

    return dataItem;
  };

  #commonColumnProcessor = ({ columnItem }) => {
    columnItem.hideInTable = !columnItem?.visible;

    if (!isEmptyValues(this.property?.columnsState?.value)) {
      this.#columnStateProcessor(columnItem);
    }

    if (
      !columnItem.render &&
      (!columnItem.valueType || columnItem?.valueType === 'text')
    ) {
      columnItem['normalRenderer'] = true;
      columnItem.render = (node, record, index) => {
        // module & moduleGroup
        let dataItem = record[columnItem.dataIndex];
        if (columnItem?.isExtraProperty === true) {
          dataItem = record?.['ExtraProperties']?.[columnItem.dataIndex];
        }
        if (columnItem?.dictionaryModule) {
          dataItem = this.#dataItemWithColumnModuleProcessor(
            columnItem?.dictionaryModule,
            dataItem,
          );
        }

        let enableTooltip = false;
        let mergedEllipseStyle = contentTextStyleProcessor(columnItem);

        if (
          this?.property?.enableMaxCharNumberEllipses === true &&
          dataItem > (this?.property?.maxEllipseCharNumber ?? 20)
        ) {
          enableTooltip = true;
        }

        if (this.property?.enableColumnResizing === true) {
          enableTooltip = true;
        }

        const CellTooltipWrapper =
          enableTooltip === true ? Tooltip : EmptyWrapper;

        // 处理一下可能会出现的多个的场景
        let columnValue = '';
        const dataItemsSplitedDelimiter = dataItem
          ?.toString()
          ?.split(multipleTranslateDelimiter);
        if (dataItemsSplitedDelimiter?.length > 1) {
          let translatedDataItems = [];
          dataItemsSplitedDelimiter?.forEach((dataItem) => {
            translatedDataItems.push(
              valueNullOrUndefinedReturnDash(
                dataItem,
                // columnItem['x-type'] || columnItem['format'],
                // columnItem['x-scale'],
                columnItem['dataType'],
                columnItem['scale'],
              ),
            );
          });
          columnValue = translatedDataItems?.join(multipleTranslateDelimiter);
        } else {
          columnValue = valueNullOrUndefinedReturnDash(
            dataItem,
            // columnItem['x-type'] || columnItem['format'],
            // columnItem['x-scale'],
            columnItem['dataType'],
            columnItem['scale'],
          );
        }

        console.log('row-cell', columnValue);

        return (
          <CellTooltipWrapper
            title={columnValue}
            placement={'topLeft'}
            getPopupContainer={getTooltipPopupContainer}
          >
            <span style={mergedEllipseStyle}>{columnValue}</span>
          </CellTooltipWrapper>
        );
      };
    }

    return {
      ...columnItem,
    };
  };

  #columnStateProcessor = (columnItem: any) => {
    let currentColumnItemState =
      this?.property?.columnsState?.value?.[columnItem?.dataIndex];
    if (!isEmptyValues(currentColumnItemState)) {
      columnItem['hideInTable'] = !currentColumnItemState?.['show'] ?? true;
      columnItem['visible'] = !currentColumnItemState?.['show'] ?? true;
      columnItem['order'] = currentColumnItemState?.['order'] ?? 999;

      columnItem['textOverflowType'] =
        currentColumnItemState?.['textOverflowType'] ?? 'none';
    }
  };

  #childItemProcessor = (item: any, processors: any[]) => {
    return item.children
      ?.map((childItem) => {
        if (childItem?.children && childItem.children?.length > 0) {
          childItem['children'] = this.#childItemProcessor(
            childItem,
            processors,
          );
        } else {
          childItem = this.#itemProcess(childItem, processors);
        }

        return childItem;
      })
      .slice();
  };

  #itemProcess = (item, processors: any[]) => {
    for (let processor of processors) {
      item = Object.assign(
        {},
        processor({
          // definitionProcessor
          columnItem: item,
          ...item,
          // 是否计算宽度
          widthCalculate: this.property?.widthCalculate,
        }),
      );
    }

    return item;
  };
}
