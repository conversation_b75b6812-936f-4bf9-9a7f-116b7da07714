import { Input, InputNumber, Select, Switch } from 'antd';
import UniReportSelect from '@/components/report-select/UniReportSelect';
import UniReportSwitch from '@/components/report-switch/UniReportSwitch';
import Datepicker from '@uni/components/src/picker/datepicker';
import UniReportPeriodic from '@/components/report-periodic/UniReportPeriodic';
import ReportRestrictInputNumber from '@/components/report-number';
import ReportFormRangePicker from '@/components/report-date-range';
import { UniInput, UniInputNumber } from '@uni/components/src';

export const argComponents = {
  UniInput: UniInput,
  UniInputNumber: UniInputNumber,
  UniReportSelect: UniReportSelect,

  UniReportFormRangePicker: ReportFormRangePicker,

  UniDatePicker: Datepicker,

  UniReportPeriodic: UniReportPeriodic,

  UniReportSwitch: UniReportSwitch,

  // 数字输入限制
  UniRestrictInputNumber: ReportRestrictInputNumber,
};
