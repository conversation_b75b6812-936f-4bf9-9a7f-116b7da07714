import React, { FC, useEffect, useState } from 'react';
import { Select } from 'antd';
import { v4 as uuidv4 } from 'uuid';
import './index.less';
import { SelectProps } from 'antd/lib/select';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { useModel } from 'umi';
import { DictionaryItem, RespVO } from '@uni/commons/src/interfaces';
import { dmrMetaDataService, metaDataService } from '@uni/services/src';

const { Option } = Select;

interface UniReportSelectProps extends SelectProps {
  width?: number;
  dataSource?: any[];

  dataSourceProcessor?: (dataSource: any[]) => any[];
  optionTitleKey?: string;
  optionNameKey?: string;
  optionValueKey?: string;
  className?: string;
  style?: React.CSSProperties;
  onChange?: (value: any) => void;
  onFilterOptions?: (inputValue) => boolean;
  value?: string | string[];
  allowClear?: boolean;
  enablePinyinSearch?: boolean;
  placeholder?: string;
  showSearch?: boolean;
  label?: string;
  /**
   * model中某一对象的key -> dataSource
   */
  modelDataKey?: string;
  moduleGroupKey?: string;
}

const UniReportSelect: FC<UniReportSelectProps> = ({
  width,
  className,
  style,
  optionTitleKey,
  optionNameKey = 'name',
  optionValueKey = 'value',
  dataSource,
  onChange,
  onFilterOptions,
  enablePinyinSearch = true,
  label,
  modelDataKey,
  moduleGroupKey,
  dataSourceProcessor,
  ...restProps
}: UniReportSelectProps) => {
  // dataSource from model
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [selectedValue, setSelectedValue] = useState(undefined);

  const [selectDataSource, setSelectDataSource] = useState(dataSource || []);

  useEffect(() => {
    const modelData = moduleGroupKey
      ? globalState?.dictData?.[moduleGroupKey]
      : globalState?.dictData;
    if (modelData && modelDataKey) {
      let modelDataSource = modelData[modelDataKey];
      if (modelDataSource) {
        if (Array.isArray(modelDataSource)) {
          setSelectDataSource(modelDataSource);
        }
      } else {
        // 接口请求获取
        getModulesData();
      }
    }
  }, [globalState, modelDataKey]);

  const getModulesData = async () => {
    let response: RespVO<DictionaryItem[]> = await metaDataService(
      modelDataKey,
      moduleGroupKey,
    );

    if (response?.code === 0 && response?.statusCode === 200) {
      setSelectDataSource(response?.data);
    }
  };

  return (
    <div style={width ? { width: width } : {}} className={'select-container'}>
      {label && <label>{label}</label>}
      <Select
        className={`select ${className}`}
        style={{ ...{}, ...style }}
        showSearch
        allowClear={restProps?.allowClear || true}
        value={restProps.value || selectedValue}
        getPopupContainer={(trigger) =>
          (restProps?.getPopupContainer &&
            restProps?.getPopupContainer(trigger)) ||
          document.body
        }
        placeholder={
          selectDataSource?.length ? `${restProps?.placeholder}` : ''
        }
        filterOption={(inputValue, option) => {
          let filterOptionsResult = true;
          if (onFilterOptions) {
            filterOptionsResult = onFilterOptions(inputValue);
          }
          return (
            (filterOptionsResult &&
              option &&
              option.children
                ?.toString()
                ?.toLowerCase()
                ?.indexOf(inputValue) !== -1) ||
            (enablePinyinSearch &&
              pinyinInitialSearch(
                option.children?.toString()?.toLowerCase(),
                inputValue.toLowerCase(),
              ))
          );
        }}
        maxTagCount={'responsive'}
        {...restProps}
        onChange={(value: any) => {
          setSelectedValue(value);
          onChange && onChange(value);
        }}
      >
        {(dataSourceProcessor
          ? dataSourceProcessor(selectDataSource || [])
          : selectDataSource || []
        )?.map((item) => {
          return (
            <Option
              key={uuidv4()}
              title={item[optionTitleKey || optionNameKey]}
              value={item[optionValueKey]}
            >
              {item[optionNameKey]}
            </Option>
          );
        })}
      </Select>
    </div>
  );
};

export default React.memo(UniReportSelect);
