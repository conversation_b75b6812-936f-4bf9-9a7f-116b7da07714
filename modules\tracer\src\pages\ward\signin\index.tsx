import {
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import { Dispatch, useDispatch, useModel, useSelector } from 'umi';
import { UniTable } from '@uni/components/src';
import {
  <PERSON><PERSON>,
  Button,
  Card,
  Col,
  Divider,
  InputNumber,
  InputRef,
  Modal,
  Popconfirm,
  Row,
  Space,
  Tooltip,
  message,
  notification,
} from 'antd';
import { useSafeState, useKeyPress, useDebounce } from 'ahooks';
import {
  IModalState,
  IReducer,
  ITableState,
} from '@uni/reducers/src/Interface';
import { ActionRecordItem, SwagTraceRecordItem } from '../../interface';
import { columnsHandler, handleMrActionApi, isRespErr } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType, SigninType } from '@/Constants';
import {
  ModalAction,
  TableAction,
  modalReducer,
  tableReducer,
  InitTableState,
  InitModalState,
} from '@uni/reducers/src';
import dayjs from 'dayjs';
import {
  ProForm,
  ProFormDependency,
  ProFormGroup,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@uni/components/src/pro-form';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { SingleSignInColumns } from './columns';
import { useTimelineReq } from '@/hooks';
import PatTimeline from '@/components/PatTimeline';
import { FileExcelOutlined, UndoOutlined } from '@ant-design/icons';
import { exportExcel } from '@uni/utils/src/excel-export';
import { exportExcelDictionaryModuleProcessor } from '@uni/components/src/table/processor/data/export';
import { modalSelectedColumns } from '@/pages/archive/columns';
import clsx from 'clsx';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import './index.less';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';

const WardSignIn = () => {
  const {
    globalState: { searchParams, dictData, userInfo },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  const proFormRef = useRef<ProFormInstance>();

  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();
  const [revertRecord, setRevertRecord] = useSafeState(null);

  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagTraceRecordItem & ActionRecordItem>, IReducer>
  >(tableReducer, InitTableState);

  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<SwagTraceRecordItem[]>, IReducer>
  >(modalReducer, {
    ...InitModalState,
    specialData: undefined,
  });
  // modal selected table key
  const [selectedRecordKey, setSelectedRecordKey] = useSafeState([]);
  // modal columns key
  const [modalColumns, setModalColumns] = useSafeState([]);
  // modal alert
  const [modalAlert, setModalAlert] = useSafeState(false);

  // 节流隐式标识
  const hiddenLoading = useRef(false);
  // 还是使用ref来替换document.getId
  const barCodeRef = useRef<InputRef>(null);
  // 设计思路是先判断是不是相同的 不是就没事 是就进debounce判断
  const newestBarCode = useRef(undefined);

  // enter 监听
  useKeyPress(
    'enter',
    () => {
      console.log('press enter only');
      proFormRef.current.validateFields().then((values) => {
        autoFetchReqActionReq(values);
      });
    },
    {
      exactMatch: true,
      target: document.getElementById('signInForm'),
    },
  );

  // enter 逻辑
  const autoFetchReqActionReq = ({ BarCode }, type = 'Single') => {
    if (!BarCode) return;
    if (hiddenLoading.current) return;
    hiddenLoading.current = true;
    proFormRef.current
      .validateFields()
      .then((values) => {
        if (values?.SignType?.value === 'BarCode') {
          searchByBarCodeReq(values, false);
        } else {
          // 对于非BarCode类型，构建参数对象
          const searchParam = {
            ..._.omit(values, 'BarCode'),
            [values.SignType.value]: values.BarCode,
          };
          searchOneReq(searchParam, false);
          // searchOneReq(values, false);
        }
      })
      .catch((err) => {
        hiddenLoading.current = false;
        message.error('请确认已填写所有归档信息！');
      });
  };

  // 查询结果处理
  const searchResultHandler = (params, res) => {
    if (!isRespErr(res)) {
      let resData;
      if (res?.data?.Items) {
        // Api/Mr/TraceRecord/GetList
        resData = res?.data?.Items?.slice();
      } else {
        // Api/Mr/TraceRecord/GetListByBarCode
        resData = res?.data?.slice();
      }
      if (resData?.length === 1) {
        // 单个，直接处理
        reqActionReq(
          { ...params, BarCode: resData?.at(0)?.BarCode },
          null,
          ReqActionType.wardSignOut,
        );
      } else if (resData?.length > 1) {
        // 多条，modal提示处理
        ModalStateDispatch({
          type: ModalAction.change,
          payload: {
            visible: true,
            record: resData,
            specialData: res?.data?.Items ? params : params?.BarCode,
            actionType: undefined,
          },
        });
      } else {
        // 没查到数据
        // 重置节流标识
        hiddenLoading.current = false;

        Modal.confirm({
          title: `查无数据`,
          content: '请确认病案标识填写正确',
          onCancel: () => {
            proFormRef.current.resetFields(['BarCode']);
            focusBarCode();
          },
          onOk: () => {
            proFormRef.current.resetFields(['BarCode']);
            focusBarCode();
          },
          cancelButtonProps: { style: { display: 'none' } },
        });
      }
    } else {
      // 重置节流标识
      hiddenLoading.current = false;
    }
  };

  // 批量查询，查询→操作→查询(只有归档需要再次查询，以获取上架号)
  const searchOneReq = async (data: any, needDataPush = false) => {
    if (!data) return;
    // 构建请求数据
    const requestData = {
      ..._.omit(data, 'OutWard'),
      SkipCount: 0,
      MaxResultCount: 999999,
    };

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetList`,
          method: 'POST',
          data: requestData,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    searchResultHandler(data, res);
  };

  // 特殊debounce 扫码枪才需要
  const handleBarCodeDebounce = (barCode) => {
    if (SearchTable?.data?.findIndex((d) => d?.BarCode === barCode) > -1) {
      // 判断时间 存在与表内 长于 特定时间，比如 2s
      if (
        dayjs().diff(
          SearchTable?.data?.find((d) => d?.BarCode === barCode)?.InsertTime,
        ) < 2000
      ) {
        // 拦截
        hiddenLoading.current = false;
        proFormRef?.current.setFieldValue('BarCode', '');
        return true;
      }
    }

    newestBarCode.current = barCode;
    return false;
  };

  // 扫码枪条码，走这里
  const searchByBarCodeReq = async (params: any, needDataPush = false) => {
    if (!params) return;

    // 扫之前，先做判断 判断条码号与上一次扫的 & 表格内的 是否相符 如果能匹配出来则特殊报错
    if (handleBarCodeDebounce(params?.BarCode)) {
      // 拦截
      return;
    }

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetListByBarCode`,
          method: 'POST',
          data: {
            BarCode: params?.BarCode,
            SkipCount: 0,
            MaxResultCount: 999999,
          },
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    searchResultHandler(params, res);
  };

  // 签收 / 撤销
  const reqActionReq = async (data: any, item: any, reqType: ReqActionType) => {
    if (!data || !reqType) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `Tracing/${reqType}`,
        requestParams: {
          url: `Api/Mr/Tracing/${reqType}`,
          method: 'POST',
          data,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    // 不管结果 重置节流标识
    hiddenLoading.current = false;
    if (!isRespErr(res)) {
      if (reqType === ReqActionType.wardSignOut) {
        // 把modal关闭
        ModalStateDispatch({
          type: ModalAction.init,
        });
        setSelectedRecordKey([]);
        setModalAlert(false);

        // StatusCode 黄神的单独处理
        let result = handleMrActionApi(res.data);
        console.log('res', data, result);

        result?.isCorrect
          ? notification.success({
              message: '签收成功',
              description: `${data?.SignType?.label}: ${
                result?.data?.[0]?.[data?.SignType?.value]
              }`,
            }) // message.success('签收成功')
          : notification.error({
              message: '签收失败',
              description: result?.errMsg?.join('。/n'),
            }); // message.error(result?.errMsg?.join('。/n'));

        if (result?.data?.length > 0) {
          // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
          // 把数据插入已记录列表
          // 在这里添加一个插入时间
          SearchTableDispatch({
            type: TableAction.dataUnshiftUniq,
            payload: {
              data: {
                ...(Array.isArray(result?.data)
                  ? result?.data?.at(0)
                  : result?.data),
                InsertTime: dayjs(),
              },
              key: 'BarCode',
              overWriteBy: {
                key: 'isCorrect',
                value: true,
              },
            },
          });
        }

        if (result?.errType === '404') {
          // 404
          Modal.error({
            title: result?.errMsg?.join('。/n'),
            onOk: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            onCancel: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
          });
        } else {
          // 其他的 barCode自动清除
          proFormRef.current.resetFields(['BarCode']);
          focusBarCode();
        }
      } else {
        // revert
        SearchTableDispatch({
          type: TableAction.dataFilt,
          payload: {
            key: 'BarCode',
            value: item.BarCode,
          },
        });
        setRevertRecord(item);

        message.success('撤销成功');
      }
    }
  };

  // columns 处理，主要用于处理options
  const columnsSolver = useMemo(() => {
    return SearchTable.columns
      ? columnsHandler(SearchTable.columns, {
          dataIndex: 'option',
          title: '操作',
          visible: true,
          width: 60,
          align: 'center',
          fixed: 'right',
          order: 11,
          render: (
            text,
            record: SwagTraceRecordItem & { isCorrect: boolean },
          ) => {
            return (
              record?.isCorrect && (
                <Popconfirm
                  key="revert"
                  title="确定要撤销？"
                  onConfirm={(e) => {
                    e.stopPropagation();
                    reqActionReq(
                      { BarCodes: [record.BarCode] },
                      record,
                      ReqActionType.wardRevertSignOut,
                    );
                  }}
                  onCancel={(e) => e.stopPropagation()}
                >
                  <Tooltip title="撤销">
                    <UndoOutlined
                      className="icon_blue-color"
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Tooltip>
                </Popconfirm>
              )
            );
          },
        })
      : [];
  }, [SearchTable.columns]);

  // 处理撤销后的操作
  useEffect(() => {
    // 时间轴如果匹配则值空
    if (SearchTable?.clkItem?.BarCode === revertRecord?.BarCode) {
      SearchTableDispatch({
        type: TableAction.clkChange,
        payload: {
          clkItem: null,
        },
      });
      setParams(null);
    }
  }, [revertRecord]);

  // columns处理 [初始进来时]
  if (columnsList?.['WardSignedOutList'] && SearchTable.columns.length < 1) {
    SearchTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          SingleSignInColumns,
          columnsList['WardSignedOutList'],
        ),
      },
    });
    setModalColumns(
      tableColumnBaseProcessor(
        modalSelectedColumns,
        columnsList['WardSignedOutList'],
      ),
    );
  }

  const focusBarCode = () => {
    // 定位
    setTimeout(() => {
      barCodeRef.current?.focus({ cursor: 'end' });
    }, 20);
  };

  console.log(
    'userInfo',
    userInfo,
    SearchTable.data,
    columnsList?.['TraceRecord'],
  );

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col xxl={7} xl={8}>
          <Card title="病区签收信息" style={{ marginBottom: '15px' }}>
            <ProForm
              layout="horizontal"
              className="ward_signin_info_form" // flex-wrap
              grid
              id="signInForm"
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 'auto' }}
              formRef={proFormRef}
              submitter={{
                render: (props, doms) => {
                  return [
                    <Button
                      type="primary"
                      style={{
                        width: 'calc(100% - 120px)',
                        float: 'right',
                        marginTop: '8px',
                      }}
                      key="submit"
                      onClick={() => {
                        if (hiddenLoading.current) return;
                        hiddenLoading.current = true;
                        props.form
                          .validateFields()
                          .then((values) => {
                            if (values?.SignType?.value === 'BarCode') {
                              searchByBarCodeReq(values);
                            } else {
                              // 对于非BarCode类型，构建参数对象
                              const searchParam = {
                                ..._.omit(values, 'BarCode'),
                                [values.SignType.value]: values.BarCode,
                              };
                              searchOneReq(searchParam, false);
                            }
                          })
                          .catch((err) => {
                            hiddenLoading.current = false;
                          });
                      }}
                    >
                      签收(Enter)
                    </Button>,
                  ];
                },
              }}
            >
              <ProFormGroup style={{ flexWrap: 'nowrap' }}>
                <ProFormSelect
                  name="SignType"
                  colProps={{ flex: '120px' }}
                  allowClear={false}
                  initialValue={{
                    label: SigninType[0].title,
                    value: SigninType[0].value,
                  }}
                  fieldProps={{
                    labelInValue: true,
                    fieldNames: {
                      label: 'title',
                      value: 'value',
                    },
                  }}
                  rules={[{ required: true }]}
                  options={SigninType as any[]}
                />
                <ProFormDependency name={['SignType']}>
                  {({ SignType }) => {
                    return (
                      <ProFormText
                        id="barCodeInput"
                        colProps={{ flex: 'auto' }}
                        name="BarCode"
                        placeholder={
                          SignType?.value === 'BarCode'
                            ? '条码号(扫码)'
                            : `请输入${SignType?.label}`
                        }
                        fieldProps={{
                          ref: barCodeRef,
                        }}
                        rules={[{ required: true }]}
                      />
                    );
                  }}
                </ProFormDependency>
              </ProFormGroup>
              <ProFormSelect
                name={'OutWard'}
                label="病区"
                rules={[{ required: true }]}
                options={
                  userInfo?.Wards?.length > 0
                    ? dictData?.Wards?.filter((data) =>
                        userInfo?.Wards?.find(
                          (userData) => userData === data.Code,
                        ),
                      )
                    : (dictData?.Wards as any[])
                }
                initialValue={userInfo?.Wards?.at(0)}
                fieldProps={{
                  // labelInValue: true,
                  fieldNames: {
                    label: 'Name',
                    value: 'Code',
                  },
                  filterOption: (inputValue, option) => {
                    return (
                      (option &&
                        option.label
                          ?.toString()
                          ?.toLowerCase()
                          ?.indexOf(inputValue) !== -1) ||
                      option.value
                        ?.toString()
                        ?.toLowerCase()
                        ?.indexOf(inputValue) !== -1 ||
                      pinyinInitialSearch(
                        option.label?.toString()?.toLowerCase(),
                        inputValue.toLowerCase(),
                      )
                    );
                  },
                }}
              />
            </ProForm>
          </Card>
          <PatTimeline
            item={SearchTable?.clkItem}
            loading={loadings['TraceRecord/GetActions']}
            timelineItems={timelineItems}
          />
        </Col>
        <Col xxl={17} xl={16}>
          <Card
            title="已签收列表"
            extra={
              <Space>
                <Divider type="vertical" />
                <Popconfirm
                  title="导出时会将错误的记录过滤掉"
                  onConfirm={(e) => {
                    let exportColumns = columnsSolver?.filter(
                      (columnItem) =>
                        columnItem.className?.indexOf('exportable') > -1 &&
                        columnItem.valueType !== 'option' &&
                        columnItem.dataIndex !== 'operation',
                    );
                    exportExcel(
                      exportColumns,
                      exportExcelDictionaryModuleProcessor(
                        exportColumns,
                        _.cloneDeep(
                          SearchTable.data?.filter((d) => d.isCorrect),
                        ),
                      ),
                      `病案单份签收列表__${dayjs().format('YYYY-MM-DD')}`,
                      [],
                    );
                  }}
                  disabled={
                    SearchTable.data?.filter((d) => d.isCorrect)?.length < 1
                  }
                >
                  <Tooltip title="导出Excel">
                    <Button
                      type="text"
                      shape="circle"
                      disabled={
                        SearchTable.data?.filter((d) => d.isCorrect)?.length < 1
                      }
                      icon={<FileExcelOutlined />}
                    />
                  </Tooltip>
                </Popconfirm>
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl: 'Api/Mr/TraceRecord/WardSignedOutList',
                    onTableRowSaveSuccess: (columns) => {
                      // 这个columns 存到dva
                      dispatch({
                        type: 'global/saveColumns',
                        payload: {
                          name: 'WardSignedOutList',
                          value: columns,
                        },
                      });
                      SearchTableDispatch({
                        type: TableAction.columnsChange,
                        payload: {
                          columns: tableColumnBaseProcessor(
                            SingleSignInColumns,
                            columns,
                          ),
                        },
                      });
                    },
                  }}
                />
              </Space>
            }
          >
            <UniTable
              id="trace_record"
              rowKey="uuid"
              showSorterTooltip={false}
              loading={loadings['TraceRecord/WardSignedOutList'] || false}
              columns={columnsSolver} // columnsHandler
              dataSource={SearchTable.data}
              dictionaryData={dictData}
              scroll={{ x: 'max-content' }}
              rowClassName={(record, index) => {
                let classname = [];
                // 互斥
                if (!record?.isCorrect) {
                  classname.push('row-error');
                } else if (index === 0) {
                  return 'row-first';
                }
                if (record?.uuid === SearchTable.clkItem?.uuid) {
                  classname.push('row-selected');
                }
                return classname.length > 0 ? clsx(classname) : null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (SearchTable.clkItem?.BarCode !== record?.BarCode) {
                      SearchTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
            />
          </Card>
        </Col>
      </Row>

      <Modal
        title="确认病案"
        open={ModalState.visible}
        width={900}
        onOk={(e) => {
          if (
            ModalState.record.findIndex(
              (d) => d.BarCode === selectedRecordKey?.at(0),
            ) > -1
          ) {
            reqActionReq(
              selectedRecordKey?.at(0),
              null,
              ReqActionType.wardSignOut,
            );
          } else {
            // 没选
            setModalAlert(true);
          }
        }}
        okButtonProps={{
          loading: loadings[`Tracing/${ReqActionType.wardSignOut}`],
        }}
        onCancel={(e) => {
          // 重置节流标识
          hiddenLoading.current = false;
          focusBarCode();
          ModalStateDispatch({
            type: ModalAction.init,
          });
          setSelectedRecordKey([]);
          setModalAlert(false);
        }}
      >
        <UniTable
          id="multi_record_check"
          rowKey="BarCode"
          showSorterTooltip={false}
          loading={
            loadings['TraceRecord/WardSignedOutList'] ||
            loadings[`Tracing/${ReqActionType.wardSignOut}`] ||
            false
          }
          columns={modalColumns} // columnsHandler
          dataSource={ModalState.record}
          scroll={{ x: 'max-content' }}
          tableAlertRender={() => {
            return modalAlert ? (
              <Alert
                message="请选择一个病案"
                description="请选择一个病案再点击确认，如果没有查询到目标病案请确认信息输入正确"
                type="error"
                closable
                onClose={() => {
                  setModalAlert(false);
                }}
              />
            ) : (
              false
            );
          }}
          tableAlertOptionRender={false}
          rowSelection={{
            alwaysShowAlert: true,
            type: 'radio',
            selectedRowKeys: selectedRecordKey,
            onChange: (
              selectedRowKeys: React.Key[],
              selectedRows: SwagTraceRecordItem[],
            ) => {
              setSelectedRecordKey(selectedRowKeys);
              setModalAlert(false);
            },
          }}
          onRow={(record) => {
            return {
              onClick: (event) => {
                setSelectedRecordKey([record?.BarCode]);
              },
            };
          }}
        />
      </Modal>
    </>
  );
};

export default WardSignIn;
