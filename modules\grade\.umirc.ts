import { name } from './package.json';
import {
  slaveCommonConfig,
  extraBabelIncludes,
  extraWebPackPlugin,
} from '../../.umirc.commom';

export default {
  base: name,
  publicPath: '/grade/',
  outputPath: '../../dist/grade',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  qiankun: { slave: {} },

  plugins: [
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/quality/hosp',
    },
    {
      path: '/quality/hosp',
      exact: true,
      component: '@/pages/quality/hosp/index2',
    },
    {
      path: '/quality/dept',
      exact: true,
      component: '@/pages/quality/dept/index',
    },
    {
      path: '/quality/medTeam',
      exact: true,
      component: '@/pages/quality/medTeam/index',
    },
    {
      path: '/sd/hosp',
      exact: true,
      component: '@/pages/sd/hosp/index2',
    },
    {
      path: '/sd/dept',
      exact: true,
      component: '@/pages/sd/dept/index',
    },
    {
      path: '/sd/medTeam',
      exact: true,
      component: '@/pages/sd/medTeam/index',
    },
    {
      path: '/complication/hosp',
      exact: true,
      component: '@/pages/complication/hosp/index2',
    },
    {
      path: '/complication/dept',
      exact: true,
      component: '@/pages/complication/dept/index',
    },
    {
      path: '/complication/medTeam',
      exact: true,
      component: '@/pages/complication/medTeam/index',
    },
    {
      path: '/serviceability/hosp',
      exact: true,
      component: '@/pages/serviceability/hosp/index2',
    },
    {
      path: '/serviceability/dept',
      exact: true,
      component: '@/pages/serviceability/dept/index2',
    },
    {
      path: '/serviceability/medTeam',
      exact: true,
      component: '@/pages/serviceability/medTeam/index',
    },
  ],

  proxy: {
    '/gradeAddress': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/gradeAddress': '' },
      secure: false, // https的dev后端的话需要配
    },
    '/common': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/common': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
};
