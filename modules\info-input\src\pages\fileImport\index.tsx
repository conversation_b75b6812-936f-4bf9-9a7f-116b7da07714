import { Reducer, useEffect, useMemo, useState, useRef } from 'react';
import { Button, Card, Col, Row, Space, Form, Tag, List, message } from 'antd';
import { useSafeState, useUpdateEffect } from 'ahooks';
import _ from 'lodash';
import {
  TriggerKeys,
  ImportTabsKeys,
  // ImportType,
  LocationImportType,
  NormalImport,
} from '../components/constants';
import { calculateTime } from '../components/rencentTaskList/utils';
import SelfForm, { IUniFormRef } from '@/components/SelfForm';
import { useLocation } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { useModel } from '@@/plugin-model/useModel';
import RencentTaskList from '../components/rencentTaskList';
import { UploadOutlined } from '@ant-design/icons';
import { Upload } from 'antd';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';

const InfoImport = () => {
  const location = useLocation();

  const { globalState } = useModel('@@qiankunStateFromMaster');
  const [tabItems, setTabItems] = useSafeState({
    data: null,
    infos: [],
    id: null,
  });

  const [udfParams, setUdfParams] = useState([]);
  const [taskId, setTaskId] = useState('');

  const {
    data: triggerUdfConfigData,
    loading,
    run: getTriggerUdfConfigsReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Sys/UdfConfigSys/GetTriggerUdfConfigs`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data[0];
        }
      },
      onSuccess: (res, params) => {
        if (res) {
          setUdfParams(res?.UdfParams);
          setTaskId(res?.UdfScript);
          // recentTaskMastersReq({ TaskId: res?.UdfScript });
        }
      },
    },
  );

  useEffect(() => {
    if (TriggerKeys?.[location.pathname]) {
      getTriggerUdfConfigsReq({ trigger: TriggerKeys?.[location.pathname] });
    }
  }, [location]);

  useEffect(() => {
    let index = Object.keys(LocationImportType).findIndex(
      (d) => d === location.pathname,
    );
    let lastUdfParams = [];
    if (udfParams?.length) {
      lastUdfParams = NormalImport.filter((item) =>
        udfParams.find((u) => u === item?.name),
      );
    }
    if (index !== -1 && lastUdfParams.length !== 0) {
      setTabItems({
        data: ImportTabsKeys[index],
        infos: lastUdfParams,
        id: triggerUdfConfigData?.Id,
      });
    }
  }, [udfParams]);

  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);

  // excel upload
  const {
    loading: configurationModulesUploadExcelLoading,
    run: configurationModulesUploadExcelReq,
  } = useRequest(
    (data) => {
      return uniCommonService(
        `Api/Udf/Udf/ExecuteInBackground/${triggerUdfConfigData?.Id}`,
        {
          method: 'POST',
          requestType: 'form',
          data,
        },
      );
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res.code === 0 && res.statusCode === 200) {
          return 'success';
        } else {
          return 'failed';
        }
      },
      onSuccess: (data) => {
        if (data === 'success') {
          message.success('导入成功!');
          setFileList([]);
          //   // TODO reload
          //   if (treeSelectedItem.NotExisted) {
          //     setTreeSelectedItem({ ...treeSelectedItem, NotExisted: false });
          //   } else {
          //     setNeedReload(true);
          //   }
        }
      },
    },
  );

  const handleUpload = async () => {
    const formData = new FormData();
    fileList.forEach((file) => {
      formData.append('File', file as RcFile);
    });
    setUploading(true);

    let res = await configurationModulesUploadExcelReq(formData);

    if (res) {
      setUploading(false);
    }
  };

  const props: UploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      // setFileList([...fileList, file]);
      setFileList([file]);
      return false;
    },
    fileList,
    accept: '.xls,.xlsx',
    listType: 'picture',
  };

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={16}>
          <Card
            title={LocationImportType[location.pathname]}
            loading={loading}
            bodyStyle={{
              overflowY: 'auto',
              height: 480,
            }}
          >
            <Form labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
              <Form.Item label="文件" name="file">
                <Upload {...props}>
                  <Button icon={<UploadOutlined />}>选择文件</Button>
                </Upload>
              </Form.Item>
              <Form.Item wrapperCol={{ offset: 6, span: 14 }}>
                <Button
                  type="primary"
                  onClick={handleUpload}
                  disabled={fileList.length === 0}
                  loading={uploading}
                >
                  {uploading ? '正在上传' : '确认导入'}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
        <Col span={8}>
          <RencentTaskList
            taskId={taskId}
            triggerUdfConfigData={triggerUdfConfigData}
          />
        </Col>
      </Row>
    </>
  );
};

export default InfoImport;
