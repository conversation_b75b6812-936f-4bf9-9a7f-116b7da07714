import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ProDescriptions } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  Button,
  Progress,
  Card,
  Badge,
  Row,
  Col,
  Tooltip,
  Modal,
  Divider,
  Alert,
} from 'antd';
import { v4 as uuidv4 } from 'uuid';
import './index.less';

const UniProgress = (props) => {
  function calculatePercentagePosition(value, minValue, maxValue) {
    const percentagePosition =
      ((value - minValue) / (maxValue - minValue)) * 100;
    return parseFloat(percentagePosition.toFixed(2));
  }

  // 当前费用位置
  const TotalFeePos = (data) => {
    // 计算总费用Icon位置
    let iconPos =
      data?.TooLowBoundFee && data?.TooHighBoundFee
        ? calculatePercentagePosition(
            data?.TotalFee,
            data?.TooLowBoundFee,
            data?.TooHighBoundFee,
          )
        : 100;
    if (iconPos <= 0) iconPos = 10;
    else {
      if (iconPos >= 100) iconPos = 90;
      else {
        iconPos = (0.2 + (0.6 * iconPos) / 100) * 100;
      }
    }
    return iconPos;
  };

  // 支付标准位置
  const CwValuePos = (data) => {
    let iconPos =
      data?.TooLowBoundFee && data?.TooHighBoundFee
        ? calculatePercentagePosition(
            data?.StdFee,
            data?.TooLowBoundFee,
            data?.TooHighBoundFee,
          )
        : -1;
    if (iconPos <= 0) {
      if (props?.data?.StdFee > props?.data?.TotalFee) iconPos = 15;
      else iconPos = 5;
    } else {
      if (iconPos >= 100) {
        if (props?.data?.StdFee > props?.data?.TotalFee) iconPos = 95;
        else iconPos = 85;
      } else {
        iconPos = (0.2 + (0.6 * iconPos) / 100) * 100;
      }
    }
    return iconPos;
  };

  return (
    <>
      <div style={{ padding: '20px 0px 30px 0px' }}>
        <div>
          <div className="uni-progress-container">
            <div
              className="d-flex"
              style={{
                position: 'absolute',
                width: '100%',
                height: '32px',
                top: '-4px',
              }}
            >
              <div style={{ width: '20%', backgroundColor: '#fff5d2' }}></div>
              <div style={{ width: '60%', backgroundColor: '#eaf9ee' }}></div>
              <div style={{ width: '20%', backgroundColor: '#ffccc769' }}></div>
            </div>
            <Progress
              percent={TotalFeePos(props?.data)}
              strokeWidth={17}
              showInfo={false}
              trailColor="#d9d9d9"
              strokeColor="#18ba56"
            />
            <div className="first-label">
              低倍率：0~{parseFloat(props?.data?.TooLowBoundFee)?.toFixed(2)}
            </div>
            <div className="second-label" style={{ left: '20%' }}>
              正常倍率
            </div>
            <div className="third-label" style={{ left: '80%' }}>
              高倍率：{parseFloat(props?.data?.TooHighBoundFee)?.toFixed(2)}~
            </div>
            <div
              className="progress-circle"
              style={{
                left: `calc(${
                  _.isNumber(TotalFeePos(props?.data))
                    ? `${TotalFeePos(props?.data)}%`
                    : '50%'
                } - 10px )`, // 通过计算定位标记
              }}
            />

            {props?.data?.TooLowBoundFee && props?.data?.TooHighBoundFee && (
              <>
                <div
                  className="box-with-arrow-down label-progress-up"
                  style={{
                    left: `calc(${
                      _.isNumber(TotalFeePos(props?.data))
                        ? `${TotalFeePos(props?.data)}%`
                        : '50%'
                    } - 5px)`, // 通过计算定位标记
                  }}
                >
                  <Tooltip title="当前值" placement="top">
                    当前值 {parseFloat(props?.data?.TotalFee)?.toFixed(2)}
                  </Tooltip>
                </div>
                {/* 参数有歧义 先注释掉 */}
                {/* <div
                  className="box-with-arrow-up label-progress-down"
                  style={{
                    left: `${
                      CwValuePos(props?.data) > -1
                        ? `${CwValuePos(props?.data)}%`
                        : '37%'
                    }`,
                    whiteSpace: 'nowrap',
                  }}
                >
                  <Tooltip title="支付标准" placement="top">
                    <div style={{ position: 'absolute', right: 0 }}>
                      支付标准{' '}
                      {props?.data?.StdFee
                        ? parseFloat(props?.data?.StdFee)?.toFixed(2)
                        : props?.data?.StdFee ?? '-'}
                    </div>
                  </Tooltip>
                </div> */}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
export default UniProgress;
