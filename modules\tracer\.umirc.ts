import { name } from './package.json';
import {
  slaveCommonConfig,
  extraBabelIncludes,
  extraWebPackPlugin,
} from '../../.umirc.commom';

export default {
  base: name,
  publicPath: '/tracer/',
  outputPath: '../../dist/tracer',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  dva: {
    immer: true,
    hmr: true,
  },

  qiankun: { slave: {} },

  plugins: [
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/traceRecordList',
    },
    {
      path: '/traceRecordList',
      exact: true,
      component: '@/pages/traceRecord/index',
    },
    // 催缴查询
    {
      path: '/askForPaySearch',
      exact: true,
      component: '@/pages/askForPay/search/index',
    },
    {
      path: '/ward',
      routes: [
        {
          path: '/ward/search',
          exact: true,
          component: '@/pages/ward/search/index',
        },
        {
          path: '/ward/searchNotSignined',
          exact: true,
          component: '@/pages/ward/searchNotSignined/index',
        },
        {
          path: '/ward/signin',
          exact: true,
          component: '@/pages/ward/signin/index',
        },
      ],
    },
    {
      path: '/mrRoom',
      routes: [
        {
          path: '/mrRoom/search',
          exact: true,
          component: '@/pages/mrRoom/search/index',
        },
        {
          path: '/mrRoom/signin',
          exact: true,
          component: '@/pages/mrRoom/signin/index',
        },
        {
          path: '/mrRoom/statistic',
          routes: [
            {
              path: '/mrRoom/statistic/bySignDate',
              exact: true,
              component: '@/pages/mrRoom/statistic/bySignDate/index',
            },
            {
              path: '/mrRoom/statistic/byOutDate',
              exact: true,
              component: '@/pages/mrRoom/statistic/byOutDate/index',
            },
          ],
        },
      ],
    },
    {
      path: '/archive',
      routes: [
        {
          path: '/archive/search',
          exact: true,
          component: '@/pages/archive/search/index',
        },
        {
          path: '/archive/register',
          exact: true,
          component: '@/pages/archive/register/index',
        },
      ],
    },
    {
      path: '/seal',
      routes: [
        {
          path: '/seal/search',
          exact: true,
          component: '@/pages/seal/search/index',
        },
        {
          path: '/seal/register',
          exact: true,
          component: '@/pages/seal/register/index',
        },
      ],
    },
    {
      path: '/borrow',
      routes: [
        {
          path: '/borrow/search',
          exact: true,
          component: '@/pages/borrow/search/index',
        },
        {
          path: '/borrow/borrowRegister',
          exact: true,
          component: '@/pages/borrow/borrowRegister/index',
        },
        {
          path: '/borrow/returnRegister',
          exact: true,
          component: '@/pages/borrow/returnRegister/index',
        },
        // 催还查询
        {
          path: '/borrow/askForReturnSearch',
          exact: true,
          component: '@/pages/borrow/askForReturn/search/index',
        },
        {
          path: '/borrow/borrowTasks',
          exact: true,
          component: '@/pages/borrow/borrowTasks/index',
        },
        {
          path: '/borrow/userBorrowTasks',
          exact: true,
          component: '@/pages/borrow/userBorrowTasks/index',
        },
      ],
    },
    // 复印
    {
      path: '/print',
      routes: [
        {
          path: '/print/search',
          exact: true,
          component: '@/pages/print/search/index',
        },
        {
          path: '/print/register',
          exact: true,
          component: '@/pages/print/register/index',
        },
      ],
    },
    // 病案室出库
    {
      path: '/dmrSignOut',
      routes: [
        {
          path: '/dmrSignOut/search',
          exact: true,
          component: '@/pages/dmrSignOut/search/index',
        },
        {
          path: '/dmrSignOut/discharge',
          exact: true,
          component: '@/pages/dmrSignOut/discharge/index',
        },
      ],
    },
    // 病案示踪分析
    {
      path: '/mrRoomSignInAnalysis',
      exact: true,
      component: '@/pages/analysis/index',
    },
  ],

  proxy: {
    // 同cra的setupProxy,代理中转实现dev版本的跨域
    '/mr': {
      target: 'http://172.16.3.152:5181', // http://172.16.3.217:8001/swagger/index.html
      changeOrigin: true,
      pathRewrite: { '^/mr': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
};
