import React from 'react';
import { isEmptyValues } from '@uni/utils/src/utils';
import { Tooltip } from 'antd';
import Formula from '@uni/commons/src/icon/Formula';
import Average from '@uni/commons/src/icon/Average';

interface ComboDetailSummaryProps {
  tableContainerRef: any;
  tableColumns: any[];
  summaryData: any[];
}

const ComboDetailSummary = (props: ComboDetailSummaryProps) => {
  console.log('ComboDetailSummary', props);

  const summaryKeys = Object.keys(props?.summaryData?.at(0) ?? {});

  const formatNumber = (value) => {
    if (isEmptyValues(value)) {
      return '-';
    }

    return value % 1 === 0
      ? value.toFixed(0)
      : value.toFixed(2).replace(/\.?00$/, '');
  };

  const tableSize = props?.tableContainerRef?.current?.getTableDataSize();

  if (tableSize === 0) {
    return null;
  }

  if (tableSize !== 0 && isEmptyValues(props?.summaryData)) {
    return null;
  }

  return (
    <tfoot className={'combo-table-detail-summary-container'}>
      <tr key={'combo-detail-footer'}>
        <td>
          <div
            className={'summary-item'}
            style={{
              alignItems: 'center',
              height: '90%',
              justifyContent: 'space-between',
            }}
          >
            <Tooltip title={'合计'}>
              <Formula theme="outline" size="18" fill="#1464f8" />
            </Tooltip>
            <Tooltip title={'平均'}>
              <Average theme="outline" size="18" fill="#1464f8" />
            </Tooltip>
          </div>
        </td>
        {props?.tableColumns?.slice(1)?.map((item) => {
          if (
            summaryKeys?.includes(
              `Sum__${item?.column?.columnDef?.meta?.dataIndex}`,
            ) ||
            summaryKeys?.includes(
              `Avg__${item?.column?.columnDef?.meta?.dataIndex}`,
            )
          ) {
            return (
              <td>
                <div className={'summary-item'}>
                  <span>
                    {formatNumber(
                      props?.summaryData?.at(0)?.[
                        `Sum__${item?.column?.columnDef?.meta?.dataIndex}`
                      ],
                    )}
                  </span>
                  <span>
                    {props?.summaryData
                      ?.at(0)
                      [
                        `Avg__${item?.column?.columnDef?.meta?.dataIndex}`
                      ]?.toFixed(2) ?? '-'}
                  </span>
                </div>
              </td>
            );
          }

          return <td />;
        })}
      </tr>
    </tfoot>
  );
};

export default ComboDetailSummary;
