import { template } from 'lodash';
import { inferUnderlyingType, isArray } from './utilities.js';
import { getSubject } from './subjectDefinition.js';
import dayjs from 'dayjs';

const textTemplates = {
  Or: ' 或者 ',
  And: ' 并且 ',
  AndInSome: ' 且 ',
  LParenthesis: '（',
  RParenthesis: '）',

  Equal: '等于',
  NotEqual: '不等于',
  LessOrEqual: '小于等于',
  Less: '小于',
  GreaterOrEqual: '大于等于',
  Greater: '大于',
  Like: '包含关键字',
  NotLike: '不包含关键字',
  StartsWith: template('以${value}开头'),
  EndsWith: template('以${value}结尾'),
  Between: template('${subject}在${value0}和${value1}之间'),
  NotBetween: template('${subject}不在${value0}和${value1}之间'),
  IsNull: '为空',
  IsNotNull: '不为空',
  SelectAnyIn: '包含',
  NotSelectAnyIn: '不包含',

  True: '是',
  False: '否',
  EqualBoolean: '：',

  Not: '不满足',
  Some: '满足',
  NotSome: '不存在',

  newlineAfterAnd: '\n',
  newlineAfterOr: '\n',
  newlineAfterParenthesis: '',
};

function getTextTemplate(key) {
  return textTemplates[key];
}

function setTextTemplate(key, value) {
  textTemplates[key] = value;
}

function fromValue(value) {
  const type = inferUnderlyingType(value);

  if (isArray(value) && type !== 'string') {
    value = value.map((v) => fromValue(v));
  }

  if (type === 'date') {
    value = dayjs(value).format('YYYY年MM月DD日');
  } else if (type === 'boolean') {
    value = value ? textTemplates.True : textTemplates.False;
  }

  return JSON.stringify(value);
}

const fromSubject = (subjectLiteral) => {
  const subject = getSubject(subjectLiteral);
  var text = subject ? subject.title2 || subject.title : subjectLiteral;
  return text;
};

const getNegativeOperator = (operator) => {
  if (operator === 'Like') return 'NotLike';
  if (operator === 'SelectAnyIn') return 'NotSelectAnyIn';
  return null;
};

const fromAtomRule = (subjectLiteral, operator, value, params) => {
  const subject = fromSubject(subjectLiteral);

  if (params.insideNot && !params.notResolved) {
    // Push down not only when in atom rule/some rule with one child
    if (!params.insideSome || !params.insideSomeMultipleChildren) {
      // Ensure the operator can be negated
      const negativeOperator = getNegativeOperator(operator);
      if (negativeOperator) {
        operator = negativeOperator;
        params.notResolved = true;
      }
    }
  }

  const operatorTmpl = textTemplates[operator];
  const type = inferUnderlyingType(value);

  if (operator === 'Between' || operator === 'NotBetween') {
    const value0 = fromValue(value[0]);
    const value1 = fromValue(value[1]);
    const text = operatorTmpl({ subject, value0, value1 });
    return text;
  } else if (operator === 'StartsWith' || operator === 'EndsWith') {
    const valueText = fromValue(value);
    const text = operatorTmpl({ valueText });
    return text;
  } else if (type === 'boolean') {
    const valueText = fromValue(value);
    const text = `${subject}${textTemplates['EqualBoolean']}${valueText}`;
    return text;
  } else {
    const valueText = fromValue(value);
    const text = `${subject}${operatorTmpl}${valueText}`;
    return text;
  }
};

const fromSomeRule = (fieldText, childrenTexts, params) => {
  const someTmpl = textTemplates.Some;
  if (childrenTexts.length === 1) {
    const text = childrenTexts[0];
    return text;
  } else {
    const text = `${params.insideNot ? '' : someTmpl}${
      textTemplates.LParenthesis
    }${childrenTexts.join(textTemplates.AndInSome)}${
      textTemplates.RParenthesis
    }`;
    return text;
  }
};

const fromNotAtomRule = (childText, params) => {
  const notTmpl = params.notResolved ? '' : textTemplates.Not;
  const text = `${notTmpl}${childText}`;
  return text;
};

const fromNotSomeRule = (childText, params) => {
  const notTmpl = params.notResolved ? '' : textTemplates.NotSome;
  const text = `${notTmpl}${childText}`;
  return text;
};

const fromParenthesisRule = (childText) => {
  const text = `${textTemplates.LParenthesis}${childText}${textTemplates.RParenthesis}${textTemplates.newlineAfterParenthesis}`;
  return text;
};

const fromAndRule = (childrenTexts) => {
  const text = childrenTexts.join(
    textTemplates.And + textTemplates.newlineAfterAnd,
  );
  return text;
};

const fromOrRule = (childrenTexts) => {
  const text = childrenTexts.join(
    textTemplates.Or + textTemplates.newlineAfterOr,
  );
  return text;
};

export {
  getTextTemplate,
  setTextTemplate,
  fromOrRule,
  fromAndRule,
  fromParenthesisRule,
  fromSubject,
  fromAtomRule,
  fromSomeRule,
  fromNotAtomRule,
  fromNotSomeRule,
};
