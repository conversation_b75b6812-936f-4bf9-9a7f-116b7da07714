import { useEffect, useState, useCallback } from 'react';
import { Form } from 'antd';
import { cloneDeep } from 'lodash';
import { triggerFormValueChangeEvent } from '@uni/grid/src/utils';

// 医保相关字段（只有这两个字段放到医保表格）
const INSUR_FIELDS = ['InsurCode', 'InsurName'];

// 主表格字段（除了医保编码和名称外的所有字段）
const MAIN_FIELDS_EXCLUDE = ['InsurCode', 'InsurName'];

interface UseInsurSeparateTableLogicProps {
  form: any;
}

export const useInsurSeparateTableLogic = (form: any) => {
  const [mainTableData, setMainTableData] = useState<any[]>([]);
  const [insurTableData, setInsurTableData] = useState<any[]>([]);
  
  // 监听原始数据变化
  const originalData = Form.useWatch('diagnosis-table', form) ?? [];
  const diagnosisTableData = Form.useWatch('diagnosisTable', form) ?? [];

  // 获取权限和联动配置
  const getUserRole = useCallback(() => {
    // TODO: 从全局状态或props中获取用户角色
    // 可以通过 useModel('@@qiankunStateFromMaster') 获取全局状态
    // 临时返回 'employee'，后续需要根据实际情况获取
    return 'employee';
  }, []);

  const getInsurSeparateTableLinkage = useCallback(() => {
    // TODO: 从配置中获取联动标志
    // 可以通过用户权限或其他业务逻辑来判断
    // 临时返回 true，后续需要根据实际情况获取
    return true;
  }, []);

  // 分离数据：将完整数据分离为主表格和医保表格数据
  const separateData = useCallback((data: any[]) => {
    if (!data || !Array.isArray(data)) {
      return { mainData: [], insurData: [] };
    }

    const mainData = data.map((item, index) => {
      const mainItem = { ...item };
      // 从主表格数据中移除医保字段
      INSUR_FIELDS.forEach(field => {
        delete mainItem[field];
      });
      return mainItem;
    });

    const insurData = data.map((item, index) => {
      const insurItem: any = {
        id: item.id,
        // 保留关联字段用于数据同步
        IcdeCode: item.IcdeCode,
        IcdeName: item.IcdeName,
        // 只保留医保相关字段
        InsurCode: item.InsurCode,
        InsurName: item.InsurName,
      };
      return insurItem;
    });

    return { mainData, insurData };
  }, []);

  // 合并数据：将主表格和医保表格数据合并为完整数据
  const mergeData = useCallback((mainData: any[], insurData: any[]) => {
    if (!mainData || !insurData) {
      return [];
    }

    return mainData.map((mainItem, index) => {
      const insurItem = insurData[index] || {};
      return {
        ...mainItem,
        // 合并医保字段
        InsurCode: insurItem.InsurCode,
        InsurName: insurItem.InsurName,
      };
    });
  }, []);

  // 同步到原始表格数据
  const syncToOriginalTable = useCallback((newMainData?: any[], newInsurData?: any[]) => {
    const currentMainData = newMainData || mainTableData;
    const currentInsurData = newInsurData || insurTableData;
    
    const mergedData = mergeData(currentMainData, currentInsurData);
    
    // 更新原始数据字段
    form.setFieldValue('diagnosis-table', mergedData);
    form.setFieldValue('diagnosisTable', cloneDeep(mergedData));
    
    // 触发表单变化事件
    triggerFormValueChangeEvent('diagnosis-table');
  }, [form, mainTableData, insurTableData, mergeData]);

  // 处理主表格数据变化
  const handleMainTableChange = useCallback((newData: any[]) => {
    setMainTableData(newData);

    const userRole = getUserRole();
    const linkageEnabled = getInsurSeparateTableLinkage();

    // 只有employee用户且联动开启时才进行联动
    if (userRole === 'employee' && linkageEnabled) {
      // 检查是否有IcdeCode变化，如果有则需要联动更新医保数据
      const updatedInsurData = newData.map((mainItem, index) => {
        const currentInsurItem = insurTableData[index] || {};
        const previousMainItem = mainTableData[index] || {};

        // 如果IcdeCode发生变化，需要联动更新医保信息
        if (mainItem.IcdeCode !== previousMainItem.IcdeCode) {
          // TODO: 这里应该调用获取医保信息的接口
          // 类似原有逻辑中的医保编码获取
          // 暂时保持原有的InsurCode和InsurName
          return {
            ...currentInsurItem,
            id: mainItem.id,
            IcdeCode: mainItem.IcdeCode,
            IcdeName: mainItem.IcdeName,
            // InsurCode和InsurName应该通过接口获取
            // 这里可以调用类似 getIcdeInfoWithStrictMode 的接口
          };
        }

        // 如果IcdeCode没有变化，只更新基本信息
        return {
          ...currentInsurItem,
          id: mainItem.id,
          IcdeCode: mainItem.IcdeCode,
          IcdeName: mainItem.IcdeName,
        };
      });

      setInsurTableData(updatedInsurData);
      syncToOriginalTable(newData, updatedInsurData);
    } else {
      // 无联动时只更新主表格
      syncToOriginalTable(newData, insurTableData);
    }
  }, [mainTableData, insurTableData, getUserRole, getInsurSeparateTableLinkage, syncToOriginalTable]);

  // 处理医保表格数据变化
  const handleInsurTableChange = useCallback((newData: any[]) => {
    setInsurTableData(newData);
    
    const userRole = getUserRole();
    
    // insur用户的修改不会联动到主表格
    if (userRole === 'insur') {
      // 只更新医保表格数据，不影响主表格
      syncToOriginalTable(mainTableData, newData);
    }
  }, [mainTableData, getUserRole, syncToOriginalTable]);

  // 监听原始数据变化，分离数据
  useEffect(() => {
    if (originalData && originalData.length >= 0) {
      const { mainData, insurData } = separateData(originalData);
      setMainTableData(mainData);
      setInsurTableData(insurData);
    }
  }, [originalData, separateData]);

  return {
    mainTableData,
    insurTableData,
    handleMainTableChange,
    handleInsurTableChange,
    syncToOriginalTable,
    getUserRole,
    getInsurSeparateTableLinkage,
  };
};
