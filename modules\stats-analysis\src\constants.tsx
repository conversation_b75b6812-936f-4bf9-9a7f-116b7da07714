import dayjs from 'dayjs';

export const StatsAnalysisEventConstant = {
  STATS_ANALYSIS_RESET: 'STATS_ANALYSIS_RESET',

  STATS_ANALYSIS_REPORT_ITEM_QUERY: 'STATS_ANALYSIS_REPORT_ITEM_QUERY',
  STATS_ANALYSIS_REPORT_ITEM_QUERY_SUBMIT:
    'STATS_ANALYSIS_REPORT_ITEM_QUERY_SUBMIT',
  STATS_ANALYSIS_REPORT_ITEM_CLICK: 'STATS_ANALYSIS_REPORT_ITEM_CLICK',
  STATS_ANALYSIS_REPORT_ARCHIVE: 'STATS_ANALYSIS_REPORT_ARCHIVE',
  STATS_ANALYSIS_REPORT_DOWNLOAD: 'STATS_ANALYSIS_REPORT_DOWNLOAD',

  // 组合查询
  STATS_ANALYSIS_COMBINE_QUERY: 'STATS_ANALYSIS_COMBINE_QUERY',
  STATS_ANALYSIS_COMBINE_QUERY_METRICS: 'STATS_ANALYSIS_COMBINE_QUERY_METRICS',
  STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT:
    'STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT',

  STATS_ANALYSIS_COMBINE_QUERY_METRICS_STATE:
    'STATS_ANALYSIS_COMBINE_QUERY_METRICS_STATE',
  STATS_ANALYSIS_COMBINE_QUERY_COLUMNS_STATE:
    'STATS_ANALYSIS_COMBINE_QUERY_COLUMNS_STATE',

  STATS_ANALYSIS_COMBINE_QUERY_DETAIL: 'STATS_ANALYSIS_COMBINE_QUERY_DETAIL',
  STATS_ANALYSIS_COMBINE_QUERY_TITLE: 'STATS_ANALYSIS_COMBINE_QUERY_TITLE',
  STATS_ANALYSIS_STATS_COMBINE_QUERY_TITLE:
    'STATS_ANALYSIS_STATS_COMBINE_QUERY_TITLE',

  STATS_ANALYSIS_AGGREGATE_ITEM_EDIT: 'STATS_ANALYSIS_AGGREGATE_ITEM_EDIT',
  STATS_ANALYSIS_GROUP_ITEM_EDIT: 'STATS_ANALYSIS_GROUP_ITEM_EDIT',
  STATS_ANALYSIS_METRIC_ITEM_EDIT: 'STATS_ANALYSIS_METRIC_ITEM_EDIT',

  STATS_ANALYSIS_TEMPLATE_CLICK: 'STATS_ANALYSIS_TEMPLATE_CLICK',

  DETAIL_COLUMN_TEMPLATE_CLICK: 'DETAIL_COLUMN_TEMPLATE_CLICK',
  METRIC_COLUMN_TEMPLATE_CLICK: 'METRIC_COLUMN_TEMPLATE_CLICK',

  STATS_ANALYSIS_TEMPLATE_SAVE_SUCCESS: 'STATS_ANALYSIS_TEMPLATE_SAVE_SUCCESS',
  DETAIL_COLUMN_TEMPLATE_SAVE_SUCCESS: 'DETAIL_COLUMN_TEMPLATE_SAVE_SUCCESS',
  METRIC_COLUMN_TEMPLATE_SAVE_SUCCESS: 'METRIC_COLUMN_TEMPLATE_SAVE_SUCCESS',

  STATS_ANALYSIS_TEMPLATE_QUERY_SUCCESS:
    'STATS_ANALYSIS_TEMPLATE_QUERY_SUCCESS',

  DETAIL_COLUMN_TEMPLATE_EXPAND: 'DETAIL_COLUMN_TEMPLATE_EXPAND',
  METRIC_COLUMN_TEMPLATE_EXPAND: 'METRIC_COLUMN_TEMPLATE_EXPAND',
  STATS_ANALYSIS_TEMPLATE_EXPAND: 'STATS_ANALYSIS_TEMPLATE_EXPAND',

  DETAIL_COLUMN_TEMPLATE_OPEN: 'DETAIL_COLUMN_TEMPLATE_OPEN',
  METRIC_COLUMN_TEMPLATE_OPEN: 'METRIC_COLUMN_TEMPLATE_OPEN',

  STATS_ANALYSIS_EXCLUDE_CLICK: 'STATS_ANALYSIS_EXCLUDE_CLICK',

  STATS_ANALYSIS_VIRTUAL_ITEM: 'STATS_ANALYSIS_VIRTUAL_ITEM',

  STATS_ANALYSIS_TITLE_ADD: 'STATS_ANALYSIS_TITLE_ADD',
  STATS_ANALYSIS_TITLE_EDIT: 'STATS_ANALYSIS_TITLE_EDIT',
  STATS_ANALYSIS_TITLE_UPDATE: 'STATS_ANALYSIS_TITLE_UPDATE',
  STATS_ANALYSIS_TITLE_LIST_UPDATE: 'STATS_ANALYSIS_TITLE_LIST_UPDATE',

  STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_ADD:
    'STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_ADD',
  // 另存为列模板 明细
  STATS_ANALYSIS_DETAIL_TEMPLATE_SAVE_AS:
    'STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_SAVE_AS',

  STATS_ANALYSIS_METRIC_TEMPLATE_TITLE_ADD:
    'STATS_ANALYSIS_METRIC_TEMPLATE_TITLE_ADD',
  // 另存为列模板 统计
  STATS_ANALYSIS_METRICL_TEMPLATE_SAVE_AS:
    'STATS_ANALYSIS_METRIC_TEMPLATE_TITLE_SAVE_AS',

  DETAIL_COLUMN_TEMPLATE_DELETE_SUCCESS:
    'DETAIL_COLUMN_TEMPLATE_DELETE_SUCCESS',
  METRIC_COLUMN_TEMPLATE_DELETE_SUCCESS:
    'METRIC_COLUMN_TEMPLATE_DELETE_SUCCESS',
  STATS_ANALYSIS_TEMPLATE_DELETE_SUCCESS:
    'STATS_ANALYSIS_TEMPLATE_DELETE_SUCCESS',

  STATS_ANALYSIS_COLUMN_SELECT: 'STATS_ANALYSIS_COLUMN_SELECT',
  STATS_ANALYSIS_COLUMN_DESELECT: 'STATS_ANALYSIS_COLUMN_DESELECT',
  STATS_ANALYSIS_COLUMN_SELECT_RESET: 'STATS_ANALYSIS_COLUMN_SELECT_RESET',

  STATS_ANALYSIS_COLUMN_SELECT_DELETE: 'STATS_ANALYSIS_COLUMN_SELECT_DELETE',
  STATS_ANALYSIS_COLUMN_SELECT_TOP: 'STATS_ANALYSIS_COLUMN_SELECT_TOP',
  STATS_ANALYSIS_COLUMN_SELECT_BOTTOM: 'STATS_ANALYSIS_COLUMN_SELECT_BOTTOM',

  STATS_ANALYSIS_METRIC_COLUMN_SELECT: 'STATS_ANALYSIS_METRIC_COLUMN_SELECT',
  STATS_ANALYSIS_METRIC_COLUMN_SELECT_RESET:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_RESET',
  STATS_ANALYSIS_METRIC_COLUMN_SELECT_NEED_RESET:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_NEED_RESET',

  STATS_ANALYSIS_METRIC_COLUMN_ITEM_SELECT:
    'STATS_ANALYSIS_METRIC_COLUMN_ITEM_SELECT',

  STATS_ANALYSIS_METRIC_COLUMN_SELECT_COMPLETE:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_COMPLETE',

  STATS_ANALYSIS_METRIC_COLUMN_SELECT_DELETE:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_DELETE',
  STATS_ANALYSIS_METRIC_COLUMN_SELECT_TOP:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_TOP',
  STATS_ANALYSIS_METRIC_COLUMN_SELECT_BOTTOM:
    'STATS_ANALYSIS_METRIC_COLUMN_SELECT_BOTTOM',

  // 病种分析
  STATS_ANALYSIS_DISEASE_DATA_SORT_CHANGE:
    'STATS_ANALYSIS_DISEASE_DATA_SORT_CHANGE',
  STATS_ANALYSIS_DISEASE_DATA_OPEN: 'STATS_ANALYSIS_DISEASE_DATA_OPEN',
  STATS_ANALYSIS_DISEASE_DATA_ADD: 'STATS_ANALYSIS_DISEASE_DATA_ADD',
  STATS_ANALYSIS_DISEASE_DATA_DELETE: 'STATS_ANALYSIS_DISEASE_DATA_DELETE',
  STATS_ANALYSIS_DISEASE_DATA_RELOAD: 'STATS_ANALYSIS_DISEASE_DATA_RELOAD',
  STATS_ANALYSIS_DISEASE_GET_EXPR: 'STATS_ANALYSIS_DISEASE_GET_EXPR',

  // 条件删除
  STATS_ANALYSIS_TABLE_CONDITION_DELETE:
    'STATS_ANALYSIS_TABLE_CONDITION_DELETE',
  STATS_ANALYSIS_TABLE_CONDITION_CLEAR: 'STATS_ANALYSIS_TABLE_CONDITION_CLEAR',
  // 条件新增
  STATS_ANALYSIS_TABLE_CONDITION_ADD: 'STATS_ANALYSIS_TABLE_CONDITION_ADD',

  // 条件组内 item新增
  STATS_ANALYSIS_TABLE_CONDITION_ITEM_ADD:
    'STATS_ANALYSIS_TABLE_CONDITION_ITEM_ADD',
  // 条件组内 item删除
  STATS_ANALYSIS_TABLE_CONDITION_ITEM_DELETE:
    'STATS_ANALYSIS_TABLE_CONDITION_ITEM_DELETE',

  STATS_ANALYSIS_COMBINE_QUERY_TABLE_TO_TREE:
    'STATS_ANALYSIS_COMBINE_QUERY_TABLE_TO_TREE',

  STATS_ANALYSIS_COMBINE_QUERY_TABLE_ITEM_ADD:
    'STATS_ANALYSIS_COMBINE_QUERY_TABLE_ITEM_ADD',

  STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE:
    'STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE',
  STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE_SELECT:
    'STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE_SELECT',

  STATS_ANALYSIS_COMBINE_QUERY_TABLE_RESET:
    'STATS_ANALYSIS_COMBINE_QUERY_TABLE_RESET',

  STATS_ANALYSIS_COMBINE_QUERY_TABLE_CONDITION_ADD:
    'STATS_ANALYSIS_COMBINE_QUERY_TABLE_CONDITION_ADD',

  STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE:
    'STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE',

  STATS_ANALYSIS_COMBINE_QUERY_COLUMN_CLEAR:
    'STATS_ANALYSIS_COMBINE_QUERY_COLUMN_CLEAR',

  // NG event
  STATS_ANALYSIS_DETAIL_TAB_COUNT: 'STATS_ANALYSIS_DETAIL_TAB_COUNT',

  STATS_ANALYSIS_METRIC_TAB_COUNT: 'STATS_ANALYSIS_METRIC_TAB_COUNT',

  STATS_ANALYSIS_DETAIL_COLUMN_CUSTOMIZER_CHANGE:
    'STATS_ANALYSIS_DETAIL_COLUMN_CUSTOMIZER_CHANGE',

  STATS_ANALYSIS_METRIC_COLUMN_CUSTOMIZER_CHANGE:
    'STATS_ANALYSIS_METRIC_COLUMN_CUSTOMIZER_CHANGE',

  STATS_ANALYSIS_TAB_SWITCH_METRIC_QUERY:
    'STATS_ANALYSIS_TAB_SWITCH_METRIC_QUERY',
  STATS_ANALYSIS_TAB_SWITCH_DETAIL_QUERY:
    'STATS_ANALYSIS_TAB_SWITCH_DETAIL_QUERY',
};

export const PeriodicPickerModes = {
  ByDay: {
    name: '按日',
    value: 'date',
  },
  ByMonth: {
    name: '按月',
    value: 'month',
  },
  ByQuarter: {
    name: '按季度',
    value: 'quarter',
  },
  ByYear: {
    name: '按年',
    value: 'year',
  },
};

export const PickerModes = {
  Date: {
    value: 'date',
    dateProcessor: (dates) => {
      return dates?.map((item) => {
        return dayjs(item).format('YYYY-MM-DD');
      });
    },
  },
  Month: {
    value: 'month',
    dateProcessor: (dates) => {
      return [
        dayjs(dates[0]).startOf('month').format('YYYY-MM-DD'),
        dayjs(dates[1]).endOf('month').format('YYYY-MM-DD'),
      ];
    },
  },
  Quarter: {
    value: 'quarter',
    dateProcessor: (dates) => {
      return [
        dayjs(dates[0]).startOf('quarter').format('YYYY-MM-DD'),
        dayjs(dates[1]).endOf('quarter').format('YYYY-MM-DD'),
      ];
    },
  },
  Year: {
    value: 'year',
    dateProcessor: (dates) => {
      return [
        dayjs(dates[0]).startOf('year').format('YYYY-MM-DD'),
        dayjs(dates[1]).endOf('year').format('YYYY-MM-DD'),
      ];
    },
  },
  DateTime: {
    value: 'date',
    dateProcessor: (dates) => {
      return dates?.map((item) => {
        return dayjs(item).format('YYYY-MM-DD HH:mm:ss');
      });
    },
  },
};

export const SurgeryTags = [
  // { Name: '章节', Code: 'Initial' },
  { Name: '类目', Code: 'Category' },
  { Name: '亚目', Code: 'SubCategory' },
  { Name: '细目', Code: 'DetailCategory' },
  { Name: '完整编码', Code: 'Default' },
];

export const DiseaseTags = [
  { Name: '章节', Code: 'Initial' },
  { Name: '类目', Code: 'Category' },
  { Name: '亚目', Code: 'SubCategory' },
  { Name: '完整编码', Code: 'Default' },
  // { Name: '细目', Code: 'DetailCategory' },
];
