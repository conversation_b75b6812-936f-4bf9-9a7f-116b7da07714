import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { UniSelect } from '@uni/components/src';
import UniEcharts from '@uni/components/src/echarts';
import { Col, Row, Tabs } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import '../index.less';
import Stats from '@/components/stats/index';
import { DifficultCaseNormalStat } from '@/constants';
import BmTable from '@/components/BmTable/index';
import { RespVO } from '@uni/commons/src/interfaces';
import SingleColumnTable from '@/components/SingleColumnTable/index';
import { BmChartSelectOptions } from '@/components/BmTable/constants';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import CardWithBtns from '@uni/components/src/cardWithBtns/index';
import { CmiLineBar } from '@/echarts/cmi.chart.opts';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import DrawerCardInfo from '@/components/drawerCardInfo/index';

const rwRangesData = [
  {
    Code: '0',
    Name: '0<=Rw<1',
  },
  {
    Code: '1',
    Name: '1<=Rw<2',
  },
  {
    Code: '2',
    Name: '2<=Rw<5',
  },
  {
    Code: '3',
    Name: '5<=Rw<10',
  },
  {
    Code: '4',
    Name: '10<=Rw',
  },
];

const DifficultCase = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // 这边调bundleData
  const {
    data: BundledData,
    loading: getBundledDataLoading,
    run: getBundledDataReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/v2/Drgs/HospHighRw/BundledHighRw', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 疑难病例特殊
  const [RwRangesSelectedValue, setRwRangesSelectedValue] = useState([
    '0',
    '1',
    '2',
    '3',
    '4',
  ]);

  const {
    data: RwDistribution,
    loading: getRwDistributionLoading,
    run: getRwDistributionDataReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/v2/Drgs/HospHighRw/HighRwDistribution', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );
  // columns
  const {
    data: RwDistributionColumnsData,
    loading: getRwDistributionColumnsLoading,
    mutate: mutateRwDistributionColumns,
    run: getRwDistributionColumnsReq,
  } = useRequest(
    () =>
      uniCommonService('Api/v2/Drgs/HospHighRw/HighRwDistribution', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      // manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
    };
    setTableParams(params);
    getBundledDataReq(params);
  }, [dateRange, hospCodes]);

  useEffect(() => {
    if (tableParams) {
      getRwDistributionDataReq({
        ...tableParams,
        RwRangeCode: RwRangesSelectedValue,
      });
    }
  }, [tableParams, RwRangesSelectedValue]);

  let tabItems = [
    {
      key: 'statistic',
      label: '全院综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Drgs/HospHighRw/BundledHighRw`}
              trendApi={`Api/v2/Drgs/HospHighRw/HighRwTrend`}
              columns={DifficultCaseNormalStat}
              defaultSelectItem={'PatCnt'}
              type="col-xl-6"
              chartHeight={310}
              tabKey={activeKey}
              useGlobalState
              tableParams={tableParams}
            />
          </Col>
          <SingleColumnTable
            title="全院疑难病例"
            args={{
              api: `Api/v2/Drgs/HospHighRw/HighRwByHosp`,
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="HospName"
            type="table"
            colSpan={{ span: 24 }}
            visibleValueKeys={['HospName', 'PatCnt']}
            chart={{
              api: `Api/v2/Drgs/HospHighRw/HighRwADrgComposition`,
              title: '疑难病例病种结构分布',
              type: 'bar',
              valueKeys: ['PatCnt'],
              category: 'ADrgName',
              yAxis: '出院人次',
              colSpan: { span: 12 },
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.HospName,
                args: {
                  Sdate: dateRange?.at(0),
                  Edate: dateRange?.at(1),
                  HospCode: [record?.HospCode],
                  HighRw: true,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                dictData: dictData, // 传入
              });
            }}
          />
          <Col span={12}>
            <CardWithBtns
              title="各RW区间分布"
              content={
                <UniEcharts
                  height={250}
                  elementId="LineBar_RwWeights"
                  loading={getRwDistributionLoading}
                  options={
                    CmiLineBar(
                      RwDistribution?.map((d) => ({
                        ...d,
                      })),
                      ['RwRangeName'],
                    ) || {}
                  }
                />
              }
              extra={
                <UniSelect
                  style={{ minWidth: '100px' }}
                  disabled={getRwDistributionLoading}
                  mode={'multiple'}
                  allowClear={false}
                  placeholder={'请选择'}
                  optionValueKey={'Code'}
                  optionNameKey={'Name'}
                  defaultValue={RwRangesSelectedValue}
                  maxTagCount={2}
                  dataSource={dictData?.['RwRange'] || []}
                  removeIcon={false}
                  onBlur={(res) => {
                    getRwDistributionDataReq({
                      ...tableParams,
                      RwRangeCode: RwRangesSelectedValue,
                    });
                  }}
                  onChange={(value) => setRwRangesSelectedValue(value)}
                />
              }
              needExport={true}
              exportTitle={'RW权重变化趋势'}
              exportData={RwDistribution}
              exportColumns={RwDistributionColumnsData}
              needModalDetails={true}
              onRefresh={() => {
                getRwDistributionDataReq({
                  ...tableParams,
                  RwRangeCode: RwRangesSelectedValue,
                });
              }}
              // columnsEditableUrl={rwDistributionTrendApi}
              // onColumnChange={(newColumns) => {
              //   mutateColumns(tableColumnBaseProcessor([], newColumns));
              // }}
            />
          </Col>
        </Row>
      ),
    },
    {
      key: 'hosp_deptAnalysis',
      label: '科室综合分析',
      children: (
        <Row gutter={[16, 16]}>
          {/* <SingleColumnTable
            title="绩效科室疑难病例分布"
            args={{
              api: undefined,
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="HospName"
            visibleValueKeys={'PatCnt'}
            // chart={{
            //   api: undefined,
            //   type: 'bar',
            //   valueKeys: ['PatCnt'],
            //   category: 'ADrgName',
            //   yAxis: 'TotalCnt',
            // }}
          /> */}
          <SingleColumnTable
            title="临床科室疑难病例分布"
            args={{
              api: 'Api/v2/Drgs/CliDeptHighRw/HighRwByCliDept',
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="CliDeptName"
            type="table"
            visibleValueKeys={[
              'CliDeptName',
              // 'TotalCnt',
              'PatCnt',
              'PatRatio',
              'DrgCnt',
              'AvgInPeriod',
              'AvgTotalFee',
              'AvgMedicineFee',
              'MedicineFeeRatio',
              'AvgMaterialFee',
              'MaterialFeeRatio',
            ]}
            colSpan={{ span: 24 }}
            // chart={{
            //   api: 'Api/Drgs/CliDeptHighRw/HighRwADrgComposition',
            //   type: 'bar',
            //   valueKeys: ['PatCnt'],
            //   category: 'ADrgName',
            //   yAxis: 'TotalCnt',
            // }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.CliDeptName,
                args: {
                  ...tableParams,
                  CliDepts: [record?.CliDept],
                  HighRw: true,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                dictData: dictData, // 传入
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_medTeamAnalysis',
      label: '医疗组综合分析',
      children: (
        <SingleColumnTable
          title="医疗组疑难病例分布"
          args={{
            api: 'Api/v2/Drgs/MedTeamHighRw/HighRwByMedTeam',
          }}
          tableParams={tableParams}
          dictData={dictData}
          category="MedTeamName"
          type="table"
          visibleValueKeys={[
            'MedTeamName',
            // 'TotalCnt',
            'PatCnt',
            'PatRatio',
            'DrgCnt',
            'AvgInPeriod',
            'AvgTotalFee',
            'AvgMedicineFee',
            'MedicineFeeRatio',
            'AvgMaterialFee',
            'MaterialFeeRatio',
          ]}
          colSpan={{ span: 24 }}
          //   chart={{
          //     api: 'Api/Drgs/MedTeamHighRw/HighRwADrgComposition',
          //     type: 'bar',
          //     valueKeys: ['PatCnt'],
          //     category: 'ADrgName',
          //     yAxis: 'TotalCnt',
          //   }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.MedTeamName,
              args: {
                ...tableParams,
                MedTeams: [record?.MedTeam],
                HighRw: true,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <SingleColumnTable
          title="医生疑难病例分布"
          args={{
            api: 'Api/v2/Drgs/DoctorHighRw/HighRwByDoctor',
          }}
          tableParams={tableParams}
          dictData={dictData}
          category="DoctorName"
          type="table"
          visibleValueKeys={[
            'DoctorName',
            // 'TotalCnt',
            'PatCnt',
            'PatRatio',
            'DrgCnt',
            'AvgInPeriod',
            'AvgTotalFee',
            'AvgMedicineFee',
            'MedicineFeeRatio',
            'AvgMaterialFee',
            'MaterialFeeRatio',
          ]}
          colSpan={{ span: 24 }}
          select={{
            dataKey: 'DoctorType',
            valueKey: 'DoctorType',
            allowClear: false,
            defaultSelect: true,
          }}
          //   chart={{
          //     api: 'Api/Drgs/MedTeamHighRw/HighRwADrgComposition',
          //     type: 'bar',
          //     valueKeys: ['PatCnt'],
          //     category: 'ADrgName',
          //     yAxis: 'TotalCnt',
          //   }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.DoctorName,
              args: {
                ...tableParams,
                DoctorCodes: [record?.DoctorCode],
                DoctorType: (record?.DoctorType as string)?.toLocaleUpperCase(),
                HighRw: true,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_BmAnalysis',
      label: '标杆值',
      children: (
        <BmTable
          tableParams={tableParams}
          api="Api/v2/Drgs/HospHighRw/HighRwHospBm"
          bundleData={BundledData}
          BmChartSelectOptions={BmChartSelectOptions}
        />
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default DifficultCase;
