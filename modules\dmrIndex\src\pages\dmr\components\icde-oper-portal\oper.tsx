import './index.less';
import React, { useEffect, useState } from 'react';
import {
  DndContext,
  PointerSensor,
  useDraggable,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { Coordinates, CSS } from '@dnd-kit/utilities';
import { CloseOutlined, HolderOutlined } from '@ant-design/icons';
import { UniTable } from '@uni/components/src';
import { operPortalColumns } from '@/pages/dmr/components/icde-oper-portal/columns';
import { IcdeOperPortalProps } from '@/pages/dmr/components/icde-oper-portal/icde';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { isEmptyValues } from '@uni/utils/src/utils';

export const defaultCoordinates = {
  x: window.innerWidth - 900,
  y: 0,
};

const OperDataPortal = (props: IcdeOperPortalProps) => {
  const [coordinates, setCoordinates] =
    useState<Coordinates>(defaultCoordinates);

  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useDraggable({
      id: 'emr-oper-container',
    });
  const style = {
    transform: CSS.Translate.toString(transform),
  };

  const [operPortalShow, setOperPortalShow] = useState<boolean>(false);
  const [dmrOperDataShow, setDmrOperDataShow] = useState<boolean>(false);

  const [emrOperationData, setEmrOperationData] = useState<any[]>([]);
  const [dmrOperData, setDmrOperData] = useState<any[]>([]);

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      showStatus: (data: any) => {
        setOperPortalShow(data?.status);
        setDmrOperDataShow(data?.dmrDataShow);
        if (data?.status === true && data?.dmrDataShow === false) {
          emrOperReq();
        }

        if (data?.status === true && data?.dmrDataShow === true) {
          let formValues = props?.form?.getFieldsValue();
          dmrOperDataSetter(formValues);
        }
      },
      updateCoordinates: (data: any) => {
        let currentCoordinates: any = {};
        currentCoordinates['x'] =
          (coordinates['x'] ?? defaultCoordinates['x']) + data['x'];
        currentCoordinates['y'] =
          (coordinates['y'] ?? defaultCoordinates['y']) + data['y'];
        setCoordinates(currentCoordinates);
      },
      updateDmrDataValue: (formValues: any) => {
        if (dmrOperDataShow === true && operPortalShow === true) {
          dmrOperDataSetter(formValues);
        }
      },
    };
  });

  const dmrOperDataSetter = (formValues: any) => {
    let dmrOperData = [];
    formValues?.['operation-table']?.forEach((item, index) => {
      if (!isEmptyValues(item?.OperCode)) {
        dmrOperData.push({
          ...item,
          Sort: index + 1,
          rowId: uuidv4(),
          OprnOprtBegntime: dayjs(item?.OprnOprtBegntime)?.format('YYYY-MM-DD'),
        });
      }
    });
    setDmrOperData(dmrOperData);
  };

  const { loading: emrOperLoading, run: emrOperReq } = useRequest(
    () => {
      return uniCommonService(`Api/Dmr/DmrCardBundle/GetEmrIcdeAndOper`, {
        method: 'POST',
        params: {
          hisId: props?.hisId,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          setEmrOperationData(
            response?.data?.Opers?.map((item, index) => {
              return {
                ...item,
                Sort: index + 1,
                rowId: uuidv4(),
                OprnOprtBegntime: dayjs(item?.OprnOprtBegntime)?.format(
                  'YYYY-MM-DD',
                ),
              };
            }),
          );
        } else {
          setEmrOperationData([]);
        }
      },
    },
  );

  return (
    <div
      className={'emr-oper-portal-container emr-draggable-container'}
      ref={setNodeRef}
      style={
        {
          display: `${operPortalShow ? 'flex' : 'none'}`,
          ...style,
          top: `${coordinates?.y}px`,
          left: `${coordinates?.x}px`,
          '--translate-x': `${transform?.x ?? 0}px`,
          '--translate-y': `${transform?.y ?? 0}px`,
        } as React.CSSProperties
      }
    >
      <div className={'emr-data-container'}>
        <div className={`emr-header-container`} {...listeners}>
          <div className={`title-container ${isDragging ? 'grabbing' : ''}`}>
            <HolderOutlined className={'handle'} size={30} />
            <span className={'title'}>
              {dmrOperDataShow === true ? '首页' : '医生端'}手术
            </span>
          </div>
          <CloseOutlined
            onClick={() => {
              setOperPortalShow(false);
            }}
          />
        </div>

        <UniTable
          id={'emr-oper-table'}
          rowKey={'rowId'}
          scroll={{ x: 'max-content', y: 300 }}
          loading={emrOperLoading}
          columns={operPortalColumns}
          dataSource={dmrOperDataShow === true ? dmrOperData : emrOperationData}
          pagination={false}
          dictionaryData={props?.dictData}
        />
      </div>
    </div>
  );
};

export default OperDataPortal;
