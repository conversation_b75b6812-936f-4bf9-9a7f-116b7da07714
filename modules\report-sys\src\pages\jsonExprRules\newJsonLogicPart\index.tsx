import _ from 'lodash';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Divider,
  Dropdown,
  Modal,
  Row,
  Space,
  Spin,
  Switch,
  Tabs,
  Tag,
  Typography,
  message,
  Descriptions,
  Empty,
  Input,
  Tooltip,
} from 'antd';
import './index.less';
import { useReducer, useState, Reducer, useEffect } from 'react';
import { Emitter } from '@uni/utils/src/emitter';
import { FieldsProvider } from './context/FieldsContext';
import {
  IReducer,
  IModalState,
  ITableState,
} from '@uni/reducers/src/interface';
import {
  InitTableState,
  TableAction,
  modalReducer,
  tableReducer,
} from '@uni/reducers/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { useModel, useRequest } from 'umi';
import { UniTable } from '@uni/components/src';
import { ExprRulesColumns } from './columns';
import { qualityControlRuleEventConstants, ReqActionType } from './constants';
import { PlusOutlined, RedoOutlined, ReloadOutlined } from '@ant-design/icons';
import QCRCFormModal from './components/formModal';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import BatchTestModal from './components/batchTestModal/index';
import TreeCtrlTable from '@uni/components/src/tree-ctrl-table';

const canRefresh =
  (window as any).externalConfig?.['his']?.devtool?.refresh ?? false;
const canEditColumn =
  (window as any).externalConfig?.['his']?.devtool?.editColumn ?? false;

const QualityControlRuleConfiguration = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, {
    ...InitTableState,
  });

  const [treeData, setTreeData] = useState(undefined);
  const [treeTableData, setTreeTableData] = useState([]);
  // treeKeySelected
  const [treeKeySelected, setTreeKeySelected] = useState(undefined);

  // table columns
  const {
    data: exprRulesColumnsData,
    mutate: mutateExprRulesColumns,
    loading: getExprRulesColumnsLoading,
    run: getExprRulesColumnsReq,
  } = useRequest(
    () =>
      uniCommonService('Api/Sys/QualitySys/GetExprRules', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      //   manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          TableDispatch({
            type: TableAction.columnsChange,
            payload: {
              columns: tableColumnBaseProcessor(
                ExprRulesColumns,
                res?.data?.Columns,
              ),
            },
          });
          return res.data?.Columns;
        }
      },
    },
  );

  // table datasource
  const {
    data: exprRulesData, // 切换template时用这个匹配
    loading: getExprRulesReqLoading,
    run: getExprRulesReq,
  } = useRequest(
    () => {
      return uniCommonService(`Api/Sys/QualitySys/GetExprRules`, {
        method: 'POST',
        requestType: 'json',
        data: {
          exprProvider: 'JsonLogic',
        },
      });
    },
    {
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0) {
          console.log(response);
          // datasource
          TableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: response?.data ?? [],
            },
          });

          return response?.data;
        } else {
          return {};
        }
      },
    },
  );

  // table 处理成 tree data 简单粗暴
  useEffect(() => {
    if (TableState.data.length > 0) {
      let treeData = [];

      TableState.data.forEach((item) => {
        // 获取 RuleType 和 SubType
        const ruleType = item.RuleType;
        const subType = item.SubType;

        // 查找或创建 ruleType 节点
        let ruleTypeNode = treeData.find((node) => node.title === ruleType);
        if (!ruleTypeNode) {
          ruleTypeNode = {
            key: ruleType,
            title: ruleType,
            name: ruleType,
            value: 0,
            remark: '',
            sort: 0, // 可以根据需要进行排序
            args: '', // 可以根据需要附加额外的参数
            children: [], // 初始化子节点
          };
          treeData.push(ruleTypeNode);
        }

        // 更新 ruleType 的数量
        ruleTypeNode.value++;

        // 查找或创建 subType 节点
        let subTypeNode = ruleTypeNode.children.find(
          (node) => node.title === subType,
        );
        if (!subTypeNode) {
          subTypeNode = {
            key: `${ruleType}-${subType}`,
            title: subType,
            name: subType,
            value: 0,
            remark: '',
            sort: 0, // 可以根据需要进行排序
            args: '', // 可以根据需要附加额外的参数
            children: [], // 可以根据需求添加子节点
          };
          ruleTypeNode.children.push(subTypeNode);
        }

        // 更新 subType 的数量
        subTypeNode.value++;
      });

      let finalTreeData = [
        {
          key: 'key-all',
          title: '全部',
          name: '全部',
          value: TableState.data?.length,
          children: treeData,
        },
      ];

      setTreeData({
        ...{ totalCount: TableState.data?.length },
        treeDataResult: finalTreeData,
      });
    }
  }, [TableState.data]);

  useEffect(() => {
    if (TableState.data.length > 0) {
      // 最后处理成 treeDataResult
      if (
        treeKeySelected?.keys?.at(0) === 'key-all' ||
        !treeKeySelected?.keys
      ) {
        setTreeTableData(TableState?.data);
        TableDispatch({
          type: TableAction.anyChange,
          payload: {
            title: '全部自定义规则列表',
          },
        });
        return;
      } else {
        setTreeTableData(
          TableState?.data?.filter((item) =>
            treeKeySelected?.subType
              ? item.RuleType === treeKeySelected?.ruleType &&
                item.SubType === treeKeySelected?.subType
              : item.RuleType === treeKeySelected?.ruleType,
          ),
        );
        TableDispatch({
          type: TableAction.anyChange,
          payload: {
            title: `${treeKeySelected?.ruleType}${
              treeKeySelected?.subType ? ' - ' + treeKeySelected?.subType : ''
            } 规则列表`,
          },
        });
      }
    }
  }, [TableState.data, treeKeySelected]);

  // 新建only 创建该规则对应的场景和errorLevel
  // Api/Sys/QualitySys/GetEditableTemplates
  const {
    data: editableTemplates,
    loading: getEditableTemplatesReqLoading,
    run: getEditableTemplatesReq,
  } = useRequest(
    () => {
      return uniCommonService(`Api/Sys/QualitySys/GetEditableTemplates`, {
        method: 'POST',
        requestType: 'json',
      });
    },
    {
      // manual: true,
      formatResult: (response: RespVO<any>) => {
        return response?.data;
      },
      onSuccess: (res, params) => {},
    },
  );

  // 更新 有效
  const { loading: upsertExprRuleReqLoading, run: upsertExprRuleReq } =
    useRequest(
      (data) => {
        return uniCommonService(`Api/Sys/QualitySys/UpsertExprRule`, {
          method: 'POST',
          requestType: 'json',
          data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<any>) => {
          return response;
        },
        onSuccess: (res, params) => {
          // params?.at(0).RuleCode === ruleCode
          if (res?.code === 0) {
            TableDispatch({
              type: TableAction.singleDataChange,
              payload: {
                key: 'RuleId',
                value: params?.at(0),
              },
            });
          }
        },
      },
    );

  useEffect(() => {
    Emitter.on(qualityControlRuleEventConstants.RELOAD_BY_JSONLOGIC, () => {
      getExprRulesReq();
    });

    Emitter.on(
      qualityControlRuleEventConstants.ISVALID_STATUS_CHANGE,
      (record) => {
        console.log('ISVALID_STATUS_CHANGE', record);
        upsertExprRuleReq(record);
      },
    );

    return () => {
      Emitter.off(qualityControlRuleEventConstants.RELOAD_BY_JSONLOGIC);
      Emitter.off(qualityControlRuleEventConstants.ISVALID_STATUS_CHANGE);
    };
  }, []);

  // 处理树选中刷新table
  const handleTreeOnSelect = (keys: React.Key[], info: any) => {
    let { node } = info;
    let ruleType = (keys?.at(0) as string)?.split('-')?.at(0);
    let subType = (keys?.at(0) as string)?.split('-')?.at(1);

    setTreeKeySelected({
      keys,
      ruleType,
      subType,
    });
  };

  // const tabItems = [
  //   {
  //     key: '1',
  //     label: '规则列表',
  //     children: (
  //       <UniTable
  //         className="json_logic_table"
  //         id="json_logic_table"
  //         rowKey="RuleId"
  //         forceColumnsUpdate
  //         loading={getExprRulesReqLoading || false}
  //         columns={TableState.columns}
  //         dataSource={TableState.data}
  //         scroll={{ x: 'max-content' }}
  //         dictionaryData={globalState?.dictData}
  //       />
  //     ),
  //   },
  //   {
  //     key: '2',
  //     label: '规则分类',
  //     children: (

  //     ),
  //   },
  // ];

  return (
    <FieldsProvider>
      {/* 以下是注释掉的代码
      <Card
      title="自定义质控规则配置明细"
      extra={
        <Space>
          <Button
            icon={<PlusOutlined />}
            onClick={(e) => {
              Emitter.emit(qualityControlRuleEventConstants.ADD_BY_JSONLOGIC);
            }}
            type="primary"
          >
            新增
          </Button>
          <Divider type="vertical" />
          {canRefresh && (
            <Tooltip title="刷新">
              <Button
                type={'text'}
                shape={'default'}
                icon={<RedoOutlined />}
                onClick={() => {
                  getExprRulesReq();
                }}
              ></Button>
            </Tooltip>
          )}
          {canEditColumn && (
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: `Api/Sys/QualitySys/GetExprRules`,
                onTableRowSaveSuccess: (newColumns) => {
                  TableDispatch({
                    type: TableAction.columnsChange,
                    payload: {
                      columns: tableColumnBaseProcessor(
                        ExprRulesColumns,
                        newColumns,
                      ),
                    },
                  });
                },
              }}
            />
          )}
        </Space>
      }
      className={'rule_list_tree_ctrl_table_container'}
      bordered={false}
    >
    */}
      <>
        <TreeCtrlTable
          className={'rule_list_tree_ctrl_table'}
          treeData={{
            title: '规则分类',
            subTitle: '自定义规则总数',
            subKey: 'totalCount',
            // noTag: true,
            data: {
              ...treeData,
            },
          }}
          treeLoading={getExprRulesReqLoading}
          treeAction={{
            onSearch: (e) => {
              //TODO
              console.log(e.target.value);
            },
            onSelect: handleTreeOnSelect,
          }}
          tableData={{
            columns: TableState.columns,
            dataSource: treeTableData,
            rowKey: 'Id',
            id: 'warning_hosp_table',
            loading: getExprRulesReqLoading,
            // pagination: TableState.backPagination,
            // onChange: backTableOnChange,
            dictionaryData: globalState?.dictData,
            scroll: { x: 'max-content' },
            // 3个额外的
            tableTitle: TableState.title,
            export: {
              isBackend: false,
              // backendObj: {
              //   url: 'Api/Emr/EmrSettleCheckWarning/ExportGetSettleCheckCardDetails',
              //   method: 'POST',
              //   data: {
              //     hospCode: searchParams?.hospCodes,
              //     Wards: searchParams?.wards,
              //     ...(TableState?.fetchParams || {}),
              //   },
              //   fileName: TableState.title as string,
              // },
              btnDisabled: TableState.data?.length < 1,
            },
            columnEdit: {
              columnInterfaceUrl: `Api/Sys/QualitySys/GetExprRules`,
              onTableRowSaveSuccess: (newColumns) => {
                TableDispatch({
                  type: TableAction.columnsChange,
                  payload: {
                    columns: tableColumnBaseProcessor(
                      ExprRulesColumns,
                      newColumns,
                    ),
                  },
                });
              },
            },
          }}
          treeColProps={{ flex: '300px' }}
          tableColProps={{ flex: 'auto' }}
        />

        <QCRCFormModal
          editableTemplates={editableTemplates}
          treeData={treeData?.treeDataResult}
        />
        <BatchTestModal
          listenEvent={qualityControlRuleEventConstants.TEST_BTN_CLK}
        />
      </>
    </FieldsProvider>
  );
};

export default QualityControlRuleConfiguration;
