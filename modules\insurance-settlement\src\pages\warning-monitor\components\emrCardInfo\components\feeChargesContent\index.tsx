import {
  But<PERSON>,
  Card,
  Col,
  Dropdown,
  MenuProps,
  Row,
  Space,
  Tabs,
  Tag,
  Tooltip,
} from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { v4 as uuidv4 } from 'uuid';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import UniEcharts from '@uni/components/src/echarts';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import './index.less';
import { UniTable } from '@uni/components/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import CardWithBtns from '@uni/components/src/cardWithBtns';
import { IChargeDetail } from './interface';
import { EventConstant } from '@uni/utils/src/emitter';
import { SettingOutlined } from '@ant-design/icons';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';

const FeeChargesTable = (props) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');

  const [tabItems, setTabItems] = useState([]);
  const [tabActiveKey, setTabActiveKey] = useState(undefined);

  const [chargeDetailStats, setChargeDetailStats] = useState([]);
  const [chargeDetails, setChargeDetails] = useState([]);
  const [chargeBm, setChargeBm] = useState([]);

  useEffect(() => {
    console.log(
      'tabIOtems',
      props?.tabData,
      props?.chargeTypeMode,
      globalState?.dictData,
    );

    if (props?.tabData) {
      let tabs = props?.tabData
        ?.filter((i) => i.ChargeTypeMode === props?.chargeTypeMode)
        ?.map((d, i) => {
          let label = globalState?.dictData?.[
            props?.chargeTypeMode === 'Med'
              ? 'MedChargeType'
              : 'StatsChargeType'
          ]?.find((v) => v?.Code === d?.FeeType)?.Name;
          if (i === 0) {
            setTabActiveKey(`${props?.chargeTypeMode}||${d?.FeeType}`);
          }
          return {
            ...d,
            FeeTypeName: label,
            label,
            key: `${props?.chargeTypeMode}||${d?.FeeType}`,
          };
        });

      const seen = new Set(); // 用于记录已经出现过的组合
      setTabItems(
        tabs.filter((item) => {
          const key = `${item.ChargeTypeMode}|${item.FeeType}`; // 组合键
          if (seen.has(key)) {
            return false; // 如果已经存在，过滤掉
          }
          seen.add(key); // 否则记录并保留
          return true;
        }),
      );
    }
  }, [props?.tabData, props?.chargeTypeMode, globalState?.dictData]);

  useEffect(() => {
    console.log('tabActiveKey && props?.hisId', tabActiveKey, props?.hisId);
    if (tabActiveKey && props?.hisId) {
      getChargeDetailStatsReq({
        HisId: props?.hisId,
        ChargeTypeMode: props?.chargeTypeMode,
        ChargeType: tabActiveKey?.split('||')?.at(1) ?? tabActiveKey,
      });
      getChargeDetailsReq({
        HisId: props?.hisId,
        ChargeTypeMode: props?.chargeTypeMode,
        ChargeType: tabActiveKey?.split('||')?.at(1) ?? tabActiveKey,
      });
      chsBmChargeDetailsDataReq({
        ChargeTypeMode: props?.chargeTypeMode,
        ChargeType: tabActiveKey?.split('||')?.at(1) ?? tabActiveKey,
        GroupCode: props?.chsDrgCode,
        ChsVersion: props?.chsVersion,
      });
    }
  }, [
    tabActiveKey,
    props?.chargeTypeMode,
    props?.chsDrgCode,
    props?.chsVersion,
    props?.hisId,
  ]);

  // GetChargeDetailStats columns
  const {
    data: chargeDetailStatsColumns,
    loading: getChargeDetailStatsColumnsLoading,
    mutate: mutateChargeDetailStatsColumns,
    run: getChargeDetailStatsColumnsReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestCardBundle/GetChargeDetailStats`,
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      ),
    {
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor(
            [
              {
                dataIndex: 'ChargeName',
                visible: true,
                width: 150,
                tooltip: '标签可查看文字提示',
                render: (text, record) => (
                  <Space>
                    <span>{text}</span>
                    {record?.IsNewToBm && (
                      <Tooltip title="未出现在历史">
                        <Tag color="red">新</Tag>
                      </Tooltip>
                    )}
                    {record?.IsCntOverToBm && (
                      <Tooltip title="数量超过历史">
                        <Tag color="magenta">数</Tag>
                      </Tooltip>
                    )}
                    {record?.IsAmtOverToBm && (
                      <Tooltip title="金额超过历史">
                        <Tag color="volcano">金</Tag>
                      </Tooltip>
                    )}
                  </Space>
                ),
              },
            ],
            res.data?.Columns,
          );
        }
      },
    },
  );
  // Current GetChargeDetailStats datasource
  const {
    data: currentChargeDetailStatsData,
    loading: getChargeDetailStatsLoading,
    run: getChargeDetailStatsReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestCardBundle/GetChargeDetailStats`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<IChargeDetail[]>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return _.orderBy(res.data, 'Cnt', 'desc');
          } else {
            return [];
          }
        }
      },
    },
  );

  // Current GetChargeDetails
  const {
    data: currentChargeDetailsData,
    loading: getChargeDetailsLoading,
    run: getChargeDetailsReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/FundSupervise/LatestCardBundle/GetChargeDetails`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            setChargeDetailStats(_.orderBy(res.data, 'Cnt', 'desc'));
            return _.orderBy(res.data, 'Cnt', 'desc');
          } else {
            setChargeDetailStats([]);
            return [];
          }
        }
      },
    },
  );

  // Current GetChargeDetails Columns
  const {
    data: currentChargeDetailsColumnsData,
    loading: currentChargeDetailsColumnsLoading,
    mutate: mutateChargeDetailsColumnsData,
    run: currentChargeDetailsColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/FundSupervise/LatestCardBundle/GetChargeDetails`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  //  GetChsBmChargeDetails
  const {
    data: chsBmChargeDetailsData,
    loading: chsBmChargeDetailsDataLoading,
    run: chsBmChargeDetailsDataReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Chs/Benchmark/GetChsBmChargeDetails`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return _.orderBy(res.data, 'UsageRatio', 'desc');
          } else {
            return [];
          }
        }
      },
    },
  );

  // GetChsBmChargeDetails columns
  const {
    data: ChsBmChargeColumnsData,
    loading: ChsBmChargeColumnsLoading,
    mutate: mutateChsBmChargeColumnsData,
    run: ChsBmChargeColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Chs/Benchmark/GetChsBmChargeDetails`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  // 根据历史数据处理当前数据
  // ● 个人收费明细与历史标杆进行join比较，对以下情况进行标记
  //     ○ 未出现在历史标杆中
  //     ○ 数量超过历史标杆
  //     ○ 金额超过历史标杆
  // ● 高亮未出现在历史标杆中的收费明细，并且排序在前面
  useEffect(() => {
    if (currentChargeDetailStatsData && currentChargeDetailsData) {
      let currentData = currentChargeDetailStatsData?.map((data) => {
        let resData = { ...data };

        if (
          (chsBmChargeDetailsData ?? [])?.findIndex(
            (item) => item.ChargeCode === data.ChargeCode,
          ) === -1
        ) {
          // 未出现在历史标杆
          resData = {
            ...resData,
            IsNewToBm: true,
          };
        }
        if (
          data?.Cnt >
          (chsBmChargeDetailsData ?? []).find(
            (item) => item.ChargeCode === data.ChargeCode,
          )?.Quantity
        ) {
          // 数量超过历史标杆
          resData = {
            ...resData,
            IsCntOverToBm: true,
          };
        }
        if (
          data?.DetItemFeeSumamt >
          (chsBmChargeDetailsData ?? []).find(
            (item) => item.ChargeCode === data.ChargeCode,
          )?.Amount
        ) {
          // 金额超过历史标杆
          resData = {
            ...resData,
            IsAmtOverToBm: true,
          };
        }
        // children
        resData = {
          ...resData,
          childrenList: (chsBmChargeDetailsData ?? [])?.filter(
            (detailData) => detailData.ChargeCode === data.ChargeCode,
          ),
        };
        return resData;
      });
      console.log('tabData', currentData, chsBmChargeDetailsData);
      setChargeDetails(
        _.orderBy(
          currentData,
          // 且新的及用量高于平均值的收费项目放在最前面
          ['IsNew', 'IsCntOverToBm', 'Cnt'],
          ['desc', 'desc', 'desc'],
        ),
      );
    }
  }, [
    currentChargeDetailStatsData,
    currentChargeDetailsData,
    chsBmChargeDetailsData,
  ]);

  // expand table
  const expandedRowRender = (record: any) => {
    console.log(
      'currentChargeDetailsColumnsData',
      currentChargeDetailsColumnsData,
    );
    return (
      <UniTable
        rowKey={'Id'}
        id="stdList"
        columns={currentChargeDetailsColumnsData}
        headerTitle={false}
        search={false}
        options={false}
        dictionaryData={globalState?.dictData}
        widthDetectAfterDictionary
        dataSource={_.cloneDeep(record?.childrenList || [])}
        pagination={false}
        scroll={{ x: 'max-content', y: 250 }}
      />
    );
  };

  // table edit dropdown
  const dropDownItems: MenuProps['items'] = [
    {
      label: '外部列设置',
      key: 'outer',
    },
    {
      label: '内部列设置',
      key: 'inner',
    },
  ];

  const dropDownOnClick: MenuProps['onClick'] = ({ key }) => {
    //
    if (key === 'outer') {
      (global?.window as any)?.eventEmitter?.emit(
        EventConstant.TABLE_COLUMN_EDIT,
        {
          columnInterfaceUrl:
            'Api/FundSupervise/LatestCardBundle/GetChargeDetailStats',
          onTableRowSaveSuccess: (newColumns) => {
            mutateChargeDetailStatsColumns(
              tableColumnBaseProcessor(
                [
                  {
                    dataIndex: 'ChargeName',
                    // visible: true,
                    width: 150,
                    tooltip: '标签可查看文字提示',
                    render: (text, record) => (
                      <Space>
                        <span>{text}</span>
                        {record?.IsNewToBm && (
                          <Tooltip title="未出现在历史">
                            <Tag color="red">新</Tag>
                          </Tooltip>
                        )}
                        {record?.IsCntOverToBm && (
                          <Tooltip title="数量超过历史">
                            <Tag color="magenta">数</Tag>
                          </Tooltip>
                        )}
                        {record?.IsAmtOverToBm && (
                          <Tooltip title="金额超过历史">
                            <Tag color="volcano">金</Tag>
                          </Tooltip>
                        )}
                      </Space>
                    ),
                  },
                ],
                newColumns,
              ),
            );
          },
        },
      );
    } else {
      (global?.window as any)?.eventEmitter?.emit(
        EventConstant.TABLE_COLUMN_EDIT,
        {
          columnInterfaceUrl:
            'Api/FundSupervise/LatestCardBundle/GetChargeDetails',
          onTableRowSaveSuccess: (newColumns) => {
            mutateChargeDetailsColumnsData(
              tableColumnBaseProcessor([], newColumns),
            );
          },
        },
      );
    }
  };

  return (
    <Card title="" size="small">
      <Row className="fee_charges_container">
        <Col style={{ width: '120px' }}>
          <Tabs
            size="small"
            tabPosition="left"
            style={{ height: 300 }}
            items={tabItems}
            activeKey={tabActiveKey}
            onTabClick={(key) => {
              setTabActiveKey(key);
            }}
          />
        </Col>
        <Col style={{ flex: 1 }}>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <h4>
                  当前收费项目（共{currentChargeDetailStatsData?.length || 0}
                  条）
                </h4>
                {(userInfo?.Roles ?? [])?.includes('Admin') && (
                  <Dropdown
                    menu={{ items: dropDownItems, onClick: dropDownOnClick }}
                  >
                    <Button
                      type="text"
                      shape="circle"
                      icon={<SettingOutlined className="infinity_rotate" />}
                    />
                  </Dropdown>
                )}
              </div>
              <UniTable
                id={`drg_fee_anal_detail_now`}
                rowKey="ChargeCode"
                scroll={{ y: 450 }}
                loading={getChargeDetailsLoading}
                columns={chargeDetailStatsColumns}
                pagination={false}
                dataSource={chargeDetails}
                // widthCalculate
                widthDetectAfterDictionary
                dictionaryData={globalState?.dictData}
                expandable={{
                  expandedRowRender: expandedRowRender,
                  rowExpandable: (record) => record.name !== 'Not Expandable',
                }}
              />
            </Col>
            <Col span={12}>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <h4>
                  历史收费项目（共{chsBmChargeDetailsData?.length || 0}条）
                </h4>
                {/* <Button
                  type="text"
                  shape="circle"
                  icon={<SettingOutlined className="infinity_rotate" />}
                  style={{ visibility: 'hidden' }}
                /> */}
                <TableColumnEditButton
                  columnInterfaceUrl={'Api/Chs/Benchmark/GetChsBmChargeDetails'}
                  onTableRowSaveSuccess={(columns) => {
                    mutateChsBmChargeColumnsData(
                      tableColumnBaseProcessor([], columns),
                    );
                  }}
                />
              </div>
              <UniTable
                id={`drg_fee_bm_detail_now`}
                rowKey="Id"
                scroll={{ y: 250 }}
                loading={chsBmChargeDetailsDataLoading}
                widthDetectAfterDictionary
                dictionaryData={globalState?.dictData}
                columns={ChsBmChargeColumnsData}
                //tableColumnBaseProcessor(
                //   [
                //     { dataIndex: 'ChargeName', visible: true, title: '项目' },
                //     { dataIndex: 'Quantity', visible: true, title: '历史数量' },
                //     { dataIndex: 'Amount', visible: true, title: '历史金额' },
                //     {
                //       dataIndex: 'UsageRatio',
                //       visible: true,
                //       title: '使用比例',
                //       dataType: 'Percent',
                //     },
                //   ],
                //   [],
                // )
                pagination={false}
                dataSource={chsBmChargeDetailsData}
              />
            </Col>
          </Row>
        </Col>
      </Row>
    </Card>
  );
};

export default FeeChargesTable;
