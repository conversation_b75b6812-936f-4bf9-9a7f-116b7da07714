import './index.less';
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  Button,
  Card,
  Col,
  Row,
  Input,
  Spin,
  Collapse,
  List,
  TableProps,
  Divider,
  Tag,
  Progress,
  Tree,
  Space,
  Statistic,
} from 'antd';
import DetailsBtn from '@uni/components/src/details-btn';
import UniEcharts from '@uni/components/src/echarts';
import { Link, useRequest } from 'umi';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src';
import {
  TableColumns,
  BasePageProps,
  RespVO,
} from '@uni/commons/src/interfaces';
import { useModel } from '@@/plugin-model/useModel';
import UniTable from '@uni/components/src/table';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import SearchForm from '@/components/searchForm';
import dayjs from 'dayjs';
import { toArrayMode } from '@/utils/widgets';
import IconBtn from '@uni/components/src/iconBtn';
import { DownOutlined } from '@ant-design/icons';
import { UseDispostionEnum, downloadFile } from '@uni/utils/src/download';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { ExportIconBtn } from '@uni/components/src';
import { TrendsOption, BarOption } from './chart.opts';
import { TrendColumns, ClideptColumns } from './columns';
import SingleStat from '@uni/components/src/statistic';

const { Panel } = Collapse;
const { Search } = Input;

interface DmrManagementProps extends BasePageProps {}

const CheckDetails = (props: DmrManagementProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, CliDepts } = globalState.searchParams;
  const [clickSubRule, setClickSubRule] = useState<any>({});
  const [defaultTreeData, setDefaultTreeData] = useState([]);
  const [requestParams, setRequestParams] = useState<any>({});
  const pagination: any = {
    current: 1,
    pageSize: 5,
    pageSizeOptions: ['5', '10', '20'],
    size: 'small',
  };
  const [
    deptSubRuleDetailTableDataSource,
    setDeptSubRuleDetailTableDataSource,
  ] = useState([]);

  const flagRef = useRef(1);

  const TransformArray = (inputArray) => {
    return inputArray.map((item) => {
      return {
        title: item.RuleType,
        key: item.RuleType,
        children: item.SubTypeStats.map((subItem) => {
          return {
            title: subItem.SubType,
            key: subItem.SubType,
            children: subItem.RuleStats.map((ruleItem) => {
              return {
                title: ruleItem.DisplayErrMsg,
                key: subItem.RuleCode,
                ...ruleItem,
              };
            }),
            ...subItem,
          };
        }),
        ...item,
      };
    });
  };
  useEffect(() => {
    if (dateRange?.length) {
      let tableParams = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
      };

      if (flagRef.current === 1) {
        flagRef.current++;
      } else {
        if (globalState?.searchParams?.triggerSource !== 'btnClick') {
        } else {
          getPrePostCheckCompareStatsReq(tableParams);
        }
      }
    }
  }, [dateRange, hospCodes]);

  const {
    loading: getPrePostCheckCompareStatsLoading,
    run: getPrePostCheckCompareStatsReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/Dmr/DmrPrePostCheckCompareReport/GetPrePostCheckCompareStats`,
        {
          method: 'POST',
          data: {
            ...data,
          },
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.RuleTypeStats.length) {
            let treeData = TransformArray(res.data?.RuleTypeStats);
            // let treeData = _.map(
            //   _.orderBy(res.data[0]?.['RuleTypeStats'], ['RuleType']),
            //   (data1, index1) => {
            //     let children = [];
            //     if (data1.SubTypeStats.length !== 0) {
            //       children = _.map(data1.SubTypeStats, (data2, index2) => {
            //         let children = [];
            //         if (data2.RuleStats.length !== 0) {
            //           children = _.map(data2.RuleStats, (data3, index3) => ({
            //             key: `key-${index1}-${index2}-${index3}`,
            //             title: data3.DisplayErrMsg,
            //             name: data3.DisplayErrMsg,
            //             value: data3.RuleCnt,
            //             args: { RuleCode: data3.RuleCode },
            //           }));
            //         }
            //         return {
            //           key: `key-${index1}-${index2}`,
            //           title: data2.SubType,
            //           name: data2.SubType,
            //           value: data2.SubTypeCnt,
            //           children,
            //           args: {
            //             RuleType: data1.RuleType,
            //             SubType: data2.SubType,
            //           },
            //         };
            //       });
            //     }
            //     return {
            //       key: `key-${index1}`,
            //       title: data1.RuleType,
            //       name: data1.RuleType,
            //       value: data1.RuleTypeCnt,
            //       children,
            //       args: { RuleType: data1.RuleType },
            //     };
            //   },
            // );
            setDefaultTreeData(treeData);
          } else {
            setDefaultTreeData([]);
            setDeptSubRuleDetailTableDataSource([]);
          }
        }
      },
    },
  );

  // 按月份
  const {
    data: prePostCheckCompareTrendData,
    loading: prePostCheckCompareTrendLoading,
    run: prePostCheckCompareTrendReq,
  } = useRequest(
    (data) => {
      return uniCommonService(
        'Api/Dmr/DmrPrePostCheckCompareReport/PrePostCheckCompareTrend',
        {
          method: 'POST',
          data: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0) {
          return response?.data;
        } else {
          return [];
        }
      },
    },
  );

  // 按科室
  const {
    data: prePostCheckCompareByCliDeptData,
    loading: prePostCheckCompareByCliDeptLoading,
    run: prePostCheckCompareByCliDeptReq,
  } = useRequest(
    (data) => {
      return uniCommonService(
        'Api/Dmr/DmrPrePostCheckCompareReport/PrePostCheckCompareByCliDept',
        {
          method: 'POST',
          data: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0 && response?.data?.length) {
          return _.orderBy(response?.data, ['PreCnt'], ['desc']);
        } else {
          return [];
        }
      },
    },
  );

  // tree
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  const dataList: { key; title: string }[] = [];
  const generateList = (data: any[]) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { key, title } = node;
      dataList.push({ key, title });
      if (node.children) {
        generateList(node.children);
      }
    }
  };
  generateList(defaultTreeData);
  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };
  const getParentKey = (key: React.Key, tree: any[]): React.Key => {
    let parentKey: React.Key;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    return parentKey!;
  };
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (value?.length === 0) {
      setExpandedKeys([]);
    } else {
      const newExpandedKeys = dataList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            return getParentKey(item.key, defaultTreeData);
          }
          return null;
        })
        .filter((item, i, self) => item && self.indexOf(item) === i);
      setExpandedKeys(newExpandedKeys as React.Key[]);
    }
    setSearchValue(value);
    setAutoExpandParent(true);
  };
  const treeData = useMemo(() => {
    const loop = (data: any[]) =>
      data.map((item) => {
        const strTitle = item.title as string;
        const index = strTitle.indexOf(searchValue);
        const beforeStr = strTitle.substring(0, index);
        const afterStr = strTitle.slice(index + searchValue.length);
        const title =
          index > -1 ? (
            <span>
              {beforeStr}
              <span className="site-tree-search-value">{searchValue}</span>
              {afterStr}
            </span>
          ) : (
            <span>{strTitle}</span>
          );
        if (item.children) {
          return { ...item, title, children: loop(item.children) };
        }

        return {
          ...item,
          title,
        };
      });
    return loop(defaultTreeData);
  }, [searchValue, defaultTreeData]);

  const onSelect = (selectedKeys, info) => {
    if (info.node?.RuleCode) setClickSubRule(info.node);
  };

  useEffect(() => {
    if (clickSubRule?.RuleCode) {
      setRequestParams({
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        RuleCode: clickSubRule?.RuleCode,
      });
    }
  }, [clickSubRule, dateRange, hospCodes]);

  useEffect(() => {
    if (Object.keys(requestParams)?.length) {
      prePostCheckCompareTrendReq(requestParams);
      prePostCheckCompareByCliDeptReq(requestParams);
    }
  }, [requestParams]);

  return (
    <>
      <div className="check-result-container" id="check-result-container">
        <Card>
          <Row gutter={20}>
            <Col span={8} className="border-right">
              <Spin spinning={getPrePostCheckCompareStatsLoading}>
                <div className="left-container">
                  <Search
                    style={{ padding: '0 0px 8px' }}
                    placeholder="搜索关键字"
                    onChange={onChange}
                  />
                  <Tree
                    showLine
                    switcherIcon={<DownOutlined />}
                    blockNode
                    onExpand={onExpand}
                    expandedKeys={expandedKeys}
                    autoExpandParent={autoExpandParent}
                    treeData={treeData}
                    onSelect={onSelect}
                    // height={880}
                    titleRender={(nodeData: any) => {
                      return (
                        <>
                          {' '}
                          <div>{nodeData?.title}</div>{' '}
                          <div>
                            <Tag>
                              {nodeData?.FixedCnt || 0}/{nodeData?.PreCnt}
                            </Tag>
                          </div>
                        </>
                      );
                    }}
                    style={{
                      maxHeight: '820px',
                      // maxHeight:
                      //   document.getElementById('site-layout-content')
                      //     ?.offsetHeight -
                      //   22 -
                      //   16 -
                      //   24 -
                      //   24 -
                      //   40 -
                      //   22 -
                      //   24 -
                      //   32 -
                      //   24 -
                      //   20,
                      overflowY: 'auto',
                    }}
                  />
                </div>
              </Spin>
            </Col>
            <Col span={16}>
              <h3 className="mb-1">
                {clickSubRule?.DisplayErrMsg && (
                  <>“{clickSubRule?.DisplayErrMsg || ''}”</>
                )}
                质控结果分析
              </h3>
              <Row
                gutter={[10, 10]}
                // style={{
                //   maxHeight:
                //     document.getElementById('site-layout-content')
                //       ?.offsetHeight -
                //     22 -
                //     16 -
                //     24 -
                //     24 -
                //     40 -
                //     22 -
                //     24 -
                //     32 -
                //     24 -
                //     20,
                //   overflowY: 'auto',
                // }}
              >
                <Col span={12}>
                  <SingleStat
                    loading={false}
                    title="质控总数量"
                    value={clickSubRule?.['PreCnt'] || '-'}
                    detailsUrl={
                      'Dmr/DmrPrePostCheckCompareReport/PreCheckReviewResultDetails'
                    }
                    args={requestParams}
                  ></SingleStat>
                </Col>
                <Col span={12}>
                  <div className={`single-stat-card`}>
                    <Row>
                      <Col span={12}>
                        <Statistic
                          title={'更正数'}
                          value={clickSubRule?.['FixedCnt'] || '-'}
                        />
                      </Col>
                      <Col
                        span={12}
                        style={{ justifyContent: 'end', display: 'flex' }}
                      >
                        <Statistic
                          title={'未更正数'}
                          value={clickSubRule?.['PostCnt'] || '-'}
                          suffix={
                            clickSubRule?.['PostCnt'] && (
                              <span style={{ fontSize: '24px' }}>
                                <DetailsBtn
                                  title={'未更正数'}
                                  args={requestParams}
                                  detailsUrl={
                                    'Dmr/DmrPrePostCheckCompareReport/PostCheckReviewResultDetails'
                                  }
                                  type={'dmr'}
                                />
                              </span>
                            )
                          }
                        />
                      </Col>
                      <Col span={24}>
                        <Progress
                          percent={
                            (clickSubRule?.['FixedCnt'] /
                              clickSubRule?.['PreCnt']) *
                              100 || 0
                          }
                          size="small"
                        />
                      </Col>
                    </Row>
                  </div>
                </Col>
                <Col span={24}>
                  <Card
                    title={
                      <div
                        className="d-flex"
                        style={{ justifyContent: 'space-between' }}
                      >
                        <span className="title">质控数变化趋势</span>
                        <Space>
                          <Divider type="vertical" />
                          <ExportIconBtn
                            isBackend={false}
                            frontendObj={{
                              columns: TrendColumns,
                              dataSource: prePostCheckCompareTrendData,
                              fileName: '质控数变化趋势',
                            }}
                            btnDisabled={
                              prePostCheckCompareTrendData?.length < 1
                            }
                          />
                        </Space>
                      </div>
                    }
                    size="small"
                  >
                    <Row gutter={10}>
                      <Col span={12} className="border-right">
                        <UniEcharts
                          elementId={'trend'}
                          height={250}
                          loading={prePostCheckCompareTrendLoading || false}
                          options={
                            (prePostCheckCompareTrendData !== 'apiErr' &&
                              prePostCheckCompareTrendData &&
                              TrendsOption(
                                prePostCheckCompareTrendData,
                                'MonthDate',
                              )) ||
                            {}
                          }
                        />
                      </Col>
                      <Col span={12}>
                        <UniTable
                          id={'pre-post-check-compare-trend-table'}
                          rowKey={'MonthDate'}
                          columns={
                            [
                              {
                                title: '',
                                width: 30,
                                fixed: 'left',
                                align: 'center',
                                visible: true,
                                valueType: 'option',
                                render: (_, record) => {
                                  return [
                                    <DetailsBtn
                                      title={`${dayjs(
                                        record?.['MonthDate'],
                                      )?.format('YYYY年MM月')}`}
                                      args={{
                                        ...requestParams,
                                        Sdate: dayjs(record?.['MonthDate'])
                                          .startOf('month')
                                          .format('YYYY-MM-DD'),
                                        Edate: dayjs(record?.['MonthDate'])
                                          .endOf('month')
                                          .format('YYYY-MM-DD'),
                                      }}
                                      detailsUrl={
                                        'Dmr/DmrPrePostCheckCompareReport/PreCheckReviewResultDetails'
                                      }
                                      type={'dmr'}
                                    />,
                                  ];
                                },
                              },
                              ...TrendColumns,
                            ] || []
                          }
                          pagination={pagination}
                          size="small"
                          scroll={{ x: 'max-content' }}
                          dataSource={prePostCheckCompareTrendData}
                          loading={prePostCheckCompareTrendLoading}
                          dictionaryData={globalState?.dictData}
                        />
                      </Col>
                    </Row>
                  </Card>
                </Col>
                <Col span={24}>
                  <Card
                    title={
                      <div
                        className="d-flex"
                        style={{ justifyContent: 'space-between' }}
                      >
                        <span className="title">科室质控数分布</span>
                        <Space>
                          <Divider type="vertical" />
                          <ExportIconBtn
                            isBackend={false}
                            frontendObj={{
                              columns: ClideptColumns,
                              dataSource: prePostCheckCompareByCliDeptData,
                              fileName: '科室质控数分布',
                            }}
                            btnDisabled={
                              prePostCheckCompareByCliDeptData?.length < 1
                            }
                          />
                        </Space>
                      </div>
                    }
                    size="small"
                  >
                    <Row gutter={10}>
                      <Col span={12} className="border-right">
                        <UniEcharts
                          elementId={'bar'}
                          height={250}
                          loading={prePostCheckCompareByCliDeptLoading || false}
                          options={
                            (prePostCheckCompareByCliDeptData !== 'apiErr' &&
                              prePostCheckCompareByCliDeptData &&
                              BarOption(
                                prePostCheckCompareByCliDeptData,
                                'CliDeptName',
                              )) ||
                            {}
                          }
                        />
                      </Col>
                      <Col span={12}>
                        <UniTable
                          id={'pre-post-check-compare-dept-table'}
                          rowKey={'CliDept'}
                          columns={
                            [
                              {
                                title: '',
                                width: 30,
                                fixed: 'left',
                                align: 'center',
                                visible: true,
                                valueType: 'option',
                                render: (_, record) => {
                                  return [
                                    <DetailsBtn
                                      title={`${record?.CliDeptName}`}
                                      args={{
                                        ...requestParams,
                                        CliDepts: [record?.CliDept],
                                      }}
                                      detailsUrl={
                                        'Dmr/DmrPrePostCheckCompareReport/PreCheckReviewResultDetails'
                                      }
                                      type={'dmr'}
                                    />,
                                  ];
                                },
                              },
                              ...ClideptColumns,
                            ] || []
                          }
                          size="small"
                          scroll={{ x: 'max-content' }}
                          dataSource={prePostCheckCompareByCliDeptData}
                          loading={prePostCheckCompareByCliDeptLoading}
                          dictionaryData={globalState?.dictData}
                          pagination={pagination}
                        />
                      </Col>
                    </Row>
                  </Card>
                </Col>
              </Row>
            </Col>
          </Row>
        </Card>
      </div>
    </>
  );
};
export default CheckDetails;
