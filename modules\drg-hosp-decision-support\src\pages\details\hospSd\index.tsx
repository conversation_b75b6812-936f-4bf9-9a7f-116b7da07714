import { Reducer, useEffect, useMemo, useReducer, useState } from 'react';
import { Dispatch, useDispatch, useModel, useSelector } from 'umi';
import { UniTable } from '@uni/components/src';
import { Card, TableProps } from 'antd';
import { useSafeState } from 'ahooks';
import { IReducer, ITableState } from '@uni/reducers/src/interface';
import { RespType, columnsHandler } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType } from '@/constants';
import { InitTableState, TableAction, tableReducer } from '@uni/reducers/src';
import { SorterResult } from 'antd/lib/table/interface';
import { SwagHospSdItem } from './interface';
import IconBtn from '@uni/components/src/iconBtn/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';
import ExportIconBtn from '@uni/components/src/backend-export';
import { addExportToLastSegment } from '@uni/utils/src/widgets';

const HospDrgs = () => {
  const {
    globalState: { searchParams },
  } = useModel('@@qiankunStateFromMaster');
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);
  let { versionedSdCode, SdCode } = searchParams;

  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagHospSdItem>, IReducer>
  >(tableReducer, {
    ...InitTableState,
    selectedKeys: [],
    selectedRecords: [],
    sorter: {},
    clkItem: null,
  });

  const [backPagination, setBackPagination] = useSafeState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    hideOnSinglePage: false,
  });

  const backTableOnChange: TableProps<any>['onChange'] = async (
    pagi,
    filter,
    sorter,
    extra,
  ) => {
    tableReq(
      searchParams,
      pagi.current,
      pagi.pageSize,
      sorter as SorterResult<SwagHospSdItem>,
    );
  };

  // 普通的tableReq
  const tableReq = async (
    params,
    cur = 1,
    size = 10,
    sorter = SearchTable.sorter,
  ) => {
    let res: (any | RespType)[] = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: ReqActionType.HospSdData,
        requestParams: [
          {
            name: ReqActionType.HospSdData,
            url: `Api/v2/Drgs/${ReqActionType.HospSdData}`,
            method: 'POST',
            data: {
              Sdate: params?.dateRange[0],
              Edate: params?.dateRange[1],
              // SdCode: versionedSdCode,
              SdCode: SdCode?.length ? SdCode : SdCode,
              HospCode:
                params?.hospCodes?.length ?? 0 > 0 ? params?.hospCodes : '%',
              current: cur,
              pageSize: size,
              //   sorting: sortingHandler(sorter), TODO
            },
          },
        ],
      },
    });
    if (typeof res !== 'string') {
      console.log('res666', res);
      let total = res.at(0)?.total;
      SearchTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res.at(0).data ?? [],
        },
      });

      // sorter
      if (!_.isEqual(sorter, SearchTable.sorter)) {
        SearchTableDispatch({
          type: TableAction.sortChange,
          payload: { sorter },
        });
      }

      setBackPagination({
        ...backPagination,
        current: cur,
        pageSize: size,
        total: total ?? 0,
      });
    }
  };

  useEffect(() => {
    tableReq(searchParams);
  }, [searchParams]);

  // columns处理
  if (
    columnsList?.[ReqActionType.HospSdData] &&
    SearchTable.columns.length < 1
  ) {
    SearchTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: columnsList[ReqActionType.HospSdData],
      },
    });
  }

  // columns 处理，主要用于处理options
  const columnsSolver = useMemo(() => {
    return SearchTable.columns ? columnsHandler(SearchTable.columns) : [];
  }, [SearchTable.columns]);

  return (
    <>
      <Card
        title="医院重点监控病种明细(院级)"
        extra={
          <ExportIconBtn
            isBackend={true}
            backendObj={{
              url: addExportToLastSegment(
                `Api/v2/Drgs/${ReqActionType.HospSdData}`,
              ),
              method: 'POST',
              data: {
                ...searchParams,
                Sdate: searchParams?.dateRange[0],
                Edate: searchParams?.dateRange[1],
                // SdCode: versionedSdCode,
                SdCode: SdCode?.length ? SdCode : undefined,
                HospCode:
                  searchParams?.hospCodes?.length ?? 0 > 0
                    ? searchParams?.hospCodes
                    : '%',
              },
              fileName: `医院重点监控病种明细(院级)`,
            }}
            btnDisabled={
              SearchTable.data?.length < 1 ||
              !SearchTable.data ||
              (SearchTable.data?.length === 1 && !SearchTable.data?.at(0))
            }
          />
        }
      >
        <UniTable
          // headerTitle="医院重点监控病种明细(院级)"
          id="hosp_sd"
          rowKey="CardId"
          showSorterTooltip={false}
          loading={loadings[ReqActionType.HospSdData] ?? false}
          columns={[
            {
              dataIndex: 'options',
              width: 40,
              visible: true,
              fixed: 'left',
              render: (text, record) => (
                <IconBtn
                  type="details"
                  style={{ margin: '0 10px' }}
                  onClick={(e) => {
                    setDrawerVisible({
                      hisId: record?.HisId,
                      type: 'drg',
                    });
                  }}
                />
              ),
            },
            ...columnsSolver,
          ]} // columnsHandler
          dataSource={SearchTable.data}
          pagination={backPagination}
          onChange={backTableOnChange}
          scroll={{ x: 'max-content' }}
          // toolBarRender={() => [<Button>导出Export</Button>]}
        />
      </Card>
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </>
  );
};

export default HospDrgs;
