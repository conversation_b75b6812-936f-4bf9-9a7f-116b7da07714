import React from 'react';

import './index.less';
import { Card, Tabs } from 'antd';
import BaseIcde from '@/pages/configuration/escalate/icde/base';
import EscalateBaseIcde from '@/pages/configuration/escalate/icde/base';
import EscalateTcmIcde from '@/pages/configuration/escalate/icde/tcm';
import EscalatePathologyIcde from '@/pages/configuration/escalate/icde/pathology';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';

const tabs = [
  {
    key: 'BaseIcde',
    label: '西医诊断',
    children: <EscalateBaseIcde />,
  },
  {
    key: 'TcmIcde',
    label: '中医诊断',
    children: <EscalateTcmIcde />,
  },
  {
    key: 'PathologyIcde',
    label: '病理诊断',
    children: <EscalatePathologyIcde />,
  },
];
const EscalateIcdeConfiguration = () => {
  const onTabChange = (key: string) => {
    Emitter.emit(ConfigurationEvents.ESCALATE_TAB_CHANGE, key);
  };

  return (
    <div
      id={'icde-configuration-container'}
      className={'icde-configuration-container'}
    >
      <Tabs
        defaultActiveKey={tabs?.at(0)?.key}
        // type="card"
        onChange={onTabChange}
        items={tabs}
      />
    </div>
  );
};

export default EscalateIcdeConfiguration;
