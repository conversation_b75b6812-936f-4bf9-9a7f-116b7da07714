import { useRequest } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { UniTable, ExportIconBtn } from '@uni/components/src';
import { Card, Divider, Space, message } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import { isRespErr } from '@/utils/widgets';
import _ from 'lodash';
import { uniMrService } from '@uni/services/src';
import { exportExcelByClaudeForCode } from '@uni/utils/src/excel-export';

const MrRoomSignInStats = () => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式

  // column
  const {
    data: columnData,
    loading: columnLoading,
    run: columnReq,
  } = useRequest(
    () => {
      return uniMrService('Api/Mr/Stats/MrRoomSignInStats', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      formatResult: (res: any) => {
        if (!isRespErr(res)) {
          console.log(res);
          // 根据 groupname过滤
          let temp = _.omit(_.groupBy(res?.data?.Columns, 'groupName'), [
            'null',
          ]);
          let result = res?.data?.Columns.filter((d) => !d?.groupName);
          result = result
            .concat(
              Object.keys(temp).map((d) => ({
                title: d,
                visible: true,
                exportable: true,
                children: temp[d]?.map((v) => ({ ...v, dataIndex: v.data })),
              })),
            )
            .map((d) => ({
              ...d,
              dataIndex: d.data,
            }));
          return result;
        } else {
          return undefined;
        }
      },
      onSuccess: (data) => {},
    },
  );

  // datasource
  const {
    data: dataSourceData,
    loading: dataSourceLoading,
    run: dataSourceReq,
  } = useRequest(
    (data) => {
      return uniMrService('Api/Mr/Stats/MrRoomSignInStats', {
        method: 'POST',
        data,
      });
    },
    {
      manual: true,
      formatResult: (res: any) => {
        if (!isRespErr(res)) {
          return res.data?.map((d) => {
            if (!d.CliDept && !d.Doctor) {
              return { ...d, CliDept: `${d.HospName}_合计` };
            } else if (d.CliDept && !d.Doctor) {
              return { ...d, CliDept: `${d.CliDept}_合计` };
            } else {
              return d;
            }
          });
        } else {
          return [];
        }
      },
      onSuccess: (data) => {},
    },
  );

  useDeepCompareEffect(() => {
    // cliDepts[], sdate, edate, hospCode[]
    let { dateRange, hospCode, dutyDept } = searchParams;
    // if (!dateRange) {
    //   console.log('dateRange', searchParams);
    //
    //   message.error('日期为必填');
    // } else {
    //   dataSourceReq({
    //     Sdate: dateRange?.at(0),
    //     Edate: dateRange?.at(1),
    //     HospCode: hospCode ? [hospCode] : undefined,
    //     CliDepts: dutyDept ? [dutyDept] : undefined,
    //   });
    // }

    // FIXME 修完header之后要去掉 下面 这个 HospCode注释 和 删除 hospCode
    dataSourceReq({
      // Sdate: dateRange?.at(0),
      // Edate: dateRange?.at(1),
      // HospCode: hospCode ? [hospCode] : undefined,
      CliDepts: dutyDept ? [dutyDept] : undefined,

      // customize header parameters
      ...searchParams,
      HospCode: hospCode
        ? Array.isArray(hospCode)
          ? hospCode
          : [hospCode]
        : undefined,
      // hospCode: hospCode
      //   ? Array.isArray(hospCode)
      //     ? hospCode
      //     : [hospCode]
      //   : undefined,
    });
  }, [searchParams]);

  return (
    <Card
      title="签收归档情况表"
      extra={
        <Space>
          <Divider type="vertical" />
          <ExportIconBtn
            isBackend={false}
            frontendObj={{
              columns: columnData,
              dataSource: dataSourceData,
              fileName: `${searchParams?.Sdate}-${searchParams?.Edate}_签收归档情况表`,
              customExportFunc: exportExcelByClaudeForCode,
            }}
            btnDisabled={!dataSourceData || dataSourceData?.length < 1}
          />
        </Space>
      }
    >
      <UniTable
        id="trace_acrchived"
        bordered
        rowKey="BarCode" // 特殊key，用于rowSelection
        showSorterTooltip={false}
        loading={dataSourceLoading}
        columns={columnData} // columnsHandler
        dataSource={dataSourceData || []}
        scroll={{ x: 'max-content' }}
        dictionaryData={dictData}
      />
    </Card>
  );
};

export default MrRoomSignInStats;
