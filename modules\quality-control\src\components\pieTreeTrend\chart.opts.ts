import theme from '@uni/components/src/echarts/themes/themeBlueYellow';
import { getLinebreakFormat } from '@uni/components/src/echarts/echarts.utils';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import _ from 'lodash';

export const RuleTypePie = (data, category = null) => {
  if (_.isEmpty(data)) {
    return {};
  }
  let color = theme.color;
  const option = {
    baseOption: {
      dataset: {
        source: data,
      },
      legend: {},
      tooltip: {
        trigger: 'item',
      },
      series: [
        {
          type: 'pie',
          encode: {
            itemName: ['RuleType'],
            value: ['RuleTypeCnt'],
            // tooltip: ['RuleTypeCnt'],
          },
          selectedMode: 'single',
          selectedOffset: 6,
          label: {
            show: true,
            fontSize: '13',
            formatter: '{d}%',
          },
          labelLine: {
            show: true,
          },
          select: {
            label: {
              show: true,
              fontWeight: 'bold',
            },
          },
          emphasis: {
            label: {
              show: true,
              fontWeight: 'bold',
            },
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 3,
          },
        },
      ],
    },
    media: [
      {
        option: {
          legend: {
            show: true,
            top: 'middle',
            right: '5%',
            orient: 'vertical',
          },
          series: [
            {
              radius: ['30%', '55%'],
              center: ['40%', '50%'],
              // emphasis: {
              //   label: {
              //     show: true,
              //   },
              // },
            },
          ],
        },
      },
      {
        //小屏
        query: {
          maxWidth: 420,
          minWidth: 0,
        },
        option: {
          legend: {
            show: true,
            top: 'top',
            right: 'auto',
            orient: 'horizontal',
          },
          series: [
            {
              radius: ['0%', '50%'],
              center: ['50%', '60%'],
            },
          ],
        },
      },
    ],
  };
  return option;
};

export const RuleTypeTreemap = (data, category = null) => {
  if (_.isEmpty(data)) {
    return {};
  }
  const option = {
    baseOption: {
      legend: {
        show: true,
      },
      tooltip: {
        formatter: function (info) {
          var value = info.value;
          return `${info.treePathInfo?.[1]?.name || ''} ${
            info.treePathInfo?.[2]?.name || ''
          }：${value}`;
        },
      },
      series: [
        {
          type: 'treemap',
          data,
          leafDepth: 1,
          levels: [
            {
              itemStyle: {
                borderColor: '#555',
                borderWidth: 0,
                gapWidth: 0,
              },
            },
            {
              colorSaturation: [0.3, 0.6],
              itemStyle: {
                borderColorSaturation: 0.7,
                gapWidth: 2,
                borderWidth: 2,
              },
            },
          ],
        },
      ],
    },
  };
  return option;
};

export const SelectedTrendsLine = (data, category) => {
  if (_.isEmpty(data)) {
    return {};
  }
  data = _.orderBy(data, 'MonthDate', 'asc');
  let color = theme.color;
  let option = {
    dataset: {
      source: data,
    },
    grid: {
      x: '13%',
      x2: '5%',
    },
    legend: {
      icon: 'circle',
      show: true,
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let value = params[0]?.data;
        if (value)
          return (
            `月份：${valueNullOrUndefinedReturnDash(
              value?.[category],
              'Month',
            )}<br /> ` +
            `例数：${valueNullOrUndefinedReturnDash(
              value?.['CardCnt'],
            )}<br /> ` +
            `占比：${valueNullOrUndefinedReturnDash(
              value?.[`CardCntRatio`],
              'Percent',
            )}%`
          );
      },
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        formatter: (value, index) => {
          return valueNullOrUndefinedReturnDash(value, 'Month');
        },
      },
    },
    yAxis: [
      {
        name: `例数`,
        type: 'value',
        position: 'left',
        splitLine: {
          show: true,
        },
      },
    ],
    series: [
      {
        name: `例数`,
        type: 'line',
        smooth: true,
        label: {
          normal: {
            show: true,
            formatter: (params) => {
              let value = params?.data;
              return value?.['CardCnt'] || '-';
            },
          },
        },
        areaStyle: {
          color: {
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 1,
                color: color[0], // 100% 处的颜色
              },
              {
                offset: 0,
                color: 'rgba(255,255,255,0)', // 0% 处的颜色
              },
            ],
            globalCoord: false, // 缺省为 false
          },
        },
        itemStyle: {
          normal: {
            color: color[0],
          },
        },
        encode: {
          x: category,
          y: ['CardCnt'],
        },
      },
    ],
  };
  return option;
};
