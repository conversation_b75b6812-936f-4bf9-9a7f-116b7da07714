import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { UniSelect } from '@uni/components/src';
import { Col, Space, Row, Tabs, Select } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import _ from 'lodash';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { uniCommonService } from '@uni/services/src/commonService';
import { RespVO } from '@uni/commons/src/interfaces';
import DrillDownDiseaseStructure from '@/components/DrillDownDiseaseStructure/index';
import Stats from '@/components/stats/index';
import BCGMatrixAndTable from '@/components/BCGMatrixAndTable/index';
import {
  DiseaseTypeDefaultOpts,
  DiseaseTypeDoctorAxisOpts,
  DiseaseTypeMedTeamAxisOpts,
} from '@/constants';
import { DiseaseNormalStat } from '@/constants';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';

const DiseaseType = () => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, MedTeams } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('diseaseStructure');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedItem, setSelectedItem] = useState(undefined);

  const [selectOpts, setSelectOpts] = useState([]);

  const [selectedDoctorValue, setSelectedDoctorValue] = useState(undefined);

  useEffect(() => {
    setSelectedDoctorValue(dictData?.['DoctorType']?.at(0)?.Code);
  }, [dictData?.['DoctorType']]);

  // tab 使用下拉框数据
  const { loading: ADrgCompositionLoading, run: ADrgCompositionReq } =
    useRequest(
      (data) =>
        uniCommonService(
          'Api/v2/Drgs/MedTeamADrgComposition/BundledADrgComposition',
          {
            method: 'POST',
            data: data,
          },
        ),
      {
        manual: true,
        formatResult: (res: RespVO<any>) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            if (res?.data?.length) {
              setSelectOpts(
                _.orderBy(res.data, 'PatCnt', 'desc')?.map((d) => ({
                  ...d,
                  label: `${d?.ADrgName}`,
                })),
              );
              // 默认把第一个设置为selected
              if (!selectedItem) {
                setSelectedItem(
                  res?.data?.at(0),
                  // _.maxBy(res?.data, function (o) {
                  //   return o.PatCnt;
                  // }),
                );
              }
            } else {
              setSelectOpts([]);
            }
          }
        },
      },
    );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
      MedTeams,
    };
    setTableParams(params);
    ADrgCompositionReq(params);
  }, [dateRange, hospCodes, MedTeams]);

  useEffect(() => {
    Emitter.on(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK, (record) => {
      setSelectedItem(record);
      setActiveKey('statistic');
    });

    return () => {
      Emitter.off(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK);
    };
  }, []);

  let tabItems = [
    {
      key: 'diseaseStructure',
      label: '病种结构',
      children: (
        <DrillDownDiseaseStructure
          tableParams={tableParams}
          filterColumns={[
            'ADrgName',
            'PatCnt',
            'PatRatio',
            'AvgRw',
            'AvgInPeriod',
            'AvgTotalFee',
          ]}
          compositionApi="Api/v2/Drgs/MedTeamADrgComposition/BundledADrgComposition"
          dataSource={selectOpts}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.ADrgName,
              args: {
                ...tableParams,
                VersionedADrgCode: record?.VersionedADrgCode,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'statistic',
      label: '医疗组综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Drgs/MedTeamADrgComposition/BundledADrgComposition`}
              trendApi={`Api/v2/Drgs/MedTeamADrgComposition/ADrgCompositionTrend`}
              columns={DiseaseNormalStat}
              defaultSelectItem={'PatCnt'}
              type="col-xl-8"
              chartHeight={300}
              tabKey={activeKey}
              useGlobalState
              level="adrg"
              selectedTableItem={selectedItem}
              tableParams={tableParams}
            />
          </Col>
          <Col span={24}>
            <BCGMatrixAndTable
              title="医疗组效率"
              tableTitle="医疗组分布"
              type="medTeam"
              emitter={EventConstant.MED_TEAM_TABLE_ROW_CLICK}
              category="MedTeamName"
              listValueKey="PatCnt"
              axisOpts={DiseaseTypeMedTeamAxisOpts}
              defaultAxisOpt={DiseaseTypeDefaultOpts}
              args={{
                api: 'Api/v2/Drgs/MedTeamADrgComposition/ADrgCompositionByMedTeam',
                columns: [],
                extraApiArgs: {
                  VersionedADrgCode: selectedItem?.VersionedADrgCode,
                },
              }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.MedTeamName,
                  args: {
                    ...tableParams,
                    MedTeams: [record?.MedTeam],
                    VersionedADrgCode: selectedItem?.VersionedADrgCode,
                  },
                  detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                  dictData: dictData, // 传入
                });
              }}
            />
          </Col>
        </Row>
      ),
    },
    {
      key: 'medTeam_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <Col span={24}>
          <BCGMatrixAndTable
            title="医生效率"
            tableTitle="医生分布"
            type="medTeam"
            emitter={EventConstant.DOCTOR_TABLE_ROW_CLICK}
            category="DoctorName"
            listValueKey="PatCnt"
            axisOpts={DiseaseTypeDoctorAxisOpts}
            defaultAxisOpt={DiseaseTypeDefaultOpts}
            args={{
              api: 'Api/v2/Drgs/DoctorADrgComposition/ADrgCompositionByDoctor',
              extraApiArgs: {
                VersionedADrgCode: selectedItem?.VersionedADrgCode,
                DoctorType: (
                  selectedDoctorValue as string
                )?.toLocaleUpperCase(),
              },
              columns: [],
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.DoctorName,
                args: {
                  ...tableParams,
                  DoctorCodes: [record?.DoctorCode],
                  DoctorType: (
                    record?.DoctorType as string
                  )?.toLocaleUpperCase(),
                  VersionedADrgCode: selectedItem?.VersionedADrgCode,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                dictData: dictData, // 传入
              });
            }}
          />
        </Col>
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
        tabBarExtraContent={{
          right: (
            <>
              <Space>
                {activeKey !== 'diseaseStructure' && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <label>当前病种：</label>
                    <UniSelect
                      width={300}
                      showSearch
                      dataSource={selectOpts}
                      value={selectedItem?.VersionedADrgCode}
                      onChange={(value) => {
                        setSelectedItem(
                          selectOpts?.find(
                            (d) => d?.VersionedADrgCode === value,
                          ),
                        );
                      }}
                      allowClear={false}
                      optionNameKey={'label'}
                      optionValueKey={'VersionedADrgCode'}
                      enablePinyinSearch={true}
                      fieldNames={{
                        // label: 'ChsDrgName',
                        value: 'VersionedADrgCode',
                      }}
                    />
                  </div>
                )}
                {activeKey === 'medTeam_doctorAnalysis' && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <label>医生类型：</label>
                    <Select
                      options={dictData?.['DoctorType']}
                      style={{ width: '200px' }}
                      fieldNames={{ value: 'Code', label: 'Name' }}
                      placeholder="请选择"
                      allowClear={false}
                      value={selectedDoctorValue}
                      onChange={(value) => {
                        setSelectedDoctorValue(value);
                      }}
                    />
                  </div>
                )}
              </Space>
            </>
          ),
        }}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default DiseaseType;
