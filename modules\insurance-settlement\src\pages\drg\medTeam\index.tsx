import { Card, Col, Row, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { useModel } from 'umi';
import Stats from '@/components/stats';
import { GrpDefaultOpts, GrpQuadrantAxisOpts } from '../optsConstants';
import {
  DeptTotalStatsColumns,
  SettleCompStatsByGrpColumns,
  SettleCompStatsByMedTeamColumns,
  TabCommonItems,
} from '../constants';
import { isEmptyValues } from '@uni/utils/src/utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import FeeCompositionAndAnalysis from '@/components/feeCompositionAndAnalysis';
import GradientChartAndTable from '@/components/gradientChartAndTable';
import IconBtn from '@uni/components/src/iconBtn';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '@/pages/drg/components/drawerCardInfo';
import GradientChartAndTableAndPie from '@/components/gradientChartAndTableAndPie';
import TrendAnalysis from '@/components/trendAnalysis';

const DrgMedTeamAnalysis = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, MedTeams, insurType } = globalState?.searchParams;

  const [activeKey, setActiveKey] = useState('statistic');
  const [requestParams, setRequestParams] = useState<any>({});
  const [drawerVisible, setDrawerVisible] = useState(undefined);
  const [selectedStatItem, setSelectedStatItem] = useState({
    contentData: 'Profit',
    dataType: 'Currency',
    title: '盈亏',
  });
  const [statsHeight, setStatsHeight] = useState<number>(295);

  useEffect(() => {
    const updateHeight = () => {
      const element = document.getElementById('medTeam-stats-list');
      if (element) {
        const height = element.getBoundingClientRect().height;
        if (height > 0) {
          setStatsHeight(height - 50 - 24);
        }
      }
    };

    // 初始更新
    updateHeight();

    // 创建一个观察器实例
    const observer = new MutationObserver(updateHeight);

    // 配置观察选项
    const config = { attributes: true, childList: true, subtree: true };

    // 开始观察目标节点
    const targetNode = document.getElementById('medTeam-stats-list');
    if (targetNode) {
      observer.observe(targetNode, config);
    }

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    if (
      (globalState?.searchParams &&
        globalState?.searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(requestParams) &&
        globalState?.searchParams?.dateRange?.length &&
        globalState?.searchParams?.hospCodes?.length &&
        globalState?.searchParams?.MedTeams?.length)
    ) {
      let tableParams = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        MedTeams,
        insurType,
      };
      setRequestParams(tableParams);
    }
  }, [globalState?.searchParams]);

  // stat click 由Component Stats传入
  useEffect(() => {
    Emitter.on(EventConstant.STAT_ON_CLICK_EMITTER, (record) => {
      setSelectedStatItem(record);
    });
    return () => {
      Emitter.off(EventConstant.STAT_ON_CLICK_EMITTER);
    };
  }, []);

  let tabItems = [
    {
      key: TabCommonItems.statistic.key,
      label: TabCommonItems.statistic.title,
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={12} xl={11}>
            <Row gutter={[16, 16]} id="medTeam-stats-list">
              <Stats
                level="medTeam"
                api={`Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsOfMedTeam`}
                columns={DeptTotalStatsColumns}
                type="col-xl-6"
                outerTableParams={requestParams}
                onClickEmitter={EventConstant.STAT_ON_CLICK_EMITTER}
              />
            </Row>
          </Col>

          <Col xs={24} sm={24} md={24} lg={12} xl={13}>
            <TrendAnalysis
              title="医疗组月度变化趋势"
              height={statsHeight}
              selectedStatItem={selectedStatItem}
              requestParams={requestParams}
              dictData={globalState.dictData}
            />
          </Col>
        </Row>
      ),
    },
    {
      key: TabCommonItems.feeAnalysis.key,
      label: TabCommonItems.feeAnalysis.title,
      children: (
        <FeeCompositionAndAnalysis
          requestParams={requestParams}
          level="medTeam"
          api={`Api/FundSupervise/LatestDrgSettleStats/FeeChargeDistribution`}
          tabKey={activeKey}
        />
      ),
    },
    {
      key: TabCommonItems.drgAnalysis.key,
      label: TabCommonItems.drgAnalysis.title,
      children: (
        <GradientChartAndTableAndPie
          args={{
            level: 'medTeam',
            type: 'drg',
            title: '病组效率',
            category: 'ChsDrgName',
            columns: [
              {
                dataIndex: 'operation',
                visible: true,
                width: 40,
                align: 'center',
                order: 1,
                title: '',
                render: (node, record, index) => {
                  return (
                    <IconBtn
                      type="details"
                      onClick={(e) => {
                        e.stopPropagation();
                        Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                          id: 'drg-med-team-settle-stats-by-chsdrg',
                          title: record?.ChsDrgName,
                          args: {
                            ...requestParams,
                            VersionedChsDrgCodes: record?.VersionedChsDrgCode
                              ? [record?.VersionedChsDrgCode]
                              : [],
                          },
                          type: 'drg',
                          detailsUrl:
                            'FundSupervise/LatestDrgSettleStats/SettleDetails',
                          dictData: globalState?.dictData,
                        });
                      }}
                    />
                  );
                },
              },
              ...SettleCompStatsByGrpColumns,
            ],
            clickable: true,
            emitter: EventConstant.DRG_TABLE_ROW_CLICK,
            detailsTitle: '病组分布',
            axisOpts: GrpQuadrantAxisOpts,
            defaultAxisOpt: GrpDefaultOpts,
            api: `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByGrp`,
          }}
          requestParams={requestParams}
        />
      ),
    },
    {
      key: TabCommonItems.deptAnalysis.key,
      label: '医疗组对比分析',
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTable
                args={{
                  clickable: false,
                  cols: 'col-xl-24',
                  title: '医疗组对比分析',
                  category: 'MedTeamName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      fixed: 'left',
                      order: 1,
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                id: 'drg-med-team-settle-stats-by-med-team',
                                title: record?.MedTeamName,
                                args: {
                                  ...requestParams,
                                  MedTeams: record?.MedTeam
                                    ? [record?.MedTeam]
                                    : [''],
                                },
                                type: 'drg',
                                detailsUrl:
                                  'FundSupervise/LatestDrgSettleStats/SettleDetails',
                                dictData: globalState?.dictData,
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByMedTeamColumns,
                  ],
                  api: `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByMedTeam`,
                  level: 'medTeam',
                }}
                noChart={true}
                requestParams={requestParams}
              />
            </Col>
          </Row>
        </>
      ),
    },
  ];

  return (
    <Card>
      <Tabs
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={globalState.dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </Card>
  );
};

export default DrgMedTeamAnalysis;
