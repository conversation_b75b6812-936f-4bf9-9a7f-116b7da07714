import React, { useEffect, useState } from 'react';
import './index.less';
import { Button, Form, Modal, Switch } from 'antd';
import { Emitter } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import Search from 'antd/es/input/Search';
import { cloneDeep } from 'lodash';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { isEmptyValues } from '@uni/utils/src/utils';
import CheckableTag from 'antd/lib/tag/CheckableTag';
import {
  fieldItemsProcessor,
  filterFlagProcessor,
  filterFlagWithLeftStartProcessor,
  treeFilter,
} from '@/pages/combine-query/utils';
import { CombineQueryFieldItem } from '@/pages/combine-query/interfaces';
import { filterIsCommonItems } from '@/pages/combine-query/combo-table/utils';
interface ColumnNameMoreProps {
  mainForm: any;
  config?: any;
  treeData?: any[];
  fields?: any[];

  queryableList?: CombineQueryFieldItem[];

  formKey?: string[];
}

const leftStartSearch =
  (window as any)?.externalConfig?.['common']?.leftStartSearch ?? false;

const ColumnNameMoreModal = (props: ColumnNameMoreProps) => {
  const [comboQueryColumnSelectOpen, setComboQueryColumnSelectOpen] =
    React.useState(false);

  const [comboQueryMoreFormKey, setComboQueryMoreFormKey] = useState(undefined);

  const [form] = Form.useForm();

  console.log('ColumnNameMoreModal fields', props?.fields);

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE,
      (payload) => {
        setComboQueryColumnSelectOpen(payload?.visible);
        setComboQueryMoreFormKey(payload?.formKey);
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_COLUMN_MORE,
      );
    };
  }, []);

  return (
    <Modal
      title={`所有条件`}
      width={1200}
      open={comboQueryColumnSelectOpen}
      className={'combo-query-column-more-container'}
      destroyOnClose={true}
      closable={true}
      onOk={() => {
        let recordId = comboQueryMoreFormKey?.at(0);
        let values = form.getFieldsValue(true);
        console.log('formValues', values);

        if (values['selectedItem']) {
          let selectedItem = values['selectedItem'];
          let paths = selectedItem?.path?.split('.');
          props?.mainForm.setFieldValue(comboQueryMoreFormKey, [
            selectedItem?.path,
          ]);

          props?.mainForm.setFieldValue([recordId, 'columnValue'], undefined);
          props?.mainForm.setFieldValue(
            [recordId, 'operator'],
            selectedItem?.extra?.operators?.at(0) ?? undefined,
          );
          props?.mainForm.setFieldValue(
            [recordId, 'expr'],
            selectedItem?.extra?.expr ?? selectedItem?.extra?.name,
          );
          props?.mainForm.setFieldValue(
            [recordId, 'extra'],
            selectedItem?.extra,
          );

          props?.mainForm.setFieldValue(
            [recordId, 'dataType'],
            selectedItem?.extra?.type,
          );

          // 表示当前第一层的是!group  | array 也要有上级 前提是当前这个只有2层 不然会出错
          if (paths?.length === 2) {
            let rootField = props?.fields?.find(
              (item) => item?.key === paths?.at(0),
            );

            if (
              rootField?.extra?.dataType === 'array' &&
              rootField?.extra?.type === '!group'
            ) {
              props?.mainForm.setFieldValue(
                [recordId, 'parentField'],
                rootField,
              );
            }
          }

          // 表示是第三层
          if (paths?.length > 2) {
            let rootField = props?.fields?.find(
              (item) => item?.key === paths?.at(0),
            );
            let secondLevelField = rootField?.items?.find(
              (item) => item?.key === paths?.at(1),
            );
            props?.mainForm.setFieldValue(
              [recordId, 'parentField'],
              secondLevelField,
            );
          }
        }

        setComboQueryColumnSelectOpen(false);
      }}
      onCancel={() => {
        setComboQueryColumnSelectOpen(false);
      }}
      getContainer={() => document.getElementById('combo-table-ng-container')}
    >
      <Form form={form} preserve={false}>
        <Form.Item hidden={true} name={'selectedItem'} />
        <ColumnNameMoreContent
          mainForm={props?.mainForm}
          treeData={props?.fields}
          formKey={comboQueryMoreFormKey}
        />
      </Form>
    </Modal>
  );
};

const ColumnNameMoreContent = (props: ColumnNameMoreProps) => {
  let checkKey = undefined;
  if (props?.formKey) {
    checkKey = props?.mainForm?.getFieldValue(props?.formKey);
  }

  return (
    <div className={'detail-columns-setting-info-container'}>
      <DetailColumnsSettingTree
        treeData={props?.treeData?.sort(
          (a, b) =>
            (a?.extra?.directorySort ?? 65535) -
            (b?.extra?.directorySort ?? 65535),
        )}
        checkKeys={checkKey}
        commonItems={filterIsCommonItems(props?.treeData)?.map((item) => {
          return {
            ...item,
            name: item?.extra?.title2,
            value: item?.path,
          };
        })}
      />
    </div>
  );
};

interface DetailColumnsSettingTreeProps {
  treeData?: any[];
  checkKeys?: string[];
  commonItems?: any[];
}

export const DetailColumnsSettingTree = (
  props: DetailColumnsSettingTreeProps,
) => {
  const form = Form.useFormInstance();

  const [keyword, setKeyword] = useState('');

  const [selectedAnchor, setSelectedAnchor] = useState<string>('');

  const [selectedItems, setSelectedItems] = useState<any[]>([]);

  useEffect(() => {
    setSelectedAnchor(props?.treeData?.at(0)?.key);
  }, [props?.treeData]);

  useEffect(() => {
    setSelectedItems(props?.checkKeys);
  }, [props?.checkKeys]);

  const onItemClicked = (item, checked) => {
    console.log('selectedItem', item);
    if (checked) {
      setSelectedItems([item?.path]);
      form.setFieldValue('selectedItem', item);
    } else {
      setSelectedItems([]);
      form.setFieldValue('selectedItem', undefined);
    }
  };

  const elementIsVisibleInViewport = () => {
    props?.treeData
      ?.map((item) => {
        return document.getElementById(item?.key);
      })
      ?.filter((item) => item)
      ?.forEach((el) => {
        const elementRect = el.getBoundingClientRect();
        const containerRect = document
          .getElementById('combo-query-column-name-tree-container')
          .getBoundingClientRect();
        let visible =
          containerRect.top - (elementRect.top - 10) > 0 &&
          containerRect.top - elementRect.top <= elementRect.height;

        if (visible) {
          setSelectedAnchor(el.id);
        }
      });
  };

  const renderCommonItems = () => {
    console.log('commonItems', props?.commonItems);

    return (
      <div className={'common-selection-container'}>
        <div className={'common-selection-header-container'}>
          <div className={'common-header-label-container'}>
            <span className={'title'}>常用</span>
          </div>
        </div>

        <div className={'common-items-container'}>
          {props?.commonItems
            ?.filter((item) => !isEmptyValues(item?.label))
            ?.filter((item) => {
              if (keyword) {
                return leftStartSearch
                  ? filterFlagWithLeftStartProcessor(item, keyword)
                  : filterFlagProcessor(item, keyword);
              }
              return true;
            })
            ?.sort(
              (a, b) =>
                (a?.extra?.commonSequence ?? 65535) -
                (b?.extra?.commonSequence ?? 65535),
            )
            ?.map((tagItem) => {
              return (
                <CheckableTag
                  className={
                    selectedItems?.find((item) => item === tagItem?.path)
                      ? 'tag-item-checked'
                      : 'tag-item'
                  }
                  key={tagItem?.key}
                  checked={
                    selectedItems?.find((item) => item === tagItem?.key) !==
                    undefined
                  }
                  onChange={(checked) => {
                    onItemClicked(tagItem, checked);
                  }}
                >
                  <div className={'flex-row-center label'}>{tagItem?.name}</div>
                </CheckableTag>
              );
            })}
        </div>
      </div>
    );
  };

  console.log('treeFilter', treeFilter(props?.treeData, keyword));
  let filteredTreeData = treeFilter(props?.treeData, keyword);

  return (
    <div className={'detail-columns-tree-container'}>
      <div className={'flex-row-center'} style={{ margin: '0px 20px' }}>
        <Search
          value={keyword}
          className={'detail-columns-search'}
          style={{ width: '100%' }}
          placeholder="请输入列名称"
          onChange={(event) => {
            setKeyword(event.target.value);
          }}
        />
      </div>

      <div className={'flex-row-center'}>
        {renderCommonItems()}

        <div className={'detail-columns-tree-content'}>
          <div className="toc-affix">
            <ul id="demo-toc" className="toc">
              {filteredTreeData
                ?.filter((item) => item?.items?.length > 0)
                ?.map((item) => {
                  console.log('anchor', item?.key, selectedAnchor);

                  return (
                    <li key={item?.key} title={item?.label}>
                      <a
                        className={
                          item?.key === selectedAnchor ? 'selected' : ''
                        }
                        onClick={() => {
                          setSelectedAnchor(item?.key);
                          document.getElementById(item?.key)?.scrollIntoView({
                            behavior: 'smooth',
                          });
                        }}
                      >
                        {item?.label}
                      </a>
                    </li>
                  );
                })}
            </ul>
          </div>

          <div
            id={'combo-query-column-name-tree-container'}
            className={'combo-query-column-name-tree-container'}
            onScroll={(event) => {
              let scrollToBottom =
                document
                  .getElementById('combo-query-column-name-tree-container')
                  .getBoundingClientRect()?.height +
                  document.getElementById(
                    'combo-query-column-name-tree-container',
                  )?.scrollTop ===
                document.getElementById(
                  'combo-query-column-name-tree-container',
                )?.scrollHeight;
              if (scrollToBottom) {
                setSelectedAnchor(
                  props?.treeData?.at(props?.treeData?.length - 1)?.key,
                );
                return;
              }
              elementIsVisibleInViewport();
            }}
          >
            {filteredTreeData?.map((treeItem) => {
              return (
                <TreeItem
                  key={treeItem?.key}
                  itemKey={treeItem?.key}
                  title={treeItem?.label}
                  selected={selectedItems}
                  items={treeItem?.items ?? []}
                  onItemClicked={onItemClicked}
                  commonKeys={props?.commonItems?.map((item) => item?.key)}
                />
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

interface TreeItemProps {
  style?: React.CSSProperties;
  titleStyle?: React.CSSProperties;
  itemKey: string;
  title: string;
  selected: any[];
  items: any[];
  onItemClicked: (item, checked) => void;
  // 常用的keys
  commonKeys?: string[];
}

export const TreeItem = (props: TreeItemProps) => {
  const filteredItems = props?.items?.sort(
    (a, b) => (a?.columnSequence ?? 0) - (b?.columnSequence ?? 0),
  );

  return (
    <>
      {filteredItems?.length > 0 && (
        <div
          id={props?.itemKey}
          className={'tree-item-container'}
          style={props?.style ?? {}}
        >
          <div className={'tree-header-container'}>
            <div className={'label-container'}>
              <span className={'title'} style={props?.titleStyle ?? {}}>
                {props?.title}
              </span>
            </div>
          </div>

          <div className={'items-container'}>
            {filteredItems
              ?.filter((item) => !isEmptyValues(item?.label))
              // ?.filter((item) => !props?.commonKeys?.includes(item?.key))
              ?.sort(
                (a, b) =>
                  ((a?.items?.length
                    ? a?.extra?.directorySort
                    : a?.extra?.columnSequence) ?? 65535) -
                  ((b?.items?.length
                    ? b?.extra?.directorySort
                    : b?.extra?.columnSequence) ?? 65535),
              )
              ?.map((tagItem) => {
                return (
                  <>
                    {tagItem?.items?.length > 0 ? (
                      <TreeItem
                        style={{
                          width: '100%',
                          marginTop: 0,
                        }}
                        titleStyle={{ fontSize: 13 }}
                        key={tagItem?.key}
                        itemKey={tagItem?.key}
                        title={tagItem?.label}
                        selected={props?.selected}
                        items={tagItem?.items ?? []}
                        onItemClicked={props?.onItemClicked}
                        commonKeys={props?.commonKeys}
                      />
                    ) : !['!group', '!struct']?.includes(
                        tagItem?.extra?.type,
                      ) ? (
                      <CheckableTag
                        className={
                          props?.selected?.find(
                            (item) => item === tagItem?.path,
                          )
                            ? 'tag-item-checked'
                            : 'tag-item'
                        }
                        key={tagItem?.key}
                        checked={
                          props?.selected?.find(
                            (item) => item === tagItem?.key,
                          ) !== undefined
                        }
                        onChange={(checked) => {
                          props.onItemClicked(tagItem, checked);
                        }}
                      >
                        <div className={'flex-row-center label'}>
                          {tagItem?.label}
                        </div>
                      </CheckableTag>
                    ) : null}
                  </>
                );
              })}
          </div>
        </div>
      )}
    </>
  );
};

export default ColumnNameMoreModal;
