import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { UniSelect } from '@uni/components/src';
import { Col, Row, Tabs } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import _ from 'lodash';
import { TotalStatsColumns } from '../constants';
import { uniCommonService } from '@uni/services/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import DrillDownSdComposition from '../../components/drillDownSdComposition/index';
import Stats from '../../components/statsWithTrend';
import SingleColumnTable from '../../components/singleColumnTable/index';
import { RespVO } from '@uni/commons/src/interfaces';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '../../components/drawerCardInfo';
import { GroupByDoctorList } from '@/pages/quality/data';

const SdComposition = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, CliDepts } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('sdComposition');

  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedItem, setSelectedItem] = useState(undefined);

  const [selectOpts, setSelectOpts] = useState([]);

  // useEffect(() => {
  //   // 这个页面，先调用table + mdc
  //   MdcCoverageOfHospReq(searchParams);
  //   fetchTableReq(searchParams);
  // }, [searchParams]);

  // // 等table数据出来了，再调用下面的数据显示
  // useUpdateEffect(() => {
  //   if (tableState.clkItem) {
  //     fetchOtherReq({
  //       ...searchParams,
  //       ADrgCode: [tableState?.clkItem?.ADrgCode],
  //     });
  //   }
  // }, [tableState.clkItem]);

  // tab 使用下拉框数据
  const { loading: HqmsSdCompositionLoading, run: HqmsSdCompositionReq } =
    useRequest(
      (data) =>
        uniCommonService(
          // 'Api/Hqms/CliDeptHqmsSdComposition/BundledHqmsSdComposition',
          'API/v2/Hqms/HqmsStats/HqmsSdCompositionOfCliDept',
          {
            method: 'POST',
            data: data,
          },
        ),
      {
        manual: true,
        formatResult: (res: RespVO<any>) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            if (res?.data?.length) {
              setSelectOpts(
                res.data?.map((d) => ({
                  ...d,
                  label: `${d?.SdName}`,
                })),
              );
              // 默认把第一个设置为selected
              if (!selectedItem) {
                setSelectedItem(
                  res?.data?.at(0),
                  // _.maxBy(res?.data, function (o) {
                  //   return o.PatCnt;
                  // }),
                );
              }
            } else {
              setSelectOpts([]);
            }
          }
        },
      },
    );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
      CliDepts,
    };
    setTableParams(params);
    HqmsSdCompositionReq(params);
  }, [dateRange, hospCodes, CliDepts]);

  useEffect(() => {
    Emitter.on(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK, (record) => {
      setSelectedItem(record);
      setActiveKey('statistic');
    });

    return () => {
      Emitter.off(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK);
    };
  }, []);

  let tabItems = [
    {
      key: 'sdComposition',
      label: '单病种质量控制',
      children: (
        <DrillDownSdComposition
          tableParams={tableParams}
          // compositionApi="Api/Hqms/CliDeptHqmsSdComposition/BundledHqmsSdComposition"
          compositionApi="API/v2/Hqms/HqmsStats/HqmsSdCompositionOfCliDept"
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.SdName,
              args: {
                ...tableParams,
                SdCodes: record?.SdCode ? [record?.SdCode] : [],
              },
              detailsUrl: 'Hqms/Details/HqmsSdDetails',
              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'statistic',
      label: '科室综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              tableParams={tableParams}
              // api={`Api/Hqms/CliDeptHqmsSdComposition/BundledHqmsSdComposition`}
              api={`API/v2/Hqms/HqmsStats/HqmsSdCompositionOfCliDept`}
              // trendApi={`Api/Hqms/CliDeptHqmsSdComposition/HqmsSdCompositionTrend`}
              trendApi={`API/v2/Hqms/HqmsStats/HqmsSdCompositionTrend`}
              columns={TotalStatsColumns(selectedItem)}
              type="col-xl-6"
              chartHeight={320}
              tabKey={activeKey}
              useGlobalState
              level="sd"
              selectedTableItem={selectedItem}
            />
          </Col>
          <SingleColumnTable
            title="该病种科室分布"
            args={{
              // api: 'Api/Hqms/CliDeptHqmsSdComposition/HqmsSdCompositionByCliDept',
              api: 'API/v2/Hqms/HqmsStats/HqmsSdCompositionByCliDept',
              extraApiArgs: {
                SdCodes: selectedItem?.SdCode ? [selectedItem?.SdCode] : [],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="CliDeptName"
            type="table"
            visibleValueKeys={[
              'CliDeptName',
              'PatCnt',
              'PatRatio',
              'AvgInPeriod',
              'InPeriodExceededCnt',
              'AvgTotalFee',
              'TotalFeeExceededCnt',
              'DeathCnt',
              'DeathRatio',
              'MedicineFeeRatio',
              'MaterialFeeRatio',
            ]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.CliDeptName,
                args: {
                  ...tableParams,
                  SdCodes: selectedItem?.SdCode ? [selectedItem?.SdCode] : [],
                  CliDepts: record?.CliDept ? [record?.CliDept] : [],
                },
                detailsUrl: 'Hqms/Details/HqmsSdDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_medTeamAnalysis',
      label: '医疗组综合分析',
      children: (
        <Row>
          <SingleColumnTable
            title="该病种医疗组分布"
            args={{
              // api: 'Api/Hqms/MedTeamHqmsSdComposition/HqmsSdCompositionByMedTeam',
              api: 'API/v2/Hqms/HqmsStats/HqmsSdCompositionByMedTeam',
              extraApiArgs: {
                SdCodes: selectedItem?.SdCode ? [selectedItem?.SdCode] : [],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="MedTeamName"
            type="table"
            visibleValueKeys={[
              'MedTeamName',
              'PatCnt',
              'PatRatio',
              'AvgInPeriod',
              'InPeriodExceededCnt',
              'AvgTotalFee',
              'TotalFeeExceededCnt',
              'DeathCnt',
              'DeathRatio',
              'MedicineFeeRatio',
              'MaterialFeeRatio',
            ]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName,
                args: {
                  ...tableParams,
                  SdCodes: selectedItem?.SdCode ? [selectedItem?.SdCode] : [],
                  MedTeams: record?.MedTeam ? [record?.MedTeam] : [],
                },
                detailsUrl: 'Hqms/Details/HqmsSdDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <Row>
          <SingleColumnTable
            title="该病种医生分布"
            args={{
              // api: 'Api/Hqms/DoctorHqmsSdComposition/HqmsSdCompositionByDoctor',
              api: 'API/v2/Hqms/HqmsStats/HqmsSdCompositionByDoctor',
              extraApiArgs: {
                SdCodes: selectedItem?.SdCode ? [selectedItem?.SdCode] : [],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="DoctorName"
            type="table"
            visibleValueKeys={[
              'DoctorName',
              'PatCnt',
              'PatRatio',
              'AvgInPeriod',
              'InPeriodExceededCnt',
              'AvgTotalFee',
              'TotalFeeExceededCnt',
              'DeathCnt',
              'DeathRatio',
              'MedicineFeeRatio',
              'MaterialFeeRatio',
            ]}
            colSpan={{ span: 24 }}
            select={{
              dataKey: 'DoctorType',
              valueKey: 'GroupByDoctor',
              allowClear: false,
              defaultSelect: true,
              options: GroupByDoctorList,
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.DoctorName,
                args: {
                  ...tableParams,
                  SdCodes: selectedItem?.SdCode ? [selectedItem?.SdCode] : [],
                  DoctorCodes: record?.DoctorCode ? [record?.DoctorCode] : [],
                  DoctorType: (
                    record?.DoctorType as string
                  )?.toLocaleUpperCase(),
                },
                detailsUrl: 'Hqms/Details/HqmsSdDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
        tabBarExtraContent={{
          right: activeKey !== 'sdComposition' && (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <label>当前单病种：</label>
              <UniSelect
                width={300}
                showSearch
                dataSource={selectOpts}
                value={selectedItem?.SdCode}
                onChange={(value) => {
                  setSelectedItem(selectOpts?.find((d) => d?.SdCode === value));
                }}
                allowClear={false}
                optionNameKey={'label'}
                optionValueKey={'SdCode'}
                enablePinyinSearch={true}
                fieldNames={{
                  // label: 'ChsDrgName',
                  value: 'SdCode',
                }}
              />
            </div>
          ),
        }}
      />
      {/* 把modal与drawer的位置提高到这里，统一在这里处理 避免id太多导致匹配混乱 & tab切换后id失效 */}
      {/* TODO DetailTableModal内部的实现方式是destoryOnClose 导致每次打开都要重新获取columns */}
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      {/* drawer 同理 这样保持复用 因为drawer 监听的是detailTableModal 或者一些table */}
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default SdComposition;
