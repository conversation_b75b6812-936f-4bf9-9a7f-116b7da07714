.emr-draggable-container {
  border-radius: 5px;
  box-shadow: var(--box-shadow);
  transform: translate3d(var(--translate-x, 0), var(--translate-y, 0), 0)
    scale(var(--scale, 1));
  transition: box-shadow 300ms ease;
  padding: 0px 5px;

  display: flex;
  flex-direction: column;
  height: 410px;
  border: 1px solid rgb(171, 171, 171);
  position: absolute;
  z-index: 10000;
  background: #ffffff;
}

.emr-icde-portal-container {
  display: flex;
  flex-direction: column;
  width: 700px;
}

.emr-oper-portal-container {
  display: flex;
  flex-direction: column;
  width: 900px;
}

.emr-data-container {
  .emr-header-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: grab;
    height: 50px;
    padding: 0px 10px;

    .title-container {
      display: flex;
      flex: 1;
      flex-direction: row;
      align-items: center;

      .title {
        font-size: 18px;
        margin-left: 10px;
        font-weight: bold;
        color: #333333;
      }
    }
  }
}
