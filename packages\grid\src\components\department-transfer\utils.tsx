const publicModeHideKeys =
  (window as any).externalConfig?.['dmr']?.['publicModeHideKeys'] ?? [];

export const mergeColumnsInDepartmentTransferTable = (
  configurableDataIndex: string[],
  propertyColumns,
  columns,
) => {
  let mergedColumns = columns?.map((item) => {
    let columnProperty = (propertyColumns ?? [])?.find(
      (propertyItem) => item?.dataIndex === propertyItem?.dataIndex,
    );

    let extraHiddenColumnProps = {};

    if (global['PublicMode'] === true) {
      if (publicModeHideKeys?.includes(item?.dataIndex)) {
        extraHiddenColumnProps['visible'] = false;
        extraHiddenColumnProps['readonly'] = true;
      }
    }

    if (columnProperty) {
      let remoteProperty = {};
      configurableDataIndex?.forEach((key) => {
        remoteProperty[key] = columnProperty?.[key] ?? null;
      });

      return {
        ...item,
        ...remoteProperty,
        ...extraHiddenColumnProps,
      };
    }

    return {
      ...item,
      ...extraHiddenColumnProps,
    };
  });

  mergedColumns?.forEach((item) => {
    if (item?.dataIndex === 'TransferSort') {
      item['onCell'] = (record, rowIndex) => {
        if (record?.id === 'ADD') {
          return {
            colSpan: mergedColumns?.filter((item) => item?.visible === true)
              ?.length,
          };
        }

        return {
          record: record,
          rowIndex: rowIndex,
          rowkey: 'id',
          dataIndex: item?.dataIndex,
        };
      };
    } else {
      item['onCell'] = (record, rowIndex) => {
        if (record?.id === 'ADD') {
          return {
            colSpan: 0,
          };
        }

        return {
          record: record,
          rowIndex: rowIndex,
          rowkey: 'id',
          dataIndex: item?.dataIndex,
        };
      };
    }
  });

  const uniqueIdItem = [
    {
      dataIndex: 'UniqueId',
      title: 'UniqueId',
      visible: false,
      readonly: true,
    },
  ];

  return uniqueIdItem.concat(
    mergedColumns?.sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0)),
  );
};
