import Datepicker from '@uni/components/src/picker/datepicker';
import './index.less';
import locale from 'antd/es/date-picker/locale/zh_CN';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { Col, Input, message, Modal, Popconfirm, Row, Spin } from 'antd';
import {
  CloseOutlined,
  DragOutlined,
  EditTwoTone,
  PlusOutlined,
} from '@ant-design/icons';
import {
  BatchItem,
  BatchMasterItem,
  BatchMasterSysItem,
} from '@/pages/review/interface';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  RespVO,
  TableCardResp,
  TableColumns,
} from '@uni/commons/src/interfaces';
import RestrictInputNumber from '@/pages/review/components/restrict-number';
import uniqBy from 'lodash/uniqBy';
import { UniSelect } from '@uni/components/src';
import { useModel } from '@@/plugin-model/useModel';
import { cloneDeep } from 'lodash';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  assignBatch,
  BatchCreateAssignmentItem,
  createBatch,
  createBatchAssignments,
  createBatchReviewees,
  createBatchReviewers,
  createBatchWithDates,
  getBatchAssignments,
  getBatchReviewees,
  getBatchReviewers,
  getQualityExamineAssignments,
  getQualityExamineReviewees,
  getQualityExamineReviewers,
  ReviewerItem,
} from '@/pages/review/components/batch-create/services';
import {
  assembleAssignmentData,
  createReviewersRevieweesAssignmentsDataProcess,
} from '@/pages/review/components/batch-create/processors';
import { v4 as uuidv4 } from 'uuid';
import pick from 'lodash/pick';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { useAsyncEffect } from 'ahooks';
import { sortByBatchDate } from '@/pages/review/utils';
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import type { DragEndEvent } from '@dnd-kit/core/dist/types';
import { useReviewTabContext } from '@/pages/review/tab-hoc';
import {
  arrayMove,
  horizontalListSortingStrategy,
  rectSortingStrategy,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useRouteProps } from '@uni/commons/src/route-context';

const operationColor = '#1464f8';

const operationItemProps = {
  style: {
    color: operationColor,
    fontWeight: 'bold',
  },
};

interface DmrReviewBatchCreateWithEmployeeProps {
  createOpenStatus?: boolean;
  containerRef: any;
  masterId: number;
  batchId: number;
  batchInfo: BatchItem;
  masterItem: BatchMasterItem;
  masterSysItem: BatchMasterSysItem;
  reviewStatusData?: any;
}

const BatchCreateDroppableContainer = (props: any) => {
  const { setNodeRef } = useDroppable({
    id: props?.id,
  });

  return (
    <div ref={setNodeRef} className={'card-drop-container'}>
      {props.children}
    </div>
  );
};

interface BatchCreateSortableContainerProps {
  id: string;
  cardItem: any;
  index: number;
  onCardRevieweeAddClick: any;
  children?: any;
  masterItem?: BatchMasterItem;
}

const BatchCreateSortableContainer = (
  props: BatchCreateSortableContainerProps,
) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: props.id });

  return (
    <Col
      ref={setNodeRef}
      span={4}
      style={{
        minWidth: 260,
        transform: CSS.Translate.toString(transform),
        transition,
      }}
      {...attributes}
    >
      <div className={'card-item'}>
        {props?.children}
        <div className={'review-operation-container'}>
          <div className={'reviewer-move-container'} {...listeners}>
            <DragOutlined style={{ marginRight: 10 }} />
            <span>移动</span>
          </div>

          {props?.masterItem?.RevieweeType !== 'UnSpec' && (
            <div
              className={'reviewee-add-container'}
              onClick={() => {
                props?.onCardRevieweeAddClick(props?.cardItem, props?.index);
              }}
            >
              <PlusOutlined style={{ marginRight: 10 }} />
              <span>新增</span>
            </div>
          )}
        </div>
      </div>
    </Col>
  );
};

const DroppableContainer = (props: any) => {
  const { setNodeRef } = useDroppable({
    id: props.id,
  });

  return (
    <div ref={setNodeRef} className={'card-reviewee-container'}>
      {props.children}
    </div>
  );
};

const DraggableContainer = (props: any) => {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id: props?.id,
  });

  return (
    <Row
      ref={setNodeRef}
      className={'card-reviewee-item'}
      style={{
        transform: CSS.Translate.toString(transform),
        zIndex: !isEmptyValues(transform) ? 1000 : 1,
      }}
    >
      <Col span={2}>
        <DragOutlined {...listeners} {...attributes} />
      </Col>
      {props?.children}
    </Row>
  );
};

const DmrReviewBatchCreateWithEmployee = (
  props: DmrReviewBatchCreateWithEmployeeProps,
) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [editingItemId, setEditingItemId] = useState<string>(undefined);

  const [batchDate, setBatchDate] = useState<string>(
    dayjs().startOf('month').format('YYYY-MM-DD'),
  );

  const [batchDateRange, setBatchDateRange] = useState<string[]>();

  const [searchKeyword, setSearchKeyword] = useState<string>(undefined);

  const [cardData, setCardData] = useState<BatchCreateAssignmentItem[]>([]);

  const [employeeData, setEmployeeData] = useState(undefined);

  const [userList, setUserList] = useState([]);

  const [latestBatchId, setLatestBatchId] = useState(undefined);
  const [latestBatchInfo, setLatestBatchInfo] = useState<BatchItem>({});

  const [dmrTotalCount, setDmrTotalCount] = useState(0);

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      batchDate: batchDate,
      cardData: cardData,
      batchDateRange: batchDateRange,
    };
  });

  useEffect(() => {
    if (props?.createOpenStatus === true) {
      if (props?.reviewStatusData?.type === 'ADJUST') {
        setBatchDate(
          dayjs(props?.batchInfo?.BatchDate)
            ?.startOf('month')
            .format('YYYY-MM-DD'),
        );

        getBatchReviewValuesReq(props?.batchInfo?.BatchId);
        getQualityExamineSysValuesReq(false);
        if (!isEmptyValues(props?.batchInfo?.BatchDate)) {
          dmrTotalCountReq(
            dayjs(props?.batchInfo?.BatchDate)
              ?.startOf('month')
              .format('YYYY-MM-DD'),
            dayjs(props?.batchInfo?.BatchDate)
              ?.endOf('month')
              .format('YYYY-MM-DD'),
          );
        }
      } else {
        latestBatchInfoReq();
      }
    }
  }, [props?.createOpenStatus]);

  const onCardDeleteClick = (cardItem: any, cardIndex: number) => {
    let modifiedCardData = cloneDeep(cardData);
    modifiedCardData.splice(cardIndex, 1);
    setCardData(modifiedCardData);
  };

  const onCardRevieweeDeleteClick = (
    cardItem: BatchCreateAssignmentItem,
    revieweeItem: any,
    cardIndex: number,
    revieweeIndex: number,
  ) => {
    let modifiedCardData = cloneDeep(cardData);
    modifiedCardData[cardIndex]?.reviewees?.splice(revieweeIndex, 1);
    setCardData(modifiedCardData);
  };

  const onCardAddClick = () => {
    let modifiedCardData = cloneDeep(cardData);
    let cardId = uuidv4();
    modifiedCardData.splice(1, 0, {
      cardId: cardId,
      reviewer: null,
      reviewees: [],
    });
    setCardData(modifiedCardData);
    setEditingItemId(cardId);
    setTimeout(() => {
      document.getElementById(`${cardId}-Select`)?.focus();
    }, 100);
  };

  const onCardRevieweeAddClick = (cardItem: any, cardIndex: number) => {
    let revieweeId = uuidv4();
    let modifiedCardData = cloneDeep(cardData);
    modifiedCardData[cardIndex]?.reviewees?.push({
      revieweeId: revieweeId,
    });
    setCardData(modifiedCardData);
    setEditingItemId(revieweeId);
    setTimeout(() => {
      document.getElementById(`${revieweeId}-Select`)?.focus();
    }, 100);
  };

  const onReviewerSelect = (selectedOption: any, cardIndex: number) => {
    let modifiedCardData = cloneDeep(cardData);
    modifiedCardData[cardIndex]['reviewer'] = {
      ...pick(modifiedCardData[cardIndex]['reviewer'] ?? {}, ['MinSampleCnt']),
      EmployCode: selectedOption?.EmployCode,
      MasterId: props?.masterId,
      Name: selectedOption?.Name,
      UserId: selectedOption?.UserId,
    };
    setCardData(modifiedCardData);
  };

  const onRevieweeSelect = (
    selectedOption: any,
    cardIndex: number,
    revieweeIndex: number,
  ) => {
    let modifiedCardData = cloneDeep(cardData);
    modifiedCardData[cardIndex]['reviewees'][revieweeIndex] = {
      ...(modifiedCardData[cardIndex]['reviewees'][revieweeIndex] ?? {}),
      Code: selectedOption?.Code,
      MasterId: props?.masterId,
      Name: selectedOption?.Name,
      RevieweeType: props?.masterItem?.RevieweeType,
    };
    setCardData(modifiedCardData);
  };

  const onReviewerCountChange = (value: any, cardIndex: number) => {
    let modifiedCardData = cloneDeep(cardData);
    modifiedCardData[cardIndex]['reviewer']['MinSampleCnt'] = value;
    setCardData(modifiedCardData);
  };

  const onRevieweeCountChange = (
    value: any,
    cardIndex: number,
    revieweeIndex: number,
  ) => {
    let modifiedCardData = cloneDeep(cardData);
    modifiedCardData[cardIndex]['reviewees'][revieweeIndex]['MinSampleCnt'] =
      value;
    setCardData(modifiedCardData);
  };

  const { loading: latestBatchInfoLoading, run: latestBatchInfoReq } =
    useRequest(
      () => {
        let data = {};
        if (!isEmptyValues(props?.masterItem)) {
          data['MasterId'] = props?.masterItem?.MasterId;
        }

        return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetBatches', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<BatchItem[]>) => {
          if (!isEmptyValues(response?.data)) {
            let latestBatch = response?.data?.sort(sortByBatchDate)?.at(0);
            // 当存在上个月的时候 用上个月的数据
            setLatestBatchId(latestBatch?.BatchId);
            setLatestBatchInfo(latestBatch);

            let currentBatchDate = dayjs(latestBatch?.BatchDate)
              ?.add(1, 'month')
              ?.startOf('month')
              .format('YYYY-MM-DD');

            setBatchDate(currentBatchDate);

            setTimeout(() => {
              getBatchReviewValuesReq(latestBatch?.BatchId);
              getQualityExamineSysValuesReq(false);

              dmrTotalCountReq(
                dayjs(currentBatchDate)?.startOf('month').format('YYYY-MM-DD'),
                dayjs(currentBatchDate)?.endOf('month').format('YYYY-MM-DD'),
              );
            }, 0);
          } else {
            // 当上个月没数据的时候 用default数据
            getQualityExamineSysValuesReq(true);
            setBatchDate(dayjs()?.startOf('month').format('YYYY-MM-DD'));
          }
        },
      },
    );

  const { loading: getBatchReviewValuesLoading, run: getBatchReviewValuesReq } =
    useRequest(
      (batchId) => {
        return Promise.all([
          getBatchReviewers(props?.masterId, batchId),
          getBatchReviewees(props?.masterId, batchId),
          getBatchAssignments(props?.masterId, batchId),
        ]);
      },
      {
        manual: true,
        formatResult: (response: RespVO<any>[]) => {
          let reviewers = response?.at(0)?.data;
          let reviewees = response?.at(1)?.data;
          let assignments = response?.at(2)?.data;

          let cardData = [];
          if (props?.masterItem?.RevieweeType === 'UnSpec') {
            cardData = reviewers?.map((reviewerItem) => {
              return {
                cardId: uuidv4(),
                reviewer: reviewerItem,
              };
            });
          } else {
            cardData = assembleAssignmentData(
              reviewers,
              reviewees,
              assignments,
              globalState?.dictData?.Employee,
            );
          }
          cardData?.unshift({
            cardId: 'ADD',
          });
          setCardData(cardData);
        },
      },
    );

  const {
    loading: getQualityExamineSysValuesLoading,
    run: getQualityExamineSysValuesReq,
  } = useRequest(
    (withDefault: boolean) => {
      return Promise.all([
        getQualityExamineReviewers(props?.masterId),
        getQualityExamineReviewees(props?.masterId),
        getQualityExamineAssignments(props?.masterId),
      ]);
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>[]) => {
        return response;
      },
      onSuccess: (response: RespVO<any>[], params: any[]) => {
        let reviewers = response?.at(0)?.data;
        let reviewees = response?.at(1)?.data;
        let assignments = response?.at(2)?.data;

        setEmployeeData({
          reviewers: reviewers,
          reviewees: reviewees,
        });

        if (params[0] == true) {
          let detaultCardData = assembleAssignmentData(
            reviewers,
            reviewees,
            assignments,
            globalState?.dictData?.Employee,
          );
          detaultCardData?.unshift({
            cardId: 'ADD',
          });
          setCardData(detaultCardData);
        }
      },
    },
  );

  // identitySys/Users
  const { loading: getUsersLoading, run: getUsersReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/IdentitySys/GetUsers', {
        method: 'POST',
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let users = response?.data.map((i) => {
            let Employee = globalState?.dictData?.Employee?.find(
              (item) => item.Code === i?.EmployeeCode,
            );
            return { ...i, employeeItem: Employee };
          });

          console.log('users', users);

          setUserList(
            response?.data.map((i) => {
              let Employee = globalState?.dictData?.Employee?.find(
                (item) => item.Code === i?.EmployeeCode,
              );
              return { ...i, employeeItem: Employee };
            }),
          );
        } else {
          setUserList([]);
        }
      },
    },
  );

  const onEditClick = (editingId: string) => {
    if (editingId === editingItemId) {
      setEditingItemId(undefined);
    } else {
      setEditingItemId(editingId);
    }
  };

  const cardDataFilter = () => {
    if (isEmptyValues(searchKeyword?.trim())) {
      return cardData;
    }

    return cardData?.filter((cardItem) => {
      if (cardItem?.cardId === 'ADD') {
        return true;
      }

      if (cardItem?.reviewer?.EmployCode?.includes(searchKeyword)) {
        return true;
      }

      if (cardItem?.reviewer?.Name?.includes(searchKeyword)) {
        return true;
      }

      if (pinyinInitialSearch(cardItem?.reviewer?.Name, searchKeyword)) {
        return true;
      }

      let filteredReviewees = cardItem?.reviewees?.filter((revieweeItem) => {
        return (
          revieweeItem?.Name?.includes(searchKeyword) ||
          pinyinInitialSearch(revieweeItem?.Name, searchKeyword) ||
          revieweeItem?.Code?.includes(searchKeyword)
        );
      });

      if (!isEmptyValues(filteredReviewees)) {
        return true;
      }

      return false;
    });
  };

  const currentReviewers = [];
  const currentReviewees = [];

  cardData?.forEach((cardItem) => {
    if (cardItem?.reviewer) {
      currentReviewers.push(cardItem?.reviewer?.EmployCode);
    }
    currentReviewees.push(
      ...(cardItem?.reviewees?.map((item) => item?.Code) ?? []),
    );
  });

  const filteredReviewers = (employeeData?.reviewers ?? [])?.map((reviewer) => {
    return {
      ...reviewer,
      disabled: currentReviewers?.includes(reviewer?.EmployCode),
    };
  });
  const filteredReviewees = (employeeData?.reviewees ?? [])?.map((reviewee) => {
    return {
      ...reviewee,
      disabled: currentReviewees?.includes(reviewee?.Code),
    };
  });

  console.log('DataSource', filteredReviewers, filteredReviewees);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // https://docs.dndkit.com/api-documentation/sensors/pointer#activation-constraints
        distance: 1,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const onItemDragEnd = (event: DragEndEvent) => {
    let isDroppable = event?.over?.id?.toString()?.includes('Droppable');
    if (isDroppable) {
      onRevieweeItemDragEnd(event);
    } else {
      onReviewerItemDragEnd(event);
    }
  };

  const onReviewerItemDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setCardData((items) => {
        const oldIndex = items.findIndex((item) => item?.cardId === active.id);
        const newIndex = items.findIndex((item) => item?.cardId === over.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const onRevieweeItemDragEnd = (event: DragEndEvent) => {
    let droppableContainerId = event?.over?.id?.toString()?.split('#')?.at(0);
    let draggableItemId = event?.active?.id?.toString()?.split('#')?.at(0);

    if (droppableContainerId && draggableItemId) {
      let droppableCard = cardData?.find(
        (item) => item?.cardId === droppableContainerId,
      );
      let draggableCard = cardData.find(
        (cardItem) =>
          cardItem?.reviewees?.find(
            (item) => item?.revieweeId === draggableItemId,
          ) !== undefined,
      );

      if (droppableCard && draggableCard) {
        if (draggableCard?.cardId === droppableContainerId) {
          return;
        }

        let draggableRevieweeItemIndex = draggableCard?.reviewees?.findIndex(
          (item) => item?.revieweeId === draggableItemId,
        );

        if (draggableRevieweeItemIndex !== -1) {
          droppableCard?.reviewees?.push(
            draggableCard?.reviewees?.at(draggableRevieweeItemIndex),
          );
          draggableCard?.reviewees?.splice(draggableRevieweeItemIndex, 1);
        }

        setCardData([...cardData]);
      } else {
        message.warn('拖动无效，请检查');
      }
    } else {
      message.warn('拖动无效，请检查');
    }
  };

  const { loading: dmrTotalCountLoading, run: dmrTotalCountReq } = useRequest(
    (startDate, endDate) => {
      if (
        props?.masterSysItem?.ScheduleSetting?.ScheduleMode === 'Periodical'
      ) {
        if (
          !isEmptyValues(
            props?.masterSysItem?.SampleSetting?.TargetPeriodOffset,
          )
        ) {
          if (props?.masterSysItem?.SampleSetting?.TargetPeriodOffset !== 0) {
            // +- 月份
            if (!isEmptyValues(startDate)) {
              startDate = dayjs(startDate)
                .add(
                  props?.masterSysItem?.SampleSetting?.TargetPeriodOffset,
                  'month',
                )
                .startOf('month')
                .format('YYYY-MM-DD');
            }
            if (!isEmptyValues(endDate)) {
              endDate = dayjs(endDate)
                .add(
                  props?.masterSysItem?.SampleSetting?.TargetPeriodOffset,
                  'month',
                )
                .endOf('month')
                .format('YYYY-MM-DD');
            }
          }
        }
      }

      let data = {
        skipFilterSorterMiddleware: true,
        DtParam: {
          Draw: 1,
          Start: 0,
          Length: 1,
        },
        Sdate: startDate,
        Edate: endDate,
        CustomRegisterStatus: '2',

        ignoreNoContent: true,
      };

      return uniCommonService('Api/Dmr/DmrDataQuery/GetCardsV2', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableCardResp<any, any>>) => {
        setDmrTotalCount(response?.data?.recordsFiltered ?? 0);
      },
    },
  );

  let hasAssignCount = 0;
  if (props?.masterItem?.RevieweeType === 'UnSpec') {
    cardData?.forEach((cardItem) => {
      hasAssignCount += parseInt(
        cardItem?.reviewer?.MinSampleCnt?.toString() ?? '0',
      );
    });
  }

  return (
    <DndContext
      sensors={sensors}
      onDragEnd={onItemDragEnd}
      collisionDetection={closestCenter}
    >
      <div id={'batch-create-container'} className={'batch-create-container'}>
        <div className={'batch-create-header'}>
          <div className={'flex-row-center'}>
            {props?.reviewStatusData?.type !== 'ADJUST' &&
              props?.masterSysItem?.ScheduleSetting?.ScheduleMode !==
                'AdHoc' && (
                <div className={'month-selector-container'}>
                  <span className={'label'}>月份：</span>
                  <Datepicker
                    className={'selector'}
                    picker={'month'}
                    allowClear={false}
                    value={dayjs(batchDate)}
                    onChange={(date, dateString) => {
                      setBatchDate(dateString);
                      dmrTotalCountReq(
                        dayjs(dateString)
                          ?.startOf('month')
                          .format('YYYY-MM-DD'),
                        dayjs(dateString)?.endOf('month').format('YYYY-MM-DD'),
                      );
                    }}
                  />
                </div>
              )}

            {props?.reviewStatusData?.type !== 'ADJUST' &&
              props?.masterSysItem?.ScheduleSetting?.ScheduleMode ===
                'AdHoc' && (
                <div className={'month-selector-container'}>
                  <span className={'label'}>时间：</span>
                  <Datepicker.RangePicker
                    className={'selector'}
                    picker={'date'}
                    allowClear={false}
                    value={batchDateRange?.map((item) => {
                      return dayjs(item);
                    })}
                    onChange={(date, dateString) => {
                      setBatchDateRange(dateString);
                      dmrTotalCountReq(dateString?.at(0), dateString?.at(1));
                    }}
                  />
                </div>
              )}

            <div className={'flex-row-center'} style={{ marginLeft: 10 }}>
              <span className={'label'}>病案总数：</span>
              <span className={'count'}>{dmrTotalCount}份</span>
            </div>

            {props?.masterItem?.RevieweeType === 'UnSpec' && (
              <div className={'flex-row-center'} style={{ marginLeft: 10 }}>
                <span className={'label'}>已经分配了</span>
                <span className={'count'}>{hasAssignCount}份</span>
                <span className={'label'}>，还有</span>
                <span className={'count'}>
                  {Math.max(dmrTotalCount - hasAssignCount, 0)}份
                </span>
                <span className={'label'}>待分配</span>
              </div>
            )}
          </div>

          <div className={'search-container'}>
            <Input.Search
              allowClear={true}
              placeholder="请输入关键字"
              value={searchKeyword}
              onChange={(event) => {
                setSearchKeyword(event?.target?.value);
              }}
            />
          </div>
        </div>

        <Spin
          spinning={
            getUsersLoading ||
            getBatchReviewValuesLoading ||
            getQualityExamineSysValuesLoading ||
            latestBatchInfoLoading
          }
        >
          <SortableContext
            items={cardDataFilter()
              ?.filter((item) => item?.['cardId'] !== 'ADD')
              ?.map((i) => i?.cardId)}
            strategy={rectSortingStrategy}
          >
            <Row className={'card-container'} gutter={[16, 16]}>
              {cardDataFilter()?.map((cardItem, index) => {
                // TODO 改了这个 用ID 不用 index
                if (cardItem?.cardId === 'ADD') {
                  return (
                    <Col span={4}>
                      <div
                        className={'card-item-add card-item'}
                        onClick={() => {
                          onCardAddClick();
                        }}
                      >
                        <PlusOutlined />
                        <span>新增审核人</span>
                      </div>
                    </Col>
                  );
                }

                return (
                  <BatchCreateSortableContainer
                    key={cardItem?.cardId}
                    id={cardItem?.cardId}
                    cardItem={cardItem}
                    index={index}
                    onCardRevieweeAddClick={onCardRevieweeAddClick}
                    masterItem={props?.masterItem}
                  >
                    <Row className={'card-reviewer-item'}>
                      <Col span={14}>
                        <UniSelect
                          id={`${cardItem?.cardId}-Select`}
                          disabled={editingItemId !== cardItem?.cardId}
                          className={'employee-selector'}
                          placeholder="请选择"
                          showSearch
                          value={cardItem?.reviewer?.EmployCode}
                          allowClear={false}
                          dataSource={filteredReviewers ?? []}
                          optionValueKey={'EmployCode'}
                          optionNameKey={'Name'}
                          onSelect={(value, option) => {
                            onReviewerSelect(option, index);
                          }}
                          getPopupContainer={(triggerNode) => {
                            return document.getElementById(
                              'batch-create-container',
                            );
                          }}
                        />
                      </Col>
                      <Col span={6}>
                        <RestrictInputNumber
                          id={`${cardItem?.cardId}-SampleCnt`}
                          disabled={editingItemId !== cardItem?.cardId}
                          value={cardItem?.reviewer?.MinSampleCnt ?? undefined}
                          setValue={(value) => {
                            onReviewerCountChange(value, index);
                          }}
                        />
                      </Col>
                      <Col span={4} className={'operation-item-container'}>
                        <div className={'item'}>
                          <EditTwoTone
                            {...operationItemProps}
                            onClick={() => {
                              onEditClick(cardItem?.cardId);
                            }}
                          />
                        </div>
                        <div className={'item'}>
                          <Popconfirm
                            title="确认删除这一项吗？"
                            onConfirm={() => {
                              onCardDeleteClick(cardItem, index);
                            }}
                          >
                            <CloseOutlined {...operationItemProps} />
                          </Popconfirm>
                        </div>
                      </Col>
                    </Row>
                    <DroppableContainer id={`${cardItem?.cardId}#Droppable`}>
                      {cardItem?.reviewees?.map(
                        (revieweeItem, revieweeIndex: number) => {
                          return (
                            <DraggableContainer
                              id={`${revieweeItem?.revieweeId}#Draggable`}
                            >
                              <Col span={12}>
                                <UniSelect
                                  id={`${revieweeItem?.revieweeId}-Select`}
                                  disabled={
                                    editingItemId !== revieweeItem?.revieweeId
                                  }
                                  className={'employee-selector'}
                                  placeholder="请选择"
                                  showSearch
                                  value={revieweeItem?.Code}
                                  allowClear={false}
                                  // dataSource={globalState?.dictData?.Employee}
                                  dataSource={filteredReviewees ?? []}
                                  optionValueKey={'Code'}
                                  optionNameKey={'Name'}
                                  onSelect={(value, option) => {
                                    onRevieweeSelect(
                                      option,
                                      index,
                                      revieweeIndex,
                                    );
                                  }}
                                  getPopupContainer={(triggerNode) => {
                                    return document.getElementById(
                                      'batch-create-container',
                                    );
                                  }}
                                />
                              </Col>
                              <Col span={6}>
                                {props?.masterItem?.RevieweeType !==
                                  'UnSpec' && (
                                  <RestrictInputNumber
                                    id={`${revieweeItem?.revieweeId}-SampleCnt`}
                                    disabled={
                                      editingItemId !== revieweeItem?.revieweeId
                                    }
                                    value={
                                      revieweeItem?.MinSampleCnt ?? undefined
                                    }
                                    setValue={(value) => {
                                      onRevieweeCountChange(
                                        value,
                                        index,
                                        revieweeIndex,
                                      );
                                    }}
                                  />
                                )}
                              </Col>
                              <Col
                                span={4}
                                className={'operation-item-container'}
                              >
                                <div className={'item'}>
                                  <EditTwoTone
                                    {...operationItemProps}
                                    onClick={() => {
                                      onEditClick(revieweeItem?.revieweeId);
                                    }}
                                  />
                                </div>
                                <div className={'item'}>
                                  <CloseOutlined
                                    {...operationItemProps}
                                    onClick={() => {
                                      onCardRevieweeDeleteClick(
                                        cardItem,
                                        revieweeItem,
                                        index,
                                        revieweeIndex,
                                      );
                                    }}
                                  />
                                </div>
                              </Col>
                            </DraggableContainer>
                          );
                        },
                      )}
                    </DroppableContainer>
                  </BatchCreateSortableContainer>
                );
              })}
            </Row>
          </SortableContext>
        </Spin>
      </div>
    </DndContext>
  );
};

interface DmrReviewBatchCreateWithEmployeeModalProps {
  batchCreateContainerRef?: any;
  batchId?: number;
  onBatchCreateSuccess?: (batchInfo: any) => void;
  batchInfo?: BatchItem;
}

const DmrReviewBatchCreateWithEmployeeModal = (
  props: DmrReviewBatchCreateWithEmployeeModalProps,
) => {
  const [reviewCreateOpen, setReviewCreateOpen] = useState(false);

  const batchAssignContainerRef = useRef(null);

  const [masterItem, setMasterItem] = useState<BatchMasterItem>(null);
  const [masterSysItem, setMasterSysItem] = useState<BatchMasterSysItem>(null);

  const { examineMasterId } = useRouteProps();

  const [createLoading, setCreateLoading] = useState(false);

  const [reviewStatusData, setReviewStatusData] = useState<any>({});

  React.useImperativeHandle(props?.batchCreateContainerRef, () => {
    return {
      setReviewCreateStatus: async (data: any) => {
        if (data?.status === true) {
          setReviewStatusData(data);

          if (!isEmptyValues(examineMasterId)) {
            await getBatchMasterItemReq(examineMasterId);
          } else if (!isEmptyValues(props?.batchInfo?.MasterId)) {
            // 换一个masterItem出来
            await getBatchMasterItemReq(props?.batchInfo?.MasterId);
          } else {
            let mastersResponse: RespVO<BatchMasterItem[]> =
              await getBatchMasterReq();
            if (mastersResponse?.data?.at(0)?.MasterId) {
              await getBatchMasterItemReq(
                mastersResponse?.data?.at(0)?.MasterId,
              );
            }
          }
        }
        setReviewCreateOpen(data?.status);
      },
    };
  });

  const { loading: getBatchMasterLoading, run: getBatchMasterReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Sys/QualityExamineSys/GetQualityExamineSettingMasters',
        {
          method: 'GET',
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
    },
  );

  const { loading: getBatchMasterItemLoading, run: getBatchMasterItemReq } =
    useRequest(
      (masterId: string | number) => {
        return uniCommonService(
          'Api/Sys/QualityExamineSys/GetQualityExamineSys',
          {
            method: 'GET',
            params: {
              MasterId: masterId,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<BatchMasterSysItem>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setMasterItem(response?.data?.Master);
            setMasterSysItem(response?.data);
          } else {
            setMasterItem(undefined);
            setMasterSysItem(undefined);
          }
        },
      },
    );

  const createBatchAndAssignBatch = async () => {
    setCreateLoading(true);
    const batchDate = batchAssignContainerRef?.current?.batchDate;
    const cardData = batchAssignContainerRef?.current?.cardData;
    const batchDateRange = batchAssignContainerRef?.current?.batchDateRange;

    if (isEmptyValues(masterItem)) {
      return null;
    }

    const masterId = props?.batchInfo?.MasterId ?? masterItem?.MasterId;

    let createBatchResponse: RespVO<BatchItem> = null;
    if (
      masterSysItem?.ScheduleSetting?.ScheduleMode !== 'AdHoc' &&
      reviewStatusData?.type !== 'ADJUST'
    ) {
      createBatchResponse = await createBatch(masterId, batchDate);
    }

    if (
      masterSysItem?.ScheduleSetting?.ScheduleMode === 'AdHoc' &&
      reviewStatusData?.type !== 'ADJUST'
    ) {
      if (batchDateRange?.length !== 2) {
        message.error('请选择时间');
        return {
          errorCode: 400,
        };
      }

      createBatchResponse = await createBatchWithDates(
        masterId,
        batchDateRange?.at(0),
        batchDateRange?.at(1),
      );
    }

    let batchId = null;
    let batchInfo = null;

    if (
      createBatchResponse?.code === 0 &&
      createBatchResponse?.statusCode === 200
    ) {
      batchId = createBatchResponse?.data?.BatchId;
      batchInfo = createBatchResponse?.data;
    }

    if (reviewStatusData?.type === 'ADJUST') {
      batchId = reviewStatusData?.batchId;
      batchInfo = reviewStatusData?.batchInfo;
    }

    if (!isEmptyValues(batchId) && !isEmptyValues(batchInfo)) {
      let { reviewers, reviewees, assignments } =
        createReviewersRevieweesAssignmentsDataProcess(
          cardData?.filter((item) => item.cardId !== 'ADD'),
          masterItem,
        );

      await Promise.allSettled([
        createBatchReviewers(batchInfo?.MasterId, batchId, reviewers),
        createBatchReviewees(batchInfo?.MasterId, batchId, reviewees),
        createBatchAssignments(batchInfo?.MasterId, batchId, assignments),
      ]);
    }

    if (!isEmptyValues(batchId)) {
      let assignBatchResponse: RespVO<any> = await assignBatch(
        masterId,
        batchId,
      );
    }

    return batchInfo;
  };

  return (
    <Modal
      width={1600}
      className={`review-task-modal-employee-container`}
      title={reviewStatusData?.title ?? `新建评审计划`}
      open={reviewCreateOpen}
      confirmLoading={createLoading}
      okText={reviewStatusData?.okText ?? '新建'}
      onOk={async () => {
        let batchInfo = await createBatchAndAssignBatch();

        setCreateLoading(false);

        if (isEmptyValues(batchInfo)) {
          message.error(reviewStatusData?.errorText ?? '创建计划错误，请检查');
        } else {
          if (isEmptyValues(batchInfo?.errorCode)) {
            props?.onBatchCreateSuccess &&
              props?.onBatchCreateSuccess(batchInfo);
          }
        }
      }}
      onCancel={() => {
        setReviewCreateOpen(false);
      }}
      destroyOnClose={true}
      getContainer={document.getElementById('reviewer-container')}
    >
      <Spin spinning={createLoading}>
        <DmrReviewBatchCreateWithEmployee
          reviewStatusData={reviewStatusData}
          createOpenStatus={reviewCreateOpen}
          containerRef={batchAssignContainerRef}
          masterId={props?.batchInfo?.MasterId ?? masterItem?.MasterId}
          batchId={props?.batchId}
          batchInfo={props?.batchInfo}
          masterItem={masterItem}
          masterSysItem={masterSysItem}
        />
      </Spin>
    </Modal>
  );
};

export default DmrReviewBatchCreateWithEmployeeModal;
