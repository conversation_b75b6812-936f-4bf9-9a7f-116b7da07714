import './index.less';
import React, { useEffect } from 'react';
import { ProForm, ProFormDateRangePicker, ProFormSelect } from '../pro-form';
import dayjs from 'dayjs';
import { DatePicker, Form, FormInstance, Input, InputNumber } from 'antd';
import { UniSelect } from '../index';
import Datepicker from '../picker/datepicker';
import { isEmptyValues } from '@uni/utils/src/utils';
import { specialDataRangeItemsMap } from './special-items';
import moment from 'moment';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';

interface DateRangeWithDateProps {
  overrideContainerClassName?: string;
  selectorOptions?: [
    {
      label: string;
      value: string;
    },
  ];

  initialValues?: {
    dateRange?: string[];
    DateType?: string;
    DateFormatType?: string;
  };
  form?: FormInstance;
  needFormWrapper?: boolean;
  customWrapper?: any;
  customWrapperProps?: any;

  enableDateFormatTypeSelector?: boolean;
  useContextFormInstance?: boolean;
  initializeDefaultOption?: boolean;
  onTypeChangeExtra?: (value: string) => void;
  // 自定义options（在医生首页质控明细，需要）
  customDefaultSelectorOptions?: [
    {
      label: string;
      value: string;
    },
  ];
  // 禁用第一个Select（在医生首页质控明细，需要）
  hiddenThisComponent?: boolean;
}

const defaultSelectorOptions = [
  { label: '按出院时间', value: 'OutDate' },
  { label: '按登记时间', value: 'RegisterDate' },
];

const dateFormatTypeOptions = [
  { label: '按年', value: 'year' },
  { label: '按季', value: 'quarter' },
  { label: '按月', value: 'month' },
  { label: '按日', value: 'date' },
];

const specialFormatTypeOptions = [{ label: '单年', value: 'singleYear' }];

export const EmptyWrapper = (props: any) => {
  return <>{props?.children}</>;
};

export const transformDateRangeValue = (dateRange: string[]) => {
  if (dateRange?.length) {
    let momentDateRange = [];
    if (!isEmptyValues(dateRange?.at(0))) {
      momentDateRange[0] = moment(dateRange?.at(0));
    }

    if (!isEmptyValues(dateRange?.at(1))) {
      momentDateRange[1] = moment(dateRange?.at(1));
    }
    return momentDateRange;
  } else {
    return dateRange;
  }
};

export const dateRangeValueProcessor = (
  values: any,
  enableDateFormatTypeSelector: boolean = false,
  forceSimpleDate: boolean = false,
) => {
  if (isEmptyValues(values?.['dateRange'])) {
    return {};
  }

  if (
    values?.['dateRange']?.filter((value) => !isEmptyValues(value))?.length !==
    2
  ) {
    return {};
  }

  let dateType = values?.['DateType'];

  let startDate = values?.['dateRange']?.[0];
  let endDate = values?.['dateRange']?.[1];

  // 如果有 enableDateFormatTypeSelector = true 要重新计算了..
  if (enableDateFormatTypeSelector) {
    let result = dateFormatTypeProcessor(
      startDate,
      endDate,
      values?.DateFormatType,
    );
    startDate = result?.startDate;
    endDate = result?.endDate;
  }

  if (forceSimpleDate === true) {
    return {
      Sdate: dayjs(startDate)?.format('YYYY-MM-DD'),
      Edate: dayjs(endDate)?.format('YYYY-MM-DD'),
    };
  }

  if (dateType == 'OutDate') {
    return {
      Sdate: dayjs(startDate)?.format('YYYY-MM-DD'),
      Edate: dayjs(endDate)?.format('YYYY-MM-DD'),
    };
  }

  if (dateType === 'RegisterDate') {
    return {
      CodeSdate: dayjs(startDate)?.format('YYYY-MM-DD'),
      CodeEdate: dayjs(endDate)?.format('YYYY-MM-DD'),
    };
  }
};

const dateFormatTypeProcessor = (
  startDate: any,
  endDate: any,
  dateFormatType: string,
) => {
  switch (dateFormatType) {
    case 'year':
      return {
        startDate: dayjs(startDate).startOf('year').format('YYYY-MM-DD'),
        endDate: dayjs(endDate).endOf('year').format('YYYY-MM-DD'),
      };
    case 'quarter':
      return {
        startDate: dayjs(startDate)
          .startOf('quarter' as any)
          .format('YYYY-MM-DD'),
        endDate: dayjs(endDate)
          .endOf('quarter' as any)
          .format('YYYY-MM-DD'),
      };
    case 'month':
      return {
        startDate: dayjs(startDate).startOf('month').format('YYYY-MM-DD'),
        endDate: dayjs(endDate).endOf('month').format('YYYY-MM-DD'),
      };
    case 'date':
      return {
        startDate: dayjs(startDate).format('YYYY-MM-DD'),
        endDate: dayjs(endDate).format('YYYY-MM-DD'),
      };
    default:
      return {
        startDate: dayjs(startDate).format('YYYY-MM-DD'),
        endDate: dayjs(endDate).format('YYYY-MM-DD'),
      };
  }
};

const dateStringToMomentItem = (values: string[]) => {
  let momentItems = [];
  if (!isEmptyValues(values?.at(0))) {
    momentItems[0] = moment(values.at(0));
  } else {
    momentItems[0] = null;
  }

  if (!isEmptyValues(values?.at(1))) {
    momentItems[1] = moment(values.at(1));
  } else {
    momentItems[1] = null;
  }

  return momentItems;
};

const DateRangeWithType = (props: DateRangeWithDateProps) => {
  const [dateRangeForm] = Form.useForm();

  let contextForm = Form.useFormInstance();

  const formInstance = props?.form ?? contextForm ?? dateRangeForm;

  let DateRangeContainerWrapper = props?.customWrapper
    ? props?.customWrapper
    : Form;
  let dateFormatType = undefined;

  if (props?.useContextFormInstance !== true) {
    contextForm = null;
  }

  if (props?.needFormWrapper === false) {
    DateRangeContainerWrapper = EmptyWrapper;
  }

  if (props?.enableDateFormatTypeSelector === true) {
    dateFormatType = Form.useWatch('DateFormatType', formInstance);

    if (dateFormatType === 'singleYear') {
      let singleYearRange = formInstance.getFieldValue('singleYearRange');
      let singleYearValue = formInstance.getFieldValue('singleYearValue');

      if (isEmptyValues(singleYearValue)) {
        singleYearValue = dayjs().year();
        formInstance.setFieldValue('singleYearValue', singleYearValue);
      }

      if (isEmptyValues(singleYearRange)) {
        let singleYearRangeItem = specialDataRangeItemsMap.find(
          (item) => item.value === 'All',
        );
        singleYearRange = singleYearRangeItem.value;
        formInstance.setFieldValue('singleYearRange', singleYearRange);
      }

      let yearRangeItem = specialDataRangeItemsMap.find(
        (item) => item.value === singleYearRange,
      );
      let rangeValue = yearRangeItem.getter(singleYearValue);
      formInstance.setFieldValue(
        'dateRange',
        dateStringToMomentItem(rangeValue),
      );
    }
  }

  useEffect(() => {
    if (contextForm !== null) {
      if (props?.initializeDefaultOption === true) {
        contextForm?.setFieldValue('DateType', 'OutDate');
        contextForm?.setFieldValue('DateFormatType', 'date');
      } else {
        // 病案首页质量监控  质控明细页面  初始化date值
        // 医生质量详情页面，初始化默认值
        if (props?.initialValues) {
          const { dateRange, DateFormatType, DateType } =
            props.initialValues || {};
          contextForm?.setFieldValue('DateType', DateType ?? 'OutDate');
          contextForm?.setFieldValue(
            'DateFormatType',
            DateFormatType ?? 'date',
          );
          if (!isEmptyValues(dateRange)) {
            const momentDateRange = transformDateRangeValue(dateRange);

            contextForm?.setFieldValue('dateRange', momentDateRange);
          }
        }
      }
      // if (props?.initialValues && props.hiddenThisComponent) {
      //   const { dateRange, DateFormatType, DateType } =
      //     props.initialValues || {};
      //   contextForm?.setFieldValue('DateFormatType', DateFormatType ?? 'date');
      //   if (!isEmptyValues(dateRange)) {
      //     const momentDateRange = transformDateRangeValue(dateRange);
      //     contextForm?.setFieldValue('dateRange', momentDateRange);
      //   }
      // }
    }
  }, []);

  return (
    <DateRangeContainerWrapper
      preserve={false}
      form={formInstance}
      initialValues={
        props?.initialValues ?? {
          DateType: 'OutDate',
          DateFormatType: 'date',
        }
      }
      {...props?.customWrapperProps}
    >
      <div
        className={`${
          props?.overrideContainerClassName ?? 'date-range-with-type-container'
        }`}
      >
        <Form.Item name="singleYearValue" hidden={true} />
        <Form.Item name="singleYearRange" hidden={true} />
        <Form.Item name="dateRange" hidden={true} />

        {props?.hiddenThisComponent ? null : (
          <Form.Item label="" name="DateType">
            <UniSelect
              showSearch={false}
              allowClear={false}
              dataSource={
                props?.customDefaultSelectorOptions?.length
                  ? props?.customDefaultSelectorOptions
                  : [
                      ...defaultSelectorOptions,
                      ...(props?.selectorOptions ?? []),
                    ]
              }
            />
          </Form.Item>
        )}

        {props?.enableDateFormatTypeSelector === true && (
          <Form.Item label="" name="DateFormatType">
            <UniSelect
              style={{ marginLeft: 10 }}
              showSearch={false}
              allowClear={false}
              dataSource={dateFormatTypeOptions.concat(
                specialFormatTypeOptions,
              )}
              onChange={(value, option) => {
                let dateFormatType =
                  formInstance.getFieldValue('DateFormatType');
                if (dateFormatType === 'singleYear' && value !== 'singleYear') {
                  // 重置一下dateRange
                  formInstance.setFieldValue('dateRange', [null, null]);
                }
                props?.onTypeChangeExtra && props?.onTypeChangeExtra(value);
              }}
            />
          </Form.Item>
        )}

        {props?.enableDateFormatTypeSelector === true &&
          dateFormatType === 'singleYear' && (
            <div className={'single-year-range-container'}>
              <Form.Item label="年份" name="singleYearValue">
                <Input
                  className={'no-arrows'}
                  style={{ width: 80 }}
                  min={1900}
                  max={2100}
                  step={1}
                  type={'number'}
                  onChange={(event) => {
                    let value = event?.target?.value;
                    formInstance.setFieldValue('singleYearValue', value);
                    let singleYearRange =
                      formInstance.getFieldValue('singleYearRange');
                    if (!isEmptyValues(value)) {
                      if (!isEmptyValues(singleYearRange)) {
                        let yearRangeItem = specialDataRangeItemsMap.find(
                          (item) => item.value === singleYearRange,
                        );
                        let rangeValue = yearRangeItem.getter(value);
                        formInstance.setFieldValue(
                          'dateRange',
                          dateStringToMomentItem(rangeValue),
                        );
                      }
                    } else {
                      formInstance.setFieldValue('dateRange', [null, null]);
                    }
                  }}
                />
              </Form.Item>

              <Form.Item
                label="时段"
                name="singleYearRange"
                style={{ marginLeft: 10 }}
              >
                <UniSelect
                  style={{ width: 100 }}
                  showSearch={false}
                  allowClear={false}
                  dataSource={specialDataRangeItemsMap}
                  listHeight={1600}
                  onFilterOptionsCapture={(inputValue, option) => {
                    return (
                      option?.label
                        ?.toLowerCase()
                        ?.toString()
                        .includes(inputValue) ||
                      option?.value
                        ?.toLowerCase()
                        ?.toString()
                        ?.includes(inputValue) ||
                      pinyinInitialSearch(
                        option?.label?.toString()?.toLowerCase(),
                        inputValue.toLowerCase(),
                      )
                    );
                  }}
                  onChange={(value, option) => {
                    formInstance.setFieldValue('singleYearRange', value);
                    let singleYearValue =
                      formInstance.getFieldValue('singleYearValue');
                    if (!isEmptyValues(singleYearValue)) {
                      let rangeValue = option.getter(singleYearValue);
                      formInstance.setFieldValue(
                        'dateRange',
                        dateStringToMomentItem(rangeValue),
                      );
                    } else {
                      formInstance.setFieldValue('dateRange', [null, null]);
                    }
                  }}
                />
              </Form.Item>
            </div>
          )}
        {props?.enableDateFormatTypeSelector === true &&
          dateFormatType !== 'singleYear' && (
            <Form.Item name={'dateRange'} style={{ marginLeft: 10 }}>
              <DatePicker.RangePicker
                picker={dateFormatType ?? 'date'}
                format={
                  props?.enableDateFormatTypeSelector === true
                    ? undefined
                    : 'YYYY-MM-DD'
                }
              />
            </Form.Item>
          )}
      </div>
    </DateRangeContainerWrapper>
  );
};

export default DateRangeWithType;
