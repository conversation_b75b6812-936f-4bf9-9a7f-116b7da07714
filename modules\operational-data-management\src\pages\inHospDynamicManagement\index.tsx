import { FC } from 'react';
import { InHospDynamicManagementColumns } from './columns';
import { InHospDynamicManagementConstants } from './constants';
import EditByDept from '../inHospDynamicRegistration/editByDept/index';
import DynamicManagement from '../../components/DynamicManagement';

const InHospDynamicManagement: FC = () => {
  return (
    <DynamicManagement
      pageTitle="住院动态管理"
      apiBasePath="DeptInpatientAmtMgmt"
      detailBtnEventKey={InHospDynamicManagementConstants.DETAIL_BTN_CLK}
      columns={InHospDynamicManagementColumns}
      id="odm_in_hosp_dynamic_management"
      EditByDeptComponent={EditByDept}
    />
  );
};

export default InHospDynamicManagement;
