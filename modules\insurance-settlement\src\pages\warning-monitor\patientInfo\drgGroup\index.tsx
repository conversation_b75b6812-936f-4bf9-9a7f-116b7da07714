import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { useAsyncEffect, useDeepCompareEffect, useSafeState } from 'ahooks';
import {
  Button,
  Card,
  Col,
  Collapse,
  Descriptions,
  Divider,
  Row,
  Space,
  Tag,
  Tooltip,
  Typography,
  message,
  Spin,
} from 'antd';
import { useEffect } from 'react';
import { useRequest } from 'umi';
import { ISwaggerCheckedData } from './interface';
import './index.less';
import { CHS_ADJUST_DRGS_LABEL, EventConstants } from '../constant';
import {
  AlertOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  PayCircleOutlined,
} from '@ant-design/icons';
import _ from 'lodash';
import { Emitter } from '@uni/utils/src/emitter';
import Modal from 'antd/lib/modal';
import GroupDetail from './components/groupDetail';
import { processExpression, processObjectNumberData } from '@/utils/utils';

function calculatePercentagePosition(value, minValue, maxValue) {
  const percentagePosition = ((value - minValue) / (maxValue - minValue)) * 100;
  return parseFloat(percentagePosition.toFixed(2));
}

const ExpressionConst = [
  {
    label: '支付参考',
    mark: '=',
    valueKey: 'OriginalFee',
  },
];

export interface IDrgGroupContainerProps {
  checkReqData?: any;
  needFetch?: any;
}

const DrgGroupContainer = ({
  checkReqData,
  needFetch,
}: IDrgGroupContainerProps) => {
  const [checkedData, setCheckedData] =
    useSafeState<ISwaggerCheckedData>(undefined);

  const [chsResult, setChsResult] = useSafeState(undefined);
  const [adjustDrgsResult, setAdjustDrgsResult] = useSafeState(undefined);
  const [drgsResult, setDrgsResult] = useSafeState(undefined);

  const [finalExpression, setFinalExpression] = useSafeState({
    firstLine: [],
    secondLine: [],
  });

  const [feeInfo, setFeeInfo] = useSafeState({
    LowerTimes: 0,
    HigherTime: 0,
    OriginalFee: 0,
    TotalFee: 0,
    TooLowBoundFee: 0,
    TooHighBoundFee: 0,
  });

  // 预入组
  const { loading: checkReqLoading, run: checkReq } = useRequest(
    (data) => {
      return uniCommonService(`Api/Emr/EmrCardBundle/Check`, {
        method: 'POST',
        data,
      });
    },
    {
      manual: true,
      formatResult: (res: RespVO<ISwaggerCheckedData>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          console.log('Check', res);
          let { ChsMetricsResult } = res?.data;
          if (ChsMetricsResult?.ErrMsg) {
            setCheckedData(res?.data);
            Modal.warning({
              title: '提示',
              content: ChsMetricsResult?.ErrMsg,
            });
          } else {
            Emitter.emit(
              EventConstants?.DRGS_RESULT_TO_GROUPFEETYPE,
              res?.data,
            );
            setCheckedData(res?.data);
            // 处理 chsResult
            if (ChsMetricsResult?.ChsDrgsResult?.length > 0) {
              setChsResult(
                processObjectNumberData(
                  ChsMetricsResult?.ChsDrgsResult?.at(0),
                  ['BaseCwPoint'],
                  ['CwPoint', 'CwRate', 'TooHighOrLowRatio'],
                ),
              );
            } else {
              setChsResult({});
            }

            setAdjustDrgsResult(ChsMetricsResult?.AdjustDrgsResult?.at(0));
            setDrgsResult(res?.data?.DrgsMetricsResult);

            setFeeInfo({
              LowerTimes: undefined,
              HigherTime: undefined,
              // 算法再这里
              OriginalFee: ChsMetricsResult?.ChsDrgsResult?.at(0)
                ?.PointBasedMethod
                ? ChsMetricsResult?.ChsDrgsResult?.at(0)?.CwPoint *
                  ChsMetricsResult?.ChsDrgsResult?.at(0)?.CwRate
                : ChsMetricsResult?.ChsDrgsResult?.at(0)?.Cw *
                  ChsMetricsResult?.ChsDrgsResult?.at(0)?.CwRate,
              TotalFee: ChsMetricsResult?.ChsDrgsResult?.at(0)?.TotalFee,
              TooLowBoundFee:
                ChsMetricsResult?.ChsDrgsResult?.at(0)?.TooLowBoundFee,
              TooHighBoundFee:
                ChsMetricsResult?.ChsDrgsResult?.at(0)?.TooHighBoundFee,
            });
            let result = processExpression(
              ChsMetricsResult?.ChsDrgsResult?.at(0)?.SettleFormula,
            );
            setFinalExpression(result);
          }

          return res?.data;
        }
        return undefined;
      },
    },
  );

  useAsyncEffect(async () => {
    if (checkReqData && !checkedData) {
      // 这个是首次进来 / 还原预入组 的时候调用的 等这个调用好后，根据其中的内容结合 Applicable 去判断外部的图片标签要怎么打
      let result = await checkReq(checkReqData);
      console.log('checkReq', result);
      if (result) {
        Emitter.emit(EventConstants.DRGS_RESULT_TO_CARDINFO, result);
      }
    }
  }, [checkReqData, checkedData]);

  // 当前费用位置
  const TotalFeePos = (data) => {
    // 计算总费用Icon位置
    let iconPos =
      data?.TooLowBoundFee && data?.TooHighBoundFee
        ? calculatePercentagePosition(
            data?.TotalFee,
            data?.TooLowBoundFee,
            data?.TooHighBoundFee,
          )
        : 100;
    return iconPos;
  };

  // 支付参考位置
  const CwValuePos = (data) => {
    let iconPos =
      data?.TooLowBoundFee && data?.TooHighBoundFee
        ? calculatePercentagePosition(
            data?.OriginalFee,
            data?.TooLowBoundFee,
            data?.TooHighBoundFee,
          )
        : -1;
    return iconPos;
  };

  useEffect(() => {
    Emitter.on(EventConstants.TEST_GROUP_BTN_CLK, () => {
      checkReq(checkReqData);
    });

    Emitter.on(EventConstants.RESET_GROUP_BTN_CLK, () => {
      setCheckedData(undefined);
      Emitter.emit(EventConstants.RESET_DRGS_RESULT);
    });

    return () => {
      Emitter.off(EventConstants.TEST_GROUP_BTN_CLK);
      Emitter.off(EventConstants.RESET_GROUP_BTN_CLK);
    };
  }, [checkReqData]);

  return (
    <>
      {/* <Col span={24}>
        <Card title="审核结果">
          <Descriptions size="small" column={2}>
            <Descriptions.Item label="诊断">{''}</Descriptions.Item>
            <Descriptions.Item label="手术">{''}</Descriptions.Item>
          </Descriptions>
        </Card>
      </Col> */}

      <Col span={24}>
        <Spin spinning={checkReqLoading}>
          {/* <Card
            title="预分组"
            className="drg_card"
            extra={
              <Space>
                <Button
                  style={{ backgroundColor: '#ff6e00', color: '#fff' }}
                  onClick={(e) => {
                    checkReq(checkReqData);
                  }}
                >
                  测试预入组
                </Button>
                <Button
                  style={{ backgroundColor: '#000', color: '#fff' }}
                  onClick={(e) => {
                    setCheckedData(undefined);
                    Emitter.emit(EventConstants.RESET_DRGS_RESULT);
                  }}
                >
                  还原预入组
                </Button>
              </Space>
            }
          >
            <Collapse defaultActiveKey={['1']}>
              <Collapse.Panel
                header="符合组"
                key="1"
                className="chs_result_container"
              >
                <Row gutter={[16, 16]} style={{ flexWrap: 'nowrap' }}>
                  <Col flex={'300px'}>
                    <Card className="drg_card_container">
                      <div className="drg_card_title">
                        <span>{chsResult?.ADrgCode}</span>
                        <Tag
                          color={
                            chsResult?.AbnFeeType === '正常倍率'
                              ? 'green'
                              : chsResult?.AbnFeeType === '低倍率'
                              ? 'gold'
                              : chsResult?.AbnFeeType === '高倍率'
                              ? 'red'
                              : ''
                          }
                        >
                          {chsResult?.AbnFeeType}
                        </Tag>
                      </div>
                      <Tooltip title={chsResult?.ADrgName}>
                        <Typography.Paragraph
                          className="drg_name"
                          ellipsis={{ rows: 2, expandable: false }}
                        >
                          {chsResult?.ADrgName}
                        </Typography.Paragraph>
                      </Tooltip>
                      <div className="drg_type">
                        <span className={`ADrgTypeName`}>
                          {chsResult?.ADrgTypeName}
                        </span>
                      </div>
                      <div className="drg_icon_fee_container">
                        <Tooltip title="住院天数">
                          <Space>
                            <ClockCircleOutlined />
                            {chsResult?.AvgInPeriod}
                          </Space>
                        </Tooltip>
                        <Tooltip title="地区均费">
                          <Space>
                            <PayCircleOutlined />
                            {chsResult?.AvgFee}
                          </Space>
                        </Tooltip>
                        <Tooltip
                          title={chsResult?.PointBasedMethod ? '点值' : '权重'}
                        >
                          <Space>
                            <AlertOutlined />
                            {chsResult?.PointBasedMethod
                              ? checkedData?.ChsMetricsResult?.ChsDrgsResult?.at(
                                  0,
                                )?.CwPoint
                              : checkedData?.ChsMetricsResult?.ChsDrgsResult?.at(
                                  0,
                                )?.Cw}
                          </Space>
                        </Tooltip>
                      </div>
                      <Divider
                        type="horizontal"
                        style={{ margin: '0.5em 0' }}
                      />
                      <div className="drg_fee_container">
                        <div>
                          <span>当前花费:</span>
                          <span>{chsResult?.TotalFee}</span>
                        </div>
                        <div>
                          <span>支付参考:</span>
                          <span>{feeInfo?.OriginalFee?.toFixed(2)}</span>
                        </div>
                        <div
                          style={{
                            color:
                              feeInfo?.OriginalFee - chsResult?.TotalFee > 0
                                ? '#18ba56'
                                : '#eb5757',
                          }}
                        >
                          <span>
                            {feeInfo?.OriginalFee - chsResult?.TotalFee > 0
                              ? '结余'
                              : '超支'}
                            :
                          </span>
                          <span>
                            {(
                              feeInfo?.OriginalFee - chsResult?.TotalFee
                            )?.toFixed(2)}
                          </span>
                        </div>
                      </div>
                      <div className="drg_extra"></div>
                    </Card>
                  </Col>
                  <Col flex={'auto'}>
                  
                    <div className="fee_group_container">
                      <div className="drg_fee_header">
                        <span className="low_header">
                          低倍率{feeInfo?.TooLowBoundFee}
                        </span>
                        <span className="high_header">
                          高倍率{feeInfo?.TooHighBoundFee}
                        </span>
                      </div>
                      <div className="drg_fee_group">
                        <div className="fee_group_Yellow"></div>
                        <div className="fee_group_Green">
                          <Tooltip title={feeInfo?.TotalFee}>
                            <div
                              className="fee_group_total_fee"
                              style={{
                                left: `${
                                  _.isNumber(TotalFeePos(feeInfo))
                                    ? TotalFeePos(feeInfo) <= 0
                                      ? '-7.5%'
                                      : TotalFeePos(feeInfo) >= 100
                                      ? '107.5%'
                                      : `${TotalFeePos(feeInfo)}%`
                                    : '50%'
                                }`,
                                backgroundColor:
                                  TotalFeePos(feeInfo) <= 0
                                    ? '#ffc300'
                                    : TotalFeePos(feeInfo) >= 100
                                    ? '#eb5757'
                                    : '#18ba56',
                              }}
                            ></div>
                          </Tooltip>
                        </div>
                        <div className="fee_group_Red"></div>
                        <Tooltip title={feeInfo?.OriginalFee?.toFixed(2)}>
                          <div
                            className="fee_group_CwValue"
                            style={{
                              left: `${
                                CwValuePos(feeInfo) > -1
                                  ? `${CwValuePos(feeInfo)}%`
                                  : '37%'
                              }`,
                            }}
                          ></div>
                        </Tooltip>
                      </div>
                      <div className="drg_fee_footer">
                        <span style={{ left: '0%' }}>0</span>
                        <span style={{ left: '15%' }}>
                          {feeInfo?.LowerTimes}
                        </span>
                        <span
                          className="fee_footer_cwValue"
                          style={{
                            left: `${
                              CwValuePos(feeInfo) > -1
                                ? `${CwValuePos(feeInfo)}%`
                                : '37%'
                            }`,
                          }}
                        >
                          支付参考
                        </span>
                        <span
                          style={{
                            right: '15%',
                            transform: 'translate(50%, 0)',
                          }}
                        >
                          {feeInfo?.HigherTime}
                        </span>
                        <span
                          style={{
                            right: '0%',
                            transform: 'translate(50%, 0)',
                          }}
                        >
                          ∞
                        </span>
                      </div>
                    </div>
                  </Col>
                </Row>
              </Collapse.Panel>
            </Collapse>
          </Card> */}
          <GroupDetail
            metricSettlesData={chsResult}
            finalExpression={finalExpression}
          />
        </Spin>
      </Col>
    </>
  );
};

export default DrgGroupContainer;
