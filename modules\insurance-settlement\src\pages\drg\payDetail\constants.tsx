import {
  ApiFilled,
  DownCircleTwoTone,
  UpCircleFilled,
  UpCircleTwoTone,
} from '@ant-design/icons';

export const PayDetailSummaryItems = [
  {
    label: '全部病例',
    dataIndex: 'TotalCnt',
    value: {},
  },
  {
    label: '高倍率病例',
    dataIndex: 'HighCnt',
    value: {
      AbnFeeType: '3',
    },
    icon: <UpCircleTwoTone twoToneColor="#eb2f96" />,
  },
  {
    label: '低倍率病例',
    dataIndex: 'LowCnt',
    value: {
      AbnFeeType: '4',
    },
    icon: <DownCircleTwoTone />,
  },
  {
    label: '普通入组病例',
    dataIndex: 'NormalCnt',
    value: {
      GroupResultType: 'Normal',
    },
  },
  {
    dataIndex: 'NormalSettleCnt',
    value: {
      GroupResultType: 'Normal',
      AbnFeeType: '1',
    },
  },
  {
    label: '异常病例',
    dataIndex: 'ProblematicCnt',
    value: {
      isProblematic: true,
    },
  },
  {
    label: '可优化病例',
    dataIndex: 'OptimizedCnt',
    value: {
      isOptimized: true,
    },
  },
  {
    label: '0000组病例',
    dataIndex: 'AbnormalCnt',
    value: {
      GroupResultType: 'Abnormal',
    },
  },
  {
    label: 'QY病例',
    dataIndex: 'QyCnt',
    value: {
      GroupResultType: 'Qy',
    },
  },
  {
    dataIndex: 'NotApplicableCnt',
    value: {
      Applicable: false,
    },
  },
  {
    dataIndex: 'UnspecCnt',
    value: {
      AbnFeeType: '0',
    },
  },
];
