import React, { useState } from 'react';
import { Tabs, Modal, Radio, message } from 'antd';
import { useModel } from '@@/plugin-model/useModel';
import { Emitter } from '@uni/utils/src/emitter';
import Management from './pages/management';
import ImportRecord from './pages/importRecord';
import { SpecialDiseaseDiscussionEventConstants } from './constants';
import { uniCommonService } from '@uni/services/src/commonService';
import { downloadFile, UseDispostionEnum } from '@uni/utils/src/download';
import { useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';

const SpecialDiseaseDiscussion = (props) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');
  // 添加状态来跟踪选中的通道类型
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);
  // 添加状态控制模态框显示
  const [modalVisible, setModalVisible] = useState(false);
  // 添加状态记录当前操作类型
  const [currentAction, setCurrentAction] = useState<'download' | 'import'>(
    'download',
  );

  const items = [
    {
      key: 'management',
      label: '病例管理',
      children: <Management />,
    },
    {
      key: 'importRecord',
      label: '上报记录',
      children: <ImportRecord />,
    },
  ];

  React.useEffect(() => {
    const showModal = (type) => {
      setSelectedChannel(null);
      setCurrentAction(type);
      setModalVisible(true);
    };

    Emitter.on(
      SpecialDiseaseDiscussionEventConstants.SHOW_TEMPLATE_MODAL,
      showModal,
    );

    return () => {
      Emitter.off(SpecialDiseaseDiscussionEventConstants.SHOW_TEMPLATE_MODAL);
    };
  }, []);

  // 下载导入模板
  const { run: downloadTemplateReq } = useRequest(
    (appealChannel) => {
      return uniCommonService(
        'Api/CenterSettle/AppealTaskImport/GetSpecialCaseImportTemplate',
        {
          method: 'POST',
          data: {
            AppealChannel: appealChannel,
          },
          responseType: 'blob',
        },
      );
    },
    {
      manual: true,
      formatResult: (response) => {
        return response;
      },
      onSuccess: (data, params) => {
        if (data.code === 0 && data.statusCode === 200) {
          message.success('Excel模板导出成功');
          let exportName = params?.[0]?.NormalChannel
            ? '特病单议病例普通申报模板'
            : '特病单议病例绿色通道模板';
          downloadFile(
            exportName,
            data?.response,
            UseDispostionEnum.combineWithNameFront,
          );
        } else {
          message.success('Excel模板导出失败，请联系管理员');
        }
      },
    },
  );

  const handleDownloadTemplate = (appealChannel) => {
    downloadTemplateReq(appealChannel);
  };

  // 导入文件
  const { run: importFileReq } = useRequest(
    (params: { file: File; appealChannel: string }) => {
      const formData = new FormData();
      formData.append('File', params.file);
      formData.append('AppealChannel', params.appealChannel);

      return uniCommonService(
        'Api/CenterSettle/AppealTaskImport/ImportSpecialCase',
        {
          method: 'POST',
          data: formData,
          requestType: 'form',
        },
      );
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res.code === 0 && res.statusCode === 200) {
          return 'success';
        } else {
          return 'failed';
        }
      },
      onSuccess: (data) => {
        if (data === 'success') {
          message.success('导入成功');
          // 刷新表格数据
          // TODO
        } else {
          message.error(data?.message || '导入失败');
        }
      },
    },
  );

  // 处理确认按钮点击
  const handleModalOk = () => {
    if (!selectedChannel) {
      message.warning('请先选择通道类型');
      return;
    }

    if (currentAction === 'download') {
      handleDownloadTemplate(selectedChannel);
    } else {
      // 创建一个隐藏的上传组件并触发点击
      const uploadInput = document.createElement('input');
      uploadInput.type = 'file';
      uploadInput.accept = '.xlsx';
      uploadInput.style.display = 'none';
      uploadInput.onchange = (e) => {
        const file = e.target.files?.[0];
        if (file) {
          importFileReq({
            file,
            appealChannel: selectedChannel,
          });
        }
        // 移除临时创建的元素
        document.body.removeChild(uploadInput);
      };
      document.body.appendChild(uploadInput);
      uploadInput.click();
    }
    setModalVisible(false);
  };

  // 处理取消按钮点击
  const handleModalCancel = () => {
    setModalVisible(false);
  };

  return (
    <>
      <Tabs
        defaultActiveKey="management"
        items={items}
        destroyInactiveTabPane
      />
      {/* <Modal
        title="模板内容"
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
      >
        <div>
          请选择模板类型：
          <Radio.Group
            onChange={(e) => setSelectedChannel(e.target.value)}
            value={selectedChannel}
          >
            {dictData?.CenterSettleAppealChannel?.map((item) => (
              <Radio key={item.Code} value={item.Code}>
                {item.Name}
              </Radio>
            ))}
          </Radio.Group>
        </div>
      </Modal> */}
    </>
  );
};

export default SpecialDiseaseDiscussion;
