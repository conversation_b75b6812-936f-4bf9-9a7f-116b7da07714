import './index.less';
import { Card, Row } from 'antd';
import { QualityExamineDetailStatus } from '@/pages/review/components/score-comment/comment/constants';
import { UniSelect } from '@uni/components/src';
import React from 'react';
import {
  RuleScoreCommentContent,
  RuleScoreCommentRestrictNumberInput,
  ruleScoreItemKeys,
} from '@/pages/review/components/score-comment/comment/rule-score-comment';
import { isEmptyValues } from '@uni/utils/src/utils';
import { cloneDeep } from 'lodash';

interface AnnotationAddProps {
  containerRef: any;
  commentInitialData: any;
  commentItem: any;
  commentRuleDetails: any;
  taskStatus: string;
  taskId?: string;
  tableReadonly: boolean;
  ruleItem?: any;
}

export const AnnotationAdd = (props: AnnotationAddProps) => {
  const [annotationContent, setAnnotationContent] = React.useState(undefined);

  const [annotationRuleItem, setAnnotationRuleItem] = React.useState(
    props?.ruleItem,
  );

  React.useEffect(() => {
    let content = {
      RuleCode: props?.commentInitialData?.RuleCode,
    };

    if (props?.commentInitialData?.ruleItem?.AllowInputScore === true) {
      content['InputScore'] = props?.commentInitialData?.ruleItem?.TotalScore;
    }

    setAnnotationContent(content);
  }, [props?.commentInitialData]);

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      getAnnotationContent: () => annotationContent,
    };
  });

  return (
    <Card id={'annotation-add-container'} bordered={false}>
      <div className={'annotation-add-content-container'}>
        {ruleScoreItemKeys
          ?.filter((item) => {
            if (item?.key === 'Status') {
              return false;
            }

            if (item?.key === 'InputScore') {
              return annotationRuleItem?.AllowInputScore === true;
            }

            if (item?.key === 'Creator') {
              return false;
            }

            return true;
          })
          ?.map((ruleItem) => {
            return (
              <div className={'annotation-add-item'}>
                <span className={'annotation-add-label'}>
                  {ruleItem?.label}：
                </span>
                {ruleItem?.key === 'OriginalInputValue' && (
                  <span className={'annotation-add-content'}>
                    {props?.commentItem?.[ruleItem?.key] || '-'}
                  </span>
                )}
                {ruleItem?.key === 'DisplayErrMsg' && (
                  <span
                    className={'annotation-add-content'}
                    style={{ width: 320 }}
                  >
                    <UniSelect
                      placeholder={'请选择规则'}
                      dataSource={props?.commentRuleDetails}
                      optionValueKey={'RuleCode'}
                      showSearch={true}
                      allowClear={false}
                      disabled={false}
                      value={annotationContent?.['RuleCode']}
                      getPopupContainer={(trigger) => {
                        return document.getElementById(
                          'annotation-add-container',
                        );
                      }}
                      optionNameKey={'DisplayErrMsg'}
                      onSelect={(value, record) => {
                        if (isEmptyValues(value)) {
                          annotationContent['RuleCode'] = undefined;
                          setAnnotationRuleItem(undefined);
                        } else {
                          annotationContent['RuleCode'] = value;
                          setAnnotationRuleItem(record);
                        }

                        if (record?.AllowInputScore === true) {
                          annotationContent['InputScore'] = record?.TotalScore;
                        }

                        setAnnotationContent(cloneDeep(annotationContent));
                      }}
                    />
                  </span>
                )}
                {ruleItem?.key === 'scoreLabel' && (
                  <span className={'annotation-add-content'}>
                    {annotationRuleItem?.[ruleItem?.key]}
                  </span>
                )}
                {ruleItem?.key === 'InputScore' && (
                  <div
                    style={{ width: 60 }}
                    className={
                      'annotation-add-content annotation-add-item-input'
                    }
                  >
                    <RuleScoreCommentRestrictNumberInput
                      needForm={false}
                      extraValue={{
                        value: annotationContent?.InputScore,
                      }}
                      ignoreUpdate={true}
                      formKey={'ANNOTATION-ADD'}
                      commentItem={props?.commentItem}
                      max={annotationRuleItem?.TotalScore}
                      taskStatus={props?.taskStatus}
                      step={
                        annotationRuleItem?.AllowMultipleErrItems === true
                          ? annotationRuleItem?.Score
                          : 1
                      }
                      commentRuleDetails={props?.commentRuleDetails}
                      updateAnnotationContent={(score: string) => {
                        annotationContent['InputScore'] = score;
                        setAnnotationContent(cloneDeep(annotationContent));
                      }}
                      tableReadonly={props?.tableReadonly}
                    />
                  </div>
                )}
                {ruleItem?.key === 'Remark' && (
                  <div
                    style={{ flex: 1 }}
                    className={
                      'annotation-add-content annotation-add-item-input'
                    }
                  >
                    <RuleScoreCommentContent
                      ignoreUpdate={true}
                      formKey={'ANNOTATION-ADD'}
                      commentItem={props?.commentItem}
                      taskId={props?.taskId}
                      commentRuleDetails={props?.commentRuleDetails}
                      updateAnnotationContent={(remark: string) => {
                        annotationContent['Remark'] = remark;
                        setAnnotationContent(cloneDeep(annotationContent));
                      }}
                      tableReadonly={props?.tableReadonly}
                      taskStatus={props?.taskStatus}
                    />
                  </div>
                )}
              </div>
            );
          })}
      </div>
    </Card>
  );
};

export default AnnotationAdd;
