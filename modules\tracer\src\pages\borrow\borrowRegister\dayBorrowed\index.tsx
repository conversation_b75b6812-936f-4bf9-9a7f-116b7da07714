import {
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import { UniTable } from '@uni/components/src';
import { Dispatch, useDispatch, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import dayjs from 'dayjs';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { FileExcelOutlined } from '@ant-design/icons';
import PdfPrint from '@uni/components/src/pdf-print';
import PatTimeline from '@/components/PatTimeline';
import { useTimelineReq } from '@/hooks';
import { IReducer, ITableState } from '@uni/reducers/src/Interface';
import { TableAction, tableReducer, InitTableState } from '@uni/reducers/src';
import { ReqActionType } from '@/Constants';
import { columnsHandler, isRespErr, sortingHandler } from '@/utils/widgets';
import { useSafeState, useKeyPress } from 'ahooks';
import { Button, Card, Col, Divider, Row, Space, Tooltip } from 'antd';
import { useReactToPrint } from 'react-to-print';
import _ from 'lodash';
import './index.less';
import { SwagBorrowRecordItem } from '../../interface';
import { UseDispostionEnum, downloadFile } from '@uni/utils/src/download';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { ProFormInstance } from '@ant-design/pro-components';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import BorrowPagePrint from '@/components/PrintBorrowPage';
import { ITableReq } from '@/Interface';

const DayBorrowedList = ({ tabKey, borrower }) => {
  const {
    globalState: { dictData, userInfo },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  // form args
  const proFormRef = useRef<ProFormInstance>();

  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();

  // 查询列表
  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagBorrowRecordItem>, IReducer>
  >(tableReducer, InitTableState);

  const [backPagination, setBackPagination] = useSafeState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    hideOnSinglePage: false,
  });

  // 搜索table的tableReq (做成 类 前端分页 因为要前端排序)
  const tableReq = async (
    params,
    cur = 1,
    size = 99999,
    sorter = SearchTable.sorter,
  ) => {
    let res: ITableReq | string = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: 'TraceRecord',
        requestParams: [
          {
            url: 'Api/Mr/BorrowRecord/GetSelectiveBorrowRecord',
            method: 'POST',
            dataType: 'mr',
            data: {
              // ...params,
              Borrower: params?.Borrower?.value ?? params?.Borrower,
              // 如果没有选择日期，默认使用当天
              BorrowSDate: params?.Date
                ? dayjs(params?.Date).format('YYYY-MM-DD')
                : dayjs().format('YYYY-MM-DD'),
              BorrowEDate: params?.Date
                ? dayjs(params?.Date).format('YYYY-MM-DD')
                : dayjs().format('YYYY-MM-DD'),
              current: cur,
              pageSize: size,
              sorting: sortingHandler(sorter),
            },
          },
        ],
      },
    });
    if (typeof res !== 'string') {
      console.log(res);
      let total = res?.datas[0]?.total;

      // 这边是前端分页
      SearchTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data:
            _.sortBy(res?.datas[0]?.data, [
              function (o) {
                return dayjs(o.BorrowDate)?.valueOf();
              },
            ])?.reverse() ?? [],
        },
      });

      // 重置时间轴等
      SearchTableDispatch({
        type: TableAction.clkChange,
        payload: {
          clkItem: null,
        },
      });
      setParams(null);

      setBackPagination({
        ...backPagination,
        current: cur,
        pageSize: size,
        total: total ?? 0,
      });
    }
  };

  // columns处理
  if (
    columnsList?.['BorrowRecord/GetLendList']?.length > 0 &&
    SearchTable.columns.length < 1
  ) {
    SearchTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          [
            {
              data: 'xuhao',
              dataIndex: 'xuhao',
              title: '序号',
              visible: true,
              align: 'center',
              render: (text, record, index) => {
                return index + 1;
              },
              order: 2,
            },
          ],
          columnsList['BorrowRecord/GetLendList'],
        ),
      },
    });
  }

  // columns 处理，主要用于处理options
  const columnsSolver = useMemo(() => {
    return SearchTable.columns.length > 0
      ? columnsHandler(SearchTable.columns, {})
      : [];
  }, [SearchTable.columns]);

  useEffect(() => {
    if (tabKey === 'dayBorrowed' && borrower) {
      console.log('borrower', borrower);
      tableReq({
        Borrower: borrower,
        Date: dayjs().format('YYYY-MM-DD'),
      });
    }
  }, [tabKey]);

  // 后端导出预留
  const exportBackendReq = async (params) => {
    // if () return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: 'ExportTraceRecord',
        requestParams: {
          url: `Api/Mr/BorrowRecord/${ReqActionType.borrowRecordPrint}`,
          method: 'POST',
          data: params,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      // message.success('导出成功');
      console.log(res);
      downloadFile('', res?.response, UseDispostionEnum.combineWithNameFront);
    }
  };

  useKeyPress(
    'enter',
    () => {
      console.log('press enter only');
      if (proFormRef.current.getFieldValue('Borrower')) {
        proFormRef.current.validateFields().then((values) => {
          console.log('values', values);
          tableReq(values);
        });
      }
    },
    {
      exactMatch: true,
      target: document.getElementById('borrowerForm'),
    },
  );

  useEffect(() => {
    if (borrower && dictData?.Mr?.Employee?.length > 0) {
      proFormRef.current.setFieldValue('Borrower', {
        value: borrower,
        label: dictData?.Mr?.Employee?.find((d) => d.Code === borrower)?.Name,
      });
    }
  }, [borrower, dictData?.Mr?.Employee]);

  // 直接打印part
  // print component
  const componentRef = useRef<any>();
  // store the resolve Promise being used in `onBeforeGetContent`
  const promiseResolveRef = useRef(null);
  const [isPrinting, setIsPrinting] = useState(false);

  useEffect(() => {
    if (isPrinting && promiseResolveRef.current) {
      // Resolves the Promise, letting `react-to-print` know that the DOM updates are completed
      promiseResolveRef.current();
    }
  }, [isPrinting]);

  const handlePrint = useReactToPrint({
    bodyClass: 'print-body',
    pageStyle: '#print_example { display: block !important; }',
    content: () => componentRef.current,
    onBeforeGetContent: () => {
      return new Promise((resolve) => {
        // 打印前的额外操作在这里写
        promiseResolveRef.current = resolve;
        setIsPrinting(true);
      });
    },
    onAfterPrint: () => {
      // 打印后的复原操作在这里写
      // Reset the Promise resolve so we can print again
      promiseResolveRef.current = null;
      setIsPrinting(false);
    },
  });

  return (
    <Row gutter={[16, 16]}>
      <Col xxl={7} xl={8}>
        <Card title={<Space>借阅信息</Space>} style={{ marginBottom: '15px' }}>
          <ProFormContainer
            spinLoading={
              loadings['TraceRecord'] ||
              loadings[`Borrowing/${ReqActionType.lend}`] ||
              false
            }
            autoFocusFirstInput={false}
            className="borrow_register_form"
            id="borrowerForm"
            formRef={proFormRef}
            labelCol={{ flex: '90px' }}
            wrapperCol={{ flex: 'auto' }}
            searchOpts={
              [
                {
                  name: 'Borrower',
                  title: '借阅者',
                  dataType: 'select',
                  rules: [{ required: true }],
                  opts: dictData?.Mr?.Employee ?? dictData?.Employee ?? [],
                  fieldProps: {
                    labelInValue: true,
                  },
                  initalValue: borrower,
                  visible: true,
                },
                {
                  name: 'Date',
                  title: '日期',
                  dataType: 'date',
                  placeholder: '不填默认今天',
                  visible: true,
                },
              ] as any
            }
            submitter={{
              render: (props, doms) => {
                return [
                  <Button
                    type="primary"
                    style={{
                      width: 'calc(100% - 100px)',
                      float: 'right',
                      marginTop: '8px',
                    }}
                    key="submit"
                    onClick={() => {
                      //   fetchReqActionReq();
                      proFormRef.current.validateFields().then((values) => {
                        console.log('values', values);
                        tableReq(values);
                      });
                    }}
                  >
                    查询(Enter)
                  </Button>,
                ];
              },
            }}
          />
        </Card>
        <PatTimeline
          item={SearchTable?.clkItem}
          loading={loadings['TraceRecord/GetActions']}
          timelineItems={timelineItems}
        />
      </Col>
      <Col xxl={17} xl={16}>
        <Card
          title="当日已借阅病案列表"
          extra={
            <Space>
              <PdfPrint
                apiUrl="Api/Mr/BorrowRecord/SelectiveBorrowRecordPrintPdf"
                tooltipTitle="打印当日借阅记录"
                buttonType="default"
                buttonSize="middle"
                paramType="data"
                params={() => {
                  // 验证必要的表单数据
                  const values = proFormRef.current?.getFieldsValue();
                  if (!values?.Borrower) {
                    return false; // 阻止请求发送
                  }

                  // 检查数据是否存在
                  if (!SearchTable.data || SearchTable.data.length < 1) {
                    return false;
                  }

                  // 处理日期：若未选择则默认今天
                  const date = values.Date
                    ? dayjs(values.Date).format('YYYY-MM-DD')
                    : dayjs().format('YYYY-MM-DD');

                  return {
                    BorrowerName: values.Borrower.label,
                    BorrowDate: date,
                    BorrowRecords: SearchTable.data.map((d, i) => ({
                      ...d,
                      Sort: i + 1,
                    })),
                  };
                }}
              >
                打印
              </PdfPrint>
              <Divider type="vertical" />
              <Tooltip title="导出Docx">
                <Button
                  type="text"
                  shape="circle"
                  disabled={SearchTable.data?.length < 1}
                  // @ts-ignore - 忽略TypeScript错误，项目中其他组件也是这样使用图标的
                  icon={<FileExcelOutlined />}
                  onClick={(e) => {
                    proFormRef.current.validateFields().then((values) => {
                      exportBackendReq({
                        BorrowerName: values?.Borrower?.label,
                        // 如果没有选择日期，默认使用当天
                        BorrowDate:
                          values?.Date || dayjs().format('YYYY-MM-DD'),
                        BorrowRecords: SearchTable?.data?.map((d, i) => ({
                          ...d,
                          Sort: i + 1,
                        })),
                      });
                    });
                  }}
                />
              </Tooltip>
              <TableColumnEditButton
                {...{
                  columnInterfaceUrl: 'Api/Mr/BorrowRecord/GetLendList',
                  onTableRowSaveSuccess: (columns) => {
                    // 这个columns 存到dva
                    dispatch({
                      type: 'global/saveColumns',
                      payload: {
                        name: 'BorrowRecord/GetLendList',
                        value: columns,
                      },
                    });
                    SearchTableDispatch({
                      type: TableAction.columnsChange,
                      payload: {
                        columns: columns,
                      },
                    });
                  },
                }}
              />
            </Space>
          }
        >
          <UniTable
            id="day_signined_list"
            rowKey="BarCode"
            showSorterTooltip={false}
            loading={loadings['TraceRecord'] ?? false}
            columns={columnsSolver}
            dataSource={SearchTable.data}
            scroll={{ x: 'max-content' }}
            pagination={backPagination}
            dictionaryData={dictData}
            // onChange={backTableOnChange}
            rowClassName={(record) => {
              if (record?.RecordId === SearchTable.clkItem?.RecordId)
                return 'row-selected';
              return null;
            }}
            onRow={(record) => {
              return {
                onClick: (event) => {
                  if (SearchTable.clkItem?.BarCode !== record?.BarCode) {
                    SearchTableDispatch({
                      type: TableAction.clkChange,
                      payload: {
                        clkItem: record,
                      },
                    });
                    setParams({ barCode: record.BarCode });
                  }
                },
              };
            }}
          />
        </Card>

        {/* 保留原有的BorrowPagePrint组件，用于本地打印预览功能 */}
        <div style={{ display: 'none' }}>
          <BorrowPagePrint
            userInfo={{
              Operator: userInfo?.Name,
              Borrower: proFormRef.current?.getFieldValue('Borrower')?.label,
            }}
            tableData={SearchTable.data?.map((d, i) => ({
              ...d,
              index: i + 1,
            }))}
            ref={componentRef}
          />
        </div>
      </Col>
    </Row>
  );
};

export default DayBorrowedList;
