import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import {
  UniDmrDragEditOnlyTable,
  UniDragEditTable,
  UniTable,
} from '@uni/components/src';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { icdeColumns, pathologyIcdeColumns } from '@/pages/dmr/columns';
import { Form, Modal } from 'antd';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';
import cloneDeep from 'lodash/cloneDeep';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';

interface PathologyIcdeDragTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];

  underConfiguration?: boolean;

  onChange?: (value: any) => void;
}

interface PathologyIcdeItem {
  PathologyIcdeId?: number;
  id?: string | number;

  PathologyIcdeSort?: number;
  PathologyIcdeName?: string;
  PathologyIcdeCode?: string;
  PalgNo?: string;
}

const hotKeyToEvents = {
  ADD: (event) => {
    Emitter.emit(EventConstant.DMR_PATHOLOGY_ICDE_ADD, event.target.id);
  },
  DELETE: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_PATHOLOGY_ICDE_DELETE, index);
    }
  },
  SCROLL_LEFT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#pathologicalDiagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: -100,
      behavior: 'smooth',
    });
  },
  SCROLL_RIGHT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#pathologicalDiagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: 100,
      behavior: 'smooth',
    });
  },
  SCROLL_UP: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#pathologicalDiagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: -50,
      behavior: 'smooth',
    });
  },
  SCROLL_DOWN: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#pathologicalDiagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: 50,
      behavior: 'smooth',
    });
  },

  UP: (event) => {
    Emitter.emit(getArrowUpDownEventKey('pathologicalDiagnosisTable'), {
      event: event,
      type: 'UP',
      trigger: 'hotkey',
    });
  },
  DOWN: (event) => {
    Emitter.emit(getArrowUpDownEventKey('pathologicalDiagnosisTable'), {
      event: event,
      type: 'DOWN',
      trigger: 'hotkey',
    });
  },
};

const clearKeysMap = {
  // 仅用于联动删除使用
  PathologyIcdeCode: ['PathologyIcdeName', 'PathologyIcdeCode'],
};

const PathologyIcdeDragTable = (props: PathologyIcdeDragTableProps) => {
  const itemRef = React.useRef<any>();

  const [form] = Form.useForm();

  const pathologyIcdeDataSource =
    Form.useWatch('pathologicalDiagnosisTable', props?.form) ?? [];

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    pathologyIcdeDataSource?.length,
  );

  useEffect(() => {
    setTableDataSourceSize(pathologyIcdeDataSource?.length);
  }, [pathologyIcdeDataSource]);

  const lineUpDownEvents = {
    LINE_UP: (event) => {
      console.log('LINE_UP', event);
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let focusId = undefined;
      if (index === 0) {
        focusId = itemId;
      } else {
        ids[2] = index - 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('pathologicalDiagnosisTable'), {
        oldIndex: index,
        newIndex: index - 1,
        focusId: focusId,
      });
    },
    LINE_DOWN: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let dataSourceSize = props?.form?.getFieldValue(
        'pathological-diagnosis-table',
      ).length;
      let focusId = undefined;
      if (index === dataSourceSize - 1) {
        focusId = itemId;
      } else {
        ids[2] = index + 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('pathologicalDiagnosisTable'), {
        oldIndex: index,
        newIndex: index + 1,
        focusId: focusId,
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      { ...hotKeyToEvents, ...lineUpDownEvents }?.[item?.type],
      {
        preventDefault: true,
        enabled: true,
        enableOnFormTags: true,
        enableOnContentEditable: true,
      },
    );
  });

  useEffect(() => {
    setTimeout(() => {
      waitFocusElementRefocusBySelector(waitFocusId);
    }, 100);
  }, [waitFocusId]);

  useEffect(() => {
    let columns = mergeColumnsInDmrTable(
      props?.columns,
      pathologyIcdeColumns,
      'PathologyIcdeDragTable',
    );

    setTableColumns(columns);
  }, [props?.columns]);

  useEffect(() => {
    // delete事件
    Emitter.on(
      getDeletePressEventKey('pathologicalDiagnosisTable'),
      (itemId) => {
        // key 包含 index 和 其他的东西
        console.log('pathologicalDiagnosisTable', itemId);
        let itemIds = itemId?.split('#');
        let index = parseInt(itemIds?.at(2));
        let key = itemIds?.at(1);

        let clearKeys = [key];
        if (clearKeysMap[key]) {
          clearKeys = clearKeysMap[key];
        }
        clearValuesByKeys(clearKeys, index);
      },
    );

    Emitter.on(EventConstant.DMR_PATHOLOGY_ICDE_ADD, (focusId?: string) => {
      let rowData = {
        id: Math.round(Date.now() / 1000),
        UniqueId: uuidv4(),
      };
      let tableData = props?.form?.getFieldValue(
        'pathological-diagnosis-table',
      );

      tableData.splice(tableData.length, 0, rowData);
      props?.form?.setFieldValue(
        'pathological-diagnosis-table',
        cloneDeep(tableData),
      );

      setWaitFocusId(
        `div[id=pathologicalDiagnosisTable] tbody > tr:nth-child(${tableData?.length}) > td input`,
      );
      setTableDataSourceSize(tableData?.length);
    });

    Emitter.on(EventConstant.DMR_PATHOLOGY_ICDE_DELETE, (index) => {
      if (index > -1) {
        let tableData = props?.form?.getFieldValue(
          'pathological-diagnosis-table',
        );
        tableData.splice(index, 1);

        // 更新form
        props?.form?.setFieldValue(
          'pathological-diagnosis-table',
          cloneDeep(tableData),
        );

        // 删除的时候 给出当前那个选中的
        // 当前选中的意思是表格中的上一个存在即是上表格中上一个的最后一个
        // 表格中不存在即写第0个的icdeName 建议写死
        let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
        if (dataItems?.length > 0) {
          setWaitFocusId(
            `div[id=diagnosisTable] tbody > tr:nth-child(${
              index >= dataItems.length - 1 ? dataItems.length : index + 1
            }) > td input`,
          );
        }
        setTableDataSourceSize(tableData?.length);
      }
    });

    Emitter.on(
      getArrowUpDownEventKey('pathologicalDiagnosisTable'),
      (payload) => {
        const pathologyIcdeDataSource = props?.form?.getFieldValue(
          'pathological-diagnosis-table',
        );
        let type = payload?.type;
        console.log('payload', payload);
        if (
          payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
          payload?.trigger === 'hotkey'
        ) {
          // 表示是 下拉框 需要定制
          return;
        }

        payload?.event?.stopPropagation();

        let { nextIndex, activePaths } = calculateNextIndex(type);
        if (type === 'UP') {
          if (nextIndex < 0) {
            nextIndex = undefined;
          }
        }

        if (type === 'DOWN') {
          if (nextIndex > pathologyIcdeDataSource?.length - 2) {
            nextIndex = undefined;
          }
        }

        if (nextIndex !== undefined) {
          activePaths[2] = nextIndex.toString();
          document.getElementById(activePaths?.join('#'))?.focus();
        }
      },
    );

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('pathologicalDiagnosisTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      resizeObserver?.disconnect();

      Emitter.off(EventConstant.DMR_PATHOLOGY_ICDE_ADD);
      Emitter.off(EventConstant.DMR_PATHOLOGY_ICDE_DELETE);

      Emitter.off(getDeletePressEventKey('pathologicalDiagnosisTable'));
      Emitter.off(getArrowUpDownEventKey('pathologicalDiagnosisTable'));
    };
  }, []);

  const clearValuesByKeys = (keys, index) => {
    const pathologyIcdeDataSource = props?.form?.getFieldValue(
      'pathological-diagnosis-table',
    );
    let formItemId = pathologyIcdeDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue('pathological-diagnosis-table');
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue(
      'pathological-diagnosis-table',
      cloneDeep(tableData),
    );
  };

  return (
    <UniDmrDragEditOnlyTable
      {...props}
      formItemContainerClassName={'form-content-item-container'}
      itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
      form={form}
      key={props?.id}
      id={props?.id}
      tableId={props?.id}
      formKey={'pathologicalDiagnosisTable'}
      forceColumnsUpdate={props?.underConfiguration ?? false}
      scroll={{
        x: 'max-content',
      }}
      pagination={false}
      className={`table-container ${props?.className || ''}`}
      dataSource={(
        props?.form?.getFieldValue('pathological-diagnosis-table') ?? []
      )
        ?.filter((item) => item.id !== 'ADD')
        ?.map((item) => {
          if (!item['id']) {
            item['id'] = Math.round(Date.now() / 1000);
          }

          return item;
        })
        ?.concat({
          id: 'ADD',
        })}
      rowKey={'id'}
      onValuesChange={(tableData) => {
        // setPathologyIcdeDataSource(tableData);

        props?.form?.setFieldValue('pathological-diagnosis-table', tableData);
        triggerFormValueChangeEvent('pathological-diagnosis-table');
      }}
      onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
        props?.form?.setFieldValue(
          'pathological-diagnosis-table',
          cloneDeep(tableData),
        );
        // props?.form?.setFieldValue(
        //   'pathologicalDiagnosisTable',
        //   cloneDeep(newTableData),
        // );
        if (focusId) {
          setTimeout(() => {
            waitFocusElementRefocus(focusId);
          }, 100);
        }
        triggerFormValueChangeEvent('pathological-diagnosis-table');
      }}
      columns={tableColumns}
    />
  );
};

export default React.memo(PathologyIcdeDragTable);
