import mammoth from 'mammoth';
import { message } from 'antd';

/**
 * 将docx文档转换为HTML并打印
 * @param docBlob docx文档的Blob数据
 * @param title 打印标题，显示在打印页面顶部
 */
export const docxToPrint = async (
  docData: any,
  title: string = '借阅清单',
): Promise<boolean> => {
  try {
    if (!docData) {
      message.error('没有获取到文档数据');
      return false;
    }

    // 将响应数据转换为ArrayBuffer
    let arrayBuffer: ArrayBuffer;

    if (docData instanceof Blob) {
      arrayBuffer = await docData.arrayBuffer();
    } else if (typeof docData === 'object' && docData.data) {
      // 处理base64编码
      const base64Data = docData.data.replace(/^data:.*?;base64,/, '');
      const binaryString = window.atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      arrayBuffer = bytes.buffer;
    } else {
      message.error('不支持的数据格式');
      return false;
    }

    // 使用mammoth将DOCX转换为HTML
    const result = await mammoth.convertToHtml(
      { arrayBuffer },
      {
        convertImage: mammoth.images.dataUri,
        styleMap: [
          "p[style-name='Title'] => h1:fresh",
          "p[style-name='Subtitle'] => h2:fresh",
          "p[style-name='Heading 1'] => h1:fresh",
          "p[style-name='Heading 2'] => h2:fresh",
          "p[style-name='Heading 3'] => h3:fresh",
          'r => span:fresh',
        ],
        // 移除不被支持的选项
        includeDefaultStyleMap: true,
      },
    );

    const html = result.value;
    const warnings = result.messages;

    if (warnings.length > 0) {
      console.warn('文档转换警告:', warnings);
    }

    // 创建一个新窗口用于打印
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      message.error('无法打开打印窗口，请检查是否被浏览器阻止');
      return false;
    }

    // 写入HTML内容，包含更多样式处理
    printWindow.document.open();
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${title}</title>
        <style>
          /* 尝试最大程度保留文档原始样式 */
          body {
            font-family: 'Times New Roman', SimSun, Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
          }
          
          /* 保留表格样式 */
          table {
            border-collapse: collapse;
            width: 100%;
          }
          
          table, th, td {
            border: 1px solid #000;
            padding: 5px;
          }
          
          /* 打印控制样式 */
          @media print {
            .no-print {
              display: none !important;
            }
          }
          
          .print-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 8px;
            z-index: 9999;
          }
          
          .print-btn {
            margin: 0 4px;
            padding: 4px 12px;
            cursor: pointer;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 2px;
          }
          
          .close-btn {
            margin: 0 4px;
            padding: 4px 12px;
            cursor: pointer;
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
          }
        </style>
      </head>
      <body>
        ${html}
        
        <div class="print-controls no-print">
          <button class="print-btn" onclick="window.print()">打印</button>
          <button class="close-btn" onclick="window.close()">关闭</button>
        </div>
        
        <script>
          window.onload = function() {
            setTimeout(function() {
              window.print();
            }, 500);
          };
        </script>
      </body>
      </html>
    `);
    printWindow.document.close();

    return true;
  } catch (error) {
    console.error('打印文档时出错:', error);
    message.error(
      '打印文档时出错: ' +
        (error instanceof Error ? error.message : String(error)),
    );
    return false;
  }
};

/**
 * 用于处理API返回的docx数据并打印的函数
 * 这是一个适配器，用于适配原有API调用
 */
export const handleDocxPrintFromApi = async (
  response: any,
  title: string = '借阅清单',
): Promise<boolean> => {
  try {
    if (!response) {
      message.error('未接收到API响应');
      return false;
    }

    // 处理Blob类型数据
    if (response instanceof Blob) {
      return await docxToPrint(response, title);
    }

    // 处理Response类型数据
    if (response.headers && typeof response.blob === 'function') {
      try {
        const contentType = response.headers.get('Content-Type');
        const blobData = await response.blob();
        const blob = new Blob([blobData], {
          type:
            contentType ||
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        });
        return await docxToPrint(blob, title);
      } catch (error) {
        console.error('Blob转换错误:', error);
        message.error('文档转换失败');
        return false;
      }
    }

    // 处理API响应对象
    if (response.code === 0 && response.statusCode === 200) {
      console.log('API响应成功，准备处理文档数据');

      // 如果response包含response字段（嵌套的响应对象）
      if (response.response) {
        const blob = new Blob([response.response], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        });
        return await docxToPrint(blob, title);
      }

      // 如果response包含data字段
      if (response.data) {
        return await docxToPrint(response.data, title);
      }
    }

    message.error('无法识别的响应格式');
    return false;
  } catch (error) {
    console.error('处理API文档数据时出错:', error);
    message.error(
      '处理文档数据时出错: ' +
        (error instanceof Error ? error.message : String(error)),
    );
    return false;
  }
};
