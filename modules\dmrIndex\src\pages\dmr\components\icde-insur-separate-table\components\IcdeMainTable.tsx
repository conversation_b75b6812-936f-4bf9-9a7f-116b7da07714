import React, { useEffect, useState } from 'react';
import { UniDmrDragEditOnlyTable } from '@uni/components/src';
import { icdeColumns } from '@/pages/dmr/columns';
import { Form } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import { triggerFormValueChangeEvent } from '@uni/grid/src/utils';

interface IcdeMainTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];
  underConfiguration?: boolean;
  onChange?: (value: any) => void;
}

const IcdeMainTable = (props: IcdeMainTableProps) => {
  const [form] = Form.useForm();

  // 监听主表格数据变化（用于触发重新渲染）
  const icdeDataSource = Form.useWatch('diagnosisMainTable', props?.form) ?? [];

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    icdeDataSource?.length,
  );

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  // 过滤列：移除医保编码和医保名称列
  useEffect(() => {
    const filteredColumns = icdeColumns.filter(column => {
      const dataIndex = column.dataIndex;
      return dataIndex !== 'InsurCode' && dataIndex !== 'InsurName';
    });
    setTableColumns(filteredColumns);
  }, []);

  useEffect(() => {
    setTableDataSourceSize(icdeDataSource?.length);
  }, [icdeDataSource]);

  useEffect(() => {
    // 当表格的数量改变的时候 重新计算 IsReportedTrueCount
    let icdeTableData = props?.form?.getFieldValue('diagnosis-main-table');
    let IsReportedTrueCount = 0;
    icdeTableData?.forEach((item) => {
      if (item?.IsReported === true) {
        IsReportedTrueCount++;
      }
    });
    form.setFieldValue('IsReportedTrueCount', IsReportedTrueCount);
  }, [tableDataSourceSize]);

  return (
    <UniDmrDragEditOnlyTable
      {...props}
      extraFormItemKeys={['IsReportedTrueCount']}
      formItemContainerClassName={'form-content-item-container'}
      form={form}
      key={props?.id}
      id={props?.id}
      tableId={props?.id}
      formKey={'diagnosisMainTable'}
      forceColumnsUpdate={props?.underConfiguration ?? false}
      scroll={{
        x: 'max-content',
      }}
      pagination={false}
      className={`table-container ${props?.className || ''}`}
      dataSource={(props?.form?.getFieldValue('diagnosis-main-table') ?? [])
        ?.filter((item) => item?.id !== 'ADD')
        ?.map((item) => {
          if (!item['id']) {
            item['id'] = Math.round(Date.now() / 1000);
          }
          return item;
        })
        ?.concat({
          id: 'ADD',
        })}
      rowKey={'id'}
      onValuesChange={(recordList, changedValues) => {
        props?.form?.setFieldValue('diagnosis-main-table', recordList);
        triggerFormValueChangeEvent('diagnosis-main-table');
      }}
      onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
        props?.form?.setFieldValue('diagnosis-main-table', cloneDeep(tableData));
        triggerFormValueChangeEvent('diagnosis-main-table');
      }}
      columns={tableColumns}
      enableRowSelection={true}
    />
  );
};

export default React.memo(IcdeMainTable);
