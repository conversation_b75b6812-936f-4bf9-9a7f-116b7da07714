import React, { useEffect, useState, useMemo } from 'react';
import { Form } from 'antd';
import { cloneDeep } from 'lodash';
import { UniDmrDragEditOnlyTable } from '@uni/components/src';
import { icdeColumns } from '../../../columns';
import { triggerFormValueChangeEvent } from '@uni/grid/src/utils';

interface IcdeMainTableProps {
  form: any;
  id: string;
  parentId: string;
  dataSource: any[];
  onChange: (data: any[]) => void;
  className?: string;
  style?: React.CSSProperties;
  underConfiguration?: boolean;
}

const IcdeMainTable: React.FC<IcdeMainTableProps> = (props) => {
  const { form, dataSource, onChange, parentId, id } = props;
  
  // 监听主表格数据变化（用于触发重新渲染）
  const mainTableWatchData = Form.useWatch('diagnosisMainTable', form) ?? [];
  const [tableDataSourceSize, setTableDataSourceSize] = useState(0);

  // 过滤列：移除医保编码和医保名称列
  const filteredColumns = useMemo(() => {
    return icdeColumns.filter(column => {
      const dataIndex = column.dataIndex;
      return dataIndex !== 'InsurCode' && dataIndex !== 'InsurName';
    });
  }, []);

  // 处理表格数据，添加ADD行
  const processedDataSource = useMemo(() => {
    if (!dataSource || !Array.isArray(dataSource)) {
      return [{ id: 'ADD' }];
    }

    return dataSource
      .filter((item) => item?.id !== 'ADD')
      .map((item) => {
        if (!item['id']) {
          item['id'] = Math.round(Date.now() / 1000);
        }
        return item;
      })
      .concat({ id: 'ADD' });
  }, [dataSource]);

  // 处理表格值变化
  const handleValuesChange = (recordList: any[], changedValues: any) => {
    // 过滤掉ADD行
    const filteredRecordList = recordList.filter(item => item?.id !== 'ADD');
    
    // 更新主表格专用字段（用于触发重新渲染）
    form.setFieldValue('diagnosis-main-table', filteredRecordList);
    form.setFieldValue('diagnosisMainTable', cloneDeep(filteredRecordList));
    
    // 触发表单变化事件
    triggerFormValueChangeEvent('diagnosis-main-table');
    
    // 调用父组件的onChange
    if (onChange) {
      onChange(filteredRecordList);
    }
  };

  // 监听数据变化更新表格大小
  useEffect(() => {
    setTableDataSourceSize(mainTableWatchData?.length || 0);
  }, [mainTableWatchData]);

  return (
    <div className="icde-main-table">
      <UniDmrDragEditOnlyTable
        formKey={'diagnosisMainTable'}
        dataSource={processedDataSource}
        columns={filteredColumns}
        onValuesChange={handleValuesChange}
        form={form}
        id={id}
        parentId={parentId}
        className={props.className}
        style={props.style}
        underConfiguration={props.underConfiguration}
        // 其他原有的props
        tableDataSourceSize={tableDataSourceSize}
      />
    </div>
  );
};

export default React.memo(IcdeMainTable);
