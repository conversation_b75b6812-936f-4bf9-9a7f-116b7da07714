import { Card, Col, Row } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import './index.less';
import { UniTable } from '@uni/components/src';
import { ScoreColumns, SuspectedCntColumns } from '../constants';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { useDeepCompareEffect } from 'ahooks';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '../../components/drawerCardInfo';

const HqmsQualityScore = () => {
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const { globalState } = useModel('@@qiankunStateFromMaster');
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, insurType } = searchParams;
  const [hqmsScoreOfHospTableColumns, setHqmsScoreOfHospTableColumns] =
    useState([]);
  const [
    hqmsSuspectedCntOfHospTableColumns,
    setHqmsSuspectedCntOfHospTableColumns,
  ] = useState([]);
  const [sdHqmsScoreOfHospTableData, setSdHqmsScoreOfHospTableData] = useState(
    [],
  );
  const [sdHqmsScoreOfHospTableFinalData, setSdHqmsScoreOfHospTableFinalData] =
    useState([]);
  const [tableParams, setTableParams] = useState(undefined);

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
    };
    setTableParams(params);
    if (dateRange?.length && hospCodes?.length) {
      getHqmsScoreOfHospReq(params);
      getHqmsSuspectedCntOfHospReq(params);
    }
  }, [dateRange, hospCodes]);

  const {
    data: hqmsScoreOfHospData,
    loading: getHqmsScoreOfHospLoading,
    run: getHqmsScoreOfHospReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/Hqms/HospHqmsQuality/HqmsScoreOfHosp', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            setSdHqmsScoreOfHospTableData(
              res.data.filter((dt) => dt.VersionedSdCode),
            );
            return res.data.filter((dt) => !dt.VersionedSdCode);
          } else return [];
        }
      },
    },
  );

  // Columns
  const { run: getHqmsScoreOfHospColumnsReq } = useRequest(
    () =>
      uniCommonService('Api/Hqms/HospHqmsQuality/HqmsScoreOfHosp', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0) {
          setHqmsScoreOfHospTableColumns(
            tableColumnBaseProcessor(ScoreColumns, res.data?.Columns),
          );
          setHqmsSuspectedCntOfHospTableColumns(
            tableColumnBaseProcessor(
              SuspectedCntColumns(tableParams),
              res.data?.Columns,
            ),
          );
        } else {
          setHqmsScoreOfHospTableColumns([]);
          setHqmsSuspectedCntOfHospTableColumns([]);
        }
      },
    },
  );

  const {
    data: hqmsSuspectedCntOfHospData,
    loading: getHqmsSuspectedCntOfHospLoading,
    run: getHqmsSuspectedCntOfHospReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/Hqms/HospHqmsQuality/HqmsSuspectedCntOfHosp', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  useEffect(() => {
    getHqmsScoreOfHospColumnsReq();
  }, []);

  useEffect(() => {
    if (
      sdHqmsScoreOfHospTableData?.length &&
      hqmsSuspectedCntOfHospData?.length
    ) {
      let finalData = sdHqmsScoreOfHospTableData.map((data) => {
        let obj = hqmsSuspectedCntOfHospData.filter(
          (d) =>
            d.IndicatorName === data.IndicatorName &&
            d.VersionedSdCode === data.VersionedSdCode,
        )[0];
        if (!obj || !obj.SuspectedCnt) {
          return data;
        }
        return {
          ...data,
          SuspectedCnt: obj?.SuspectedCnt,
        };
      });
      setSdHqmsScoreOfHospTableFinalData(finalData);
    }
  }, [sdHqmsScoreOfHospTableData, hqmsSuspectedCntOfHospData]);

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="全院监测">
            <UniTable
              id={'hqmsScoreOfHosp-of-hosp-table'}
              className="hqmsScoreOfHosp-table-container"
              rowKey={'DisplayName'}
              scroll={{ x: 'max-content' }}
              clickable={true}
              loading={getHqmsScoreOfHospLoading}
              columns={hqmsScoreOfHospTableColumns}
              dataSource={hqmsScoreOfHospData}
              // needExport={true}
            />
          </Card>
        </Col>
        <Col span={24}>
          <Card title="单病种监测">
            <UniTable
              id={'hqmsSuspectedCntOfHosp-of-hosp-table'}
              className="hqmsSuspectedCntOfHosp-table-container"
              rowKey={'SdName'}
              scroll={{ x: 'max-content' }}
              clickable={true}
              loading={getHqmsSuspectedCntOfHospLoading}
              columns={hqmsSuspectedCntOfHospTableColumns}
              dataSource={sdHqmsScoreOfHospTableFinalData}
              // needExport={true}
            />
          </Card>
        </Col>
      </Row>
      <DetailTableModal
        dictData={globalState.dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'dip',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default HqmsQualityScore;
