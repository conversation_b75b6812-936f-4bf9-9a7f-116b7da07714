import { QualityExamineEventConstants } from '@/pages/qualityExamineSys/constants';
import { UniTable } from '@uni/components/src';
import {
  ProForm,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@uni/components/src/pro-form';
import { Emitter } from '@uni/utils/src/emitter';
import { AutoComplete, Form } from 'antd';
import ReviewSettings from './review';
import SampleSettings from './sample';
import ScheduleSettings from './schedule';
import './index.less';
import { useState } from 'react';

const QualityExamineFirstStep = ({
  recordDetail,
  dictData,
  appcodeOpts,
  templateData,
  isCreate,
  formRef,
}) => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <ProForm.Group>
        <Form.Item
          name={['MasterArgs', 'AppCode']}
          label="应用场景"
          initialValue={recordDetail?.Master?.AppCode || undefined}
          rules={[{ required: true }]}
          style={{ width: '33.33%' }}
        >
          <AutoComplete
            placeholder="请输入"
            style={{ width: '250px' }}
            options={
              [...new Set(appcodeOpts)]?.map((d) => ({
                label: d,
                value: d,
              })) ?? []
            }
          />
        </Form.Item>
        <ProFormText
          name={['MasterArgs', 'Title']}
          label="标题"
          // width="md"
          initialValue={recordDetail?.Master?.Title || undefined}
          placeholder="请输入"
          rules={[{ required: true }]}
          colProps={{
            span: 8,
          }}
        />
        <ProFormSelect
          name={['MasterArgs', 'DocType']}
          label="审核对象文档类型"
          initialValue={recordDetail?.Master?.DocType || undefined}
          placeholder="请选择"
          width="sm"
          options={dictData.MedicalDocType}
          fieldProps={{
            fieldNames: {
              label: 'Name',
              value: 'Code',
            },
          }}
          rules={[{ required: true }]}
          colProps={{
            span: 8,
          }}
        />
        <ProFormSelect
          name={['MasterArgs', 'RevieweeType']}
          label="被审核对象类型"
          initialValue={recordDetail?.Master?.RevieweeType || undefined}
          placeholder="请选择"
          width="sm"
          options={dictData.RevieweeType}
          fieldProps={{
            fieldNames: {
              label: 'Name',
              value: 'Code',
            },
          }}
          rules={[{ required: true }]}
          colProps={{
            span: 8,
          }}
        />
        <ProFormSelect
          name={['MasterArgs', 'TemplateIds']}
          label="模板"
          initialValue={recordDetail?.Master?.TemplateIds || undefined}
          placeholder="请选择"
          width="sm"
          options={templateData?.map((template) => ({
            label: template.Description,
            value: template.TemplateId,
          }))}
          fieldProps={{
            dropdownMatchSelectWidth: 400,
            open: open,
            onDropdownVisibleChange: (visible) => setOpen(visible),
            dropdownRender: (menu) => {
              return (
                <UniTable
                  id="templateTable"
                  rowKey="TemplateId"
                  style={{ width: 400 }}
                  scroll={{ y: 300 }}
                  showHeader={true}
                  columns={[
                    {
                      dataIndex: 'TemplateId',
                      visible: false,
                    },
                    {
                      dataIndex: 'AppCode',
                      visible: true,
                      title: 'AppCode',
                    },
                    {
                      dataIndex: 'Description',
                      visible: true,
                      title: 'Description',
                    },
                    {
                      dataIndex: 'FullScore',
                      visible: true,
                      title: 'FullScore',
                    },
                    {
                      dataIndex: 'Expr',
                      visible: true,
                      title: 'Expr',
                    },
                  ]}
                  bordered
                  dataSource={templateData}
                  pagination={false}
                  rowClassName="row_clickable"
                  onRow={(rowRecord) => {
                    return {
                      onClick: (e) => {
                        Emitter.emit(
                          QualityExamineEventConstants.TEMPLATEID_CHANGE,
                          rowRecord,
                        );
                        setOpen(false);
                      },
                    };
                  }}
                />
              );
            },
          }}
          colProps={{
            span: 8,
          }}
        />
        <ProFormSwitch
          name={['MasterArgs', 'IsPublic']}
          label="IsPublic"
          initialValue={recordDetail?.Master?.IsPublic ?? false}
          colProps={{
            span: 4,
          }}
        />
        <ProFormSwitch
          name={['MasterArgs', 'IsValid']}
          label="IsValid"
          initialValue={recordDetail?.Master?.IsValid ?? true}
          colProps={{
            span: 4,
          }}
        />
        <ProFormText
          name={['MasterArgs', 'Description']}
          label="描述"
          // width="md"
          initialValue={recordDetail?.Master?.Title || undefined}
          placeholder="请输入"
          rules={[{ required: true }]}
          colProps={{
            span: 12,
          }}
        />
        <ProFormSelect
          name={['MasterArgs', 'MenuDirectories']}
          label="MenuDirectories"
          initialValue={recordDetail?.Master?.MenuDirectories || undefined}
          placeholder="请输入MenuDirectories"
          fieldProps={{
            mode: 'tags',
            maxTagCount: 5,
          }}
          colProps={{
            span: 6,
          }}
        />
        <ProFormDigit
          name={['MasterArgs', 'MenuSort']}
          label="MenuSort"
          initialValue={recordDetail?.Master?.MenuSort ?? undefined}
          placeholder="请输入MenuSort"
          fieldProps={{
            precision: 0,
          }}
          colProps={{
            span: 6,
          }}
        />
      </ProForm.Group>
      <ScheduleSettings recordDetail={recordDetail} dictData={dictData} />
      <SampleSettings
        recordDetail={recordDetail}
        dictData={dictData}
        isCreate={isCreate}
        formRef={formRef}
      />
      <ReviewSettings
        recordDetail={recordDetail}
        dictData={dictData}
        isCreate={isCreate}
        formRef={formRef}
      />
    </>
  );
};

export default QualityExamineFirstStep;
