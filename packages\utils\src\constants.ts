class Constants {
  static DateFormatType = {
    // 小写的表示后端定义的基本类型
    double: 'double',
    int32: 'int32',
    string: 'string',

    DateTime: 'DateTime',
    Datetime: 'Datetime',
    Boolean: 'Boolean',
    MonthDate: 'MonthDate',

    Date: 'Date',
    DateByDash: 'DateByDash',
    DateHourByDash: 'DateHourByDash',
    DateTimeByDash: 'DateTimeByDash',
    Month: 'Month',
    Decimal: 'Decimal',
    Currency: 'Currency',
    Percent: 'Percent',
    Permillage: 'Permillage',
    String: 'String',
    CurrencyWithoutSuffix: 'CurrencyWithoutSuffix',

    RMB: 'RMB',
  };

  // 传数据时对应的 dict名称 目前不全 只写了些必要的
  // searchOpts做lowercase化
  static SearchOptsDictType = {
    hospcodes: 'Hospital',
    hospcode: 'Hospital',

    clidepts: 'CliDepts',
    clidept: 'CliDepts',

    wards: 'Wards',
    ward: 'Wards',

    insurtypes: 'InsurType',
    insurtype: 'InsurType',
  };
}

export default Constants;
