import { Sorta<PERSON><PERSON>andle } from 'react-sortable-hoc';
import React from 'react';
import {
  PlusOutlined,
  DeleteOutlined,
  AlertOutlined,
  MenuOutlined,
  PlusCircleTwoTone,
  InfoCircleTwoTone,
} from '@ant-design/icons';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { Checkbox, Input, Popconfirm, Tag, Tooltip } from 'antd';
import IcdeSelect from '@/pages/chs/components/icde-select';
import OperationSelect from '@/pages/chs/components/oper-select';
import dayjs from 'dayjs';
import {
  drgsDegreeMap,
  hqmsDegreeMap,
  icdeExtraMap,
  IcdeExtraTagsItem,
  IcdeFieldInput,
  IcdeOperationInputSelector,
  IcdeOperationReadonlyItem,
  IcdeOperCheckbox,
  IcuDurationFieldInput,
  operationExtraMap,
  OperationExtraTagsItem,
  OperationFieldInput,
  OperIcdeExtraMapItem,
} from '@uni/grid/src/components/icde-oper-input/input';
import DateSelect from '@uni/grid/src/components/date-select';
import IconBtn from '@uni/components/src/iconBtn';
import { employeeDataSourceProcessor } from '@/pages/chs/utils';

const tableOnlyAddIconTrigger =
  (window as any).externalConfig?.['chs']?.tableOnlyAddIconTrigger ?? false;

const tableDeleteAnyway =
  (window as any).externalConfig?.['chs']?.tableDeleteAnyway ?? false;

const srcIcdeOperShow =
  (window as any).externalConfig?.['chs']?.srcIcdeOperShow ?? false;

const nonAddCell = (record, index) => {
  if (record?.id === 'ADD') {
    return {
      colSpan: 0,
    };
  }

  return {};
};

const DragHandler = (node) => {
  return true
    ? SortableHandle(() => <div className={'grab-handle'}>{node}</div>)
    : () => node;
};

interface ExtraTitlePromptItem {
  [key: string]: any;
}

const extraTitle = (
  extraMap?: { [key: string]: OperIcdeExtraMapItem },
  prompts?: ExtraTitlePromptItem,
) => {
  return (
    <Tooltip title={extraTitlePrompt(prompts, extraMap)}>
      <span>
        注<InfoCircleTwoTone style={{ marginLeft: 3 }} />
      </span>
    </Tooltip>
  );
};

const extraTitlePrompt = (
  prompts: ExtraTitlePromptItem,
  extraMap: { [key: string]: OperIcdeExtraMapItem },
) => {
  return (
    <>
      {Object.keys(extraMap)?.map((key, index) => {
        if (key === 'HqmsDegree') {
          return (
            <>
              {Object.keys(hqmsDegreeMap)
                ?.slice(-1)
                ?.map((hqmsKey, index) => {
                  return (
                    <PromptItem
                      color={hqmsDegreeMap?.[hqmsKey]?.color}
                      display={hqmsDegreeMap?.[hqmsKey]?.display}
                      prompt={
                        prompts?.[key]?.prompt ??
                        hqmsDegreeMap?.[hqmsKey]?.label
                      }
                      extraLine={true}
                    />
                  );
                })}
            </>
          );
        }

        if (key === 'DrgsDegree') {
          return (
            <>
              {Object.keys(drgsDegreeMap)
                ?.slice(-1)
                ?.map((drgsKey, index) => {
                  return (
                    <PromptItem
                      color={drgsDegreeMap?.[drgsKey]?.color}
                      display={drgsDegreeMap?.[drgsKey]?.display}
                      prompt={
                        prompts?.[key]?.prompt ??
                        drgsDegreeMap?.[drgsKey]?.label
                      }
                      extraLine={
                        index !== Object.keys(drgsDegreeMap)?.length - 1
                      }
                    />
                  );
                })}
            </>
          );
        }
        return (
          <PromptItem
            color={extraMap?.[key]?.color}
            display={extraMap?.[key]?.display}
            prompt={prompts?.[key]?.prompt ?? extraMap?.[key]?.label}
            extraLine={index !== Object.keys(extraMap)?.length - 1}
          />
        );
      })}
    </>
  );
};

const PromptItem = ({ color, display, prompt, extraLine }) => {
  return (
    <>
      <Tag
        style={{ margin: '5px 5px', border: 'none', borderRadius: 4 }}
        color={color}
      >
        {display}
      </Tag>
      <span>{prompt}</span>
      {extraLine && <br />}
    </>
  );
};

export const icdeColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcdeExtra',
    title: extraTitle(icdeExtraMap, {
      InsurIsObsolete: {
        prompt: '医保置灰',
      },
    }),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: '5%',
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.CHS_ICDE_ADD);
                  }
                : undefined
            }
          >
            <div
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true ? { cursor: 'pointer' } : {}
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_ICDE_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        if (record?.id !== 'ADD') {
          return (
            <IcdeExtraTagsItem
              value={record?.['IcdeExtra']}
              nameKey={'IcdeExtra'}
            />
          );
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 8,
        };
      }

      return {};
    },
  },
  {
    key: 'sort',
    dataIndex: 'IcdeSort',
    title: '序',
    visible: true,
    align: 'center',
    width: '8%',
    fixed: 'left',
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      let labelNode = null;
      if (index === 0) {
        labelNode = <span>主</span>;
      } else {
        // labelNode = <span>{`次要诊断${index}`}</span>;
        labelNode = <span style={{ whiteSpace: 'nowrap' }}>{`${index}`}</span>;
      }
      if (record?.id !== 'ADD') {
        const SortDragHandler = DragHandler(labelNode);
        return <SortDragHandler />;
      }
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeCode',
    title: '诊断编码',
    align: 'center',
    fixed: 'left',
    visible: true,
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      return (
        <IcdeSelect
          tableId={'diagnosisTable'}
          componentId={`IcdeCode#${index}`}
          paramKey={'OutHospital'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsDscg'}
          instantSelect={true}
          // selectFormKey={`IcdeCode#${index}`}
          formKeys={{
            IcdeName: 'Name',
            IcdeCode: 'Code',
            InsurName: 'InsurName',
            InsurCode: 'InsurCode',
            HqmsName: 'HqmsName',
            HqmsCode: 'HqmsCode',
            IcdeExtra: 'IcdeExtra',
            // 首页诊断，编辑时清空
            SrcIcdeCode: 'SrcIcdeCode',
            SrcIcdeName: 'SrcIcdeName',
          }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeName',
    title: '诊断名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IsMain',
    title: '主诊',
    visible: true,
    width: 50,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex) => {
      return (
        <IcdeOperCheckbox
          id={`formItem#IsMain#${index}#IcdeTable`}
          recordId={record?.id}
          dataIndex={dataIndex}
          onChangeExtra={(checked) => {
            Emitter.emit(EventConstant.CHS_ICDE_INSURE_MAIN, {
              id: record?.id,
              values: {
                IsMain: checked,
              },
              index: index,
            });
          }}
        />
      );
    },
  },
  {
    dataIndex: 'IcdeCond',
    title: '入院病情',
    visible: true,
    align: 'center',
    width: '10%',
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeCond'}
          index={index}
          conditionDictionaryKey={'RYBQ'}
          conditionDictionaryGroup={'Insur'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'SrcIcdeCode',
    title: '首页编码',
    visible: srcIcdeOperShow,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'SrcIcdeName',
    title: '首页名称',
    visible: srcIcdeOperShow,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeNote',
    title: '诊断描述',
    visible: true,
    width: '15%',
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeFieldInput
          dataIndex={'IcdeNote'}
          index={index}
          tableId={'icde-table'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeOutcome',
    title: '治疗情况',
    visible: false,
    align: 'center',
    width: '15%',
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeOutcome'}
          index={index}
          listHeight={130}
          conditionDictionaryKey={'IcdeOutcome'}
          conditionDictionaryGroup={'Insur'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    readonly: true,
    width: '5%',
    render: (node, record, index, action) => {
      return (
        <>
          <IconBtn
            btnDisabled={
              tableDeleteAnyway === true
                ? false
                : record?.['addByUser'] !== true
            }
            type="delete"
            onClick={() => {
              Emitter.emit(EventConstant.CHS_ICDE_DELETE, index);
            }}
          />
        </>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

const OperationDragHandle = SortableHandle(() => (
  <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
));
export const operationColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'OperExtra',
    title: extraTitle(operationExtraMap, {
      InsurIsObsolete: {
        prompt: '医保置灰',
      },
      IsMicro: {
        prompt: '微创手术',
      },
      HqmsDegree: {
        prompt: '国考手术等级',
      },
      DrgsDegree: {
        prompt: 'DRG手术等级',
      },
    }),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 60,
    readonly: true,
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'operation-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.CHS_OPER_ADD);
                  }
                : undefined
            }
          >
            <div
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true ? { cursor: 'pointer' } : {}
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_OPER_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        if (record?.id !== 'ADD') {
          return (
            <OperationExtraTagsItem
              eventName={`${EventConstant.CHS_OPER_SELECT_ADD}#${record?.id}`}
              nameKey={'OperExtra'}
              conditionDictionaryKey={'SSJB'}
              conditionDictionaryGroup={'Insur'}
              form={form}
            />
          );
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 14,
        };
      }
      return {};
    },
  },
  {
    key: 'sort',
    dataIndex: 'OperSort',
    title: '序',
    visible: true,
    align: 'center',
    fixed: 'left',
    readonly: true,
    width: 70,
    render: (node, record, index, action) => {
      let labelNode = (
        <span
          style={{ whiteSpace: 'nowrap' }}
          className={'operation-index'}
        >{`${index + 1}`}</span>
      );
      const SortDragHandler = DragHandler(labelNode);
      return <SortDragHandler />;
    },
  },
  {
    dataIndex: 'HqmsDegree',
    title: '国考手术级别',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'DrgsDegree',
    title: 'DRG手术级别',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'OperGroupNo',
    title: '台次',
    visible: false,
    align: 'center',
    width: 100,
    renderColumnFormItem: (node, record, index) => {
      return <OperationFieldInput dataIndex={'OperGroupNo'} index={index} />;
    },
  },
  {
    dataIndex: 'OperType',
    title: '手术类型',
    visible: true,
    align: 'center',
    width: 100,
    fixed: 'left',
    renderColumnFormItem: (node, record, index) => {
      console.log('OperType render');
      return (
        <IcdeOperationReadonlyItem
          className={'dmr-oper-type'}
          conditionDictionaryKey={'OperType'}
          conditionDictionaryGroup={'Insur'}
        />
      );
    },
  },
  {
    dataIndex: 'OperCode',
    title: '手术及操作编码',
    visible: true,
    fixed: 'left',
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      return (
        <OperationSelect
          tableId={'operationTable'}
          componentId={`OperCode#${index}`}
          interfaceUrl={'Api/Insur/InsurSearch/Oper'}
          rowDataKey={record['id']}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          parentId={'operation-table-content'}
          formKeys={{
            OperName: 'Name',
            OperCode: 'Code',
            InsurName: 'InsurName',
            InsurCode: 'InsurCode',
            HqmsName: 'HqmsName',
            HqmsCode: 'HqmsCode',
            OperRate: 'Degree',
            OperType: 'OperType',
            OperExtra: 'OperExtra',
            HqmsDegree: 'HqmsDegree',
            DrgsDegree: 'DrgsDegree',
            RowClassName: 'RowClassName',
            // 首页手术，编辑时清空
            SrcOperCode: 'SrcOperCode',
            SrcOperName: 'SrcOperName',
          }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
        />
      );
    },
  },
  {
    dataIndex: 'OperName',
    title: '手术及操作名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem className={'dmr-oper-name'} />;
    },
  },

  {
    dataIndex: 'OprnOprtBegntime',
    title: '手术及操作日期',
    visible: true,
    align: 'center',
    width: 166,
    renderColumnFormItem: (node, record, index) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#OprnOprtBegntime#${index}#CompactInput`}
          formKey={'OprnOprtBegntime'}
          dataTableIndex={index}
          bordered={true}
          showHours={true}
          showMinutes={true}
          showSeconds={true}
        />
      );
    },
  },
  {
    dataIndex: 'OperRate',
    title: '手术级别',
    visible: true,
    align: 'center',
    width: 100,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationReadonlyItem
          conditionDictionaryKey={'SSJB'}
          conditionDictionaryGroup={'Insur'}
        />
      );
    },
  },
  {
    dataIndex: 'IsMain',
    title: '主手术',
    visible: true,
    width: 70,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      return (
        <IcdeOperCheckbox
          id={`IsMain#${index}#OperTable`}
          recordId={record?.id}
          dataIndex={dataIndex}
          onChangeExtra={(checked) => {
            Emitter.emit(EventConstant.CHS_OPER_INSURE_MAIN, {
              id: record?.id,
              values: {
                IsMain: checked,
              },
              index: index,
            });
          }}
        />
      );
    },
  },
  {
    dataIndex: 'Operator',
    title: '手术者',
    visible: true,
    width: 80,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'Operator'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
        />
      );
    },
  },
  {
    dataIndex: 'Firstasst',
    title: 'Ⅰ助',
    visible: true,
    width: 80,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'Firstasst'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
        />
      );
    },
  },
  {
    dataIndex: 'Secondasst',
    title: 'Ⅱ助',
    visible: true,
    width: 80,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'Secondasst'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
        />
      );
    },
  },
  {
    dataIndex: 'WoundHealingRateClass',
    title: '手术切口愈合等级',
    visible: true,
    width: 80,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'WoundHealingRateClass'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'QKYHLB'}
          conditionDictionaryGroup={'Insur'}
        />
      );
    },
  },
  {
    dataIndex: 'AnaType',
    title: '麻醉方式',
    visible: true,
    width: 80,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'AnaType'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'MZFS'}
          conditionDictionaryGroup={'Insur'}
          dropdownMatchSelectWidth={false}
        />
      );
    },
  },
  {
    dataIndex: 'AnaDoc',
    title: '麻醉医师',
    visible: true,
    align: 'center',
    width: 80,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationTable'}
          className={'operation-input'}
          dataIndex={'AnaDoc'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          leftOneAutoSelect={true}
        />
      );
    },
  },
  {
    dataIndex: 'SrcOperCode',
    title: '首页编码',
    visible: srcIcdeOperShow,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem className={'dmr-oper-name'} />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'SrcOperName',
    title: '首页名称',
    visible: srcIcdeOperShow,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem className={'dmr-oper-name'} />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: 70,
    render: (node, record, index) => {
      return (
        <div className={'operation-item'}>
          <IconBtn
            type="copy"
            onClick={() => {
              Emitter.emit(EventConstant.CHS_OPER_COPY, index);
            }}
          />
          <IconBtn
            btnDisabled={
              tableDeleteAnyway === true
                ? false
                : record?.['addByUser'] !== true
            }
            type="delete"
            onClick={() => {
              Emitter.emit(EventConstant.CHS_OPER_DELETE, index);
            }}
          />
        </div>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

export const dmrSearchColumns = [
  {
    dataIndex: 'status',
    visible: true,
    width: 30,
    align: 'center',
    title: '',
    orderable: false,
    order: 1,
    render: (node, record, index) => {
      if (
        record?.CardStatusName === null ||
        record?.CardStatusName === undefined
      )
        return record?.CardStatusName;
      switch (record?.CardStatusName) {
        case '未登记':
          return (
            <Tooltip title={'未登记'}>
              <span className="font-warning">
                <AlertOutlined />
              </span>
            </Tooltip>
          );
        case '已登记':
          return (
            <Tooltip title={record?.CardStatusName}>
              <span className="font-success">
                <AlertOutlined />
              </span>
            </Tooltip>
          );
        default:
          return node;
      }
    },
  },
  {
    dataIndex: 'HospCode',
    visible: false,
  },
  {
    dataIndex: 'PatNo',
    title: '病案号',
    width: 80,
    visible: true,
  },
  {
    dataIndex: 'SetlBegnDate',
    title: '结算开始日期',
    width: 110,
    visible: false,
    render: (node, record, index) => {
      if (node) {
        let date = dayjs(record['SetlBegnDate']);
        if (date.isValid()) {
          return <span>{date.format('YYYY-MM-DD')}</span>;
        }
      }

      return <span>-</span>;
    },
  },
  {
    dataIndex: 'SetlEndDate',
    title: '结算结束日期',
    width: 110,
    visible: false,
    render: (node, record, index) => {
      if (node) {
        let date = dayjs(record['SetlEndDate']);
        if (date.isValid()) {
          return <span>{date.format('YYYY-MM-DD')}</span>;
        }
      }

      return <span>-</span>;
    },
  },
  {
    dataIndex: 'OutDate',
    width: 90,
    visible: true,
    render: (node, record, index, action) => {
      if (node === null || node === undefined) return node;
      return dayjs(record['OutDate']).format('YYYY-MM-DD');
    },
  },
  {
    dataIndex: 'InDate',
    visible: false,
  },
  {
    dataIndex: 'PatSex',
    visible: false,
  },
  {
    dataIndex: 'PatName',
    title: '姓名',
    width: 60,
    visible: true,
  },
  {
    dataIndex: 'CliDept',
    title: '科室',
    visible: true,
  },
  {
    dataIndex: 'CardStatusName',
    visible: false,
  },
  {
    dataIndex: 'PerfDept',
    visible: false,
  },
  {
    dataIndex: 'MedTeam',
    visible: false,
  },
  {
    dataIndex: 'Director',
    visible: false,
  },
  {
    dataIndex: 'Chief',
    visible: false,
  },
  {
    dataIndex: 'Resident',
    visible: false,
  },
  {
    dataIndex: 'Attending',
    visible: false,
  },
  {
    dataIndex: 'IsLocked',
    visible: false,
  },
];

export const icuColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcuSort',
    title: '',
    visible: true,
    align: 'center',
    width: 40,
    readonly: true,
    render: (node, record, index) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'operation-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.CHS_ICU_ADD);
                  }
                : undefined
            }
          >
            <div
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true ? { cursor: 'pointer' } : {}
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_ICU_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        if (record?.id !== 'ADD') {
          return <OperationDragHandle />;
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 5,
        };
      }

      return {};
    },
  },
  {
    dataIndex: 'InpoolIcuTime',
    title: '进重症监护室时间',
    visible: true,
    width: 250,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return (
        <DateSelect
          formItemId={`formItem#InpoolIcuTime#${index}#CompactInput`}
          type={'compact'}
          formKey={'InpoolIcuTime'}
          dataTableIndex={index}
          bordered={true}
          showHours={true}
          showMinutes={true}
          showSeconds={true}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'OutIcuTime',
    title: '出重症监护室时间',
    visible: true,
    width: 250,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return (
        <DateSelect
          formItemId={`formItem#OutIcuTime#${index}#CompactInput`}
          type={'compact'}
          formKey={'OutIcuTime'}
          dataTableIndex={index}
          bordered={true}
          showHours={true}
          showMinutes={true}
          showSeconds={true}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcuCategory',
    title: '监护类型',
    visible: true,
    align: 'center',
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          tableId={'icuTable'}
          className={'in-hospital-diagnosis'}
          dataIndex={'IcuCategory'}
          index={index}
          conditionDictionaryKey={'IcuCategory'}
          conditionDictionaryGroup={'Insur'}
          listHeight={130}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcuCode',
    title: '重症监护病房类型',
    visible: true,
    align: 'center',
    width: 180,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          tableId={'icuTable'}
          className={'in-hospital-diagnosis'}
          dataIndex={'IcuCode'}
          index={index}
          conditionDictionaryKey={'IcuType'}
          conditionDictionaryGroup={'Insur'}
          listHeight={130}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcuDuration',
    title: '重症监护使用时长（小时）',
    visible: true,
    width: 150,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcuDurationFieldInput
          className={'in-hospital-diagnosis'}
          dataIndex={'IcuDuration'}
          index={index}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: 40,
    readonly: true,
    render: (node, record, index) => {
      return (
        <IconBtn
          type="delete"
          onClick={() => {
            Emitter.emit(EventConstant.CHS_ICU_DELETE, index);
          }}
        />
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

export const bloodColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'BloodSort',
    title: '',
    visible: true,
    align: 'center',
    width: 40,
    render: (node, record, index) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'operation-add-container'}
            onClick={() => {
              // 新增传空对象
              Emitter.emit(EventConstant.CHS_BLOOD_ADD);
            }}
          >
            <PlusCircleTwoTone />
            <span>新增</span>
          </div>
        );
      } else {
        if (record?.id !== 'ADD') {
          return <OperationDragHandle />;
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 4,
        };
      }

      return {};
    },
  },
  {
    dataIndex: 'BldCat',
    title: '输血品种',
    visible: true,
    align: 'center',
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return (
        <IcdeOperationInputSelector
          className={'in-hospital-diagnosis'}
          dataIndex={'BldCat'}
          index={index}
          conditionDictionaryKey={'BldCat'}
          conditionDictionaryGroup={'Insur'}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'BldAmt',
    title: '输血量',
    visible: true,
    align: 'center',
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <OperationFieldInput dataIndex={'BldAmt'} index={index} />;
    },
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: 40,
    readonly: true,
    render: (node, record, index) => {
      return (
        <IconBtn
          type="delete"
          onClick={() => {
            Emitter.emit(EventConstant.CHS_BLOOD_DELETE, index);
          }}
        />
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

export const chargesColumns = [
  {
    dataIndex: 'MedChrgitm',
    title: '项目名称',
    visible: true,
    align: 'center',
    width: 200,
    dictionaryModule: 'MedChrgitm',
    dictionaryModuleGroup: 'Insur',
  },
  {
    dataIndex: 'Amt',
    title: '金额',
    visible: true,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return (
        <OperationFieldInput
          containerClassName={'fee-input'}
          dataIndex={'Amt'}
          index={index}
        />
      );
    },
  },
  {
    dataIndex: 'ClaaSumfee',
    title: '甲类',
    visible: true,
    align: 'center',
    width: 100,
    renderColumnFormItem: (node, record, index) => {
      return (
        <OperationFieldInput
          containerClassName={'fee-input'}
          dataIndex={'ClaaSumfee'}
          index={index}
        />
      );
    },
  },
  {
    dataIndex: 'ClabAmt',
    title: '乙类',
    visible: true,
    align: 'center',
    width: 100,
    renderColumnFormItem: (node, record, index) => {
      return (
        <OperationFieldInput
          containerClassName={'fee-input'}
          dataIndex={'ClabAmt'}
          index={index}
        />
      );
    },
  },
  {
    dataIndex: 'FulamtOwnpayAmt',
    title: '自费',
    visible: true,
    align: 'center',
    width: 100,
    renderColumnFormItem: (node, record, index) => {
      return (
        <OperationFieldInput
          containerClassName={'fee-input'}
          dataIndex={'FulamtOwnpayAmt'}
          index={index}
        />
      );
    },
  },
  {
    dataIndex: 'OthAmt',
    title: '其他',
    visible: true,
    align: 'center',
    width: 100,
    renderColumnFormItem: (node, record, index) => {
      return (
        <OperationFieldInput
          containerClassName={'fee-input'}
          dataIndex={'OthAmt'}
          index={index}
        />
      );
    },
  },
];

export const paymentColumns = [
  {
    dataIndex: 'FundPayType',
    title: '基金支付类型',
    visible: true,
    align: 'center',
    width: 200,
  },
  {
    dataIndex: 'FundPayamt',
    title: '基金支付金额',
    visible: true,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return <OperationFieldInput dataIndex={'AmFundPayamtt'} index={index} />;
    },
  },
];
