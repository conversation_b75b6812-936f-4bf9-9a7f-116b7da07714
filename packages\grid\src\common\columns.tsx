import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { PlusCircleTwoTone } from '@ant-design/icons';
import UniDmrSelect from '../components/dmr-select/UniDmrSelect';
import DateSelect from '../components/date-select';
import { Popconfirm } from 'antd';
import { nonAddCell } from './index';
import { getArrowUpDownEventKey } from '../utils';
import { DepartmentTransferFieldInput } from '../components/department-transfer';
import { IcdeOperationInputSelector } from '../components/icde-oper-input/input';
import React from 'react';
import { SortableHandle } from 'react-sortable-hoc';

const DragHandler = (node) => {
  return SortableHandle(() => <div className={'grab-handle'}>{node}</div>);
};

export const departmentTransferColumns = (tableOnlyAddIconTrigger: boolean) => [
  {
    key: 'sort',
    dataIndex: 'TransferSort',
    title: '',
    visible: true,
    align: 'center',
    readonly: true,
    width: 40,
    render: (node, record, index, action) => {
      if (record?.id !== 'ADD') {
        let labelNode = (
          <span className={'operation-index'}>{`${index + 1}`}</span>
        );
        const SortDragHandler = DragHandler(labelNode);
        return <SortDragHandler />;
      } else {
        return (
          <div
            className={'department-transfer-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER);
                  }
                : undefined
            }
          >
            <div
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true ? { cursor: 'pointer' } : {}
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 6,
        };
      }

      return {};
    },
  },
  {
    dataIndex: 'OutCliDept',
    title: '转出科室',
    visible: true,
    width: 155,
    align: 'center',
    conditionDictionaryKey: 'CliDepts',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'departmentTransferTable'}
          dataIndex={dataIndex}
          index={index}
          conditionDictionaryKey={'CliDepts'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InCliDept',
    title: '转入科室',
    visible: true,
    width: 155,
    align: 'center',
    conditionDictionaryKey: 'CliDepts',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'departmentTransferTable'}
          dataIndex={dataIndex}
          index={index}
          conditionDictionaryKey={'CliDepts'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'TransferInDate',
    title: '转入日期',
    visible: false,
    width: 150,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          formItemId={`formItem#TransferInDate#${index}#DepartmentTransfer`}
          type={'compact'}
          formKey={'TransferInDate'}
          dataTableIndex={index}
          bordered={true}
          onChange={(value) => {
            Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER_INPUT_ADD, {
              value: value,
              key: 'TransferInDate',
              index: index,
            });
          }}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? false}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? false}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? false}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'TransferOutDate',
    title: '转出日期',
    visible: true,
    width: 150,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          formItemId={`formItem#TransferOutDate#${index}#DepartmentTransfer`}
          type={'compact'}
          formKey={'TransferOutDate'}
          dataTableIndex={index}
          bordered={true}
          onChange={(value) => {
            Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER_INPUT_ADD, {
              value: value,
              key: 'TransferOutDate',
              index: index,
            });
          }}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? false}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? false}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? false}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InDeptHours',
    title: '天数',
    visible: true,
    width: 100,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return (
        <DepartmentTransferFieldInput formKey={'InDeptHours'} index={index} />
      );
    },
    onCell: nonAddCell,
  },
  {
    title: '操作',
    visible: true,
    align: 'center',
    width: 60,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      return (
        <Popconfirm
          title="确定删除？"
          onConfirm={() => {
            Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER_DELETE, index);
          }}
        >
          <a>删除</a>
        </Popconfirm>
      );
    },
    onCell: nonAddCell,
  },
];
