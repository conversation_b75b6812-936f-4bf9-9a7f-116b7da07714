@import '~@uni/commons/src/style/variables.less';
@import './theme/index';
@import '~@uni/grid/src/style/view-mode';

.dmr-diff-container {
  background: #dfffed !important;
  background-color: #dfffed !important;

  td {
    background: #dfffed !important;
    background-color: #dfffed !important;
  }

  input {
    color: #a3f1af;
  }
}

.dmr-container-common {
  transition: width 0.5s ease, height 0.5s ease;
  display: flex;
  flex-direction: column;

  .dmr-root-container,
  .dmr-content-container {
    transition: width 0.5s ease, height 0.5s ease;
  }
}

.dmr-normal-container {
  height: 100%;
  width: 100%;
}

.dmr-fullscreen-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 900;
  background: #ffffff;
  padding-bottom: 10px;

  .ant-card {
    height: calc(100% - 25px);
    width: calc(100% - 20px);
    margin: 25px 0px 0px 20px;
  }
}

.dmr-container-common .uni-drag-edit-table-container {
  .ant-table-header .ant-table-cell {
    text-align: center;
  }

  //td.ant-table-cell-row-hover {
  //  background: var(--form-background-color);
  //}

  .ant-table.ant-table-small .ant-table-tbody > tr > td {
    padding: 0px 4px !important;

    .ant-form-item {
      margin-block: 0px !important;
    }

    .ant-form-item-control-input {
      min-height: auto;
    }
  }

  .ant-table.ant-table-small .ant-table-thead > tr > th {
    padding: 4px 4px !important;
  }
}

.border-bottom-light {
  // border-bottom: 0px solid @border-color;
  border-bottom: 1px solid transparent;

  // 先行去掉hover
  &:focus,
  &:active {
    // border-bottom: 1px solid @border-color;
    border-bottom: 1px solid transparent;
  }
}

.dmr-root-operation-header {
  width: 100%;
  display: flex;
  //flex-direction: row;
  flex-flow: row wrap;
  align-items: center;
  justify-content: space-between;
  padding: 10px 10px 10px 90px;
  margin-bottom: 5px;
  row-gap: 8px;
  //height: 50px;

  .title {
    font-size: 30px;
    font-weight: bold;
  }

  .operation-icon-custom-stroke {
    svg {
      circle,
      ellipse,
      rect,
      path {
        stroke: var(--customStrokeColor) !important;
      }
    }
  }

  .operation-item {
    &:not(:last-child) {
      margin-right: 10px;
    }
    .anticon {
      svg {
        height: 1em;
        width: 1em;
      }
    }
    &:hover,
    &:focus {
      .anticon {
        svg {
          circle,
          ellipse,
          rect,
          path {
            stroke: #40a9ff;
          }
        }
      }
    }
    &:not(disabled) {
      .anticon {
        svg {
          circle,
          ellipse,
          rect,
          path {
            stroke: rgba(0, 0, 0, 0.85);
          }
        }
      }
    }
    &[disabled] {
      .anticon {
        svg {
          circle,
          ellipse,
          rect,
          path {
            stroke: rgba(0, 0, 0, 0.25);
          }
        }
      }
    }
  }
}

.dmr-root-container {
  display: flex;
  flex-direction: row;
  //height: calc(100% - 56px - 10px);
  height: 100%;
  background: white;
  position: relative;
  border-radius: 10px;

  // scrollbar
  ::-webkit-scrollbar {
    margin-left: 5px;
    width: 10px;
    background-color: transparent;
    position: absolute;
    z-index: 100;
    right: 0;
  }
  ::-webkit-scrollbar:horizontal {
    height: 10px;
    bottom: 0;
  }
  ::-webkit-scrollbar-thumb {
    position: absolute;
    z-index: 101;
    border-radius: 20px;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
    background-color: rgb(189, 189, 189);
  }
  ::-webkit-scrollbar-thumb:horizontal {
    border-radius: 20px;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
    background-color: rgb(189, 189, 189);
  }
  ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    background-color: #f5f5f5;
  }

  //loading
  .ant-spin-nested-loading {
    width: 100%;

    .ant-spin-container {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .dmr-search-container {
    //position: absolute;
    //height: 100%;
    height: 100%;
    width: 100%;
  }

  .dmr-search-drawer-container {
    position: absolute;

    .ant-drawer-body {
      padding: 10px;
      background: #fcfcfc;
    }

    .ant-drawer-mask {
      background: rgba(0, 0, 0, 0);
    }

    .ant-drawer-header {
      display: none;
    }
  }
}

.dmr-left-container {
  display: flex;
  flex-direction: row;
  //transition: width 1s;
  position: absolute;
  top: 0px;
  left: 0px;

  .left-content {
    width: calc(100% - 30px);
    display: flex;
    flex: 1;
    flex-direction: column;
    // padding: 10px 10px;
    border-right: 1px solid @card-border-color;
  }

  .search-collapsed {
    display: flex;
    background: @blue-color;
    color: white;
    height: 80px;
    letter-spacing: 2px;
    writing-mode: vertical-lr;
    align-items: center;
    justify-content: center;
    width: 30px;
    border-top-right-radius: @border-radius-md;
    border-bottom-right-radius: @border-radius-md;
    cursor: pointer;
  }
}

.dmr-left-trigger-container {
  z-index: 1;
  display: flex;
  flex-direction: column;
  // width: 30px;
  margin-top: 10px;

  .result-collapsed {
    display: flex;
    background: @blue-color;
    color: white;
    height: 30px;
    letter-spacing: 2px;
    // writing-mode: vertical-lr;
    align-items: center;
    justify-content: center;
    width: 60px;
    border-top-right-radius: @border-radius-sm;
    border-bottom-right-radius: @border-radius-sm;
    cursor: pointer;
  }
}

.dmr-right-trigger-container {
  display: flex;
  flex-direction: column;
  // width: 30px;
  margin-top: 50px;

  .result-collapsed {
    display: flex;
    background: @yellow-color;
    color: white;
    height: 30px;
    letter-spacing: 2px;
    writing-mode: vertical-lr;
    align-items: center;
    justify-content: center;
    width: 30px;
    border-top-left-radius: @border-radius-sm;
    border-bottom-left-radius: @border-radius-sm;
    cursor: pointer;

    svg {
      width: 20px;
      height: 20px;
    }
  }
}

.dmr-right-container {
  width: 350px;
  min-width: 350px;
  display: flex;
  flex-direction: row;
  //transition: width 1s;
  position: relative;
  z-index: 2;

  .right-content {
    flex: 1;
    flex-direction: column;
  }
}

.dmr-right-comment-container {
  width: 350px;
  min-width: 350px;
  display: flex;
  flex-direction: row;
  //transition: width 1s;
  position: relative;
  z-index: 2;

  .right-content {
    flex: 1;
    flex-direction: column;
  }
}

.dmr-bottom-quality-container {
  position: absolute;
  bottom: 10px;
  left: 10px;

  display: flex;
  flex-direction: row;
  align-items: center;
  z-index: 121;

  .trigger-container {
    height: 40px;
    width: 40px;
    border-radius: 50%;
    background: #f4a741;
    cursor: pointer;

    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
}

.content-open {
  opacity: 1;
  width: auto;
  transition: all 0.3s ease;
}

.content-close {
  overflow: hidden;
  width: 0px;
  opacity: 0;
  padding: 0 !important;
  transition: all 0.3s ease;
}

.dmr-loading {
  width: 100%;
  height: 100%;
  background: #ffffff;

  & > .ant-spin-blur {
    opacity: 0;
  }
}

.dmr-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  position: relative;
  transition: width 1s;

  .dmr-main-container-doctor-emr-enable {
    margin-left: 0px !important;
    //margin-right: 55px;
    width: 100% !important;

    .separator-container {
      width: calc(100% - 10px);
    }
  }

  .dmr-main-container {
    //margin-left: 80px;
    //margin-right: 55px;
    //width: calc(100% - 80px);
    width: 100%;
    flex: 1;
    display: flex;
    //overflow-y: auto;
    overflow-y: hidden;
    flex-direction: column;

    fieldset {
      width: calc(100% - 4px);
    }
  }

  .dmr-form-container {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;

    .ant-spin-nested-loading {
      height: 100%;
    }

    // TODO
    // select popup

    .ant-select-selector {
      border: none !important;
      height: 30px !important;
      background-color: transparent !important;
    }

    .ant-select-focused .ant-select-selector,
    .ant-select-selector:focus,
    .ant-select-selector:hover,
    .ant-select-selector:active,
    .ant-select-open .ant-select-selector {
      border: none !important;
      box-shadow: none !important;
    }

    // input disabled
    .ant-input[disabled] {
      color: rgba(0, 0, 0, 0.85) !important;
    }

    // diagnosis-table
    #diagnosis-table-content {
      tr {
        height: auto;
        min-height: 40px;
      }
    }

    .extra-explanation-container {
      display: flex;
      flex-direction: column;
      margin: 10px 20px;
      width: calc(100% - 40px);

      .suffix-items-container {
        --form-label-font-color: rgba(0, 0, 0, 0.85);
      }
    }

    .ant-select-item-option-active:not(.ant-select-item-option-disabled),
    .keyboard-select-item:hover {
      background: rgba(256, 186, 0, 0.4);
    }
  }

  .dmr-header-container {
    display: flex;
    flex-direction: column;
    padding: 0px;
  }

  .dmr-content-container {
    position: relative;
    // margin-right: 10px;
    overflow-y: auto;
    scroll-behavior: smooth;
    margin-bottom: 10px;
  }

  // header
  .dmr-info-header-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .prefix {
      font-weight: normal !important;
    }
  }

  // content

  .react-grid-layout {
    width: 100%;
    position: relative;
  }

  .react-grid-item:not(.react-grid-placeholder) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .react-grid-item.react-grid-placeholder {
    background: rgba(0, 0, 0, 0.6);
  }

  .transparent-item-container {
    background-color: transparent;
    border: 1px solid transparent;
  }

  .grid-item-start-container {
    align-items: flex-start !important;

    > .prefix {
      margin-top: 5px;
    }
  }

  .grid-item-start-column-container {
    align-items: flex-start !important;
    flex-direction: column !important;

    > .prefix {
      margin-top: 5px;
    }
  }

  .grid-stack-item-container {
    height: 100%;
    padding: 5px 10px;
  }

  .grid-stack-item-info-container {
    display: flex;
    width: 100%;
    //height: 100%;
    flex-direction: row;
    align-items: center;
  }

  .grid-stack-item-info-container-has-comment {
    background: #ffd7d7;
    border-left: 3px solid #fa3535;
    border-right: 3px solid #fa3535;
  }

  .grid-stack-item-info-container-has-pre-comment {
    background: #ffd7d7;
    border-left: 3px solid #fa3535;
    border-right: 3px solid #fa3535;
  }

  .grid-item-container {
    display: flex;
    width: 100% !important;
    //height: 100%;
    flex-direction: row;
    align-items: center;

    div,
    span,
    input,
    label {
      font-size: 16px !important;
    }

    .ant-form-item {
      margin-bottom: 0px !important;
    }

    .prefix,
    .label,
    .suffix {
      white-space: nowrap;
      // font-weight: bold;
      margin-right: 5px;
      color: @label-color;
    }

    .input {
      flex: auto;
    }

    .suffix {
      display: block;
      white-space: nowrap;
      margin-left: 5px;
    }
  }

  .separator-container-full-width {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 4px;
    position: absolute;
    width: calc(100% - 10px);
    z-index: 99;

    .separator {
      min-height: 1px;
      max-height: 1px;
      background: @dmr-index-border-color;
      width: 100%;
    }
  }

  .separator-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 4px;
    position: absolute;
    width: calc(100% - 10px);
    z-index: 99;

    .separator {
      min-height: 1px;
      max-height: 1px;
      background: @dmr-index-border-color;
      width: 100%;
    }
  }

  .section-separator {
    width: 1px;
    max-width: 1px;
    min-width: 1px;
    background: #ff0000;
    position: absolute;
    z-index: 120;
  }

  .dmr-header {
    display: flex;
    flex-direction: column;

    .ant-form-item {
      margin: 0;
    }

    .medical-institution-container {
      //display: flex;
      display: none;
      flex-flow: row wrap;
      align-items: center;
      align-self: center;
      max-width: 80%;
      margin-bottom: 0.5rem;

      .item {
        display: flex;
        flex-direction: row;
        align-items: center;

        .label {
          white-space: nowrap;
        }
      }
    }

    .input {
      //padding: 4px 4px;
    }

    .header-title {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .header-label {
      font-size: 1.4rem;
      font-weight: bold;
      letter-spacing: 5px;
    }

    .medical-payment-container {
      display: flex;
      flex-direction: row;
      align-items: center;

      // .label {
      //   white-space: nowrap;
      //   font-size: 18px;
      //   color: #003366;
      // }

      .payment-input {
        width: 200px;
        max-width: 200px;
        // border-bottom: 1px solid @border-color;
      }
    }
  }

  .out-purpose {
    .suffix-input {
      max-width: 100%;
    }
  }

  .allergy-medicine {
    .suffix-input {
      max-width: 100%;
    }
  }

  .department-container {
    //max-width: 180px;
  }

  .ant-select-selector {
    padding: 0px 0px 0px 11px !important;
  }

  // override
  .ant-select-single:not(.ant-select-customize-input)
    .ant-select-selector
    .ant-select-selection-search-input {
    height: 100% !important;
  }
}

// By ZRR
// content
.dmr-content-container {
  .ant-pro-card {
    background: transparent;
  }
}

// border-color
.dmr-form-container,
.baby-content-modal-container {
  .date-select-container .compact-date-container,
  .separate-province-selector-container .selector-container,
  .separate-province-selector-container .detail-address,
  .time-range-container .time-range-item-container .range-input-container input,
  .out-type-container .input-container,
  .out-type-container .select-container,
  .suffix-item-container .suffix-input {
    border-bottom: 0px solid @border-color;

    &:hover {
      border-bottom: 0px solid @border-color;
    }
  }
}

// 公共样式

// 表格内时间输入框
#operationTable,
#icuTable {
  .timescape {
    justify-content: center;
  }
}

.baby-content-modal-container {
  .ant-select-selector {
    border: none !important;
    height: 30px !important;
    background-color: transparent !important;
  }

  .ant-select-focused .ant-select-selector,
  .ant-select-selector:focus,
  .ant-select-selector:hover,
  .ant-select-selector:active,
  .ant-select-open .ant-select-selector {
    border: none !important;
    box-shadow: none !important;
  }

  // input disabled
  .ant-input[disabled] {
    color: rgba(0, 0, 0, 0.85) !important;
  }

  .ant-select-item-option-active:not(.ant-select-item-option-disabled),
  .keyboard-select-item:hover {
    background: rgba(256, 186, 0, 0.4);
  }
}

.table-duplicate-highlight-item {
  background: #ffcc00;
  background-color: #ffcc00;

  td {
    background: #ffcc00 !important;
    background-color: #ffcc00 !important;
  }
}

.table-check-error-highlight-item {
  background: #ffcc00;
  background-color: #ffcc00;

  td {
    background: #ffcc00 !important;
    background-color: #ffcc00 !important;
  }
}

#dmr-content-grid-layout #tanstack-table-container thead .header-item > span,
#dmr-content-grid-layout
  #tanstack-table-container
  thead
  .header-item
  > div.flex-row-center
  > span {
  width: 100%;
}
