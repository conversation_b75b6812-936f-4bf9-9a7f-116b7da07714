import { useEffect } from 'react';
import { Col, Row } from 'antd';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import './index.less';
import { RespVO } from '@uni/commons/src/interfaces';
import _ from 'lodash';
import { hqmsQualityOfEntityRadarOption } from '../chart.opts';
import { useModel } from '@@/plugin-model/useModel';
import CardEchart from '@uni/components/src/cardEchart';
import CardWithBtns from '@uni/components/src/cardWithBtns';
import StatsCard from '@/pages/quality/components/stats-card';
import SingleList from '@/pages/components/single-list';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';

const GradeQuality = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes } = globalState.searchParams;

  useEffect(() => {
    if (dateRange?.length) {
      let tableParams = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
      };
      getBundledGradeQualityOfHospReq(tableParams);
      getQualityByHospReq(tableParams);
      getQualitByCliDeptReq(tableParams);
    }
  }, [dateRange, hospCodes]);

  // 综合能力分析
  const {
    data: bundledGradeQualityOfHospData,
    loading: bundledGradeQualityOfHospLoading,
    run: getBundledGradeQualityOfHospReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Grade/HospGrade/GradeQualityOfHosp`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.Stats[0];
        }
      },
    },
  );

  // 综合能力分析
  const {
    data: bundledGradeQualityOfHospColumns,
    loading: bundledGradeQualityOfHospColumnsLoading,
    run: getBundledGradeQualityOfHospColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Grade/HospGrade/GradeQualityOfHosp`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  // 全院分布
  const {
    data: qualityByHospData,
    loading: getQualityByHospLoading,
    run: getQualityByHospReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Grade/HospGrade/GradeQualityByHosp`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
      },
    },
  );
  const {
    data: qualityByHospColumns,
    loading: getQualityByHospColumnsLoading,
    run: getQualityByHospColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Grade/HospGrade/GradeQualityByHosp`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  // 科室
  const {
    data: qualityByCliDeptData,
    loading: getQualityByCliDeptLoading,
    run: getQualitByCliDeptReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Grade/CliDeptGrade/GradeQualityByCliDept`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
      },
    },
  );
  const {
    data: qualityByCliDeptColumns,
    loading: getQualityByCliDeptColumnsLoading,
    run: getQualitByCliDeptColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Grade/CliDeptGrade/GradeQualityByCliDept`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  useEffect(() => {
    getBundledGradeQualityOfHospColumnsReq();
    getQualityByHospColumnsReq();
    getQualitByCliDeptColumnsReq();
  }, []);

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <StatsCard
                args={{
                  Sdate: dateRange?.at(0),
                  Edate: dateRange?.at(1),
                  HospCode: hospCodes,
                }}
                detailsUrl={'Grade/Details/HospGradeCompositeData'}
                loading={bundledGradeQualityOfHospLoading}
                data={bundledGradeQualityOfHospData}
              ></StatsCard>
            </Col>
          </Row>
        </Col>
        <Col span={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={24} lg={24} xl={10}>
              <CardEchart
                height={250}
                elementId="LineBar"
                loading={bundledGradeQualityOfHospLoading}
                options={
                  (bundledGradeQualityOfHospData &&
                    hqmsQualityOfEntityRadarOption(
                      bundledGradeQualityOfHospData,
                    )) ||
                  {}
                }
                needExport={true}
                exportTitle={'全院医疗质量综合分析'}
                exportData={[bundledGradeQualityOfHospData]}
                exportColumns={bundledGradeQualityOfHospColumns}
              ></CardEchart>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12} xl={7}>
              <CardWithBtns
                content={
                  <>
                    <SingleList
                      loading={getQualityByHospLoading}
                      data={_.map(
                        _.orderBy(qualityByHospData, ['DeathCnt'], 'desc'),
                        (data, idx) => {
                          return {
                            key: idx + 1,
                            name: data?.HospName,
                            value: data?.DeathCnt,
                          };
                        },
                      )}
                    ></SingleList>
                  </>
                }
                needExport={true}
                exportTitle={'全院死亡人次分布'}
                exportData={qualityByHospData}
                exportColumns={qualityByHospColumns}
              ></CardWithBtns>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12} xl={7}>
              <CardWithBtns
                content={
                  <>
                    <SingleList
                      loading={getQualityByCliDeptLoading}
                      data={_.map(
                        _.orderBy(qualityByCliDeptData, ['DeathCnt'], 'desc'),
                        (data, idx) => {
                          return {
                            key: idx + 1,
                            name: data?.CliDeptName,
                            value: data?.DeathCnt,
                          };
                        },
                      )}
                    ></SingleList>
                  </>
                }
                needExport={true}
                exportTitle={'科室死亡人次分布'}
                exportData={qualityByCliDeptData}
                exportColumns={qualityByCliDeptColumns}
              ></CardWithBtns>
            </Col>
          </Row>
        </Col>
      </Row>
    </>
  );
};

export default GradeQuality;
