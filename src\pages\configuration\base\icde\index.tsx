import React from 'react';

import './index.less';
import { Card, Tabs } from 'antd';
import IcdeDictionary from '@/pages/configuration/base/icde/components/icde';
import PathologyIcdeDictionary from '@/pages/configuration/base/icde/components/path-icde';
import TcmIcdeDictionary from '@/pages/configuration/base/icde/components/tcm-icde';

const tabs = [
  {
    key: 'BaseIcde',
    label: '西医诊断',
    children: <IcdeDictionary moduleGroup={'Dmr'} />,
  },
  {
    key: 'PathologyIcde',
    label: '肿瘤形态学编码',
    children: <PathologyIcdeDictionary moduleGroup={'Dmr'} />,
  },
  {
    key: 'TcmIcde',
    label: '中医诊断',
    children: <TcmIcdeDictionary moduleGroup={'Dmr'} />,
  },
];
const IcdeConfiguration = () => {
  const onTabChange = (key: string) => {
    console.log(key);
  };

  return (
    <div
      id={'icde-configuration-container'}
      className={'icde-configuration-container'}
    >
      <Tabs
        defaultActiveKey={tabs?.at(0)?.key}
        // type="card"
        onChange={onTabChange}
        items={tabs}
      />
    </div>
  );
};

export default IcdeConfiguration;
