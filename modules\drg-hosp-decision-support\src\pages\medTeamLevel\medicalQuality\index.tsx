import { useRef, useState } from 'react';
import { useRequest, useModel } from 'umi';
import { Col, Row, Tabs } from 'antd';
import _ from 'lodash';
import './index.less';
import { RespVO } from '@uni/commons/src/interfaces';
import SingleColumnTable from '@/components/SingleColumnTable/index';
import { uniCommonService } from '@uni/services/src/commonService';
import Stats from '@/components/stats/index';
import { DeathNormalStat } from '@/constants';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';
import { useDeepCompareEffect } from 'ahooks';

const MedicalQuality = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, MedTeams } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const isInitialLoad = useRef(true);
  const prevSearchParams = useRef(null);
  const ignoreInitialLoad = useRef(false);

  // 这边调bundleData
  const {
    data: BundledData,
    loading: getBundledDataLoading,
    run: getBundledDataReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/v2/Drgs/MedTeamDeath/BundledDeath', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
      MedTeams,
    };
    // 正常处理逻辑
    setTableParams(params);
    getBundledDataReq(params);
  }, [dateRange, hospCodes, MedTeams]);

  // 处理tableParams
  // useEffect(() => {
  //   let params: any = {
  //     Sdate: dateRange?.at(0),
  //     Edate: dateRange?.at(1),
  //     HospCode: hospCodes,
  //     MedTeams,
  //   };

  //   // 这么写的原因：header form 在首屏加载时会触发多次 相同值的更新 要避免首屏的多次重新调用
  //   // 但是之后要保证用户点击查询时能正常重新获取

  //   // 在初次加载阶段设置一个标志位以忽略多次触发
  //   if (isInitialLoad.current) {
  //     isInitialLoad.current = false;
  //     ignoreInitialLoad.current = true;
  //     setTimeout(() => {
  //       ignoreInitialLoad.current = false;
  //     }, 1000); // 根据具体情况调整延时时间
  //     return;
  //   }

  //   // 如果是首次加载且标志位为true，则跳过逻辑
  //   if (ignoreInitialLoad.current) {
  //     return;
  //   }

  //   // 正常处理逻辑
  //   setTableParams(params);
  //   getBundledDataReq(params);
  // }, [dateRange, hospCodes, MedTeams]);

  let tabItems = [
    {
      key: 'statistic',
      label: '医疗组综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Drgs/MedTeamDeath/BundledDeath`}
              trendApi={'Api/v2/Drgs/MedTeamDeath/DeathTrend'}
              columns={DeathNormalStat}
              defaultSelectItem={'DeathCnt'}
              type="col-xl-6"
              tabKey={activeKey}
              useGlobalState
              tableParams={tableParams}
            />
          </Col>
          <SingleColumnTable
            title="医疗组死亡病例统计"
            args={{
              api: `Api/v2/Drgs/MedTeamDeath/DeathByMedTeam`,
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="MedTeamName"
            type="table"
            colSpan={{ span: 24 }}
            visibleValueKeys={['MedTeamName', 'DeathCnt']}
            chart={{
              api: 'Api/v2/Drgs/MedTeamDeath/ADrgCompositionInDeath',
              title: '死亡病例病种结构',
              type: 'bar',
              valueKeys: ['DeathCnt'],
              category: 'ADrgName',
              yAxis: '死亡人次',
              colSpan: { span: 24 },
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName + '死亡人次',
                args: {
                  ...tableParams,
                  MedTeams: [record?.MedTeam],
                  Dead: true,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
                dictData: dictData, // 传入
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'dept_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <SingleColumnTable
          title="医生死亡病例统计"
          args={{
            api: 'Api/v2/Drgs/DoctorDeath/DeathByDoctor',
          }}
          tableParams={tableParams}
          dictData={dictData}
          type="table"
          visibleValueKeys={[
            'DoctorName',
            'DeathCnt',
            'DeathRatio',
            'SurgeryDeathCnt',
            'SurgeryDeathRatio',
            'PeriOperDeathCnt',
            'PeriOperDeathRatio',
            'NeonateDeathCnt',
            'NeonateDeathRatio',
            'CancerDeathCnt',
            'CancerDeathRatio',
            'LowRiskDeathCnt',
            'LowRiskDeathRatio',
          ]}
          colSpan={{ span: 24 }}
          select={{
            dataKey: 'DoctorType',
            valueKey: 'DoctorType',
            allowClear: false,
            defaultSelect: true,
          }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.DoctorName,
              args: {
                ...tableParams,
                DoctorCodes: [record?.DoctorCode],
                DoctorType: (record?.DoctorType as string)?.toLocaleUpperCase(),
                Dead: true,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default MedicalQuality;
