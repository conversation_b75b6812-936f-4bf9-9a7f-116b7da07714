import { CheckOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { Space, Tooltip } from 'antd';
import IconBtn from '@uni/components/src/iconBtn';
import { EVENT_BORROW_TASK } from './constants';
import { Emitter } from '@uni/utils/src/emitter';

export const BorrowTaskColumns = [
  // {
  //   dataIndex: 'ApplicationId',
  //   data: 'ApplicationId',
  //   visible: true,
  //   title: '清单号',
  // },
  // {
  //   dataIndex: 'BorrowerName',
  //   data: 'BorrowerName',
  //   visible: true,
  //   title: '借阅人',
  // },
  // {
  //   dataIndex: 'BorrowDate',
  //   data: 'BorrowDate',
  //   visible: true,
  //   title: '借阅日期',
  //   dataType: 'date',
  // },
  // {
  //   dataIndex: 'Purpose',
  //   data: 'Purpose',
  //   visible: true,
  //   title: '借阅目的',
  // },
  // {
  //   dataIndex: 'Reason',
  //   data: 'Reason',
  //   visible: true,
  //   title: '申请原因',
  // },
  // {
  //   dataIndex: 'Status',
  //   data: 'Status',
  //   // filterType: 'filter',
  //   visible: true,
  //   title: '申请状态',
  //   description: null,
  //   name: 'Status',
  //   width: null,
  //   exportable: true,
  //   orderable: true,
  //   orderMode: null,
  //   orderPriority: 0,
  //   aggregable: false,
  //   responsivePriority: 10,
  //   className: ' exportable',
  //   columnType: 'String',
  //   dataType: 'String',
  //   precision: null,
  //   scale: null,
  //   dictionaryModule: 'ApplicationStatus',
  //   dictionaryModuleGroup: null,
  //   groupName: [],
  //   shortTitle: null,
  //   shortTitleDescription: null,
  //   isReadOnly: false,
  //   isNullable: false,
  //   order: 7,
  //   pivot: null,
  //   isExtraProperty: false,
  //   unpivot: null,
  //   extraProperties: {
  //     columnTypeExtra: '',
  //   },
  // },
  {
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 90,
    render: (node, record, index, action) => (
      <>
        <Space size={10}>
          {record?.Status === '0' && (
            <Tooltip title={'开始处理'}>
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  Emitter.emit(EVENT_BORROW_TASK.HANDLE_APPLICATION, record);
                }}
              >
                <PlayCircleOutlined />
              </a>
            </Tooltip>
          )}
          {['0', '4', '6', '8', '10'].includes(record?.Status) && (
            <IconBtn
              key="details2"
              type="details2"
              title="病案明细"
              onClick={() => {
                Emitter.emit(EVENT_BORROW_TASK.VIEW_DETAILS, record);
              }}
            />
          )}
          {['4'].includes(record?.Status) && (
            <IconBtn
              key="accept"
              type="accept"
              title="病案准备完成（未处理病案默认可取）"
              openPop={true}
              popOnConfirm={() => {
                Emitter.emit(EVENT_BORROW_TASK.PREPARE_APPLICATION, record);
              }}
            />
          )}
          {['0', '4'].includes(record?.Status) && (
            <IconBtn
              key="destroy"
              type="destroy"
              title="拒绝申请"
              onClick={() => {
                Emitter.emit(EVENT_BORROW_TASK.REJECT_APPLICATION, record);
              }}
            />
          )}
          {record?.Status === '6' && (
            <IconBtn
              key="review"
              type="review"
              title="病案已取"
              onClick={() => {
                Emitter.emit(EVENT_BORROW_TASK.FETCH_APPLICATION, record);
              }}
            />
          )}
          {['10'].includes(record?.Status) && (
            <>
              <IconBtn
                key="print"
                type="print"
                title="借阅打印信息"
                onClick={() => {
                  Emitter.emit(EVENT_BORROW_TASK.PRINT_APPLICATION, record);
                }}
              />
              {!record?.IsReturned && (
                <IconBtn
                  key="undo"
                  type="undo"
                  title="批量归还"
                  openPop={true}
                  popOnConfirm={() => {
                    Emitter.emit(EVENT_BORROW_TASK.BATCH_RETURN, record);
                  }}
                />
              )}
            </>
          )}
        </Space>
      </>
    ),
  },
];
