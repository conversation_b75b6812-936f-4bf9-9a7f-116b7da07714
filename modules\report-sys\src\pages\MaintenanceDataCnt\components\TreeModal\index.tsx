import { Divider, Modal, Radio, RadioChangeEvent, Tree } from 'antd';
import { useState } from 'react';
import { treeData } from './data';

function TreeModal({
  modalOpen,
  onModalCancel,
  onModalOk,
}: {
  modalOpen: boolean;
  onModalCancel: () => void;
  onModalOk: (data: any) => void;
}) {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([
    'DrgsMetrics',
    'HqmsMetrics',
    'GradeMetrics',
  ]);
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  const onCheck = (checkedKeysValue: string[]) => {
    setCheckedKeys(checkedKeysValue);
  };

  const [value, setValue] = useState(1);

  const onChange = (e: RadioChangeEvent) => {
    setValue(e.target.value);
  };

  const onCancel = () => {
    onModalCancel();
    setValue(1);
    setCheckedKeys([]);
  };

  const onOk = async () => {
    const list = checkedKeys?.filter(
      (i) => !['DrgsMetrics', 'HqmsMetrics', 'GradeMetrics']?.includes(i),
    );
    const data = {
      SubFeatures: list,
      UnCalcOnly: value === 1 ? true : false,
    };
    await onModalOk(data);
    setValue(1);
    setCheckedKeys([]);
  };

  return (
    <Modal
      title="分组设置"
      open={modalOpen}
      onCancel={onCancel}
      destroyOnClose={true}
      onOk={onOk}
    >
      <Tree
        checkable
        onExpand={onExpand}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        onCheck={onCheck}
        checkedKeys={checkedKeys}
        selectable={false}
        treeData={treeData}
      />
      <Divider />
      <Radio.Group onChange={onChange} value={value}>
        <Radio value={1}>分组</Radio>
        <Radio value={2}>仅分组未分组</Radio>
      </Radio.Group>
    </Modal>
  );
}

export default TreeModal;
