import _ from 'lodash';
import { useRequest } from 'umi';
import { Tabs, Col, Row } from 'antd';
import { useEffect, useState } from 'react';
import Stats from '../../components/statsWithTrend';
import { useModel } from '@@/plugin-model/useModel';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import DrawerCardInfo from '../../components/drawerCardInfo';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { TotalStatsColumns, TabCommonItems } from '../constants';
import QualityOfEntityRadar from '../components/qualityOfEntityRadar';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import SingleColumnTable from '../../components/singleColumnTable/index';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';

const GradeQuality = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, CliDepts } = searchParams;

  // tab
  const [activeKey, setActiveKey] = useState('major_perf_dept_analysis');

  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // 请求参数
  const [tableParams, setTableParams] = useState(undefined);

  useEffect(() => {
    if (dateRange?.length) {
      let params = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        CliDepts,
      };
      setTableParams(params);
    }
  }, [dateRange, hospCodes, CliDepts]);

  // 综合能力分析
  const {
    data: bundledGradeQualityOfHospColumns,
    loading: bundledGradeQualityOfHospColumnsLoading,
    run: getBundledGradeQualityOfHospColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/v2/Grade/GradeStats/GradeQualityOfCliDept`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  const {
    data: qualityByHospColumns,
    loading: getQualityByHospColumnsLoading,
    run: getQualityByHospColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/v2/Grade/GradeStats/GradeQualityByCliDept`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  const {
    data: qualityByCliDeptColumns,
    loading: getQualityByCliDeptColumnsLoading,
    run: getQualitByCliDeptColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/v2/Grade/GradeStats/GradeQualityByCliDept`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  useEffect(() => {
    getBundledGradeQualityOfHospColumnsReq();
    getQualityByHospColumnsReq();
    getQualitByCliDeptColumnsReq();
  }, []);

  // tabs
  let tabItems = [
    {
      key: TabCommonItems.majorPerfDeptAnalysis.key,
      label: TabCommonItems.majorPerfDeptAnalysis.title,
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={24} xl={16}>
            <Stats
              api={`Api/v2/Grade/GradeStats/GradeQualityOfCliDept`}
              trendApi={`Api/v2/Grade/GradeStats/GradeQualityTrend`}
              columns={TotalStatsColumns}
              type="col-xl-12"
              level="dept"
              tabKey={activeKey}
              chartHeight={320}
              useGlobalState
              defaultSelectItem="DeathCnt"
            />
          </Col>
          <QualityOfEntityRadar
            tableParams={tableParams}
            title="科室医疗质量综合分析"
            api="Api/v2/Grade/GradeStats/GradeQualityOfCliDept"
          />
          <SingleColumnTable
            title="科室死亡人次分布"
            args={{
              api: 'Api/v2/Grade/GradeStats/GradeQualityByCliDept',
            }}
            tableParams={tableParams}
            dictData={dictData}
            type="table"
            orderKey="DeathCnt"
            category="CliDeptName"
            visibleValueKeys={[
              'CliDeptName',
              'PatCnt',
              'DeathCnt',
              'DeathRatio',
              'SurgeryDeathCnt',
              'SurgeryDeathRatio',
              'NeonateDeathCnt',
              'NeonateDeathRatio',
              'GradeLowRiskDeathCnt',
              'GradeLowRiskDeathRatio',
              // 'LowRiskDeathCnt',
              // 'LowRiskDeathRatio',
              'UnexpectedBackInCnt',
              'UnexpectedBackInRatio',
            ]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.CliDeptName + '死亡人次',
                args: {
                  ...tableParams,
                  CliDepts: record?.CliDept ? [record?.CliDept] : null,
                  Dead: true,
                },
                detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: TabCommonItems.medTeamAnalysis.key,
      label: TabCommonItems.medTeamAnalysis.title,
      children: (
        <>
          <Row>
            <SingleColumnTable
              title="医疗组死亡人次分布"
              args={{
                api: 'Api/v2/Grade/GradeStats/GradeQualityByMedTeam',
              }}
              tableParams={tableParams}
              dictData={dictData}
              category="MedTeamName"
              type="table"
              orderKey="DeathCnt"
              visibleValueKeys={[
                'MedTeamName',
                'PatCnt',
                'DeathCnt',
                'DeathRatio',
                'SurgeryDeathCnt',
                'SurgeryDeathRatio',
                'NeonateDeathCnt',
                'NeonateDeathRatio',
                'GradeLowRiskDeathCnt',
                'GradeLowRiskDeathRatio',
                // 'LowRiskDeathCnt',
                // 'LowRiskDeathRatio',
                'UnexpectedBackInCnt',
                'UnexpectedBackInRatio',
              ]}
              colSpan={{ span: 24 }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.MedTeamName + '死亡人次',
                  args: {
                    ...tableParams,
                    MedTeams: [record?.MedTeam],
                    Dead: true,
                  },
                  detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
                  dictData: dictData,
                });
              }}
            />
          </Row>
        </>
      ),
    },
    {
      key: TabCommonItems.doctorAnalysis.key,
      label: TabCommonItems.doctorAnalysis.title,
      children: (
        <>
          <Row>
            <SingleColumnTable
              title="医生死亡人次分布"
              args={{
                api: 'Api/v2/Grade/GradeStats/GradeQualityByDoctor',
              }}
              tableParams={tableParams}
              dictData={dictData}
              category="DoctorName"
              type="table"
              orderKey="DeathCnt"
              visibleValueKeys={[
                'DoctorName',
                'PatCnt',
                'SurgeryPatCnt',
                'DeathCnt',
                'GradeLowRiskDeathCnt',
                'GradeLowRiskDeathRatio',
                'GradeLowRiskPatCnt',
                'DeathRatio',
                'SurgeryDeathCnt',
                'SurgeryDeathRatio',
                'NeonateDeathCnt',
                'NeonateDeathRatio',
                'GradeLowRiskDeathCnt',
                'GradeLowRiskDeathRatio',
                // 'LowRiskDeathCnt',
                // 'LowRiskDeathRatio',
                'UnexpectedBackInCnt',
                'UnexpectedBackInRatio',
              ]}
              colSpan={{ span: 24 }}
              select={{
                dataKey: 'DoctorType',
                valueKey: 'GroupByDoctor',
                allowClear: false,
                defaultSelect: true,
                capitalizeString: true,
              }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.DoctorName + '死亡人次',
                  args: {
                    ...tableParams,
                    DoctorCodes: [record?.DoctorCode],
                    DoctorType: record?.DoctorType,
                    Dead: true,
                  },
                  detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
                  dictData: dictData,
                });
              }}
            />
          </Row>
        </>
      ),
    },
  ];

  return (
    <>
      <div>
        <Tabs
          size="small"
          items={tabItems}
          activeKey={activeKey}
          onChange={(key) => setActiveKey(key)}
        />
        {/* 把modal与drawer的位置提高到这里，统一在这里处理 避免id太多导致匹配混乱 & tab切换后id失效 */}
        {/* TODO DetailTableModal内部的实现方式是destoryOnClose 导致每次打开都要重新获取columns */}
        <DetailTableModal
          dictData={dictData}
          detailAction={(record) => {
            // 这里替代内部 操作 onClick
            setDrawerVisible({
              hisId: record?.HisId,
              type: 'drg',
            });
          }}
        />
        {/* drawer 同理 这样保持复用 因为drawer 监听的是detailTableModal 或者一些table */}
        <DrawerCardInfo
          visible={drawerVisible}
          onClose={() => setDrawerVisible(undefined)}
        />
      </div>
    </>
  );
};

export default GradeQuality;
