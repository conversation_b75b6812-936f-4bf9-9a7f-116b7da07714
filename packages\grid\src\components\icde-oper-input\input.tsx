import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { Checkbox, Form, Input, InputNumber, Tag, Tooltip } from 'antd';
// @ts-ignore
import { useModel } from '@@/plugin-model/useModel';
import {
  FormTableItemBaseProps,
  useDmrDragEditOnlyTableContext,
} from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import {
  getArrowUpDownEventKey,
  getSelectorDropdownContainerNode,
} from '../../utils';
import UniDmrSelect from '../dmr-select/UniDmrSelect';
import { isEmptyValues } from '@uni/utils/src/utils';
import { onReadonlyInputAndReadonlyItemClick } from '../../utils';

const readonlyInputClickSelectAll =
  (window as any).externalConfig?.['dmr']?.readonlyInputClickSelectAll ?? false;

const tableSelectorDropdownHeight = (window as any).externalConfig?.['dmr']
  ?.tableSelectorDropdownHeight;

interface IcdeOperCheckboxProps extends FormTableItemBaseProps {
  id: string;
  recordId: string;
  dataIndex: string;
  onChangeExtra?: (checked: boolean) => void;
  disabled?: boolean;
  dependencyKey?: string;
  dependencyValue?: any;
  form?: any;

  minimumChecked?: number;
}

export const IcdeOperCheckbox = (props: IcdeOperCheckboxProps) => {
  const dependencyFormValue =
    props?.recordId && props?.dependencyKey && props?.form
      ? Form.useWatch([props?.recordId, props?.dependencyKey], props?.form)
      : null;

  const icdeOperCheckboxValue = Form.useWatch(
    [props?.recordId, props?.dataIndex],
    props?.form,
  );

  let reachMinimumChecked = false;
  if (props?.minimumChecked !== undefined && props?.minimumChecked >= 0) {
    const IsReportedTrueCount = Form.useWatch(
      'IsReportedTrueCount',
      props?.form,
    );

    if (IsReportedTrueCount <= props?.minimumChecked) {
      reachMinimumChecked = true;
    }
  }

  return (
    <div
      className={'flex-row-center form-content-item-container'}
      style={{ justifyContent: 'center' }}
    >
      <Checkbox
        id={props?.id}
        checked={icdeOperCheckboxValue}
        disabled={
          (props?.dependencyValue
            ? dependencyFormValue === props?.dependencyValue
            : false) ||
          (reachMinimumChecked && icdeOperCheckboxValue)
        }
        onChange={(event) => {
          props?.onChange && props?.onChange(event?.target?.checked);
          props?.onChangeExtra && props?.onChangeExtra(event?.target?.checked);
        }}
      />
    </div>
  );
};

export interface OperIcdeExtraMapItem {
  display: string;
  label: string;
  color: string;
  className?: string;
  style?: React.CSSProperties;
}

interface OperationFieldInputProps extends FormTableItemBaseProps {
  containerClassName?: string;
  className?: string;
  recordId?: string;
  dataIndex: string;
  index: number;
  disabled?: boolean;
}
export const OperationFieldInput = (props: OperationFieldInputProps) => {
  return (
    <Tooltip title={props?.value}>
      <div
        className={`form-content-item-container ${props?.containerClassName}`}
      >
        <Input
          id={`formItem#${props?.dataIndex}#${props?.index}#Input`}
          className={`operation-input ${props?.className ?? ''}`}
          bordered={false}
          value={props?.value ?? ''}
          placeholder={'请输入'}
          contentEditable={true}
          disabled={props.disabled ?? false}
          onChange={(event) => {
            props?.onChange && props?.onChange(event?.target?.value);
          }}
        />
      </div>
    </Tooltip>
  );
};

interface IcdeOperationInputSelectorProps extends FormTableItemBaseProps {
  className?: string;
  recordId?: string;
  dataIndex: string;
  index: number;

  conditionDictionaryKey?: string;

  conditionDictionaryGroup?: string;

  optionNameKey?: string;
  optionValueKey?: string;
  optionTitleKey?: string;
  optionLabelProp?: string;

  dataSourceProcessor?: (dataSource: any[]) => any[];
  dataSource?: any[];

  disabled?: boolean;

  dropdownStyle?: React.CSSProperties;
  listHeight?: number;

  dropdownMatchSelectWidth?: boolean | number;
  nameFormat?: string;

  tableId?: string;

  onChangeExtra?: (value: any) => void;

  leftOneAutoSelect?: boolean;

  numberSelectItem?: boolean;

  // 表示当前 column item；
  extraItem?: any;

  parentNodeId?: string;
}

export const IcdeOperationInputSelector = (
  props: IcdeOperationInputSelectorProps,
) => {
  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateFromMaster',
  );

  const dmrTableContext = useDmrDragEditOnlyTableContext();

  const containerRef =
    dmrTableContext?.columnItemRefMapGetter?.(
      `${props?.dataIndex}~${props?.tableId}`,
    ) ?? React.createRef<any>();
  dmrTableContext?.columnItemRefMapSetter?.(
    `${props?.dataIndex}~${props?.tableId}`,
    containerRef,
  );

  const onValueChange = (currentValue) => {
    props?.onChangeExtra && props?.onChangeExtra(currentValue);

    props?.onChange && props?.onChange(currentValue);
  };

  const getPopupContainer = (trigger) => {
    return getSelectorDropdownContainerNode(props?.parentNodeId);
  };

  const onKeyDown = (event: any) => {
    if (props?.tableId && event?.hosted !== true && event?.ctrlKey === false) {
      if (event?.key === 'ArrowUp') {
        Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
          event: event,
          type: 'UP',
          trigger: 'selectkeydown',
        });
      }

      if (event?.key === 'ArrowDown') {
        Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
          event: event,
          type: 'DOWN',
          trigger: 'selectkeydown',
        });
      }
    }
  };

  const dictModule =
    props?.extraItem?.conditionDictionaryKey ?? props?.conditionDictionaryKey;
  const dictModuleGroup =
    props?.extraItem?.conditionDictionaryGroup ??
    props?.conditionDictionaryGroup;

  const currentDataSources =
    props?.dataSource ??
    (dictModuleGroup
      ? globalState?.dictData?.[dictModuleGroup]?.[dictModule]
      : globalState?.dictData[dictModule]) ??
    [];

  const conditions = props?.dataSourceProcessor
    ? props?.dataSourceProcessor(currentDataSources || [])
    : currentDataSources || [];

  console.log('IcdeOperationInputSelector conditions', conditions);

  return (
    <div className={'form-content-item-container'} style={{ width: '100%' }}>
      <UniDmrSelect
        containerRef={containerRef}
        className={props?.className}
        bordered={false}
        disabled={props?.disabled || false}
        formItemId={`formItem#${props?.dataIndex}#${props?.index}#Input${
          dictModule === 'Employee' ? '#Employee' : ''
        }${props?.tableId ? `#${props?.tableId}` : ''}`}
        dataSource={conditions}
        dropdownStyle={props?.dropdownStyle}
        listHeight={tableSelectorDropdownHeight ?? props?.listHeight}
        dropdownMatchSelectWidth={
          props?.dropdownMatchSelectWidth === undefined
            ? 200
            : props?.dropdownMatchSelectWidth
        }
        virtual={true}
        value={props?.value}
        optionNameKey={props?.optionNameKey}
        optionValueKey={props?.optionValueKey}
        optionTitleKey={props?.optionTitleKey}
        optionLabelProp={props?.optionLabelProp}
        nameFormat={props?.nameFormat}
        onChange={(value) => {
          if (isEmptyValues(value)) {
            onValueChange(value);
          }
        }}
        onSelect={onValueChange}
        getPopupContainer={getPopupContainer}
        leftOneAutoSelect={props?.leftOneAutoSelect ?? false}
        onKeyDown={onKeyDown}
        getTooltipPopupContainer={(triggerNode) => {
          return document?.querySelector(
            `#${props?.tableId} #tanstack-table-container tbody`,
          );
        }}
        numberSelectItem={props?.numberSelectItem ?? false}
        // 用于  表格内 自定义 展示
        labelFormat={props?.extraItem?.labelFormat ?? null}
      />
    </div>
  );
};

interface IcdeFieldInputProps extends FormTableItemBaseProps {
  recordId?: string;
  dataIndex: string;
  index: number;
  tableId: string;
}
export const IcdeFieldInput = (props: IcdeFieldInputProps) => {
  return (
    <Tooltip title={props?.value}>
      <div className={'form-content-item-container'}>
        <Input
          id={`formItem#${props?.dataIndex}#${props?.index}#Input#${props?.tableId}`}
          className={'icde-input icde-table-item'}
          bordered={false}
          value={props?.value ?? ''}
          onChange={(event) => {
            console.log('icde-input', event, event.target.value);
            props?.onChange && props?.onChange(event.target.value);
          }}
          contentEditable={true}
          // onChange={(event) => {
          //   setValue(event.target.value);
          // }}
        />
      </div>
    </Tooltip>
  );
};

interface IcuDurationInputProps extends FormTableItemBaseProps {
  recordId?: string;
  className?: string;
  dataIndex: string;
  index: number;
}

export const IcuDurationFieldInput = (props: IcuDurationInputProps) => {
  return (
    <div
      className={'form-content-item-container'}
      style={{ justifyContent: 'flex-start' }}
    >
      <InputNumber
        id={`formItem#${props?.dataIndex}#${props?.index}#IcuTable`}
        className={props?.className}
        bordered={false}
        value={props?.value ?? ''}
        min={1}
        precision={1}
        controls={false}
        keyboard={false}
        contentEditable={true}
        onChange={props?.onChange}
      />
    </div>
  );
};

interface PathologyIcdeFieldInputProps extends FormTableItemBaseProps {
  recordId: string;
  disabled?: boolean;
  dataIndex: string;
  index: number;
}
export const PathologyIcdeFieldInput = (
  props: PathologyIcdeFieldInputProps,
) => {
  return (
    <div className={'form-content-item-container'}>
      <Input
        id={`formItem#${props?.dataIndex}#${props?.index}#Input#PathologyTable`}
        className={'icde-input icde-table-item'}
        style={props?.disabled === true ? { padding: '4px 0px' } : {}}
        bordered={false}
        readOnly={props?.disabled ?? false}
        value={props?.value ?? ''}
        onChange={(event) => {
          props?.onChange && props?.onChange(event?.target?.value);
        }}
        contentEditable={true}
        onClick={onReadonlyInputAndReadonlyItemClick}
        // onChange={(event) => {
        //   setValue(event.target.value);
        // }}
      />
    </div>
  );
};

interface IcdeOperationReadonlyItemProps {
  recordId?: string;
  dataIndex?: string;
  className?: string;
  value?: string;

  conditionDictionaryKey?: string;
  conditionDictionaryGroup?: string;
  extraItem?: any;
}

export const IcdeOperationReadonlyItem = (
  props: IcdeOperationReadonlyItemProps,
) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const dictModule =
    props?.extraItem?.conditionDictionaryKey ?? props?.conditionDictionaryKey;
  const dictModuleGroup =
    props?.extraItem?.conditionDictionaryGroup ??
    props?.conditionDictionaryGroup;

  const dictData = dictModuleGroup
    ? globalState?.dictData?.[dictModuleGroup]
    : globalState?.dictData;

  const label = dictModule
    ? dictData?.[dictModule]?.find(
        (item) => item?.Code?.toString() === props?.value?.toString(),
      )?.Name || '-'
    : props?.value || '-';

  console.log('IcdeOperationReadonlyItem', props, dictData, label);

  return (
    <Tooltip title={label}>
      <span
        style={
          readonlyInputClickSelectAll
            ? {
                userSelect: 'auto',
              }
            : {}
        }
        className={`icde-oper-readonly-item ${props?.className ?? ''}`}
        onClick={onReadonlyInputAndReadonlyItemClick}
      >
        {label}
      </span>
    </Tooltip>
  );
};

export const operationExtraMap: { [key: string]: OperIcdeExtraMapItem } = {
  IsMicro: {
    display: '微',
    label: '微创手术',
    color: 'blue',
  },
  InsurIsObsolete: {
    display: '灰',
    label: '置灰',
    color: '#eb5757',
  },

  HqmsDegree: {
    display: '',
    label: '',
    color: '',
  },

  DrgsDegree: {
    display: '',
    label: '',
    color: '',
  },

  OperationCombo: {
    display: '手术组',
    label: '手术组',
    color: 'cyan',
  },
};

export const hqmsDegreeMap = {
  '3': {
    color: 'red',
    display: '国三',
    label: '国三手术',
  },
  '4': {
    color: 'red',
    display: '国四',
    label: '国四手术',
  },
  '4-condition': {
    color: 'volcano',
    display: '条件四级',
    label: '条件四级',
  },
};

export const hqmsOperDetailTypeMap = {
  HqmsThirdOper: '3',
  HqmsFourthOper: '4',
};

export const drgsDegreeMap = {
  '1': {
    color: 'gold',
    display: '一',
    label: '一级手术',
  },
  '2': {
    color: 'gold',
    display: '二',
    label: '二级手术',
  },
  '3': {
    color: 'gold',
    display: '三',
    label: '三级手术',
  },
  '4': {
    color: 'gold',
    display: '四',
    label: '四级手术',
  },
};

interface OperationExtraTagsItemProps {
  inDropDown?: boolean;
  record?: any;
  value?: string[];
  eventName?: string;
  nameKey?: string;
  conditionDictionaryKey?: string;
  conditionDictionaryGroup?: string;
  form?: any;
  uniqueId?: string;
}

export const OperationExtraTagsItem = (props: OperationExtraTagsItemProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [bundleCheckExtraOperDetail, setBundleCheckExtraOperDetail] =
    useState(null);

  useEffect(() => {
    if (!isEmptyValues(props?.uniqueId)) {
      Emitter.on(`BundleCheck-Extra-${props?.uniqueId}`, (data) => {
        setBundleCheckExtraOperDetail(data);
      });
    }

    return () => {
      if (!isEmptyValues(props?.uniqueId)) {
        Emitter.off(`BundleCheck-Extra-${props?.uniqueId}`);
      }
    };
  }, []);

  let value = Form.useWatch(
    [props?.record?.id, 'OperExtra'],
    props?.form,
  ) as any;
  let record = Form.useWatch(props?.record?.id, props?.form) as any;

  if (props?.inDropDown === true) {
    value = props?.value;
    record = props?.record;
  }

  const dictData = props?.conditionDictionaryGroup
    ? globalState?.dictData?.[props?.conditionDictionaryGroup]
    : globalState?.dictData;

  let valueKeys = value ? [...value] : [];

  // 国考手术等级
  let hqmsDegree = record?.['HqmsDegree'];
  let recordData = true;
  if (!isEmptyValues(bundleCheckExtraOperDetail)) {
    if (!isEmptyValues(bundleCheckExtraOperDetail?.OperDetailType)) {
      hqmsDegree =
        hqmsOperDetailTypeMap[bundleCheckExtraOperDetail?.OperDetailType];
      recordData = false;
    }
  }

  if (hqmsDegree && hqmsDegreeMap[hqmsDegree]) {
    if (recordData === true) {
      if (hqmsDegree === '4' && !isEmptyValues(record?.['DegreeRemark'])) {
        hqmsDegree = '4-condition';
      }
    }

    operationExtraMap['HqmsDegree']['display'] =
      hqmsDegreeMap[hqmsDegree]?.display;
    operationExtraMap['HqmsDegree']['label'] =
      hqmsDegree === '4-condition'
        ? record?.['DegreeRemark']
        : hqmsDegreeMap[hqmsDegree]?.label;
    operationExtraMap['HqmsDegree']['color'] = hqmsDegreeMap[hqmsDegree]?.color;
  } else {
    // 把degree剔除
    valueKeys = valueKeys?.filter((key) => key !== 'HqmsDegree');
  }

  // DRGS手术等级
  if (record?.['DrgsDegree'] && drgsDegreeMap[record?.['DrgsDegree']]) {
    operationExtraMap['DrgsDegree']['display'] =
      drgsDegreeMap[record?.['DrgsDegree']]?.display;
    operationExtraMap['DrgsDegree']['label'] =
      drgsDegreeMap[record?.['DrgsDegree']]?.label;
    operationExtraMap['DrgsDegree']['color'] =
      drgsDegreeMap[record?.['DrgsDegree']]?.color;
  } else {
    // 把degree剔除
    valueKeys = valueKeys?.filter((key) => key !== 'DrgsDegree');
  }

  return (
    <>
      {valueKeys?.length > 0 ? (
        valueKeys.map((key) => {
          return (
            <Tooltip
              title={operationExtraMap[key]?.label}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
            >
              <Tag
                style={{ margin: '5px 5px', border: 'none', borderRadius: 4 }}
                color={operationExtraMap[key]?.color}
              >
                {operationExtraMap[key]?.display}
              </Tag>
            </Tooltip>
          );
        })
      ) : (
        <span>-</span>
      )}
    </>
  );
};

export const icdeExtraMap: { [key: string]: OperIcdeExtraMapItem } = {
  InsurIsObsolete: {
    display: '灰',
    label: '置灰',
    color: '#eb5757',
  },
  IsCc: {
    display: 'CC',
    label: '并发症或合并症',
    color: '#ffc300',
  },
  IsMcc: {
    display: 'MCC',
    label: '严重并发症或合并症',
    color: '#fb5607',
  },
};

interface IcdeExtraTagsItemProps {
  value?: string[];
  nameKey?: string;
}

export const IcdeExtraTagsItem = (props: IcdeExtraTagsItemProps) => {
  return (
    <>
      {props?.value?.length > 0 ? (
        props?.value.map((key) => {
          return (
            <Tooltip title={icdeExtraMap[key]?.label}>
              <Tag
                style={{ margin: '5px 5px', border: 'none', borderRadius: 4 }}
                color={icdeExtraMap[key]?.color}
              >
                {icdeExtraMap[key]?.display}
              </Tag>
            </Tooltip>
          );
        })
      ) : (
        <span>-</span>
      )}
    </>
  );
};
