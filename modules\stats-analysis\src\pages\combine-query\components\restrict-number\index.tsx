import React, { useState } from 'react';
import { Col, Input } from 'antd';
import './index.less';

interface RestrictInputNumberProps {
  min?: number;
  max?: number;

  step?: number;

  scale?: number;

  precious?: number;

  value?: any;
  onChange?: (value: any) => void;
  suffix?: string;

  setValue?: (value: any) => void;
}

const RestrictInputNumber = (props: RestrictInputNumberProps) => {
  const [value, setValue] = useState(props?.value);

  console.error('props', props);

  return (
    <Col>
      <div className={'combine-input-remove-arrow'}>
        <Input
          min={props?.min}
          max={props?.max}
          className={'restrict-input-number-container'}
          value={value}
          type={'number'}
          step={props?.step || 0.01}
          suffix={props?.suffix || undefined}
          onChange={(event) => {
            let value = event.target.value;

            if (value) {
              // 点位之前的东西
              // TODO scale
              if (props?.scale) {
                if (props?.scale - (props?.precious || 0) > 0) {
                  let valuesWithoutDot = value.split('.');
                  value = `${valuesWithoutDot?.at(0)}`.slice(
                    0,
                    props?.scale - (props?.precious || 0),
                  );
                  if (valuesWithoutDot?.length > 1 && valuesWithoutDot?.at(1)) {
                    value += `.${valuesWithoutDot?.at(1)}`;
                  }
                }
              }

              // 点位之后的东西
              if (props?.precious) {
                if (props?.precious === 0) {
                  if (/^\d+\.\d*$/.test(value?.toString())) {
                    value = parseFloat(value)?.toFixed(0);
                  }
                } else {
                  let regex = new RegExp(
                    `^\\d+\\.\\d{${props?.precious + 1},}$`,
                  );
                  if (regex.test(value?.toString())) {
                    value = parseFloat(value)?.toFixed(props?.precious);
                  }
                }
              }

              // 最大最小值
              if (props?.max) {
                if (parseFloat(value) > props.max) {
                  value = props?.max.toString();
                }
              }
            }

            if (/^-.*$/.test(value?.toString())) {
              value = '';
            }

            setValue(value);
            props?.setValue && props?.setValue(value);
          }}
        />
      </div>
    </Col>
  );
};

export default RestrictInputNumber;
