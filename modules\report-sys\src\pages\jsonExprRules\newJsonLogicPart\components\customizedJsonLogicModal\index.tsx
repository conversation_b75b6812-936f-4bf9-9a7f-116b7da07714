import { Reducer, useEffect, useReducer, useRef } from 'react';
import {
  IReducer,
  IModalState,
  ITableState,
} from '@uni/reducers/src/interface';
import {
  InitTableState,
  TableAction,
  modalReducer,
  tableReducer,
} from '@uni/reducers/src';
import { ModalAction } from '@uni/reducers/src/modalReducer';
import _ from 'lodash';
import { useRequest } from 'umi';
import { UniTable } from '@uni/components/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  StatsAnalysisEventConstant,
  qualityControlRuleEventConstants,
  ReqActionType,
} from '../../constants';
import { useSafeState } from 'ahooks';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import {
  <PERSON><PERSON>,
  Col,
  Divider,
  Modal,
  Row,
  Space,
  Switch,
  Tabs,
  message,
  Empty,
  Input,
} from 'antd';
import DmrQueryHeader from '../dmr-query-header';
import { ExprRuleItem } from '../../interface';
import IconBtn from '@uni/components/src/iconBtn';
import ProFormContainer from '@uni/components/src/pro-form-container';
import '@uni/components/src/pro-form-container/index.less';
import { v4 as uuidv4 } from 'uuid';
import {
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@uni/components/src/pro-form';
import { loadSubjects, jsonLogic } from '@uni/combo-condition-parser';
import JsonExprQueryBuilder from './components/json-expr';
import { Fields } from '@react-awesome-query-builder/core';
import { fieldsProcessorByDirectories } from './components/json-expr/processor';
import { CombineQueryFieldItem } from './components/json-expr/interfaces';
import DescriptionPart from './components/patient-info';
import { Emitter } from '@uni/utils/src/emitter';
import { recurrenceFindOne } from '../../utils';
import { useFields } from '../../context/FieldsContext';

import './index.less';

const EXPR_PROVIDER = 'JsonLogic';

const CustomizedJsonLogic = ({ ModalState }) => {
  // 最终expr
  const expr = useRef(undefined);

  // 使用Context中的数据
  const {
    queryFields,
    keySort,
    loading: jsonExprFieldsLoading,
    refresh: refreshFields,
  } = useFields();

  return (
    <Col span={24}>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div className="ant-form-item-label expr-form-item">
          <label className="ant-form-item-required">规则条件</label>
        </div>
        <Space>
          <Button
            size="small"
            onClick={() => {
              Emitter.emit(
                StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_VIEW_TEXT,
              );
            }}
          >
            查看语法
          </Button>
          <Button
            type={'primary'}
            size="small"
            onClick={() => {
              Emitter.emit(
                StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_CONDITION_ADD,
              );
            }}
          >
            新增条件
          </Button>
        </Space>
      </div>

      <JsonExprQueryBuilder
        fields={queryFields}
        keySort={keySort}
        onChange={(value) => {
          console.log('JsonExprQueryBuilder onChange:', value);
          if (value) {
            expr.current = value;
          } else {
            expr.current = undefined;
          }
        }}
        onSave={({ DisplayExpr, Expr }, doTest) => {
          console.log('JsonExprQueryBuilder onsave', DisplayExpr, Expr);
        }}
        initialValue={ModalState?.record}
      />
    </Col>
  );
};

export default CustomizedJsonLogic;
