import {
  MutableRefObject,
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import { Dispatch, useDispatch, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import UniEditableTable, {
  EditableFormInstance,
} from '@uni/components/src/table/edittable';
import IconBtn from '@uni/components/src/iconBtn';
import {
  Alert,
  Button,
  Card,
  Checkbox,
  Col,
  InputNumber,
  List,
  Modal,
  Popconfirm,
  Row,
  Space,
  Table,
  TableProps,
  Tabs,
  Tooltip,
  message,
} from 'antd';
import { useSafeState, useUpdateEffect } from 'ahooks';
import { v4 as uuidv4 } from 'uuid';
import { SwagInpatientAmtItem } from '../../../interface';
import {
  arrElMoveTop,
  canDynddrReview,
  columnsHandler,
  isRespErr,
  sortingHandler,
} from '@/utils/widgets';
import _ from 'lodash';
import { SorterResult, TableRowSelection } from 'antd/lib/table/interface';
import { ReqActionType } from '@/Constants';
import './index.less';
import dayjs from 'dayjs';
import {
  proofreadDailyCompareColumns,
  proofreadDeptCompareColumns,
} from '../../../columns';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import ConfigToImg from '@uni/components/src/config2Img';
import ProFormContainer from '@uni/components/src/pro-form-container';
import {
  FormListActionType,
  ProForm,
  ProFormInstance,
} from '@uni/components/src/pro-form';
import {
  IReducer,
  IModalState,
  ITableState,
  IEditableState,
} from '@uni/reducers/src/interface';
import {
  InitTableState,
  InitEditableState,
  TableAction,
  modalReducer,
  tableReducer,
  tableEditPropsReducer,
  EditableTableAction,
} from '@uni/reducers/src';
import UniTable, { ActionType } from '@uni/components/src/table';
import { ITableReq } from '@/interface';
// import { SearchFormItems } from './formItems';
import { ModalAction } from '@uni/reducers/src/modalReducer';
import SingleStat from '@uni/components/src/statistic';

const Proofead = () => {
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式

  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  // table state
  const [DeptCompareTableState, DeptCompareTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);

  const [DailyCompareTableState, DailyCompareTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);
  // 病案明细 total
  const [DetailTableState, DetailTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);
  // 病案明细 extra
  const [DetailExtraTableState, DetailExtraTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);
  // 动态明细 total
  const [DynamicTableState, DynamicTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);
  // 动态明细 extra
  const [DynamicExtraTableState, DynamicExtraTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);
  // 分页state
  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  // 科室校对 过滤
  const [onlyDiffed, setOnlyDiffed] = useState(false);
  const [deptDataSource, setDeptDataSource] = useState([]);

  // 每日科室校对
  const [dailyOnlyDiffed, setDailyOnlyDiffed] = useState(false);
  const [dailyDeptDataSource, setDailyDeptDataSource] = useState([]);

  // search args form
  const [searchForm] = ProForm.useForm<ProFormInstance>();
  const searchFormRef = useRef<ProFormInstance>();

  const [hospCompare, setHospCompare] = useState(undefined);
  const [deptCompare, setDeptCompare] = useState([]);
  const [dailyCompare, setDailyCompare] = useState([]);

  const [tabActiveKey, setTabActiveKey] = useState('1');

  const [expandedRowKeys, setExpandedRowKeys] = useState([]);

  const tableDom1 = useRef(undefined);

  // 校对数据req
  const proofreadReq = async (
    reqData: any,
    reqType: ReqActionType,
    reqId = undefined,
  ) => {
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `InpatientAmtCheck/${reqType}`,
        requestParams: {
          url: `Api/Dyn-ddr/InpatientAmtCheck/${reqType}`,
          method: 'POST',
          data: reqData, // 数据统一放到调接口之前自己处理
          dataType: 'dyn-ddr',
          requestType: 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      let { data } = res;

      if (reqType === ReqActionType.GetHospCompare) {
        setHospCompare(data);
      } else if (reqType === ReqActionType.GetDeptCompare) {
        setDeptCompare(data);
        DeptCompareTableDispatch({
          type: TableAction.dataChange,
          payload: { data },
        });
      } else if (reqType === ReqActionType.GetDeptDailyDdrAmtCompare) {
        setDailyCompare(data);
        DailyCompareTableDispatch({
          type: TableAction.dataChange,
          payload: { data },
        });
      }
    }
  };

  console.log('DeptCompareTableState', DeptCompareTableState.data);

  const tableReq = async (params) => {
    console.log('fetchParams: ', params);

    let res: ITableReq | string = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: 'InpatientAmtCheck',
        requestParams: [
          {
            url: 'Api/Dyn-ddr/InpatientAmtCheck/GetCardDetails',
            method: 'POST',
            data: params,
            dataType: 'dyn-ddr',
          },
          {
            url: 'Api/Dyn-ddr/InpatientAmtCheck/GetDetails',
            method: 'POST',
            data: params,
            dataType: 'dyn-ddr',
          },
          {
            url: 'Api/Dyn-ddr/InpatientAmtCheck/GetExtraCardDetails',
            method: 'POST',
            data: params,
            dataType: 'dyn-ddr',
          },
          {
            url: 'Api/Dyn-ddr/InpatientAmtCheck/GetExtraDetails',
            method: 'POST',
            data: params,
            dataType: 'dyn-ddr',
          },
        ],
      },
    });
    if (typeof res !== 'string') {
      console.log(res);
      console.log(res.datas[0]?.data);
      DetailTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res.datas[0]?.data,
        },
      });
      DynamicTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res.datas[1]?.data,
        },
      });
      DetailExtraTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res.datas[2]?.data,
        },
      });
      DynamicExtraTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res.datas[3]?.data,
        },
      });
    }
  };

  useUpdateEffect(() => {
    console.log(searchParams);
    if (searchParams?.hospCode && searchParams?.dateRange?.length > 0) {
      let reqData = {
        hospCode: searchParams.hospCode,
        Sdate: searchParams.dateRange?.at(0),
        Edate: searchParams.dateRange?.at(1),
      };
      proofreadReq(reqData, ReqActionType.GetHospCompare);
      proofreadReq(reqData, ReqActionType.GetDeptCompare);

      // Added
      // 新增逻辑 当且仅当 有展开的时候 请求一份展开的table
      Emitter.emit('ON_SEARCH_WITH_EXPAND_ROW_KEYS');

      DetailTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: [],
        },
      });
      DynamicTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: [],
        },
      });
      DetailExtraTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: [],
        },
      });
      DynamicExtraTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: [],
        },
      });
    }
  }, [searchParams]);

  // Custom Added
  useEffect(() => {
    Emitter.on('ON_SEARCH_WITH_EXPAND_ROW_KEYS', () => {
      if (expandedRowKeys?.length > 0 && expandedRowKeys?.length === 1) {
        let reqData = {
          hospCode: searchParams.hospCode,
          Sdate: searchParams.dateRange?.at(0),
          Edate: searchParams.dateRange?.at(1),
          deptCode: expandedRowKeys?.at(0),
        };
        proofreadReq(reqData, ReqActionType.GetDeptDailyDdrAmtCompare);
      }
    });

    return () => {
      Emitter.off('ON_SEARCH_WITH_EXPAND_ROW_KEYS');
    };
  }, [expandedRowKeys]);

  // columns 处理
  if (
    DeptCompareTableState?.columns?.length < 1 &&
    columnsList?.['InpatientAmtCheck/GetDeptCompare']
  ) {
    console.log(
      tableColumnBaseProcessor(
        [],
        columnsList['InpatientAmtCheck/GetDeptCompare'],
      ),
    );
    DeptCompareTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          proofreadDeptCompareColumns,
          columnsList['InpatientAmtCheck/GetDeptCompare'],
          'local',
        ),
      },
    });
  }
  if (
    DailyCompareTableState?.columns?.length < 1 &&
    columnsList?.['InpatientAmtCheck/GetDeptDailyDdrAmtCompare']
  ) {
    DailyCompareTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          proofreadDailyCompareColumns,
          columnsList['InpatientAmtCheck/GetDeptDailyDdrAmtCompare'],
          'local',
        ),
      },
    });
  }
  if (
    DetailTableState?.columns?.length < 1 &&
    columnsList?.['InpatientAmtCheck/GetCardDetails']
  ) {
    DetailTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          [],
          columnsList['InpatientAmtCheck/GetCardDetails']?.map((d) => ({
            ...d,
            width: 150,
          })),
        ),
      },
    });
  }
  if (
    DynamicTableState?.columns?.length < 1 &&
    columnsList?.['InpatientAmtCheck/GetDetails']
  ) {
    DynamicTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          [],
          columnsList['InpatientAmtCheck/GetDetails']?.map((d) => ({
            ...d,
            width: 150,
          })),
        ),
      },
    });
  }
  if (
    DetailExtraTableState?.columns?.length < 1 &&
    columnsList?.['InpatientAmtCheck/GetExtraCardDetails']
  ) {
    DetailExtraTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          [],
          columnsList['InpatientAmtCheck/GetExtraCardDetails']?.map((d) => ({
            ...d,
            width: 150,
          })),
        ),
      },
    });
  }
  if (
    DynamicExtraTableState?.columns?.length < 1 &&
    columnsList?.['InpatientAmtCheck/GetExtraDetails']
  ) {
    DynamicExtraTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          [],
          columnsList['InpatientAmtCheck/GetExtraDetails']?.map((d) => ({
            ...d,
            width: 150,
          })),
        ),
      },
    });
  }

  // 科室table点击
  useEffect(() => {
    if (DeptCompareTableState.clkItem) {
      // 只有在这里才重置每日的点击
      DailyCompareTableDispatch({
        type: TableAction.clkChange,
        payload: {
          clkItem: null,
        },
      });
      setTabActiveKey('2');
      let reqData = {
        hospCode: searchParams.hospCode,
        Sdate: searchParams.dateRange?.at(0),
        Edate: searchParams.dateRange?.at(1),
        deptCode: DeptCompareTableState.clkItem?.DeptCode,
      };
      proofreadReq(reqData, ReqActionType.GetDeptDailyDdrAmtCompare);
      tableReq(reqData);
    }
  }, [DeptCompareTableState.clkItem]);

  // 每日点击
  useEffect(() => {
    if (DailyCompareTableState.clkItem) {
      setTabActiveKey('2');
      let reqData = {
        hospCode: searchParams.hospCode,
        Sdate: dayjs(DailyCompareTableState.clkItem?.ExactDate)?.format(
          'YYYY-MM-DD',
        ),
        Edate: dayjs(DailyCompareTableState.clkItem?.ExactDate)?.format(
          'YYYY-MM-DD',
        ),
        deptCode: DeptCompareTableState.clkItem?.DeptCode,
      };
      tableReq(reqData);
    }
  }, [DailyCompareTableState.clkItem]);

  // 科室校对过滤
  useEffect(() => {
    if (onlyDiffed) {
      setDeptDataSource(
        DeptCompareTableState.data.filter((item) => item.DiffNum !== 0),
      );
    } else {
      setDeptDataSource(DeptCompareTableState.data);
    }

    if (onlyDiffed) {
      setDailyDeptDataSource(
        DailyCompareTableState.data.filter((item) => item.DiffNum !== 0),
      );
    } else {
      setDailyDeptDataSource(DailyCompareTableState.data);
    }
  }, [onlyDiffed, DeptCompareTableState?.data, DailyCompareTableState?.data]);
  // 每日科室校对过滤
  // useEffect(() => {
  //   if (DailyCompareTableState.data?.length > 0) {
  //     if (dailyOnlyDiffed) {
  //       setDailyDeptDataSource(
  //         DailyCompareTableState.data.filter((item) => item.DiffNum  0),
  //       );
  //     } else {
  //       setDailyDeptDataSource(DailyCompareTableState.data);
  //     }
  //   }
  // }, [dailyOnlyDiffed, DailyCompareTableState.data]);

  // 右
  const rightTabsItems = [
    {
      key: '1',
      label: `病案差异明细`,
      children: (
        <UniTable
          id="CardDetails"
          rowKey="CardId"
          loading={loadings?.[`InpatientAmtCheck/GetCardDetails`]}
          columns={DetailExtraTableState.columns}
          dataSource={DetailExtraTableState.data}
          scroll={{ x: 'max-content' }}
          pagination={{ pageSize: 9, size: 'small' }}
        />
      ),
    },
    {
      key: '2',
      label: `动态差异明细`,
      children: (
        <UniTable
          id="CardDetails"
          rowKey="CardId"
          loading={loadings?.['InpatientAmtCheck/GetDetails']}
          columns={DynamicExtraTableState.columns}
          dataSource={DynamicExtraTableState.data}
          scroll={{ x: 'max-content' }}
          pagination={{ pageSize: 9, size: 'small' }}
        />
      ),
    },
    {
      key: '3',
      label: `病案明细`,
      children: (
        <UniTable
          id="CardDetails"
          rowKey="CardId"
          columns={DetailTableState.columns}
          loading={loadings?.[`InpatientAmtCheck/GetExtraCardDetails`]}
          dataSource={DetailTableState.data}
          scroll={{ x: 'max-content' }}
          pagination={{ pageSize: 9, size: 'small' }}
        />
      ),
    },
    {
      key: '4',
      label: `动态明细`,
      children: (
        <UniTable
          id="DynamicDetails"
          rowKey="ExactDate"
          columns={DynamicTableState.columns}
          dataSource={DynamicTableState.data}
          loading={loadings?.[`InpatientAmtCheck/GetExtraDetails`]}
          scroll={{ x: 'max-content' }}
          pagination={{ pageSize: 9, size: 'small' }}
        />
      ),
    },
  ];

  const tableCells = tableDom1?.current?.querySelectorAll('.ant-table-cell');
  if (tableCells?.length > 0) {
    tableCells.forEach((cell) => {
      cell.style.minWidth = '150px';
    });
  }

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={24} md={12} lg={12} xl={12}>
          <SingleStat
            loading={
              loadings?.[`InpatientAmtCheck/${ReqActionType.GetHospCompare}`]
            }
            title={`院区校对：${
              dictData?.Hospital?.find((d) => d.Code === searchParams?.hospCode)
                ?.Name
            } 动态数`}
            dataType={'string'}
            value={hospCompare?.Amt}
            suffix={
              <>
                {hospCompare?.DiffNum ? (
                  <Tooltip title="差异数">
                    <span style={{ color: 'red', fontSize: '14px' }}>
                      ({hospCompare?.DiffNum})
                    </span>
                  </Tooltip>
                ) : null}
              </>
            }
            footerTitle="首页数"
            footerValue={hospCompare?.CardAmt}
          />
        </Col>
        <Col xs={24} sm={24} md={12} lg={12} xl={12}>
          <SingleStat
            loading={
              loadings?.[`InpatientAmtCheck/${ReqActionType.GetDeptCompare}`]
            }
            title={`科室校对：差异科室数`}
            value={deptCompare?.filter((d) => d.DiffNum !== 0)?.length}
            // suffix={
            //   <>
            //     {deptCompare?.filter((d) => d.DiffNum > 0)?.length > 0 ? (
            //       <Button
            //         size="small"
            //         onClick={(e) => {
            //           setOnlyDiffed(!onlyDiffed);
            //         }}
            //       >
            //         {onlyDiffed ? '查看全部科室' : '只看错误科室'}
            //       </Button>
            //     ) : null}
            //   </>
            // }
          />
        </Col>
        <Col xs={24} sm={24} md={24} lg={10} xl={10}>
          <Card
            title="病案校对结果"
            extra={
              <Checkbox
                onChange={(e) => {
                  setOnlyDiffed(e.target.checked);
                }}
              >
                仅显示差异
              </Checkbox>
            }
          >
            {/* <Tabs
              defaultActiveKey="1"
              activeKey={tabActiveKey}
              onTabClick={setTabActiveKey}
              type="card"
              // size={size}
              items={tabsItems}
            /> */}
            <UniTable
              id="DeptCompareTable"
              rowKey="DeptCode"
              columns={DeptCompareTableState.columns}
              dataSource={deptDataSource}
              loading={
                loadings?.[`InpatientAmtCheck/${ReqActionType.GetDeptCompare}`]
              }
              rowClassName={(record) => {
                if (
                  record?.DeptCode === DeptCompareTableState.clkItem?.DeptCode
                )
                  return 'row-selected';
                return null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (
                      DeptCompareTableState.clkItem?.DeptCode !==
                      record?.DeptCode
                    ) {
                      DeptCompareTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                    }
                  },
                };
              }}
              expandable={{
                expandedRowKeys: expandedRowKeys,
                onExpand(expanded, record) {
                  if (expanded) {
                    setExpandedRowKeys([record.DeptCode]);
                    DeptCompareTableDispatch({
                      type: TableAction.clkChange,
                      payload: {
                        clkItem: record,
                      },
                    });
                  } else {
                    setExpandedRowKeys([]);
                  }
                },
                expandedRowRender: (record) => {
                  return (
                    // <div style={{ width: '100%', border: '1px solid black' }}>
                    <UniTable
                      id="DailyCompareTable"
                      rowKey="ExactDate"
                      columns={DailyCompareTableState.columns}
                      loading={
                        loadings?.[
                          `InpatientAmtCheck/${ReqActionType.GetDeptDailyDdrAmtCompare}`
                        ]
                      }
                      dataSource={dailyDeptDataSource}
                      rowClassName={(record) => {
                        if (
                          record?.ExactDate ===
                          DailyCompareTableState.clkItem?.ExactDate
                        )
                          return 'row-selected';
                        return null;
                      }}
                      onRow={(record) => {
                        return {
                          onClick: (event) => {
                            if (
                              DailyCompareTableState.clkItem?.ExactDate !==
                              record?.ExactDate
                            ) {
                              DailyCompareTableDispatch({
                                type: TableAction.clkChange,
                                payload: {
                                  clkItem: record,
                                },
                              });
                            }
                          },
                        };
                      }}
                      scroll={{ y: 200 }}
                      pagination={false}
                    />
                    // </div>
                  );
                },
              }}
              pagination={{ size: 'small' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={24} md={24} lg={14} xl={14}>
          {/* <Card
            title={`${
              DeptCompareTableState.clkItem
                ? DeptCompareTableState.clkItem?.DeptName
                : '无科室'
            } 病案每日校对结果`}
            extra={
              <Checkbox
                onChange={(e) => {
                  setDailyOnlyDiffed(e.target.checked);
                }}
              >
                仅显示差异
              </Checkbox>
            }
            style={{ marginBottom: '20px' }}
          >
            <UniTable
              id="DailyCompareTable"
              rowKey="ExactDate"
              columns={DailyCompareTableState.columns}
              loading={
                loadings?.[
                  `InpatientAmtCheck/${ReqActionType.GetDeptDailyDdrAmtCompare}`
                ]
              }
              dataSource={dailyDeptDataSource}
              rowClassName={(record) => {
                if (
                  record?.ExactDate ===
                  DailyCompareTableState.clkItem?.ExactDate
                )
                  return 'row-selected';
                return null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (
                      DailyCompareTableState.clkItem?.ExactDate !==
                      record?.ExactDate
                    ) {
                      DailyCompareTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                    }
                  },
                };
              }}
              scroll={{ y: 200 }}
              pagination={false}
            />
          </Card> */}
          <Card
            title={`${
              DeptCompareTableState.clkItem
                ? DeptCompareTableState.clkItem?.DeptName
                : '无科室'
            }`}
            extra={`${
              DailyCompareTableState.clkItem?.ExactDate
                ? dayjs(DailyCompareTableState.clkItem?.ExactDate)?.format(
                    'YYYY-MM-DD',
                  )
                : '无日期'
            }`}
          >
            <Tabs
              defaultActiveKey="1"
              // type="card"
              // size={size}
              items={rightTabsItems}
            />
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default Proofead;
