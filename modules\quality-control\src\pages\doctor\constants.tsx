import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';

const showGrpCnt =
  (window as any).externalConfig?.['his']?.statsShowGrpCnt ?? false;

// 以下为新版本
// 可点击Stat
export const TotalStatsColumns = [
  {
    contentData: 'RecordCnt',
    clickable: true,
    footerYoy: true,
    detailsUrl: 'Emr/QcResultStats/GetCards',
  },
  {
    contentData: 'QcRecordCnt',
    clickable: true,
    footerYoy: true,
    detailsUrl: 'Emr/QcResultStats/GetScoredCards',
  },
  {
    contentData: 'ErrorCount',
    clickable: true,
    footerYoy: true,
    rightFootValue: 'ErrorRate',
    rightFooterTitle: '占比',
    detailsUrl: 'Emr/QcResultStats/GetQcCards',
  },
  {
    contentData: 'AvgScore',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'PassRate',
    clickable: true,
    footerYoy: true,
  },
];
