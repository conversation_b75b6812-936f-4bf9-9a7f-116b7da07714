import _ from 'lodash';
import './index.less';
import { useEffect, useState } from 'react';
import { Col, Row, Tabs } from 'antd';
import Stats from '../../components/statsWithTrend';
import { UniSelect } from '@uni/components/src';
import { useDeepCompareEffect } from 'ahooks';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '../../components/drawerCardInfo';
import SingleColumnTable from '../../components/singleColumnTable/index';
import { RespVO } from '@uni/commons/src/interfaces';
import { useModel } from '@@/plugin-model/useModel';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { TotalStatsColumns } from '../constants';
import DrillDownOperComplicationComposition from '../../components/drillDownOperComplicationComposition/index';

const GradeComplicationComposition = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, CliDepts } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('sdComposition');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedItem, setSelectedItem] = useState(undefined);

  const [selectOpts, setSelectOpts] = useState([]);

  // tab 使用下拉框数据
  const {
    data: bundledGradeComplicationCompositionOfHospData,
    loading: getBundledGradeComplicationCompositionOfHospLoading,
    run: getBundledGradeComplicationCompositionOfHospReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/v2/Grade/GradeStats/GradeOperComplicationCompositionOfCliDept`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.length) {
            setSelectOpts(
              res.data?.map((d) => ({
                ...d,
                label: `${d?.OperComplicationName}`,
              })),
            );
            // 默认把第一个设置为selected
            if (!selectedItem) {
              setSelectedItem(
                res?.data?.at(0),
                // _.maxBy(res?.data, function (o) {
                //   return o.PatCnt;
                // }),
              );
            }
          } else {
            setSelectOpts([]);
          }
        }
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    if (dateRange?.length) {
      let params: any = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        CliDepts,
      };
      setTableParams(params);
      getBundledGradeComplicationCompositionOfHospReq(params);
    }
  }, [dateRange, hospCodes, CliDepts]);

  useEffect(() => {
    Emitter.on(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK, (record) => {
      setSelectedItem(record);
      setActiveKey('hosp_deptAnalysis');
    });

    return () => {
      Emitter.off(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK);
    };
  }, []);

  let tabItems = [
    {
      key: 'sdComposition',
      label: '并发症',
      children: (
        <DrillDownOperComplicationComposition
          tableParams={tableParams}
          compositionApi="Api/v2/Grade/GradeStats/GradeOperComplicationCompositionOfCliDept"
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.OperComplicationName + '发生例数',
              args: {
                ...tableParams,
                OperComplicationCodes: [record?.OperComplicationCode],
              },
              detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
              dictData: dictData,
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_deptAnalysis',
      label: '科室综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Grade/GradeStats/GradeOperComplicationCompositionOfCliDept`}
              trendApi={`Api/v2/Grade/GradeStats/GradeOperComplicationCompositionTrend`}
              columns={TotalStatsColumns}
              type="col-xl-24"
              tabKey={activeKey}
              useGlobalState
              level="operComp"
              tableParams={tableParams}
              chartHeight={300}
              selectedTableItem={selectedItem}
            />
          </Col>
          <SingleColumnTable
            title="该并发症科室分布"
            args={{
              api: 'Api/v2/Grade/GradeStats/GradeOperComplicationCompositionByCliDept',
              extraApiArgs: {
                OperComplicationCodes: [selectedItem?.OperComplicationCode],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="CliDeptName"
            type="table"
            visibleValueKeys={['CliDeptName', 'PatCnt']}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title:
                  record?.OperComplicationName +
                  record?.CliDeptName +
                  '发生例数',
                args: {
                  ...tableParams,
                  OperComplicationCodes: [selectedItem?.OperComplicationCode],
                  CliDepts: [record?.CliDept],
                  IsSelectiveSurgeryForComplication: true,
                },
                detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
                dictData: dictData,
              });
            }}
            colSpan={{ span: 24 }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_medTeamAnalysis',
      label: '医疗组综合分析',
      children: (
        <Row>
          <SingleColumnTable
            title="该并发症医疗组分布"
            args={{
              api: 'Api/v2/Grade/GradeStats/GradeOperComplicationCompositionByMedTeam',
              extraApiArgs: {
                OperComplicationCodes: [selectedItem?.OperComplicationCode],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="MedTeamName"
            type="table"
            visibleValueKeys={[
              'MedTeamName',
              'DischargedCnt',
              'PatCnt',
              'PatRatio',
            ]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title:
                  record?.OperComplicationName +
                  record?.MedTeamName +
                  '发生例数',
                args: {
                  ...tableParams,
                  MedTeams: [record?.MedTeam],
                  OperComplicationCodes: [selectedItem?.OperComplicationCode],
                  IsSelectiveSurgeryForComplication: true,
                },
                detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <Row>
          <SingleColumnTable
            title="该并发症医生分布"
            args={{
              api: 'Api/v2/Grade/GradeStats/GradeOperComplicationCompositionByDoctor',
              extraApiArgs: {
                OperComplicationCodes: [selectedItem?.OperComplicationCode],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="DoctorName"
            type="table"
            visibleValueKeys={[
              'DoctorName',
              'DischargedCnt',
              'PatCnt',
              'PatRatio',
            ]}
            colSpan={{ span: 24 }}
            select={{
              dataKey: 'DoctorType',
              valueKey: 'GroupByDoctor',
              allowClear: false,
              defaultSelect: true,
              capitalizeString: true,
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title:
                  record?.OperComplicationName +
                  record?.DoctorName +
                  '发生例数',
                args: {
                  ...tableParams,
                  DoctorCodes: [record?.DoctorCode],
                  DoctorType: record?.DoctorType,
                  OperComplicationCodes: [selectedItem?.OperComplicationCode],
                  IsSelectiveSurgeryForComplication: true,
                },
                detailsUrl: 'V2/Grade/GradeDetails/CompositeDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
  ];

  return (
    <>
      <div>
        <Tabs
          size="small"
          items={tabItems}
          activeKey={activeKey}
          onChange={(key) => setActiveKey(key)}
          tabBarExtraContent={{
            right: activeKey !== 'sdComposition' && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <label>当前并发症：</label>
                <UniSelect
                  width={300}
                  showSearch
                  dataSource={selectOpts}
                  value={selectedItem?.OperComplicationCode}
                  onChange={(value) => {
                    setSelectedItem(
                      selectOpts?.find(
                        (d) => d?.OperComplicationCode === value,
                      ),
                    );
                  }}
                  allowClear={false}
                  optionNameKey={'label'}
                  optionValueKey={'OperComplicationCode'}
                  enablePinyinSearch={true}
                  fieldNames={{
                    // label: 'ChsDrgName',
                    value: 'OperComplicationCode',
                  }}
                />
              </div>
            ),
          }}
        />
        {/* 把modal与drawer的位置提高到这里，统一在这里处理 避免id太多导致匹配混乱 & tab切换后id失效 */}
        {/* TODO DetailTableModal内部的实现方式是destoryOnClose 导致每次打开都要重新获取columns */}
        <DetailTableModal
          dictData={dictData}
          detailAction={(record) => {
            // 这里替代内部 操作 onClick
            setDrawerVisible({
              hisId: record?.HisId,
              type: 'drg',
            });
          }}
        />
        {/* drawer 同理 这样保持复用 因为drawer 监听的是detailTableModal 或者一些table */}
        <DrawerCardInfo
          type={'DrgAndHqms'}
          visible={drawerVisible}
          onClose={() => setDrawerVisible(undefined)}
        />
      </div>
    </>
  );
};

export default GradeComplicationComposition;
