import React, { useEffect, useRef, useState } from 'react';
import { Button, Dropdown, Form, Input, Select, Spin, Tooltip } from 'antd';
import './index.less';
import { v4 as uuidv4 } from 'uuid';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { IcdeOperItem, IcdeOperResp } from '@/pages/chs/network/interfaces';
import { useDebounceFn } from 'ahooks';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  getSelectorDropdownContainerNode,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
} from '@uni/grid/src/utils';
import { UniAntdSelect } from '@uni/components/src';
import { icdeExtraMap } from '@uni/grid/src/components/icde-oper-input/input';
import { isEmptyValues } from '@uni/utils/src/utils';
import { FormTableItemBaseProps } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import cloneDeep from 'lodash/cloneDeep';
import EmptyWrapper from '@uni/grid/src/components/empty-wrapper';
import { IcdeOperKeyboardFriendlyDropdown } from '@uni/grid/src/components/dmr-select/keyboard';

const { Option } = Select;

const inputFocusNotSelectAll =
  (window as any).externalConfig?.['chs']?.inputFocusNotSelectAll ?? false;

const tableSelectorDropdownHeight = (window as any).externalConfig?.['chs']
  ?.tableSelectorDropdownHeight;

const enableKeyboardFriendlySelect =
  (window as any).externalConfig?.['chs']?.enableKeyboardFriendlySelect ??
  false;

const numberSelectItem =
  (window as any).externalConfig?.['chs']?.numberSelectItem ?? false;

export interface IcdeFormKeysItem {
  [key: string]: string;
}

export interface IcdeSelectProps extends FormTableItemBaseProps {
  form?: any;

  dataIndex?: string;

  paramKey: string;

  parentId?: string;

  formKeys?: IcdeFormKeysItem; // 暂定

  componentId: string;

  selectFormKey?: string;
  itemKey?: string;

  getPopupContainer?: (trigger) => HTMLElement;

  dropdownStyle?: React.CSSProperties;
  listHeight?: number;

  interfaceUrl?: string;
  disabled?: boolean;

  // 为了兼容 置灰的手术 / 诊断用于立即搜索
  instantSelect?: boolean;
  dropdownAlign?: any;

  onChangeValueProcessor?: (value: any) => string;

  recordId?: string;

  icdeSelectType?: string;

  tableId?: string;

  numberSelectItem?: boolean;
}

const maxResultCount = 100;
const IcdeSelect = (props: IcdeSelectProps) => {
  const icdeInputRef = useRef(null);

  const [dataSource, setDataSource] = useState<IcdeOperItem[]>([]);

  const [offset, setOffset] = useState(0);
  const [recordTotal, setRecordTotal] = useState(0);

  const [loading, setLoading] = useState(false);
  const [keyword, setKeyword] = useState(undefined);
  const [keywordChange, setKeywordChange] = useState(false);

  const [hasSearched, setHasSearched] = useState(false);

  const [errorTooltipOpen, setErrorTooltipOpen] = useState(false);

  //  keyboard friendly
  const [optionOpen, setOptionOpen] = useState(false);
  const listRef = React.useRef(null);

  useEffect(() => {
    if (props?.selectFormKey) {
      Emitter.on(getDeletePressEventKey(props?.selectFormKey), (key) => {
        if (key.includes(props?.selectFormKey)) {
          // 表示就是当前组件的formKey
          onIcdeSelectClear();
        }
      });
    }

    return () => {
      if (props?.selectFormKey) {
        Emitter.off(getDeletePressEventKey(props?.selectFormKey));
      }
    };
  }, []);

  const setDataSourceOrNot = (keyword: string, searchKeyword: string) => {
    if (props?.instantSelect) {
      return !!searchKeyword;
    } else {
      return keyword && searchKeyword;
    }
  };

  const getDiagnosisDataSource = async (offset, searchKeyword) => {
    if (!searchKeyword) {
      return;
    }

    if (
      offset !== 0 &&
      (dataSource.length >= recordTotal || offset >= recordTotal)
    ) {
      // 表示 全量数据
      return;
    }

    setLoading(true);
    let data = {
      Keyword: searchKeyword?.trim(),
      SkipCount: offset,
      HasInsurCompare: true,
      HasHqmsCompare: true,
      HasDrgsCompare: true,
      MaxResultCount: maxResultCount,
    };

    data = searchArgsProcessor(data, props?.icdeSelectType);

    let diagnosisDataSourceResponse: RespVO<IcdeOperResp> =
      await uniCommonService(
        props?.interfaceUrl ?? 'Api/Insur/InsurSearch/Icde',
        {
          params: data,
        },
      );

    if (diagnosisDataSourceResponse?.code === 0) {
      if (diagnosisDataSourceResponse?.statusCode === 200) {
        let existDataSource = offset === 0 ? [] : dataSource.slice();
        if (setDataSourceOrNot(keyword, searchKeyword)) {
          setDataSource(
            existDataSource.concat(
              diagnosisDataSourceResponse?.data?.Data?.map((item) => {
                return {
                  ...item,
                  key: item?.Code,
                  value: item?.Code,
                  title: item?.Code,
                  label: `${item?.Code} ${item?.Name}`,
                  IcdeCode: item?.Code,
                  IcdeName: item?.Name,
                };
              }) || [],
            ),
          );
          setOffset(offset + maxResultCount);

          setRecordTotal(diagnosisDataSourceResponse?.data?.RecordsTotal);
        } else {
          setDataSource([]);
          setOffset(0);
          setRecordTotal(0);
        }
      }
    }

    setLoading(false);
  };

  const { run: getDiagnosisDataSourceWithDebounce } = useDebounceFn(
    (offset, keyword) => {
      getDiagnosisDataSource(offset, keyword);
    },
    {
      wait: 200,
    },
  );

  const searchArgsProcessor = (data, icdeSelectType) => {
    data[icdeSelectType] = true;
    return data;
  };

  const onIcdePopUpScroll = (event) => {
    let contentElement = event.target;
    let scrollNearlyEnd =
      Math.abs(
        contentElement.scrollHeight -
          contentElement.scrollTop -
          contentElement.clientHeight,
      ) < 100;
    console.error('nearly end', scrollNearlyEnd);
    if (scrollNearlyEnd && !loading) {
      getDiagnosisDataSource(offset, keyword);
    }
  };

  const onIcdeSelectClear = () => {
    let fieldsValue = {};
    if (props?.formKeys) {
      // 第一个是文本 第二个是编码
      Object.keys(props?.formKeys).forEach((key) => {
        fieldsValue[key] = '';
      });
    }
    if (props?.form) {
      if (props?.itemKey) {
        fieldsValue[props?.itemKey] = {};
      }
      if (props?.recordId) {
        Object.keys(fieldsValue)?.forEach((key) => {
          props?.form.setFieldValue([props?.recordId, key], fieldsValue[key]);
        });
      } else {
        props?.form.setFieldsValue(fieldsValue);
      }
    }

    // 清一下dataSource  keyword offset total
    setDataSource([]);
    setHasSearched(false);
    setKeyword(undefined);
    setOffset(0);
    setRecordTotal(0);

    setOptionOpen(false);

    listRef?.current?.clear();

    if (props?.onChangeValueProcessor) {
      fieldsValue = props?.onChangeValueProcessor(fieldsValue);
    }

    props?.onChange && props?.onChange(fieldsValue);
  };

  const onIcdeSelect = (value: string, externalDataSource?: any[]) => {
    let fieldsValue = {};
    let currentSelected = (externalDataSource ?? dataSource)?.find(
      (item) => item.Code === value,
    );
    if (currentSelected) {
      if (props?.formKeys) {
        // 第一个是文本 第二个是编码
        Object.keys(props?.formKeys).forEach((key) => {
          // 微创手术这个单独做处理
          if (key === 'IcdeExtra') {
            fieldsValue[key] = Object.keys(icdeExtraMap)?.filter((key) => {
              if (key === 'InsurIsObsolete') {
                return currentSelected?.['IsObsolete'] ?? false;
              } else {
                return currentSelected?.[key] ?? false;
              }
            });
          }
          // 首页诊断 不处理
          else if (key === 'SrcIcdeCode' || key === 'SrcIcdeName') {
            fieldsValue[key] = fieldsValue[key] || undefined;
          } else {
            fieldsValue[key] = currentSelected?.[props?.formKeys[key]];
          }
        });
      }
      if (props?.form) {
        if (props?.itemKey) {
          fieldsValue[props?.itemKey] = currentSelected;
        }
        if (props?.recordId) {
          console.log('fieldsValue', fieldsValue);
          let recordFieldValue = cloneDeep(fieldsValue);
          Object.keys(recordFieldValue)?.forEach((key) => {
            props?.form.setFieldValue(
              [props?.recordId, key],
              recordFieldValue[key],
            );
          });
        } else {
          props?.form.setFieldsValue(fieldsValue);
        }
      }

      if (props?.onChangeValueProcessor) {
        fieldsValue = props?.onChangeValueProcessor(fieldsValue);
      }

      props?.onChange && props?.onChange(fieldsValue);

      setDataSource([]);
      setErrorTooltipOpen(false);
      setHasSearched(false);
      setOffset(0);
      setRecordTotal(0);
      setKeyword(undefined);

      setTimeout(() => {
        icdeInputRef?.current?.focus();
      }, 0);
    }
  };

  useEffect(() => {
    if (dataSource?.length > 0) {
      setTimeout(() => {
        let dropDownElement = document.getElementById(
          'icde-select-dropdown-container',
        );
        if (dropDownElement) {
          let firstLi = dropDownElement?.querySelector('ul li');
          if (firstLi) {
            (firstLi as any)?.focus();
            setTimeout(() => {
              (firstLi as any)?.scrollIntoView({
                block: 'center',
                inline: 'center',
              });
            }, 100);
          }
        }
      }, 0);
    }
  }, [dataSource]);

  const Wrapper = props?.recordId ? EmptyWrapper : Form.Item;

  const extraProps = props?.recordId ? { value: props?.value } : {};

  const icdeKeyboardFriendlyProps = numberSelectItem
    ? {
        dropdownRender: (menu: any) => {
          return (
            <IcdeOperKeyboardFriendlyDropdown
              type={'Icde'}
              enableKeyboardFriendlySelect={enableKeyboardFriendlySelect}
              listRef={listRef}
              interfaceUrl={
                props?.interfaceUrl ?? 'Api/Insur/InsurSearch/IcdeReorder'
              }
              value={props?.value}
              onSelectChange={(value, item, dataSources) => {
                onIcdeSelect(value, dataSources);
              }}
              optionOpen={optionOpen}
              setOptionOpen={setOptionOpen}
              searchKeyword={keyword}
              setSearchKeyword={setKeyword}
              instantSelect={props?.instantSelect}
              icdeSelectType={props?.icdeSelectType}
            />
          );
        },
        open: optionOpen && !isEmptyValues(keyword),
        onDropdownVisibleChange: (visible: boolean) => setOptionOpen(visible),
        onKeyboardNumberSelect: (event: any, index: any) => {
          listRef?.current?.onKeyboardNumberSelect(event, index);
        },
        onKeyboardPageFlip: (event: any) => {
          listRef?.current?.onKeyboardPageFlip(event);
        },
        onPopupScroll: () => {},
      }
    : {};

  return (
    <div className={'form-content-item-container'}>
      <Tooltip
        open={errorTooltipOpen}
        color={'rgba(235, 87, 87, 0.85)'}
        title={'诊断不存在，请检查后重新选择'}
      >
        <Wrapper className={'icde-table-item'} name={props?.selectFormKey}>
          <UniAntdSelect
            id={`formItem#${props?.componentId}#IcdeSelect`}
            className={`select`}
            {...extraProps}
            showSearch
            showAction={props?.instantSelect ? ['focus'] : []}
            showArrow={false}
            allowClear={false}
            disabled={props?.disabled}
            dropdownMatchSelectWidth={false}
            dropdownAlign={props?.dropdownAlign}
            optionLabelProp={'value'}
            getPopupContainer={(trigger) =>
              (props.getPopupContainer && props?.getPopupContainer(trigger)) ||
              getSelectorDropdownContainerNode()
            }
            onInputKeyDown={(event) => {
              if (hasSearched) {
                if (event.key === 'Enter' && dataSource?.length === 0) {
                  setErrorTooltipOpen(true);
                  event.preventDefault();
                  event.stopPropagation();
                }
              }
            }}
            enterSwitch={true}
            contentEditable={!inputFocusNotSelectAll}
            dropdownStyle={props?.dropdownStyle || {}}
            listHeight={tableSelectorDropdownHeight ?? props?.listHeight}
            placeholder={
              props?.selectFormKey
                ? props?.form?.getFieldValue(props?.selectFormKey)
                : '请选择诊断'
            }
            onSearch={(searchKeyword) => {
              if (enableKeyboardFriendlySelect === true) {
                setKeyword(searchKeyword);
                return;
              }

              if (numberSelectItem !== true) {
                setHasSearched(true);
                if (searchKeyword) {
                  if (searchKeyword !== keyword) {
                    // fetch
                    getDiagnosisDataSourceWithDebounce(0, searchKeyword);
                  } else {
                    getDiagnosisDataSourceWithDebounce(offset, searchKeyword);
                  }
                } else {
                  setDataSource([]);
                  setOffset(0);
                  setRecordTotal(0);
                }
              }
              setKeyword(searchKeyword);
            }}
            onPopupScroll={onIcdePopUpScroll}
            filterOption={false}
            notFoundContent={loading ? <Spin size="small" /> : null}
            onFocus={() => {
              if (props?.instantSelect) {
                if (!isEmptyValues(props?.value)) {
                  setKeyword(props?.value);
                  if (numberSelectItem !== true) {
                    getDiagnosisDataSource(0, props?.value);
                  }
                }
              }

              listRef?.current?.onFocus(null, props?.value);
            }}
            onBlur={(event) => {
              if (numberSelectItem !== true) {
                setTimeout(() => {
                  setDataSource([]);
                  setHasSearched(false);
                  setErrorTooltipOpen(false);
                  setKeyword('');
                  setOffset(0);
                  setRecordTotal(0);
                }, 0);
              }
            }}
            onClear={() => {
              onIcdeSelectClear();
            }}
            onKeyDown={(event) => {
              console.log('onKeyDown', event?.key);

              // 当且仅当
              if (numberSelectItem === true) {
                listRef?.current?.onKeyDown(event);
              }

              if (
                props?.tableId &&
                (event as any)?.hosted !== true &&
                event?.ctrlKey === false
              ) {
                if (event?.key === 'ArrowUp') {
                  Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
                    event: event,
                    type: 'UP',
                    trigger: 'selectkeydown',
                  });
                }

                if (event?.key === 'ArrowDown') {
                  Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
                    event: event,
                    type: 'DOWN',
                    trigger: 'selectkeydown',
                  });
                }
              }
            }}
            onSelect={(value) => {
              onIcdeSelect(value);
            }}
            options={
              numberSelectItem
                ? [
                    {
                      Code: 'Default',
                      Name: 'Default',
                    },
                  ]
                : dataSource
            }
            dumbOnComposition={true}
            mousedownOptionOpen={false}
            doubleClickPopUp={false}
            numberSelectItem={numberSelectItem}
            // keyboard friendly
            {...icdeKeyboardFriendlyProps}
          />
        </Wrapper>
      </Tooltip>
    </div>
  );
};

export default IcdeSelect;
