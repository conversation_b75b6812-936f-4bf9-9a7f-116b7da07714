import './type.less';
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { EventConstant } from '@uni/utils/src/emitter';
import {
  Button,
  Card,
  Col,
  Row,
  Input,
  Spin,
  TableProps,
  Tag,
  Tree,
  Space,
  Divider,
  message,
} from 'antd';
import { useRequest } from 'umi';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src';
import { TableColumns, RespVO } from '@uni/commons/src/interfaces';
import { useModel } from '@@/plugin-model/useModel';
import UniTable from '@uni/components/src/table';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { OptsProps, SearchItemsOpts } from './formItems';
import SearchForm from '@/components/searchForm';
import IconBtn from '@uni/components/src/iconBtn';
import { DownOutlined } from '@ant-design/icons';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { ExportIconBtn } from '@uni/components/src';
import cloneDeep from 'lodash/cloneDeep';
import { isEmptyValues } from '@uni/utils/src/utils';
import { useUpdateEffect } from 'ahooks';
import pick from 'lodash/pick';
import { nextPreviousDmrInfoTableDataSetter } from '@uni/utils/src/next-dmr-utils';
import { dateRangeValueProcessor } from '@uni/components/src/date-range-with-type';
import { isEmptyObj } from '@/utils/widgets';
import { getSessionStorage } from '@/utils/utils';
import { v4 as uuidv4 } from 'uuid';

const { Search } = Input;

const headerSearchForceModified =
  (window as any).externalConfig?.['common']?.headerSearchForceModified ??
  false;

const TypeDetail = () => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster');

  const [searchedValue, setSearchedValue] = useState(() => {
    let curObj;
    if (isEmptyObj(searchParams)) {
      const obj = getSessionStorage('searchOpts');
      let currentObj = { ...obj };
      let startDate = obj?.['dateRange']?.[0];
      let endDate = obj?.['dateRange']?.[1];
      if (startDate && endDate) {
        let dates = dateRangeValueProcessor(obj, true, true);
        currentObj['Sdate'] = dates?.['Sdate'];
        currentObj['Edate'] = dates?.['Edate'];
        currentObj['dateRange'] = [dates?.['Sdate'], dates?.['Edate']];
      }
      curObj = {
        ..._.omit(currentObj, 'OutType'),
      };
    } else {
      curObj = {
        ..._.omit(searchParams, 'OutType'),
      };
    }
    return curObj;
  });

  const [problematicRecordCnt, setProblematicRecordCnt] = useState(null);

  const [clickSubRule, setClickSubRule] = useState<any>({});

  const [defaultTreeData, setDefaultTreeData] = useState([]);

  const [selectedHisId, setSelectedHisId] = useState(undefined);

  const cmrInfoViewRef = useRef(null);

  const [
    deptSubRuleDetailTableDataSource,
    setDeptSubRuleDetailTableDataSource,
  ] = useState([]);

  useEffect(() => {
    deptSubRuleDetailColumnsReq();
  }, []);

  useUpdateEffect(() => {
    getQcRuleTypeAnalReq();
    resetStates();
  }, [searchedValue]);

  const resetStates = () => {
    setClickSubRule({});
    setDeptSubRuleDetailTableDataSource([]);
    setProblematicRecordCnt(null);
    setSelectedHisId(undefined);
  };

  const {
    data: qcRuleTypeAnalData,
    loading: getQcRuleTypeAnalLoading,
    run: getQcRuleTypeAnalReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Emr/EmrQcReport/GetQcStats`, {
        method: 'POST',
        data: {
          ...pick(searchedValue, Object.keys(new OptsProps())),
          Coders: searchedValue?.Coder || [],
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.at(0)?.['RuleTypeStats'].length) {
            setProblematicRecordCnt(res.data[0]?.['ProblematicCardCnt']);
            let treeData = _.map(
              _.orderBy(res.data[0]?.['RuleTypeStats'], ['RuleType']),
              (data1, index1) => {
                let children = [];
                if (data1.SubTypeStats.length !== 0) {
                  children = _.map(data1.SubTypeStats, (data2, index2) => {
                    let children = [];
                    if (data2.RuleStats.length !== 0) {
                      children = _.map(data2.RuleStats, (data3, index3) => ({
                        key: `key-${index1}-${index2}-${index3}`,
                        title: data3.DisplayErrMsg,
                        name: data3.DisplayErrMsg,
                        value: data3.RuleCnt,
                        args: { RuleCode: data3.RuleCode },
                      }));
                    }
                    return {
                      key: `key-${index1}-${index2}`,
                      title: data2.SubType,
                      name: data2.SubType,
                      value: data2.SubTypeCnt,
                      children,
                      args: {
                        RuleType: data1.RuleType,
                        SubType: data2.SubType,
                      },
                    };
                  });
                }
                return {
                  key: `key-${index1}`,
                  title: data1.RuleType,
                  name: data1.RuleType,
                  value: data1.RuleTypeCnt,
                  children,
                  args: { RuleType: data1.RuleType },
                };
              },
            );
            treeData = [
              {
                key: 'key-main',
                title: '全部',
                name: '全部',
                value: res.data[0]?.['ProblematicCardCnt'],
                children: treeData,
                args: {},
              },
            ];
            setDefaultTreeData(treeData);
          } else {
            setDefaultTreeData([]);
            setDeptSubRuleDetailTableDataSource([]);
            setProblematicRecordCnt(null);
            setSelectedHisId(undefined);
          }
        }
      },
    },
  );

  const [deptSubRuleDetailColumns, setDeptSubRuleDetailColumns] = useState([]);
  const { run: deptSubRuleDetailColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Emr/EmrQcReport/GetQcCardByReviewResult', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setDeptSubRuleDetailColumns(
            tableColumnBaseProcessor(
              [
                { dataIndex: 'PatNo', fixed: 'left', orderable: false },
                { dataIndex: 'PatName', orderable: false },
                { dataIndex: 'OutDate', orderable: false },
                { dataIndex: 'CliDept', orderable: false },
                { dataIndex: 'Coder', width: 100 },
                { dataIndex: 'CodeTime', width: 130 },
                {
                  dataIndex: 'ReviewResults',
                  title: '审核结果',
                  width: 'auto',
                  render: (text, record) => {
                    return record?.ReviewResults.map((item) => (
                      <Tag key={uuidv4()} style={{ margin: '5px 5px 0 0' }}>
                        {item.ErrMsg}
                      </Tag>
                    ));
                  },
                },
              ],
              response?.data?.Columns,
            ),
          );
        } else {
          setDeptSubRuleDetailColumns([]);
        }
      },
    },
  );

  // 后端分页
  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  const backTableOnChange: TableProps<any>['onChange'] = (page) => {
    setBackPagination({
      ...backPagination,
      current: page.current,
      pageSize: page.pageSize,
    });

    deptSubRuleDetailTableReq(page.current, page.pageSize);
  };

  const {
    loading: deptSubRuleDetailTableLoading,
    run: deptSubRuleDetailTableReq,
  } = useRequest(
    (current, pageSize) => {
      let data = {
        DtParam: {
          Draw: 1,
          Start: (current - 1) * pageSize,
          Length: pageSize,
        },
        ...pick(searchedValue, Object.keys(new OptsProps())),
        Coders: searchedValue?.Coder || [],
        ...clickSubRule?.args,
      };

      return uniCommonService('Api/Emr/EmrQcReport/GetQcCardByReviewResult', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0) {
          nextPreviousDmrInfoTableDataSetter(
            response?.data,
            setDeptSubRuleDetailTableDataSource,
            backPagination,
            setBackPagination,
          );
        } else {
          setDeptSubRuleDetailTableDataSource([]);
          setSelectedHisId(undefined);
        }

        return response?.data?.data ?? [];
      },
    },
  );

  useEffect(() => {
    if (Object.keys(clickSubRule).length !== 0)
      deptSubRuleDetailTableReq(1, 10);
  }, [clickSubRule]);

  // tree
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  const dataList: { key; title: string }[] = [];
  const generateList = (data: any[]) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { key, title } = node;
      dataList.push({ key, title });
      if (node.children) {
        generateList(node.children);
      }
    }
  };
  generateList(defaultTreeData);
  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  const getParentKey = (key: React.Key, tree: any[]): React.Key => {
    let parentKey: React.Key;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    return parentKey!;
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (value?.length === 0) {
      setExpandedKeys([]);
    } else {
      const newExpandedKeys = dataList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            return getParentKey(item.key, defaultTreeData);
          }
          return null;
        })
        .filter((item, i, self) => item && self.indexOf(item) === i);
      setExpandedKeys(newExpandedKeys as React.Key[]);
    }
    setSearchValue(value);
    setAutoExpandParent(true);
  };

  const treeData = useMemo(() => {
    const loop = (data: any[]) =>
      data
        .map((item) => {
          const strTitle = item.title as string;
          const index = strTitle.indexOf(searchValue);
          const beforeStr = strTitle.substring(0, index);
          const afterStr = strTitle.slice(index + searchValue.length);
          const title =
            index > -1 ? (
              <span>
                {beforeStr}
                <span className="site-tree-search-value">{searchValue}</span>
                {afterStr}
              </span>
            ) : (
              <span>{strTitle}</span>
            );
          if (item.children) {
            return { ...item, title, children: loop(item.children) };
          }

          return {
            ...item,
            title,
          };
        })
        ?.sort((a, b) => (b?.value ?? Infinity) - (a?.value ?? Infinity));
    return loop(defaultTreeData);
  }, [searchValue, defaultTreeData]);

  const onSelect = (selectedKeys, info) => {
    setClickSubRule({
      item: info?.node,
      args: info?.node?.args,
    });
  };

  const onExtraSearchedHisIdLastOne = async () => {
    let current = backPagination.current + 1;
    setBackPagination({
      ...backPagination,
      current: current,
    });

    let nextPageData = await deptSubRuleDetailTableReq(
      current,
      backPagination?.pageSize,
    );
    setSelectedHisId(undefined);

    return (
      nextPageData
        ?.map((item) => item?.HisId)
        ?.filter((item) => !isEmptyValues(item)) ?? []
    );
  };

  const onEmrInfoViewClick = (record: any) => {
    (global?.window as any)?.eventEmitter?.emit('DMR_Index_STATUS', {
      status: true,
      hisId: record.HisId,
      extraSearchedHisIds: deptSubRuleDetailTableDataSource
        ?.map((item) => item?.HisId)
        ?.filter((item) => !isEmptyValues(item)),
    });
  };

  React.useImperativeHandle(cmrInfoViewRef, () => {
    return {
      onEmrInfoViewClick: (record) => {
        onEmrInfoViewClick(record);
      },
    };
  });

  return (
    <>
      <Card style={{ marginBottom: '20px' }}>
        <SearchForm
          className="casebook_flex_form"
          grid
          rowProps={{ gutter: [16, 16] }}
          searchOpts={[
            ...SearchItemsOpts(searchedValue, {
              hospOpts: dictData.Hospital,
              deptOpts: dictData.CliDepts,
              coderOpts: dictData?.['Dmr']?.Coder,
            }),
            {
              title: '规则等级',
              dataType: 'select',
              name: 'ErrorLevel',
              opts: [
                { Name: '提示性规则错误', Code: '2' },
                { Name: '审核强制规则错误', Code: '5' },
              ],
              initialValue: searchedValue?.ErrorLevel,
              fieldProps: { mode: 'single' },
              colProps: { span: 6 },
            },
          ]}
          submitter={{
            render: (props, doms) => {
              return [
                <Button
                  type="primary"
                  style={{
                    width: '150px',
                    float: 'right',
                  }}
                  key="reset"
                  onClick={() => {
                    props.form?.submit?.();
                  }}
                >
                  查询
                </Button>,
              ];
            },
          }}
          onFinish={async (values) => {
            let dates = dateRangeValueProcessor(values, true, true);
            values['Sdate'] = dates?.['Sdate'];
            values['Edate'] = dates?.['Edate'];
            values['dateRange'] = [dates?.['Sdate'], dates?.['Edate']];

            let searchValues = {
              ...values,
              UseCodeTimeFilter:
                values.DateType === 'RegisterDate' ? true : false,
              ErrorLevel: values?.ErrorLevel,
            };

            setSearchedValue(
              headerSearchForceModified === false
                ? cloneDeep(searchValues)
                : searchValues,
            );
            (global?.window as any)?.eventEmitter?.emit(
              EventConstant.HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY,
              {
                ...searchValues,
                searchNow: headerSearchForceModified === false,
              },
            );
            return true;
          }}
        />
      </Card>
      <div className="check-result-container" id="check-result-container">
        <Card>
          <Row gutter={20}>
            <Col span={10} className="border-right">
              <div
                className="d-flex"
                style={{ justifyContent: 'space-between', padding: '12px 0px' }}
              >
                <div className="title">质控分类</div>
                <div>
                  <Tag color="red" style={{ marginRight: 0 }}>
                    问题病例数：{problematicRecordCnt || ''}
                  </Tag>
                </div>
              </div>
              <Spin spinning={getQcRuleTypeAnalLoading}>
                <div className="left-container">
                  <Search
                    style={{ padding: '0 0px 8px' }}
                    placeholder="搜索关键字"
                    onChange={onChange}
                  />
                  <Tree
                    showLine
                    switcherIcon={
                      <DownOutlined
                        onPointerEnterCapture={false}
                        onPointerLeaveCapture={false}
                      />
                    }
                    blockNode
                    onExpand={onExpand}
                    expandedKeys={
                      expandedKeys.length ? expandedKeys : ['key-main']
                    }
                    selectedKeys={[clickSubRule?.item?.key]}
                    autoExpandParent={autoExpandParent}
                    treeData={treeData?.sort(
                      (a, b) => (b?.value ?? Infinity) - (a?.value ?? Infinity),
                    )}
                    onSelect={onSelect}
                    // height={}
                    titleRender={(nodeData: any) => {
                      return (
                        <>
                          {' '}
                          <div>{nodeData?.title}</div>{' '}
                          <div>
                            <Tag>{nodeData?.value}</Tag>
                          </div>
                        </>
                      );
                    }}
                    style={{
                      maxHeight:
                        document.getElementById('site-layout-content')
                          ?.offsetHeight -
                        50 -
                        50 -
                        50 -
                        24,
                      overflowY: 'auto',
                    }}
                  />
                </div>
              </Spin>
            </Col>
            <Col span={14}>
              <UniTable
                scroll={{ x: 'max-content' }}
                id={'qc-dept-detail-table'}
                className="qc-dept-detail-table"
                rowClassName={(record, index) => {
                  return !isEmptyValues(record?.HisId) &&
                    record?.HisId === selectedHisId
                    ? 'table-hisId-row-selected'
                    : '';
                }}
                title={() => (
                  <>
                    <div
                      className="table-header d-flex"
                      style={{ justifyContent: 'space-between' }}
                    >
                      <span className="title">{`${
                        clickSubRule?.item?.name || ''
                      } 病案明细`}</span>
                      <Space>
                        <Divider type="vertical" />
                        <ExportIconBtn
                          isBackend={true}
                          backendObj={{
                            url: 'Api/Emr/EmrQcReport/ExportGetQcCardByReviewResult',
                            method: 'POST',
                            data: {
                              ...searchedValue,
                              Coders: searchedValue?.Coder || [],
                              ...clickSubRule?.args,
                            },
                            fileName: `${clickSubRule?.item?.name}病案明细`,
                          }}
                          btnDisabled={
                            deptSubRuleDetailTableDataSource?.length < 1
                          }
                        />
                        <TableColumnEditButton
                          {...{
                            columnInterfaceUrl:
                              'Api/Emr/EmrQcReport/GetQcCardByReviewResult',
                            onTableRowSaveSuccess: (columns) => {
                              setDeptSubRuleDetailColumns(
                                tableColumnBaseProcessor(
                                  [
                                    {
                                      dataIndex: 'PatNo',
                                      fixed: 'left',
                                      orderable: false,
                                    },
                                    { dataIndex: 'PatName', orderable: false },
                                    { dataIndex: 'OutDate', orderable: false },
                                    { dataIndex: 'CliDept', orderable: false },
                                    { dataIndex: 'Coder', width: 100 },
                                    { dataIndex: 'CodeTime', width: 130 },
                                    {
                                      dataIndex: 'ReviewResults',
                                      title: '审核结果',
                                      width: 'auto',
                                      render: (text, record) => {
                                        return record?.ReviewResults.map(
                                          (item) => (
                                            <Tag
                                              key={uuidv4()}
                                              style={{ margin: '5px 5px 0 0' }}
                                            >
                                              {item.ErrMsg}
                                            </Tag>
                                          ),
                                        );
                                      },
                                    },
                                  ],
                                  columns,
                                ),
                              );
                            },
                          }}
                        />
                      </Space>
                    </div>
                  </>
                )}
                columns={
                  (deptSubRuleDetailColumns.length && [
                    {
                      title: '',
                      colSize: 0.5,
                      width: 40,
                      fixed: 'left',
                      align: 'center',
                      visible: true,
                      valueType: 'option',
                      render: (_, record) => {
                        return [
                          <IconBtn
                            type="checkDoctorInfo"
                            className="operation-btn"
                            onClick={() => {
                              if (!isEmptyValues(record?.HisId)) {
                                cmrInfoViewRef?.current?.onEmrInfoViewClick(
                                  record,
                                );
                              } else {
                                message.error(
                                  '没有可查看的医生明细--HisId为空',
                                );
                              }
                            }}
                          />,
                        ];
                      },
                    },
                    ...deptSubRuleDetailColumns,
                  ]) ||
                  []
                }
                rowKey={'aguid'}
                loading={deptSubRuleDetailTableLoading}
                dataSource={deptSubRuleDetailTableDataSource || []}
                pagination={
                  deptSubRuleDetailTableDataSource?.length
                    ? backPagination
                    : false
                }
                onChange={backTableOnChange}
                dictionaryData={dictData}
              />
            </Col>
          </Row>
        </Card>
      </div>
    </>
  );
};

export default TypeDetail;
