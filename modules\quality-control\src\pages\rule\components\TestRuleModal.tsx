import { useRef, useState } from 'react';
import { Modal, Button, Row, Col, Spin, Typography } from 'antd';
import { UniSelect } from '@uni/components/src';
import JsonSrcTest from '@uni/components/src/json-src-test';
import ReactJson from 'react-json-view';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';

interface TestRuleModalProps {
  visible: boolean;
  title?: string;
  onCancel: () => void;
  qualityCheckRulesData: any[];
  globalState: any;
}

const TestRuleModal: React.FC<TestRuleModalProps> = ({
  visible,
  title,
  onCancel,
  qualityCheckRulesData,
  globalState,
}) => {
  const [selectedRuleCodes, setSelectedRuleCodes] = useState<string[]>([]);
  const jsonFormData = useRef<any>(undefined);

  // 将测试规则的API请求逻辑移至组件内
  const {
    data: testCheckRuleData,
    loading: testCheckRuleLoading,
    mutate: mutateTestCheckRule,
    run: testCheckRuleReq,
  } = useRequest(
    (codes, data) =>
      uniCommonService(`Api/Sys/QualitySys/TestQualityCheckRule`, {
        method: 'POST',
        data: {
          AppCode: 'Dmr',
          TemplateType: 'Drgs-Review',
          RuleCodes: codes,
          CardJson: data,
        },
      }),
    {
      manual: true,
      onSuccess: (res: RespVO<any>) => {
        console.log(res);
      },
      onError: (res: RespVO<any>) => {},
    },
  );

  const handleCancel = () => {
    setSelectedRuleCodes([]);
    mutateTestCheckRule(undefined);
    onCancel();
  };

  const handleTest = () => {
    console.log(jsonFormData.current);
    testCheckRuleReq(selectedRuleCodes, jsonFormData.current?.stringifyValue);
  };

  return (
    <Modal
      width={1200}
      style={{ top: '30px' }}
      title={
        title ? (
          <Typography.Text
            ellipsis={{ tooltip: title }}
            style={{ maxWidth: 'calc(100% - 30px)', display: 'inline-block' }}
          >
            {title}
          </Typography.Text>
        ) : null
      }
      open={visible}
      onCancel={handleCancel}
      okButtonProps={{ style: { display: 'none' } }}
      cancelText="关闭"
    >
      <Row gutter={[12, 12]}>
        <Col span={5}>
          <label>1. 选择测试用RuleCodes</label>
          <div>
            <UniSelect
              style={{ width: '100%' }}
              enableSelectAll
              dataSource={qualityCheckRulesData?.map((d) => ({
                value: d?.RuleCode,
                label: `${d?.RuleCode}  ${d?.DisplayErrMsg}`,
              }))}
              value={selectedRuleCodes}
              requiredStatus
              mode="multiple"
              maxTagCount={12}
              placeholder="请选择以操作"
              showSearch
              onChange={(value) => {
                setSelectedRuleCodes(value);
              }}
            />
          </div>
        </Col>
        <Col span={10}>
          <label>2. 设置测试用Json</label>
          <JsonSrcTest
            dictData={globalState?.dictData}
            onValueChange={(values) => {
              jsonFormData.current = values;
            }}
          />
          <Button onClick={handleTest}>测试</Button>
        </Col>
        <Col span={8}>
          <label>3. 测试结果</label>
          <Spin spinning={testCheckRuleLoading}>
            <ReactJson
              name={false}
              collapsed={2}
              enableClipboard={false}
              onEdit={false}
              onDelete={false}
              onAdd={false}
              src={testCheckRuleData}
              theme="summerfruit:inverted"
              style={{ marginTop: '10px' }}
            />
          </Spin>
        </Col>
      </Row>
    </Modal>
  );
};

export default TestRuleModal;
