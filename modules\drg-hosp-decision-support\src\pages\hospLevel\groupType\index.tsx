import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { UniSelect } from '@uni/components/src';
import { Col, Space, Row, Tabs, Select } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import SingleColumnTable from '@/components/SingleColumnTable/index';
import _ from 'lodash';
import { uniCommonService, uniHqmsService } from '@uni/services/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import '../index.less';
import ConfigToImg from '@uni/components/src/config2Img';
import DrillDownDiseaseStructure from '@/components/DrillDownDiseaseStructure/index';
import Stats from '@/components/stats/index';
import BCGMatrixAndTable from '@/components/BCGMatrixAndTable/index';
import {
  GroupTypeCliDeptAxisOpts,
  GroupTypeDefaultOpts,
  GroupTypeDoctorAxisOpts,
  GroupNormalStat,
  GroupTypeMedTeamAxisOpts,
} from '@/constants';
import BmTable from '@/components/BmTable/index';
import { RespVO } from '@uni/commons/src/interfaces';
import { GroupBmChartSelectOptions } from './constants';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';

const DiseaseType = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('groupStructure');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedItem, setSelectedItem] = useState(undefined);

  const [selectOpts, setSelectOpts] = useState([]);

  const [selectedDoctorValue, setSelectedDoctorValue] = useState(undefined);

  useEffect(() => {
    setSelectedDoctorValue(dictData?.['DoctorType']?.at(0)?.Code);
  }, [dictData?.['DoctorType']]);

  // tab 使用下拉框数据
  const {
    data: BundledData,
    loading: ADrgCompositionLoading,
    run: ADrgCompositionReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/v2/Drgs/HospDrgComposition/BundledDrgComposition', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.length) {
            setSelectOpts(
              _.orderBy(res.data, 'PatCnt', 'desc')?.map((d) => ({
                ...d,
                label: `${d?.DrgCode} ${d?.DrgName}`,
              })),
            );
            // 默认把第一个设置为selected
            if (!selectedItem) {
              setSelectedItem(
                res?.data?.at(0),
                // _.maxBy(res?.data, function (o) {
                //   return o.PatCnt;
                // }),
              );
            }
          }
          return res?.data;
        }
        setSelectOpts([]);
        return [];
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
    };
    setTableParams(params);
    ADrgCompositionReq(params);
  }, [dateRange, hospCodes]);

  useEffect(() => {
    Emitter.on(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK, (record) => {
      setSelectedItem(record);
      setActiveKey('statistic');
    });

    return () => {
      Emitter.off(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK);
    };
  }, []);

  let tabItems = [
    {
      key: 'groupStructure',
      label: 'DRG组结构',
      children: (
        <DrillDownDiseaseStructure
          label="DRG组结构"
          tableParams={tableParams}
          filterColumns={[
            'DrgCode',
            'DrgName',
            'PatCnt',
            'PatRatio',
            'AvgInPeriod',
            'AvgTotalFee',
            'AvgMedicineFee',
            'MedicineFeeRatio',
            'AvgMaterialFee',
            'MaterialFeeRatio',
          ]}
          compositionApi="Api/v2/Drgs/HospDrgComposition/BundledDrgComposition"
          dataSource={selectOpts}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.DrgName,
              args: {
                ...tableParams,
                VersionedDrgCode: record?.VersionedDrgCode,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'statistic',
      label: '全院综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Drgs/HospDrgComposition/BundledDrgComposition`}
              trendApi={`Api/v2/Drgs/HospDrgComposition/DrgCompositionTrend`}
              columns={GroupNormalStat}
              defaultSelectItem={'PatCnt'}
              type="col-xl-8"
              chartHeight={300}
              tabKey={activeKey}
              useGlobalState
              level="drp"
              selectedTableItem={selectedItem}
              tableParams={tableParams}
              extraApiArgs={{
                VersionedDrgCode: selectedItem?.VersionedDrgCode,
                VersionedDrgCodes: selectedItem?.VersionedDrgCode
                  ? [selectedItem?.VersionedDrgCode]
                  : [],
              }}
            />
          </Col>
          <SingleColumnTable
            title="全院分布"
            args={{
              api: 'Api/v2/Drgs/HospDrgComposition/DrgCompositionByHosp',
              columns: [],
              extraApiArgs: {
                VersionedDrgCode: selectedItem?.VersionedDrgCode,
                VersionedDrgCodes: selectedItem?.VersionedDrgCode
                  ? [selectedItem?.VersionedDrgCode]
                  : [],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="HospName"
            type="table"
            visibleValueKeys={['HospName', 'PatCnt']}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.HospName,
                args: {
                  Sdate: dateRange?.at(0),
                  Edate: dateRange?.at(1),
                  HospCode: [record?.HospCode],
                  VersionedDrgCode: selectedItem?.VersionedDrgCode,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
                dictData: dictData, // 传入
              });
            }}
          />
          {/* <Col span={6}>
            <BCGMatrixAndTable
              title="全院效率"
              tableTitle="全院分布"
              type="hosp"
              emitter={EventConstant.HOSP_TABLE_ROW_CLICK}
              category="HospName"
              listValueKey="PatCnt"
              axisOpts={HospAxisOpts}
              defaultAxisOpt={GroupTypeDefaultOpts}
              args={{
                api: 'Api/Drgs/HospDrgComposition/DrgCompositionByHosp',
                columns: [],
                extraApiArgs: {
                  VersionedDrgCodes: selectedItem?.VersionedDrgCode ? [selectedItem?.VersionedDrgCode] : [],
                },
              }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.HospName,
                  args: {
                    Sdate: dateRange?.at(0),
                    Edate: dateRange?.at(1),
                    HospCode: [record?.HospCode],
                    VersionedDrgCodes: selectedItem?.VersionedDrgCode ? [selectedItem?.VersionedDrgCode] : [],
                  },
                  detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
                  dictData: dictData, // 传入
                });
              }}
            />
          </Col> */}
        </Row>
      ),
    },
    {
      key: 'hosp_deptAnalysis',
      label: '科室综合分析',
      children: (
        <Row gutter={[16, 16]}>
          {/* <Col span={24}>
            <BCGMatrixAndTable
              title="绩效科室效率"
              tableTitle="绩效科室分布"
              type="MajorPerfDept"
              emitter={EventConstant.MAJOR_PERF_DEPT_TABLE_ROW_CLICK}
              category="MajorPerfDeptName"
              axisOpts={HospAxisOpts}
              defaultAxisOpt={GroupTypeDefaultOpts}
              args={{
                // TODO
                api: undefined,
                columns: [],
                extraApiArgs: {
                  VersionedDrgCodes: selectedItem?.VersionedDrgCode ? [selectedItem?.VersionedDrgCode] : [],
                },
              }}
            />
          </Col> */}
          <Col span={24}>
            <BCGMatrixAndTable
              title="临床科室效率"
              tableTitle="临床科室分布"
              // type="hosp"
              emitter={EventConstant.DEPT_TABLE_ROW_CLICK}
              category="CliDeptName"
              listValueKey="PatCnt"
              axisOpts={GroupTypeCliDeptAxisOpts}
              defaultAxisOpt={GroupTypeDefaultOpts}
              args={{
                api: 'Api/v2/Drgs/CliDeptDrgComposition/DrgCompositionByCliDept',
                extraApiArgs: {
                  VersionedDrgCode: selectedItem?.VersionedDrgCode,
                },
                columns: [],
              }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.CliDeptName,
                  args: {
                    ...tableParams,
                    CliDepts: [record?.CliDept],
                    VersionedDrgCode: selectedItem?.VersionedDrgCode,
                  },
                  detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
                  dictData: dictData, // 传入
                });
              }}
            />
          </Col>
        </Row>
      ),
    },
    {
      key: 'hosp_medTeamAnalysis',
      label: '医疗组综合分析',
      children: (
        <Col span={24}>
          <BCGMatrixAndTable
            title="医疗组效率"
            tableTitle="医疗组分布"
            type="hosp"
            emitter={EventConstant.MED_TEAM_TABLE_ROW_CLICK}
            category="MedTeamName"
            listValueKey="PatCnt"
            axisOpts={GroupTypeMedTeamAxisOpts}
            defaultAxisOpt={GroupTypeDefaultOpts}
            args={{
              api: 'Api/v2/Drgs/MedTeamDrgComposition/DrgCompositionByMedTeam',
              extraApiArgs: {
                VersionedDrgCode: selectedItem?.VersionedDrgCode,
              },
              columns: [],
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName,
                args: {
                  ...tableParams,
                  MedTeams: [record?.MedTeam],
                  VersionedDrgCode: selectedItem?.VersionedDrgCode,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
                dictData: dictData, // 传入
              });
            }}
          />
        </Col>
      ),
    },
    {
      key: 'hosp_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <Col span={24}>
          <BCGMatrixAndTable
            title="医生效率"
            tableTitle="医生分布"
            type="hosp"
            emitter={EventConstant.DOCTOR_TABLE_ROW_CLICK}
            category="DoctorName"
            listValueKey="PatCnt"
            axisOpts={GroupTypeDoctorAxisOpts}
            defaultAxisOpt={GroupTypeDefaultOpts}
            args={{
              api: 'Api/v2/Drgs/DoctorDrgComposition/DrgCompositionByDoctor',
              extraApiArgs: {
                VersionedDrgCode: selectedItem?.VersionedDrgCode,
                DoctorType: (
                  selectedDoctorValue as string
                )?.toLocaleUpperCase(),
              },
              columns: [],
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.DoctorName,
                args: {
                  ...tableParams,
                  DoctorCodes: [record?.DoctorCode],
                  DoctorType: (
                    record?.DoctorType as string
                  )?.toLocaleUpperCase(),
                  VersionedDrgCode: selectedItem?.VersionedDrgCode,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
                dictData: dictData, // 传入
              });
            }}
          />
        </Col>
      ),
    },
    {
      key: 'hosp_BmAnalysis',
      label: '标杆值',
      children: (
        <Col span={24}>
          <BmTable
            tableParams={tableParams}
            bundleData={
              selectedItem
                ? [
                    BundledData?.find(
                      (data) =>
                        data?.VersionedDrgCode ===
                        selectedItem?.VersionedDrgCode,
                    ),
                  ]
                : []
            }
            extraApiArgs={{
              VersionedDrgCode: selectedItem?.VersionedDrgCode,
            }}
            api="Api/v2/Drgs/HospDrgComposition/DrgCompositionHospBm"
            BmChartSelectOptions={GroupBmChartSelectOptions}
          />
        </Col>
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
        tabBarExtraContent={{
          right: (
            <Space>
              {activeKey !== 'groupStructure' && (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <label>当前DRG组：</label>
                  <UniSelect
                    width={300}
                    showSearch
                    dataSource={selectOpts}
                    value={selectedItem?.VersionedDrgCode}
                    onChange={(value) => {
                      setSelectedItem(
                        selectOpts?.find((d) => d?.VersionedDrgCode === value),
                      );
                    }}
                    allowClear={false}
                    optionNameKey={'label'}
                    optionValueKey={'VersionedDrgCode'}
                    enablePinyinSearch={true}
                    fieldNames={{
                      // label: 'ChsDrgName',
                      value: 'VersionedDrgCode',
                    }}
                  />
                </div>
              )}
              {activeKey === 'hosp_doctorAnalysis' && (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <label>医生类型：</label>
                  <Select
                    options={dictData?.['DoctorType']}
                    style={{ width: '200px' }}
                    fieldNames={{ value: 'Code', label: 'Name' }}
                    placeholder="请选择"
                    allowClear={false}
                    value={selectedDoctorValue}
                    onChange={(value) => {
                      setSelectedDoctorValue(value);
                    }}
                  />
                </div>
              )}
            </Space>
          ),
        }}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default DiseaseType;
