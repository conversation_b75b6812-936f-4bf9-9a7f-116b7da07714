import { CheckCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { Space, Tooltip } from 'antd';
import dayjs from 'dayjs';

// 出库类型
export enum DmrSignOutType {
  BORROW = 'borrow',
  TRANSFER = 'transfer',
  OTHER = 'other',
}

// 出库类型选项
export const DmrSignOutTypeOptions = [
  { Name: '借出', Code: DmrSignOutType.BORROW },
  { Name: '转移', Code: DmrSignOutType.TRANSFER },
  { Name: '其他', Code: DmrSignOutType.OTHER },
];

// 出库登记表格列配置
export const DmrSignOutColumns = [
  {
    data: 'isCorrect',
    dataIndex: 'isCorrect',
    visible: true,
    align: 'center',
    render: (text: string, record: any) => {
      return (
        <>
          {record?.errMsg ? (
            <Tooltip title={record?.errMsg?.join('。/n')}>
              <WarningOutlined />
            </Tooltip>
          ) : (
            <CheckCircleOutlined />
          )}
        </>
      );
    },
    order: 1,
  },
  // {
  //   data: 'xuhao',
  //   dataIndex: 'xuhao',
  //   title: '序号',
  //   visible: true,
  //   align: 'center',
  //   render: (text, record, index) => {
  //     return index + 1;
  //   },
  //   order: 2,
  // },
];

// 打印列配置
export const DmrSignOutPrintColumns = [
  {
    data: 'index',
    dataIndex: 'index',
    visible: true,
    title: '序号',
    align: 'center',
  },
  {
    data: 'PatNo',
    dataIndex: 'PatNo',
    visible: true,
    title: '病案号',
    align: 'center',
  },
  {
    data: 'PatName',
    dataIndex: 'PatName',
    visible: true,
    title: '患者姓名',
    align: 'center',
  },
  {
    data: 'OutDept',
    dataIndex: 'OutDept',
    visible: true,
    title: '出院科室',
    align: 'center',
  },
  {
    data: 'OutDate',
    dataIndex: 'OutDate',
    visible: true,
    title: '出院日期',
    align: 'center',
    render: (text: any) => {
      return dayjs(text)?.format('YYYY-MM-DD');
    },
  },
  {
    data: 'Doctor',
    dataIndex: 'Doctor',
    visible: true,
    title: '责任医师',
    align: 'center',
  },
  {
    data: 'DmrSignOutDate',
    dataIndex: 'DmrSignOutDate',
    visible: true,
    title: '出库日期',
    align: 'center',
    render: (text: any) => {
      return dayjs(text)?.format('YYYY-MM-DD');
    },
  },
  {
    data: 'DmrSignOutOperator',
    dataIndex: 'DmrSignOutOperator',
    visible: true,
    title: '出库人',
    align: 'center',
  },
];

// 弹窗选择列
export const DmrSignOutModalColumns = [
  {
    dataIndex: 'IsSignedOut',
    title: '出库状态',
    visible: true,
    valueType: 'boolean',
    fixed: 'left',
    width: 90,
  },
];
