export interface IcdeOperResp {
  Data?: IcdeOperItem[];
  RecordsTotal?: number;
}

export interface IcdeOperItem {
  Code?: string;
  Name?: string;

  InsurCode?: string;
  InsurName?: string;
  HqmsCode?: string;
  HqmsName?: string;

  Degree?: string;
  OperType?: string;
  InsurDegree?: string;
  InsurOperType?: string;
  IsObsolete?: boolean;
  HqmsDegree?: string;
  HqmsOperType?: string;
  IsDaySurgery?: boolean;
  IsMicro?: boolean;
  WtCode?: string;
  WtName?: string;
  WtDegree?: string;
  WtOperType?: string;
  DrgsDegree?: string;
  DrgsOperType?: string;

  IsValid?: boolean;
}
