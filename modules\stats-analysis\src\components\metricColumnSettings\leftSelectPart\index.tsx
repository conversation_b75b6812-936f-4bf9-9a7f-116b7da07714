import './index.less';
import {
  Affix,
  Anchor,
  Button,
  Checkbox,
  Col,
  Modal,
  Row,
  Space,
  Switch,
  TableColumnType,
  Tooltip,
  Tree,
  Tag,
  List,
  Form,
  Tabs,
  Badge,
} from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import Search from 'antd/es/input/Search';
import { ColumnItem } from '@uni/commons/src/interfaces';
import {
  pinyinInitialSearch,
  searchFunctionGetter,
} from '@uni/utils/src/pinyin';
import { cloneDeep } from 'lodash';
import { Emitter } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import {
  CheckOutlined,
  CloseOutlined,
  FlagOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { UniDragEditTable } from '@uni/components/src';
import { arrayMoveImmutable } from '@/pages/combine-query/utils';
import {
  MetricsAggregateItem,
  MetricsGroupItem,
  MetricsMetricItem,
} from '@/pages/combine-query/interfaces';
import { metricsItemsProcessor } from '@/pages/combine-query/processor';
import { ReactComponent as IconDiamond } from '@/assets/icon_diamond.svg';
import Diamond from '@uni/commons/src/icon/Diamond';
import Flag from '@uni/commons/src/icon/Flag';
import { SelectedMetricItem } from '@/pages/combine-query/containers/metric';
import {
  metricColumnsSelectCompleteProcessor,
  metricGroupTreeProcessor,
  metricStateSelectCompleteProcessor,
  rightTableColumnsProcessor,
} from '@/pages/combine-query/containers/metric/metric-columns/processors';
import { useModel } from 'umi';
import { metricColumnSettingColumns } from '@/pages/combine-query/containers/metric/metric-columns/columns';
import { isEmptyValues } from '@uni/utils/src/utils';
import MetricPreviewTable from '@/pages/combine-query/containers/metric/metric-columns/preview';
import { AggregationNames, TabKeys } from '../constants';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { ColumnSettingAction } from '../../columnSettings/constants';

const { CheckableTag } = Tag;

const externalStatsAnalysisConfig = (window as any).externalConfig?.[
  'statsAnalysis'
];

const tagBaseStyle = externalStatsAnalysisConfig?.['tagBaseStyle'] ?? {};

interface MetricColumnsSettingTreeProps {
  tabKey?: string;
  treeData?: any[];
  metricsState?: any;
  metricColumnsSettings$?: EventEmitter<any>;
}

const MetricColumnTagsTree = (props: MetricColumnsSettingTreeProps) => {
  const [keyword, setKeyword] = useState('');

  const [basicSelectedAnchor, setBasicSelectedAnchor] = useState<string>('');

  const [statisticsSelectedAnchor, setStatisticsSelectedAnchor] =
    useState<string>('');

  useEffect(() => {
    setAnchorKey(props?.treeData[0]?.key, true);
  }, [props?.treeData]);

  const setAnchorKey = (key, initial = false) => {
    if (props?.tabKey === TabKeys.BASIC) {
      if (initial) {
        if (isEmptyValues(basicSelectedAnchor)) {
          setBasicSelectedAnchor(key);
        }
      } else {
        setBasicSelectedAnchor(key);
      }
    }

    if (props?.tabKey === TabKeys.STATISTICS) {
      if (initial) {
        if (isEmptyValues(statisticsSelectedAnchor)) {
          setStatisticsSelectedAnchor(key);
        }
      } else {
        setStatisticsSelectedAnchor(key);
      }
    }
  };

  useEffect(() => {
    // TODO 转换成table数据
  }, [props?.metricsState, props?.treeData]);

  const elementIsVisibleInViewport = () => {
    props?.treeData
      ?.map((item) => {
        return document.getElementById(item?.key);
      })
      ?.filter((item) => item)
      ?.forEach((el) => {
        const elementRect = el.getBoundingClientRect();
        const containerRect = document
          .getElementById(`metric-tree-container-${props?.tabKey}`)
          .getBoundingClientRect();
        let visible =
          containerRect.top - (elementRect.top - 10) > 0 &&
          containerRect.top - elementRect.top <= elementRect.height;

        if (visible) {
          setAnchorKey(el.id);
        }
      });
  };

  return (
    <div className={'metric-columns-tree-container'}>
      <div className={'flex-row-center'} style={{ margin: '0px 20px' }}>
        <Search
          value={keyword}
          className={'metric-columns-search'}
          style={{ width: '100%' }}
          placeholder="请输入关键字"
          onChange={(event) => {
            setKeyword(event.target.value);
          }}
        />
        <Button
          className={'reset'}
          ghost={true}
          onClick={() => {
            // TODO 重置
            props?.metricColumnsSettings$?.emit({
              type: ColumnSettingAction.ItemReset,
              payload: { tabKey: props?.tabKey },
            });

            Emitter.emit(
              StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_NEED_RESET,
              props?.tabKey,
            );
          }}
        >
          重置
        </Button>
      </div>

      <div className={'metric-columns-tree-content'}>
        <div className="toc-affix">
          <ul id="demo-toc" className="toc">
            {props?.treeData?.map((item) => {
              return (
                <li key={item?.key} title={item?.title}>
                  <a
                    style={{ minWidth: 70 }}
                    className={
                      item?.key === basicSelectedAnchor ||
                      item.key === statisticsSelectedAnchor
                        ? 'selected'
                        : ''
                    }
                    onClick={() => {
                      setAnchorKey(item?.key);
                      document.getElementById(item?.key)?.scrollIntoView({
                        behavior: 'smooth',
                      });
                    }}
                  >
                    {item?.title}
                  </a>
                </li>
              );
            })}
          </ul>
        </div>

        <div
          id={`metric-tree-container-${props?.tabKey}`}
          className={'metric-tree-container'}
          onScroll={(event) => {
            let scrollToBottom =
              document
                .getElementById(`metric-tree-container-${props?.tabKey}`)
                .getBoundingClientRect()?.height +
                document.getElementById(
                  `metric-tree-container-${props?.tabKey}`,
                )?.scrollTop ===
              document.getElementById(`metric-tree-container-${props?.tabKey}`)
                ?.scrollHeight;
            if (scrollToBottom) {
              setAnchorKey(
                props?.treeData?.at(props?.treeData?.length - 1)?.key,
              );
              return;
            }
            elementIsVisibleInViewport();
          }}
        >
          {cloneDeep(props?.treeData)?.map((treeItem) => {
            return (
              <TreeItem
                key={treeItem?.key}
                itemKey={treeItem?.itemKey}
                title={treeItem?.title}
                items={treeItem?.children ?? []}
                keyword={keyword}
                metricsState={props?.metricsState}
                metricColumnsSettings$={props?.metricColumnsSettings$}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

interface TreeItemProps {
  itemKey: string;
  title: string;
  items: any[];
  keyword?: string;
  metricsState: any;
  metricColumnsSettings$: EventEmitter<any>;
}

interface TreeItemProcessItem {
  selected: (items, metricsState) => number;
  total: (items) => number;
  filterItems: (items, keyword) => any[];
}

const treeItemTypeProcessor: { [key: string]: TreeItemProcessItem } = {
  METRIC: {
    selected: (items, metricsState) => {
      let selected = metricsState?.['metric'];
      if (selected && selected?.length > 0) {
        let total = 0;
        let selectedKeys = selected?.map((item) => item?.id);
        items?.forEach((outerItem) => {
          outerItem?.items?.forEach((item) => {
            if (
              selectedKeys?.find((selectedKey) => selectedKey === item?.id) !==
              undefined
            ) {
              total += 1;
            }
          });
        });

        return total;
      } else {
        return 0;
      }
    },
    total: (items) => {
      let total = 0;
      items?.forEach((item) => {
        total += item?.items?.length ?? 0;
      });

      return total;
    },
    filterItems: (items, keyword) => {
      let treeItems = [];
      let originItems = Object.assign([], items);
      originItems?.forEach((metricItem) => {
        let filteredItems = metricItem?.items?.filter((item) =>
          keyword
            ? item?.title?.[searchFunctionGetter(false)](keyword) ||
              pinyinInitialSearch(item?.title, keyword)
            : true,
        );
        if (filteredItems?.length > 0) {
          metricItem['items'] = filteredItems;
          treeItems.push(metricItem);
        }
      });

      return treeItems;
    },
  },
  AGGREGATE: {
    selected: (items, metricsState) => {
      console.log('selected', items, metricsState);
      return 0;
    },
    total: (items) => items?.length,
    filterItems: (items, keyword) => {
      return items?.filter((item) =>
        keyword
          ? item?.title?.[searchFunctionGetter(false)](keyword) ||
            pinyinInitialSearch(item?.title, keyword)
          : true,
      );
    },
  },
  GROUP: {
    selected: (items, metricsState) => {
      let selected = metricsState?.['group'];
      if (selected && selected?.length > 0) {
        let total = 0;
        let selectedKeys = selected?.map((item) => item?.id);
        items?.forEach((item) => {
          if (
            selectedKeys?.find(
              (selectedKey) => selectedKey?.indexOf(item?.id) > -1,
            ) !== undefined
          ) {
            total += 1;
          }
        });

        return total;
      } else {
        return 0;
      }
    },
    total: (items) => items?.length,
    filterItems: (items, keyword) => {
      return items?.filter((item) =>
        keyword
          ? item?.title?.[searchFunctionGetter(false)](keyword) ||
            pinyinInitialSearch(item?.title, keyword)
          : true,
      );
    },
  },
};

export const TreeItem = (props: TreeItemProps) => {
  const filteredItems = treeItemTypeProcessor[props?.itemKey]?.filterItems(
    props?.items,
    props?.keyword,
  );

  return (
    <>
      {filteredItems?.length > 0 && (
        <div
          id={props?.title ?? props?.itemKey}
          className={'tree-item-container'}
        >
          <div className={'tree-header-container'}>
            <div className={'label-container'}>
              <span className={'title'}>{props?.title}</span>
              <span style={{ fontSize: 12, marginLeft: 3, marginBottom: 2 }}>
                <span className={'selected-number'}>
                  {treeItemTypeProcessor[props?.itemKey]?.selected(
                    props?.items,
                    props?.metricsState,
                  )}
                </span>
                /{treeItemTypeProcessor[props?.itemKey]?.total(filteredItems)}
              </span>
            </div>
          </div>

          <div className={'items-container'}>
            {props?.itemKey === 'GROUP' && (
              <>
                {filteredItems?.filter((item) => {
                  return item?.allowedGroupOptions?.length === 0;
                })?.length > 0 && (
                  <div className={'group-items-container'}>
                    {filteredItems
                      ?.filter((item) => {
                        return item?.allowedGroupOptions?.length === 0;
                      })
                      ?.map((tagItem) => {
                        return (
                          <>
                            <GroupItem
                              key={tagItem?.key}
                              type={'group'}
                              name={tagItem?.name}
                              title={tagItem?.title || tagItem?.name}
                              title2={tagItem?.title2}
                              itemId={tagItem?.id}
                              item={tagItem}
                              selectedItem={props?.metricsState?.['group']}
                              metricColumnsSettings$={
                                props?.metricColumnsSettings$
                              }
                            />
                          </>
                        );
                      })}
                  </div>
                )}
                {filteredItems?.filter((item) => {
                  return item?.allowedGroupOptions?.length > 0;
                })?.length > 0 && (
                  <div className={'group-periods-container'}>
                    {filteredItems
                      ?.filter((item) => {
                        return item?.allowedGroupOptions?.length > 0;
                      })
                      ?.map((tagItem) => {
                        return (
                          <>
                            <GroupItemPeriod
                              key={tagItem?.key}
                              type={'group'}
                              name={tagItem?.name}
                              title={tagItem?.title || tagItem?.name}
                              title2={tagItem?.title2}
                              itemId={tagItem?.id}
                              item={tagItem}
                              selectedItem={props?.metricsState?.['group']}
                              metricColumnsSettings$={
                                props?.metricColumnsSettings$
                              }
                            />
                          </>
                        );
                      })}
                  </div>
                )}
              </>
            )}
            {(props?.itemKey === 'METRIC' || props?.itemKey === 'AGGREGATE') &&
              filteredItems?.map((tagItem) => {
                return (
                  <>
                    {props?.itemKey === 'METRIC' && (
                      <MetricItem
                        key={tagItem?.key}
                        label={tagItem?.label}
                        items={tagItem?.items}
                        type={'metric'}
                        selectedItem={props?.metricsState?.['metric']}
                        metricColumnsSettings$={props?.metricColumnsSettings$}
                      />
                    )}
                    {props?.itemKey === 'AGGREGATE' && (
                      <AggregateItem
                        key={tagItem?.key}
                        type={'aggregate'}
                        name={tagItem?.name}
                        title={tagItem?.title || tagItem?.name}
                        title2={tagItem?.title2}
                        itemId={tagItem?.id}
                        item={tagItem}
                        selectedItem={props?.metricsState?.['aggregate']}
                        metricColumnsSettings$={props?.metricColumnsSettings$}
                      />
                    )}
                  </>
                );
              })}
          </div>
        </div>
      )}
    </>
  );
};

interface TagItemProps {
  [key: string]: any;

  type?: 'aggregate' | 'group' | 'metric';
  name?: string;
  customTitle?: string;
  title?: string;
  title2?: string;
  itemId?: string;
  item?: any;

  // 预设指标
  items?: any[];
  label?: string;

  selectedItem: SelectedMetricItem[];
  metricColumnsSettings$: EventEmitter<any>;
}

export const GroupItem = (props: TagItemProps) => {
  return (
    <TagItem
      tagItem={props}
      checked={
        !!props?.selectedItem?.find(
          (item) => item?.itemKey === `${props?.itemId}#Default`,
        )
      }
      onItemClicked={(tagItem, checked) => {
        props?.metricColumnsSettings$?.emit({
          type: ColumnSettingAction.ItemChangedBySelect,
          payload: {
            selectedItems: [
              {
                type: props?.type,
                checked: checked,
                id: `${props?.itemId}#Default`,
                tabKey: TabKeys.STATISTICS,
                item: {
                  ...props?.item,

                  customTitle: props?.item?.customTitle,

                  // tagTitle: `${props?.item.title}-${
                  //   GROUP_DATE?.find((item) => item.value === periodValue)?.name
                  // }`,
                  tagTitle: props?.item?.title,
                  itemType: props?.type,
                  itemKey: `${props?.itemId}#Default`,
                },
              },
            ],
          },
        });
        // Emitter.emit(
        //   StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
        //   [
        //     {
        //       type: props?.type,
        //       checked: checked,
        //       id: `${props?.itemId}#Default`,
        //       tabKey: TabKeys.STATISTICS,
        //       item: {
        //         ...props?.item,

        //         customTitle: props?.item?.customTitle,

        //         // tagTitle: `${props?.item.title}-${
        //         //   GROUP_DATE?.find((item) => item.value === periodValue)?.name
        //         // }`,
        //         tagTitle: props?.item?.title,
        //         itemType: props?.type,
        //         itemKey: `${props?.itemId}#Default`,
        //       },
        //     },
        //   ],
        // );
      }}
    />
  );
};

export const GroupItemPeriod = (props: TagItemProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const anaGroupOptions = globalState?.dictData?.['AnaGroupOption'];

  return (
    <div className={'group-period-item-container'}>
      <span className={'group-period-item-header'}>{props?.title}</span>
      <div className={'group-period-items-container'}>
        {props?.item?.allowedGroupOptions?.map((tagKey) => {
          let groupPeriodItem = anaGroupOptions?.find(
            (item) => item?.Code === tagKey,
          );
          let groupPeriodItemName = groupPeriodItem?.Name;
          let tagItem = {
            title: groupPeriodItemName,
          };
          return (
            <TagItem
              tagItem={tagItem}
              checked={
                !!props?.selectedItem?.find(
                  (item) => item?.itemKey === `${props?.itemId}#${tagKey}`,
                )
              }
              onItemClicked={(tagItem, checked) => {
                props?.metricColumnsSettings$?.emit({
                  type: ColumnSettingAction.ItemChangedBySelect,
                  payload: {
                    selectedItems: [
                      {
                        type: props?.type,
                        checked: checked,
                        id: `${props?.itemId}#${tagKey}`,
                        tabKey: TabKeys.STATISTICS,
                        radio: true,
                        item: {
                          ...props?.item,

                          title: `${props?.item?.title}-${groupPeriodItemName}`,
                          customTitle: props?.item?.customTitle,

                          // tagTitle: `${props?.item.title}-${
                          //   GROUP_DATE?.find((item) => item.value === periodValue)?.name
                          // }`,
                          tagTitle: props?.item?.title,
                          itemType: props?.type,
                          itemKey: `${props?.itemId}#${tagKey}`,
                        },
                      },
                    ],
                  },
                });
                // Emitter.emit(
                //   StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
                //   [
                //     {
                //       type: props?.type,
                //       checked: checked,
                //       id: `${props?.itemId}#${tagKey}`,
                //       tabKey: TabKeys.STATISTICS,
                //       radio: true,
                //       item: {
                //         ...props?.item,

                //         title: `${props?.item?.title}-${groupPeriodItemName}`,
                //         customTitle: props?.item?.customTitle,

                //         // tagTitle: `${props?.item.title}-${
                //         //   GROUP_DATE?.find((item) => item.value === periodValue)?.name
                //         // }`,
                //         tagTitle: props?.item?.title,
                //         itemType: props?.type,
                //         itemKey: `${props?.itemId}#${tagKey}`,
                //       },
                //     },
                //   ],
                // );
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export const MetricItem = (props: TagItemProps) => {
  return (
    <div className={'metric-item-container'}>
      <span className={'metric-item-header'}>{props?.label}</span>
      <div className={'metric-items-container'}>
        {props?.items?.map((tagItem) => {
          return (
            <TagItem
              tagItem={tagItem}
              checked={props?.selectedItem?.find(
                (item) => item?.itemKey === tagItem?.id,
              )}
              onItemClicked={(tagItem, checked) => {
                props?.metricColumnsSettings$?.emit({
                  type: ColumnSettingAction.ItemChangedBySelect,
                  payload: {
                    selectedItems: [
                      {
                        type: props?.type,
                        checked: checked,
                        id: tagItem?.id,
                        tabKey: TabKeys.BASIC,
                        item: {
                          ...tagItem,

                          tagTitle: tagItem?.title,
                          itemType: props?.type,
                          itemKey: tagItem?.id,
                        },
                      },
                    ],
                  },
                });
                // Emitter.emit(
                //   StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
                //   [
                //     {
                //       type: props?.type,
                //       checked: checked,
                //       id: tagItem?.id,
                //       tabKey: TabKeys.BASIC,
                //       item: {
                //         ...tagItem,

                //         tagTitle: tagItem?.title,
                //         itemType: props?.type,
                //         itemKey: tagItem?.id,
                //       },
                //     },
                //   ],
                // );
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export const AggregateItem = (props: TagItemProps) => {
  return (
    <div className={'aggregate-item-container'}>
      <span className={'aggregate-item-header'}>{props?.title}</span>
      <div className={'aggregate-items-container'}>
        {props?.item?.allowedAggregations?.map((tagKey) => {
          let aggregateItemName = AggregationNames[tagKey];
          let aggregateItem = props?.item?.aggregationNames[tagKey];
          let tagItem = {
            title: aggregateItemName,
          };
          return (
            <TagItem
              tagItem={tagItem}
              checked={
                !!props?.selectedItem?.find(
                  (item) => item?.itemKey === `${props?.itemId}#${tagKey}`,
                )
              }
              onItemClicked={(tagItem, checked) => {
                props?.metricColumnsSettings$?.emit({
                  type: ColumnSettingAction.ItemChangedBySelect,
                  payload: {
                    selectedItems: [
                      {
                        type: props?.type,
                        checked: checked,
                        id: `${props?.itemId}#${tagKey}`,
                        tabKey: TabKeys.BASIC,
                        item: {
                          ...props?.item,
                          ...aggregateItem,

                          customTitle: aggregateItem?.customTitle,

                          tagTitle: aggregateItem?.title,
                          itemType: props?.type,
                          itemKey: `${props?.itemId}#${tagKey}`,
                        },
                      },
                    ],
                  },
                });
                // Emitter.emit(
                //   StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
                //   [
                //     {
                //       type: props?.type,
                //       checked: checked,
                //       id: `${props?.itemId}#${tagKey}`,
                //       tabKey: TabKeys.BASIC,
                //       item: {
                //         ...props?.item,
                //         ...aggregateItem,

                //         customTitle: aggregateItem?.customTitle,

                //         tagTitle: aggregateItem?.title,
                //         itemType: props?.type,
                //         itemKey: `${props?.itemId}#${tagKey}`,
                //       },
                //     },
                //   ],
                // );
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export const TagItem = (props: any) => {
  return (
    <CheckableTag
      style={tagBaseStyle}
      className={props?.checked ? 'tag-item-checked' : 'tag-item'}
      key={props?.tagItem?.name}
      checked={props?.checked}
      onChange={(checked) => {
        props.onItemClicked(props?.tagItem, checked);
      }}
    >
      <div className={'flex-row-center'}>
        {props?.checked && <CheckOutlined className={'tick'} />}

        {props?.tagItem?.title}
      </div>
    </CheckableTag>
  );
};

export default MetricColumnTagsTree;
