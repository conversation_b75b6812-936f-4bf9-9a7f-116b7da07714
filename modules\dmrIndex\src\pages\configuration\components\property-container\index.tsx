import {
  ProForm,
  DrawerForm,
  ProFormDependency,
  ProFormSelect,
} from '@uni/components/src/pro-form';
import React, { useEffect, useState } from 'react';
import { Emitter } from '@uni/utils/src/emitter';
import {
  DmrConfigurationConstants,
  PropertyItemConstants,
} from '@/pages/configuration/constants';
import {
  basicProperties,
  dimensionProperties,
} from '@/pages/configuration/properties/basic';
import {
  DmrComponentMapping,
  propertiesPostProcessorMapping,
  propertyComponentMapping,
} from '@/pages/configuration/components/property-container/propertyComponentMapping';
import { PropertyItem } from '@/pages/configuration/interfaces';
import './index.less';
import {
  componentDataLayoutEdited,
  componentDataPostProcessor,
  componentDataPreProcessor,
} from '@/pages/configuration/components/property-container/processor';
import { Form, message } from 'antd';
import merge from 'lodash/merge';
import mergeWith from 'lodash/mergeWith';
import { omitBy } from 'lodash';
import cloneDeep from 'lodash/cloneDeep';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  valueDependenciesProperties,
  valueDependenciesSwitchProperties,
} from '@/pages/configuration/properties/dependency';

interface PropertyContainerProps {
  existTableComponentNames?: string[];
}

const initialValues = {
  w: 2, // 宽度
  h: 1, // 高度
  modelDataGroup: 'Dmr', // 字典库
  codeLength: 2, // 邮编组件默认长度
  contentAddZone: 'CONTENT',
};

export const tableLabelMapping = {
  IcdeDragTable: '诊断表格',
  OperationDragTable: '手术表格',
  IcuDragTable: 'ICU表格',
  PathologyIcdeDragTable: '病理诊断表格',
  TcmIcdeDragTable: '中医诊断表格',
};

const PropertyContainer = (props: PropertyContainerProps) => {
  const [form] = Form.useForm();

  const [propertyDrawerOpen, setPropertyDrawerOpen] = useState(false);

  const [propertyDrawerType, setPropertyDrawerType] = useState('ADD');

  const [propertyItemType, setPropertyItemType] = useState('CONTENT');

  const [previousItemId, setPreviousItemId] = useState<any>(undefined);

  const [componentsData, setComponentsData] = useState<any>(undefined);

  const [gridItemRef, setGridItemRef] = useState<any>(undefined);

  useEffect(() => {
    Emitter.on(
      DmrConfigurationConstants.DMR_CONFIGURATION_DRAWER_DATA,
      (componentsData) => {
        setComponentsData(componentsData);
        form?.setFieldsValue({
          ...initialValues,
          ...componentDataPreProcessor(componentsData),
          originData: componentsData,
        });

        console.log('componentsData', form.getFieldsValue(true));
      },
    );

    Emitter.on(
      DmrConfigurationConstants.DMR_CONFIGURATION_DRAWER_STATUS,
      (data) => {
        setPropertyDrawerOpen(data?.status);
        setPropertyDrawerType(data?.type);
        setPropertyItemType(data?.itemType);
        setGridItemRef(data?.gridItemRef);
        setPreviousItemId(data?.currentItemId);
        // default params
        if (!isEmptyValues(data?.defaultParams)) {
          Object.keys(data?.defaultParams)?.forEach((key) => {
            form.setFieldValue(key, data?.defaultParams?.[key]);
          });
        }
      },
    );

    return () => {
      Emitter.off(DmrConfigurationConstants.DMR_CONFIGURATION_DRAWER_STATUS);
      Emitter.off(DmrConfigurationConstants.DMR_CONFIGURATION_DRAWER_DATA);
    };
  }, []);

  const drawerFormProps = {
    resize: {
      onResize() {
        console.log('resize!');
      },
      maxWidth: window.innerWidth * 0.8,
      minWidth: 300,
    },
    getContainer: document.getElementById('property-container'),
  };

  return (
    <div id={'property-container'} className={'property-container'}>
      <DrawerForm
        {...drawerFormProps}
        title={`${
          propertyDrawerType === 'ADD'
            ? '新建'
            : `修改 ${
                tableLabelMapping?.[form.getFieldValue('data.component')] ??
                form.getFieldValue('data.props.prefix') ??
                form.getFieldValue('data.prefix')
              }
           `
        }配置项`}
        form={form}
        initialValues={initialValues}
        autoFocusFirstInput
        drawerProps={{
          destroyOnClose: true,
          onClose: () => {
            setPropertyDrawerOpen(false);
          },
        }}
        open={propertyDrawerOpen}
        width={530}
        submitTimeout={2000}
        onValuesChange={(changeValues: any, values: any) => {
          console.log('changeValues', changeValues, values);
          Object.keys(changeValues)?.forEach((key) => {
            if (key === 'y' || key === 'x') {
              form.setFieldValue('positionEdited', true);
            }

            if (key === 'h' || key === 'w') {
              form.setFieldValue('sizeEdited', true);
            }

            if (propertiesPostProcessorMapping?.[key]) {
              propertiesPostProcessorMapping?.[key](changeValues?.[key], form);
            }
          });
        }}
        onFinish={async (values) => {
          // 先行验证table是否存在多个

          let propertyValidationResult = await form.validateFields();

          // let layoutEdit = componentDataLayoutEdited(values, componentsData);

          let componentEditedData = componentDataPostProcessor(values);

          let latestComponentData = merge(
            {},
            omitBy(cloneDeep(componentsData), (value, key) => {
              return key === 'data';
            }),
            componentEditedData,
          );

          console.log('latestComponentData', latestComponentData, componentsData, componentEditedData);

          message.success(
            `${propertyDrawerType === 'ADD' ? '新建' : '修改'}成功`,
          );

          if (propertyDrawerType === 'EDIT') {
            Emitter.emit(
              `${DmrConfigurationConstants.DMR_CONFIGURATION_ITEM_DATA_CHANGE}${componentsData?.gridId}`,
              {
                latestComponentData: latestComponentData,
                positionEdited: form.getFieldValue('positionEdited') === true,
                gridItemRef: gridItemRef,
                itemGroup: propertyItemType,
              },
            );

            // if(layoutEdit) {
            //   Emitter.emit(DmrConfigurationConstants.DMR_CONFIGURATION_ITEM_LAYOUT_CHANGE, latestComponentData);
            // }
          } else {
            latestComponentData['i'] = latestComponentData?.data?.key;
            Emitter.emit(
              DmrConfigurationConstants.DMR_CONFIGURATION_ITEM_LAYOUT_ADD,
              {
                ...latestComponentData,
                contentAddZone: values?.contentAddZone,
                previousItemId: previousItemId,
                itemGroup: propertyItemType,
              },
            );
          }

          form.resetFields();

          // 不返回不会关闭弹框
          setPropertyDrawerOpen(false);
          return false;
        }}
      >
        <div className={'property-container'}>
          <PropertyFormGroup
            form={form}
            properties={
              basicProperties(
                propertyDrawerType,
                propertyDrawerType === 'ADD'
                  ? props?.existTableComponentNames
                  : props?.existTableComponentNames?.filter(
                      (item) => item !== form.getFieldValue('data.component'),
                    ),
              ) as any
            }
            title={'组件基本信息'}
            propertyDrawerType={propertyDrawerType}
          />
          {propertyDrawerType === 'ADD' && (
            <ProFormSelect
              options={[
                {
                  value: 'CONTENT',
                  label: '内容区',
                },
                {
                  value: 'HEADER',
                  label: '标题区',
                },
              ]}
              width="md"
              name="contentAddZone"
              tooltip="新增组件到某一区域，标题区为表格上方"
              label={`组件新增区域`}
            />
          )}
          <PropertyFormGroup
            form={form}
            properties={dimensionProperties(propertyDrawerType)}
            title={'组件大小位置'}
            propertyDrawerType={propertyDrawerType}
          />
          <PropertyFormGroup
            form={form}
            properties={valueDependenciesSwitchProperties}
            title={'依赖项配置'}
            propertyDrawerType={propertyDrawerType}
          />
          <ProFormDependency name={['data.props.hasValueDependency']}>
            {(record) => {
              if (record?.['data.props.hasValueDependency'] === true) {
                return (
                  <PropertyFormGroup
                    form={form}
                    properties={valueDependenciesProperties}
                    title={`依赖项key-value配置`}
                    propertyDrawerType={propertyDrawerType}
                  />
                );
              }
              return <></>;
            }}
          </ProFormDependency>
          {
            <ProFormDependency name={['data.component']}>
              {(record) => {
                let mappingItem = DmrComponentMapping?.find(
                  (item) => item?.component === record?.['data.component'],
                );
                if (mappingItem && mappingItem?.properties) {
                  return (
                    <PropertyFormGroup
                      form={form}
                      properties={mappingItem?.properties}
                      title={`${mappingItem?.label} 配置`}
                      propertyDrawerType={propertyDrawerType}
                    />
                  );
                }
                return <></>;
              }}
            </ProFormDependency>
          }
        </div>
      </DrawerForm>
    </div>
  );
};

interface PropertyFormGroupProps {
  form: any;
  title: string;
  properties?: PropertyItem[][];
  propertyDrawerType: string;
}

const PropertyFormGroup = (props: PropertyFormGroupProps) => {
  return (
    <>
      <span className={'title'}>{props?.title}</span>
      {props?.properties?.map((properties) => {
        return (
          <ProForm.Group>
            {properties
              ?.filter((propertyItem) => {
                return (
                  propertyComponentMapping[propertyItem?.component] !==
                  undefined
                );
              })
              ?.map((propertyItem) => {
                const PropertyComponent = propertyComponentMapping[
                  propertyItem?.component
                ] as React.FC<any>;
                if (propertyItem?.label === '表单中字段名称') {
                  console.log(
                    'propertyItem',
                    propertyItem,
                    props?.propertyDrawerType,
                    {
                      ...propertyItem?.fieldProps,
                      disabled:
                        propertyItem?.key === 'data.props.formKey'
                          ? props?.propertyDrawerType === 'ADD'
                            ? false
                            : true
                          : propertyItem?.key === 'data.key'
                          ? true
                          : false,
                    },
                  );
                }
                return (
                  <>
                    {propertyItem?.dependencyKey ? (
                      <ProFormDependency name={[propertyItem?.dependencyKey]}>
                        {(record) => {
                          if (
                            record[propertyItem?.dependencyKey]?.toString() ===
                            propertyItem?.dependencyValue
                          ) {
                            return (
                              <PropertyComponent
                                {...propertyItem}
                                form={props?.form}
                                name={propertyItem?.key}
                                rules={[
                                  {
                                    required: propertyItem?.required,
                                    message: `${propertyItem?.label}不能为空`,
                                  },
                                ]}
                                width={propertyItem?.width ?? 'md'}
                                label={propertyItem.label}
                                tooltip={propertyItem?.description}
                                placeholder="请输入"
                                fieldProps={propertyItem?.fieldProps}
                              />
                            );
                          }
                          return <></>;
                        }}
                      </ProFormDependency>
                    ) : (
                      <PropertyComponent
                        {...propertyItem}
                        form={props?.form}
                        name={propertyItem?.key}
                        rules={[
                          {
                            required: propertyItem?.required,
                            message: `${propertyItem?.label}不能为空`,
                          },
                          ...(propertyItem?.rules ?? []),
                        ]}
                        width={propertyItem?.width ?? 'md'}
                        label={propertyItem.label}
                        tooltip={propertyItem?.description}
                        placeholder="请输入"
                        fieldProps={{
                          ...propertyItem?.fieldProps,
                          disabled:
                            propertyItem?.key === 'data.props.formKey'
                              ? props?.propertyDrawerType === 'ADD'
                                ? false
                                : true
                              : propertyItem?.key === 'data.key'
                              ? true
                              : false,
                        }}
                      />
                    )}
                  </>
                );
              })}
          </ProForm.Group>
        );
      })}
    </>
  );
};

export default PropertyContainer;
