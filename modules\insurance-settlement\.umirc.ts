import { name } from './package.json';
import {
  slaveCommonConfig,
  extraBabelIncludes,
  extraWebPackPlugin,
  title,
} from '../../.umirc.commom';

export default {
  // title: title,
  base: name,
  publicPath: '/chs/',
  outputPath: '../../dist/chs',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  qiankun: {
    master: {
      // 注册子应用信息
      apps: [
        {
          name: 'report',
          entry:
            process.env.NODE_ENV === 'production'
              ? '/report/'
              : '//localhost:8006',
        },
      ],
    },
    slave: {},
  },

  plugins: [
    require.resolve('@uni/commons/src/plugins/qiankun.ts'),
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/main/index',
    },
    {
      path: '/main/index',
      exact: true,
      component: '@/pages/chs/index',
    },
    {
      path: '/main/index/fullscreen',
      exact: true,
      component: '@/pages/chs/index',
    },

    {
      path: '/main/management',
      exact: true,
      component: '@/pages/management/index',
    },
    {
      path: '/main/monitor',
      exact: true,
      component: '@/pages/monitor/index',
    },
    {
      path: '/main/settleInfo',
      exact: true,
      component: '@/pages/settleInfo/index',
    },
    {
      path: '/main/match',
      exact: true,
      component: '@/pages/match',
    },
    {
      path: '/main/qcAnalysis/hosp',
      exact: true,
      component: '@/pages/quality-check/hosp',
    },
    {
      path: '/main/qcAnalysis/dept',
      exact: true,
      component: '@/pages/quality-check/dept',
    },
    {
      path: '/main/details/dept',
      exact: true,
      component: '@/pages/details/dept',
    },
    {
      path: '/main/encode/diff',
      exact: true,
      component: '@/pages/encodeAnalysis/diff',
    },
    {
      path: '/main/encode/details',
      exact: true,
      component: '@/pages/encodeAnalysis/details',
    },
    // 特病单议
    {
      path: '/main/specialDisease/discussion',
      exact: true,
      component: '@/pages/specialDiseaseDiscussion',
    },
    // 结算清单数据(通过结算清单数据新增特病单议病例)
    // {
    //   path: '/main/insurDetails',
    //   exact: true,
    //   component: '@/pages/specialDiseaseDiscussion/pages/insurDetails',
    // },
    // 特病单议 统计
    {
      path: '/main/specialDisease/reportStats',
      exact: true,
      component: '@/pages/specialDiseaseDiscussion/pages/amountStatistics',
    },
    {
      path: '/main/specialDisease/importRecord',
      exact: true,
      component: '@/pages/specialDiseaseDiscussion/pages/importRecord',
    },
    //
    {
      path: '/dip/analysis/hosp',
      exact: true,
      component: '@/pages/dip/hosp',
    },
    {
      path: '/dip/analysis/majorPerfDept',
      exact: true,
      component: '@/pages/dip/majorPerfDept',
    },
    {
      path: '/dip/analysis/dept',
      exact: true,
      component: '@/pages/dip/dept',
    },
    {
      path: '/dip/analysis/group',
      exact: true,
      component: '@/pages/dip/group',
    },
    {
      path: '/dip/analysis/medTeam',
      exact: true,
      component: '@/pages/dip/medTeam',
    },
    {
      path: '/dip/analysis/pay',
      exact: true,
      component: '@/pages/dip/pay',
    },
    // dip导出明细
    {
      path: '/dip/exportDetail',
      exact: true,
      component: '@/pages/dip/exportDetail',
    },
    // dip 在院
    {
      path: '/dip/warning/hosp',
      exact: true,
      component: '@/pages/warning-monitor/in/hosp',
    },
    {
      path: '/dip/warning/dept',
      exact: true,
      component: '@/pages/warning-monitor/in/dept',
    },
    // dip 的 在院患者明细
    {
      path: '/dip/warning/patientInfo',
      exact: true,
      component: '@/pages/warning-monitor/patientInfo',
    },
    // 事后监控
    {
      path: '/dip/important/hosp',
      exact: true,
      component: '@/pages/warning-monitor/after/hosp',
    },
    {
      path: '/dip/important/majorPerfDept',
      exact: true,
      component: '@/pages/warning-monitor/after/majorPerfDept',
    },
    {
      path: '/dip/important/dept',
      exact: true,
      component: '@/pages/warning-monitor/after/dept',
    },
    {
      path: '/dip/important/medTeam',
      exact: true,
      component: '@/pages/warning-monitor/after/medTeam',
    },

    //预支付分析
    {
      path: '/analysis/pre/index',
      exact: true,
      component: '@/pages/analysis/index',
    },

    {
      path: '/analysis/pre/hosp',
      exact: true,
      component: '@/pages/analysis/hosp/index',
    },
    {
      path: '/analysis/pre/dept',
      exact: true,
      component: '@/pages/analysis/dept/index',
    },
    {
      path: '/analysis/pre/disease',
      exact: true,
      component: '@/pages/analysis/disease/index',
    },
    // {
    //   path: '/analysis/pre/fee',
    //   exact: true,
    //   component: '@/pages/analysis/fee/index',
    // },
    // drg支付监控分析
    {
      path: '/analysis/drg/hosp',
      exact: true,
      component: '@/pages/drg/hosp',
    },
    {
      path: '/analysis/drg/majorPerfDept',
      exact: true,
      component: '@/pages/drg/majorPerfDept',
    },
    {
      path: '/analysis/drg/dept',
      exact: true,
      component: '@/pages/drg/dept',
    },
    {
      path: '/analysis/drg/medTeam',
      exact: true,
      component: '@/pages/drg/medTeam',
    },
    {
      path: '/analysis/drg/group',
      exact: true,
      component: '@/pages/drg/group',
    },
    {
      path: '/analysis/drg/pay',
      exact: true,
      component: '@/pages/drg/pay',
    },
    // 支付明细查询
    {
      path: '/analysis/payDetail',
      exact: true,
      component: '@/pages/drg/payDetail',
    },
    // 监控
    // 在院监控
    {
      path: '/analysis/warning/hosp',
      exact: true,
      component: '@/pages/warning-monitor/in/hosp',
    },
    {
      path: '/analysis/warning/dept',
      exact: true,
      component: '@/pages/warning-monitor/in/dept',
    },
    // drg 的 在院患者明细
    {
      path: '/analysis/warning/patientInfo',
      exact: true,
      component: '@/pages/warning-monitor/patientInfo',
    },
    // 重点病例监控
    {
      path: '/analysis/important/hosp',
      exact: true,
      component: '@/pages/warning-monitor/after/hosp',
    },
    {
      path: '/analysis/important/majorPerfDept',
      exact: true,
      component: '@/pages/warning-monitor/after/majorPerfDept',
    },
    {
      path: '/analysis/important/dept',
      exact: true,
      component: '@/pages/warning-monitor/after/dept',
    },
    {
      path: '/analysis/important/medTeam',
      exact: true,
      component: '@/pages/warning-monitor/after/medTeam',
    },
    // 重点病例监控 / 高差异度病种监控
    {
      path: '/analysis/important/variableConditions',
      exact: true,
      component: '@/pages/variableConditions',
    },
    {
      path: '/dip/important/variableConditions',
      exact: true,
      component: '@/pages/dip/variableConditions',
    },
    // 医保病案详情
    {
      path: '/analysis/cardInfo',
      exact: true,
      component: '@/pages/warning-monitor/components/cardInfo',
    },
    // 支付审核规则配置 drg / dip
    {
      path: '/analysis/payRuleSettings',
      exact: true,
      component: '@/pages/settleCheck/payRuleSettings',
    },
    {
      path: '/dip/payRuleSettings',
      exact: true,
      component: '@/pages/settleCheck/payRuleSettings',
    },

    // report
    {
      path: '/analysis/report',
      exact: true,
      component: '@/pages/report/normalReport',
    },
    {
      path: '/analysis/highlight/:id',
      exact: true,
      component: '@/pages/report/highlightReport',
    },
    {
      path: '/dip/report',
      exact: true,
      component: '@/pages/report/normalReport',
    },
    {
      path: '/dip/highlight/:id',
      exact: true,
      component: '@/pages/report/highlightReport',
    },
    {
      path: '/main/report',
      exact: true,
      component: '@/pages/report/normalReport',
    },
    {
      path: '/main/highlight/:id',
      exact: true,
      component: '@/pages/report/highlightReport',
    },
  ],

  proxy: {
    '/insurAddress': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/insurAddress': '' },
      secure: false, // https的dev后端的话需要配
    },
    '/common': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/common': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
};
