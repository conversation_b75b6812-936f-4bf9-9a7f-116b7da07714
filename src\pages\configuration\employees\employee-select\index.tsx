import { useModel } from '@@/plugin-model/useModel';
import React, { useEffect, useState } from 'react';
import { DictionaryItem, RespVO } from '@uni/commons/src/interfaces';
import { metaDataService } from '@uni/services/src';
import { UniSelect } from '@uni/components/src';
import * as module from 'module';

export interface EmployeeFormKeysItem {
  [key: string]: string;
}

interface EmployeeSelectProps {
  moduleKey: string;

  onEmployeeSelect?: (data) => void;

  parentId?: string;

  value?: any;
  onChange?: (value: any) => void;
  mode?: string;
}

export const EmployeeSelect = (props: EmployeeSelectProps) => {
  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateForSlave',
  );

  const [dataSource, setDataSource] = useState([]);

  const dictData = globalState?.dictData;

  useEffect(() => {
    if (dictData?.[props?.moduleKey]) {
      setDataSource(dictData[props?.moduleKey]);
    } else {
      getEmployeeModules();
    }
  }, [dictData]);

  const getEmployeeModules = async () => {
    let response: RespVO<DictionaryItem[]> = await metaDataService(
      props?.moduleKey,
      '',
    );

    if (response?.code === 0) {
      if (response?.statusCode === 200) {
        setDataSource(response?.data);

        let dictData = globalState?.dictData;
        dictData[props?.moduleKey] = response?.data;
        setQiankunGlobalState({
          ...globalState,
          dictData: dictData,
        });

        return;
      }
    }

    setDataSource([]);
  };

  const onSelectChange = (value?: string) => {
    props?.onEmployeeSelect && props?.onEmployeeSelect(value);
  };

  return (
    <UniSelect
      dataSource={dataSource}
      placeholder="请选择"
      showSearch
      optionNameKey={'Name'}
      optionValueKey={'Code'}
      dropdownMatchSelectWidth={false}
      value={props?.value}
      allowClear={false}
      mode={props?.mode as any}
      getPopupContainer={(trigger) =>
        (props?.parentId && document.getElementById(props?.parentId)) ||
        document.body
      }
      onChange={(value) => {
        onSelectChange(value);
      }}
      onClear={() => {
        onSelectChange(null);
      }}
    />
  );
};
