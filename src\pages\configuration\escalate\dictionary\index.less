.escalate-dictionary-configuration-container {
  display: flex;
  flex-direction: row;
  background: #ffffff;
  height: 100%;
  // padding: 10px 20px;

  .escalate-dictionary-tree-search-container {
    margin-right: 10px;
  }

  .configuration-tree-container {
    width: 240px;
    height: calc(100% - 35px);
    overflow-y: auto;
    padding: 10px 0px 0px 10px;
  }

  .configuration-table-container {
    width: calc(100% - 200px);
  }

  .ant-popover-arrow {
    display: none;
  }

  .ant-tree-node-content-wrapper .ant-dropdown-trigger {
    display: block;
    max-width: 130px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .ant-popover-buttons {
    display: flex;
    flex-direction: row;
  }

  .dictionary-table-container {
    .ant-table-body {
      overflow-x: clip;
    }

    .dictionary-item-operation {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 0px 5px;

      .operation-item:not(:first-child) {
        margin-left: 10px;
      }
    }

    .dictionary-item-add-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      cursor: pointer;
      padding: 8px 8px;

      span {
        margin-right: 10px;
      }
    }
  }
}
