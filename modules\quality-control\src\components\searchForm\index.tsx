import {
  ProForm,
  ProFormDatePicker,
  ProFormDateRangePicker,
  ProFormDateTimePicker,
  ProFormInstance,
  ProFormProps,
  ProFormSelect,
  ProFormText,
  ProFormCheckbox,
  ModalForm,
} from '@uni/components/src/pro-form';
import { useRef } from 'react';
import _ from 'lodash';
import { AutoComplete, Button, Form } from 'antd';
import './index.less';
import { useDebounceFn } from 'ahooks';
import clsx from 'clsx';
import 'dayjs/locale/zh-cn';

export type SearchFormOptType = {
  title?: string;
  dataType: string;
  hidden?: boolean;
  name: string;
  opts?: any[];
  render?: any;
  initialValue?: any;
};

export type SearchFormProps = ProFormProps & {
  searchOpts: SearchFormOptType[];
  classNames?: string[];
  fetchCallback?: Function;
  isModalForm?: boolean;
};

const SearchForm = ({
  searchOpts = [],
  fetchCallback,
  isModalForm,
  className,
  ...restProps
}: SearchFormProps) => {
  const proFormRef = useRef<ProFormInstance>();

  const { run } = useDebounceFn(
    (cValues, aValues) => {
      fetchCallback && fetchCallback(aValues);
    },
    { wait: 500 },
  );

  const renderFormItem = (item) => {
    let props = {
      key: item.name,
      name: item.name,
      label: item.title,
      ...item,
    };
    switch (item.dataType) {
      case 'checkbox':
        // 2.3.58的checkbox有bug，回传的数据是event，要单独处理，建议在transform处理,或者直接使用checkbox.group
        return <ProFormCheckbox {...props} />;
      case 'checkboxGroup':
        return <ProFormCheckbox.Group {...props} />;
      case 'dateRange':
        return <ProFormDateRangePicker {...props} />;
      case 'date':
        return <ProFormDatePicker {...props} />;
      case 'dateTime':
        return <ProFormDateTimePicker {...props} />;
      case 'select':
        return (
          <ProFormSelect
            {...props}
            showSearch
            options={_.cloneDeep(item?.opts ?? [])}
            fieldProps={{
              fieldNames: { label: 'Name', value: 'Code' },
              ...(props?.fieldProps ?? {}),
            }}
          />
        );
      case 'autoComplete':
        return (
          <Form.Item {...props}>
            <AutoComplete
              options={_.cloneDeep(
                item?.opts?.map((d) => ({
                  label: d.Name,
                  value: d.Code,
                })) ?? [],
              )}
            />
          </Form.Item>
        );
      case 'text':
      default:
        return <ProFormText {...props} />;
    }
  };
  return (
    <>
      {!isModalForm && (
        <ProForm
          layout="horizontal"
          className={clsx('search_proform', className)}
          formRef={proFormRef}
          submitter={false}
          grid
          rowProps={{
            gutter: [16, 16],
          }}
          // labelCol={{ span: 8 }}
          // wrapperCol={{ span: 16 }}
          onValuesChange={run}
          {...restProps}
        >
          {searchOpts
            ?.filter((d) => !d.hidden)
            .map((d) =>
              typeof d.render === 'function'
                ? d.render(proFormRef)
                : d.render ?? renderFormItem(d),
            )}
        </ProForm>
      )}
      {isModalForm &&
        searchOpts
          ?.filter((d) => !d.hidden)
          .map((d) =>
            typeof d.render === 'function'
              ? d.render(proFormRef)
              : d.render ?? renderFormItem(d),
          )}
    </>
  );
};

export default SearchForm;
