import { Col, Row, Tabs } from 'antd';
import { useState } from 'react';
import { useModel } from 'umi';
import _ from 'lodash';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '../../components/drawerCardInfo';
import { TotalStatsColumns, TabCommonItems } from '../constants';
import Stats from '../../components/statsWithTrend';
import SingleColumnTable from '../../components/singleColumnTable/index';
import { useDeepCompareEffect } from 'ahooks';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { GroupByDoctorList } from '../data';

const HqmsQuality = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, MedTeams } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);

  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
      MedTeams,
    };
    setTableParams(params);
  }, [dateRange, hospCodes, MedTeams]);

  // tabs
  let tabItems = [
    {
      key: 'statistic',
      label: '医疗组综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={24} xl={24}>
            <Stats
              level="medTeam"
              // api={`Api/Hqms/MedTeamHqmsQuality/BundledHqmsQuality`}
              // trendApi={`Api/Hqms/MedTeamHqmsQuality/HqmsQualityTrend`}
              api={`API/v2/Hqms/HqmsStats/HqmsQualityOfMedTeam`}
              trendApi={`API/v2/Hqms/HqmsStats/HqmsQualityTrend`}
              columns={TotalStatsColumns}
              type="col-xl-6"
              tabKey={activeKey}
              chartHeight={'auto'}
              useGlobalState
              canEditColumn
            />
          </Col>
          <SingleColumnTable
            title="医疗组医疗质量"
            args={{
              // api: 'Api/Hqms/MedTeamHqmsQuality/HqmsQualityByMedTeam',
              api: 'API/v2/Hqms/HqmsStats/HqmsQualityByMedTeam',
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="MedTeamName"
            type="table"
            visibleValueKeys={[
              'MedTeamName',
              'DeathCnt',
              'TotalCnt',
              'DeathRatio',
            ]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName + '死亡人次',
                args: {
                  ...tableParams,
                  MedTeams: record?.MedTeam ? [record?.MedTeam] : [],
                  Dead: true,
                },
                detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: TabCommonItems.doctorAnalysis.key,
      label: TabCommonItems.doctorAnalysis.title,
      children: (
        <>
          <Row>
            <SingleColumnTable
              title="医生医疗质量"
              args={{
                // api: 'Api/Hqms/DoctorHqmsQuality/HqmsQualityByDoctor',
                api: 'API/v2/Hqms/HqmsStats/HqmsQualityByDoctor',
              }}
              tableParams={tableParams}
              dictData={dictData}
              category="DoctorName"
              type="table"
              orderKey="DeathCnt"
              visibleValueKeys={[
                'DoctorName',
                'TotalCnt',
                'DeathCnt',
                'DeathRatio',
                'SelectiveSurgeryForComplicationPatCnt',
                'OperComplicationPatCnt',
                'OperComplicationPatRatio',
                'Class1WoundPatCnt',
                'Class1WoundInfectionPatCnt',
                'Class1WoundInfectionPatRatio',
                'UnexpectedBackInCnt',
              ]}
              colSpan={{ span: 24 }}
              select={{
                dataKey: 'DoctorType',
                valueKey: 'GroupByDoctor',
                allowClear: false,
                defaultSelect: true,
                options: GroupByDoctorList,
              }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.DoctorName + '死亡人次',
                  args: {
                    ...tableParams,
                    DoctorCodes: record?.DoctorCode ? [record?.DoctorCode] : [],
                    DoctorType: (
                      record?.DoctorType as string
                    )?.toLocaleUpperCase(),
                    Dead: true,
                  },
                  detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
                  dictData: dictData,
                });
              }}
            />
          </Row>
        </>
      ),
    },
  ];
  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      {/* 把modal与drawer的位置提高到这里，统一在这里处理 避免id太多导致匹配混乱 & tab切换后id失效 */}
      {/* TODO DetailTableModal内部的实现方式是destoryOnClose 导致每次打开都要重新获取columns */}
      <DetailTableModal
        dictData={globalState.dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      {/* drawer 同理 这样保持复用 因为drawer 监听的是detailTableModal 或者一些table */}
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default HqmsQuality;
