@import '~@uni/commons/src/style/variables.less';

.dmr-comment-drawer-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 10px 4px 10px 10px;
  border-left: 1px solid @border-color;

  .dmr-comment-drawer-title {
    display: flex;
    flex-direction: column;
    font-size: 16px;
    padding: 0px 10px 10px 10px;
    font-weight: bold;
  }

  .comment-drawer-base-container {
    display: flex;
    flex-direction: column;
  }

  .ant-drawer-body {
    padding: 10px;
  }

  .pre-comment-relative-container {
    width: 100%;
    display: flex;
    flex-direction: column;

    position: relative;

    min-height: var(--gridMinHeight, 750) !important;
    height: var(--gridMinHeight, 750) !important;
  }

  .comment-item-list-container {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100%;
    padding-right: 6px;
  }

  .dmr-comment-item-container {
    display: flex;
    flex-direction: column;
    border: 1px solid @border-color;
    border-radius: 4px;
    padding: 10px;

    .delete-icon {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;
      //cursor: pointer;
    }

    .comment-item {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;
    }

    .comment-item-label {
      font-size: 14px;
      width: 80px;
      min-width: 80px;
      white-space: nowrap;
    }

    .comment-item-content {
      display: flex;
      flex-direction: row;
      width: 100%;
    }
  }
}
