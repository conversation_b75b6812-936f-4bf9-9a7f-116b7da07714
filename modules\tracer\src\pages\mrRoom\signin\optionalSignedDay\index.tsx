import { Reducer, useEffect, useMemo, useReducer, useRef } from 'react';
import { Dispatch, useDispatch, useSelector } from 'umi';
import { UniTable } from '@uni/components/src';
import {
  <PERSON>ert,
  Button,
  Card,
  Col,
  Divider,
  InputNumber,
  InputRef,
  Modal,
  Popconfirm,
  Row,
  Space,
  Tooltip,
  message,
} from 'antd';
import { useSafeState, useKeyPress } from 'ahooks';
import {
  IModalState,
  IReducer,
  ITableState,
} from '@uni/reducers/src/Interface';
import { ActionRecordItem, SwagTraceRecordItem } from '../../interface';
import { columnsHandler, isRespErr, handleMrActionApi } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType, SigninType } from '@/Constants';
import {
  ModalAction,
  TableAction,
  modalReducer,
  tableReducer,
  InitTableState,
  InitModalState,
} from '@uni/reducers/src';
import dayjs from 'dayjs';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormGroup,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@uni/components/src/pro-form';
import './index.less';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { useTimelineReq } from '@/hooks';
import PatTimeline from '@/components/PatTimeline';
import {
  CheckCircleOutlined,
  FileExcelOutlined,
  UndoOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { exportExcel } from '@uni/utils/src/excel-export';
import { exportExcelDictionaryModuleProcessor } from '@uni/components/src/table/processor/data/export';
import { modalSelectedColumns } from '@/pages/archive/columns';
import clsx from 'clsx';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import './index.less';
import { useModel } from '@@/plugin-model/useModel';

const Columns = [
  {
    data: 'isCorrect',
    dataIndex: 'isCorrect',
    visible: true,
    render: (text: string, record: any) => {
      return (
        <>
          {record?.errMsg ? (
            <Tooltip title={record?.errMsg?.join('。/n')}>
              <WarningOutlined />
            </Tooltip>
          ) : (
            <CheckCircleOutlined />
          )}
        </>
      );
    },
    order: 1,
  },
  {
    data: 'xuhao',
    dataIndex: 'xuhao',
    title: '序号',
    visible: true,
    align: 'center',
    render: (text, record, index) => {
      return index + 1;
    },
    order: 2,
  },
  {
    dataIndex: 'BarCode',
    title: '条码号',
    visible: true,
    className: 'exportable',
  },
];

const OptionalDayMrRoomSignIn = () => {
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  const proFormRef = useRef<ProFormInstance>();

  const { globalState } = useModel('@@qiankunStateFromMaster');

  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();
  const [revertRecord, setRevertRecord] = useSafeState(null);

  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagTraceRecordItem & ActionRecordItem>, IReducer>
  >(tableReducer, InitTableState);

  // modal state
  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<SwagTraceRecordItem[]>, IReducer>
  >(modalReducer, {
    ...InitModalState,
    specialData: undefined,
  });
  // modal selected table key
  const [selectedRecordKey, setSelectedRecordKey] = useSafeState([]);
  // modal columns key
  const [modalColumns, setModalColumns] = useSafeState([]);
  // modal alert
  const [modalAlert, setModalAlert] = useSafeState(false);

  // 节流隐式标识
  const hiddenLoading = useRef(false);
  // 还是使用ref来替换document.getId
  const barCodeRef = useRef<InputRef>(null);

  // 实际签收数
  const [actualCnt, setActualCnt] = useSafeState(0);
  // 实际签收数编辑状态
  const actualCntInputRef = useRef();

  useKeyPress(
    'enter',
    () => {
      console.log(hiddenLoading.current);
      if (hiddenLoading.current) return;
      hiddenLoading.current = true;
      proFormRef.current
        .validateFields()
        .then((values) => {
          if (values?.SignType?.value === 'BarCode') {
            searchByBarCodeReq(values, false);
          } else {
            // 对于非BarCode类型，创建请求参数对象
            const searchParam = {
              ..._.omit(values, 'BarCode'),
              [values.SignType.value]: values.BarCode,
              SelectiveSignInDate: values.SelectiveSignInDate,
            };
            searchOneReq(searchParam, false);
          }
        })
        .catch((err) => {
          hiddenLoading.current = false;
        });
    },
    {
      exactMatch: true,
      target: document.getElementById('optionalDaySignInForm'),
    },
  );

  // 查询结果统一处理（处理方式一致）
  const searchResultHandler = (
    params: any,
    res: any,
    needDataPush: boolean,
  ) => {
    // 重置节流标识
    hiddenLoading.current = false;
    if (!isRespErr(res)) {
      let resData;
      if (res?.data?.Items) {
        // Api/Mr/TraceRecord/GetList
        resData = res?.data?.Items?.slice();
      } else {
        // Api/Mr/TraceRecord/GetListByBarCode
        resData = res?.data?.slice();
      }
      if (!needDataPush) {
        if (resData?.length === 1) {
          // 单个，直接处理
          reqActionReq(
            {
              ...params,
              BarCode: resData?.at(0)?.BarCode,
            },
            null,
            ReqActionType.selectiveMrRoomSignIn,
          );
        } else if (resData?.length > 1) {
          // 多条，modal提示处理
          ModalStateDispatch({
            type: ModalAction.change,
            payload: {
              visible: true,
              record: resData,
              specialData: res?.data?.Items ? params : params?.BarCode,
              actionType: undefined,
            },
          });
        } else {
          // 没查到数据
          // 重置节流标识
          hiddenLoading.current = false;
          Modal.confirm({
            title: `查无数据`,
            content: '请确认病案标识填写正确',
            onOk: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            onCancel: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            cancelButtonProps: { style: { display: 'none' } },
          });
        }
      } else {
        // 重置节流标识
        hiddenLoading.current = false;
      }
    }
  };

  // 批量查询，先查，再签收
  const searchOneReq = async (params: any, needDataPush = false) => {
    if (!params) return;

    // 构建请求数据
    let requestData = params;

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetList`,
          method: 'POST',
          data: requestData,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    searchResultHandler(params, res, needDataPush);
  };

  // 扫码枪条码，走这里
  const searchByBarCodeReq = async (params: any, needDataPush = false) => {
    if (!params) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetListByBarCode`,
          method: 'POST',
          data: {
            BarCode: params?.BarCode,
            SkipCount: 0,
            MaxResultCount: 999999,
          },
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    searchResultHandler(params, res, needDataPush);
  };

  // 签收 / 撤销
  const reqActionReq = async (
    params: any,
    item: any,
    reqType: ReqActionType,
  ) => {
    if (!params || !reqType) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `Tracing/${reqType}`,
        requestParams: {
          url: `Api/Mr/Tracing/${reqType}`,
          method: 'POST',
          data:
            reqType === ReqActionType.selectiveMrRoomSignIn
              ? params // 单条操作，才能获取
              : { BarCodes: [params] }, // 撤销传BarCode会400...黄神没改，还是用多条的撤销形式 ,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    // 不管结果 重置节流标识
    hiddenLoading.current = false;

    if (!isRespErr(res)) {
      if (reqType === ReqActionType.selectiveMrRoomSignIn) {
        // 把modal关闭
        ModalStateDispatch({
          type: ModalAction.init,
        });
        setSelectedRecordKey([]);
        setModalAlert(false);

        // StatusCode 黄神的单独处理
        let result = handleMrActionApi(res.data);
        if (result?.isCorrect) {
          setActualCnt(actualCnt + 1);
        }
        result?.isCorrect
          ? message.success('签收成功')
          : message.error(result?.errMsg?.join('。/n'));

        if (result?.data?.length > 0) {
          // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
          // 把数据插入已记录列表
          SearchTableDispatch({
            type: TableAction.dataUnshiftUniq,
            payload: {
              data: Array.isArray(result?.data)
                ? result?.data?.at(0)
                : result?.data,
              key: 'BarCode',
              overWriteBy: {
                key: 'isCorrect',
                value: true,
              },
            },
          });
        }

        if (result?.errType === '404') {
          // 404
          Modal.error({
            title: result?.errMsg?.join('。/n'),
            onOk: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            onCancel: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
          });
        } else {
          // 其他的 barCode自动清除
          proFormRef.current.resetFields(['BarCode']);
          focusBarCode();
        }
      } else {
        // revert
        SearchTableDispatch({
          type: TableAction.dataFilt,
          payload: {
            key: 'BarCode',
            value: item.BarCode,
          },
        });
        setRevertRecord(item);

        message.success('撤销成功');
      }
    }
  };

  // columns 处理，主要用于处理options
  const columnsSolver = useMemo(() => {
    return SearchTable.columns
      ? columnsHandler(SearchTable.columns, {
          dataIndex: 'option',
          title: '操作',
          visible: true,
          width: 60,
          align: 'center',
          fixed: 'right',
          render: (
            text,
            record: SwagTraceRecordItem & { isCorrect: boolean },
          ) => {
            return (
              record?.isCorrect && (
                <Popconfirm
                  key="revert"
                  title="确定要撤销？"
                  onConfirm={(e) => {
                    e.stopPropagation();
                    reqActionReq(
                      record.BarCode,
                      record,
                      ReqActionType.mrRoomRevertSignIn,
                    );
                  }}
                  onCancel={(e) => e.stopPropagation()}
                >
                  <Tooltip title="撤销">
                    <UndoOutlined
                      className="icon_blue-color"
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Tooltip>
                </Popconfirm>
              )
            );
          },
        })
      : [];
  }, [SearchTable.columns]);

  // 处理撤销后的操作
  useEffect(() => {
    // 时间轴如果匹配则值空
    if (SearchTable?.clkItem?.BarCode === revertRecord?.BarCode) {
      SearchTableDispatch({
        type: TableAction.clkChange,
        payload: {
          clkItem: null,
        },
      });
      setParams(null);
    }
  }, [revertRecord]);

  // columns处理
  if (columnsList?.['TraceRecord'] && SearchTable.columns.length < 1) {
    SearchTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(Columns, columnsList['TraceRecord']),
      },
    });
    setModalColumns(
      tableColumnBaseProcessor(
        modalSelectedColumns,
        columnsList['TraceRecord'],
      ),
    );
  }

  const focusBarCode = () => {
    // 定位
    setTimeout(() => {
      barCodeRef.current?.focus({ cursor: 'end' });
    }, 20);
  };

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col xxl={7} xl={8}>
          <Card title="签收信息" style={{ marginBottom: '15px' }}>
            <ProForm
              layout="horizontal"
              className="sign_info_form" // flex-wrap
              grid
              rowProps={{
                gutter: 4,
              }}
              id="optionalDaySignInForm"
              labelCol={{ flex: '120px' }}
              wrapperCol={{ flex: 'auto' }}
              formRef={proFormRef}
              submitter={{
                render: (props, doms) => {
                  return [
                    <Button
                      type="primary"
                      style={{
                        width: 'calc(100% - 120px)',
                        float: 'right',
                        marginTop: '8px',
                      }}
                      key="submit"
                      onClick={() => {
                        if (hiddenLoading.current) return;
                        hiddenLoading.current = true;
                        props.form
                          .validateFields()
                          .then((values) => {
                            if (values.SignType.value === 'BarCode') {
                              searchByBarCodeReq(values, false);
                            } else {
                              // 对于非BarCode类型，创建请求参数对象
                              const searchParam = {
                                ..._.omit(values, 'BarCode'),
                                [values.SignType.value]: values.BarCode,
                                SelectiveSignInDate: values.SelectiveSignInDate,
                              };
                              searchOneReq(searchParam, false);
                            }
                          })
                          .catch((err) => {
                            hiddenLoading.current = false;
                          });
                      }}
                    >
                      签收(Enter)
                    </Button>,
                  ];
                },
              }}
            >
              <ProFormDatePicker
                name="SelectiveSignInDate"
                label="签收日期"
                placeholder={'请选择'}
                rules={[{ required: true }]}
                fieldProps={{
                  style: { width: '100%' },
                  format: 'YYYY-MM-DD',
                }}
              />
              <ProFormGroup>
                <ProFormSelect
                  name="SignType"
                  colProps={{ flex: '120px' }}
                  allowClear={false}
                  initialValue={{
                    label: SigninType[0].title,
                    value: SigninType[0].value,
                  }}
                  fieldProps={{
                    labelInValue: true,
                    fieldNames: {
                      label: 'title',
                      value: 'value',
                    },
                  }}
                  rules={[{ required: true }]}
                  options={SigninType as any[]}
                />
                <ProFormDependency name={['SignType']}>
                  {({ SignType }) => {
                    return (
                      <ProFormText
                        colProps={{ flex: 'auto' }}
                        name="BarCode"
                        placeholder={
                          SignType?.value === 'BarCode'
                            ? '条码号(扫码)'
                            : `请输入${SignType?.label}`
                        }
                        fieldProps={{
                          ref: barCodeRef,
                        }}
                        rules={[{ required: true }]}
                      />
                    );
                  }}
                </ProFormDependency>
              </ProFormGroup>
            </ProForm>
          </Card>
          <PatTimeline
            item={SearchTable?.clkItem}
            loading={loadings['TraceRecord/GetActions']}
            timelineItems={timelineItems}
          />
        </Col>
        <Col xxl={17} xl={16}>
          <Card
            title="已签收列表"
            extra={
              <Space>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'end',
                  }}
                >
                  实际签收数：
                  <InputNumber
                    ref={actualCntInputRef}
                    min={0}
                    controls={false}
                    value={actualCnt}
                    bordered={true}
                    onChange={(value) => {
                      if (!value) {
                        setActualCnt(0);
                      } else {
                        setActualCnt(value);
                      }
                    }}
                    className="actual_sign_cnt"
                    addonAfter={
                      <span
                        style={{ cursor: 'pointer' }}
                        onClick={(e) => {
                          setActualCnt(0);
                        }}
                      >
                        清零
                      </span>
                    }
                  />
                </div>
                <Divider type="vertical" />
                <Popconfirm
                  title="导出时会将错误的记录过滤掉"
                  onConfirm={(e) => {
                    let exportColumns = columnsSolver?.filter(
                      (columnItem) =>
                        columnItem.className?.indexOf('exportable') > -1 &&
                        columnItem.valueType !== 'option' &&
                        columnItem.dataIndex !== 'operation',
                    );
                    exportExcel(
                      exportColumns,
                      exportExcelDictionaryModuleProcessor(
                        exportColumns,
                        _.cloneDeep(
                          SearchTable.data?.filter((d) => d.isCorrect),
                        ),
                      ),
                      `病案单份签收列表_择期_${dayjs().format('YYYY-MM-DD')}`,
                      [],
                    );
                  }}
                  disabled={
                    SearchTable.data?.filter((d) => d.isCorrect)?.length < 1
                  }
                >
                  <Tooltip title="导出Excel">
                    <Button
                      type="text"
                      shape="circle"
                      disabled={
                        SearchTable.data?.filter((d) => d.isCorrect)?.length < 1
                      }
                      icon={<FileExcelOutlined />}
                    />
                  </Tooltip>
                </Popconfirm>
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl: 'Api/Mr/TraceRecord/GetList',
                    onTableRowSaveSuccess: (columns) => {
                      // 这个columns 存到dva
                      dispatch({
                        type: 'global/saveColumns',
                        payload: {
                          name: 'TraceRecord',
                          value: columns,
                        },
                      });
                      SearchTableDispatch({
                        type: TableAction.columnsChange,
                        payload: {
                          columns: tableColumnBaseProcessor(Columns, columns),
                        },
                      });
                    },
                  }}
                />
              </Space>
            }
          >
            <UniTable
              id="trace_record"
              rowKey="uuid"
              showSorterTooltip={false}
              loading={loadings['TraceRecord/GetList'] || false}
              columns={columnsSolver} // columnsHandler
              dataSource={SearchTable.data}
              scroll={{ x: 'max-content' }}
              dictionaryData={globalState?.dictData}
              rowClassName={(record, index) => {
                let classname = [];
                // 互斥
                if (!record?.isCorrect) {
                  classname.push('row-error');
                } else if (index === 0) {
                  return 'row-first';
                }
                if (record?.uuid === SearchTable.clkItem?.uuid) {
                  classname.push('row-selected');
                }

                return classname.length > 0 ? clsx(classname) : null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (SearchTable.clkItem?.BarCode !== record?.BarCode) {
                      SearchTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
            />
          </Card>
        </Col>
      </Row>

      <Modal
        title="确认病案"
        open={ModalState.visible}
        width={900}
        onOk={(e) => {
          console.log(
            ModalState.record,
            selectedRecordKey,
            ModalState.record.findIndex(
              (d) => d.BarCode === selectedRecordKey?.at(0),
            ),
          );
          if (
            ModalState.record.findIndex(
              (d) => d.BarCode === selectedRecordKey?.at(0),
            ) > -1
          ) {
            reqActionReq(
              {
                ..._.omit(ModalState.specialData, 'BarCode'),
                BarCode: selectedRecordKey?.at(0),
              },
              null,
              ReqActionType.selectiveMrRoomSignIn,
            );
          } else {
            // 没选
            setModalAlert(true);
          }
        }}
        okButtonProps={{
          loading: loadings[`Tracing/${ReqActionType.selectiveMrRoomSignIn}`],
        }}
        onCancel={(e) => {
          // 重置节流标识
          hiddenLoading.current = false;
          focusBarCode();
          ModalStateDispatch({
            type: ModalAction.init,
          });
          setSelectedRecordKey([]);
          setModalAlert(false);
        }}
      >
        <UniTable
          id="multi_record_check"
          rowKey="BarCode"
          showSorterTooltip={false}
          loading={
            loadings['TraceRecord/GetList'] ||
            loadings[`Tracing/${ReqActionType.selectiveMrRoomSignIn}`] ||
            false
          }
          columns={modalColumns} // columnsHandler
          dataSource={ModalState.record}
          scroll={{ x: 'max-content' }}
          tableAlertRender={() => {
            return modalAlert ? (
              <Alert
                message="请选择一个病案"
                description="请选择一个病案再点击确认，如果没有查询到目标病案请确认信息输入正确"
                type="error"
                closable
                onClose={() => {
                  setModalAlert(false);
                }}
              />
            ) : (
              false
            );
          }}
          tableAlertOptionRender={false}
          rowSelection={{
            alwaysShowAlert: true,
            type: 'radio',
            selectedRowKeys: selectedRecordKey,
            onChange: (
              selectedRowKeys: React.Key[],
              selectedRows: SwagTraceRecordItem[],
            ) => {
              setSelectedRecordKey(selectedRowKeys);
              setModalAlert(false);
            },
          }}
          onRow={(record) => {
            return {
              onClick: (event) => {
                setSelectedRecordKey([record?.BarCode]);
              },
            };
          }}
        />
      </Modal>
    </>
  );
};

export default OptionalDayMrRoomSignIn;
