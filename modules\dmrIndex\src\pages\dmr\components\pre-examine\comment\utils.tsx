import { RuleItem } from '../interface';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  icdeColumns,
  operationColumns,
  pathologyIcdeColumns,
  icuColumns,
} from '@/pages/dmr/columns';

const tableToTableColumnName = {
  diagnosisTable: 'IcdeTable',
  operationTable: 'OperTable',
  pathologicalDiagnosisTable: 'PathoTable',
  tcmDiagnosisTable: 'TcmIcdeTable',
  icuTable: 'IcuTable',
};

const tableToMissingKey = {
  diagnosisTable: 'MissingIcde',
  operationTable: 'MissingOper',
  pathologicalDiagnosisTable: 'MissingIcde',
  tcmDiagnosisTable: 'MissingTcmIcde',
  icuTable: 'MissingIcu',
};

export const ruleCoderFinder = (formKey: string, rulesItems: RuleItem[]) => {
  let ruleCode = undefined;
  let formKeys = formKey?.split('-');

  // 主诊 主手术
  if (formKey?.includes('-Main') && formKey?.includes('diagnosisTable')) {
    // 返回 主要诊断 categoryCode
    let mainIcdeRuleDetailItem = rulesItems?.find(
      (item) => item?.ColumnName === `Main${formKeys?.at(2)}`,
    );
    return mainIcdeRuleDetailItem?.RuleCode;
  }

  // 主诊 主手术
  if (formKey?.includes('-Main') && formKey?.includes('operationTable')) {
    // 返回 主手术 categoryCode
    let mainOperRuleDetaiItem = rulesItems?.find(
      (item) => item?.ColumnName === `Main${formKeys?.at(2)}`,
    );
    return mainOperRuleDetaiItem?.RuleCode;
  }

  // 遗漏
  if (formKey?.includes('-Missing')) {
    let ruleDetailColumnOfTable = `${
      tableToTableColumnName?.[formKeys?.at(1)]
    }-${tableToMissingKey[formKeys?.at(1)]}`;
    let missingRuleDetailItem = rulesItems?.find(
      (item) => item?.ColumnName === ruleDetailColumnOfTable,
    );
    return missingRuleDetailItem?.RuleCode;
  }

  // 表格
  if (formKey?.startsWith('Table-')) {
    let ruleDetailColumnOfTable = `${
      tableToTableColumnName?.[formKeys?.at(1)]
    }-${formKeys?.at(2)}`;
    let tableRuleDetailItem = rulesItems?.find((item) =>
      item?.ColumnName?.split(',')?.includes(ruleDetailColumnOfTable),
    );

    if (!isEmptyValues(tableRuleDetailItem)) {
      return tableRuleDetailItem?.RuleCode;
    }
  }

  // 费用
  if (formKey?.startsWith('FeeItem-')) {
    let feeRuleDetailItem = rulesItems?.find((item) =>
      item?.ColumnName?.includes(formKey?.replace('FeeItem-', '')),
    );

    if (!isEmptyValues(feeRuleDetailItem)) {
      return feeRuleDetailItem?.RuleCode;
    }
  }

  for (let commentRuleDetailItem of rulesItems) {
    if (
      commentRuleDetailItem?.ColumnName?.split(',')?.includes(
        formKey?.replace('SeparateSelector', ''),
      )
    ) {
      ruleCode = commentRuleDetailItem?.RuleCode;
    }

    if (!isEmptyValues(ruleCode)) {
      break;
    }
  }

  return ruleCode;
};

export const tableItemFeeItemSpecialKeyProcessor = (formKey: string) => {
  if (formKey?.includes('Table-')) {
    return formKey?.replace('-Main', '');
  }

  if (formKey?.includes('FeeItem')) {
    return formKey;
  }

  if (
    formKey?.toLowerCase()?.includes('fee') ||
    formKey?.toLowerCase()?.includes('amt')
  ) {
    return `FeeItem-${formKey}`;
  }

  return formKey;
};

export const columnNameToLabel = (columnName: string) => {
  if (columnName?.includes('Table-')) {
    let columnIndex = columnName?.split('-')?.at(2);

    if (columnName?.includes('diagnosisTable')) {
      if (columnName?.includes('-Missing')) {
        return `缺失诊断`;
      }

      let icdeColumnItem = icdeColumns?.find(
        (item) => item?.dataIndex === columnIndex,
      );
      if (!isEmptyValues(icdeColumnItem)) {
        if (columnName?.includes('-Main')) {
          return `主要诊断-${icdeColumnItem?.title}`;
        } else {
          return `诊断-${icdeColumnItem?.title}`;
        }
      }
    }

    if (columnName?.includes('operationTable')) {
      if (columnName?.includes('-Missing')) {
        return `缺失手术`;
      }
      let operColumnItem = operationColumns?.find(
        (item) => item?.dataIndex === columnIndex,
      );
      if (operColumnItem) {
        if (columnName?.includes('-Main')) {
          return `主手术-${operColumnItem?.title}`;
        } else {
          return `手术-${operColumnItem?.title}`;
        }
      }
    }

    if (columnName?.includes('pathologicalDiagnosisTable')) {
      if (columnName?.includes('-Missing')) {
        return `缺失病理诊断`;
      }

      let pathoColumnItem = pathologyIcdeColumns?.find(
        (item) => item?.dataIndex === columnIndex,
      );
      if (pathoColumnItem) {
        return `病理-${pathoColumnItem?.title}`;
      }
    }

    if (columnName?.includes('icuTable')) {
      if (columnName?.includes('-Missing')) {
        return `缺失ICU数据`;
      }

      let icuColumnItem = icuColumns?.find(
        (item) => item?.dataIndex === columnIndex,
      );
      if (icuColumnItem) {
        return `ICU-${icuColumnItem?.title}`;
      }
    }

    // TODO 中医诊断表
  } else if (columnName?.includes('FeeItem-')) {
    let dmrElementFeeItem = document.querySelector(
      `#${columnName} > span[class="label"]`,
    );
    if (!isEmptyValues(dmrElementFeeItem)) {
      return dmrElementFeeItem?.textContent
        ?.replace(':', '')
        ?.replace('：', '')
        ?.replace('*', '')
        ?.replace(/\(\d*\)/g, '')
        ?.replace(/（\d*）/g, '');
    }
  } else {
    let dmrElementItem = document.querySelector(
      `#${columnName} span[id^="grid-item-prefix"]`,
    );
    if (!isEmptyValues(dmrElementItem)) {
      return dmrElementItem?.textContent;
    }
  }
};

export const isSpecialColumnNameKey = (columnName: string) => {
  if (
    columnName?.toLowerCase()?.includes('fee') ||
    columnName?.toLowerCase()?.includes('amt')
  ) {
    return true;
  }

  if (columnName?.startsWith('Table-')) {
    return true;
  }

  // TODO 有可能有其他的 所以先留个口子

  return false;
};

export const containerKeyProcessor = (columnName: string) => {
  if (
    columnName?.toLowerCase()?.includes('fee') ||
    columnName?.toLowerCase()?.includes('amt')
  ) {
    let feeFormKey = `FeeItem-${columnName.replace('FeeItem-', '')}`;

    let formKeyContainer = document
      .getElementById(feeFormKey)
      ?.closest('.grid-stack-item');
    return {
      containerKey: formKeyContainer?.id,
    };
  }

  if (columnName?.startsWith('Table-')) {
    let columnNames = columnName.split('-');
    return {
      containerKey: columnNames?.at(1),
    };
  }

  return {};
};

export const deleteDmrHasPreCommentStyle = () => {
  let hasPreCommentStyleElements = document?.querySelectorAll(
    '.grid-stack-item-info-container-has-pre-comment',
  );
  hasPreCommentStyleElements?.forEach((item) => {
    item?.classList?.remove('grid-stack-item-info-container-has-pre-comment');
  });
};

export const clearAllPreCommentClass = () => {
  let hasPreCommentStyleElements = document?.querySelectorAll(
    '.grid-stack-item-info-container-has-pre-comment',
  );
  hasPreCommentStyleElements?.forEach((item) => {
    item?.classList?.remove('grid-stack-item-info-container-has-pre-comment');
  });
};

export const formKeyHasNoCommentItem = (
  commentItems: any[],
  formKey: string,
) => {
  return commentItems?.filter((item) => item?.formKey === formKey)?.length <= 0;
};

export const deleteDmrHasPreCommentStyleByFormKey = (formKey: string) => {
  let hasPreCommentStyleElement = document?.querySelector(`#${formKey}`);

  hasPreCommentStyleElement?.classList?.remove(
    'grid-stack-item-info-container-has-pre-comment',
  );

  hasPreCommentStyleElement
    ?.querySelector('.grid-stack-item-info-container-has-pre-comment')
    ?.classList?.remove('grid-stack-item-info-container-has-pre-comment');
};

export const addDmrHasPreCommentStyle = (commentItem: any) => {
  if (!isSpecialColumnNameKey(commentItem?.formKey)) {
    let itemContentElement = document.querySelector(
      `#dmr-form-container #${commentItem?.formKey} .grid-stack-item-info-container`,
    );
    if (itemContentElement) {
      itemContentElement?.classList?.add(
        'grid-stack-item-info-container-has-pre-comment',
      );
    }
  } else {
    // 表示 是 费用或者其他的 ？
    if (
      commentItem?.formKey?.toLowerCase()?.includes('fee') ||
      commentItem?.formKey?.toLowerCase()?.includes('amt')
    ) {
      let itemContentElement = document.querySelector(
        `#dmr-form-container #${tableItemFeeItemSpecialKeyProcessor(
          commentItem?.formKey,
        )}`,
      );
      if (itemContentElement) {
        itemContentElement?.classList?.add(
          'grid-stack-item-info-container-has-pre-comment',
        );
      }
    }

    // 表格
    if (commentItem?.formKey?.toLowerCase()?.startsWith('table-')) {
      let itemContentElement = document.querySelector(
        `#dmr-form-container div[itemid='${tableItemFeeItemSpecialKeyProcessor(
          commentItem?.formKey,
        )}']`,
      );
      if (itemContentElement) {
        itemContentElement?.classList?.add(
          'grid-stack-item-info-container-has-pre-comment',
        );
      }
    }
  }
};
