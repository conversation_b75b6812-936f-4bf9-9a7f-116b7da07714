import { Sorta<PERSON><PERSON>andle } from 'react-sortable-hoc';
import React, { useEffect, useState } from 'react';
import {
  PlusOutlined,
  DeleteOutlined,
  AlertOutlined,
  MenuOutlined,
  PlusCircleTwoTone,
  InfoCircleTwoTone,
} from '@ant-design/icons';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { Checkbox, Input, Popconfirm, Tag, Tooltip, Button } from 'antd';
import IcdeSelect from '@/pages/dmr/components/icde-select';
import OperationSelect from '@/pages/dmr/components/oper-select';
import dayjs from 'dayjs';
import {
  IcdeOperationInputSelector,
  IcdeFieldInput,
  OperationFieldInput,
  IcdeOperationReadonlyItem,
  IcdeExtraTagsItem,
  OperationExtraTagsItem,
  icdeExtraMap,
  OperIcdeExtraMapItem,
  operationExtraMap,
  hqmsDegreeMap,
  drgsDegreeMap,
  PathologyIcdeFieldInput,
  IcuDurationFieldInput,
  IcdeOperCheckbox,
} from '@uni/grid/src/components/icde-oper-input/input';
import { RowSelectionCheckbox } from '@uni/grid/src/components/row-selection';
import { RowSelectionHeader } from '@uni/grid/src/components/row-selection-header';
import { BatchDeleteButton } from '@/pages/dmr/components/batch-delete-button';
import DateSelect from '@uni/grid/src/components/date-select';
import IconBtn from '@uni/components/src/iconBtn';
import RestrictInputNumber from '@uni/grid/src/components/restrict-number';
import { employeeDataSourceProcessor } from '@/pages/dmr/utils';
import { getArrowUpDownEventKey } from '@uni/grid/src/utils';

const tableOnlyAddIconTrigger =
  (window as any).externalConfig?.['dmr']?.tableOnlyAddIconTrigger ?? false;

const enableTableDropdownNG =
  (window as any).externalConfig?.['dmr']?.enableTableDropdownNG ?? false;

const icdeOperRowSelection =
  (window as any).externalConfig?.['dmr']?.icdeOperRowSelection ?? false;

const nonAddCell = (record, index) => {
  if (record?.id === 'ADD') {
    return {
      colSpan: 0,
    };
  }

  return {};
};

const DragHandler = (node) => {
  return true
    ? SortableHandle(() => <div className={'grab-handle'}>{node}</div>)
    : () => node;
};

interface ExtraTitlePromptItem {
  [key: string]: any;
}

const extraTitle = (
  extraMap?: { [key: string]: OperIcdeExtraMapItem },
  prompts?: ExtraTitlePromptItem,
) => {
  return (
    <Tooltip title={extraTitlePrompt(prompts, extraMap)}>
      <span>
        注<InfoCircleTwoTone style={{ marginLeft: 3 }} />
      </span>
    </Tooltip>
  );
};

const extraTitlePrompt = (
  prompts: ExtraTitlePromptItem,
  extraMap: { [key: string]: OperIcdeExtraMapItem },
) => {
  return (
    <>
      {Object.keys(extraMap)?.map((key, index) => {
        if (key === 'HqmsDegree') {
          return (
            <>
              {Object.keys(hqmsDegreeMap)
                ?.slice(-1)
                ?.map((hqmsKey, index) => {
                  return (
                    <PromptItem
                      color={hqmsDegreeMap?.[hqmsKey]?.color}
                      display={hqmsDegreeMap?.[hqmsKey]?.display}
                      prompt={
                        prompts?.[key]?.prompt ||
                        hqmsDegreeMap?.[hqmsKey]?.prompt
                      }
                      key={index}
                    />
                  );
                })}
            </>
          );
        } else if (key === 'DrgsDegree') {
          return (
            <>
              {Object.keys(drgsDegreeMap)
                ?.slice(-1)
                ?.map((drgsKey, index) => {
                  return (
                    <PromptItem
                      color={drgsDegreeMap?.[drgsKey]?.color}
                      display={drgsDegreeMap?.[drgsKey]?.display}
                      prompt={
                        prompts?.[key]?.prompt ||
                        drgsDegreeMap?.[drgsKey]?.prompt
                      }
                      key={index}
                    />
                  );
                })}
            </>
          );
        } else {
          return (
            <PromptItem
              color={extraMap?.[key]?.color}
              display={extraMap?.[key]?.display}
              prompt={prompts?.[key]?.prompt || extraMap?.[key]?.prompt}
              key={index}
            />
          );
        }
      })}
    </>
  );
};

const PromptItem = ({ color, display, prompt, extraLine = false }) => {
  return (
    <>
      <Tag color={color} style={{ marginRight: 4 }}>
        {display}
      </Tag>
      {prompt}
      {extraLine && <br />}
    </>
  );
};

// 主表格专用的列定义（过滤掉医保相关列）
export const icdeMainColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcdeExtra',
    title: extraTitle(icdeExtraMap, {
      InsurIsObsolete: {
        prompt: '医保置灰',
      },
    }),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_ICDE_MAIN_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-diagnosisMainTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_ICDE_MAIN_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      }

      return (
        <IcdeExtraTagsItem
          record={record}
          index={index}
          extraMap={icdeExtraMap}
        />
      );
    },
    onCell: nonAddCell,
  },
  // 行选择列
  ...(icdeOperRowSelection
    ? [
        {
          key: 'RowSelection',
          dataIndex: 'RowSelection',
          title: (
            <RowSelectionHeader
              tableId="diagnosisMainTable"
              batchDeleteButton={
                <BatchDeleteButton tableId="diagnosisMainTable" />
              }
            />
          ),
          fixed: 'left',
          visible: true,
          align: 'center',
          width: 50,
          readonly: false,
          renderColumnFormItem: (node, record, index) => {
            if (record?.id === 'ADD') {
              return null;
            }
            return (
              <RowSelectionCheckbox
                record={record}
                index={index}
                tableId="diagnosisMainTable"
              />
            );
          },
          onCell: nonAddCell,
        },
      ]
    : []),
  // 拖拽排序列
  {
    key: 'DRAG_SORT',
    dataIndex: 'DRAG_SORT',
    title: '',
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 30,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      const DragHandle = DragHandler(
        <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />,
      );
      return <DragHandle />;
    },
    onCell: nonAddCell,
  },
  // 序号列
  {
    key: 'LineNumber',
    dataIndex: 'LineNumber',
    title: '序号',
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 60,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }
      return <span>{index + 1}</span>;
    },
    onCell: nonAddCell,
  },
  // 诊断编码列
  {
    key: 'IcdeCode',
    dataIndex: 'IcdeCode',
    title: '诊断编码',
    fixed: 'left',
    visible: true,
    align: 'left',
    width: 120,
    readonly: false,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <IcdeSelect
          record={record}
          index={index}
          tableId="diagnosisMainTable"
          onSelectAdd={() => {
            Emitter.emit(EventConstant.DMR_ICDE_MAIN_SELECT_ADD, {
              record,
              index,
            });
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  // 诊断名称列
  {
    key: 'IcdeName',
    dataIndex: 'IcdeName',
    title: '诊断名称',
    visible: true,
    align: 'left',
    width: 200,
    readonly: false,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <IcdeFieldInput
          record={record}
          index={index}
          tableId="diagnosisMainTable"
          onInputAdd={() => {
            Emitter.emit(EventConstant.DMR_ICDE_MAIN_INPUT_ADD, {
              record,
              index,
            });
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  // 诊断情况列
  {
    key: 'IcdeCond',
    dataIndex: 'IcdeCond',
    title: '诊断情况',
    visible: true,
    align: 'left',
    width: 120,
    readonly: false,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <IcdeOperationInputSelector
          record={record}
          index={index}
          tableId="diagnosisMainTable"
          fieldKey="IcdeCond"
          onAdd={() => {
            Emitter.emit(EventConstant.DMR_ICDE_MAIN_COND_ADD, {
              record,
              index,
            });
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  // 诊断结果列
  {
    key: 'IcdeOutcome',
    dataIndex: 'IcdeOutcome',
    title: '诊断结果',
    visible: true,
    align: 'left',
    width: 120,
    readonly: false,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <IcdeOperationInputSelector
          record={record}
          index={index}
          tableId="diagnosisMainTable"
          fieldKey="IcdeOutcome"
          onAdd={() => {
            Emitter.emit(EventConstant.DMR_ICDE_MAIN_OUTCOME_ADD, {
              record,
              index,
            });
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  // 主诊断列
  {
    key: 'IsMain',
    dataIndex: 'IsMain',
    title: '主诊断',
    visible: true,
    align: 'center',
    width: 80,
    readonly: false,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <IcdeOperCheckbox
          record={record}
          index={index}
          tableId="diagnosisMainTable"
          fieldKey="IsMain"
          onChange={(checked) => {
            Emitter.emit(EventConstant.DMR_ICDE_MAIN_INSURE_MAIN, {
              index,
              checked,
            });
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  // 上报列
  {
    key: 'IsReported',
    dataIndex: 'IsReported',
    title: '上报',
    visible: true,
    align: 'center',
    width: 60,
    readonly: false,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <IcdeOperCheckbox
          record={record}
          index={index}
          tableId="diagnosisMainTable"
          fieldKey="IsReported"
          onChange={(checked) => {
            Emitter.emit(EventConstant.DMR_ICDE_MAIN_REPORT, checked);
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  // 操作列
  {
    key: 'ACTIONS',
    dataIndex: 'ACTIONS',
    title: '操作',
    fixed: 'right',
    visible: true,
    align: 'center',
    width: 100,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <div className="table-actions">
          <IconBtn
            customIcon={<PlusOutlined />}
            size="small"
            title="复制"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_ICDE_MAIN_COPY, {
                index,
                record,
              });
            }}
          />
          <Popconfirm
            title="确定删除这条诊断记录吗？"
            onConfirm={() => {
              Emitter.emit(EventConstant.DMR_ICDE_MAIN_DELETE, index);
            }}
            okText="确定"
            cancelText="取消"
          >
            <IconBtn
              customIcon={<DeleteOutlined />}
              size="small"
              title="删除"
              danger
            />
          </Popconfirm>
        </div>
      );
    },
    onCell: nonAddCell,
  },
];
