import dayjs from 'dayjs';
import { Popconfirm, Tooltip, Space } from 'antd';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import React from 'react';
import { DmrCardOperation, DmrEventConstant } from '@/utils/constants';
import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  EyeFilled,
  EyeOutlined,
  LockOutlined,
  UnlockOutlined,
} from '@ant-design/icons';
import { history } from 'umi';
import IconBtn from '@uni/components/src/iconBtn';

export const dmrManagementStatColumns = [
  {
    dataIndex: 'MonthDate',
    title: '日期',
    visible: true,
    render: (node, record, index) => {
      return <span>{dayjs(record['MonthDate']).format('YYYY-MM')}</span>;
    },
  },
];

export const dmrManagementCardColumns = (hideInSearchPage) => [
  {
    dataIndex: 'PatNo',
    // title: '病案号',
    visible: true,
  },
  {
    dataIndex: 'OutDate',
    // title: '出院时间',
    visible: true,
    width: 150,
  },
  {
    dataIndex: 'PatName',
    // title: '姓名',
    visible: true,
  },
  {
    dataIndex: 'CliDept',
    // title: '科室',
    visible: true,
  },
  {
    dataIndex: 'IsLocked',
    // title: '锁定状态',
    visible: true,
    width: 100,
    align: 'center',
    // filterType: 'filter',
    // filterMultiple: false,
    // filters: [
    //   {
    //     text: '已锁定',
    //     value: true,
    //   },
    //   {
    //     text: '未锁定',
    //     value: false,
    //   },
    // ],
    render: (node, record, index) => {
      return (
        <span>
          {record['IsLocked'] ? (
            <Tooltip title={'已锁定'}>
              <LockOutlined />
            </Tooltip>
          ) : (
            <Tooltip title={'未锁定'}>
              <UnlockOutlined />
            </Tooltip>
          )}
        </span>
      );
    },
  },
  // {
  //   dataIndex: 'RegisterStatusName',
  //   title: '锁定状态',
  //   visible: true,
  //   width: 100,
  //   align: 'center',
  //   // filterType: 'filter',
  //   // filterMultiple: false,
  //   // filters: [
  //   //   {
  //   //     text: '已锁定',
  //   //     value: true,
  //   //   },
  //   //   {
  //   //     text: '未锁定',
  //   //     value: false,
  //   //   },
  //   // ],
  //   render: (node, record, index) => {
  //     return <span>{record['IsLocked'] ? <CheckOutlined /> : <CloseOutlined />}</span>;
  //   },
  // },
  {
    dataIndex: 'operation',
    title: '操作',
    width: hideInSearchPage ? 60 : 80,
    visible: true,
    align: 'center',
    fixed: 'right',
    render: (node, record, index) => {
      return (
        <Space size={10}>
          <IconBtn
            type="check"
            className="operation-item"
            onClick={() => {
              // window.open(
              //   `/dmr/index?hisId=${encodeURIComponent(
              //     record.HisId,
              //   )}&instantAudit=true`,
              // );
              // global['tabAwareWorker'].port.postMessage({
              //   type: 'DMR_VIEW_CLICK',
              //   payload: {
              //     hisId: encodeURIComponent(record.HisId),
              //     instantAudit: true,
              //   },
              // });
              (global?.window as any)?.eventEmitter?.emit('DMR_AOD_STATUS', {
                status: true,
                hisId: record.HisId,
                instantAudit: true,
              });
            }}
          />

          {!record['IsLocked'] && record['CanLock'] && !hideInSearchPage && (
            <IconBtn
              type="lock"
              openPop={true}
              popOnConfirm={() => {
                Emitter.emit(DmrEventConstant.DMR_MANAGEMENT_CARD_OPERATION, {
                  type: DmrCardOperation.LOCK,
                  record: record,
                  index: index,
                });
              }}
            />
          )}

          {record['IsLocked'] && !hideInSearchPage && (
            <IconBtn
              type="unlock"
              openPop={true}
              popOnConfirm={() => {
                Emitter.emit(DmrEventConstant.DMR_MANAGEMENT_CARD_OPERATION, {
                  type: DmrCardOperation.UNLOCK,
                  record: record,
                  index: index,
                });
              }}
            />
          )}
        </Space>
      );
    },
  },
];

export const dmrManagementDeptCardColumns = [
  {
    dataIndex: 'IsLocked',
    // title: '锁定状态',
    visible: true,
    width: 90,
    align: 'center',
    // filterType: 'filter',
    // filterMultiple: false,
    // filters: [
    //   {
    //     text: '已锁定',
    //     value: true,
    //   },
    //   {
    //     text: '未锁定',
    //     value: false,
    //   },
    // ],
    render: (node, record, index) => {
      return (
        <span>
          {record['IsLocked'] ? <CheckOutlined /> : <CloseOutlined />}
        </span>
      );
    },
  },
];
