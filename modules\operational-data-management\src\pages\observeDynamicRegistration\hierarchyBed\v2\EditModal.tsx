import React, { useEffect, useRef, useState } from 'react';
import { Modal, message, Button } from 'antd';
import { UpsertBedAmtParams, HierarchyBedAmtItem } from './interface';
import { HIERARCHY_TYPE } from './constants';
import dayjs from 'dayjs';
import ProFormContainer, {
  SearchFormOptType,
} from '@uni/components/src/pro-form-container';
import { ProFormInstance } from '@uni/components/src/pro-form';

interface EditModalProps {
  visible: boolean;
  record: HierarchyBedAmtItem | null;
  columnsData: any[]; // 列定义数据
  dictData: any;
  onCancel: () => void;
  onSubmit: (values: UpsertBedAmtParams) => Promise<void>;
}

const EditModal: React.FC<EditModalProps> = ({
  visible,
  record,
  columnsData,
  dictData,
  onCancel,
  onSubmit,
}) => {
  const isEdit = !!record?.Id;
  const title = isEdit ? '编辑床位数' : '新增床位数';
  const today = dayjs().format('YYYY-MM-DD');
  const [formItems, setFormItems] = useState<SearchFormOptType[]>([]);
  const formRef = useRef<ProFormInstance>();
  const [submitLoading, setSubmitLoading] = useState(false);

  // 保存当前HospCode值，确保即使字段是hidden也能正确使用
  const [currentHospCode, setCurrentHospCode] = useState<string>('');

  // 定义床位数量字段 - 提取到外部以便复用
  const bedAmtFields = [
    { name: 'ApprovedBedAmt', defaultTitle: '核定床位数' },
    { name: 'OpenBedAmt', defaultTitle: '开放床位数' },
    { name: 'Reserve1Amt', defaultTitle: '其他床位数1' },
    { name: 'Reserve2Amt', defaultTitle: '其他床位数2' },
    { name: 'Reserve3Amt', defaultTitle: '其他床位数3' },
    { name: 'Reserve4Amt', defaultTitle: '其他床位数4' },
    { name: 'Reserve5Amt', defaultTitle: '其他床位数5' },
  ];

  // 找到科室字段的字典配置
  const findHierarchyColumnDictionary = () => {
    // 查找层级编码相关的列定义
    const hierarchyColumn = columnsData.find(
      (col) => col.dataIndex === 'HierarchyCode' || col.key === 'HierarchyCode',
    );

    if (hierarchyColumn) {
      const dictModule = hierarchyColumn.dictionaryModule;
      const dictModuleGroup = hierarchyColumn.dictionaryModuleGroup;

      // 根据dictionaryModule和dictionaryModuleGroup确定字典
      if (dictModuleGroup && dictData?.[dictModuleGroup]?.[dictModule]) {
        return dictData[dictModuleGroup][dictModule];
      } else if (dictData?.[dictModule]) {
        return dictData[dictModule];
      }
    }

    // 默认使用DynDepts
    return dictData?.DynDepts || [];
  };

  // 处理科室选择，更新HospCode
  const handleHierarchyChange = (value: any) => {
    // 使用labelInValue时，value是一个包含value和label的对象
    const selectedValue = value?.value || value;

    const deptList = findHierarchyColumnDictionary();
    const selectedDept = deptList.find((dept) => dept.Code === selectedValue);

    // 如果找到选择的科室，并且有ExtraProperties.HospCode，则设置HospCode
    if (selectedDept?.ExtraProperties?.HospCode) {
      const hospCode = selectedDept.ExtraProperties.HospCode;
      formRef.current?.setFieldValue('HospCode', hospCode);
      setCurrentHospCode(hospCode);
      console.log('设置HospCode:', hospCode);
    }
  };

  // 判断字段是否可见并获取列定义
  const getColumnDefinition = (fieldName: string) => {
    const column = columnsData.find(
      (col) => col.dataIndex === fieldName || col.key === fieldName,
    );

    // 返回列定义和可见性
    return {
      column,
      // 如果没有找到列定义或者没有visible属性，默认为可见
      // 如果有visible属性并且明确设置为false，则不可见
      visible: !column || column.visible !== false,
    };
  };

  // 初始化表单
  useEffect(() => {
    // 设置表单项
    const deptList = findHierarchyColumnDictionary();

    // 自定义日期验证规则
    const dateRangeValidator = async (_, value) => {
      if (!value || !Array.isArray(value) || value.length !== 2) {
        throw new Error('请选择有效的日期范围');
      }

      // 确保日期是dayjs对象
      const startDate = dayjs.isDayjs(value[0]) ? value[0] : dayjs(value[0]);
      const endDate = dayjs.isDayjs(value[1]) ? value[1] : dayjs(value[1]);

      if (!startDate.isValid() || !endDate.isValid()) {
        throw new Error('日期格式无效');
      }

      const Sdate = startDate.format('YYYY-MM-DD');
      const Edate = endDate.format('YYYY-MM-DD');

      // 开始时间必须小于等于当前日期
      if (dayjs(Sdate).isAfter(today)) {
        throw new Error('开始时间不能大于当前日期');
      }

      // 结束日期不能小于等于当前日期
      if (dayjs(Edate).isBefore(today) || dayjs(Edate).isSame(today)) {
        throw new Error('结束日期必须大于当前日期');
      }

      return Promise.resolve();
    };

    // 定义基础表单项
    const baseItems: SearchFormOptType[] = [
      // 第一行 - 科室
      {
        name: 'HierarchyCode',
        title: '科室',
        dataType: 'select',
        colProps: { span: 12 },
        rules: [{ required: true, message: '请选择科室' }],
        opts: deptList.map((item) => ({
          Name: item.Name,
          Code: item.Code,
        })),
        fieldProps: {
          placeholder: '请选择科室',
          showSearch: true,
          optionFilterProp: 'label',
          labelInValue: true,
          onChange: handleHierarchyChange,
        },
      },

      // 隐藏的HospCode字段
      {
        name: 'HospCode',
        title: '院区代码',
        dataType: 'text',
        hidden: true,
      },

      // 第二行 - 日期范围
      {
        name: 'dateRange',
        title: '生效日期',
        dataType: 'dateRange',
        colProps: { span: 12 },
        rules: [
          { required: true, message: '请选择生效日期范围' },
          { validator: dateRangeValidator },
        ],
        fieldProps: {
          style: { width: '100%' },
        },
      },
    ];

    // 过滤掉不可见的床位字段并获取列定义中的标题
    const visibleBedAmtFields = bedAmtFields
      .map((field) => {
        const { column, visible } = getColumnDefinition(field.name);
        if (!visible) return null;

        // 使用列定义中的title，如果没有则使用默认title
        const title = column?.title || field.defaultTitle;
        return { ...field, title };
      })
      .filter(Boolean); // 过滤掉null值

    // 将可见的床位字段添加到表单项中
    const bedAmtFormItems = visibleBedAmtFields.map((field) => ({
      name: field.name,
      title: field.title,
      dataType: 'number',
      colProps: { span: 12 },
      fieldProps: {
        style: { width: '100%' },
        min: 0,
        precision: 0, // 不允许输入小数点
      },
    }));

    // 隐藏字段，用于传递额外参数
    const hiddenItems: SearchFormOptType[] = [
      {
        name: 'Id',
        hidden: true,
        dataType: 'text',
      },
      {
        name: 'HierarchyType',
        hidden: true,
        dataType: 'text',
      },
    ];

    // 合并所有表单项
    const items = [...baseItems, ...bedAmtFormItems, ...hiddenItems];
    setFormItems(items);
  }, [columnsData, dictData, today]);

  // 初始化表单值
  useEffect(() => {
    if (visible) {
      if (record && formRef.current) {
        // 编辑模式，设置表单值
        const hospCode = record.HospCode || '';

        formRef.current.setFieldsValue({
          ...record,
          dateRange: [
            record.Sdate ? dayjs(record.Sdate) : null,
            record.Edate ? dayjs(record.Edate) : null,
          ],
          HierarchyType: HIERARCHY_TYPE,
          HospCode: hospCode, // 确保HospCode被设置
        });

        setCurrentHospCode(hospCode); // 保存当前HospCode
        console.log('编辑模式设置HospCode:', hospCode);
      } else if (formRef.current) {
        // 新增模式，清空表单
        formRef.current.setFieldsValue({
          HierarchyType: HIERARCHY_TYPE,
          HospCode: '', // 初始化为空字符串
        });

        setCurrentHospCode('');
      }
    }
  }, [visible, record]);

  // 关闭时清空表单
  const handleCancel = () => {
    if (formRef.current) {
      formRef.current.resetFields();
      setCurrentHospCode('');
    }
    onCancel();
  };

  // 表单提交
  const handleSubmit = async () => {
    try {
      setSubmitLoading(true);

      // 验证表单
      const values = await formRef.current?.validateFields();
      if (!values) {
        setSubmitLoading(false);
        return;
      }

      // 获取日期范围
      const Sdate = values.dateRange[0].format('YYYY-MM-DD');
      const Edate = values.dateRange[1].format('YYYY-MM-DD');

      // 获取HospCode - 优先使用表单值，如果表单没有则使用状态保存的值
      const hospCode = values.HospCode || currentHospCode;
      console.log('提交的HospCode:', hospCode);

      if (!hospCode) {
        message.error('科室所属院区不能为空，请重新选择科室');
        setSubmitLoading(false);
        return;
      }

      // 构建提交数据 - 更优雅且鲁棒的方式
      const submitData: UpsertBedAmtParams = {
        // 1. 先以record作为基础（如果有）
        ...(record || {}),

        // 2. 设置Id（编辑模式使用原Id，新增模式使用0）
        Id: record?.Id || 0,

        // 3. 处理必须字段
        HospCode: hospCode,
        HierarchyType: HIERARCHY_TYPE,
        Sdate,
        Edate,

        // 4. 处理层级代码和名称
        HierarchyCode: values.HierarchyCode?.value || values.HierarchyCode,
        HierarchyName:
          typeof values.HierarchyCode === 'object'
            ? values.HierarchyCode.label
            : record?.HierarchyName || '',
      };

      // 5. 安全地添加所有可能的床位数量字段（只添加values中存在且字段可见的字段）

      // 过滤出可见的字段
      const visibleFieldNames = bedAmtFields
        .map((field) => field.name)
        .filter((fieldName) => getColumnDefinition(fieldName).visible);

      visibleFieldNames.forEach((fieldName) => {
        // 只有当values中存在该字段时才设置
        if (fieldName in values) {
          submitData[fieldName] = values[fieldName];
        }
      });

      console.log('提交的完整数据:', submitData);
      let res = await onSubmit(submitData);
      handleCancel();
      setSubmitLoading(false);
    } catch (error) {
      console.error('Form validation failed:', error);
      setSubmitLoading(false);
    }
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      destroyOnClose={false}
      width={800}
      maskClosable={false}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={submitLoading}
          onClick={handleSubmit}
        >
          确认
        </Button>,
      ]}
    >
      <ProFormContainer
        searchOpts={formItems}
        formRef={formRef}
        submitter={false} // 不使用内置submitter
        grid
        rowProps={{
          gutter: 16,
        }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
      />
    </Modal>
  );
};

export default EditModal;
