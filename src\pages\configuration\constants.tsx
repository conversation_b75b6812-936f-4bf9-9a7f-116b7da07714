export const ConfigurationEvents = {
  USER_MENU_EDIT: 'USER_MENU_EDIT',
  HOSP_MENU_EDIT: 'HOSP_MENU_EDIT',
  HOSP_MENU_EDIT_SUCCESS: 'HOSP_MENU_EDIT_SUCCESS',
  G<PERSON><PERSON><PERSON>L_MENU_EDIT_SUCCESS: 'G<PERSON><PERSON><PERSON><PERSON>_MENU_EDIT_SUCCESS',
  HOSP_SUBSYSTEM_EDIT: 'HOSP_SUBSYSTEM_EDIT',
  USERS_SUBSYSTEM_EDIT: 'USERS_SUBSYSTEM_EDIT',
  USERS_SUBSYSTEM_CREATE: 'USERS_SUBSYSTEM_CREATE',
  USERS_SUBSYSTEM_EDIT_PWD: 'USERS_SUBSYSTEM_EDIT_PWD',
  USERS_SUBSYSTEM_RESET_PWD: 'USERS_SUBSYSTEM_RESET_PWD',
  USERS_SUBSYSTEM_UNLOCK: 'USERS_SUBSYSTEM_UNLOCK',
  USERS_SUBSYSTEM_DELETE: 'USERS_SUBSYSTEM_DELETE',

  ROLE_MENU_EDIT: 'ROLE_MENU_EDIT',
  ROLE_SUBSYSTEM_EDIT: 'ROLE_SUBSYSTEM_EDIT',
  ROLES_SUBSYSTEM_EDIT: 'ROLES_SUBSYSTEM_EDIT',
  ROLES_SUBSYSTEM_DELETE: 'ROLES_SUBSYSTEM_DELETE',

  ENROLLMENT_EDIT: 'ENROLLMENT_EDIT',

  // base config events

  DICTIONARY_CONFIGURATION_ITEM_ADD: 'DICTIONARY_CONFIGURATION_ITEM_ADD',
  DICTIONARY_CONFIGURATION_ITEM_DELETE: 'DICTIONARY_CONFIGURATION_ITEM_DELETE',
  DICTIONARY_CONFIGURATION_TAB_CHANGE: 'DICTIONARY_CONFIGURATION_TAB_CHANGE',
  DICTIONARY_CONFIGURATION_GLOBAL_UPDATE:
    'DICTIONARY_CONFIGURATION_GLOBAL_UPDATE',
  DICTIONARY_CONFIGURATION_ITEM_EDIT: 'DICTIONARY_CONFIGURATION_ITEM_EDIT',
  DICTIONARY_CONFIGURATION_ADD: 'DICTIONARY_CONFIGURATION_ADD',
  DICTIONARY_CONFIGURATION_EDIT: 'DICTIONARY_CONFIGURATION_EDIT',

  DICTIONARY_CONFIGURATION_ITEM_RELATION_ADD:
    'DICTIONARY_CONFIGURATION_ITEM_RELATION_ADD',

  // hospital
  HOSPITAL_CONFIGURATION_DICTIONARY_EDIT:
    'HOSPITAL_CONFIGURATION_DICTIONARY_EDIT',
  HOSPITAL_CONFIGURATION_ITEM_ADD: 'HOSPITAL_CONFIGURATION_ITEM_ADD',

  CLI_DEPTS_CONFIGURATION_DICTIONARY_EDIT:
    'CLI_DEPTS_CONFIGURATION_DICTIONARY_EDIT',
  CLI_DEPTS_CONFIGURATION_ITEM_ADD: 'CLI_DEPTS_CONFIGURATION_ITEM_ADD',
  CLI_DEPTS_CONFIGURATION_ITEM_DELETE: 'CLI_DEPTS_CONFIGURATION_ITEM_DELETE',
  CLI_DEPTS_CONFIGURATION_ITEM_RELATION_ADD:
    'CLI_DEPTS_CONFIGURATION_ITEM_RELATION_ADD',

  ESCALATE_TAB_CHANGE: 'ESCALATE_TAB_CHANGE',

  ESCALATE_INSURANCE_ICDE_SELECT: 'ESCALATE_INSURANCE_ICDE_SELECT',
  ESCALATE_HQMS_ICDE_SELECT: 'ESCALATE_HQMS_ICDE_SELECT',
  ESCALATE_WT_ICDE_SELECT: 'ESCALATE_WT_ICDE_SELECT',

  ESCALATE_INSURANCE_ICDE_SELECT_NAME: 'ESCALATE_INSURANCE_ICDE_SELECT_NAME',
  ESCALATE_HQMS_ICDE_SELECT_NAME: 'ESCALATE_HQMS_ICDE_SELECT_NAME',
  ESCALATE_WT_ICDE_SELECT_NAME: 'ESCALATE_WT_ICDE_SELECT_NAME',

  ESCALATE_INSURANCE_OPER_SELECT: 'ESCALATE_INSURANCE_OPER_SELECT',
  ESCALATE_HQMS_OPER_SELECT: 'ESCALATE_HQMS_OPER_SELECT',
  ESCALATE_WT_OPER_SELECT: 'ESCALATE_WT_OPER_SELECT',

  ESCALATE_INSURANCE_OPER_SELECT_NAME: 'ESCALATE_INSURANCE_OPER_SELECT_NAME',
  ESCALATE_HQMS_OPER_SELECT_NAME: 'ESCALATE_HQMS_OPER_SELECT_NAME',
  ESCALATE_WT_OPER_SELECT_NAME: 'ESCALATE_WT_OPER_SELECT_NAME',

  ESCALATE_INSURANCE_DEPARTMENT_SELECT: 'ESCALATE_INSURANCE_DEPARTMENT_SELECT',
  ESCALATE_HQMS_DEPARTMENT_SELECT: 'ESCALATE_HQMS_DEPARTMENT_SELECT',
  ESCALATE_WT_DEPARTMENT_SELECT: 'ESCALATE_WT_DEPARTMENT_SELECT',

  ESCALATE_INSURANCE_DEPARTMENT_SELECT_NAME:
    'ESCALATE_INSURANCE_DEPARTMENT_SELECT_NAME',
  ESCALATE_HQMS_DEPARTMENT_SELECT_NAME: 'ESCALATE_HQMS_DEPARTMENT_SELECT_NAME',
  ESCALATE_WT_DEPARTMENT_SELECT_NAME: 'ESCALATE_WT_DEPARTMENT_SELECT_NAME',

  ESCALATE_INSURANCE_DICTIONARY_SELECT: 'ESCALATE_INSURANCE_DICTIONARY_SELECT',
  ESCALATE_HQMS_DICTIONARY_SELECT: 'ESCALATE_HQMS_DICTIONARY_SELECT',
  ESCALATE_WT_DICTIONARY_SELECT: 'ESCALATE_WT_DICTIONARY_SELECT',

  ESCALATE_INSURANCE_DICTIONARY_SELECT_NAME:
    'ESCALATE_INSURANCE_DICTIONARY_SELECT_NAME',
  ESCALATE_HQMS_DICTIONARY_SELECT_NAME: 'ESCALATE_HQMS_DICTIONARY_SELECT_NAME',
  ESCALATE_WT_DICTIONARY_SELECT_NAME: 'ESCALATE_WT_DICTIONARY_SELECT_NAME',

  EMPLOYEE_JOB_CLASS_SELECT: 'EMPLOYEE_JOB_CLASS_SELECT',
  EMPLOYEE_SEX_SELECT: 'EMPLOYEE_SEX_SELECT',
  EMPLOYEE_STATUS_SELECT: 'EMPLOYEE_STATUS_SELECT',
  EMPLOYEE_TITLE_SELECT: 'EMPLOYEE_TITLE_SELECT',
  EMPLOYEE_RELATED_DEPT_SELECT: 'EMPLOYEE_RELATED_DEPT_SELECT',

  CLIDEPTS_CATEGORY_TAB_SWITCH: 'CLIDEPTS_CATEGORY_TAB_SWITCH',
  CLIDEPTS_CATEGORY_ITEM_ADD: 'CLIDEPTS_CATEGORY_ITEM_ADD',
  CLIDEPTS_CATEGORY_ITEM_EDIT: 'CLIDEPTS_CATEGORY_ITEM_EDIT',

  CLIDEPTS_CATEGORY_ITEM_RELATION_ADD:
    'DICTIONARY_CONFIGURATION_ITEM_RELATION_ADD',

  MISSING_DELETE: 'MISSING_DELETE',
  MISSING_CODE_SELECT: 'MISSING_CODE_SELECT',

  DMR_TAB_CHANGE: 'DMR_TAB_CHANGE',

  DMR_INSURANCE_ICDE_SELECT: 'DMR_INSURANCE_ICDE_SELECT',
  DMR_HQMS_ICDE_SELECT: 'DMR_HQMS_ICDE_SELECT',
  DMR_WT_ICDE_SELECT: 'DMR_WT_ICDE_SELECT',

  DMR_INSURANCE_ICDE_SELECT_NAME: 'DMR_INSURANCE_ICDE_SELECT_NAME',
  DMR_HQMS_ICDE_SELECT_NAME: 'DMR_HQMS_ICDE_SELECT_NAME',
  DMR_WT_ICDE_SELECT_NAME: 'DMR_WT_ICDE_SELECT_NAME',

  DMR_INSURANCE_OPER_SELECT: 'DMR_INSURANCE_OPER_SELECT',
  DMR_HQMS_OPER_SELECT: 'DMR_HQMS_OPER_SELECT',
  DMR_WT_OPER_SELECT: 'DMR_WT_OPER_SELECT',

  DMR_INSURANCE_OPER_SELECT_NAME: 'DMR_INSURANCE_OPER_SELECT_NAME',
  DMR_HQMS_OPER_SELECT_NAME: 'DMR_HQMS_OPER_SELECT_NAME',
  DMR_WT_OPER_SELECT_NAME: 'DMR_WT_OPER_SELECT_NAME',

  DMR_INSURANCE_DEPARTMENT_SELECT: 'DMR_INSURANCE_DEPARTMENT_SELECT',
  DMR_HQMS_DEPARTMENT_SELECT: 'DMR_HQMS_DEPARTMENT_SELECT',
  DMR_WT_DEPARTMENT_SELECT: 'DMR_WT_DEPARTMENT_SELECT',

  DMR_INSURANCE_DEPARTMENT_SELECT_NAME: 'DMR_INSURANCE_DEPARTMENT_SELECT_NAME',
  DMR_HQMS_DEPARTMENT_SELECT_NAME: 'DMR_HQMS_DEPARTMENT_SELECT_NAME',
  DMR_WT_DEPARTMENT_SELECT_NAME: 'DMR_WT_DEPARTMENT_SELECT_NAME',

  DMR_INSURANCE_DICTIONARY_SELECT: 'DMR_INSURANCE_DICTIONARY_SELECT',
  DMR_HQMS_DICTIONARY_SELECT: 'DMR_HQMS_DICTIONARY_SELECT',
  DMR_WT_DICTIONARY_SELECT: 'DMR_WT_DICTIONARY_SELECT',

  DMR_INSURANCE_DICTIONARY_SELECT_NAME: 'DMR_INSURANCE_DICTIONARY_SELECT_NAME',
  DMR_HQMS_DICTIONARY_SELECT_NAME: 'DMR_HQMS_DICTIONARY_SELECT_NAME',
  DMR_WT_DICTIONARY_SELECT_NAME: 'DMR_WT_DICTIONARY_SELECT_NAME',

  DMR_ICDE_DELETE: 'DMR_ICDE_DELETE',
  DMR_OPER_DELETE: 'DMR_OPER_DELETE',
  DMR_ITEM_DELETE: 'DMR_ITEM_DELETE',

  DMR_ICDE_EDIT: 'DMR_ICDE_EDIT',
  DMR_OPER_EDIT: 'DMR_OPER_EDIT',
  DMR_ITEM_EDIT: 'DMR_ITEM_EDIT',

  DMR_DICT_SELECT_MODULE: 'DMR_DICT_SELECT_MODULE',
  DMR_DICT_GET: 'DMR_DICT_GET',
  DMR_DICT_HQMS_NAME: 'DMR_DICT_HQMS_NAME',
  DMR_DICT_INSUR_NAME: 'DMR_DICT_INSUR_NAME',
  DMR_DICT_WT_NAME: 'DMR_DICT_WT_NAME',
  DMR_DICT_DELETE: 'DMR_DICT_DELETE',

  BASE_ICDE_DELETE: 'BASE_ICDE_DELETE',
  BASE_OPER_DELETE: 'BASE_OPER_DELETE',
  BASE_CATEGORY_DELETE: 'BASE_CATEGORY_DELETE',
  BASE_DICT_DELETE: 'BASE_DICT_DELETE',

  DMR_OPER_SELECT: 'DMR_OPER_SELECT',
  DMR_OPER_SELECT_NAME: 'DMR_OPER_SELECT_NAME',

  DMR_SELECT_CODE: 'DMR_SELECT_CODE',
  DMR_SELECT_NAME: 'DMR_SELECT_NAME',

  HIERARCHY_PERFDEPT_DELETE: 'HIERARCHY_PERFDEPT_DELETE',
  MAJOR_PERFDEPT_DELETE: 'MAJOR_PERFDEPT_DELETE',
  EMPLOYEE_DELETE: 'EMPLOYEE_DELETE',

  COL_DICT_MATCH_ISVALID: 'COL_DICT_MATCH_ISVALID',
  COL_DICT_MATCH_MODULE: 'COL_DICT_MATCH_MODULE',

  HIERARCHY_BED_EDIT: 'HIERARCHY_BED_EDIT',
  HIERARCHY_BED_DELETE: 'HIERARCHY_BED_DELETE',

  // data-grid test
  FETCH_DATA_INFINITE_SCROLL: 'FETCH_DATA_INFINITE_SCROLL',
  FETCH_DATA_SEARCH_CODE: 'FETCH_DATA_SEARCH_CODE',

  OPER_SET_EDIT: 'OPER_SET_EDIT',
  OPER_SET_DELETE: 'OPER_SET_DELETE',
};
