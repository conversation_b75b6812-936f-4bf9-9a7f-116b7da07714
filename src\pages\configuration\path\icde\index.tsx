import React from 'react';

import './index.less';
import { Card, Tabs } from 'antd';
import BaseCompareCard from '@/pages/configuration/path/components/compares';
import {
  comparesIcdeColumns,
  comparesTcmColumns,
  comparesPathoColumns,
} from '@/pages/configuration/path/columns';

const tabs = [
  {
    key: 'BaseIcde',
    label: '西医诊断',
    children: (
      <BaseCompareCard
        title={'西医诊断'}
        type={'icde'}
        columns={comparesIcdeColumns}
        getApi={'GetDmrIcdeCompares'}
        updateApi={'UpsertDmrIcdeCompare'}
        deleteApi={'DeleteDmrIcdeCompare'}
        excelTemplateApi={'GetDmrIcdeCompareExcelTemplate'}
        excelFileApi={'UploadDmrIcdeCompareExcelFile'}
      />
    ),
    // <IcdeDictionary moduleGroup={'Dmr'} />,
  },
  {
    key: 'PathologyIcde',
    label: '肿瘤形态学编码',
    children: (
      <BaseCompareCard
        title={'肿瘤形态学编码'}
        type={'pathoIcde'}
        columns={comparesPathoColumns}
        getApi={'GetDmrPathologyCompares'}
        updateApi={'UpsertDmrPathologyCompare'}
        deleteApi={'DeleteDmrPathologyCompare'}
        excelTemplateApi={'GetDmrPathologyCompareExcelTemplate'}
        excelFileApi={'UploadDmrPathologyCompareExcelFile'}
      />
    ),
  },
  {
    key: 'TcmIcde',
    label: '中医诊断',
    children: (
      <BaseCompareCard
        title={'中医诊断'}
        type={'tcmIcde'}
        columns={comparesTcmColumns}
        getApi={'GetDmrTcmIcdeCompares'}
        updateApi={'UpsertDmrTcmIcdeCompare'}
        deleteApi={'DeleteDmrTcmIcdeCompare'}
        excelTemplateApi={'GetDmrTcmIcdeCompareExcelTemplate'}
        excelFileApi={'UploadDmrTcmIcdeCompareExcelFile'}
      />
    ),
    // <TcmIcdeDictionary moduleGroup={'Dmr'} />,
  },
];
const IcdeConfiguration = () => {
  const onTabChange = (key: string) => {
    console.log(key);
  };

  return (
    <div
      id={'icde-configuration-container'}
      className={'icde-configuration-container'}
    >
      <Tabs
        defaultActiveKey={tabs?.at(0)?.key}
        // type="card"
        onChange={onTabChange}
        items={tabs}
      />
    </div>
  );
};

export default IcdeConfiguration;
