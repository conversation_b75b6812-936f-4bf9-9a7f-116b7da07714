import { useState } from 'react';
import { useRequest, useModel } from 'umi';
import { Col, Row, Tabs } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import _ from 'lodash';
import { DeathBmChartSelectOptions } from './constants';
import BmTable from '@/components/BmTable/index';
import { RespVO } from '@uni/commons/src/interfaces';
import SingleColumnTable from '@/components/SingleColumnTable/index';
import { uniCommonService } from '@uni/services/src/commonService';
import Stats from '@/components/stats/index';
import { DeathNormalStat } from '@/constants';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';
import './index.less';

const MedicalQuality = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // 这边调bundleData
  const {
    data: BundledData,
    loading: getBundledDataLoading,
    run: getBundledDataReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/v2/Drgs/HospDeath/BundledDeath', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
    };
    setTableParams(params);
    getBundledDataReq(params);
  }, [dateRange, hospCodes]);

  let tabItems = [
    {
      key: 'statistic',
      label: '全院综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Drgs/HospDeath/BundledDeath`}
              trendApi={'Api/v2/Drgs/HospDeath/DeathTrend'}
              columns={DeathNormalStat}
              defaultSelectItem={'DeathCnt'}
              type="col-xl-6"
              tabKey={activeKey}
              useGlobalState
              tableParams={tableParams}
            />
          </Col>
          <SingleColumnTable
            title="全院死亡病例统计"
            args={{
              api: `Api/v2/Drgs/HospDeath/DeathByHosp`,
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="HospName"
            type="table"
            colSpan={{ span: 24 }}
            visibleValueKeys={['HospName', 'DeathCnt']}
            chart={{
              api: 'Api/v2/Drgs/HospDeath/ADrgCompositionInDeath',
              title: '死亡病例病种结构',
              type: 'bar',
              valueKeys: ['DeathCnt'],
              category: 'ADrgName',
              yAxis: '死亡人次',
              colSpan: { span: 24 },
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.HospName + '死亡人次',
                args: {
                  Sdate: dateRange?.at(0),
                  Edate: dateRange?.at(1),
                  HospCode: [record?.HospCode],
                  Dead: true,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
                dictData: dictData, // 传入
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_deptAnalysis',
      label: '科室综合分析',
      children: (
        <Row gutter={[16, 16]}>
          {/* <SingleColumnTable
            title="绩效科室死亡病例统计"
            args={{
              api: undefined,
            }}
            tableParams={tableParams}
            dictData={dictData}
            type="table"
            visibleValueKeys={['HospName', 'DeathCnt']}
          /> */}
          <SingleColumnTable
            title="临床科室死亡病例统计"
            args={{
              api: 'Api/v2/Drgs/CliDeptDeath/DeathByCliDept',
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="CliDeptName"
            type="table"
            visibleValueKeys={[
              'CliDeptName',
              'DeathCnt',
              'DeathRatio',
              'SurgeryDeathCnt',
              'SurgeryDeathRatio',
              'PeriOperDeathCnt',
              'PeriOperDeathRatio',
              'NeonateDeathCnt',
              'NeonateDeathRatio',
              'CancerDeathCnt',
              'CancerDeathRatio',
              'LowRiskDeathCnt',
              'LowRiskDeathRatio',
            ]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.CliDeptName,
                args: {
                  ...tableParams,
                  CliDepts: [record?.CliDept],
                  Dead: true,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
                dictData: dictData, // 传入
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_medTeamAnalysis',
      label: '医疗组综合分析',
      children: (
        <SingleColumnTable
          title="医疗组死亡病例统计"
          args={{
            api: 'Api/v2/Drgs/MedTeamDeath/DeathByMedTeam',
          }}
          tableParams={tableParams}
          dictData={dictData}
          category="MedTeamName"
          type="table"
          visibleValueKeys={[
            'MedTeamName',
            'DeathCnt',
            'DeathRatio',
            'SurgeryDeathCnt',
            'SurgeryDeathRatio',
            'PeriOperDeathCnt',
            'PeriOperDeathRatio',
            'NeonateDeathCnt',
            'NeonateDeathRatio',
            'CancerDeathCnt',
            'CancerDeathRatio',
            'LowRiskDeathCnt',
            'LowRiskDeathRatio',
          ]}
          colSpan={{ span: 24 }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.MedTeamName,
              args: {
                ...tableParams,
                MedTeams: [record?.MedTeam],
                Dead: true,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <SingleColumnTable
          title="医生死亡病例统计"
          args={{
            api: 'Api/v2/Drgs/DoctorDeath/DeathByDoctor',
          }}
          tableParams={tableParams}
          dictData={dictData}
          type="table"
          rowKey="CardId"
          visibleValueKeys={[
            'DoctorName',
            'DeathCnt',
            'DeathRatio',
            'SurgeryDeathCnt',
            'SurgeryDeathRatio',
            'PeriOperDeathCnt',
            'PeriOperDeathRatio',
            'NeonateDeathCnt',
            'NeonateDeathRatio',
            'CancerDeathCnt',
            'CancerDeathRatio',
            'LowRiskDeathCnt',
            'LowRiskDeathRatio',
          ]}
          colSpan={{ span: 24 }}
          select={{
            dataKey: 'DoctorType',
            valueKey: 'DoctorType',
            allowClear: false,
            defaultSelect: true,
          }}
          //   chart={{
          //     api: 'Api/Drgs/MedTeamHighRw/HighRwADrgComposition',
          //     type: 'bar',
          //     valueKeys: ['PatCnt'],
          //     category: 'ADrgName',
          //     yAxis: 'TotalCnt',
          //   }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.DoctorName,
              args: {
                ...tableParams,
                DoctorCodes: [record?.DoctorCode],
                DoctorType: (record?.DoctorType as string)?.toLocaleUpperCase(),
                Dead: true,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_BmAnalysis',
      label: '标杆值',
      children: (
        <BmTable
          tableParams={tableParams}
          api="Api/v2/Drgs/HospDeath/DeathHospBm"
          bundleData={BundledData}
          BmChartSelectOptions={DeathBmChartSelectOptions}
        />
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default MedicalQuality;
