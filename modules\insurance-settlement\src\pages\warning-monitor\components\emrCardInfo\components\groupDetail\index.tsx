import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ProDescriptions } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  Tag,
  Card,
  Badge,
  Row,
  Col,
  Tooltip,
  Modal,
  Divider,
  Alert,
  Space,
} from 'antd';
import { useModel } from '@@/plugin-model/useModel';
import { v4 as uuidv4 } from 'uuid';
import Progress from '../progress/index';
import { ExclamationCircleTwoTone } from '@ant-design/icons';
import { ChsResultColumns } from '../../columns';
import { handleChsLabel } from '@uni/utils/src/cwUtils';
import './index.less';

function isAllChinese(str) {
  // 匹配中文字符
  const reg = /^[\u4e00-\u9fa5]+$/;
  return reg.test(str);
}

const GroupDetail = (props) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  // 分组条件
  const [isModalOpen, setIsModalOpen] = useState({
    visible: false,
    record: { code: null, name: null },
  });

  const handleChsArr = (array) => {
    let cwTypes = {};
    try {
      cwTypes = JSON.parse(
        sessionStorage?.getItem('configurationInfo') ?? '{}',
      )?.chsDefs;
    } catch (e) {
      console.warn('handleChsArr JSON error', e);
    }
    return handleChsLabel(array, 'key', 'title', cwTypes);
  };

  const [finalRenderMetricSettleData, setFinalRenderMetricSettleData] =
    useState({});

  useEffect(() => {
    if (props?.metricSettlesData) {
      // let obj = _.cloneDeep(props?.metricSettlesData);
      // Object.keys(obj).forEach((k) => {
      //   obj[k] = ['BaseCwPoint'].includes(k)
      //     ? obj[k]
      //       ? parseFloat(obj[k]).toFixed(4)
      //       : obj[k]
      //     : obj[k];

      //   obj[k] = ['CwPoint', 'CwRate', 'TooHighOrLowRatio'].includes(k)
      //     ? obj[k]
      //       ? parseFloat(obj[k]).toFixed(6)
      //       : obj[k]
      //     : obj[k];
      // });

      setFinalRenderMetricSettleData(props?.metricSettlesData);
    }
  }, [props?.metricSettlesData]);

  console.log('props?.metricSettlesData', props?.type);
  return (
    <>
      <Card
        size="small"
        title={
          <div>
            <Tooltip
              title={`${props?.metricSettlesData?.DrgCode}${props?.metricSettlesData?.DrgName}`}
            >
              <b className="mr-1">
                {props?.metricSettlesData?.DrgCode}&nbsp;&nbsp;
                {props?.metricSettlesData?.DrgName}
              </b>
            </Tooltip>
            <Tag
              color={
                props?.metricSettlesData?.AbnFeeType === '3' ||
                props?.metricSettlesData?.AbnFeeType === '高倍率'
                  ? 'red'
                  : props?.metricSettlesData?.AbnFeeType === '4' ||
                    props?.metricSettlesData?.AbnFeeType === '低倍率'
                  ? 'gold'
                  : props?.metricSettlesData?.AbnFeeType === '0' ||
                    props?.metricSettlesData?.AbnFeeType === '其他'
                  ? 'orange'
                  : 'green'
              }
            >
              {isAllChinese(props?.metricSettlesData?.AbnFeeType)
                ? props?.metricSettlesData?.AbnFeeType
                : dictData?.Dmr?.AbnFeeType?.find(
                    (a) => a?.Code === props?.metricSettlesData?.AbnFeeType,
                  )?.Name}
            </Tag>
            {props?.metricSettlesData &&
              !props?.metricSettlesData?.IsKeySpecDisease &&
              !props?.metricSettlesData?.IsPrimaryDisease && (
                <Tag>普通病例</Tag>
              )}
            {props?.metricSettlesData &&
              props?.metricSettlesData?.IsKeySpecDisease &&
              !props?.metricSettlesData?.IsPrimaryDisease && (
                <Tag>基层病种病例</Tag>
              )}
            {props?.metricSettlesData &&
              !props?.metricSettlesData?.IsKeySpecDisease &&
              props?.metricSettlesData?.IsPrimaryDisease && (
                <Tag>重点专科病种病例</Tag>
              )}
            {props?.metricSettlesData &&
              props?.metricSettlesData?.IsKeySpecDisease &&
              props?.metricSettlesData?.IsPrimaryDisease && (
                <Tag>基层病种重点专科病种病例</Tag>
              )}
          </div>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <ProDescriptions
              size="small"
              column={{ xs: 1, sm: 1, md: 2, lg: 4, xl: 4, xxl: 6 }}
              dataSource={finalRenderMetricSettleData}
              columns={ChsResultColumns(props?.type === 'dip' ? 'DIP' : 'DRG')}
            />
          </Col>
          <Col span={24}>
            <Progress data={props?.metricSettlesData || {}} />
          </Col>
        </Row>
      </Card>
    </>
  );
};
export default GroupDetail;
