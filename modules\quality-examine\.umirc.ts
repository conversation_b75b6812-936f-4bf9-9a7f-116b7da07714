import { name } from './package.json';
import { slaveCommonConfig } from '../../.umirc.commom';

export default {
  base: name,
  publicPath: '/qualityExamine/',
  outputPath: '../../dist/qualityExamine',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  qiankun: {
    // master: {
    //   // 注册子应用信息
    //   apps: [
    //     {
    //       name: 'report',
    //       entry:
    //         process.env.NODE_ENV === 'production'
    //           ? '/report/'
    //           : '//localhost:8006',
    //     },
    //   ],
    // },
    slave: {},
  },

  plugins: [
    // require.resolve('@uni/commons/src/plugins/qiankun.ts'),
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/reviewer',
    },
    {
      path: '/reviewer',
      exact: true,
      component: '@/pages/review/reviewer/index',
    },
    {
      path: '/auditee',
      exact: true,
      component: '@/pages/review/auditee/index',
    },
    {
      path: '/management',
      exact: true,
      component: '@/pages/review/management/index',
    },
    {
      path: '/analysis',
      exact: true,
      component: '@/pages/review/analysis/index',
    },
    // {
    //   path: '/selectiveReview',
    //   exact: true,
    //   component: '@/pages/review/selectiveReview/index',
    // },
  ],

  proxy: {
    // 同cra的setupProxy,代理中转实现dev版本的跨域
    '/common': {
      target: 'http://172.16.3.152:5180',
      changeOrigin: true,
      pathRewrite: { '^/common': '' },
      secure: false, // https的dev后端的话需要配
    },

    '/metaData': {
      target: 'http://172.16.3.152:5180',
      changeOrigin: true,
      pathRewrite: { '^/metaData': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
};
