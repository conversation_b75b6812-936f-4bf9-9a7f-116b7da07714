import { Card, Col, Row } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { v4 as uuidv4 } from 'uuid';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import UniEcharts from '@uni/components/src/echarts';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import { StatisticCard } from '@uni/components/src/index';
import { ApplicablePieOption } from '../../../chart.opts';
import './index.less';
const { Statistic, Divider } = StatisticCard;
import DetailsBtn from '@uni/components/src/details-btn';
import IconBtn from '@uni/components/src/iconBtn';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import DetailTableModal from '@uni/components/src/detailed-table-modal';

const GroupCntStats = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, insurType } = globalState?.searchParams;

  // 适用/不适用
  const [applicablePieData, setApplicablePieData] = useState([]);
  const [notApplicablePieData, setNotApplicablePieData] = useState([]);

  useEffect(() => {
    if (dateRange?.length) {
      let tableParams = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
      };
      getSettleGrpStatsOfHospReq(tableParams);
    }
  }, [dateRange, hospCodes]);

  // 入组概况
  const {
    data: settleGrpStatsOfHospData,
    loading: getSettleGrpStatsOfHospLoading,
    run: getSettleGrpStatsOfHospReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestDipSettleStats/SettleGrpStatsOfHosp`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (Object.keys(res?.data?.Stats[0])?.length) {
            let data = res?.data?.Stats[0];
            setApplicablePieData([
              {
                name: '适用病例例数',
                data: 'ApplicablePatCnt',
                value: data?.ApplicablePatCnt || 0,
                itemStyle: {
                  color: '#1464f8',
                },
              },
              {
                name: '不适用病例例数',
                data: 'NotApplicablePatCnt',
                value: data?.NotApplicablePatCnt || 0,
                itemStyle: {
                  color: '#eee',
                },
              },
            ]);
            setNotApplicablePieData([
              {
                name: '不适用病例例数',
                data: 'NotApplicablePatCnt',
                value: data?.NotApplicablePatCnt || 0,
                itemStyle: {
                  color: '#eb5757',
                },
              },
              {
                name: '适用病例例数',
                data: 'ApplicablePatCnt',
                value: data?.ApplicablePatCnt || 0,
                itemStyle: {
                  color: '#eee',
                },
              },
            ]);
            return data;
          } else return [];
        }
      },
    },
  );

  return (
    <>
      <Col xs={24} sm={24} md={24} lg={24} xl={17}>
        <StatisticCard.Group direction={'row'} className="drg-statistic-card">
          <StatisticCard
            statistic={{
              title: '出院结算总例数',
              value:
                applicablePieData[0]?.value + applicablePieData[1]?.value ||
                '-',
            }}
          />
          <Divider type={'vertical'} />
          <UniEcharts
            height={120}
            elementId="Pie"
            loading={getSettleGrpStatsOfHospLoading || false}
            options={
              (applicablePieData.length &&
                ApplicablePieOption(applicablePieData, 'name')) ||
              {}
            }
          ></UniEcharts>
          <StatisticCard
            statistic={{
              title: '适用病例',
              value: `${settleGrpStatsOfHospData?.ApplicablePatCnt || '-'}`,
              description: (
                <Statistic
                  title="总费用"
                  value={`${valueNullOrUndefinedReturnDash(
                    settleGrpStatsOfHospData?.ApplicableTotalFee,
                    'Currency',
                  )}`}
                />
              ),
            }}
          />
          <UniEcharts
            height={120}
            elementId="Pie"
            loading={getSettleGrpStatsOfHospLoading || false}
            options={
              (notApplicablePieData.length &&
                ApplicablePieOption(notApplicablePieData, 'name')) ||
              {}
            }
          ></UniEcharts>
          <StatisticCard
            statistic={{
              title: '不适用病例',
              value: `${settleGrpStatsOfHospData?.NotApplicablePatCnt || '-'}`,
              prefix: (
                <>
                  {settleGrpStatsOfHospData?.NotApplicablePatCnt > 0 && (
                    <IconBtn
                      type="details"
                      onClick={(e) => {
                        e.stopPropagation();
                        Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                          id: 'dip-pay-settle-grp-stats-not-applicable', // 要匹配到对应的DetailTableModal id
                          title: `不适用病例`,
                          args: {
                            Sdate: dateRange?.at(0),
                            Edate: dateRange?.at(1),
                            HospCode: hospCodes,
                            Applicable: false,
                          },
                          type: 'dip',
                          detailsUrl:
                            'FundSupervise/LatestDipSettleStats/SettleDetails',
                          dictData: globalState?.dictData, // 传入
                        });
                      }}
                      // detailsUrl={
                      //   'FundSupervise/LatestDipSettleStats/SettleDetails'
                      // }
                      // detailType="chsCardInfo"
                      // type="dip"
                    />
                    // <DetailsBtn
                    //   title={'不适用病例'}
                    //   args={{
                    //     Sdate: dateRange?.at(0),
                    //     Edate: dateRange?.at(1),
                    //     HospCode: hospCodes,
                    //     Applicable: false,
                    //   }}
                    //   detailsUrl={
                    //     'FundSupervise/LatestDipSettleStats/SettleDetails'
                    //   }
                    //   type={'chs'}
                    // />
                  )}
                  <DetailTableModal id="dip-pay-settle-grp-stats-not-applicable" />
                </>
              ),
              description: (
                <Statistic
                  title="总费用"
                  value={`${valueNullOrUndefinedReturnDash(
                    settleGrpStatsOfHospData?.NotApplicableTotalFee,
                    'Currency',
                  )}`}
                />
              ),
            }}
          />
        </StatisticCard.Group>
      </Col>
      <Col xs={24} sm={24} md={24} lg={24} xl={7}>
        <StatisticCard.Group direction={'row'} className="drg-statistic-card">
          <StatisticCard
            statistic={{
              title: '入组率',
              value: `${valueNullOrUndefinedReturnDash(
                settleGrpStatsOfHospData?.NormalGrpPatCntRatio,
                'Percent',
              )}`,
              description: (
                <Statistic
                  title="入组人次"
                  value={`${settleGrpStatsOfHospData?.NormalGrpPatCnt || '-'}`}
                />
              ),
            }}
          />
          <Divider type={'vertical'} />
          <StatisticCard
            statistic={{
              title: '未入组人次',
              value: `${settleGrpStatsOfHospData?.AbnormalGrpPatCnt || '-'}`,
              prefix: (
                <>
                  {settleGrpStatsOfHospData?.AbnormalGrpPatCnt > 0 && (
                    <IconBtn
                      type="details"
                      onClick={(e) => {
                        e.stopPropagation();
                        Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                          id: 'dip-pay-settle-grp-stats-abnormal-grp', // 要匹配到对应的DetailTableModal id
                          title: `未入组人次`,
                          args: {
                            Sdate: dateRange?.at(0),
                            Edate: dateRange?.at(1),
                            HospCode: hospCodes,
                            GroupResultType: 'Abnormal',
                          },
                          type: 'dip',
                          detailsUrl:
                            'FundSupervise/LatestDipSettleStats/SettleDetails',
                          dictData: globalState?.dictData, // 传入
                        });
                      }}
                      // detailsUrl={
                      //   'FundSupervise/LatestDipSettleStats/SettleDetails'
                      // }
                      // detailType="chsCardInfo"
                      // type="dip"
                    />
                    // <DetailsBtn
                    //   title={'未入组人次'}
                    //   args={{
                    //     Sdate: dateRange?.at(0),
                    //     Edate: dateRange?.at(1),
                    //     HospCode: hospCodes,
                    //     GroupResultType: 'Abnormal',
                    //   }}
                    //   detailsUrl={
                    //     'FundSupervise/LatestDipSettleStats/SettleDetails'
                    //   }
                    //   type={'chs'}
                    // />
                  )}
                  <DetailTableModal id="dip-pay-settle-grp-stats-abnormal-grp" />
                </>
              ),
            }}
          />
        </StatisticCard.Group>
      </Col>
    </>
  );
};

export default GroupCntStats;
