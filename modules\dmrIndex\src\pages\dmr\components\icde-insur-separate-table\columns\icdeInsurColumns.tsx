import { Sorta<PERSON><PERSON>andle } from 'react-sortable-hoc';
import React, { useEffect, useState } from 'react';
import {
  PlusOutlined,
  DeleteOutlined,
  AlertOutlined,
  MenuOutlined,
  PlusCircleTwoTone,
  InfoCircleTwoTone,
} from '@ant-design/icons';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { Checkbox, Input, Popconfirm, Tag, Tooltip, Button } from 'antd';
import IcdeSelect from '@/pages/dmr/components/icde-select';
import OperationSelect from '@/pages/dmr/components/oper-select';
import dayjs from 'dayjs';
import {
  IcdeOperationInputSelector,
  IcdeFieldInput,
  OperationFieldInput,
  IcdeOperationReadonlyItem,
  IcdeExtraTagsItem,
  OperationExtraTagsItem,
  icdeExtraMap,
  OperIcdeExtraMapItem,
  operationExtraMap,
  hqmsDegreeMap,
  drgsDegreeMap,
  PathologyIcdeFieldInput,
  IcuDurationFieldInput,
  IcdeOperCheckbox,
} from '@uni/grid/src/components/icde-oper-input/input';
import { RowSelectionCheckbox } from '@uni/grid/src/components/row-selection';
import { RowSelectionHeader } from '@uni/grid/src/components/row-selection-header';
import { BatchDeleteButton } from '@/pages/dmr/components/batch-delete-button';
import DateSelect from '@uni/grid/src/components/date-select';
import IconBtn from '@uni/components/src/iconBtn';
import RestrictInputNumber from '@uni/grid/src/components/restrict-number';
import { employeeDataSourceProcessor } from '@/pages/dmr/utils';
import { getArrowUpDownEventKey } from '@uni/grid/src/utils';

const tableOnlyAddIconTrigger =
  (window as any).externalConfig?.['dmr']?.tableOnlyAddIconTrigger ?? false;

const enableTableDropdownNG =
  (window as any).externalConfig?.['dmr']?.enableTableDropdownNG ?? false;

const icdeOperRowSelection =
  (window as any).externalConfig?.['dmr']?.icdeOperRowSelection ?? false;

const nonAddCell = (record, index) => {
  if (record?.id === 'ADD') {
    return {
      colSpan: 0,
    };
  }

  return {};
};

const DragHandler = (node) => {
  return true
    ? SortableHandle(() => <div className={'grab-handle'}>{node}</div>)
    : () => node;
};

// 医保表格专用的列定义（只包含医保相关列和关联显示列）
export const icdeInsurColumns = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'InsurExtra',
    title: '操作',
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_ICDE_INSUR_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-diagnosisInsurTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_ICDE_INSUR_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      }

      return null; // 医保表格不需要额外标签
    },
    onCell: nonAddCell,
  },
  // 行选择列
  ...(icdeOperRowSelection
    ? [
        {
          key: 'RowSelection',
          dataIndex: 'RowSelection',
          title: (
            <RowSelectionHeader
              tableId="diagnosisInsurTable"
              batchDeleteButton={
                <BatchDeleteButton tableId="diagnosisInsurTable" />
              }
            />
          ),
          fixed: 'left',
          visible: true,
          align: 'center',
          width: 50,
          readonly: false,
          renderColumnFormItem: (node, record, index) => {
            if (record?.id === 'ADD') {
              return null;
            }
            return (
              <RowSelectionCheckbox
                record={record}
                index={index}
                tableId="diagnosisInsurTable"
              />
            );
          },
          onCell: nonAddCell,
        },
      ]
    : []),
  // 序号列
  {
    key: 'LineNumber',
    dataIndex: 'LineNumber',
    title: '序号',
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 60,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }
      return <span>{index + 1}</span>;
    },
    onCell: nonAddCell,
  },
  // 诊断编码列（只读，用于显示关联信息）
  {
    key: 'IcdeCode',
    dataIndex: 'IcdeCode',
    title: '诊断编码',
    visible: true,
    align: 'left',
    width: 120,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <IcdeOperationReadonlyItem
          record={record}
          index={index}
          fieldKey="IcdeCode"
        />
      );
    },
    onCell: nonAddCell,
  },
  // 诊断名称列（只读，用于显示关联信息）
  {
    key: 'IcdeName',
    dataIndex: 'IcdeName',
    title: '诊断名称',
    visible: true,
    align: 'left',
    width: 200,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <IcdeOperationReadonlyItem
          record={record}
          index={index}
          fieldKey="IcdeName"
        />
      );
    },
    onCell: nonAddCell,
  },
  // 医保编码列（可编辑）
  {
    key: 'InsurCode',
    dataIndex: 'InsurCode',
    title: '医保编码',
    visible: true,
    align: 'left',
    width: 120,
    readonly: false,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <IcdeSelect
          record={record}
          index={index}
          tableId="diagnosisInsurTable"
          fieldKey="InsurCode"
          onSelectAdd={() => {
            Emitter.emit(EventConstant.DMR_ICDE_INSUR_SELECT_ADD, {
              record,
              index,
            });
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  // 医保名称列（可编辑）
  {
    key: 'InsurName',
    dataIndex: 'InsurName',
    title: '医保名称',
    visible: true,
    align: 'left',
    width: 200,
    readonly: false,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <IcdeFieldInput
          record={record}
          index={index}
          tableId="diagnosisInsurTable"
          fieldKey="InsurName"
          onInputAdd={() => {
            Emitter.emit(EventConstant.DMR_ICDE_INSUR_INPUT_ADD, {
              record,
              index,
            });
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  // 操作列
  {
    key: 'ACTIONS',
    dataIndex: 'ACTIONS',
    title: '操作',
    fixed: 'right',
    visible: true,
    align: 'center',
    width: 100,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return null;
      }

      return (
        <div className="table-actions">
          <IconBtn
            customIcon={<PlusOutlined />}
            size="small"
            title="复制"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_ICDE_INSUR_COPY, {
                index,
                record,
              });
            }}
          />
          <Popconfirm
            title="确定删除这条医保记录吗？"
            onConfirm={() => {
              Emitter.emit(EventConstant.DMR_ICDE_INSUR_DELETE, index);
            }}
            okText="确定"
            cancelText="取消"
          >
            <IconBtn
              customIcon={<DeleteOutlined />}
              size="small"
              title="删除"
              danger
            />
          </Popconfirm>
        </div>
      );
    },
    onCell: nonAddCell,
  },
];
