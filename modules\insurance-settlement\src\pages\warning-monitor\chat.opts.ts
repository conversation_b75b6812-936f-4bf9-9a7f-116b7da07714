import { valueNullOrUndefinedReturnDash } from '@/utils/utils';
import theme from '@uni/components/src/echarts/themes/themeBlueYellow';
import _ from 'lodash';

const hospDeptStatsBar = (data) => {
  if (_.isEmpty(data)) {
    return {};
  }
  console.log(data);
  let option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    color: '#3d87ff',
    legend: {},
    dataset: {
      source: data?.filter((d) => d?.value > 0),
    },
    grid: { left: 10, right: 10, top: 5, bottom: 0 },
    xAxis: {
      type: 'category',
      axisLabel: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        show: false,
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: 26,
        label: {
          show: false,
        },
        encode: {
          x: 'name',
          y: 'value',
        },
        // itemStyle: {
        //   color: function (param) {
        //     return param?.value?.color;
        //   },
        // },
      },
    ],
  };

  return option;
};

const hospDeptWarningCntBar = (data) => {
  if (_.isEmpty(data)) {
    return {};
  }

  let option = {
    legend: {},
    dataset: {
      source: [
        {
          name: '#3d87ff|提示性',
          key: 'IndicativeWarningLevelPatCnt',
          value: data?.IndicativeWarningLevelPatCnt ?? 0,
          color: '#3d87ff',
          amount: `#3d87ff|${data?.IndicativeWarningLevelPatCnt ?? 0}例`,
        },
        {
          name: '#ffc300|黄色异常',
          key: 'YellowWarningLevelPatCnt',
          value: data?.YellowWarningLevelPatCnt ?? 0,
          color: '#ffc300',
          amount: `#ffc300|${data?.YellowWarningLevelPatCnt ?? 0}例`,
        },
        {
          name: '#fa8c16|橙色异常',
          key: 'OrangeWarningLevelPatCnt',
          value: data?.OrangeWarningLevelPatCnt ?? 0,
          color: '#fa8c16',
          amount: `#fa8c16|${data?.OrangeWarningLevelPatCnt ?? 0}例`,
        },
        {
          name: '#eb5757|红色异常',
          key: 'RedWarningLevelPatCnt',
          value: data?.RedWarningLevelPatCnt ?? 0,
          color: '#eb5757',
          amount: `#eb5757|${data?.RedWarningLevelPatCnt ?? 0}例`,
        },
      ],
    },
    grid: { left: 60, right: 60, top: 5, bottom: 0 },
    xAxis: {
      type: 'value',
      axisLabel: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'category',
        axisLabel: {
          show: true,
          align: 'right',
          formatter: (value, index) => {
            return value?.split('|')?.at(1);
          },
          color: function (value, index) {
            return value?.split('|')?.at(0);
          },
        },
      },
      {
        type: 'category',
        offset: 45,
        axisLabel: {
          show: true,
          align: 'right',
          formatter: (value, index) => {
            return value?.split('|')?.at(1);
          },
          color: function (value, index) {
            return value?.split('|')?.at(0);
          },
        },
      },
    ],
    series: [
      {
        type: 'bar',
        barWidth: 22,
        barMaxHeight: 10,
        label: {
          show: false,
        },
        encode: {
          x: 'value',
          y: 'name',
        },
        itemStyle: {
          color: function (param) {
            return param?.value?.color;
          },
        },
      },
      {
        type: 'bar',
        barWidth: 1,
        yAxisIndex: 1,
        encode: {
          y: 'amount',
        },
        itemStyle: {
          color: function (param) {
            return param?.value?.color;
          },
        },
      },
    ],
  };

  return option;
};

const hospDeptWarningCntPie = (data) => {
  if (_.isEmpty(data)) {
    return {};
  }

  const option = {
    baseOption: {
      legend: {
        orient: 'vertical',
        right: 10,
        top: '20%',
        bottom: 20,
        icon: 'circle',
        formatter: (name) => {
          let item = data.find((d) => d.name === name);
          return `{name|${name}}{cnt|${
            item?.value
          }}{unit|例}{totalFee|${valueNullOrUndefinedReturnDash(
            item?.totalFee,
            'Currency',
          )}}`;
        },
        textStyle: {
          // width: 250,
          rich: {
            name: {
              color: '#000',
              width: 50,
              padding: [0, 10, 0, 0],
            },
            cnt: {
              width: 80,
              padding: [0, 0, 0, 10],
              align: 'right',
              fontWeight: 600,
              fontSize: '16px',
            },
            unit: {
              color: '#808080',
              fontSize: '12px',
              align: 'right',
              padding: [0, 15, 0, 0],
            },
            totalFee: {
              color: '#808080',
              align: 'right',
              width: 100,
            },
          },
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          console.log(params);
          return `${params?.name} : ${
            params?.seriesIndex === 0
              ? valueNullOrUndefinedReturnDash(params?.value, 'percent')
              : params?.value
          }`;
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '65%'],
          center: ['15%', '50%'],
          // label: {
          //   show: true,
          //   formatter: (params) => {
          //     return `${params?.name} ${valueNullOrUndefinedReturnDash(
          //       params?.value,
          //       'percent',
          //     )}`;
          //   },
          // },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 1,
          },
          data: data,
        },
      ],
    },
  };
  return option;
};

export { hospDeptStatsBar, hospDeptWarningCntBar, hospDeptWarningCntPie };
