import _, { keyBy, isArray, isObject } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
/* normal */
export function isString(str) {
  return str instanceof String || typeof str == 'string';
}
export function isNumber(obj) {
  return obj === +obj;
}

export type RespType<T = any> = {
  code?: number;
  requestUrl?: string;
  statusCode?: number;
  message?: string;
  data?: T;
};
// api err
const isRespErr = (res: RespType) => {
  if (!!res?.code === false) return false;
  let { code, statusCode } = res;
  if (code === 0 && statusCode === 200) {
    return false;
  }
  return true;
};

/** 对umi-request进行formdata解析时候数组的处理 */
// data: 要传入的对象，类型obj；keys：要做处理的key名称，就是对象里面是数组的那个键的string
export function forUmiRequestFormData(data, keys: string[] = []) {
  if (Array.isArray(data)) {
    let tempC = data.map((d, i) => ({ [i]: d }));
    return tempC;
  } else {
    let tempD = Object.assign({}, data);
    if (!keys.length) {
      Object.keys(data).forEach((d) => {
        if (Array.isArray(data[d])) {
          tempD[d] = data[d].map((v, i) => ({ [i]: v }));
        }
      });
    } else {
      keys.forEach((key) => {
        if (tempD[key]?.length > 0) {
          tempD[key] = tempD[key].map((data, index) => {
            return {
              ...data,
              umiRequestIndex: index,
            };
          });
        }
      });
      keys.forEach((key) => {
        if (tempD[key]?.length > 0) {
          let result = keyBy(tempD[key], 'umiRequestIndex');
          tempD[key] = result;
          // // console.log(result)
        }
      });
    }

    return tempD;
  }
}
// 删除null或者undefined或者空对象,或指定的键
export function removePropertyOfNull(obj: object, strs: string[] = []) {
  if (!obj) return null;
  Object.keys(obj).forEach((item) => {
    if (strs.includes(item)) {
      delete obj[item];
    }
    if (isObject(obj[item]) && Object.keys(obj[item]).length > 0) {
      removePropertyOfNull(obj[item]);
      if (Object.keys(obj[item]).length === 0) {
        delete obj[item];
      }
    } else {
      if (
        obj[item] === null ||
        obj[item] === undefined ||
        (isObject(obj[item]) && Object.keys(obj[item]).length === 0)
      ) {
        delete obj[item];
      }
    }
  });
  return obj;
}

/** 平铺数组转成树 */
// arr, 找父节点时用的key1，定义匹配key1时使用的父节点key2
export function flatArr2Tree<T>(
  arr: T[],
  parentCode: string,
  parentKey: string,
  parentType?: string,
  typeValue?: string | number | boolean,
): T[] {
  let dataMap = {};
  // 先处理把cateCode变成新对象的键
  arr.forEach((node) => {
    if (!parentType && node[parentKey]) {
      dataMap[node[parentKey]] = node;
    } else {
      if (node[parentType] === typeValue && node[parentKey]) {
        dataMap[node[parentKey]] = node;
      }
    }
  });
  let result = [];
  arr.forEach((node) => {
    // 在dataMap找父节点
    var parent = dataMap[node[parentCode]];
    if (parent) {
      // 没有children就建一个
      (parent.children || (parent.children = []))
        // 插入数据
        .push(node);
    } else {
      // 没有父节点就是根结点
      result.push(node);
    }
  });
  return result;
}

/* tree数据类型，过滤没有children的目录 */
export function treeFilterNoChildren<T extends { children?: T[] }>(
  targetArr: T[],
  childSpecialKey: string,
  childSpecialKeyValue: string | number | boolean = null,
) {
  let result: T[] = [];
  targetArr.forEach((item, index, arr) => {
    let temp: T | T[] = null;

    if (
      item.children &&
      item.children.length > 0 &&
      item[childSpecialKey] !== childSpecialKeyValue
    ) {
      // 一定要是目录并且有children 才进递归循环
      temp = treeFilterNoChildren(
        item.children,
        childSpecialKey,
        childSpecialKeyValue,
      );
      if (temp.length === 0) {
        temp = null;
      }
    } else {
      if (item[childSpecialKey] === childSpecialKeyValue) {
        // 是项目，非目录
        temp = item;
      }
    }

    // 判读完之后判断temp
    if (isArray(temp)) {
      // 说明从递归来的，是个数组
      if ((temp as T[]).length > 0) {
        result.push({
          ...item,
          children: temp,
        });
      }
    } else if (JSON.stringify(temp) !== 'null') {
      // 说明是当前循环，是item
      result.push(temp as T);
    }
  });
  return result;
}

/* 根据给定key将数组拆分成多个数组 */
export function separateArrByKey<T>(targetArr: T[], key: string) {
  let dataArr: { [str: string]: any; List: T[] }[] = [];
  targetArr.map((d: any) => {
    if (dataArr.length == 0) {
      dataArr.push({ [key]: d[key], List: [d] });
    } else {
      let res = dataArr.some((item: any) => {
        // 有就添加到当前项
        if (item[key] == d[key]) {
          item.List.push(d);
          return true;
        }
      });
      if (!res) {
        // 没找到添加一个新对象
        dataArr.push({ [key]: d[key], List: [d] });
      }
    }
  });
  return dataArr;
}

export function flatDeepSource<T extends { children?: T[] }>(
  arr: T[],
  parentUid: string | number = null,
) {
  let result: (T & { uid: string; parentUid?: string | number })[] = [];
  arr.forEach((item) => {
    var res = JSON.parse(JSON.stringify(item)); // 先克隆一份数据作为第一层级的填充
    delete res['children'];
    let uid = uuidv4();
    result.push({ ...res, uid, parentUid });
    if (item.children instanceof Array && item.children.length > 0) {
      // 如果当前child为数组并且长度大于0，才可进入flat()方法
      result = result.concat(flatDeepSource(item.children, uid));
    }
  });
  return result;
}

/**
 * dataTable-type filter && sorter for back end
 */
export function dataTableTypeFilterSorter<
  T extends {
    needClear?: boolean;
    backendSorterObj?: { data: string; dir: 'asc' | 'desc' }[];
    backendFilterObj?: { data: string; searchValue: string }[];
  },
>(params: T, sorters, filters) {
  let finalNeedClear = params?.needClear || false;
  let needSetSorter = false,
    needSetFilter = false;
  // -- start: 处理后端sorter  --
  let sorterPayload: any[] = [];
  if (sorters) {
    // 处理成dataTable需要的格式: https://datatables.net/manual/server-side
    Object.keys(sorters).map((key) => {
      sorterPayload.push({
        data: key,
        dir: sorters[key] === 'ascend' ? 'asc' : 'desc',
      });
    });
  }
  // 在有sorter的情况下，每次切换页面也会携带sorter参数
  // 在以下情况需要清空重新调接口
  if (
    params?.backendSorterObj?.length !== 0 &&
    params?.backendSorterObj?.length === sorterPayload.length
  ) {
    for (let i = 0; i < sorterPayload.length; i++) {
      if (
        !params?.backendSorterObj?.find(
          (o) =>
            o?.data === sorterPayload[i]?.data &&
            o?.dir === sorterPayload[i]?.dir,
        )
      ) {
        finalNeedClear = true;
        needSetSorter = true;
        break;
      }
    }
  } else if (params?.backendSorterObj?.length !== sorterPayload.length) {
    finalNeedClear = true;
    needSetSorter = true;
  }
  // -- end: 处理后端sorter  --
  // -- start: 处理后端filter --
  let filterPayload: any[] = [];
  if (filters) {
    // 处理成dataTable需要的格式: https://datatables.net/manual/server-side
    Object.keys(filters).forEach((key) => {
      if (filters[key]) {
        filterPayload.push({
          data: key,
          searchValue: filters[key][0] || null,
        });
      }
    });
  }
  // 在有filter的情况下，每次切换页面也会携带filter参数
  // 在以下情况需要清空重新调接口
  if (
    params?.backendFilterObj?.length !== 0 &&
    params?.backendFilterObj?.length === filterPayload.length
  ) {
    for (let i = 0; i < filterPayload.length; i++) {
      if (
        !params?.backendFilterObj?.find(
          (o) =>
            o?.data === filterPayload[i]?.data &&
            o?.searchValue === filterPayload[i]?.searchValue,
        )
      ) {
        finalNeedClear = true;
        needSetFilter = true;
        break;
      }
    }
  } else if (params?.backendFilterObj?.length !== filterPayload.length) {
    finalNeedClear = true;
    needSetFilter = true;
  }
  return {
    forPayloadParams: {
      finalNeedClear,
      sorterPayload,
      filterPayload,
    },
    needSetFilter,
    needSetSorter,
  };
}

/**
 * columns handler for ysh's columnapi
 */
export function handleColumnsYsh(response, tableEditable = false) {
  if (!response) return null;
  if (!isUmiRequestResponseErr(response)) {
    let { properties } = response;
    if (!properties) return null;
    let tempFilters = [];
    let tempSorters = [];
    let cols = Object.keys(properties).map((d) => {
      let temp2 = {};
      if (properties[d]?.enum) {
        let arr = properties[d].enum;
        if (isArray(arr[0])) {
          let temp = arr[0]?.map((v, i) => ({
            value: v,
            name: arr[1][i],
            keyValue: `${arr[1][i]}:${v}`,
          }));
          temp.forEach((d) => {
            temp2[d.value] = { text: d.name };
          });
          tempFilters.push({
            data: d,
            filterType: 'filter',
            valueEnum: temp2,
          });
        }
      }
      if (properties[d].sortby) {
        tempSorters.push({
          data: d,
          sorterData: properties[d].sortby,
        });
      }
      return {
        data: d,
        dataIndex: d,
        dataType: properties[d].format || properties[d].type,
        title: properties[d].description || d,
        visible: d === 'id' ? false : properties[d].visible,
        orderable: properties[d].sortby ? true : false,
        align: properties[d].enum ? 'center' : undefined,
        formItemProps:
          tableEditable &&
          !properties[d].nullable &&
          properties[d].visible &&
          properties[d].type !== 'boolean'
            ? {
                rules: [
                  {
                    required: true,
                    whitespace: true,
                    message: '此项是必填项',
                  },
                ],
              }
            : null,
      };
    });

    return {
      columns: cols,
      filters: tempFilters,
      sorters: tempSorters,
    };
  } else {
    return null;
  }
}

/**
 * 临时用来处理clidept的传后端的数据处理 */
export const tempHandleCliDepts4Back = (apiKeyValue, postValues, HCM) => {
  let needFeeItemOne = apiKeyValue.headerItems.filter(
    (d) => d === 'FeeItemCodeOne',
  );
  let needHospCodeOne = apiKeyValue.headerItems.filter(
    (d) => d === 'HospCodeOne',
  );
  let needCliDeptsDependsHospCodeOne = apiKeyValue.headerItems.filter(
    (d) => d === 'CliDeptsDependsHospCodeOne',
  );
  let v;
  v = _.cloneDeep(postValues);

  if (needHospCodeOne.length) {
    v['HospCode'] = [postValues[needHospCodeOne[0]]] || [];
  }
  if (needCliDeptsDependsHospCodeOne.length) {
    v['CliDepts'] = postValues['CliDeptsDependsHospCodeOne'] || [];
  }

  // if (postValues['FeeItemCode']) {
  //   v['FeeItemCode'] =
  //     postValues['FeeItemCode']?.map(d => d.split(':')[1]) || [];
  // }
  if (needFeeItemOne.length) {
    v['FeeItemCode'] = postValues['FeeItemCodeOne'] || [];
  }
  // console.log(v);
  v = _.omit(v, [
    'HospCodeOne',
    'FeeItemCodeOne',
    'CliDeptsDependsHospCodeOne',
  ]);
  return v;
};

/**
 * 递归找到第一个包含的数据的树
 *  */
export const recurrenceFindOne = (target, targetKey, arr) => {
  let result = null;
  let isFind = false;
  arr.forEach((d) => {
    if (d[targetKey] === target) {
      isFind = true;
      result = d;
    }
    if (d.children && !isFind) {
      let temp = recurrenceFindOne(target, targetKey, d.children);
      if (temp.isFind) {
      }
      result = result.concat();
    }
  });
  return { result, isFind };
};

/**
 * 响应异常
 */
export class ResponseError extends Error {
  data;
  response;
  request;
  type;
  constructor(response, text, data, request, type = 'ResponseError') {
    super(text || response.statusText);
    this.name = 'ResponseError';
    this.data = data;
    this.response = response;
    this.request = request;
    this.type = type;
  }
}
/**
 * 安全的JSON.parse
 */
export function safeJsonParse(
  data,
  throwErrIfParseFail = false,
  response = null,
  request = null,
) {
  try {
    return JSON.parse(data);
  } catch (e) {
    if (throwErrIfParseFail) {
      throw new ResponseError(
        response,
        'JSON.parse fail',
        data,
        request,
        'ParseError',
      );
    }
  } // eslint-disable-line no-empty
  return data;
}

/**
 * 自用异常统一处理基于umirequest + 特殊处理
 */
export function isUmiRequestResponseErr(response) {
  if (response === 'apiErr' || response === 'Abort') {
    return true;
  }
  return false;
}

export const toArrayMode = (value) => {
  return Array.isArray(value)
    ? value
    : _.isString(value?.toString())
    ? [value]
    : [];
};

export const isEmptyObj = (obj) => {
  let isEmpty = true;
  if (!obj || Object.keys(obj)?.length === 0) return isEmpty;
  Object.keys(obj).forEach((d) => {
    if (obj[d] != null && obj[d] !== '') {
      isEmpty = false;
    }
  });
  return isEmpty;
};
