export const onDetailTableColumnChange = (
  extraContentContainerRef: any,
  currentColumns: any[],
  columnState: any,
) => {
  let visibleColumns = currentColumns?.filter((item) => {
    return columnState?.[item?.dataIndex]?.show === true;
  });

  let hasOpersColumn =
    visibleColumns?.filter((item) => {
      return item?.expr?.startsWith('Opers.');
    })?.length > 0;
  let hasIcdeDscgsColumn =
    visibleColumns?.filter((item) => {
      return item?.expr?.startsWith('IcdeDscgs.');
    })?.length > 0;

  let hasOperGroupsColumn =
    visibleColumns?.filter((item) => {
      return item?.expr?.startsWith('OperGroups.');
    })?.length > 0;

  let selectedValueOptions = [];
  let selectedValue = 'NORMAL';

  if (hasIcdeDscgsColumn === true) {
    selectedValue = 'ICDE';
    selectedValueOptions.push('ICDE');
  }

  if (hasOpersColumn === true) {
    selectedValue = 'OPER';
    selectedValueOptions.push('OPER');
  }

  if (hasOperGroupsColumn === true) {
    selectedValue = 'OPER_GROUP';
    selectedValueOptions.push('OPER_GROUP');
  }

  extraContentContainerRef?.current?.setDetailSelectedValueExternal(
    selectedValue,
  );
  extraContentContainerRef?.current?.setDetailSelectedValueOptionsExternal(
    selectedValueOptions,
  );
};
