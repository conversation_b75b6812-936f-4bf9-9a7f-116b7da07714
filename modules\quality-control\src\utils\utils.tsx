import { SorterResult } from 'antd/lib/table/interface';
import _ from 'lodash';

export const dataTableFilterSorterHandler = (
  columns,
  filtData = {},
  sortData: SorterResult<any> = {},
) => {
  let columnDataObj = [];
  let orderDataObj = [];
  let orderDataIndex = 0;

  columns.forEach((col: any, index) => {
    // sort
    let temp = sortData?.columnKey === col.data;
    if (temp) {
      orderDataObj[orderDataIndex] = {
        column: index,
        dir: sortData.order === 'ascend' ? 'asc' : 'desc',
      };
      orderDataIndex++;
    }
    // filt
    let temp2 = filtData[col.data];
    columnDataObj[index] = {
      data: col.data,
      name: col.name,
      searchable: temp2 ? true : false,
      orderable: temp,
      search: {
        // ...forUmiRequestFormData
        value: temp2 ? (Array.isArray(temp2) ? temp2.join(',') : temp2) : '',
        regex: false,
      },
    };
  });

  return { columns: columnDataObj, order: orderDataObj };
};
// hack 获取 sessionStorage
export const getSessionStorage = (key: string) => {
  return JSON.parse(sessionStorage.getItem(key) as string);
};
