import { Card, Col, Row, Divider, Descriptions, Tabs } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '../../components/drawerCardInfo';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import OperChangeTrend from '../components/operChangeTrend/index';
import DaySurgeryChangeTrend from '../components/daySurgeryChangeTrend/index';
import { OperTotalStatsColumns, TabCommonItems } from '../constants';
import Stats from '../../components/statsWithTrend';
import SingleColumnTable from '../../components/singleColumnTable/index';
import { useDebounceEffect, useDeepCompareEffect, useSafeState } from 'ahooks';

const HqmsOper = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, MedTeams } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);

  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
      MedTeams,
    };
    setTableParams(params);
  }, [dateRange, hospCodes, MedTeams]);

  // tabs
  let tabItems = [
    {
      key: 'statistic',
      label: '医疗组综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={24} xl={24}>
            <Stats
              level="medTeam"
              // api={`Api/Hqms/MedTeamHqmsOper/BundledHqmsOper`}
              // trendApi={`Api/Hqms/MedTeamHqmsOper/HqmsOperTrend`}
              api={`API/v2/Hqms/HqmsStats/HqmsOperOfMedTeam`}
              trendApi={`API/v2/Hqms/HqmsStats/HqmsOperTrend`}
              columns={OperTotalStatsColumns}
              type="col-xl-8"
              tabKey={activeKey}
              chartHeight={320}
              useGlobalState
            />
          </Col>
          <SingleColumnTable
            title="医疗组手术分布"
            args={{
              // api: 'Api/Hqms/MedTeamHqmsOper/HqmsOperByMedTeam',
              api: 'API/v2/Hqms/HqmsStats/HqmsOperByMedTeam',
            }}
            tableParams={tableParams}
            dictData={dictData}
            type="table"
            category="MedTeamName"
            visibleValueKeys={[
              'MedTeamName',
              'OperPatCnt',
              'TotalCnt',
              'OperPatRatio',
            ]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName + '手术人数',
                args: {
                  ...tableParams,
                  MedTeams: record?.MedTeam ? [record?.MedTeam] : [],
                  IsValidOper: true,
                },
                detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
                dictData: dictData,
              });
            }}
          />
          <OperChangeTrend
            tableParams={tableParams}
            // api="Api/Hqms/MedTeamHqmsOper/HqmsOperTrend"
            api="API/v2/Hqms/HqmsStats/HqmsOperTrend"
          />
          <DaySurgeryChangeTrend
            tableParams={tableParams}
            // api="Api/Hqms/MedTeamHqmsOper/HqmsOperTrend"
            api="API/v2/Hqms/HqmsStats/HqmsOperTrend"
          />
        </Row>
      ),
    },
    {
      key: TabCommonItems.doctorAnalysis.key,
      label: TabCommonItems.doctorAnalysis.title,
      children: (
        <>
          <Row>
            <SingleColumnTable
              title="医生手术分布"
              args={{
                // api: 'Api/Hqms/SurgeonHqmsOper/HqmsOperBySurgeon',
                api: 'API/v2/Hqms/HqmsStats/HqmsOperBySurgeon',
              }}
              tableParams={tableParams}
              dictData={dictData}
              category="SurgeonName"
              type="table"
              orderKey="OperPatCnt"
              visibleValueKeys={[
                'SurgeonName',
                'TotalCnt',
                'OperPatCnt',
                'OperPatRatio',
              ]}
              colSpan={{ span: 24 }}
              select={{
                dataKey: 'SurgeonType',
                valueKey: 'GroupByDoctor',
                allowClear: false,
                defaultSelect: true,
              }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.SurgeonName + '手术人数',
                  args: {
                    ...tableParams,
                    SurgeonCodes: record?.SurgeonCode
                      ? [record?.SurgeonCode]
                      : [],
                    SurgeonType: record?.SurgeonType,
                    IsValidOper: true,
                  },
                  detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
                  dictData: dictData,
                });
              }}
            />
          </Row>
        </>
      ),
    },
  ];
  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      {/* 把modal与drawer的位置提高到这里，统一在这里处理 避免id太多导致匹配混乱 & tab切换后id失效 */}
      {/* TODO DetailTableModal内部的实现方式是destoryOnClose 导致每次打开都要重新获取columns */}
      <DetailTableModal
        dictData={globalState.dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      {/* drawer 同理 这样保持复用 因为drawer 监听的是detailTableModal 或者一些table */}
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default HqmsOper;
