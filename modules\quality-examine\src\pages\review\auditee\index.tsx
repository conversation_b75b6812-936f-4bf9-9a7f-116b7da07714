import { <PERSON>ge, <PERSON><PERSON>, Card, Col, Input, Row, TableProps } from 'antd';
import {
  ReviewHeaderErrorProgress,
  reviewHeaderErrorProgressItems,
  ReviewHeaderErrorTotals,
  ReviewHeaderGauge,
  ReviewHeaderProgress,
  ReviewHeaderTotals,
} from '@/pages/review/components/header';
import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns, TableResp } from '@uni/commons/src/interfaces';
import {
  BatchItem,
  TaskItem,
  TaskStatusSummary,
} from '@/pages/review/interface';
import { useUpdateEffect } from 'ahooks';
import { isEmptyValues } from '@uni/utils/src/utils';
import ScoreCommentDrawerContainer from '@/pages/review/components/score-comment';
import ReviewTable from '@/pages/review/components/review-table';
import { dmrAuditeeColumns, dmrReviewColumns } from '@/pages/review/columns';
import { UniSelect } from '@uni/components/src';
import locale from 'antd/es/date-picker/locale/zh_CN';
import dayjs from 'dayjs';
import Datepicker from '@uni/components/src/picker/datepicker';
import { useModel } from 'umi';
import {
  examineSummarySpan,
  noDisablePermissionAsAdminCoderManagementExtra,
  sortByBatchDate,
} from '@/pages/review/utils';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { cloneDeep } from 'lodash';
import { useReviewTabContext } from '@/pages/review/tab-hoc';
import { useRouteProps } from '@uni/commons/src/route-context';
import ExportIconBtn from '@uni/components/src/backend-export';

const examineType =
  (window as any).externalConfig?.['dmr']?.['examineType'] ?? 'Score';

const examineSummaryShow =
  (window as any).externalConfig?.['dmr']?.['examineSummaryShow'] ?? true;

const DmrReviewAuditee = () => {
  const taskCreateContainerRef = useRef(null);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const taskTableRef = useRef(null);

  const dmrPreviewContainerRef = useRef(null);

  const [batchId, setBatchId] = useState(undefined);
  const [batchInfo, setBatchInfo] = useState<BatchItem>({});

  const [batchArgs, setBatchArgs] = useState<any>({});

  const { examineMasterId } = useRouteProps();

  const [taskSummaryInfo, setTaskSummaryInfo] =
    useState<TaskStatusSummary>(undefined);

  useEffect(() => {
    if (isEmptyValues(batchId)) {
      if (isEmptyValues(globalState?.searchParams?.BatchItem)) {
        latestBatchInfoReq();
      } else {
        batchInfoProcessor(globalState?.searchParams?.BatchItem);
      }
    }
  }, [globalState?.searchParams?.BatchItem, batchId]);

  const getUserEmployeeCode = () => {
    // return '0007';
    // TODO 不写死..

    let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
    return userInfo?.EmployeeCode;
  };

  const { loading: latestBatchInfoLoading, run: latestBatchInfoReq } =
    useRequest(
      () => {
        let data = {};
        if (!isEmptyValues(examineMasterId)) {
          data['MasterId'] = examineMasterId;
        }

        return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetBatches', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<BatchItem[]>) => {
          let latestBatch = response?.data?.sort(sortByBatchDate)?.at(0);
          batchInfoProcessor(latestBatch);
        },
      },
    );

  const batchInfoProcessor = (batchData: BatchItem) => {
    setBatchId(batchData?.BatchId);
    let batchArgs = {
      Coder: getUserEmployeeCode(),
    };
    if ((userInfo?.Roles ?? [])?.includes('Admin')) {
      batchArgs['Coder'] = undefined;
    }

    if (!isEmptyValues(batchData?.BatchArgs)) {
      try {
        const infoBatchArgs = JSON.parse(batchData?.BatchArgs);

        Object.keys(infoBatchArgs).forEach((key) => {
          if (key?.includes('date')) {
            batchArgs[key.charAt(0).toUpperCase() + key.slice(1)] =
              infoBatchArgs[key];
          } else {
            batchArgs[key] = infoBatchArgs[key];
          }
        });
      } catch (e) {
        //
        console.log('BatchArgs Error', e);
      }
    }
    setBatchArgs(batchArgs);
    setBatchInfo(batchData);
    setTimeout(() => {
      // 首次请求
      if (!isEmptyValues(examineMasterId ?? batchInfo.MasterId)) {
        taskStatusSummaryReq();
        taskTableRef?.current?.freshQueryTable();
      }
    }, 100);
  };

  const { loading: taskStatusSummaryLoading, run: taskStatusSummaryReq } =
    useRequest(
      () => {
        return uniCommonService(
          'Api/Dmr/DmrCardQualityExamine/GetTaskStatusSummary',
          {
            method: 'POST',
            data: {
              MasterId: examineMasterId ?? batchInfo.MasterId,
              Coder: batchArgs?.Coder,
              Sdate: batchArgs?.['Sdate']
                ? dayjs(batchArgs?.['Sdate'])?.format('YYYY-MM-DD')
                : undefined,
              Edate: batchArgs?.['Edate']
                ? dayjs(batchArgs?.['Edate'])?.format('YYYY-MM-DD')
                : undefined,
              Keyword: batchArgs?.Keyword,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<TaskStatusSummary>) => {
          let summaryInfo = response?.data;
          summaryInfo['CommentErrorTotal'] =
            reviewHeaderErrorProgressItems?.reduce(
              (accumulator, item) =>
                accumulator + (summaryInfo?.[item?.valueKey] ?? 0),
              0,
            );
          setTaskSummaryInfo(summaryInfo ?? {});
        },
      },
    );

  const userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');

  return (
    <div id={'auditee-container'} className={'auditee-container'}>
      <div className={'batch-range-container'}>
        <Row gutter={[0, 16]} style={{ width: '100%' }}>
          <Col span={18}>
            <Row className={'flex-row-center'} gutter={[16, 16]}>
              <Col span={8}>
                <div className={'flex-row-center'}>
                  <span className={'batch-label'}>病案标识：</span>
                  <Input
                    placeholder={'请输入病案标识'}
                    value={batchArgs?.Keyword}
                    onChange={(event) => {
                      setBatchArgs({
                        ...batchArgs,
                        Keyword: event?.target?.value,
                      });
                    }}
                  />
                </div>
              </Col>
              <Col span={8}>
                <div className={'flex-row-center'}>
                  <span className={'batch-label'}>时间：</span>
                  <Datepicker.RangePicker
                    style={{ flex: 1 }}
                    locale={locale}
                    picker={'date'}
                    allowClear={false}
                    value={[
                      batchArgs?.['Sdate']
                        ? dayjs(batchArgs?.['Sdate'])
                        : undefined,
                      batchArgs?.['Edate']
                        ? dayjs(batchArgs?.['Edate'])
                        : undefined,
                    ]}
                    showTime={false}
                    format={'YYYY-MM-DD'}
                    onChange={(dates, dateStrings) => {
                      setBatchArgs({
                        ...batchArgs,
                        Sdate: dateStrings?.at(0),
                        Edate: dateStrings?.at(1),
                      });
                    }}
                  />
                </div>
              </Col>

              <Col span={8}>
                <div className={'flex-row-center'}>
                  <span className={'batch-label'}>编码员：</span>
                  <UniSelect
                    allowClear={true}
                    value={batchArgs?.Coder}
                    placeholder={'请选择编码员'}
                    disabled={noDisablePermissionAsAdminCoderManagementExtra(
                      userInfo,
                    )}
                    dataSource={globalState?.dictData?.Coder || []}
                    optionValueKey={'Code'}
                    optionNameKey={'Name'}
                    onClear={() => {
                      delete batchArgs['Coder'];
                      setBatchArgs(cloneDeep(batchArgs));
                    }}
                    onSelect={(value) => {
                      batchArgs['Coder'] = value;
                      if (isEmptyValues(value)) {
                        delete batchArgs['Coder'];
                      }
                      setBatchArgs(cloneDeep(batchArgs));
                    }}
                  />
                </div>
              </Col>
            </Row>
          </Col>

          <Col span={6} style={{ justifyContent: 'flex-end', display: 'flex' }}>
            <Button
              className={'search-btn'}
              type={'primary'}
              onClick={() => {
                taskStatusSummaryReq();
                taskTableRef?.current?.freshQueryTable();
              }}
            >
              查询
            </Button>
          </Col>
        </Row>
      </div>

      {examineSummaryShow === true && (
        <Row gutter={[16, 16]} className={'stats-container'}>
          <Col span={examineSummarySpan?.[examineType]?.at(0)}>
            <ReviewHeaderTotals
              summaryInfo={taskSummaryInfo}
              loading={taskStatusSummaryLoading || latestBatchInfoLoading}
            />
          </Col>
          <Col span={examineSummarySpan?.[examineType]?.at(1)}>
            <ReviewHeaderProgress
              summaryInfo={taskSummaryInfo}
              loading={taskStatusSummaryLoading || latestBatchInfoLoading}
            />
          </Col>
          <Col span={examineSummarySpan?.[examineType]?.at(2)}>
            <ReviewHeaderErrorTotals
              summaryInfo={taskSummaryInfo}
              loading={taskStatusSummaryLoading || latestBatchInfoLoading}
            />
          </Col>

          {examineType === 'Score' && (
            <Col span={examineSummarySpan?.[examineType]?.at(3)}>
              <ReviewHeaderGauge
                summaryInfo={taskSummaryInfo}
                loading={taskStatusSummaryLoading || latestBatchInfoLoading}
              />
            </Col>
          )}

          {examineType === 'Comment' && (
            <Col span={examineSummarySpan?.[examineType]?.at(3)}>
              <ReviewHeaderErrorProgress
                summaryInfo={taskSummaryInfo}
                loading={taskStatusSummaryLoading || latestBatchInfoLoading}
              />
            </Col>
          )}
        </Row>
      )}

      <Card
        style={{ marginTop: 20 }}
        title={'评审结果'}
        extra={
          <>
            <ExportIconBtn
              getExternalExportConfig={() => {
                return {
                  isBackend: true,
                  backendObj: {
                    url: 'Api/Dmr/DmrCardQualityExamine/ExportGetTasks',
                    method: 'POST',
                    data: {
                      // BatchId: batchInfo?.BatchId,
                      MasterId: batchInfo?.MasterId,
                      Coder: batchArgs?.Coder,
                      Sdate: batchArgs?.['Sdate']
                        ? dayjs(batchArgs?.['Sdate'])?.format('YYYY-MM-DD')
                        : undefined,
                      Edate: batchArgs?.['Edate']
                        ? dayjs(batchArgs?.['Edate'])?.format('YYYY-MM-DD')
                        : undefined,
                      Keyword: batchArgs?.Keyword,
                      ...taskTableRef?.current?.getFastSelectKeyToStatuses(),
                    },
                    fileName: '评审结果',
                  },
                };
              }}
            />
            <TableColumnEditButton
              columnInterfaceUrl={'Api/Dmr/DmrCardQualityExamine/GetTasks'}
              onTableRowSaveSuccess={(columns) => {
                taskTableRef?.current?.setTaskTableColumns(columns);
              }}
            />
          </>
        }
      >
        <ReviewTable
          id={'dmr-review-card-progress-table'}
          noBatchIdInData={true}
          masterId={batchInfo?.MasterId}
          batchId={batchId}
          batchInfo={batchInfo}
          taskTableRef={taskTableRef}
          dmrPreviewContainerRef={dmrPreviewContainerRef}
          scroll={{
            x: 'max-content',
            y: examineSummaryShow === false ? 570 : 345,
          }}
          onSummarySelectKeysFilter={(item) => {
            return ['All', 'Reviewed', 'Rejected', 'ReSubmitted']?.includes(
              item?.key,
            );
          }}
          summaryInfo={taskSummaryInfo}
          fastSelectDefaultKey={'Rejected'}
          taskExtraParams={() => {
            return {
              Coder: batchArgs?.Coder,
              Sdate: batchArgs?.['Sdate']
                ? dayjs(batchArgs?.['Sdate'])?.format('YYYY-MM-DD')
                : undefined,
              Edate: batchArgs?.['Edate']
                ? dayjs(batchArgs?.['Edate'])?.format('YYYY-MM-DD')
                : undefined,
              Keyword: batchArgs?.Keyword,
            };
          }}
          extraHiddenColumns={dmrAuditeeColumns}
        />
      </Card>

      <ScoreCommentDrawerContainer
        tableReadonly={true}
        dmrReadonly={false}
        drawerContainerRef={dmrPreviewContainerRef}
        onScoreCommentReviewEnd={(taskId: number) => {
          taskStatusSummaryReq();
          // taskTableRef?.current?.freshQueryTable();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        onScoreCommentClose={(taskId: number) => {
          taskStatusSummaryReq();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        onScoreCommentTableRefresh={(taskId: number) => {
          // taskTableRef?.current?.queryTasksCurrent();
          taskStatusSummaryReq();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        getContainer={() => {
          return document.getElementById('auditee-container');
        }}
      />
    </div>
  );
};

export default DmrReviewAuditee;
