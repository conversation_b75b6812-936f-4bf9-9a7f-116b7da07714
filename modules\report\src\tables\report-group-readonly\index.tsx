import StatsReportReadonlyTable from '@/tables/stats-readonly';
import React, { useEffect, useRef } from 'react';
import {
  ReportDependenciesItem,
  ReportMasterItem,
  ReportRelationsItem,
  TableBaseProps,
} from '@/interfaces';
import './index.less';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { ColumnItem, RespVO } from '@uni/commons/src/interfaces';
import {
  reportBackgroundBriefExport,
  reportBackgroundBundleExport,
  reportBackgroundExport,
  reportTableGroupNameHeaderProcessor,
} from '@/utils';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { RelationMode, ReportEventConstant, ReportModes } from '@/constants';
import DetailsReportReadonlyTable from '@/tables/detail-readonly';
import { message, Spin, Tooltip } from 'antd';
import { isEmptyValues } from '@uni/utils/src/utils';
import { Emitter } from '@uni/utils/src/emitter';
import isNil from 'lodash/isNil';
import Click from '@uni/commons/src/icon/Click';
import cloneDeep from 'lodash/cloneDeep';
import omit from 'lodash/omit';
import dayjs from 'dayjs';
import isEmpty from 'lodash/isEmpty';
import { exportExcel } from '@uni/utils/src/excel-export';
import { exportExcelDictionaryModuleProcessor } from '@uni/components/src/table/processor/data/export';
import { useAsyncEffect } from 'ahooks';

interface ReportGroupReadonlyTableProps extends TableBaseProps {}

interface ReportGroupDependencyItem<T> {
  [key: string]: T[];
}

let dependencyReportContainerRefMap = {};
const ReportGroupReadonlyTable = (props: ReportGroupReadonlyTableProps) => {
  const [reportDependencies, setReportDependencies] = React.useState<
    ReportDependenciesItem[]
  >([]);
  const [reportRelations, setReportRelations] = React.useState<
    ReportGroupDependencyItem<ReportRelationsItem>
  >({});
  const [reportDependenciesMasterItem, setReportDependenciesMasterItem] =
    React.useState<ReportGroupDependencyItem<ReportMasterItem>>({});

  const [reportRelationIdArgs, setReportRelationIdArgs] = React.useState({});

  const groupContainerRef = useRef(null);

  React.useImperativeHandle(groupContainerRef, () => {
    return {
      getReportDependencies: () => {
        return reportDependencies;
      },
      getReportRelations: () => {
        return reportRelations;
      },
    };
  });

  useEffect(() => {
    if (props?.masterItem?.Id) {
      reportDependenciesReq(props?.masterItem?.Id);
      reportRelationsReq(props?.masterItem?.Id);
    }
  }, [props?.masterItem]);

  const buildBundleExportReq = (
    basicReportMasterItem: any,
    reportDependenciesMasterItems: any[],
    reportArgParams: any,
    relationExportArgs: any,
  ) => {
    let body = {};
    body['ReportSettingMasterId'] = basicReportMasterItem.Id;
    body['Reports'] = [];
    // 先推一个 当前的
    body['Reports'].push({
      ReportSettingMasterId: basicReportMasterItem.Id,
      ReportArgs: reportArgParams,
      Reload: false,
    });

    reportDependenciesMasterItems?.forEach((masterItem: any) => {
      let currentRelationExportArgs = {};

      Object.keys(relationExportArgs[masterItem.Id] ?? {})?.forEach((key) => {
        currentRelationExportArgs[key] = '%';
      });

      body['Reports'].push({
        ReportSettingMasterId: masterItem.Id,
        ReportArgs: {
          ...reportArgParams,
          ...currentRelationExportArgs,
        },
        Reload: false,
      });
    });

    return body;
  };

  const buildBriefExportReq = (
    basicReportMasterItem: any,
    reportDependenciesMasterItems: any[],
    reportArgParams: any,
    relationExportArgs: any,
  ) => {
    let body = {};
    body['ReportSettingMasterId'] = basicReportMasterItem.Id;
    body['ReportArgs'] = reportArgParams;
    body['Reports'] = [];
    // 先推一个 当前的
    body['Reports'].push({
      Identifier: 'main',
      ReportSettingMasterId: basicReportMasterItem.Id,
      ReportArgs: reportArgParams,
      Reload: false,
    });

    reportDependenciesMasterItems?.forEach((masterItem: any) => {
      let currentRelationExportArgs = {};

      Object.keys(relationExportArgs[masterItem.Id] ?? {})?.forEach((key) => {
        currentRelationExportArgs[key] = '%';
      });

      let reportItem = {
        ReportSettingMasterId: masterItem.Id,
        ReportArgs: {
          ...reportArgParams,
          ...currentRelationExportArgs,
        },
        Reload: false,
      };

      if (!isEmptyValues(masterItem?.ExtraConfig)) {
        let extraConfig = {};
        try {
          extraConfig = JSON.parse(masterItem?.ExtraConfig);
        } catch (error) {
          extraConfig = {};
        }

        Object.keys(extraConfig)?.forEach((key) => {
          reportItem[key] = extraConfig[key];
        });
      }

      body['Reports'].push(reportItem);
    });

    return body;
  };

  useEffect(() => {
    Emitter.on(ReportEventConstant.REPORT_BUNDLE_EXPORT, (payload) => {
      message.success('导出中');

      // 后端导出
      let exportExcelTitle = props?.masterItem?.Title;
      let bundleExportRequestBody = buildBundleExportReq(
        props?.masterItem,
        reportDependencies,
        payload?.args,
        reportRelationIdArgs,
      );

      console.log('bundleExportRequestBody', bundleExportRequestBody);
      reportBackgroundBundleExport(exportExcelTitle, bundleExportRequestBody);
    });

    Emitter.on(ReportEventConstant.REPORT_BRIEF_EXPORT, (payload) => {
      message.success('简报导出中');

      // 后端导出
      let exportExcelTitle = props?.masterItem?.Title;
      let bundleExportRequestBody = buildBriefExportReq(
        props?.masterItem,
        reportDependencies,
        payload?.args,
        reportRelationIdArgs,
      );

      console.log('briefExportRequestBody', bundleExportRequestBody);
      reportBackgroundBriefExport(exportExcelTitle, bundleExportRequestBody);
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_BUNDLE_EXPORT);
      Emitter.off(ReportEventConstant.REPORT_BRIEF_EXPORT);
    };
  }, [props?.masterItem, reportDependencies, reportRelationIdArgs]);

  // 接口start
  const { loading: reportDependenciesLoading, run: reportDependenciesReq } =
    useRequest(
      (id) => {
        return uniCommonService('Api/Report/Report/GetReportDependencys', {
          params: {
            ReportSettingMasterId: id,
          },
        });
      },
      {
        manual: true,
        formatResult: async (response: RespVO<ReportDependenciesItem[]>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            let reportDependencies = response?.data?.sort(
              (a, b) => (a?.Sort ?? 0) - (b?.Sort ?? 0),
            );
            setReportDependencies(reportDependencies);
            reportDependenciesMasterItemReq(
              reportDependencies?.map((item) => {
                item['Id'] = item?.DependencyProviderKey;
                return item;
              }),
            );
          } else {
            setReportDependencies([]);
          }
        },
      },
    );

  const { loading: reportRelationsLoading, run: reportRelationsReq } =
    useRequest(
      (id) => {
        return uniCommonService('Api/Report/Report/GetReportRelations', {
          params: {
            ReportSettingMasterId: id,
          },
        });
      },
      {
        manual: true,
        formatResult: async (response: RespVO<ReportRelationsItem[]>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            let reportRelationItem = {};
            response?.data?.forEach((item) => {
              if (reportRelationItem[item?.DstReportId]) {
                reportRelationItem[item?.DstReportId] = [
                  ...reportRelationItem[item?.DstReportId],
                  item,
                ];
              } else {
                reportRelationItem[item?.DstReportId] = [item];
              }
            });
            setReportRelations(reportRelationItem);
          } else {
            setReportRelations({});
          }
        },
      },
    );

  const {
    loading: reportDependenciesMasterItemLoading,
    run: reportDependenciesMasterItemReq,
  } = useRequest(
    (reportItems) => {
      return uniCommonService(
        'Api/Report/Report/GetRestrictedReportMastersByIds',
        {
          params: {
            ReportSettingMasterIds: reportItems.map((item) => item.Id),
          },
        },
      );
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ReportMasterItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let reportDependenciesMasterItem = {};
          response?.data?.forEach((item) => {
            if (reportDependenciesMasterItem[item?.Id]) {
              reportDependenciesMasterItem[item?.Id] = [
                ...reportDependenciesMasterItem[item?.Id],
                item,
              ];
            } else {
              reportDependenciesMasterItem[item?.Id] = [item];
            }

            // 设定一套Ref
            if (isEmptyValues(dependencyReportContainerRefMap?.[item?.Id])) {
              dependencyReportContainerRefMap[item?.Id] = React.createRef();
            }
          });

          setReportDependenciesMasterItem(reportDependenciesMasterItem);
        } else {
          setReportDependenciesMasterItem({});
        }
      },
    },
  );

  // 接口end

  const reportRelationItemProcessor = (
    relationItem: ReportRelationsItem,
    lineData: any,
    columnDataIndex?: string,
  ) => {
    let args = {};
    let keyToLabel = {};
    if (relationItem?.RelationMode === RelationMode.ByRow) {
      relationItem?.RowParamPicks?.forEach((item) => {
        args[item] = lineData[item];
      });
    }

    if (relationItem?.RelationMode === RelationMode.ByColumn) {
      let columnArgSettled = false;
      if (relationItem?.SrcColumn === columnDataIndex) {
        columnArgSettled = true;
        args = {
          ...args,
          ...(JSON.parse(relationItem?.SrcColumnParamValue) ?? {}),
        };
      }

      if (columnArgSettled && !isEmptyValues(relationItem?.RowParamPicks)) {
        relationItem?.RowParamPicks?.forEach((item) => {
          args[item] = lineData[item];
        });
      }

      if (columnArgSettled === false) {
        // 当columnDataIndex 为空的时候
        // 通知ByColumn的表格清除数据 即为当前点击的列不是表格所依赖的列的时候清除数据
        Emitter.emit(
          `${ReportEventConstant.REPORT_GROUP_DATA_CLEAR}_${relationItem?.DstReportId}`,
        );
      }
    }

    return {
      args: args,
      keyToLabel: keyToLabel,
    };
  };

  return (
    <div className={'report-group-readonly-container'}>
      <StatsReportReadonlyTable
        reportGroup={true}
        masterItem={props?.masterItem}
        clickable={true}
        onReportQuery={(args) => {
          // 反查 通过差值 来调用 剩下的
          let reportDependencies: ReportDependenciesItem[] =
            groupContainerRef?.current?.getReportDependencies();
          let reportRelations: ReportGroupDependencyItem<ReportRelationsItem> =
            groupContainerRef?.current?.getReportRelations();
          console.log('onReportQuery', reportRelations, reportDependencies);

          if (!isEmptyValues(reportDependencies)) {
            reportDependencies?.forEach((reportDependencyItem) => {
              let containerRef =
                dependencyReportContainerRefMap?.[
                  reportDependencyItem?.DependencyProviderKey
                ];
              if (!isEmptyValues(containerRef)) {
                // 清除所有子表 数据
                containerRef?.current?.clearData();
              }

              if (
                isEmptyValues(
                  reportRelations?.[
                    reportDependencyItem?.DependencyProviderKey
                  ],
                )
              ) {
                // 表示要实时查询
                if (!isEmptyValues(containerRef)) {
                  containerRef?.current?.instantQuery(
                    reportDependencyItem?.DependencyProviderKey,
                    args,
                  );
                }
              }
            });
          }
        }}
        cellClickableProcessor={(columns) => {
          if (!isEmptyValues(reportRelations)) {
            Object.keys(reportRelations).forEach((relationId) => {
              let relationItems = reportRelations?.[relationId];

              relationItems?.forEach((relationItem) => {
                if (relationItem?.RelationMode === RelationMode.ByColumn) {
                  let columnItem = columns?.find(
                    (item) => item?.dataIndex === relationItem?.SrcColumn,
                  );
                  let columnItemBak = cloneDeep(columnItem);
                  if (columnItem) {
                    // title 追加 可点击标识
                    if (isEmptyValues(columnItem['titleLabel'])) {
                      columnItem['titleLabel'] = columnItem['title'];
                    }
                    columnItem['title'] = () => {
                      return (
                        <div className={'flex-row-center'}>
                          {columnItemBak?.title}
                          <Tooltip title={'列的每一格可点击选中并筛选'}>
                            <Click
                              theme="outline"
                              size="20"
                              fill="#1464F8"
                              style={{ marginLeft: '5px' }}
                            />
                          </Tooltip>
                        </div>
                      );
                    };

                    // click事件
                    columnItem['onCell'] = (record, rowIndex) => {
                      return {
                        onClick: (event) => {
                          event.stopPropagation();
                          console.log('onCell', record, rowIndex, relationItem);
                          Emitter.emit(
                            `${ReportEventConstant.STATS_READONLY_CELL_CLICK}_${props?.masterItem?.Id}`,
                            {
                              record: record,
                              rowIndex: rowIndex,
                              relationItem: relationItem,
                              submitClick: true,
                            },
                          );
                        },
                      };
                    };
                  }
                }
              });
            });
          }

          return columns;
        }}
        onReportSubmitClick={(
          initialize: boolean,
          args: any,
          lineData: any,
          columnDataIndex?: string,
          originLineData?: any,
        ) => {
          console.log('onReportSubmitClick', args, lineData);

          // 根据relation 里面的isDefaultParam来看是不是发送事件给到关连表
          Object.keys(reportRelations)?.forEach((relationId) => {
            let instantQuery = false;
            let relationItems = reportRelations?.[relationId];

            // args拼一套title出来
            let selectedTitleSuffix = '';
            let relationArgs = {};

            let reportArgParams = { ...args };
            if (!isEmptyValues(relationItems)) {
              for (let relationItem of relationItems) {
                if (initialize) {
                  if (relationItem?.IsDefaultParam === true) {
                    const processedResult = reportRelationItemProcessor(
                      relationItem,
                      lineData,
                      columnDataIndex,
                    );
                    relationArgs = processedResult?.args ?? {};
                    if (!isEmptyValues(relationArgs)) {
                      instantQuery = true;
                      // TODO 发送事件出去 要通知选中状态
                      if (
                        relationItem?.RelationMode === RelationMode.ByColumn
                      ) {
                        Emitter.emit(
                          `${ReportEventConstant.STATS_READONLY_CELL_CLICK}_${props?.masterItem?.Id}`,
                          {
                            record: lineData,
                            rowIndex: 0,
                            relationItem: relationItem,
                            submitClick: false,
                          },
                        );
                      }

                      if (relationItem?.RelationMode === RelationMode.ByRow) {
                        Emitter.emit(
                          `${ReportEventConstant.REPORT_GROUP_ROW_INITIAL_CLICK}_${props?.masterItem?.Id}`,
                          originLineData ?? lineData,
                        );
                      }
                    }
                  }
                } else {
                  // 表示 非初始化
                  const processedResult = reportRelationItemProcessor(
                    relationItem,
                    lineData,
                    columnDataIndex,
                  );
                  relationArgs = processedResult?.args ?? {};
                  if (!isEmptyValues(relationArgs)) {
                    instantQuery = true;
                    break;
                  }
                }
              }

              reportArgParams = {
                ...reportArgParams,
                ...relationArgs,
              };

              // TODO 留下部分字段用于可能需要的title 追加 点击之后的筛选项
              if (instantQuery) {
                setReportRelationIdArgs({
                  ...reportRelationIdArgs,
                  [relationId]: relationArgs,
                });

                Emitter.emit(
                  `${ReportEventConstant.REPORT_ITEM_QUERY_SUBMIT}_${relationId}`,
                  {
                    args: reportArgParams,
                    selectedItem: lineData,
                    selectedDataIndex: columnDataIndex,
                    selectedTitleSuffix: selectedTitleSuffix,
                  },
                );
              }
            }
          });
        }}
      />

      {/*关联表格*/}
      <Spin
        className={'report-dependency-container'}
        spinning={
          reportRelationsLoading ||
          reportDependenciesLoading ||
          reportDependenciesMasterItemLoading
        }
        tip={'关联报表加载中'}
      >
        {reportDependencies?.map((reportDependencyItem) => {
          let dependencyMasterItem =
            reportDependenciesMasterItem?.[
              reportDependencyItem?.DependencyProviderKey
            ]?.at(0);

          let containerRef =
            dependencyReportContainerRefMap?.[
              reportDependencyItem?.DependencyProviderKey
            ];
          return (
            <div className={'report-dependency-item-container'}>
              {dependencyMasterItem?.ReportMode ===
                ReportModes.DetailsReadOnly && (
                <DetailsReportReadonlyTable
                  containerRef={containerRef}
                  masterItem={dependencyMasterItem}
                />
              )}

              {dependencyMasterItem?.ReportMode ===
                ReportModes.StatsReadOnly && (
                <StatsReportReadonlyTable
                  containerRef={containerRef}
                  masterItem={dependencyMasterItem}
                />
              )}
            </div>
          );
        })}
      </Spin>
    </div>
  );
};

export default ReportGroupReadonlyTable;
