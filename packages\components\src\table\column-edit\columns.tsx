import { ExportOutlined, MenuOutlined } from '@ant-design/icons';
import React from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Checkbox, Form, Popconfirm, Select, Switch, Tooltip } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import Constants from '@uni/utils/src/constants';

const ColumnAlignOptions = {
  left: '居左',
  right: '居右',
  center: '居中',
};

const ColumnOrderModeOptions = {
  ascend: '升序',
  descend: '降序',
};

const ColumnPivotOptions = {
  X: 'X',
  Y: 'Y',
  'X-Category': '分类类别',
};

const DictionaryModuleGroupOptions = {
  Dmr: '病案首页',
  Insur: '结算清单',
  Hqms: '医保上报',
  Wt: '卫统',
};

const ColumnFixedOptions = {
  left: '左固定',
  right: '右固定',
};

export const commonTableColumnProperty = {
  bordered: false,
};

export const tableEditColumns = (containerRef: any) => [
  {
    key: 'sort',
    title: '',
    visible: true,
    align: 'center',
    fixed: 'left',
    width: 60,
    readonly: true,
    render: (node, record, index) => {
      return <MenuOutlined />;
    },
    renderFormItem: (node, record, index) => {
      return <MenuOutlined />;
    },
  },
  {
    dataIndex: 'columnTypeExtra',
    title: '操作',
    fixed: 'left',
    align: 'center',
    width: 60,
    visible: true,
    readonly: true,
    renderFormItem: (
      { index, entity },
      { isEditable },
      editableForm,
      utils,
    ) => {
      return (
        <div className={'flex-row-center'} style={{ justifyContent: 'center' }}>
          <Popconfirm
            title={'确定删除此列？'}
            disabled={entity?.columnTypeExtra !== 'CUSTOM_ADD'}
            onConfirm={() => {
              containerRef?.current?.deleteRecord(entity?.id);
            }}
          >
            <a
              style={
                entity?.columnTypeExtra === 'CUSTOM_ADD'
                  ? { cursor: 'pointer' }
                  : { cursor: 'default', color: '#00000040' }
              }
            >
              删除
            </a>
          </Popconfirm>
        </div>
      );
    },
  },
];

export const tableDataSourceProcessor = (tableDataSource: any[]) => {
  return tableDataSource
    ?.map((item, index) => {
      item['id'] = uuidv4();
      if (item?.['order'] === undefined || item?.['order'] === null) {
        item['order'] = index + 1;
      }

      item['columnTypeExtra'] = item?.extraProperties?.columnTypeExtra ?? '';

      return item;
    })
    ?.sort((a, b) => {
      return a['order'] - b['order'];
    });
};

export const tableColumnPropertiesToColumns = (properties: any[]) => {
  return properties.map((property) => {
    return {
      dataIndex: property.key,
      title: property.label,
      width: property.width,
      visible: property?.visible ?? true,
      valueType: property.valueType,
      align: 'center',
      fixed: property.fixed,
      valueEnum: property.valueEnum,
      editable: (text, record, index) => {
        return text !== '-';
      },
      render: property?.render,
      renderFormItem: property?.renderFormItem,
      readonly: property.readonly,
      fieldProps: {
        ...commonTableColumnProperty,
        ...(property?.fieldProps ?? {}),
      },
    };
  });
};

export const TableColumnFullProperties = [
  {
    key: 'order',
    label: '顺序',
    component: 'Input',
    width: 60,
    fixed: 'left',
    readonly: true,
  },
  {
    key: 'data',
    label: '表格列字段',
    component: 'Input',
    valueType: 'string',
    width: 150,
    // readonly: true,
    fixed: 'left',
    fieldProps: {
      allowClear: false,
      // disabled: true,
    },
  },
  {
    key: 'title',
    label: '标题',
    component: 'Input',
    valueType: 'string',
    width: 200,
    fixed: 'left',
    fieldProps: {
      allowClear: false,
    },
  },
  {
    key: 'visible',
    label: '显示',
    width: 60,
    fixed: 'left',
    fieldProps: {},
    renderFormItem: (
      { index, entity },
      { isEditable },
      editableForm,
      utils,
    ) => {
      return <VisibleSwitch recordId={entity.id} form={editableForm} />;
    },
  },
  {
    key: 'exportable',
    label: '列导出',
    width: 80,
    component: 'Switch',
    valueType: 'switch',
    fieldProps: {},
  },
  // {
  //   key: 'className',
  //   label: '列导出',
  //   component: 'Select',
  //   renderFormItem: (
  //     { index, entity },
  //     { isEditable },
  //     editableForm,
  //     utils,
  //   ) => {
  //     return (
  //       <ExportSwitch
  //         record={entity}
  //         recordId={entity.id}
  //         form={editableForm}
  //       />
  //     );
  //   },
  //   fieldProps: {},
  // },
  {
    key: 'align',
    label: '列对齐',
    width: 80,
    component: 'Select',
    valueEnum: ColumnAlignOptions,
    valueType: 'select',
    visible: false,
    fieldProps: {},
  },
  {
    key: 'width',
    label: '宽度',
    component: 'Input',
    valueType: 'digit',
    width: 80,
    fieldProps: {
      min: 40,
      keyboard: false,
      precision: 0,
      controls: false,
    },
  },
  {
    key: 'orderable',
    label: '启用排序',
    width: 80,
    component: 'Switch',
    valueType: 'switch',
    fieldProps: {},
  },
  {
    key: 'dataType',
    label: '数据展示类型',
    width: 100,
    fieldProps: {},
    renderFormItem: (
      { index, entity },
      { isEditable },
      editableForm,
      utils,
    ) => {
      return <DataTypeSelect recordId={entity.id} form={editableForm} />;
    },
  },
  {
    key: 'scale',
    label: '小数位数',
    width: 100,
    component: 'Input',
    valueType: 'digit',
    fieldProps: {
      min: 0,
      step: 1,
      keyboard: false,
      precision: 0,
      controls: false,
    },
  },
  {
    key: 'description',
    label: '字段提示',
    component: 'Input',
    valueType: 'string',
    width: 180,
    fieldProps: {
      allowClear: false,
    },
  },
  {
    key: 'dictionaryModule',
    label: '字典库key',
    component: 'Input',
    valueType: 'string',
    width: 100,
    fieldProps: {
      allowClear: true,
    },
  },
  {
    key: 'dictionaryModuleGroup',
    label: '字典库Group',
    component: 'Input',
    valueType: 'string',
    width: 100,
    fieldProps: {
      allowClear: true,
    },
  },
  {
    key: 'orderMode',
    label: '排序模式',
    width: 100,
    component: 'Select',
    valueEnum: ColumnOrderModeOptions,
    valueType: 'select',
    visible: true,
    fieldProps: {},
  },
  {
    key: 'orderPriority',
    label: '默认排序列',
    width: 100,
    component: 'Input',
    valueType: 'digit',
    fieldProps: {
      min: 0,
      step: 1,
      keyboard: false,
      precision: 0,
      controls: false,
    },
  },
  {
    key: 'isExtraProperty',
    label: 'Extra字段',
    width: 80,
    component: 'Switch',
    valueType: 'switch',
    fieldProps: {},
  },
];

export const pivotColumns = [
  {
    key: 'pivot',
    label: '行转列',
    component: 'Select',
    valueEnum: ColumnPivotOptions,
    valueType: 'select',
    visible: true,
    fieldProps: {},
  },
];

interface ColumnEditItemProps {
  form: any;
  recordId?: string;
  value?: any;
  onChange?: (value: any) => void;
}

export const OrderPriorityCheckbox = (props: ColumnEditItemProps) => {
  let visible = Form.useWatch([props?.recordId, 'visible'], props?.form);

  return (
    <Checkbox
      disabled={!visible}
      checked={
        typeof props?.value === 'boolean' ? props?.value : props?.value !== 0
      }
      onChange={(event: CheckboxChangeEvent) => {
        let checked = event?.target?.checked;
        console.log('Form', props?.form);

        // 当且仅当只有一行能 被check
        if (checked === true) {
          let values = props?.form?.getFieldsValue();
          Object.keys(values)?.forEach((key) => {
            if (key !== props?.recordId) {
              let keyRecord = values?.[key];
              if (typeof keyRecord?.['orderPriority'] === 'boolean') {
                props?.form?.setFieldValue([key, 'orderPriority'], false);
              } else if (keyRecord?.['orderPriority'] !== 0) {
                props?.form?.setFieldValue([key, 'orderPriority'], 0);
              }
            }
          });
        }

        props?.onChange && props?.onChange(checked);
      }}
    />
  );
};

export const VisibleSwitch = (props: ColumnEditItemProps) => {
  return (
    <Switch
      checked={props?.value}
      onChange={(checked: boolean, event: any) => {
        // 当且仅当只有一行能 被check
        if (checked === false) {
          props?.form?.setFieldValue([props?.recordId, 'orderPriority'], false);
        }

        props?.onChange && props?.onChange(checked);
      }}
    />
  );
};

export const ExportSwitch = (props: any) => {
  return (
    <Switch
      checked={props?.value?.includes('exportable')}
      onChange={(checked: boolean, event: any) => {
        props?.onChange && props?.onChange(checked ? ' exportable' : '');
      }}
    />
  );
};

export const DataTypeSelect = (props) => {
  return (
    <Select
      value={props?.value}
      onChange={(value: string) => {
        props?.onChange && props?.onChange(value);
      }}
      options={Object.keys(Constants.DateFormatType)?.map((key) => {
        return {
          label: Constants.DateFormatType[key],
          value: key,
        };
      })}
    />
  );
};

const reportModeMap = {
  DetailsReadOnly: '只读明细报表',
  StatsPersist: '上报统计报表',
  StatsReadOnly: '只读统计报表',
  DetailsPersist: '上报明细报表',
  ReportGroupReadOnly: '下钻报表组',
};

export const TableColumnsApiRedirectionColumns = [
  {
    dataIndex: 'Title',
    title: '标题',
    visible: true,
    width: '55%',
    align: 'center',
  },
  {
    dataIndex: 'ReportMode',
    title: '类型',
    visible: true,
    width: '30%',
    align: 'center',
    render: (node, record, index) => {
      return <span>{reportModeMap[record['ReportMode']]}</span>;
    },
  },
  // 先隐藏掉，因为功能还未实现。等做报表预览的时候再一起做。
  // {
  //   dataIndex: 'operation',
  //   title: '',
  //   visible: true,
  //   width: '10%',
  //   align: 'center',
  //   render: (node, record, index) => {
  //     return (
  //       <div className={'flex-row-center'} style={{ justifyContent: 'center' }}>
  //         <Tooltip title={'查看报表列配置'}>
  //           <ExportOutlined />
  //         </Tooltip>
  //       </div>
  //     );
  //   },
  // },
];
