import { getSubject } from './subjectDefinition.js';
import { v4 as uuid } from 'uuid';
import { times } from 'lodash';
import { inferUnderlyingType } from './utilities.js';
function mapDataType(dataType) {
  if (!dataType) return null;
  dataType = dataType.toLowerCase();
  if (dataType === 'int' || dataType === 'long' || dataType === 'decimal') {
    return 'number';
  } else if (dataType === 'bool') {
    return 'boolean';
  } else if (dataType === 'datetime') {
    return 'date';
  }

  return 'text';
}
function tryInferDataType(field, operator, value) {
  var subject = getSubject(field);
  if (subject) return mapDataType(subject.dataType);

  if (operator === 'select_any_in') return 'text';

  var type = inferUnderlyingType(value);
  if (type === 'string') return 'text';
  return type;
}

function constant(value) {
  return () => value;
}

function toAtomRule(field, operator, value) {
  const dataType = tryInferDataType(field, operator, value);

  const isBetween = operator === 'between';

  value = dataType ? (isBetween ? [...value] : [value]) : [];
  const valueSrc = dataType ? times(isBetween ? 2 : 1, constant('value')) : [];
  const valueType = dataType
    ? times(isBetween ? 2 : 1, constant(dataType))
    : [];

  const rule = {
    type: 'rule',
    id: uuid(),
    properties: {
      field,
      operator,
      value,
      valueSrc,
      valueType,
    },
  };

  return rule;
}

function toNotRule(childRule) {
  const rule = {
    type: 'group',
    id: uuid(),
    properties: {
      conjunction: 'AND',
      not: true,
    },
    children1: [childRule],
  };

  return rule;
}

function toSomeRule(field, childrenRules) {
  const rule = {
    type: 'rule_group',
    id: uuid(),
    properties: {
      conjunction: 'AND',
      not: false,
      field,
    },
    children1: [...childrenRules],
  };

  return rule;
}

function toConjunctionRule(childrenRules, conjunction) {
  const rule = {
    type: 'group',
    id: uuid(),
    properties: {
      conjunction,
      not: false,
    },
    children1: [...childrenRules],
  };
  return rule;
}

function toRuleFromChildRule(childRule) {
  if (childRule.type === 'group') return childRule;

  const rule = {
    type: 'group',
    id: uuid(),
    properties: {
      conjunction: 'AND',
      not: false,
    },
    children1: [childRule],
  };
  return rule;
}

export {
  toAtomRule,
  toNotRule,
  toSomeRule,
  toConjunctionRule,
  toRuleFromChildRule,
};
