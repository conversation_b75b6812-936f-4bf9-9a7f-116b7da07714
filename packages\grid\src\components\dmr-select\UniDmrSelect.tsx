import React, { FC, useCallback, useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import './index.less';
import { SelectProps } from 'antd/lib/select';
import {
  pinyinInitialSearch,
  searchFunctionGetter,
} from '@uni/utils/src/pinyin';
// @ts-ignore
import { useModel } from '@@/plugin-model/useModel';
import {
  getDefaultItemByDataSource,
  isEmptyValues,
} from '@uni/utils/src/utils';
import { UniAntdSelect } from '@uni/components/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  getSelectorDropdownContainerNode,
  getDeletePressEventKey,
} from '../../utils';
import { Button, Divider, Input, Select, Space, Tooltip } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';
import { TooltipPlacement } from 'antd/lib/tooltip';
import GridItemContext from '@uni/commons/src/grid-context';
import {
  DownOutlined,
  LeftOutlined,
  RightOutlined,
  UpOutlined,
} from '@ant-design/icons';
import chunk from 'lodash/chunk';
import KeyCode from 'rc-util/lib/KeyCode';
import {
  KeyboardFriendlyDropdown,
  KeyboardNumberSelectProps,
} from './keyboard';
import orderBy from 'lodash/orderBy';

const { Option } = UniAntdSelect;

const numberSelectSpecialKeys = [
  KeyCode.EQUALS,
  KeyCode.DASH,
  KeyCode.NUM_MINUS,
  KeyCode.NUM_PLUS,
];

interface UniDmrSelectProps extends SelectProps, KeyboardNumberSelectProps {
  form?: any;
  containerRef?: any;

  width?: number;
  dataSource?: any[];

  formItemId?: string;

  dataSourceProcessor?: (dataSource: any[]) => any[];
  optionTitleKey?: string;
  optionNameKey?: string;
  optionValueKey?: string;
  className?: string;
  containerClassName?: string;
  style?: React.CSSProperties;
  onChange?: (value: any, option?: any) => void;
  onFilterOptions?: (inputValue) => boolean;
  value?: string | string[];
  allowClear?: boolean;
  enablePinyinSearch?: boolean;
  placeholder?: string;
  showSearch?: boolean;
  label?: string;
  /**
   * model中某一对象的key -> dataSource
   */
  modelDataKey?: string;
  modelDataGroup?: string;

  showAction?: ('focus' | 'click')[];

  nameFormat?: string;
  labelFormat?: string;
  formKey?: string;

  optionKey?: string;

  optionLabelTooltip?: boolean;

  optionErrorTooltip?: boolean;
  optionErrorTooltipLabel?: string;

  optionLabelProp?: string;

  // tableId
  tableId?: string;

  contentEditable?: boolean;

  leftOneAutoSelect?: boolean;

  getTooltipPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;

  tooltipPlacement?: TooltipPlacement;

  valueFormat?: string;

  doubleClickPopUp?: boolean;

  numberSelectItem?: boolean;

  parentNodeId?: string;
}

const UniDmrSelect: FC<UniDmrSelectProps> = ({
  containerRef,
  form,

  width,
  formItemId,
  containerClassName,
  className,
  style,
  optionTitleKey,
  optionNameKey = 'Name',
  optionValueKey = 'Code',
  dataSource,
  onChange,
  onSelect,
  onFilterOptions,
  enablePinyinSearch = true,
  label,
  modelDataKey,
  modelDataGroup,
  dataSourceProcessor,
  showAction,
  nameFormat,
  labelFormat,
  formKey,
  optionKey,
  optionLabelProp,
  optionErrorTooltipLabel,
  optionErrorTooltip = true,
  tableId,
  contentEditable,
  leftOneAutoSelect,
  getTooltipPopupContainer,
  tooltipPlacement,
  valueFormat,

  numberSelectItem,
  numberSelectRecordsTotal,
  numberSelectPageSize,
  numberPagination,

  parentNodeId,

  ...restProps
}: UniDmrSelectProps) => {
  const listRef = React.useRef(null);

  const context = React.useContext(GridItemContext);

  const inputFocusNotSelectAll =
    context?.externalConfig?.inputFocusNotSelectAll ?? false;

  const selectorWithNotValid = context?.extra?.selectorWithNotValid ?? false;

  const doubleClickPopUp = context?.externalConfig?.doubleClickPopUp ?? false;

  const doubleClickPopUpFalseKeys =
    context?.externalConfig?.doubleClickPopUpFalseKeys ?? [];

  const enableKeyboardFriendlySelect =
    context?.externalConfig?.enableKeyboardFriendlySelect ?? false;

  const flipPrev = context?.externalConfig?.keyboardFlipKeys?.prev ?? [
    109, 189,
  ];
  const flipNext = context?.externalConfig?.keyboardFlipKeys?.next ?? [
    107, 187,
  ];

  // dataSource from model
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [optionOpen, setOptionOpen] = useState(false);

  const [optionsPageCurrent, setOptionsPageCurrent] = useState(0);

  const [errorTooltipOpen, setErrorTooltipOpen] = useState(false);

  const [searchValue, setSearchValue] = useState('');

  const modelData = modelDataGroup
    ? globalState?.dictData?.[modelDataGroup]
    : globalState?.dictData;

  const itemFormatter = (item, format) => {
    if (item) {
      return format?.replace(/{([a-zA-Z\\.]+)}/g, function (match, param) {
        // console.log('itemFormatter', param);

        // check if the argument is present
        if (param?.includes('ExtraProperties')) {
          // 如果是extraConfig 那就需要 追加替换
          let actualKey = param?.replace('ExtraProperties.', '');
          if (item?.ExtraProperties?.[actualKey]) {
            return item?.ExtraProperties?.[actualKey] == undefined
              ? ''
              : item?.ExtraProperties?.[actualKey];
          }
          return '';
        }
        return item[param] == undefined ? '' : item[param];
      });
    }

    return '';
  };

  const defaultDataSourceProcessor = (dataSource: any[]) => {
    let selectDataSource = [];
    dataSource.slice().forEach((item) => {
      if (nameFormat) {
        item['label'] = itemFormatter(item, nameFormat);
      }

      if (labelFormat) {
        item['label'] = itemFormatter(item, labelFormat);
      }

      selectDataSource.push(item);
    });

    return selectDataSource;
  };

  const [selectDataSource, setSelectDataSource] = useState<any[]>(
    dataSourceProcessor
      ? dataSourceProcessor(dataSource || [])
      : defaultDataSourceProcessor(dataSource || []),
  );

  const [originDataSource, setOriginDataSource] = useState<any[]>(
    dataSourceProcessor
      ? dataSourceProcessor(dataSource || [])
      : defaultDataSourceProcessor(dataSource || []),
  );

  const dataSourceValueProcessor = (dataSource: any[]) => {
    if (valueFormat !== undefined) {
      return dataSource?.map((item) => {
        item['value'] = itemFormatter(item, valueFormat) || item['value'];

        return item;
      });
    }

    return dataSource;
  };

  useEffect(() => {
    Emitter.on(getDeletePressEventKey(formKey), (key) => {
      if (key.includes(formKey)) {
        // 表示就是当前组件的formKey
        onChange && onChange(undefined);
      }
    });

    return () => {
      Emitter.off(getDeletePressEventKey(formKey));
    };
  }, []);

  useEffect(() => {
    if (modelData && modelDataKey) {
      let modelDataSource = modelData[modelDataKey];
      if (modelDataSource && Array.isArray(modelDataSource)) {
        let selectDataSource = dataSourceProcessor
          ? dataSourceProcessor(modelDataSource || [])
          : defaultDataSourceProcessor(modelDataSource || []);
        setSelectDataSource(selectDataSource);
        setOriginDataSource(cloneDeep(selectDataSource));
      }
    }
  }, [modelData, modelDataKey]);

  useEffect(() => {
    if (dataSource && dataSource?.length > 0) {
      let processedDataSource = dataSourceProcessor
        ? dataSourceProcessor(dataSource || [])
        : defaultDataSourceProcessor(dataSource || []);
      if (!isEqual(processedDataSource, selectDataSource)) {
        setSelectDataSource(processedDataSource);
        setOriginDataSource(cloneDeep(processedDataSource));
      }
    } else {
      // 保证  modelDataKey 不存在的情况下才允许 置为空 因为modelDataKey 和 DataSource是互斥的
      if (isEmptyValues(modelDataKey)) {
        setSelectDataSource([]);
        setOriginDataSource([]);
      }
    }
  }, [dataSource, modelDataKey]);

  // value改变时 null 设定一份 defaultValue
  useEffect(() => {
    if (!isEmptyValues(dataSource)) {
      if (restProps?.value === null || restProps?.value === undefined) {
        let defaultItem = getDefaultItemByDataSource(dataSource);
        if (defaultItem) {
          onChange && onChange(defaultItem?.Code, defaultItem);
          Emitter.emit(EventConstant.DMR_FORM_VALUE_RESET_WITH_DEFAULT_VALUE);
        }
      }
    }
  }, [restProps?.value, dataSource]);

  const commonDataSourceProcessor = (dataSource: any) => {
    dataSource?.forEach((item) => {
      item['title'] = item[optionTitleKey || optionNameKey];
      item['value'] = item[optionValueKey];
      item['label'] =
        item[
          !isEmptyValues(labelFormat) || !isEmptyValues(nameFormat)
            ? 'label'
            : optionNameKey
        ];

      item['Sort'] = item?.['Sort'] ?? 999;

      if (selectorWithNotValid === false) {
        item['disabled'] = item?.IsValid === false;
      }
    });

    return orderBy(dataSource, ['Sort', 'disabled'], ['asc', 'asc']);
  };

  const onSearch = (searchValue) => {
    if (isEmptyValues(searchValue)) {
      setSelectDataSource(originDataSource?.slice());
      return;
    }

    let selectDataSource = originDataSource?.slice()?.filter((item) => {
      return optionFilter(searchValue, item);
    });

    // 自动选中
    if (leftOneAutoSelect === true) {
      if (selectDataSource?.length === 1) {
        onSelectChange(selectDataSource[0][optionValueKey]);
        let nextEvent = { target: {} };
        nextEvent['target']['id'] =
          formItemId ??
          `formItem#${formKey ?? uuidv4()}#DmrSelect${
            modelDataKey === 'Employee' ? '#Employee' : ''
          }`;
        nextEvent['target']['classList'] = ['ant-select'];
        Emitter.emit(context?.eventNames?.TABLE_NEXT_KEY, {
          event: nextEvent,
          indexOffset: 1,
        });
      }
    }

    setSelectDataSource(cloneDeep(selectDataSource));
  };

  const onValueChange = (value: any, option?: any) => {
    setErrorTooltipOpen(false);
    onChange && onChange(value ?? '', option);
  };

  const onSelectChange = (value: any, option?: any) => {
    setErrorTooltipOpen(false);
    onSelect && onSelect(value ?? '', option);
  };

  const optionFilter = (inputValue, option) => {
    let filterOptionsResult = true;
    if (onFilterOptions) {
      filterOptionsResult = onFilterOptions(inputValue);
    }
    return (
      (filterOptionsResult &&
        option &&
        option?.[optionNameKey]
          ?.toString()
          ?.toLowerCase()
          ?.[searchFunctionGetter(false)](inputValue?.toLowerCase())) ||
      option?.['label']
        ?.toString()
        ?.toLowerCase()
        ?.[searchFunctionGetter(false)](inputValue?.toLowerCase()) ||
      option?.[optionValueKey]
        ?.toString()
        ?.toLowerCase()
        ?.[searchFunctionGetter(false)](inputValue?.toLowerCase()) ||
      // 暂时写死Name
      option?.['Name']
        ?.toString()
        ?.toLowerCase()
        ?.[searchFunctionGetter(false)](inputValue?.toLowerCase()) ||
      (enablePinyinSearch &&
        pinyinInitialSearch(
          option?.[optionTitleKey || optionNameKey]?.toString()?.toLowerCase(),
          inputValue?.toLowerCase(),
        ))
    );
  };

  const doubleClickPopUpTransform = () => {
    if (doubleClickPopUpFalseKeys?.includes(modelDataKey)) {
      return false;
    }

    return restProps?.doubleClickPopUp ?? doubleClickPopUp ?? false;
  };

  const options = dataSourceValueProcessor(
    commonDataSourceProcessor(selectDataSource),
  )?.filter((optionItem) => {
    if (numberSelectItem !== true) {
      return optionItem;
    }

    if (isEmptyValues(searchValue)) {
      return true;
    }
    return optionFilter(searchValue, optionItem);
  });

  const chunkPageSize = Math.min(numberSelectPageSize ?? 9, 9);

  // 最大也就 9 个 再大不支持
  const chunkOptions = chunk(options, chunkPageSize);

  const keyboardFriendlyDropdownRender = (menu: any) => {
    return (
      <KeyboardFriendlyDropdown
        enableKeyboardFriendlySelect={enableKeyboardFriendlySelect}
        searchValue={searchValue}
        listRef={listRef}
        chunkOptions={chunkOptions}
        value={restProps?.value}
        onValueChange={onValueChange}
        onSelectChange={onSelectChange}
        optionOpen={optionOpen}
        setOptionOpen={setOptionOpen}
        optionTitleKey={optionTitleKey}
        optionNameKey={optionNameKey}
        chunkPageSize={chunkPageSize}
        optionsPageCurrent={optionsPageCurrent}
        setOptionsPageCurrent={setOptionsPageCurrent}
        numberSelectItem={numberSelectItem}
        numberSelectPageSize={numberSelectPageSize}
        numberSelectRecordsTotal={numberSelectRecordsTotal}
        numberPagination={numberPagination}
      />
    );
  };

  const keyboardFriendlyProps = numberSelectItem
    ? {
        dropdownRender: keyboardFriendlyDropdownRender,
        open: optionOpen,
        onDropdownVisibleChange: (visible: boolean) => setOptionOpen(visible),
        onKeyboardNumberSelect: (event: any, index: any) => {
          if (enableKeyboardFriendlySelect === true && index <= chunkPageSize) {
            event?.preventDefault();
            const actualIndex = index - 1;
            let selectedItem =
              chunkOptions?.[numberPagination ? 0 : optionsPageCurrent]?.[
                actualIndex
              ];
            if (selectedItem) {
              onValueChange && onValueChange(selectedItem?.value, selectedItem);

              onSelectChange &&
                onSelectChange(selectedItem?.value, selectedItem);

              setOptionOpen(false);
            }
          }
        },
        onKeyboardPageFlip: (event: any) => {
          const { which, ctrlKey } = event;

          if (flipPrev?.includes(which)) {
            event?.preventDefault();
            event?.stopPropagation();
            let newPageCurrent = optionsPageCurrent - 1;
            if (newPageCurrent >= 0) {
              setOptionsPageCurrent(newPageCurrent);
              listRef?.current?.onKeyboardPageFlip();
            }
          }

          if (flipNext?.includes(which)) {
            event?.preventDefault();
            event?.stopPropagation();
            let newPageCurrent = optionsPageCurrent + 1;
            if (newPageCurrent < chunkOptions?.length) {
              setOptionsPageCurrent(newPageCurrent);
              listRef?.current?.onKeyboardPageFlip();
            }
          }
        },
      }
    : {};

  return (
    <Tooltip
      open={errorTooltipOpen}
      color={'rgba(235, 87, 87, 0.85)'}
      title={optionErrorTooltipLabel ?? '选项不存在，请检查'}
      placement={tooltipPlacement ?? 'top'}
      getPopupContainer={(trigger) =>
        getTooltipPopupContainer
          ? getTooltipPopupContainer(trigger)
          : document.getElementById('dmr-content-container')
      }
    >
      <div
        style={width ? { width: width } : {}}
        className={`select-container dmr-select-container ${containerClassName}`}
      >
        {label && <label>{label}</label>}
        <UniAntdSelect
          {...restProps}
          ref={containerRef}
          id={
            formItemId ??
            `formItem#${formKey ?? uuidv4()}#DmrSelect${
              modelDataKey === 'Employee' ? '#Employee' : ''
            }`
          }
          {...keyboardFriendlyProps}
          showArrow={false}
          showAction={showAction ?? ['click']}
          className={`select ${className}`}
          style={{ ...{}, ...style }}
          showSearch
          // allowClear={restProps?.allowClear || true}
          allowClear={false}
          value={restProps?.value}
          getPopupContainer={(trigger) =>
            (restProps?.getPopupContainer &&
              restProps?.getPopupContainer(trigger)) ||
            getSelectorDropdownContainerNode(parentNodeId)
          }
          numberSelectItem={numberSelectItem ?? false}
          optionLabelTooltipProps={{
            getTooltipContainer: (trigger) => {
              return document.getElementById('dmr-content-container');
            },
          }}
          contentEditable={contentEditable ?? !inputFocusNotSelectAll}
          mousedownOptionOpen={false}
          onSearch={
            leftOneAutoSelect
              ? onSearch
              : (searchValue: string) => {
                  if (numberSelectItem === true) {
                    setSearchValue(searchValue);
                  }
                }
          }
          filterOption={leftOneAutoSelect ? false : optionFilter}
          onKeyDown={(event) => {
            console.log('onInputKeyDown', event);

            // 当且仅当
            if (numberSelectItem === true && optionOpen === true) {
              listRef?.current?.onKeyDown(event);
            }

            if (restProps?.onKeyDown) {
              restProps?.onKeyDown(event);
            }

            if (
              (event as any)?.hosted !== true &&
              selectDataSource?.length > 0
            ) {
              if (optionErrorTooltip && event.key === 'Enter') {
                let inputValue = (event.target as any).value;
                let aliasDataItem = selectDataSource?.find((item) => {
                  return optionFilter(inputValue, item);
                });

                if (aliasDataItem === undefined) {
                  setErrorTooltipOpen(true);
                  event.preventDefault();
                  event.stopPropagation();
                }
              }
            }
          }}
          enterSwitch={true}
          // dropdownMatchSelectWidth={false}
          // virtual={true}
          onBlur={() => {
            if (!isEmptyValues(restProps?.value)) {
              setErrorTooltipOpen(false);
            }
          }}
          placeholder={dataSource?.length ? `${restProps?.placeholder}` : ''}
          onChange={onValueChange}
          onSelect={onSelectChange}
          optionLabelProp={optionLabelProp}
          options={options}
          searchValueEmptyClose={true}
          dumbOnComposition={true}
          doubleClickPopUp={doubleClickPopUpTransform()}
        />
      </div>
    </Tooltip>
  );
};

export default UniDmrSelect;
