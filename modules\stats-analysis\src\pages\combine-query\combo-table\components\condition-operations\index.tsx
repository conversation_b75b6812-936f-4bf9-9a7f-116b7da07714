import IconBtn from '@uni/components/src/iconBtn';
import { Emitter } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import React from 'react';
import './index.less';
import { Form } from 'antd';
import { ClearOutlined, PlusOutlined } from '@ant-design/icons';
import { searchRecordParentFieldByGroupId } from '@/pages/combine-query/combo-table/utils';
import groupBy from 'lodash/groupBy';

interface IComboQueryTableConditionOperationProps {
  recordId: string;
  index: number;
  groupId: string;
  dataSource: { id: string; groupId: string; [key: string]: any }[];
}

const ComboQueryTableConditionOperation = ({
  recordId,
  groupId,
  index,
  dataSource,
}: IComboQueryTableConditionOperationProps) => {
  let groupByResult = groupBy(dataSource, 'groupId');
  let offset = groupByResult?.[groupId]?.length
    ? groupByResult?.[groupId]?.length - 1
    : 0;

  return (
    <>
      <IconBtn
        // style={{ marginRight: dataSource?.length > 1 && 10 }}
        style={{ marginRight: 10 }}
        type="add"
        onClick={() => {
          Emitter.emit(
            StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ADD,
            {
              index,
              groupId,
              recordId,
              offset,
            },
          );
        }}
      />
      {dataSource?.length > 1 && (
        <IconBtn
          type="delete"
          onClick={() => {
            Emitter.emit(
              StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_DELETE,
              {
                index,
                groupId,
                recordId,
              },
            );
          }}
        />
      )}

      {index === 0 && dataSource?.length === 1 && (
        <IconBtn
          type="clear"
          customIcon={<ClearOutlined />}
          title={'清空'}
          onClick={() => {
            Emitter.emit(
              StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_CLEAR,
              {
                index,
                groupId,
                recordId,
              },
            );
          }}
        />
      )}
    </>
  );
};

export default ComboQueryTableConditionOperation;
