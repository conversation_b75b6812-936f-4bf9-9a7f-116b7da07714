import {
  But<PERSON>,
  Card,
  Divider,
  message,
  Modal,
  Space,
  Spin,
  Tooltip,
} from 'antd';
import _ from 'lodash';
import { useModel } from '@@/plugin-model/useModel';
import './index.less';
import { UniTable } from '@uni/components/src';
import { useReducer, useRef, Reducer, useState } from 'react';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { useDeepCompareEffect } from 'ahooks';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { InitTableState, TableAction, tableReducer } from '@uni/reducers/src';
import { IReducer, ITableState } from '@uni/reducers/src/interface';
import TreeModal from './components/TreeModal';
import { RedoOutlined } from '@ant-design/icons';

const ReportDataFlow = () => {
  const {
    globalState: { searchParams },
  } = useModel('@@qiankunStateFromMaster');

  const { dateRange, hospCodes } = searchParams;

  const ref = useRef<any>();

  // summarys data
  const [SummaryTableState, SummaryTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, {
    ...InitTableState,
  });

  const [dataSource, setDataSource] = useState([]);

  const [tableParams, setTableParams] = useState(undefined);

  const [dataSourceColumns, setDataSourceColumns] = useState([]);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const onModalOk = async (data) => {
    console.log('分组设置数据:', data);
    if (data && SummaryTableState.selectedKeys?.length) {
      const params = {
        MonthlyDataIds: SummaryTableState.selectedKeys,
        ...data,
      };
      await ProtocolCalculate({ ...params });
    }
    setIsModalOpen(false);
    await refreshData();
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes || [],
    };
    setTableParams(params);
    if (dateRange && dateRange.length) {
      getDataSourceColumnsReq();
      getDataSourceReq(params);
    }
  }, [dateRange, hospCodes]);

  // 查询data
  const { loading: dataSourceLoading, run: getDataSourceReq } = useRequest(
    (params) => {
      return uniCommonService(
        'Api/MedQuality/DataManagement/GetMonthlyDataCnt',
        {
          method: 'POST',
          data: params,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any[]>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          setDataSource(response?.data);
        } else {
          setDataSource([]);
        }
      },
    },
  );

  // Columns
  const {
    data: tableColumns,
    mutate: mutateDescriptionColumns,
    run: getDataSourceColumnsReq,
  } = useRequest(
    () => {
      return uniCommonService(
        'Api/MedQuality/DataManagement/GetMonthlyDataCnt',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          let list = tableColumnBaseProcessor([], response?.data?.Columns);
          list = list.map((i) => {
            if (['MonthDate', 'MonthlyDataStatus'].includes(i?.name)) {
              i['width'] = 180;
            }
            return {
              ...i,
            };
          });
          setDataSourceColumns(list);
        } else {
          setDataSourceColumns([]);
        }
      },
    },
  );

  // 删除api
  const { loading: ProtocolCalculateLoading, run: ProtocolCalculate } =
    useRequest(
      (data) => {
        return uniCommonService(
          'Api/MedQuality/DataManagement/ProtocolCalculate ',
          {
            method: 'POST',
            data: {
              ...data,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (res) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            setTimeout(() => {
              message.success('计算成功');
            }, 1000);
            return null;
          }
          return null;
        },
      },
    );
  // 删除api
  const { loading: appealTaskDeleteLoading, run: appealTaskDeleteReq } =
    useRequest(
      (MonthlyDataIds) => {
        return uniCommonService(
          'Api/MedQuality/DataManagement/DeleteHqmsCards',
          {
            method: 'POST',
            data: {
              MonthlyDataIds,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (res) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            setTimeout(() => {
              message.success('删除成功');
            }, 1000);
            return null;
          }
          return null;
        },
      },
    );

  // 锁定api
  const { loading: ConfirmTaskLoading, run: ConfirmTaskReq } = useRequest(
    (MonthlyDataIds) => {
      return uniCommonService(
        'Api/MedQuality/DataManagement/ConfirmMonthlyDatas',
        {
          method: 'POST',
          data: {
            MonthlyDataIds,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          setTimeout(() => {
            message.success(
              `锁定${SummaryTableState.selectedKeys.length}项成功`,
            );
          }, 1000);
          return null;
        }
        return null;
      },
    },
  );

  // 解锁api
  const { loading: UnConfirmTaskLoading, run: UnConfirmTaskReq } = useRequest(
    (MonthlyDataIds) => {
      return uniCommonService(
        'Api/MedQuality/DataManagement/UnConfirmMonthlyDatas',
        {
          method: 'POST',
          data: {
            MonthlyDataIds,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          setTimeout(() => {
            message.success(
              `解锁${SummaryTableState.selectedKeys.length}项成功`,
            );
          }, 1000);
          return null;
        }
        return null;
      },
    },
  );

  // 初始化api
  const { loading: initTaskLoading, run: initTaskReq } = useRequest(
    (MonthlyDataIds) => {
      return uniCommonService(
        'Api/MedQuality/DataManagement/InitMonthlyDatas',
        {
          method: 'POST',
          data: {
            MonthlyDataIds,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          setTimeout(() => {
            message.success(
              `初始化${SummaryTableState.selectedKeys.length}项成功`,
            );
          }, 1000);
          return null;
        }
        return null;
      },
    },
  );

  const initialState = async () => {
    await initTaskReq(SummaryTableState.selectedKeys);
    await refreshData();
  };
  const handleConfirmTask = async () => {
    await ConfirmTaskReq(SummaryTableState.selectedKeys);
    await refreshData();
  };
  const handleUnConfirmTask = async () => {
    await UnConfirmTaskReq(SummaryTableState.selectedKeys);
    await refreshData();
  };

  const refreshData = async () => {
    await clearAllSelectRowKeys();
    await getDataSourceReq(tableParams);
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '删除',
      content: (
        <>
          <span>确定要删除这{SummaryTableState.selectedKeys.length}项吗？</span>
        </>
      ),
      onOk: async () => {
        if (SummaryTableState.selectedKeys?.length) {
          await appealTaskDeleteReq(SummaryTableState.selectedKeys);
          await refreshData();
        } else {
          await clearAllSelectRowKeys();
        }
      },
    });
  };

  const clearAllSelectRowKeys = async () => {
    await SummaryTableDispatch({
      type: TableAction.selectionChange,
      payload: {
        selectedKeys: [],
        selectedRecords: [],
      },
    });
  };

  return (
    <Card
      title={'数据统计'}
      extra={
        <Space>
          <Divider type="vertical" />
          <TableColumnEditButton
            columnInterfaceUrl="Api/MedQuality/DataManagement/GetMonthlyDataCnt"
            onTableRowSaveSuccess={(newColumns) => {
              mutateDescriptionColumns(
                tableColumnBaseProcessor(
                  [],
                  newColumns?.map((d) => ({
                    ...d,
                    valueType: d?.columnType === 'Boolean' ? 'boolean' : '',
                  })),
                ),
              );
            }}
          />
          <Tooltip title="刷新">
            <Button
              type={'text'}
              shape={'default'}
              icon={
                <RedoOutlined
                  onPointerEnterCapture={() => {}}
                  onPointerLeaveCapture={() => {}}
                />
              }
              onClick={() => {
                getDataSourceReq(tableParams);
              }}
            ></Button>
          </Tooltip>
        </Space>
      }
    >
      <UniTable
        actionRef={ref}
        forceColumnsUpdate
        id={`user-data-cnt-key-table`}
        className={'user-data-cnt-key-table'}
        rowKey={'MonthlyDataId'}
        scroll={{ y: 540 }}
        bordered={true}
        loading={dataSourceLoading}
        columns={dataSourceColumns}
        dataSource={dataSource}
        clickable={false}
        toolBarRender={null}
        tableAlertRender={() => (
          <Space size={24}>
            {SummaryTableState.selectedKeys.length !== 0 && (
              <span>
                已选 {SummaryTableState.selectedKeys.length} 项
                <Space size={16}>
                  <Divider type="vertical" />
                  <Spin
                    spinning={
                      appealTaskDeleteLoading ||
                      dataSourceLoading ||
                      ConfirmTaskLoading ||
                      UnConfirmTaskLoading ||
                      ProtocolCalculateLoading ||
                      initTaskLoading
                    }
                    style={{ display: 'inlineBlock' }}
                  >
                    <a
                      style={{ marginLeft: 8 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        showModal();
                      }}
                    >
                      分组
                    </a>
                    <a
                      style={{ marginLeft: 8 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        initialState();
                      }}
                    >
                      初始化
                    </a>
                    <a
                      style={{ marginLeft: 8 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleDelete();
                      }}
                    >
                      删除
                    </a>
                    <a
                      style={{ marginLeft: 8 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleConfirmTask();
                      }}
                    >
                      锁定
                    </a>
                    <a
                      style={{ marginLeft: 8 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleUnConfirmTask();
                      }}
                    >
                      解锁
                    </a>
                  </Spin>
                </Space>
              </span>
            )}
          </Space>
        )}
        rowSelection={{
          fixed: true,
          selectedRowKeys: SummaryTableState.selectedKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            SummaryTableDispatch({
              type: TableAction.selectionChange,
              payload: {
                selectedKeys: selectedRowKeys,
                selectedRecords: selectedRows,
              },
            });
          },
          selections: [
            {
              key: 'selectAll',
              text: '全选所有',
              onSelect: async () => {
                SummaryTableDispatch({
                  type: TableAction.selectionChange,
                  payload: {
                    selectedKeys: dataSource?.map((d) => d?.MonthlyDataId),
                    selectedRecords: dataSource,
                  },
                });
              },
            },
            {
              key: 'clearAll',
              text: '清空所有',
              onSelect: async () => {
                await clearAllSelectRowKeys();
              },
            },
          ],
        }}
      />
      <TreeModal
        onModalOk={onModalOk}
        modalOpen={isModalOpen}
        onModalCancel={handleCancel}
      />
    </Card>
  );
};

export default ReportDataFlow;
