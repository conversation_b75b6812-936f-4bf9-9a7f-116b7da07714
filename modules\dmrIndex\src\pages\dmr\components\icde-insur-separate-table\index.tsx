import React from 'react';
import { Form } from 'antd';
import { Row, Col } from 'antd';
import IcdeMainTable from './components/IcdeMainTable';
import IcdeInsurTable from './components/IcdeInsurTable';
import { useInsurSeparateTableLogic } from './hooks/useInsurSeparateTableLogic';
import './index.less';

interface IcdeInsurSeparateTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];
  underConfiguration?: boolean;
  onChange?: (value: any) => void;
}

const IcdeInsurSeparateTable: React.FC<IcdeInsurSeparateTableProps> = (props) => {
  const {
    mainTableData,
    insurTableData,
    handleMainTableChange,
    handleInsurTableChange,
    syncToOriginalTable,
  } = useInsurSeparateTableLogic(props.form);

  return (
    <div className={`icde-insur-separate-container ${props.className || ''}`} style={props.style}>
      <Row gutter={16}>
        {/* 主表格 - 占14列 */}
        <Col span={14}>
          <IcdeMainTable
            {...props}
            id={`${props.id}-main`}
            parentId="diagnosisMainTable"
            dataSource={mainTableData}
            onChange={handleMainTableChange}
          />
        </Col>

        {/* 医保表格 - 占10列 */}
        <Col span={10}>
          <IcdeInsurTable
            {...props}
            id={`${props.id}-insur`}
            parentId="diagnosisInsurTable"
            dataSource={insurTableData}
            onChange={handleInsurTableChange}
          />
        </Col>
      </Row>
    </div>
  );
};

export default React.memo(IcdeInsurSeparateTable);