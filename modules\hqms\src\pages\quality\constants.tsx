import { Progress, Tag } from 'antd';
import IconBtn from '@uni/components/src/iconBtn';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';

const showGrpCnt =
  (window as any).externalConfig?.['his']?.statsShowGrpCnt ?? false;

// 新增Card 统计数据 20250428
export const NewTotalStatsColumns = [
  {
    contentData: 'FourthOperRatePatCnt',
    clickable: true,
    footerYoy: true,

    args: { FourthOperRatePatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'FourthOperRateDeathCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'FourthOperRateDeathRatio',
    rightFooterTitle: '死亡率',

    args: { FourthOperRateDeathCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'FourthOperRateOutOrDeathCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'FourthOperRateOutWithoutAdviceOrDeathRatio',
    rightFooterTitle: '死亡率',

    args: { FourthOperRateOutOrDeathCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'OperDeathCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'FourthOperRateInOperDeathRatio',
    rightFooterTitle: '死亡率',

    args: { OperDeathCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },

  {
    contentData: 'CancerOperPatCnt',
    clickable: true,
    footerYoy: true,

    args: { CancerOperPatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'CancerOperDeathCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'CancerOperDeathRatio',
    rightFooterTitle: '死亡率',

    args: { CancerOperDeathCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },

  {
    contentData: 'H48BackInCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'H48BackInRatio',
    rightFooterTitle: '再入院率',

    args: { H48BackInCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'TotalCnt',
    clickable: true,
    footerYoy: true,

    args: { TotalCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'MainOperClass1WoundPatCnt',
    clickable: true,
    footerYoy: true,

    args: { MainOperClass1WoundPatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'MainOperClass1WoundAntiPatCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'MainOperClass1WoundAntiPatRatio',
    rightFooterTitle: '使用率',

    args: { MainOperClass1WoundAntiPatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },

  {
    contentData: 'OperPatCnt',
    clickable: true,
    footerYoy: true,

    args: { OperPatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'UnexpectedBackInOperPatCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'UnexpectedBackInOperPatRatio',
    rightFooterTitle: '再入院率',

    args: { UnexpectedBackInOperPatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  //
  {
    contentData: 'HqmsOperPatCnt',
    clickable: true,
    footerYoy: true,

    args: { HqmsOperPatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'BackInFourthOperRatePatCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'BackInFourthOperRatePatRatio',
    rightFooterTitle: '再手术率',

    args: { BackInFourthOperRatePatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'FourOrMoreFourthOperRatePatCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'FourOrMoreFourthOperRatePatRatio',
    rightFooterTitle: '比例',

    args: { FourOrMoreFourthOperRatePatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'SixOrMoreOperPatCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'SixOrMoreOperPatRatio',
    rightFooterTitle: '占比',

    args: { SixOrMoreOperPatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'FifteenOrMoreDiagPatCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'FifteenOrMoreDiagPatRatio',
    rightFooterTitle: '占比',

    args: { FifteenOrMoreDiagPatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  // 123
  {
    contentData: 'PeriOperDeathCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'PeriOperDeathRatio',
    rightFooterTitle: '围手术期患者死亡率',

    args: { PeriOperDeathCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'IcdLowRiskPatCnt',
    clickable: true,
    footerYoy: true,

    args: { IcdLowRiskPatCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'IcdLowRiskDeathCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'IcdLowRiskDeathRatio',
    rightFooterTitle: '死亡率',

    args: { IcdLowRiskDeathCntFlag: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
];
// 以下为新版本
// 可点击Stat
export const TotalStatsColumns = [
  {
    contentData: 'SelectiveSurgeryForComplicationPatCnt',
    clickable: true,
    footerYoy: true,

    args: { IsSelectiveSurgeryForComplication: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  ...NewTotalStatsColumns,
  {
    contentData: 'OperComplicationPatCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'OperComplicationPatRatio',
    rightFooterTitle: '发生率',

    args: { IsSelectiveSurgery: true, IsOperComplication: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'Class1WoundPatCnt',
    clickable: true,
    footerYoy: true,

    args: { IsClass1Wound: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'Class1WoundInfectionPatCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'Class1WoundInfectionPatRatio',
    rightFooterTitle: '感染率',

    args: { IsClass1WoundInfection: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'DeathCnt',
    clickable: true,
    footerYoy: true,

    rightFootValue: 'DeathRatio',
    rightFooterTitle: '死亡率',

    args: { Dead: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
  {
    contentData: 'UnexpectedBackInCnt',
    clickable: true,
    footerYoy: true,

    args: { IsUnexpectedBackInCnt: true },
    detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
  },
];

export const TabCommonItems = {
  statistic: { key: 'statistic', title: '全院综合分析' },
  majorPerfDeptAnalysis: {
    key: 'major_perf_dept_analysis',
    title: '科室综合分析',
  },
  medTeamAnalysis: { key: 'medteam_analysis', title: '医疗组综合分析' },
  doctorAnalysis: { key: 'doctor_analysis', title: '医生综合分析' },
};

export const ScoreColumns = [
  {
    dataIndex: 'operation',
    visible: true,
    width: 40,
    align: 'center',
    title: '',
    order: 0,
    fixed: 'left',
    render: (node, record, index, action) => {
      let url = '';
      switch (record.IndicatorName) {
        case 'OperPatCntRatio':
          url = '/uniHqms/oper/hosp';
          break;
        case 'Grade4OperPatRatio':
          url = '/uniHqms/oper/hosp';
          break;
        case 'MicrosurgeryPatRatio':
          url = '/uniHqms/oper/hosp';
          break;
        case 'Class1WoundInfectionPatRatio':
          url = '/uniHqms/quality/hosp';
          break;
        case 'LowRiskDeathRatio':
          url = '/uniHqms/quality/hosp';
          break;
        case 'OperComplicationPatRatio':
          url = '/uniHqms/operComplicationComposition/hosp';
          break;
        case 'Sd':
          url = '/uniHqms/sd/hosp';
          break;
        default:
          url = '/'; // TODO?
      }
      return (
        <IconBtn
          type="check"
          onClick={() => {
            window.open(url);
          }}
        />
      );
    },
  },
  {
    dataIndex: 'SdName',
    visible: false,
  },
  {
    dataIndex: 'Score',
    visible: false,
  },
  {
    dataIndex: 'Deduction',
    visible: false,
  },
  {
    dataIndex: 'DisplayName',
    order: 1,
  },
  {
    dataIndex: 'Stats',
    dataType: 'Percent',
    order: 2,
  },
  {
    dataIndex: 'FullMark',
    dataType: 'Percent',
    order: 3,
  },
  {
    dataIndex: 'Median',
    dataType: 'Percent',
    order: 4,
  },
  {
    dataIndex: 'TotalPoints',
    order: 5,
  },
  {
    dataIndex: 'Progress',
    title: '国考评分预警',
    width: 180,
    visible: true,
    order: 6,
    render: (node, record, index) => {
      if (node) {
        let progress = <></>;
        if (record.Score && record.TotalPoints) {
          let totalPoints = record.TotalPoints, //总分
            score = record.Score, //分数
            ratio = score / totalPoints, //占比
            width = ratio * 100 || 0;
          if (ratio >= 0.85) {
            progress = (
              <Progress percent={width} size={'small'} strokeColor="#18ba56" />
            );
          } else if (ratio >= 0.6 && ratio < 0.85) {
            progress = (
              <Progress percent={width} size={'small'} strokeColor="#f4a741" />
            );
          } else if (ratio >= 0 && ratio < 0.6) {
            progress = (
              <Progress percent={width} size={'small'} strokeColor="#eb5757" />
            );
          }
          return progress;
        }
      }

      return <span>-</span>;
    },
  },
];

export const SuspectedCntColumns = (params) => [
  {
    dataIndex: 'operation',
    visible: true,
    width: 40,
    align: 'center',
    title: '',
    order: 0,
    fixed: 'left',
    render: (node, record, index, action) => {
      return (
        <IconBtn
          type="check"
          onClick={() => {
            window.open(
              `/uniHqms/sd/hosp?versionedSdcode=${record.VersionedSdCode}`,
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'SdName',
    visible: true,
    order: 0,
  },
  {
    dataIndex: 'Score',
    visible: false,
  },
  {
    dataIndex: 'Deduction',
    visible: false,
  },
  {
    dataIndex: 'DisplayName',
    order: 1,
  },
  {
    dataIndex: 'Stats',
    order: 2,
    render: (node, record, index) => {
      if (
        (record.IndicatorName === 'PatCnt' && record.FullMark > record.Stats) ||
        (record.IndicatorName !== 'PatCnt' && record.FullMark < record.Stats)
      ) {
        return <span className="font-danger">{record.Stats}</span>;
      } else return <span>{record.Stats}</span>;
    },
  },
  {
    dataIndex: 'FullMark',
    order: 3,
  },
  {
    dataIndex: 'Median',
    order: 4,
  },
  {
    dataIndex: 'TotalPoints',
    visible: false,
  },
  {
    dataIndex: 'Description',
    visible: false,
  },
  {
    dataIndex: 'SdAnomaly',
    title: '潜在/异常单病种病例',
    width: 180,
    visible: true,
    order: 6,
    render: (node, record, index) => {
      let title = '',
        content = '',
        newParams = params,
        url = 'Hqms/Details/HqmsSdDetails',
        sdName = record?.SdName,
        suspectedCnt = record?.SuspectedCnt,
        displayName = record.DisplayName;
      if (node && suspectedCnt) {
        switch (record.IndicatorName) {
          case 'PatCnt':
            title = `全院潜在${sdName}病例明细`;
            content = `潜在病例`;
            url = 'Hqms/Details/HqmsSuspectedSdDetails';
            break;
          case 'OperComplicationPatRatio':
            title = `全院${sdName}并发症病例明细`;
            content = `并发症病例`;
            break;
          case 'DeathRatio':
            title = `全院${sdName}死亡病例明细`;
            content = `死亡病例`;
            break;
          default:
            title = `全院${sdName}${displayName}过高病例明细`;
            content = `${displayName}过高`;
            break;
        }

        switch (record.IndicatorName) {
          case 'AvgTotalFee':
            newParams = {
              ...params,
              TotalFeeStd: record.FullMark,
            };
            break;
          case 'AvgInPeriod':
            newParams = {
              ...params,
              InPeriodStd: record.FullMark,
            };
            break;
          case 'DeathRatio':
            newParams = {
              ...params,
              Dead: true,
            };
            break;
          case 'OperComplicationPatRatio':
            newParams = {
              ...params,
              IsSelectiveSurgeryForComplication: true,
              IsOperComplication: true,
            };
            break;
        }

        return (
          <>
            <span>
              <Tag color="">
                {content}：{suspectedCnt}
              </Tag>
              <IconBtn
                type="details"
                title={title}
                onClick={(e) => {
                  e.stopPropagation();
                  Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                    title: title,
                    args: {
                      ...newParams,
                      VersionedSdCodes: [record?.VersionedSdCode],
                    },
                    type: 'drg',
                    detailsUrl: url,
                    // dictData: globalState?.dictData, // 传入
                  });
                }}
              />
            </span>
          </>
        );
      }

      return <span>-</span>;
    },
  },
];
