import { <PERSON>, Col, Row, Divider, But<PERSON>, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import Stats from '@/components/stats';
import CardEchart from '@uni/components/src/cardEchart';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  GrpDefaultOpts,
  GrpQuadrantAxisOpts,
  AdrgQuadrantAxisOpts,
  AdrgDefaultOpts,
} from '@/pages/dip/optsConstants';
import { SettleCompStatsSelectedTrendsLineOption } from '@/pages/dip/chart.opts';
import {
  DeptTotalStatsColumns,
  SettleCompStatsByGrpColumns,
  TabCommonItems,
} from '@/pages/dip/constants';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import FeeCompositionAndAnalysis from '@/components/feeCompositionAndAnalysis';
import GradientChartAndTable from '@/components/gradientChartAndTable';
import IconBtn from '@uni/components/src/iconBtn';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import {
  HospTotalStatsColumns,
  HospGainLossProfitStatsColumns,
  SettleCompStatsByCliDeptColumns,
} from '../constants';
import DrawerCardInfo from '@/pages/dip/components/drawerCardInfo';
import { StatsDetailColumns } from '../components/stats/constants';
import GradientChartAndTableAndPie from '@/components/gradientChartAndTableAndPie/index';

const DrgMajorPerfDeptAnalysis = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, MajorPerfDepts, insurType } =
    globalState?.searchParams;

  // tab
  const [activeKey, setActiveKey] = useState('statistic');

  const [requestParams, setRequestParams] = useState<any>({});
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedStatItem, setSelectedStatItem] = useState({
    contentData: 'Profit',
    dataType: 'Currency',
    title: '盈亏',
  });

  // 趋势 Columns
  const { data: settleCompStatsTrendColumnsData } = useRequest(
    () =>
      uniCommonService(
        `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsTrend`,
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      ),
    {
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  // 趋势 Data
  const {
    data: settleCompStatsTrendData,
    loading: getSettleCompStatsTrendLoading,
    run: getSettleCompStatsTrendReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsTrend`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 概况
  const {
    data: settleCompStatsOfCliDeptData,
    loading: getSettleCompStatsOfCliDeptLoading,
    run: getSettleCompStatsOfCliDeptReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsOfMajorPerfDept`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 处理 header data
  useEffect(() => {
    if (dateRange?.length && MajorPerfDepts?.length) {
      let tableParams = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        MajorPerfDepts,
        insurType,
      };
      setRequestParams(tableParams);
    }
  }, [dateRange, hospCodes, MajorPerfDepts, insurType]);

  // 处理完header data后 调接口
  useEffect(() => {
    if (Object.keys(requestParams)?.length) {
      getSettleCompStatsOfCliDeptReq(requestParams);
      getSettleCompStatsTrendReq(requestParams);
    }
  }, [requestParams]);

  // stat click 由Component Stats传入
  useEffect(() => {
    Emitter.on(EventConstant.STAT_ON_CLICK_EMITTER, (record) => {
      setSelectedStatItem(record);
    });
    return () => {
      Emitter.off(EventConstant.STAT_ON_CLICK_EMITTER);
    };
  }, []);

  let tabItems = [
    {
      key: TabCommonItems.statistic.key,
      label: TabCommonItems.statistic.title,
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={12} xl={11}>
            <Row gutter={[16, 16]}>
              <Stats
                level="majorPerfDept"
                api={`Api/FundSupervise/LatestDipSettleStats/SettleCompStatsOfMajorPerfDept`}
                columns={DeptTotalStatsColumns} // 先用科室的
                type="col-xl-6"
                outerTableParams={requestParams}
                onClickEmitter={EventConstant.STAT_ON_CLICK_EMITTER}
              />
            </Row>
          </Col>

          <Col xs={24} sm={24} md={24} lg={12} xl={13}>
            <CardEchart
              title={<>学科月度变化趋势</>}
              height={430}
              dictData={globalState.dictData}
              elementId="Trend"
              loading={getSettleCompStatsTrendLoading}
              options={
                (settleCompStatsTrendData !== 'apiErr' &&
                  settleCompStatsTrendData &&
                  SettleCompStatsSelectedTrendsLineOption(
                    settleCompStatsTrendData,
                    'MonthDate',
                    selectedStatItem,
                  )) ||
                {}
              }
              needExport={true}
              exportTitle={'学科月度变化趋势'}
              exportData={settleCompStatsTrendData}
              exportColumns={settleCompStatsTrendColumnsData}
              needModalDetails={true}
              onRefresh={() => {
                getSettleCompStatsTrendReq(requestParams);
              }}
            ></CardEchart>
          </Col>
        </Row>
      ),
    },
    {
      key: TabCommonItems.feeAnalysis.key,
      label: TabCommonItems.feeAnalysis.title,
      children: (
        <FeeCompositionAndAnalysis
          api={`Api/FundSupervise/LatestDipSettleStats/FeeChargeDistribution`}
          requestParams={requestParams}
          tabKey={activeKey}
        />
      ),
    },
    {
      key: TabCommonItems.drgAnalysis.key,
      label: TabCommonItems.drgAnalysis.title,
      children: (
        <Row gutter={[16, 16]}>
          <GradientChartAndTableAndPie
            args={{
              level: 'majorPerfDept',
              type: 'grp',
              title: '病组效率',
              category: 'ChsDrgName',
              columns: [
                {
                  dataIndex: 'operation',
                  visible: true,
                  width: 40,
                  align: 'center',
                  order: 1,
                  fixed: 'left',
                  title: '',
                  render: (node, record, index) => {
                    return (
                      <IconBtn
                        type="details"
                        onClick={(e) => {
                          e.stopPropagation();
                          Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                            id: 'dip-clidept-settle-stats-by-drp', // 要匹配到对应的DetailTableModal id
                            title: `${record?.ChsDrgName}`,
                            args: {
                              ...requestParams,
                              VersionedChsDrgCodes: record?.VersionedChsDrgCode
                                ? [record?.VersionedChsDrgCode || undefined]
                                : [],
                            },
                            type: 'dip',
                            detailsUrl:
                              'FundSupervise/LatestDipSettleStats/SettleDetails',
                            dictData: globalState?.dictData, // 传入
                          });
                        }}
                      />
                    );
                  },
                },
                ...SettleCompStatsByGrpColumns,
              ],
              clickable: true,
              detailsTitle: '病组分布',
              axisOpts: GrpQuadrantAxisOpts,
              defaultAxisOpt: GrpDefaultOpts,
              api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByGrp`,
            }}
            requestParams={requestParams}
            tabKey={activeKey}
          />
        </Row>
      ),
    },
    {
      key: TabCommonItems.majorPerfDeptAnalysis.key,
      label: '学科对比分析',
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTable
                args={{
                  level: 'majorPerfDept',
                  clickable: false,
                  cols: 'col-xl-24',
                  title: '学科对比分析',
                  category: 'MajorPerfDeptName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      fixed: 'left',
                      order: 1,
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                title: `${record?.MajorPerfDeptName}`,
                                args: {
                                  ...requestParams,
                                  MajorPerfDepts: record?.MajorPerfDept
                                    ? [record?.MajorPerfDept]
                                    : [''],
                                },
                                type: 'dip',
                                detailsUrl:
                                  'FundSupervise/LatestDipSettleStats/SettleDetails',
                                dictData: globalState?.dictData, // 传入
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByCliDeptColumns,
                  ],
                  // detailsTitle: '科室分布',
                  // defaultAxisOpt: CliDeptDefaultOpts,
                  // axisOpts: CliDeptQuadrantAxisOpts,
                  api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByMajorPerfDept`,
                }}
                noChart={true}
              />
            </Col>
          </Row>
        </>
      ),
    },
    {
      key: TabCommonItems.deptAnalysis.key,
      label: '科室对比分析',
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTable
                args={{
                  clickable: false,
                  cols: 'col-xl-24',
                  title: '科室对比分析',
                  category: 'CliDeptName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      fixed: 'left',
                      order: 1,
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                title: `${record?.CliDeptName}`,
                                args: {
                                  ...requestParams,
                                  CliDepts: record?.CliDept
                                    ? [record?.CliDept]
                                    : ['%'],
                                },
                                type: 'dip',
                                detailsUrl:
                                  'FundSupervise/LatestDipSettleStats/SettleDetails',
                                dictData: globalState?.dictData, // 传入
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByCliDeptColumns,
                  ],
                  // detailsTitle: '科室分布',
                  // defaultAxisOpt: CliDeptDefaultOpts,
                  // axisOpts: CliDeptQuadrantAxisOpts,
                  api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByCliDept`,
                  level: 'majorPerfDept',
                }}
                noChart={true}
              />
            </Col>
          </Row>
        </>
      ),
    },
  ];

  return (
    <Card>
      <Tabs
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={globalState.dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'dip',
          });
        }}
      />
      <DrawerCardInfo
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </Card>
  );
};

export default DrgMajorPerfDeptAnalysis;
