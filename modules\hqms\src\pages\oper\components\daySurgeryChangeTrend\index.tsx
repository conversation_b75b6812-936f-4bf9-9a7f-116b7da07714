import CardWithBtns from '@uni/components/src/cardWithBtns/index';
import UniEcharts from '@uni/components/src/echarts/index';
import { Col } from 'antd';
import { hqmsDaySurgeryTrendOfEntityLine } from '../../chart.opts';
import { useEffect, useState } from 'react';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src/commonService';
import { RespVO } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';

const DaySurgeryChangeTrend = ({ tableParams, api }) => {
  const {
    data: ColumnsData,
    loading: getColumnsLoading,
    mutate: mutateColumns,
    run: getColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(api, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  const {
    data: Data,
    loading: getDataLoading,
    run: getDataReq,
  } = useRequest(
    (data) =>
      uniCommonService(api, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // columns
  useEffect(() => {
    if (api) {
      if (!ColumnsData?.length) getColumnsReq();
    }
  }, [api]);

  // data
  useEffect(() => {
    if (tableParams) {
      getDataReq(tableParams);
    }
  }, [tableParams]);

  return (
    <Col xs={24} sm={24} md={24} lg={12} xl={12}>
      <CardWithBtns
        title="日间占择期手术变化趋势"
        content={
          <UniEcharts
            height={250}
            elementId="Line_CMI"
            loading={getDataLoading}
            options={
              (Data?.length > 0 &&
                hqmsDaySurgeryTrendOfEntityLine(
                  Data?.map((d) => ({
                    ...d,
                    MonthDate: valueNullOrUndefinedReturnDash(
                      d?.MonthDate,
                      'Month',
                    ),
                  })),
                  'MonthDate',
                )) ||
              {}
            }
          />
        }
        needExport={true}
        exportTitle={'日间占择期手术变化趋势'}
        exportData={Data}
        exportColumns={ColumnsData}
        needModalDetails={true}
        onRefresh={() => {
          getDataReq(tableParams);
        }}
        columnsEditableUrl={api}
        onColumnChange={(newColumns) => {
          mutateColumns(tableColumnBaseProcessor([], newColumns));
        }}
      />
    </Col>
  );
};

export default DaySurgeryChangeTrend;
