import { SearchFormOptType } from '@/components/searchForm';
import { SearchOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
} from '@uni/components/src/pro-form';
import dayjs from 'dayjs';

const errorLevelOpts = [
  { Name: '无错误', Code: '0' },
  { Name: '提示性规则错误', Code: '2' },
  { Name: '强制性规则错误', Code: '5' },
];

export const SearchOpts: (
  initialValue: {
    SearchKeyWord;
    dateRange;
    UseRegistDate;
    HospCodes;
    HospCode;
    CliDepts;
    OutWards;
    OutTypes;
    YlfkfSs;
    Coder;
    ErrorLevels;
  },
  { hospOpts, deptOpts, wardOpts, outTypeOpts, ylfkfsOpts, coderOpts },
  hideInSearchPage: boolean,
) => SearchFormOptType[] = (
  {
    SearchKeyWord,
    dateRange,
    UseRegistDate,
    HospCodes,
    HospCode,
    CliDepts,
    OutWards,
    OutTypes,
    ErrorLevels,
    YlfkfSs,
    Coder,
  },
  { hospOpts, deptOpts, wardOpts, outTypeOpts, ylfkfsOpts, coderOpts },
  hideInSearchPage,
) => [
  {
    title: '编码员',
    dataType: 'select',
    name: 'Coder',
    opts: coderOpts,
    initialValue: Coder,
    fieldProps: {
      disabled: true,
    },
  },
  {
    dataType: 'text',
    title: '病案标识',
    placeholder: '病案号/姓名/住院号/条形码',
    name: 'SearchKeyWord',
    // labelCol: { span: 0 },
    // wrapperCol: { span: 24 },
    // fieldProps: {
    //   suffix: <SearchOutlined />,
    // },
  },
  {
    dataType: 'DateRange.Group',
    name: 'DateRange.Group',
    render: (
      <ProForm.Group>
        <ProFormSelect
          name={['UseRegistDate']}
          label="时间"
          options={[
            { label: '按出院时间', value: 'Normal' },
            { label: '按登记时间', value: 'UseRegistDate' },
          ]}
          initialValue={UseRegistDate ? 'UseRegistDate' : 'Normal'}
          fieldProps={{
            allowClear: false,
          }}
        />
        <ProFormDateRangePicker
          wrapperCol={{ offset: 6 }}
          {...{
            key: 'DateRange',
            name: 'DateRange',
            label: '',
            style: { width: '100%' },
            initialValue: dateRange,
            transform: (values) => {
              return {
                Sdate: values
                  ? dayjs(values[0]).format('YYYY-MM-DD')
                  : undefined,
                Edate: values
                  ? dayjs(values[1]).format('YYYY-MM-DD')
                  : undefined,
              };
            },
            fieldProps: {
              style: { width: '100%' },
              format: 'YYYY-MM-DD',
            },
          }}
        />
      </ProForm.Group>
    ),
  },
  // {
  //   title: '出院日期',
  //   dataType: 'dateRange',
  //   name: 'DateRange',
  //   initialValue: dateRange,
  //   transform: (values) => {
  //     return {
  //       Sdate: values
  //         ? dayjs(values[0]).startOf('M').format('YYYY-MM-DD')
  //         : undefined,
  //       Edate: values
  //         ? dayjs(values[1]).endOf('M').format('YYYY-MM-DD')
  //         : undefined,
  //     };
  //   },
  //   fieldProps: {
  //     picker: 'month',
  //     format: 'YYYY-MM',
  //   },
  //   render: (
  //     <ProFormDependency name={['UseRegistDate']}>
  //       {({ UseRegistDate }) => {
  //         return (
  //           <ProFormDateRangePicker
  //             {...{
  //               key: 'DateRange',
  //               name: 'DateRange',
  //               label: UseRegistDate ? '登记日期' : '出院日期',
  //               initialValue: dateRange,
  //               transform: (values) => {
  //                 return {
  //                   Sdate: values
  //                     ? dayjs(values[0]).format('YYYY-MM-DD')
  //                     : undefined,
  //                   Edate: values
  //                     ? dayjs(values[1]).format('YYYY-MM-DD')
  //                     : undefined,
  //                 };
  //               },
  //               fieldProps: {
  //                 format: 'YYYY-MM-DD',
  //               },
  //             }}
  //           />
  //         );
  //       }}
  //     </ProFormDependency>
  //   ),
  // },
  // {
  //   // title: '',
  //   dataType: 'checkboxGroup',
  //   name: 'UseRegistDate',
  //   // addonAfter: '登记日期查询',
  //   options: ['登记日期查询'],
  //   initialValue: UseRegistDate,
  //   transform: (values) => {
  //     return {
  //       UseRegistDate: values?.length ? true : false,
  //     };
  //   },
  //   labelCol: { span: 0 },
  //   wrapperCol: { span: 16, offset: 7 },
  // },
  {
    title: '院区',
    dataType: 'select',
    name: 'HospCode',
    initialValue: HospCodes ?? HospCode,
    opts: hospOpts,
    fieldProps: {
      mode: 'multiple',
    },
  },
  {
    title: '科室',
    dataType: 'select',
    name: 'CliDepts',
    initialValue: CliDepts,
    opts: deptOpts,
    fieldProps: {
      mode: 'multiple',
    },
  },
  {
    title: '病区',
    dataType: 'select',
    name: 'OutWards',
    initialValue: OutWards,
    opts: wardOpts,
    fieldProps: {
      mode: 'multiple',
    },
  },
  {
    title: '离院方式',
    dataType: 'select',
    name: 'OutTypes',
    opts: outTypeOpts,
    initialValue: OutTypes,
    fieldProps: {
      mode: 'multiple',
    },
    hidden: hideInSearchPage,
  },
  // {
  //   title: '付费方式',
  //   dataType: 'select',
  //   name: 'YlfkfSs',
  //   opts: ylfkfsOpts,
  //   initialValue: YlfkfSs,
  //   fieldProps: {
  //     mode: 'tags',
  //   },
  //   hidden: hideInSearchPage,
  // },
  {
    title: '质控等级',
    dataType: 'select',
    name: 'ErrorLevels',
    opts: errorLevelOpts,
    initialValue: ErrorLevels,
    fieldProps: {},
  },
];
