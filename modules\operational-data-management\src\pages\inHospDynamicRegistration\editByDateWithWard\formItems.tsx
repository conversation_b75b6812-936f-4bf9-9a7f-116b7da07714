import dayjs from 'dayjs';
import { ProFormDependency, ProFormSelect } from '@uni/components/src/pro-form';
import { bulkFormItems, byDateformItems } from '../constants';

export const SearchFormItems = (hospList) => [
  {
    name: 'ExactDate',
    title: '日期',
    dataType: 'date',
    colProps: { span: 12 },
    rules: [{ required: true }],
    fieldProps: {
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
    },
  },
  {
    name: 'HospCode',
    title: '院区',
    dataType: 'select',
    rules: [{ required: true }],
    fieldProps: {
      mode: 'multiple',
      // mode: 'single',
    },
    visible: true,
    opts: hospList,
    colProps: { span: 12 },
  },
];

// 单条新建
export const CreateFormItems = (hospList, deptList) => [
  {
    name: 'ExactDate',
    title: '日期',
    dataType: 'date',
    colProps: { span: 8 },
    rules: [{ required: true }],
    // transform: (values) => {
    //   return {
    //     ExactDate: values ? dayjs(values[0]).format('YYYY-MM-DD') : undefined,
    //   };
    // },
    fieldProps: {
      style: { width: '100%' },
      disabled: true,
      format: 'YYYY-MM-DD',
    },
  },
  {
    name: 'HospCode',
    title: '院区',
    dataType: 'select',
    rules: [{ required: true }],
    // fieldProps: {
    //   mode: 'single',
    // },
    disabled: true,
    visible: true,
    opts: hospList,
    colProps: { span: 8 },
  },
  {
    name: 'DeptCode',
    title: '科室(基于院区)',
    dataType: 'Custom',
    visible: true,
    render: (
      <ProFormDependency name={['HospCode']}>
        {({ HospCode }) => {
          let options = deptList?.filter(
            (item) => HospCode == item?.ExtraProperties?.HospCode,
          );

          return (
            <ProFormSelect
              {...{
                key: 'DeptCode',
                name: 'DeptCode',
                label: '科室(基于院区)',
                // initialValue: dateRange,
                rules: [{ required: true }],
                colProps: { span: 8 },
                fieldProps: {
                  placeholder: '请选择',
                  options: options,
                  fieldNames: {
                    label: 'Name',
                    value: 'Code',
                  },
                  // labelInValue: false,
                },
              }}
            />
          );
        }}
      </ProFormDependency>
    ),
  },
  ...byDateformItems?.map((d) => {
    return {
      ...d,
      dataType:
        d?.dataType === 'Integer' || d?.dataType === 'Number'
          ? 'number'
          : 'text',
      fieldProps: {
        precision: d?.dataType === 'Integer' ? 0 : 2,
      },
      min: 0,
      colProps: { span: 6 },
    };
  }),
];

// 批量修改床位
export const BulkChangeFormItems = (hospList, wardList) => [
  // {
  //   name: 'ExactDate',
  //   title: '日期',
  //   dataType: 'date',
  //   colProps: { span: 12 },
  //   rules: [{ required: true }],
  //   // transform: (values) => {
  //   //   return {
  //   //     ExactDate: values ? dayjs(values[0]).format('YYYY-MM-DD') : undefined,
  //   //   };
  //   // },
  //   fieldProps: {
  //     style: { width: '100%' },
  //     format: 'YYYY-MM-DD',
  //     disabled: true,
  //   },
  // },
  {
    name: 'DateRange',
    title: '时间',
    dataType: 'dateRange',
    colProps: { span: 24 },
    rules: [{ required: true }],
    transform: (values) => {
      return {
        Sdate: values ? dayjs(values[0]).format('YYYY-MM-DD') : undefined,
        Edate: values ? dayjs(values[1]).format('YYYY-MM-DD') : undefined,
      };
    },
    fieldProps: {
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
    },
  },
  {
    name: 'HospCode',
    title: '院区',
    dataType: 'select',
    rules: [{ required: true }],
    // disabled: true,
    opts: hospList,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
    visible: true,
    colProps: { span: 24 },
  },
  {
    name: 'DeptCode',
    title: '病区',
    dataType: 'select',
    rules: [{ required: true }],
    opts: wardList,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
    visible: true,
    colProps: { span: 24 },
  },
  ...bulkFormItems?.map((d) => {
    return {
      ...d,
      dataType:
        d?.dataType === 'Integer' || d?.dataType === 'Number'
          ? 'number'
          : 'text',
      fieldProps: {
        precision: d?.dataType === 'Integer' ? 0 : 2,
      },
      min: 0,
      colProps: { span: 12 },
    };
  }),
];

// 下载模板
export const DownloadTemplateFormItems = [
  {
    name: 'fileName',
    title: '文件名称',
    dataType: 'text',
    rules: [{ required: true }],
    // fieldProps: {
    //   mode: 'single',
    // },
    visible: true,
    colProps: { span: 24 },
  },
  {
    name: 'containsData',
    title: '是否携带当前数据',
    dataType: 'switch',
    visible: true,
    colProps: { span: 24 },
  },
];
