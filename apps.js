const apps = [
  {
    name: 'external',
    entry:
      process.env.NODE_ENV === 'production' ? '/external/' : '//localhost:8009',
    bundle: true,
  },
  {
    name: 'dmrIndex',
    entry: process.env.NODE_ENV === 'production' ? '/dmr/' : '//localhost:8002',
    bundle: true,
  },
  {
    name: 'quality-examine',
    entry:
      process.env.NODE_ENV === 'production'
        ? '/qualityExamine/'
        : '//localhost:8012',
    bundle: true,
  },
  {
    name: 'insurance-settlement',
    entry: process.env.NODE_ENV === 'production' ? '/chs/' : '//localhost:8003', //'//************:8003',
    bundle: true,
  },
  {
    // 模块中 publicPath 估计打包的时候用到
    name: 'quality-control',
    entry:
      process.env.NODE_ENV === 'production'
        ? '/qualityControl/'
        : '//localhost:8004',
    bundle: true,
  },
  {
    name: 'energy-efficiency-management',
    entry:
      process.env.NODE_ENV === 'production'
        ? '/energyEfficiencyManagement/'
        : '//localhost:8951',
    bundle: true,
  },
  {
    name: 'hqms',
    entry:
      process.env.NODE_ENV === 'production' ? '/uniHqms/' : '//localhost:8005',
    bundle: true,
  },
  {
    name: 'grade',
    entry:
      process.env.NODE_ENV === 'production' ? '/grade/' : '//localhost:8010',
    bundle: true,
  },

  {
    name: 'report',
    entry:
      process.env.NODE_ENV === 'production' ? '/report/' : '//localhost:8006',
    bundle: true,
  },

  {
    name: 'stats-analysis',
    entry:
      process.env.NODE_ENV === 'production'
        ? '/statsAnalysis/'
        : '//localhost:8007',
    bundle: true,
  },
  // 医院数据运营管理
  {
    name: 'operational-data-management',
    entry:
      process.env.NODE_ENV === 'production'
        ? '/operationalDataManagement/'
        : '//localhost:8099',
    bundle: true,
  },
  // 示踪
  {
    name: 'tracer',
    entry:
      process.env.NODE_ENV === 'production' ? '/tracer/' : '//localhost:8098',
    bundle: true,
  },
  // DRG数据管理
  {
    name: 'drg-data-manager',
    entry:
      process.env.NODE_ENV === 'production'
        ? '/drgDataManager/'
        : '//localhost:8022',
    bundle: true,
  },
  // drg医院决策支持
  {
    name: 'drg-hosp-decision-support',
    entry:
      process.env.NODE_ENV === 'production'
        ? '/drgHospDecisionSupport/'
        : '//localhost:8097',
    bundle: true,
  },
  // 导入
  {
    name: 'info-input',
    entry:
      process.env.NODE_ENV === 'production'
        ? '/infoImport/'
        : '//localhost:8096',
    bundle: true,
  },
  // 维护
  {
    name: 'report-sys',
    entry:
      process.env.NODE_ENV === 'production'
        ? '/reportSys/'
        : '//localhost:8095',
    bundle: true,
  },
  // 医保基金监控
  {
    name: 'medical-insurance-fund-monitor',
    entry:
      process.env.NODE_ENV === 'production'
        ? '/medicalInsuranceFundMonitor/'
        : '//localhost:8094',
    bundle: false,
  },
  // wiki
  {
    name: 'wiki',
    entry:
      process.env.NODE_ENV === 'production' ? '/wiki/' : '//localhost:8008',
    bundle: true,
  },
];

module.exports = {
  apps,
};
