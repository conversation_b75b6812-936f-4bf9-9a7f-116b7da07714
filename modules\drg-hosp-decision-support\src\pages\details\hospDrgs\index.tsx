import { Reducer, useEffect, useReducer, useState } from 'react';
import { Dispatch, useDispatch, useModel, useSelector } from 'umi';
import { UniTable } from '@uni/components/src';
import { Card, Space, TableProps } from 'antd';
import { useSafeState } from 'ahooks';
import { IReducer, ITableState } from '@uni/reducers/src/interface';
import { RespType, isRespErr } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType } from '@/constants';
import { InitTableState, TableAction, tableReducer } from '@uni/reducers/src';
import { SorterResult } from 'antd/lib/table/interface';
import { SwagHospDrgsItem } from './interface';
import IconBtn from '@uni/components/src/iconBtn/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';
import ExportIconBtn from '@uni/components/src/backend-export';
import { addExportToLastSegment } from '@uni/utils/src/widgets';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';

const HospDrgs = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster');
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  let { versionedDrgCodes, MainIcdeCode, FirstOperCode, CliDepts, uniqueId } =
    searchParams;

  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagHospDrgsItem>, IReducer>
  >(tableReducer, InitTableState);

  const [backPagination, setBackPagination] = useSafeState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    hideOnSinglePage: false,
  });

  const backTableOnChange: TableProps<any>['onChange'] = async (
    pagi,
    filter,
    sorter,
    extra,
  ) => {
    tableReq(
      searchParams,
      pagi.current,
      pagi.pageSize,
      sorter as SorterResult<SwagHospDrgsItem>,
    );
  };

  // 普通的tableReq
  const tableReq = async (
    params,
    cur = 1,
    size = 10,
    sorter = SearchTable.sorter,
  ) => {
    let res: (any | RespType)[] = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: ReqActionType.HospDrgsData,
        requestParams: [
          {
            name: ReqActionType.HospDrgsData,
            url: `Api/v2/Drgs/${ReqActionType.HospDrgsData}`,
            method: 'POST',
            data: {
              Sdate: params?.dateRange[0],
              Edate: params?.dateRange[1],
              VersionedDrgCodes:
                versionedDrgCodes?.length ?? 0 > 0
                  ? versionedDrgCodes
                  : undefined,
              MainIcdeCode: MainIcdeCode || undefined,
              FirstOperCode: FirstOperCode || undefined,
              uniqueId: uniqueId || undefined,
              CliDepts: CliDepts?.length ? CliDepts : undefined,
              HospCode:
                params?.hospCodes?.length ?? 0 > 0
                  ? Array.isArray(params?.hospCodes)
                    ? params?.hospCodes
                    : [params?.hospCodes]
                  : ['%'],
              current: cur,
              pageSize: size,
              //   sorting: sortingHandler(sorter), TODO
            },
          },
        ],
      },
    });
    if (!isRespErr(res?.at(0))) {
      let total = res.at(0)?.total;
      SearchTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res.at(0).data ?? [],
        },
      });

      // sorter
      if (!_.isEqual(sorter, SearchTable.sorter)) {
        SearchTableDispatch({
          type: TableAction.sortChange,
          payload: { sorter },
        });
      }

      setBackPagination({
        ...backPagination,
        current: cur,
        pageSize: size,
        total: total ?? 0,
      });
    }
  };

  useEffect(() => {
    tableReq(searchParams);
  }, [searchParams]);

  // columns处理
  useEffect(() => {
    if (
      columnsList[ReqActionType.HospDrgsData] &&
      SearchTable.columns.length < 1
    ) {
      SearchTableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: tableColumnBaseProcessor(
            [],
            columnsList[ReqActionType.HospDrgsData],
          ),
        },
      });
    }
  }, [columnsList?.[ReqActionType.HospDrgsData]]);

  // columns 处理，主要用于处理options
  // const columnsSolver = useMemo(() => {
  //   return SearchTable.columns ? columnsHandler(SearchTable.columns) : [];
  // }, [SearchTable.columns, searchParams, backPagination]);

  return (
    <>
      <Card
        title="医院分组明细(院级)"
        extra={
          <Space>
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: `Api/v2/Drgs/${ReqActionType.HospDrgsData}`,
                onTableRowSaveSuccess: (columns) => {
                  SearchTableDispatch({
                    type: TableAction.columnsChange,
                    payload: {
                      columns: tableColumnBaseProcessor([], columns),
                    },
                  });
                },
              }}
            />
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: addExportToLastSegment(
                  `Api/v2/Drgs/${ReqActionType.HospDrgsData}`,
                ),
                method: 'POST',
                data: {
                  ...searchParams,
                  Sdate: searchParams?.dateRange[0],
                  Edate: searchParams?.dateRange[1],
                  VersionedDrgCodes:
                    versionedDrgCodes?.length ?? 0 > 0
                      ? versionedDrgCodes
                      : undefined,
                  MainIcdeCode: MainIcdeCode || undefined,
                  FirstOperCode: FirstOperCode || undefined,
                  uniqueId: uniqueId || undefined,
                  CliDepts: CliDepts?.length ? CliDepts : undefined,
                  HospCode:
                    searchParams?.hospCodes?.length ?? 0 > 0
                      ? Array.isArray(searchParams?.hospCodes)
                        ? searchParams?.hospCodes
                        : [searchParams?.hospCodes]
                      : ['%'],
                },
                fileName: `医院分组明细(院级)`,
              }}
              btnDisabled={
                SearchTable.data?.length < 1 ||
                !SearchTable.data ||
                (SearchTable.data?.length === 1 && !SearchTable.data?.at(0))
              }
            />
          </Space>
        }
      >
        <UniTable
          // headerTitle="医院分组明细(院级)"
          id="hosp_drgs"
          rowKey="CardId"
          showSorterTooltip={false}
          loading={loadings[ReqActionType.HospDrgsData] ?? false}
          forceColumnsUpdate
          columns={[
            {
              dataIndex: 'options',
              width: 40,
              visible: true,
              fixed: 'left',
              render: (text, record) => (
                <IconBtn
                  type="details"
                  style={{ margin: '0 10px' }}
                  onClick={(e) => {
                    setDrawerVisible({
                      hisId: record?.HisId,
                      type: 'drg',
                    });
                  }}
                />
              ),
            },
            ...SearchTable.columns,
          ]} // columnsHandler
          dataSource={SearchTable.data}
          pagination={backPagination}
          onChange={backTableOnChange}
          scroll={{ x: 'max-content' }}
          // toolBarRender={() => [<Button>导出Export</Button>]}
        />
      </Card>
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </>
  );
};

export default HospDrgs;
