import { Space } from 'antd';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { Emitter } from '@uni/utils/src/emitter';
import { HierarchyBedAmtItem } from './interface';
import { EVENTS } from './constants';
import IconBtn from '@uni/components/src/iconBtn/index';
import { HistoryOutlined, LinkOutlined } from '@ant-design/icons';

export const getBedAmtColumns = [
  {
    title: '',
    key: 'options',
    fixed: 'left' as 'left', // 指定TypeScript类型
    visible: true,
    width: 75,
    render: (_, record: HierarchyBedAmtItem) => (
      <Space size="small">
        <IconBtn
          type="edit"
          onClick={() => Emitter.emit(EVENTS.EDIT_BED, record)}
          title="编辑"
        />
        <IconBtn
          type="details" // 使用系统内置的details图标
          customIcon={<LinkOutlined />}
          openPop
          popOnConfirm={() => Emitter.emit(EVENTS.BULK_UPDATE_BED, record)}
          title="更新留观动态登记床位数"
        />
        <IconBtn
          type="details"
          customIcon={<HistoryOutlined />}
          onClick={() => Emitter.emit(EVENTS.CHECK_BED_HISTORY, record)}
          title="查看科室床位数记录"
        />
      </Space>
    ),
  },
];
