import { But<PERSON>, Divider, Space } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { isEmptyValues } from '@uni/utils/src/utils';
import React, { useEffect, useState } from 'react';
import KeyCode from 'rc-util/lib/KeyCode';
import { UniTable } from '@uni/components/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import chunk from 'lodash/chunk';
import {
  icdeColumns,
  operColumns,
  operComboRemarkColumns,
} from './keyboard-columns';
import { v4 as uuidv4 } from 'uuid';
import { icdeExtraMap, operationExtraMap } from '../icde-oper-input/input';
import GridItemContext from '@uni/commons/src/grid-context';
import { useDebounceFn } from 'ahooks';

interface IcdeOperResp {
  Data?: IcdeOperItem[];
  RecordsTotal?: number;
}

export interface OperComboResp {
  Data?: OperComboItem[];
  RecordsTotal?: number;
}

export interface OperComboItem {
  Code?: string;
  Name?: string;
  OperRate?: string;
  PrimaryOperCode?: string;
  RelatedDepts?: string[];
  RelatedOperCodes?: string[];
  Remark?: string;
}

interface IcdeOperItem {
  Code?: string;
  Name?: string;

  InsurCode?: string;
  InsurName?: string;
  HqmsCode?: string;
  HqmsName?: string;

  IsObsolete?: boolean;
  IsDaySurgery?: boolean;
  IsMicro?: boolean;

  Degree?: string;
  OperType?: string;

  key?: string;
  label?: any;

  IcdeCode?: string;
  IcdeName?: string;
  OperCode?: string;
  OperName?: string;

  TcmIcdeCode?: string;
}

export interface KeyboardNumberSelectProps {
  enableKeyboardFriendlySelect?: boolean;

  numberSelectItem?: boolean;
  numberSelectRecordsTotal?: number;
  numberSelectPageSize?: number;
  numberPagination?: (
    current: number,
    pageSize: number,
    direction: 'LEFT' | 'RIGHT',
  ) => void;
}

interface KeyboardFriendlyDropdownProps extends KeyboardNumberSelectProps {
  listRef?: any;

  chunkOptions: any[];
  value?: any;
  onValueChange?: (value: any, option?: any) => void;
  onSelectChange?: (value: any, option?: any) => void;
  optionOpen?: boolean;
  setOptionOpen?: any;
  optionTitleKey?: string;

  optionNameKey?: string;

  chunkPageSize?: number;

  optionsPageCurrent?: number;
  setOptionsPageCurrent?: any;

  searchValue?: string;
  filterOptions?: (searchValue: string, option: any) => boolean;
}

const keyboardFriendlyDefaultSelectFirst =
  (window as any).externalConfig?.['dmr']?.keyboardFriendlyDefaultSelectFirst ??
  false;

const operationComboInput =
  (window as any).externalConfig?.['dmr']?.operationComboInput ?? false;

const KeyboardFriendlyDropdown = (props: KeyboardFriendlyDropdownProps) => {
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    if (props?.optionOpen === true) {
      setActiveIndex(0);
    }
  }, [props?.optionOpen]);

  React.useImperativeHandle(props?.listRef, () => {
    return {
      onKeyDown: (event: any) => {
        const currentTotalRecords =
          props?.chunkOptions?.[
            props?.numberPagination ? 0 : props?.optionsPageCurrent
          ]?.length;
        const { which, ctrlKey } = event;
        switch (which) {
          case KeyCode.UP:
          case KeyCode.DOWN: {
            event?.preventDefault();
            event?.stopPropagation();
            event['hosted'] = true;
            let offset = 0;
            if (which === KeyCode.UP) {
              offset = -1;
            } else if (which === KeyCode.DOWN) {
              offset = 1;
            }

            if (offset !== 0) {
              const nextActiveIndex = activeIndex + offset;
              if (
                nextActiveIndex < 0 ||
                nextActiveIndex >= currentTotalRecords
              ) {
                // 上下翻页
                if (which === KeyCode.UP) {
                  if (activeIndex < 0) {
                    // 翻页 同时设定成最后一个
                    const newOptionCurrent = props?.optionsPageCurrent - 1;
                    if (newOptionCurrent >= 0) {
                      props?.setOptionsPageCurrent(newOptionCurrent);
                      setActiveIndex(
                        props?.chunkOptions?.[newOptionCurrent]?.length - 1,
                      );
                    }
                  }
                }

                if (which === KeyCode.DOWN) {
                  if (activeIndex >= currentTotalRecords) {
                    // 翻页 同时设定成第一个
                    const newOptionCurrent = props?.optionsPageCurrent + 1;

                    if (newOptionCurrent < props?.chunkOptions?.length) {
                      props?.setOptionsPageCurrent(newOptionCurrent);
                      setActiveIndex(0);
                    }
                  }
                }
                return;
              }
              setActiveIndex(nextActiveIndex);
            }

            break;
          }

          // >>> Select
          case KeyCode.ENTER: {
            // value
            const item =
              props?.chunkOptions?.[
                props?.numberPagination ? 0 : props?.optionsPageCurrent
              ][activeIndex];
            if (item) {
              props?.onValueChange && props?.onValueChange(item?.value, item);

              props?.onSelectChange && props?.onSelectChange(item?.value, item);

              event['hosted'] = true;
            } else {
              // onSelectValue(undefined);
              props?.onValueChange && props?.onValueChange('', undefined);

              props?.onSelectChange && props?.onSelectChange('', undefined);

              event['hosted'] = true;
            }

            if (props?.optionOpen === true) {
              event.stopPropagation();
            }

            props?.setOptionOpen(false);

            break;
          }

          case KeyCode.ESC: {
            props?.setOptionOpen(false);
            if (props?.optionOpen === true) {
              event.stopPropagation();
            }

            event['hosted'] = true;
          }
        }
      },
      onKeyboardPageFlip: () => {
        setActiveIndex(0);
      },
    };
  });

  return (
    <>
      {props?.chunkOptions?.[
        props?.numberPagination ? 0 : props?.optionsPageCurrent
      ]?.map((item: any, index: number) => {
        return (
          <div
            style={{
              padding: '5px 12px 0px 5px',
            }}
            className={`ant-select-item ant-select-item-option ${
              item?.['value'] === props?.value
                ? 'ant-select-item-option-selected'
                : ''
            } ${index === activeIndex ? 'ant-select-item-option-active' : ''}`}
            title={item[props?.optionTitleKey || props?.optionNameKey]}
            onMouseMove={() => {
              if (activeIndex === index) {
                return;
              }
              setActiveIndex(index);
            }}
            onClick={() => {
              props?.onValueChange && props?.onValueChange(item?.value, item);

              props?.onSelectChange && props?.onSelectChange(item?.value, item);

              props?.setOptionOpen(false);
            }}
          >
            {props?.enableKeyboardFriendlySelect && (
              <span className={'keyboard-item-index'}>{index + 1}:</span>
            )}
            <div className={'ant-select-item-option-content'}>
              {item?.label}
            </div>
          </div>
        );
      })}
      {(props?.chunkOptions?.length > 0 ||
        Math.ceil(props?.numberSelectRecordsTotal / props?.chunkPageSize) >
          0) && (
        <>
          <Divider style={{ margin: '2px 0' }} />
          <Space style={{ padding: '0 8px 4px' }}>
            <Button
              type={'text'}
              disabled={props?.optionsPageCurrent <= 0}
              className={'dmr-left-right-pagination-icon'}
              onClick={() => {
                // FIXME  怎么来判定 前面是不是有 已经存在的所以不用 再从接口拿？
                const newOptionCurrent = props?.optionsPageCurrent - 1;
                if (props?.numberPagination) {
                  props?.numberPagination(
                    newOptionCurrent,
                    props?.chunkPageSize,
                    'LEFT',
                  );
                  props?.setOptionsPageCurrent(newOptionCurrent);
                } else {
                  if (newOptionCurrent >= 0) {
                    props?.setOptionsPageCurrent(newOptionCurrent);
                  }
                }
              }}
            >
              <LeftOutlined />
            </Button>
            <Button
              disabled={
                props?.optionsPageCurrent >= props?.chunkOptions?.length - 1 ||
                (!isEmptyValues(props?.numberSelectRecordsTotal)
                  ? props?.optionsPageCurrent >=
                    Math.ceil(
                      props?.numberSelectRecordsTotal / props?.chunkPageSize,
                    ) -
                      1
                  : false)
              }
              type="text"
              className={'dmr-left-right-pagination-icon'}
              onClick={() => {
                const newOptionCurrent = props?.optionsPageCurrent + 1;
                if (props?.numberPagination) {
                  props?.numberPagination(
                    newOptionCurrent,
                    props?.chunkPageSize,
                    'RIGHT',
                  );
                  props?.setOptionsPageCurrent(newOptionCurrent);
                } else {
                  if (
                    newOptionCurrent < props?.chunkOptions?.length ||
                    (!isEmptyValues(props?.numberSelectRecordsTotal)
                      ? newOptionCurrent <
                        Math.ceil(
                          props?.numberSelectRecordsTotal /
                            props?.chunkPageSize,
                        )
                      : false)
                  ) {
                    props?.setOptionsPageCurrent(newOptionCurrent);
                  }
                }
              }}
            >
              <RightOutlined />
            </Button>
          </Space>
        </>
      )}
    </>
  );
};

interface IcdeOperKeyboardFriendlyDropdownProps
  extends KeyboardNumberSelectProps {
  columnType?: string;

  listRef?: any;
  type: 'Icde' | 'Oper';

  value?: any;
  onValueChange?: (value: any, option?: any) => void;
  onSelectChange?: (value: any, option?: any, dataSources?: any[]) => void;
  optionOpen?: boolean;
  setOptionOpen?: any;

  searchKeyword?: string;
  setSearchKeyword?: any;
  instantSelect?: boolean;
  icdeSelectType?: string;
  icdeTcmCategory?: string;
  // 透传 一些search时需要判断的参数，目前仅在select search时处理
  paramKey?: string;
  form?: any;
  tableId?: string;
  recordId?: string;
  // 透传 结束
  interfaceUrl: string;

  codeColumnWidth?: number | string;

  leftRightSwitchPage?: boolean;
}

export const isKeyboardFriendlyDropdownVisible = (elementId: string) => {
  const element = document.getElementById(elementId);

  if (!element) {
    console.error(`Element with ID "${elementId}" not found`);
    return false;
  }

  return new Promise((resolve) => {
    const observer = new IntersectionObserver((entries) => {
      const entry = entries[0];
      resolve(entry.isIntersecting);
      observer.disconnect();
    });

    observer.observe(element);
  });
};

const IcdeOperKeyboardFriendlyDropdown = (
  props: IcdeOperKeyboardFriendlyDropdownProps,
) => {
  const defaultPageSize = 100;

  const [activeIndex, setActiveIndex] = useState(undefined);

  const [dataSource, setDataSource] = useState([]);

  const [recordTotal, setRecordTotal] = useState(0);

  const [loading, setLoading] = useState(false);

  const [offset, setOffset] = useState(0);

  const [maxPageIndex, setMaxPageIndex] = useState(0);

  const [currentPageIndex, setCurrentPageIndex] = useState(0);

  const chunkOptions = chunk(dataSource, 9);

  const context = React.useContext(GridItemContext);

  const enableKeyboardFriendlySelect =
    context?.externalConfig?.enableKeyboardFriendlySelect ?? false;

  const flipPrev = context?.externalConfig?.keyboardFlipKeys?.prev ?? [
    109, 189,
  ];
  const flipNext = context?.externalConfig?.keyboardFlipKeys?.next ?? [
    107, 187,
  ];

  const keyboardFriendlyEnterSelectFirst =
    context?.externalConfig?.keyboardFriendlyEnterSelectFirst ?? false;

  const { run: getIcdeOperDataSourceWithDebounce } = useDebounceFn(
    (offset, keyword) => {
      getIcdeOperDataSource(offset, keyword);
    },
    {
      wait: 200,
    },
  );

  useEffect(() => {
    if (enableKeyboardFriendlySelect === false) {
      getIcdeOperDataSourceWithDebounce(0, props?.searchKeyword);
    }
  }, [props?.searchKeyword]);

  useEffect(() => {
    if (props?.optionOpen === false) {
      setActiveIndex(undefined);
    }
  }, [props?.optionOpen]);

  React.useImperativeHandle(props?.listRef, () => {
    return {
      onFocus: (event: any, keyword: string) => {
        if (props?.instantSelect) {
          getIcdeOperDataSource(0, keyword);
        }
      },
      onBlur: () => {
        resetStates();
      },
      clear: () => {
        resetStates();
      },
      onKeyboardNumberSelect: (event: any, index: any) => {
        if (enableKeyboardFriendlySelect === true && index <= 9) {
          event?.preventDefault();
          const actualIndex = index - 1;
          let selectedItem = chunkOptions?.[currentPageIndex]?.[actualIndex];
          if (selectedItem) {
            props?.onSelectChange &&
              props?.onSelectChange(
                selectedItem?.value,
                selectedItem,
                dataSource,
              );

            resetStates();
          }
        }
      },
      onKeyboardPageFlip: (event) => {
        const { which, ctrlKey } = event;
        if (
          flipPrev?.includes(which) ||
          (props?.leftRightSwitchPage === true && which === KeyCode.LEFT)
        ) {
          event?.preventDefault();
          event?.stopPropagation();
          const newOptionCurrent = currentPageIndex - 1;

          if (newOptionCurrent >= 0) {
            setCurrentPageIndex(newOptionCurrent);
            if (keyboardFriendlyDefaultSelectFirst === true) {
              setActiveIndex(0);
            } else {
              setActiveIndex(undefined);
            }
          }
        }

        if (
          flipNext?.includes(which) ||
          (props?.leftRightSwitchPage === true && which === KeyCode.RIGHT)
        ) {
          event?.preventDefault();
          event?.stopPropagation();
          const newOptionCurrent = currentPageIndex + 1;

          if (newOptionCurrent < maxPageIndex) {
            setCurrentPageIndex(newOptionCurrent);

            if (keyboardFriendlyDefaultSelectFirst === true) {
              setActiveIndex(0);
            } else {
              setActiveIndex(undefined);
            }
          }

          // 当到一半的时候再捞一下 100个也就是10页
          if (newOptionCurrent >= 5 && newOptionCurrent % 5 === 0) {
            getIcdeOperDataSource(offset, props?.searchKeyword);
          }
        }
      },
      onKeyDown: (event: any) => {
        const { which, ctrlKey } = event;
        switch (which) {
          case KeyCode.UP:
          case KeyCode.DOWN: {
            event?.preventDefault();
            event?.stopPropagation();
            event['hosted'] = true;
            let offset = 0;
            if (which === KeyCode.UP) {
              offset = -1;
            } else if (which === KeyCode.DOWN) {
              offset = 1;
            }

            if (offset !== 0) {
              const nextActiveIndex = (activeIndex ?? -1) + offset;
              if (
                nextActiveIndex < 0 ||
                nextActiveIndex > chunkOptions?.[currentPageIndex]?.length - 1
              ) {
                // 上下翻页
                if (which === KeyCode.UP) {
                  if (nextActiveIndex < 0) {
                    // 翻页 同时设定成最后一个
                    const newOptionCurrent = currentPageIndex - 1;
                    if (newOptionCurrent >= 0) {
                      setCurrentPageIndex(newOptionCurrent);
                      setActiveIndex(
                        chunkOptions?.[newOptionCurrent]?.length - 1,
                      );
                    }
                  }
                }

                if (which === KeyCode.DOWN) {
                  console.log('Down', nextActiveIndex);
                  if (
                    nextActiveIndex >
                    chunkOptions?.[currentPageIndex]?.length - 1
                  ) {
                    // 翻页 同时设定成第一个
                    const newOptionCurrent = currentPageIndex + 1;

                    if (newOptionCurrent < maxPageIndex) {
                      setCurrentPageIndex(newOptionCurrent);
                      setActiveIndex(0);
                    }
                  }
                }
                return;
              }
              setActiveIndex(nextActiveIndex);
            }

            break;
          }

          // >>> Select
          case KeyCode.ENTER: {
            // 表示 当前 没开放
            if (enableKeyboardFriendlySelect === true) {
              if (props?.optionOpen === false) {
                // 搜索
                getIcdeOperDataSource(0, props?.searchKeyword);
                props?.setOptionOpen(true);
              }
            }

            if (!isEmptyValues(activeIndex)) {
              // value
              const item = chunkOptions?.[currentPageIndex ?? 0][activeIndex];
              if (item) {
                props?.onValueChange && props?.onValueChange(item?.Code, item);
                props?.onSelectChange &&
                  props?.onSelectChange(item?.Code, item, dataSource);

                event['hosted'] = true;
              } else {
                props?.onValueChange && props?.onValueChange('', undefined);
                props?.onSelectChange &&
                  props?.onSelectChange('', undefined, dataSource);

                event['hosted'] = true;
              }

              if (props?.optionOpen === true) {
                event.stopPropagation();
              }

              resetStates();
            } else {
              // 保证下拉框已经开启了
              if (props?.optionOpen === true) {
                // 自动选中第一个 需要config开启
                if (keyboardFriendlyEnterSelectFirst === true) {
                  if (chunkOptions?.[currentPageIndex]?.length > 0) {
                    // 选中第一个
                    let firstOne = chunkOptions?.[currentPageIndex]?.at(0);
                    if (!isEmptyValues(firstOne)) {
                      props?.onSelectChange &&
                        props?.onSelectChange(
                          firstOne?.Code,
                          firstOne,
                          dataSource,
                        );

                      event['hosted'] = true;
                    }
                  }
                }
              }
            }
            break;
          }

          case KeyCode.ESC: {
            resetStates();
            if (props?.optionOpen === true) {
              event.stopPropagation();
            }

            event['hosted'] = true;
          }
        }
      },
    };
  });

  const setDataSourceOrNot = (keyword: string, searchKeyword: string) => {
    if (props?.instantSelect) {
      return !!searchKeyword;
    } else {
      return keyword && searchKeyword;
    }
  };

  const searchArgsProcessor = (
    data,
    icdeSelectType,
    icdeTcmCategory = undefined,
    paramKey = undefined,
  ) => {
    // 病理诊断 && 是表格内部select的情况 单独处理
    if (props?.paramKey === 'pathologicalDiagnosis' && props?.tableId) {
      if (props?.form) {
        let pathoType = props?.form?.getFieldValue([
          props?.recordId,
          'PathoType',
        ]);
        switch (pathoType) {
          case 'MCode':
            data['IsMorphology'] = true;
            break;
          case 'Patho':
            data['IsPatho'] = true;
            break;
          default:
            data[icdeSelectType] = true;
            break;
        }
      }
    } else {
      if (icdeSelectType?.toLowerCase()?.includes('istcm')) {
        // 中医可能出现2个 所以要 多一套这个
        data['IsTcm'] = true;
        if (icdeSelectType?.toLowerCase()?.includes('istcmmain')) {
          data['TcmIcdeCategory'] = 'A';
        } else {
          data['TcmIcdeCategory'] = icdeTcmCategory ?? 'B';
        }
      } else {
        data[icdeSelectType] = true;
      }
    }

    return data;
  };

  const getIcdeOperDataSource = async (offset, searchKeyword) => {
    if (!searchKeyword) {
      return;
    }

    if (
      offset !== 0 &&
      (dataSource.length >= recordTotal || offset >= recordTotal)
    ) {
      // 表示 全量数据
      return;
    }

    setLoading(true);
    let data = {
      Keyword: searchKeyword?.trim(),
      SkipCount: offset,
      HasInsurCompare: true,
      HasHqmsCompare: true,
      HasDrgsCompare: true,
      MaxResultCount: defaultPageSize,
    };

    if (!isEmptyValues(props?.icdeSelectType)) {
      data = searchArgsProcessor(
        data,
        props?.icdeSelectType,
        props?.icdeTcmCategory,
        props?.paramKey,
      );
    }

    let icdeOperDataSourceResponse: RespVO<IcdeOperResp> =
      await uniCommonService(props?.interfaceUrl, {
        params: data,
      });

    let operComboData: OperComboItem[] = [];
    if (props?.type === 'Oper' && props?.columnType === 'Dmr') {
      let operComboDataSourceResponse: RespVO<OperComboResp> =
        await uniCommonService('Api/Dmr/DmrSearch/SearchOperCombo', {
          params: data,
        });

      operComboData = (operComboDataSourceResponse?.data?.Data ?? [])?.filter(
        (item) => !isEmptyValues(item?.RelatedOperCodes),
      );
    }

    let isTcm = props?.icdeSelectType?.toLowerCase()?.includes('istcm');

    if (icdeOperDataSourceResponse?.code === 0) {
      if (icdeOperDataSourceResponse?.statusCode === 200) {
        let existDataSource = offset === 0 ? [] : dataSource.slice();

        // 先行concat OperCombo 的数据
        if (props?.type === 'Oper' && !isEmptyValues(operComboData)) {
          existDataSource = existDataSource.concat(
            operComboData?.map((item) => {
              return Object.assign(item, {
                type: 'OperationCombo',
                id: uuidv4(),
                key: item?.Code,
                value: item?.Code,
                title: item?.Code,
                label: `${item?.Code} ${item?.Name}`,
                Remark: item?.Remark,
                OperExtra: ['OperationCombo'],
              });
            }),
          );
        }

        // 实时拿当前这个input的数据
        // 当且仅当 查询结果 和输入的keyword一致的时候set
        console.log('Keyword', props?.searchKeyword, searchKeyword);

        setDataSource(
          existDataSource.concat(
            icdeOperDataSourceResponse?.data?.Data?.map((item) => {
              if (isTcm) {
                return Object.assign(item, {
                  id: uuidv4(),
                  key: item?.Code,
                  value: item?.TcmIcdeCode,
                  title: item?.TcmIcdeCode,
                  label: `${item?.TcmIcdeCode} ${item?.Name}`,
                  IcdeExtra: Object.keys(icdeExtraMap)?.filter((key) => {
                    if (key === 'InsurIsObsolete') {
                      return item?.['IsObsolete'] ?? false;
                    } else {
                      return item?.[key] ?? false;
                    }
                  }),
                  OperExtra: Object.keys(operationExtraMap)?.filter((key) => {
                    if (key === 'InsurIsObsolete') {
                      return item?.['IsObsolete'] ?? false;
                    } else {
                      return item?.[key] ?? false;
                    }
                  }),
                  IcdeCode: item?.TcmIcdeCode,
                  IcdeName: item?.Name,
                });
              }

              return Object.assign(item, {
                id: uuidv4(),
                key: item?.Code,
                value: item?.Code,
                title: item?.Code,
                label: `${item?.Code} ${item?.Name}`,
                IcdeExtra: Object.keys(icdeExtraMap)?.filter((key) => {
                  if (key === 'InsurIsObsolete') {
                    return item?.['IsObsolete'] ?? false;
                  } else {
                    return item?.[key] ?? false;
                  }
                }),
                OperExtra: Object.keys(operationExtraMap)?.filter((key) => {
                  if (key === 'InsurIsObsolete') {
                    return item?.['IsObsolete'] ?? false;
                  } else {
                    return item?.[key] ?? false;
                  }
                }),
                IcdeCode: item?.Code,
                IcdeName: item?.Name,
              });
            }) || [],
          ),
        );
        setOffset(offset + defaultPageSize);

        if (offset === 0) {
          setCurrentPageIndex(0);
        }

        setRecordTotal(icdeOperDataSourceResponse?.data?.RecordsTotal);
        setMaxPageIndex(
          Math.ceil(icdeOperDataSourceResponse?.data?.RecordsTotal / 9),
        );

        if (!isEmptyValues(activeIndex)) {
          setActiveIndex(0);
        }

        if (keyboardFriendlyDefaultSelectFirst === true) {
          setActiveIndex(0);
        }

        props?.setOptionOpen(true);
      }
    }

    setLoading(false);
  };

  console.log('chunkOptions', chunkOptions, currentPageIndex, maxPageIndex);

  const resetStates = () => {
    setDataSource([]);
    setOffset(0);
    setRecordTotal(0);
    props?.setOptionOpen(false);
    props?.setSearchKeyword('');
  };

  return (
    <div
      id={'icde-oper-dropdown-container'}
      className={'icde-oper-dropdown-container'}
    >
      <UniTable
        id={'icde-oper-search-table'}
        rowKey={'id'}
        size={'small'}
        loading={loading}
        columns={
          props?.type === 'Icde'
            ? icdeColumns(props?.codeColumnWidth, props?.searchKeyword)
            : operColumns(props?.codeColumnWidth, props?.searchKeyword).concat(
                operationComboInput === true ? operComboRemarkColumns : [],
              )
        }
        dataSource={chunkOptions?.[currentPageIndex]}
        pagination={false}
        toolBarRender={null}
        clickable={true}
        forceColumnsUpdate={true}
        rowClassName={(record, index) => {
          return index === activeIndex ? 'row-active' : '';
        }}
        onRow={(record, rowIndex) => {
          return {
            onClick: (event) => {
              console.log('Table onClick', event);

              props?.onSelectChange &&
                props?.onSelectChange(
                  record?.value,
                  record,
                  chunkOptions?.[currentPageIndex],
                );
              resetStates();
            },
          };
        }}
      />

      {maxPageIndex > 0 && (
        <>
          <Space style={{ padding: '0 8px 4px' }}>
            <Button
              type={'text'}
              disabled={currentPageIndex <= 0}
              className={'dmr-left-right-pagination-icon'}
              onClick={() => {
                // FIXME  怎么来判定 前面是不是有 已经存在的所以不用 再从接口拿？
                const newOptionCurrent = currentPageIndex - 1;

                if (currentPageIndex >= 0) {
                  setCurrentPageIndex(newOptionCurrent);
                }
              }}
            >
              <LeftOutlined />
            </Button>
            <Button
              disabled={currentPageIndex >= maxPageIndex - 1}
              type="text"
              className={'dmr-left-right-pagination-icon'}
              onClick={() => {
                const newOptionCurrent = currentPageIndex + 1;

                if (currentPageIndex <= maxPageIndex) {
                  setCurrentPageIndex(newOptionCurrent);
                }

                // 当到一半的时候再捞一下 100个也就是10页
                if (currentPageIndex >= 5 && currentPageIndex % 5 === 0) {
                  getIcdeOperDataSource(offset, props?.searchKeyword);
                }
              }}
            >
              <RightOutlined />
            </Button>
          </Space>
        </>
      )}
    </div>
  );
};

export { IcdeOperKeyboardFriendlyDropdown, KeyboardFriendlyDropdown };
