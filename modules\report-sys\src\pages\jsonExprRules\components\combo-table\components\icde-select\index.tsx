import React, { useEffect, useRef, useState } from 'react';
import { Col, Form, Select, Spin } from 'antd';
import './index.less';
import { v4 as uuidv4 } from 'uuid';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { useDebounceFn } from 'ahooks';
import { IcdeOperItem, IcdeOperResp } from '../../interfaces';

const { Option } = Select;

const moduleToInterface = {
  Dmr: 'Api/Dmr/DmrSearch/IcdeUnionCategory',
  Hqms: 'Api/Hqms/HqmsSearch/IcdeUnionCategory',
  Insur: 'Api/Insur/InsurSearch/IcdeUnionCategory',
  Wt: 'Api/Wt/WtSearch/IcdeUnionCategory',
};
export interface IcdeSelectProps {
  value?: any;

  parentId?: string;

  onChange?: (value: any) => void;

  dictionaryModuleGroup?: string;
  [name: string]: any;
}

const maxResultCount = 100;
const CombineQueryIcdeSelect = ({
  value,
  parentId,
  onChange,
  dictionaryModuleGroup,
  ...restProps
}: IcdeSelectProps) => {
  const [dataSource, setDataSource] = useState<IcdeOperItem[]>([]);

  const [offset, setOffset] = useState(0);
  const [recordTotal, setRecordTotal] = useState(0);

  const [loading, setLoading] = useState(false);
  const [keyword, setKeyword] = useState('');

  const getDiagnosisDataSource = async (offset, keyword) => {
    if (!keyword) {
      return;
    }

    if (
      offset !== 0 &&
      (dataSource.length >= recordTotal || offset >= recordTotal)
    ) {
      // 表示 全量数据
      return;
    }

    setLoading(true);
    let data = {
      Keyword: keyword?.trim(),
      SkipCount: offset,
      MaxResultCount: maxResultCount,
    };

    let dataType = restProps?.fieldDefinition?.dataType;
    let searchType = dataType?.split('_')?.at(1);
    if (searchType) {
      data[`Is${searchType}`] = true;
      if (searchType?.toLowerCase()?.includes('tcm')) {
        data['TcmIcdeCategory'] = dataType?.split('_')?.at(2) ?? 'B';
      }
    } else {
      data['IsOtp'] = true;
    }

    let diagnosisDataSourceResponse: RespVO<IcdeOperResp> =
      await uniCommonService(moduleToInterface[dictionaryModuleGroup], {
        params: data,
      });

    if (diagnosisDataSourceResponse?.code === 0) {
      if (diagnosisDataSourceResponse?.statusCode === 200) {
        let existDataSource = offset === 0 ? [] : dataSource.slice();
        if (keyword) {
          setDataSource(
            existDataSource.concat(
              diagnosisDataSourceResponse?.data?.Data || [],
            ),
          );
          setOffset(offset + maxResultCount);

          setRecordTotal(diagnosisDataSourceResponse?.data?.RecordsTotal);
        }
      }
    }

    setLoading(false);
  };

  const { run: getDiagnosisDataSourceWithDebounce } = useDebounceFn(
    (offset, keyword) => {
      getDiagnosisDataSource(offset, keyword);
    },
    {
      wait: 500,
    },
  );

  const onIcdePopUpScroll = (event) => {
    let contentElement = event.target;
    let scrollNearlyEnd =
      Math.abs(
        contentElement.scrollHeight -
          contentElement.scrollTop -
          contentElement.clientHeight,
      ) < 100;
    console.error('nearly end', scrollNearlyEnd);
    if (scrollNearlyEnd && !loading) {
      getDiagnosisDataSourceWithDebounce(offset, keyword);
    }
  };

  // TODO 当这个页面显示的时候会出问题的  select option 是没有的  需要在做定制
  console.log(dataSource);
  return (
    <Col className={'combine-query'}>
      <Select
        {...restProps}
        className={`combine-query-icde-select`}
        showSearch
        allowClear={true}
        loading={loading}
        dropdownMatchSelectWidth={false}
        getPopupContainer={(trigger) =>
          (parentId && document.getElementById(parentId)) || document.body
        }
        placeholder={value || '请选择诊断'}
        onSearch={(searchKeyword) => {
          if (searchKeyword) {
            if (searchKeyword !== keyword) {
              // fetch
              getDiagnosisDataSourceWithDebounce(0, searchKeyword);
            } else {
              getDiagnosisDataSourceWithDebounce(offset, searchKeyword);
            }
          } else {
            setDataSource([]);
            setOffset(0);
            setRecordTotal(0);
          }
          setKeyword(searchKeyword);
        }}
        onPopupScroll={onIcdePopUpScroll}
        filterOption={false}
        optionLabelProp={'Code'}
        notFoundContent={loading ? <Spin size="small" /> : null}
        onBlur={(event) => {
          setDataSource([]);
          setKeyword('');
          setOffset(0);
          setRecordTotal(0);
        }}
        onChange={(value: string | string[]) => {
          restProps?.setValue && restProps?.setValue(value);
        }}
        value={value}
      >
        {dataSource?.map((item) => {
          return (
            <Option
              title={item.Name}
              value={`${item?.Code}${
                item?.IsValid === false ? ' (已停用)' : ''
              }`}
            >
              {item?.Code} {item.Name}
              <span style={{ color: '#eb5757' }}>
                {item?.IsValid === false ? ' (已停用)' : ''}
              </span>
            </Option>
          );
        })}
      </Select>
    </Col>
  );
};

export default CombineQueryIcdeSelect;
