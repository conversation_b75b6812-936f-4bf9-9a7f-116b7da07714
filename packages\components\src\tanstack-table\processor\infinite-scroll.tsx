import React from 'react';
import {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from '@tanstack/react-table';
import sorter from './sorter';

export interface InfiniteScrollProps {
  infiniteScroll?: boolean;
  bottomReachMargin?: number;
  infiniteScrollPageSize?: number;
  infiniteScrollSorterFilter?: (sorter: any, filter: any) => void;

  fetchNextPage?: (
    pageIndex: number,
    pageSize: number,
    callback?: () => void,
  ) => Promise<any>;
}

export const calculateDataThreshold = (
  infiniteScrollPageSize: number,
  pageIndex: number,
) => {
  return (
    Math.floor(infiniteScrollPageSize * 0.8) +
    pageIndex * infiniteScrollPageSize -
    1
  );
};

export const onInfiniteScrollWithNoSense = (
  rowVirtualizer: any,
  infiniteProps: any,
  pagination: PaginationState,
  setPagination: any,
) => {
  const virtualItems = rowVirtualizer.getVirtualItems();
  if (virtualItems.length === 0) return;

  const lastVisibleRowIndex = virtualItems[virtualItems.length - 1].index;

  // Calculate current page
  const currentPage = Math.floor(
    lastVisibleRowIndex / infiniteProps?.infiniteScrollPageSize,
  );

  // Calculate the 80% threshold for the current page
  const eightyPercentThreshold = calculateDataThreshold(
    infiniteProps?.infiniteScrollPageSize,
    currentPage,
  );

  // Check if we've scrolled past 80% of the current page data
  if (lastVisibleRowIndex >= eightyPercentThreshold) {
    setPagination({
      pageIndex: pagination?.pageIndex + 1,
      pageSize:
        infiniteProps?.infiniteScrollPageSize ?? pagination?.pageSize ?? 10,
    });
    infiniteProps?.fetchNextPage &&
      infiniteProps?.fetchNextPage(
        pagination?.pageIndex + 1,
        infiniteProps?.infiniteScrollPageSize ?? pagination?.pageSize ?? 10,
      );
  }
};

export const fetchMoreOnBottomReached = (
  containerRefElement: HTMLDivElement | null,
  infiniteProps: any,
  pagination: PaginationState,
  setPagination: any,
) => {
  if (infiniteProps?.infiniteScroll === true) {
    if (containerRefElement) {
      const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
      //once the user has scrolled within 500px of the bottom of the table, fetch more data if we can
      if (
        scrollHeight - scrollTop - clientHeight <
        (infiniteProps?.bottomReachMargin ?? 500)
      ) {
        setPagination({
          pageIndex: pagination?.pageIndex + 1,
          pageSize:
            infiniteProps?.infiniteScrollPageSize ?? pagination?.pageSize ?? 10,
        });
        infiniteProps?.fetchNextPage &&
          infiniteProps?.fetchNextPage(
            pagination?.pageIndex + 1,
            infiniteProps?.infiniteScrollPageSize ?? 100,
          );
      }
    }
  }
};
