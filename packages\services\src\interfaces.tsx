export interface TableColumns {
  Columns?: ColumnItem[];
  DictColumns?: any;
}

export interface ColumnItem {
  aggregable?: boolean;
  className?: string;
  data?: string;
  dataType?: string;
  dictionaryModule?: string;
  dictionaryModuleGroup?: string;
  groupName?: string;
  name?: string;
  orderMode?: any;
  orderPriority?: number;
  orderable?: boolean;
  responsivePriority?: number;
  scale?: any;
  shortTitle?: number;
  shortTitleDescription?: string;
  title?: string;
  visible?: boolean;

  order?: number;

  DictModule?: string;
  DictModuleGroup?: string;
  [key: string]: any;
}
