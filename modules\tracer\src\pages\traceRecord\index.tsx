import { Reducer, useContext, useEffect, useMemo, useReducer } from 'react';
import { Dispatch, useDispatch, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { UniTable } from '@uni/components/src';
import {
  Button,
  Card,
  Col,
  Divider,
  Popconfirm,
  Row,
  Space,
  TableProps,
  Tooltip,
  Typography,
  message,
} from 'antd';
import { useSafeState, useUpdateEffect } from 'ahooks';
import { IReducer, ITableState } from '@uni/reducers/src/Interface';
import { SwagTraceRecordItem } from '../interface';
import { columnsHandler, isRespErr, sortingHandler } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType } from '@/Constants';
import { TableAction, tableReducer, InitTableState } from '@uni/reducers/src';
import { SorterResult } from 'antd/lib/table/interface';
import { useTimelineReq } from '@/hooks';
import PatTimeline from '@/components/PatTimeline';
import { BellOutlined } from '@ant-design/icons';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import ExportIconBtn from '@uni/components/src/backend-export';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { ITableReq } from '@/Interface';
import { pickOnlyNeedKeys } from '@uni/utils/src/search-context';

const createAlert = false;

const TraceRecordSearchList = () => {
  const {
    globalState: { searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const dispatch: Dispatch = useDispatch();

  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagTraceRecordItem>, IReducer>
  >(tableReducer, InitTableState);

  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();

  const [backPagination, setBackPagination] = useSafeState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    hideOnSinglePage: false,
  });

  const backTableOnChange: TableProps<any>['onChange'] = async (
    pagi,
    filter,
    sorter,
    extra,
  ) => {
    tableReq(
      searchParams,
      pagi.current,
      pagi.pageSize,
      sorter as SorterResult<SwagTraceRecordItem>,
    );
  };

  // 普通的tableReq
  const tableReq = async (
    params,
    cur = 1,
    size = 10,
    sorter = SearchTable.sorter,
  ) => {
    console.log('fetchParams: ', params);
    let res: ITableReq | string = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: 'TraceRecord',
        requestParams: [
          {
            url: 'Api/Mr/TraceRecord/GetList',
            method: 'POST',
            data: {
              ..._.pick(params, [
                'hospCode',
                'outDept',
                'workFlowStatus',
                'Sdate',
                'Edate',
              ]),
              SearchableText: params?.searchKeyword,
              // Sdate: params?.Sdate || params?.dateRange ? params?.dateRange[0] : undefined,
              // Edate: params?.dateRange ? params?.dateRange[1] : undefined,
              CliDept: params?.dutyDept || undefined,
              ...pickOnlyNeedKeys(params, true),

              current: cur,
              pageSize: size,
              sorting: sortingHandler(sorter),
            },
            dataType: 'mr',
          },
        ],
      },
    });
    if (typeof res !== 'string') {
      let total = res?.datas[0]?.total;
      SearchTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res?.datas[0]?.data ?? [],
        },
      });

      // sorter
      if (!_.isEqual(sorter, SearchTable.sorter)) {
        SearchTableDispatch({
          type: TableAction.sortChange,
          payload: { sorter },
        });
      }

      setBackPagination({
        ...backPagination,
        current: cur,
        pageSize: size,
        total: total ?? 0,
      });
    }
  };

  // 目前 只有 催缴 用到
  const reqActionReq = async (params: any, reqType: ReqActionType) => {
    if (!params || !reqType) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceManagement/${reqType}`,
        requestParams: {
          url: `Api/Mr/TraceManagement/${reqType}`,
          method: 'POST',
          data: params,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      message.success('操作成功');

      timeLineReset();

      tableReq(searchParams, backPagination.current, backPagination.pageSize);
    }
  };
  // columns 处理，主要用于处理options
  const columnsSolver = useMemo(() => {
    return SearchTable.columns
      ? columnsHandler(
          SearchTable.columns,
          createAlert === false
            ? null
            : {
                dataIndex: 'option',
                title: '操作',
                visible: true,
                width: 50,
                align: 'center',
                // valueType: 'option',
                fixed: 'right',
                render: (text, record: SwagTraceRecordItem) => [
                  <Tooltip key="alert" title="催缴">
                    <BellOutlined
                      className="icon_blue-color"
                      style={{
                        display: !record.DmrSignInDate ? 'block' : 'none',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        // 催还api
                        reqActionReq(
                          { RecordIds: [record.RecordId] },
                          ReqActionType.createAlerts,
                        );
                      }}
                    />
                  </Tooltip>,
                ],
              },
        )
      : [];
  }, [SearchTable.columns, searchParams, backPagination]);

  useEffect(() => {
    console.log('searchParams', searchParams);
    tableReq(searchParams);
    timeLineReset();
  }, [searchParams]);

  const timeLineReset = () => {
    SearchTableDispatch({
      type: TableAction.clkChange,
      payload: {
        clkItem: null,
      },
    });
    setParams(null);
  };
  // columns处理
  if (columnsList?.['TraceRecord'] && SearchTable.columns.length < 1) {
    SearchTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          [
            {
              data: 'IsSignedIn',
              dataIndex: 'IsSignedIn',
              visible: true,
              align: 'center',
              render: (text: string, record: any) => {
                return <>{text ? '是' : '否'}</>;
              },
              order: 2,
            },
          ],
          columnsList['TraceRecord'],
          'local',
        ),
      },
    });
  }

  return (
    <>
      <Card
        title="示踪病案列表"
        extra={
          <Space>
            <Popconfirm
              style={createAlert === false ? { display: 'none' } : {}}
              title={
                <>
                  将会对
                  <Typography.Text type="danger">所有</Typography.Text>
                  未签收的病案进行催缴
                </>
              }
              key="totalAskForReturn"
              overlayInnerStyle={{
                width: 200,
              }}
              onConfirm={() => {
                reqActionReq(
                  { RecordIds: SearchTable.selectedKeys },
                  ReqActionType.createAlerts,
                );
              }}
            >
              <Button>全部催缴</Button>
            </Popconfirm>
            <Divider
              type="vertical"
              style={createAlert === false ? { display: 'none' } : {}}
            />
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: 'Api/Mr/TraceRecord/ExportGetList',
                method: 'POST',
                data: {
                  ..._.omit(searchParams, [
                    'HospCode',
                    'hospCode',
                    'HospCodes',
                    'hospCodes',
                    'CliDepts',
                  ]),
                  SearchableText: searchParams?.searchKeyword,
                  // sdate: searchParams?.dateRange
                  //   ? searchParams?.dateRange[0]
                  //   : undefined,
                  // edate: searchParams?.dateRange
                  //   ? searchParams?.dateRange[1]
                  //   : undefined,
                  CliDept: searchParams?.dutyDept || undefined,
                  ...pickOnlyNeedKeys(searchParams, true),
                  MaxResultCount: 999999,
                },
              }}
              btnDisabled={SearchTable.data?.length < 1}
            />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: 'Api/Mr/TraceRecord/GetList',
                onTableRowSaveSuccess: (columns) => {
                  // 这个columns 存到dva
                  dispatch({
                    type: 'global/saveColumns',
                    payload: {
                      name: 'TraceRecord',
                      value: columns,
                    },
                  });
                  SearchTableDispatch({
                    type: TableAction.columnsChange,
                    payload: {
                      columns: tableColumnBaseProcessor(
                        [
                          {
                            data: 'IsSignedIn',
                            dataIndex: 'IsSignedIn',
                            visible: true,
                            align: 'center',
                            render: (text: string, record: any) => {
                              return <>{text ? '是' : '否'}</>;
                            },
                            order: 2,
                          },
                        ],
                        columns,
                        'local',
                      ),
                    },
                  });
                },
              }}
            />
          </Space>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={18}>
            <UniTable
              id="trace_acrchived"
              rowKey="BarCode" // 特殊key，用于rowSelection
              showSorterTooltip={false}
              loading={
                loadings['TraceRecord'] ??
                loadings[`TraceManagement/${ReqActionType.createAlerts}`] ??
                false
              }
              columns={columnsSolver} // columnsHandler
              dataSource={SearchTable.data}
              pagination={backPagination}
              onChange={backTableOnChange}
              scroll={{ x: 'max-content' }}
              rowClassName={(record) => {
                if (record?.BarCode === SearchTable.clkItem?.BarCode)
                  return 'row-selected';
                return null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (SearchTable.clkItem?.BarCode !== record?.BarCode) {
                      SearchTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
            />
          </Col>
          <Col span={6}>
            <PatTimeline
              item={SearchTable?.clkItem}
              timelineItems={timelineItems}
              loading={loadings['TraceRecord/GetActions']}
            />
          </Col>
        </Row>
      </Card>
    </>
  );
};

export default TraceRecordSearchList;
