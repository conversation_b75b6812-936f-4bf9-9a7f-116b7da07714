import _ from 'lodash';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import GradientChartAndTableAndPie from '../../../components/gradientChartAndTableAndPie';
import './index.less';
import { SettleCompStatsByGrpColumns } from '@/pages/dip/constants';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import IconBtn from '@uni/components/src/iconBtn';
import { Card, Col, Row, Tabs, Button } from 'antd';

const AdrgAndDrgAnalysis = (props) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, CliDepts, insurType } = globalState?.searchParams;

  return (
    <Row gutter={[16, 16]}>
      <GradientChartAndTableAndPie
        args={{
          level: 'medTeam',
          type: 'grp',
          title: '病组效率',
          category: 'ChsDrgName',
          columns: [
            {
              dataIndex: 'operation',
              visible: true,
              width: 40,
              align: 'center',
              order: 1,
              title: '',
              render: (node, record, index) => {
                return (
                  <IconBtn
                    type="details"
                    onClick={(e) => {
                      e.stopPropagation();
                      Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                        id: 'drg-dept-settle-stats-by-chsdrg', // 要匹配到对应的DetailTableModal id
                        title: record?.ChsDrgName,
                        args: {
                          Sdate: dateRange?.at(0),
                          Edate: dateRange?.at(1),
                          HospCode: hospCodes,
                          CliDepts,
                          insurType,
                          VersionedChsDrgCodes: [
                            record?.VersionedChsDrgCode || undefined,
                          ],
                        },
                        type: 'dip',
                        detailsUrl:
                          'FundSupervise/LatestDipSettleStats/SettleDetails',
                        dictData: globalState?.dictData, // 传入
                      });
                    }}
                  />
                );
              },
            },
            ...SettleCompStatsByGrpColumns,
          ],
          clickable: true,
          emitter: EventConstant.DRG_TABLE_ROW_CLICK,
          detailsTitle: '病组分布',
          axisOpts: props?.args?.grpAxisOpts,
          defaultAxisOpt: props?.args?.defaultGrpAxisOpts,
          api: props?.args?.grpApi,
        }}
      />
    </Row>
  );
};

export default AdrgAndDrgAnalysis;
