import { Card, Col, Row, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { useModel } from 'umi';
import './index.less';
import {
  HospTotalStatsColumns,
  SettleCompStatsByCliDeptColumns,
  SettleCompStatsByGrpColumns,
  SettleCompStatsByMedTeamColumns,
  TabCommonItems,
} from '../constants';
import {
  CliDeptDefaultOpts,
  CliDeptQuadrantAxisOpts,
  GrpDefaultOpts,
  GrpQuadrantAxisOpts,
} from '../optsConstants';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import IconBtn from '@uni/components/src/iconBtn';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import Stats from '@/components/stats';
import FeeCompositionAndAnalysis from '@/components/feeCompositionAndAnalysis';
import GradientChartAndTableAndPie from '@/components/gradientChartAndTableAndPie';
import DrawerCardInfo from '@/pages/drg/components/drawerCardInfo';
import { isEmptyValues } from '@uni/utils/src/utils';
import { mergeTableClickParams } from '@/utils/utils';
import TrendAnalysis from '@/components/trendAnalysis';

const DrgHospAnalysis = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, insurType } = globalState?.searchParams;
  const [selectedStatItem, setSelectedStatItem] = useState({
    contentData: 'Profit',
    dataType: 'Currency',
    title: '盈亏',
  });
  const [requestParams, setRequestParams] = useState<any>({});
  const [activeKey, setActiveKey] = useState('statistic');
  const [drawerVisible, setDrawerVisible] = useState(undefined);
  const [statsHeight, setStatsHeight] = useState<number>(295);

  useEffect(() => {
    const updateHeight = () => {
      const element = document.getElementById('hosp-stats-list');
      if (element) {
        const height = element.getBoundingClientRect().height;
        if (height > 0) {
          setStatsHeight(height - 50 - 24);
        }
      }
    };

    // 初始更新
    updateHeight();

    // 创建一个观察器实例
    const observer = new MutationObserver(updateHeight);

    // 配置观察选项
    const config = { attributes: true, childList: true, subtree: true };

    // 开始观察目标节点
    const targetNode = document.getElementById('hosp-stats-list');
    if (targetNode) {
      observer.observe(targetNode, config);
    }

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    if (
      (globalState?.searchParams &&
        globalState?.searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(requestParams) &&
        globalState?.searchParams?.dateRange?.length)
    ) {
      let tableParams = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        insurType,
      };
      setRequestParams(tableParams);
    }
  }, [globalState?.searchParams]);

  // stat click 由Component Stats传入
  useEffect(() => {
    Emitter.on(EventConstant.STAT_ON_CLICK_EMITTER, (record) => {
      setSelectedStatItem(record);
    });
    return () => {
      Emitter.off(EventConstant.STAT_ON_CLICK_EMITTER);
    };
  }, []);

  let tabItems = [
    {
      key: TabCommonItems.statistic.key,
      label: TabCommonItems.statistic.title,
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={12} xl={11}>
            <Row gutter={[16, 16]} id="hosp-stats-list">
              <Stats
                api={`Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsOfHosp`}
                columns={HospTotalStatsColumns}
                type="col-xl-6"
                tabKey={activeKey}
                outerTableParams={requestParams}
                onClickEmitter={EventConstant.STAT_ON_CLICK_EMITTER}
              />
            </Row>
          </Col>
          <Col xs={24} sm={24} md={24} lg={12} xl={13}>
            <TrendAnalysis
              title="全院月度变化趋势"
              height={statsHeight}
              selectedStatItem={selectedStatItem}
              requestParams={requestParams}
              dictData={globalState.dictData}
            />
          </Col>
        </Row>
      ),
    },
    {
      key: TabCommonItems.feeAnalysis.key,
      label: TabCommonItems.feeAnalysis.title,
      children: (
        <FeeCompositionAndAnalysis
          requestParams={requestParams}
          api={`Api/FundSupervise/LatestDrgSettleStats/FeeChargeDistribution`}
          tabKey={activeKey}
        />
      ),
    },
    {
      key: TabCommonItems.drgAnalysis.key,
      label: TabCommonItems.drgAnalysis.title,
      children: (
        <>
          <GradientChartAndTableAndPie
            args={{
              level: 'hosp',
              type: 'drg',
              title: '病组效率',
              category: 'ChsDrgName',
              columns: [
                {
                  dataIndex: 'operation',
                  visible: true,
                  width: 40,
                  align: 'center',
                  order: 1,
                  title: '',
                  render: (node, record, index) => {
                    return (
                      <IconBtn
                        type="details"
                        onClick={(e) => {
                          e.stopPropagation();
                          Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                            id: 'drg-hosp-settle-stats-by-drg',
                            title: `${record?.ChsDrgName}`,
                            args: {
                              ...requestParams,
                              VersionedChsDrgCodes: record?.VersionedChsDrgCode
                                ? [record?.VersionedChsDrgCode]
                                : [],
                            },
                            type: 'drg',
                            detailsUrl:
                              'FundSupervise/LatestDrgSettleStats/SettleDetails',
                            dictData: globalState?.dictData,
                          });
                        }}
                      />
                    );
                  },
                },
                ...SettleCompStatsByGrpColumns,
              ],
              clickable: true,
              emitter: EventConstant.DRG_TABLE_ROW_CLICK,
              detailsTitle: '病组分布',
              axisOpts: GrpQuadrantAxisOpts,
              defaultAxisOpt: GrpDefaultOpts,
              api: `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByGrp`,
            }}
            requestParams={requestParams}
          />
        </>
      ),
    },
    // 学科
    {
      key: TabCommonItems.majorPerfDeptAnalysis.key,
      label: TabCommonItems.majorPerfDeptAnalysis.title,
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTableAndPie
                args={{
                  clickable: true,
                  emitter: EventConstant.MAJOR_PERF_DEPT_TABLE_ROW_CLICK,
                  type: 'majorPerfDept',
                  title: '学科效率',
                  category: 'MajorPerfDeptName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      fixed: 'left',
                      order: 1,
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            title="查看数据"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                title: `${record?.MajorPerfDeptName}`,
                                args: {
                                  ...mergeTableClickParams(
                                    requestParams,
                                    record,
                                    ['HospCode'],
                                  ),
                                  MajorPerfDepts: record?.MajorPerfDept
                                    ? [record?.MajorPerfDept]
                                    : ['%'],
                                },
                                type: 'drg',
                                detailsUrl:
                                  'FundSupervise/LatestDrgSettleStats/SettleDetails',
                                dictData: globalState?.dictData,
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByCliDeptColumns,
                  ],
                  detailsTitle: '学科分布',
                  defaultAxisOpt: CliDeptDefaultOpts,
                  axisOpts: CliDeptQuadrantAxisOpts,
                  api: `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByMajorPerfDept`,
                }}
                requestParams={requestParams}
              />
            </Col>
          </Row>
        </>
      ),
    },
    // 科室
    {
      key: TabCommonItems.deptAnalysis.key,
      label: TabCommonItems.deptAnalysis.title,
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTableAndPie
                args={{
                  clickable: true,
                  emitter: EventConstant.DEPT_TABLE_ROW_CLICK,
                  type: 'dept',
                  title: '科室效率',
                  category: 'CliDeptName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      fixed: 'left',
                      order: 1,
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            title="查看数据"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                id: 'drg-hosp-settle-stats-by-dept-1111111',
                                title: `${record?.CliDeptName}`,
                                args: {
                                  ...requestParams,
                                  CliDepts: record?.CliDept
                                    ? [record?.CliDept]
                                    : ['%'],
                                },
                                type: 'drg',
                                detailsUrl:
                                  'FundSupervise/LatestDrgSettleStats/SettleDetails',
                                dictData: globalState?.dictData,
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByCliDeptColumns,
                  ],
                  detailsTitle: '科室分布',
                  defaultAxisOpt: CliDeptDefaultOpts,
                  axisOpts: CliDeptQuadrantAxisOpts,
                  api: `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByCliDept`,
                }}
                requestParams={requestParams}
              />
            </Col>
          </Row>
        </>
      ),
    },
    // 医疗组
    {
      key: TabCommonItems.medTeamAnalysis.key,
      label: TabCommonItems.medTeamAnalysis.title,
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTableAndPie
                args={{
                  type: 'medTeam',
                  clickable: true,
                  emitter: EventConstant.MED_TEAM_TABLE_ROW_CLICK,
                  title: '医疗组效率',
                  category: 'MedTeamName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      order: 1,
                      fixed: 'left',
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            title="查看数据"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                id: 'drg-hosp-settle-stats-by-med-team',
                                title: `${record?.MedTeamName}`,
                                args: {
                                  ...requestParams,
                                  MedTeams: record?.MedTeam
                                    ? [record?.MedTeam]
                                    : ['%'],
                                },
                                type: 'drg',
                                detailsUrl:
                                  'FundSupervise/LatestDrgSettleStats/SettleDetails',
                                dictData: globalState?.dictData,
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByMedTeamColumns,
                  ],
                  detailsTitle: '医疗组分布',
                  defaultAxisOpt: CliDeptDefaultOpts,
                  axisOpts: CliDeptQuadrantAxisOpts,
                  api: `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByMedTeam`,
                }}
                requestParams={requestParams}
              />
            </Col>
          </Row>
        </>
      ),
    },
  ];

  return (
    <Card>
      <Tabs
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={globalState.dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </Card>
  );
};

export default DrgHospAnalysis;
