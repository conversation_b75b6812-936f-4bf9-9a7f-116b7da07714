import { name } from './package.json';
import {
  slaveCommonConfig,
  extraBabelIncludes,
  extraWebPackPlugin,
} from '../../.umirc.commom';

export default {
  base: name,
  publicPath: '/uniHqms/',
  outputPath: '../../dist/uniHqms',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  qiankun: { slave: {} },

  plugins: [
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/oper/hosp',
    },
    {
      path: '/oper',
      routes: [
        {
          path: '/oper/hosp',
          exact: true,
          component: '@/pages/oper/hosp/index2',
        },
        {
          path: '/oper/dept',
          exact: true,
          component: '@/pages/oper/dept/index2',
        },
        {
          path: '/oper/medTeam',
          exact: true,
          component: '@/pages/oper/medTeam/index2',
        },
      ],
    },
    {
      path: '/cmi',
      routes: [
        {
          path: '/cmi/hospLevelCmi',
          exact: true,
          component: '@/pages/cmi/hospLevelCmi/index',
        },
        {
          path: '/cmi/deptLevelCmi',
          exact: true,
          component: '@/pages/cmi/deptLevelCmi/index',
        },
        {
          path: '/cmi/medTeamLevelCmi',
          exact: true,
          component: '@/pages/cmi/medTeamLevelCmi/index',
        },
      ],
    },
    {
      path: '/quality',
      routes: [
        {
          path: '/quality/hosp',
          exact: true,
          component: '@/pages/quality/hosp/index2',
        },
        {
          path: '/quality/dept',
          exact: true,
          component: '@/pages/quality/dept/index2',
        },
        {
          path: '/quality/medTeam',
          exact: true,
          component: '@/pages/quality/medTeam/index2',
        },
      ],
    },
    {
      path: '/operComplicationComposition',
      routes: [
        {
          path: '/operComplicationComposition/hosp',
          exact: true,
          component: '@/pages/operComplicationComposition/hosp/index2',
        },
        {
          path: '/operComplicationComposition/dept',
          exact: true,
          component: '@/pages/operComplicationComposition/dept/index2',
        },
        {
          path: '/operComplicationComposition/medTeam',
          exact: true,
          component: '@/pages/operComplicationComposition/medTeam/index2',
        },
      ],
    },
    {
      path: '/sd',
      routes: [
        {
          path: '/sd/hosp',
          exact: true,
          component: '@/pages/sd/hosp/index2',
        },
        {
          path: '/sd/dept',
          exact: true,
          component: '@/pages/sd/dept/index2',
        },
        {
          path: '/sd/medTeam',
          exact: true,
          component: '@/pages/sd/medTeam/index2',
        },
      ],
    },
    {
      path: '/tcm',
      routes: [
        {
          path: '/tcm/hosp',
          exact: true,
          component: '@/pages/tcm/hosp/index2',
        },
        {
          path: '/tcm/dept',
          exact: true,
          component: '@/pages/tcm/dept/index2',
        },
        {
          path: '/tcm/medTeam',
          exact: true,
          component: '@/pages/tcm/medTeam/index2',
        },
      ],
    },
    {
      path: '/score',
      exact: true,
      component: '@/pages/quality/score/index2',
    },
  ],

  proxy: {
    '/hqmsAddress': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/hqmsAddress': '' },
      secure: false, // https的dev后端的话需要配
    },
    '/common': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/common': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
};
