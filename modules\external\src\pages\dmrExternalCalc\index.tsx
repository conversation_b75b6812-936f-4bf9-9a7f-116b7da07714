import { Ta<PERSON>, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import qs from 'qs';
import { ProDescriptions } from '@ant-design/pro-descriptions';
import { BaseInfoColumns } from './columns';
import IcdeTables from './components/icdeTables';
import OperTables from './components/operTables';
import FeeInfoDescription from './components/feeInfo';
import RightContainer from './components/rightContainer';
import './index.less';
import { useModel } from 'umi';

const dmrWithHisId =
  (window as any).externalConfig?.['external']?.dmrWithHisId ?? false;

const DmrExternalCalcIndex = (props) => {
  // 获传的参数
  const { globalState, setQiankunGlobalState, dmrIndexProps } = useModel(
    '@@qiankunStateFromMaster',
  );

  const [currentDmrHisId, setCurrentDmrHisId] = useState('');
  const [currentFullCard, setCurrentFullCard] = useState({
    CardFlat: {},
    IcdeResult: { IcdeDscgs: [] },
    InsurIcdeDscgs: [],
    Opers: [],
    InsurOpers: [],
  });
  const [currentResponseContent, setCurrentResponseContent] = useState<any>({
    CheckResult: { ChsMetricsResult: {} },
    SettleCheckResult: {},
  });
  const [relatedProps, setRelatedProps] = useState({});

  const {
    data: externalCalcRecordData,
    mutate: jsonLogicSrcObjectDataMutate,
    loading: externalCalcRecordLoading,
    run: externalCalcRecordReq,
  } = useRequest(
    (hisId) => {
      return uniCommonService(
        dmrWithHisId
          ? 'Api/Dmr/DmrExternalCalc/ExternalCalcWithHisId'
          : 'Api/Dmr/DmrExternalCalc/GetExternalCalcRecord',
        {
          method: 'POST',
          data: { hisId },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          let { FullCard, ResponseContent } = response?.data;
          setCurrentFullCard(JSON.parse(FullCard));
          setCurrentResponseContent(JSON.parse(ResponseContent));
        } else {
        }
      },
    },
  );

  useEffect(() => {
    if (location.search) {
      const search = location.search.replace(/\+/g, '%2B');
      let urlParam = qs.parse(search, {
        ignoreQueryPrefix: true,
      });
      if (urlParam.hisId && urlParam?.hisId !== currentDmrHisId) {
        setCurrentDmrHisId(urlParam.hisId);
      }
    } else {
      if (dmrIndexProps?.extra?.hisId) {
        setCurrentDmrHisId(dmrIndexProps?.extra?.hisId);
      }
    }
  }, [location?.search, dmrIndexProps]);

  useEffect(() => {
    if (currentDmrHisId.length) {
      externalCalcRecordReq(currentDmrHisId);
    }
  }, [currentDmrHisId]);

  useEffect(() => {
    if (currentResponseContent.CheckResult) {
      if (currentResponseContent.CheckResult?.ChsMetricsResult) {
        let chsMetricsResult =
          currentResponseContent.CheckResult?.ChsMetricsResult;
        if (chsMetricsResult?.DrgsResult?.length) {
          setRelatedProps(
            Object.values(chsMetricsResult?.DrgsResult[0]?.RelatedProps || []),
          );
        }
      }
    }
  }, [currentResponseContent]);

  const items = [
    {
      key: '1',
      label: '病案信息',
      children: (
        <>
          <div>
            <h4 className="title">（一）基本信息</h4>
            <Spin spinning={externalCalcRecordLoading}>
              <ProDescriptions
                size="small"
                column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
                dataSource={currentFullCard.CardFlat}
                columns={BaseInfoColumns}
              ></ProDescriptions>
            </Spin>
          </div>
          <div>
            <h4 className="title">（二）诊断信息</h4>
            <IcdeTables
              relatedProps={relatedProps}
              dataSource={currentFullCard}
              loading={externalCalcRecordLoading}
              checkResult={currentResponseContent.CheckResult}
            />
          </div>
          <div>
            <h4 className="title">（三）手术信息</h4>
            <OperTables
              relatedProps={relatedProps}
              dataSource={currentFullCard}
              loading={externalCalcRecordLoading}
              checkResult={currentResponseContent.CheckResult}
            />
          </div>
        </>
      ),
    },
    {
      key: '2',
      label: '收费信息',
      children: (
        <>
          <Spin spinning={externalCalcRecordLoading}>
            <FeeInfoDescription dataSource={currentFullCard.CardFlat} />
          </Spin>
        </>
      ),
    },
  ];

  return (
    <>
      <div className="card-container">
        <div className="left-container">
          <Tabs defaultActiveKey="1" items={items} size="large" />
        </div>
        <div className="right-container">
          <RightContainer
            loading={externalCalcRecordLoading}
            dataSource={currentResponseContent.CheckResult}
            settleCheckResult={currentResponseContent.SettleCheckResult}
          />
        </div>
      </div>
    </>
  );
};

export default DmrExternalCalcIndex;
