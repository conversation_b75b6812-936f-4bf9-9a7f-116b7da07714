import { useEffect, useMemo, useRef } from 'react';
import { useRequest } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { ExportIconBtn, UniTable } from '@uni/components/src';
import { uniCommonService } from '@uni/services/src';
import {
  Button,
  Card,
  Col,
  Divider,
  Popconfirm,
  Row,
  Space,
  TableProps,
  Tooltip,
  message,
} from 'antd';
import { useSafeState } from 'ahooks';
import { RespVO } from '@uni/commons/src/interfaces';
import { DmrSignOutRecord } from '../interface';
import { sortingHandler } from '@/utils/widgets';
import _ from 'lodash';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { DmrSignOutSearchColumns } from './columns';
import { useTimelineReq } from '@/hooks';
import PatTimeline from '@/components/PatTimeline';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { pickOnlyNeedKeys } from '@uni/utils/src/search-context';
import PdfPrint from '@uni/components/src/pdf-print';
import { UndoOutlined } from '@ant-design/icons';

const DmrSignOutSearch = () => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster');

  // timeline请求
  const [timelineItems, { setParams }] = useTimelineReq();

  // 本地状态
  const [tableData, setTableData] = useSafeState<DmrSignOutRecord[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useSafeState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useSafeState<DmrSignOutRecord[]>([]);
  const [clickedItem, setClickedItem] = useSafeState<DmrSignOutRecord | null>(
    null,
  );
  const [columns, setColumns] = useSafeState<any[]>([]);
  const [sortConfig, setSortConfig] = useSafeState<any>(null);

  // 分页
  const [pagination, setPagination] = useSafeState({
    current: 1,
    pageSize: 10,
    total: 0,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    hideOnSinglePage: false,
  });

  // 获取表格数据
  const {
    data: signedOutListData,
    loading: getSignedOutListLoading,
    run: getSignedOutList,
  } = useRequest(
    (params, current = 1, pageSize = 10, sorter = {}) =>
      uniCommonService('Api/Mr/TraceRecord/DmrSignedOutList', {
        method: 'POST',
        data: {
          ..._.pick(params, ['hospCode', 'outWard', 'DmrSignOutOperator']),
          // 自定义的其他查询参数
          ...pickOnlyNeedKeys(params, true),
          // 分页和排序
          SkipCount: (current - 1) * pageSize,
          MaxResultCount: pageSize,
          sorting: handleSorting(sorter),
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          const { Items, TotalCount } = res.data || {};
          return { data: Items || [], total: TotalCount || 0 };
        }
        return { data: [], total: 0 };
      },
      onSuccess: ({ data, total }) => {
        setTableData(data);
        setPagination((prevPagination) => ({
          ...prevPagination,
          total,
        }));
      },
    },
  );

  // 获取列定义
  const { run: getSignedOutListColumns, mutate: setSignedOutListColumns } =
    useRequest(
      () =>
        uniCommonService('Api/Mr/TraceRecord/DmrSignedOutList', {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        }),
      {
        formatResult: (res: RespVO<any>) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            return tableColumnBaseProcessor(
              DmrSignOutSearchColumns,
              res.data?.Columns,
            );
          }
          return [];
        },
        onSuccess: (columnsData) => {
          setColumns(columnsData);

          // 提取默认排序配置
          const defaultSortColumn = columnsData?.find((col) => col.orderMode);
          if (defaultSortColumn) {
            const defaultSorting = {
              field: defaultSortColumn.data,
              order: defaultSortColumn.orderMode,
            };
            console.log('Default sorting:', defaultSorting);
            setSortConfig(defaultSorting);
          }
        },
      },
    );

  // 批量查询所有数据（用于全选/清空功能）
  const { loading: selectAllReqLoading, run: selectAllReq } = useRequest(
    (params, totalCount) =>
      uniCommonService('Api/Mr/TraceRecord/DmrSignedOutList', {
        method: 'POST',
        data: {
          ..._.pick(params, ['hospCode', 'outWard', 'DmrSignOutOperator']),
          // 自定义的其他查询参数
          ...pickOnlyNeedKeys(params, true),
          // 获取全部数据
          SkipCount: 0,
          MaxResultCount: totalCount || 9999,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          const { Items } = res.data || {};
          return Items || [];
        }
        return [];
      },
    },
  );

  // 撤销出库
  const { loading: revertSignOutLoading, run: revertSignOut } = useRequest(
    (barCodes) =>
      uniCommonService('Api/Mr/Tracing/DmrRevertSignOut', {
        method: 'POST',
        data: { BarCodes: barCodes },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        return res;
      },
      onSuccess: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          message.success('撤销出库成功');
          // 刷新表格数据，使用当前的排序配置
          getSignedOutList(
            searchParams,
            pagination.current,
            pagination.pageSize,
            sortConfig,
          );
          // 重置选中状态
          setSelectedRowKeys([]);
          setSelectedRows([]);
          // 重置时间轴
          resetTimeline();
        } else {
          message.error(res?.message || '撤销出库失败');
        }
      },
    },
  );

  // 处理排序参数
  const handleSorting = (sorter: any) => {
    // 如果是来自默认列配置的排序
    if (sortConfig) {
      const { field, order } = sortConfig;
      return `${field} ${order === 'descend' ? 'desc' : 'asc'}`;
    }
    // 如果是来自表格交互的排序
    if (sorter && Object.keys(sorter).length > 0) {
      return sortingHandler(sorter);
    }

    return undefined;
  };

  // 表格变更处理
  const tableOnChange: TableProps<DmrSignOutRecord>['onChange'] = (
    pagi,
    filters,
    sorter,
    extra,
  ) => {
    setPagination((prev) => ({
      ...prev,
      current: pagi.current,
      pageSize: pagi.pageSize,
    }));

    // 更新排序配置
    if (sorter && !Array.isArray(sorter) && sorter.column) {
      setSortConfig({
        field: sorter.field,
        order: sorter.order,
      });
    }

    getSignedOutList(searchParams, pagi.current, pagi.pageSize, sorter);
  };

  // 重置时间轴
  const resetTimeline = () => {
    setClickedItem(null);
    setParams(null);
  };

  // 配置表格列，添加操作列
  const tableColumns = useMemo(() => {
    if (!columns.length) return [];

    const actionColumn = {
      dataIndex: 'action',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 80,
      render: (_, record) => (
        <Space>
          <Tooltip title="撤销出库">
            <Popconfirm
              title="确定要撤销出库吗？"
              onConfirm={() => revertSignOut([record.BarCode])}
              okText="确定"
              cancelText="取消"
            >
              <UndoOutlined className="icon_blue-color" />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    };

    return [...columns, actionColumn];
  }, [columns]);

  // 加载列定义
  useEffect(() => {
    getSignedOutListColumns();
  }, []);

  // 加载数据
  useEffect(() => {
    // 确保已经加载了列定义和排序配置
    if (columns.length > 0) {
      getSignedOutList(
        searchParams,
        pagination.current,
        pagination.pageSize,
        sortConfig,
      );
      resetTimeline();
    }
  }, [searchParams, columns, sortConfig]);

  // 行选择处理
  const rowSelection: any = {
    selectedRowKeys,
    type: 'checkbox',
    columnWidth: 50,
    preserveSelectedRowKeys: true,
    onChange: (
      selectedKeys: React.Key[],
      selectedItems: DmrSignOutRecord[],
    ) => {
      setSelectedRowKeys(selectedKeys);
      setSelectedRows(selectedItems);
    },
    selections: [
      {
        key: 'backendSelectAll',
        text: '全选当前所有',
        onSelect: async () => {
          let result = await selectAllReq(searchParams, pagination.total);
          if (result?.length) {
            // 合并当前选择和全部数据
            const newSelectedRows = _.unionBy(selectedRows, result, 'BarCode');
            setSelectedRows(newSelectedRows);
            setSelectedRowKeys(newSelectedRows.map((item) => item.BarCode));
          } else {
            console.error('获取全部数据失败');
          }
        },
      },
      {
        key: 'backendClearAll',
        text: '清空当前所有',
        onSelect: async () => {
          let result = await selectAllReq(searchParams, pagination.total);
          if (result?.length) {
            // 从当前选择中移除全部数据
            const newSelectedRows = _.differenceBy(
              selectedRows,
              result,
              'BarCode',
            );
            setSelectedRows(newSelectedRows);
            setSelectedRowKeys(newSelectedRows.map((item) => item.BarCode));
          } else {
            console.error('获取全部数据失败');
          }
        },
      },
    ],
  };

  return (
    <>
      <Card
        title="病案出库查询列表"
        extra={
          <Space>
            <Popconfirm
              title="确定批量撤销出库？"
              onConfirm={() => {
                if (selectedRowKeys.length === 0) {
                  message.warning('请先选择要撤销的记录');
                  return;
                }
                revertSignOut(selectedRows.map((item) => item.BarCode));
              }}
              okText="确定"
              cancelText="取消"
              disabled={selectedRowKeys.length === 0}
            >
              <Button disabled={selectedRowKeys.length === 0}>
                批量撤销出库
              </Button>
            </Popconfirm>
            <Divider type="vertical" />
            <PdfPrint
              apiUrl="Api/Mr/TraceRecord/PrintDmrSignedOutList"
              tooltipTitle="打印出库清单"
              buttonType="ghost"
              buttonSize="middle"
              paramType="data"
              disabled={tableData?.length < 1}
              params={() => {
                if (!searchParams) {
                  message.error('请先设置查询条件');
                  return false;
                }
                return {
                  ..._.pick(searchParams, [
                    'hospCode',
                    'outWard',
                    'DmrSignOutOperator',
                  ]),
                  // 自定义的其他查询参数
                  ...pickOnlyNeedKeys(searchParams, true),
                  MaxResultCount: 999999,
                };
              }}
            >
              {/* 打印清单 */}
            </PdfPrint>
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: 'Api/Mr/TraceRecord/ExportDmrSignedOutList',
                method: 'POST',
                data: {
                  ..._.pick(searchParams, [
                    'hospCode',
                    'outWard',
                    'DmrSignOutOperator',
                  ]),
                  // 自定义的其他查询参数
                  ...pickOnlyNeedKeys(searchParams, true),
                  MaxResultCount: 999999,
                },
                fileName: '病案出库查询列表',
              }}
              btnDisabled={tableData?.length < 1}
            />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: 'Api/Mr/TraceRecord/DmrSignedOutList',
                onTableRowSaveSuccess: (columnsData) => {
                  setColumns(
                    tableColumnBaseProcessor(
                      DmrSignOutSearchColumns,
                      columnsData,
                    ),
                  );
                },
              }}
            />
          </Space>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={18}>
            <UniTable
              id="dmr_signed_out_list"
              rowKey="BarCode"
              showSorterTooltip={false}
              loading={
                getSignedOutListLoading ||
                revertSignOutLoading ||
                selectAllReqLoading
              }
              columns={tableColumns}
              forceColumnsUpdate
              dataSource={tableData}
              pagination={pagination}
              onChange={tableOnChange}
              scroll={{ x: 'max-content' }}
              rowSelection={rowSelection}
              dictionaryData={dictData}
              rowClassName={(record) => {
                if (record?.BarCode === clickedItem?.BarCode)
                  return 'row-selected';
                return null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (clickedItem?.BarCode !== record?.BarCode) {
                      setClickedItem(record);
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
            />
          </Col>
          <Col span={6}>
            <PatTimeline
              item={clickedItem}
              timelineItems={timelineItems}
              loading={false}
            />
          </Col>
        </Row>
      </Card>
    </>
  );
};

export default DmrSignOutSearch;
