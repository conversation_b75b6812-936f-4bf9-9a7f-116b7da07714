export interface CombineQueryFieldItem {
  id?: string;
  IsCommon?: boolean;
  commonSequence?: number;
  exprProvider?: string;
  expr?: string;
  directories?: string;
  directorySort?: number;
  columnTitle?: string;
  columnTitle2?: string;
  columnName?: string;
  isVisible?: boolean;
  isOrderable?: boolean;
  orderMode?: string;
  orderPriority?: number;
  aggregable?: boolean;
  responsivePriority?: number;
  className?: string;
  dataType?: string;
  columnPrecision?: number;
  columnScale?: number;
  dictModule?: string;
  dictModuleGroup?: string;
  groupName?: string[];
  shortTitle?: string;
  shortTitleDescription?: string;
  isReadOnly?: boolean;
  returnColumnName?: string;
  allowedOperators?: string[];
  parentColumnName?: string;
  subjectType?: string;
  dictionaryModule?: string;
  dictionaryModuleGroup?: string;
  name?: string;
  orderable?: boolean;
  precision?: number;
  scale?: number;
  title?: string;
  title2?: string;
  visible?: boolean;

  columnSequence?: number;
}

export interface DetailColumnItem {
  name?: string;
  isSignificant?: boolean;
  id?: string;
  exprProvider?: string;
  directories?: string[];
  directorySort?: number;
  title?: string;
  title2?: string;
  visible?: boolean;
  orderable?: boolean;
  orderMode?: string;
  orderPriority?: number;
  aggregable?: boolean;
  responsivePriority?: number;
  className?: string;
  dataType?: string;
  precision?: number;
  scale?: number;
  dictionaryModule?: string;
  dictionaryModuleGroup?: string;
  groupName?: string;
  shortTitle?: string;
  shortTitleDescription?: string;
  isReadOnly?: boolean;

  columnSort?: number;
  columnSequence?: number;

  // 默认展示值
  comboQuerySignificant?: boolean;
  diseaseSignificant?: boolean;
  icdCategorySignificant?: boolean;
}

export interface MetricsAggregateItem extends DetailColumnItem {
  allowedAggregations?: string[];

  aggregationNames?: AggregationNames;
}

export interface MetricsGroupItem extends DetailColumnItem {
  allowedGroupOptions?: string[];
}

export interface MetricsMetricItem extends DetailColumnItem {}

interface AggregationNames {
  [key: string]: AggregateNameItem;
}

export interface AggregateNameItem {
  name?: string;
  dataType?: string;
  title?: string;
  scale?: number;
}

export interface IcdeOperResp {
  Data?: IcdeOperItem[];
  RecordsTotal?: number;
}

export interface IcdeOperItem {
  Code?: string;
  Name?: string;

  InsurCode?: string;
  InsurName?: string;
  HqmsCode?: string;
  HqmsName?: string;

  Degree?: string;
  OperType?: string;
  InsurDegree?: string;
  InsurOperType?: string;
  IsObsolete?: boolean;
  HqmsDegree?: string;
  HqmsOperType?: string;
  IsDaySurgery?: boolean;
  IsMicro?: boolean;
  WtCode?: string;
  WtName?: string;
  WtDegree?: string;
  WtOperType?: string;
  DrgsDegree?: string;
  DrgsOperType?: string;

  IsValid?: boolean;
}

export interface CombineQueryDetail {
  AggregationOutputColumns?: CombineQueryDetailColumnItem[];
  ApplyDict?: boolean;
  DisPlayExpr?: string;
  Expr?: string;
  ExtraConfig?: string;
  GrouperCols?: CombineQueryDetailColumnItem[];
  Id?: string;
  IsPublic?: boolean;
  MenuDirectories?: string[];
  MenuItemUrl?: string;
  MenuSort?: number;
  MetricCols?: CombineQueryDetailColumnItem[];
  Slug?: string;
  SubjectOutputColumns?: CombineQueryDetailColumnItem[];
  Title?: string;

  Diseases?: string[];
}

export interface CombineQueryDetailColumnItem {
  Id?: string;
  CustomTitle?: string;
  AggregationOperator?: string;
  AnaGroupOption?: string;

  ColumnSort?: number;
  Width?: number;
  ExtraProperties?: string;
}

export interface TemplateItem {
  ExtraConfig?: string;
  Id?: string;
  IsPublic?: boolean;
  MenuItemUrl?: string;
  MenuSort?: number;
  Slug?: string;
  TemplateType?: string;
  Title?: string;

  value?: string;
  label?: string;

  columns?: any[];
}
