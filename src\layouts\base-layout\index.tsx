import { But<PERSON>, Card, Col, Form, Layout, Row, Spin, Tooltip } from 'antd';
import React, { ReactNode, useEffect, useState } from 'react';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { dynamicComponentsMap } from '@/utils/dynamicComponents';
import { processSearchData } from '@/utils/headerSearchProcessor';
import isNil from 'lodash/isNil';
import omitBy from 'lodash/omitBy';
import isArray from 'lodash/isArray';
import { headers } from '@/headers';
import { useAsyncEffect, useSessionStorageState } from 'ahooks';
import { Dispatch, useDispatch, useLocation } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import cloneDeep from 'lodash/cloneDeep';
import { useSelector } from '@@/plugin-dva/exports';
import { isEmptyValues } from '@uni/utils/src/utils';
import dayjs from 'dayjs';
import { customDefaultParamsProcessor } from '@/layouts/utils';
import QueryHeaderForm from '@/layouts/base-layout/query-header';
import { QueryParameterConfigurationEditButton } from '@uni/components/src/query-configuration';
import { argDefToHeaderFormItems } from '@uni/components/src/query-configuration/header-processor';
import { headerFormValuesPostProcessor } from '@uni/components/src/query-configuration/header-processor';
import { getDayjsEndOfByType, getDayjsStartOfByType } from '@/utils/utils';
import groupBy from 'lodash/groupBy';
import { uniCommonService } from '@uni/services/src';
import {
  DynamicMenuItem,
  getHeaderKeyOfComponentByComponentHint,
} from '@/layouts/root/dynamic-menu';
import { RespVO } from 'packages/commons/src/interfaces';

const headerSearchForceModified =
  (window as any).externalConfig?.['common']?.headerSearchForceModified ??
  false;

const enableDynamicMenuItem =
  (window as any).externalConfig?.['common']?.enableDynamicMenuItem ?? false;

interface BaseLayoutProps {}

export interface BaseLayoutHeaderItem {
  outerLabelHidden?: boolean; // 目前仅用于 radio date picker
  label?: string | ReactNode;
  required?: boolean;
  componentName: string;
  needFetch?: boolean;
  props?: any;
}

export interface BaseLayoutHeaderProps {
  searchBtnRef?: any;
  form: any;
  headerRef: any;
  headerKey: string | object;
  items: BaseLayoutHeaderItem[];
  value?: any;

  onHeaderItemsChange?: (headerItems: any, headerDataSource: any[]) => void;
}

const BaseLayoutHeader = (props: BaseLayoutHeaderProps) => {
  const dispatch: Dispatch = useDispatch();

  const headerDataSource = useSelector((state: any) => state.uniDict.dictData);

  const form = props?.form;

  const [errorFields, setErrorFields] = useState([]);

  const [headerItems, setHeaderItems] = useState([]);

  React.useImperativeHandle(props?.headerRef, () => {
    return {
      getHeaderItems: () => {
        return headerItems;
      },
    };
  });

  useEffect(() => {
    setHeaderItems([]);
    setErrorFields([]);
  }, [props?.items]);

  useEffect(() => {
    if (!isEmptyValues(headerItems)) {
      console.log('onHeaderItemsChange', headerDataSource, headerItems);
      props?.onHeaderItemsChange &&
        props?.onHeaderItemsChange(headerItems, headerDataSource);
    }
  }, [headerItems, headerDataSource]); // 删除监听headerDataSource 默认header在渲染之前dataSource都已经获取到了，即使没有，在headerItems更新的时候也都已经有了

  useEffect(() => {
    Object.keys(props?.value)?.forEach((key) => {
      if (!isEmptyValues(props?.value?.[key])) {
        form.setFieldValue(key, props?.value?.[key]);
      }
    });
  }, [props?.value]);

  const { initialState }: any = useModel('@@initialState');

  useEffect(() => {
    initializeHeaderDictionary();
  }, []);

  const initializeHeaderDictionary = async () => {
    let dictionaryFetchItems = props.items
      ?.filter((item) => item.needFetch)
      ?.map((headerItem: BaseLayoutHeaderItem) => {
        // fetchKey优先于dataKey
        return {
          module: headerItem?.props.fetchKey || headerItem?.props.dataKey,
          moduleGroup: headerItem?.props?.dataKeyGroup ?? '',
        };
      });

    let groupedByModuleGroup = groupBy(dictionaryFetchItems, 'moduleGroup');
    for (let moduleGroup of Object.keys(groupedByModuleGroup)) {
      if (!isEmptyValues(groupedByModuleGroup[moduleGroup] ?? [])) {
        await dispatch({
          type: 'uniDict/fetchDictionaryData',
          param: {
            modules: (groupedByModuleGroup[moduleGroup] ?? [])?.map(
              (item) => item?.module,
            ),
            moduleGroup: moduleGroup ?? '',
            isHeader: true,
          },
        });
      }
    }
  };

  const onHeaderItemChange = (value: any, dataKey: string) => {
    form.setFieldValue(dataKey, value);

    let editedKeys = form.getFieldValue('editedKeys');
    editedKeys = Array.isArray(editedKeys) ? editedKeys : [];

    form.setFieldValue('editedKeys', new Set([...editedKeys, dataKey]));
  };
  // 做个修改，修改下入参
  const onHeaderItemChangeOnMultipleValues = (
    data: { [key: string]: any }[] | { [key: string]: any },
    dataKey: string,
  ) => {
    console.log(data);
    let dataMap: { [key: string]: any } = {};
    if (isArray(data)) {
      data.forEach((d) => {
        dataMap = { ...d };
      });
    } else {
      dataMap = data;
    }
    Object.keys(dataMap).forEach((key) => {
      form.setFieldValue(key, dataMap[key]);
    });

    let editedKeys = form.getFieldValue('editedKeys');
    editedKeys = Array.isArray(editedKeys) ? editedKeys : [];
    form.setFieldValue(
      'editedKeys',
      new Set([...editedKeys, ...Object.keys(dataMap)]),
    );
  };

  const onSearchBtnClick = (values: any) => {
    setErrorFields([]);

    Emitter.emit(EventConstant.HEADER_SEARCH_CLICK, values);
    props.searchBtnRef.current = true;
  };

  const onSearchBtnClickValidateFailed = ({
    values,
    errorFields,
    outOfDate,
  }) => {
    console.log(
      'onSearchBtnClickValidateFailed',
      values,
      errorFields,
      outOfDate,
    );
    setErrorFields(errorFields);
  };

  let formKeysInOtherPages = Object.keys(props?.value)?.filter((key) => {
    let currentHeaderItem = headerItems?.find(
      (headerItem) =>
        (headerItem?.props?.formKey ??
          headerItem?.props?.valueKey ??
          headerItem?.props?.dataKey) === key,
    );

    return currentHeaderItem === undefined;
  });

  console.log('getFieldsValue in header', form.getFieldsValue());

  return (
    <Card
      id={'base-layout-header-container'}
      className="base-layout-header-container"
    >
      <QueryParameterConfigurationEditButton
        className="header-query-param-edit"
        type={'HEADER'}
        currentSearchOptsLength={headerItems?.length ?? 0}
        setSearchOpts={setHeaderItems}
        formItems={[...props?.items]}
        dictData={headerDataSource}
        queryInterfaceUrl={
          typeof props?.headerKey === 'object'
            ? (props.headerKey as any)?.url
            : undefined
        }
        onFormItemsSaveSuccess={(argDefs, defaultArgs: any) => {
          let transformed = argDefToHeaderFormItems(argDefs, defaultArgs);

          setHeaderItems([...transformed]);
        }}
        setHeaderType={(type) => {
          form.setFieldValue('customizeParamType', type);
        }}
      />

      <Spin spinning={false}>
        <Form
          form={form}
          onFinish={onSearchBtnClick}
          onFinishFailed={onSearchBtnClickValidateFailed}
          autoComplete="off"
          wrapperCol={{ flex: 1 }}
          // initialValues={props?.value ?? {}}
        >
          {formKeysInOtherPages?.map((key) => {
            return <Form.Item hidden={true} name={key} />;
          })}

          {/*修改过的字段列表*/}
          <Form.Item hidden={true} name={'editedKeys'} />

          <Form.Item
            hidden={true}
            name={'customizeParamType'}
            initialValue={false}
          />

          <Row gutter={[16, 16]}>
            <QueryHeaderForm
              headerRef={props.headerRef}
              headerKey={props.headerKey}
              items={headerItems}
              form={form}
              headerDataSource={headerDataSource}
              errorFields={errorFields}
              onHeaderItemChange={onHeaderItemChange}
              onHeaderItemChangeOnMultipleValues={
                onHeaderItemChangeOnMultipleValues
              }
            />
            <Col
              span={6}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'end',
              }}
            >
              <Button
                type="primary"
                htmlType="submit"
                className="base-layout-search-btn"
              >
                查询
              </Button>
            </Col>
          </Row>
        </Form>
      </Spin>
    </Card>
  );
};

const parameterDateMock =
  (window as any).externalConfig?.['common']?.parameterDateMock ?? false;

const customDefaultSearchParam =
  (window as any).externalConfig?.['common']?.customDefaultSearchParam ?? {};

const BaseLayout = (props: any) => {
  const route = props.route;
  const { globalState } = useModel('@@qiankunStateForSlave');

  const [dynamicMenuItems, setDynamicMenuItems] = useState([]);

  const location = useLocation();

  const headerRef = React.useRef(null);

  const [headerKey, setHeaderKey] = React.useState(route?.headerKey);

  const [originHeaderKey, setOriginHeaderKey] = React.useState(
    route?.headerKey,
  );

  (window as any).headerRef = headerRef;

  const [form] = Form.useForm();

  const searchBtnRef = React.useRef(false);

  useAsyncEffect(async () => {
    if (enableDynamicMenuItem === true) {
      // 获取 DynamicMenuItems
      let dynamicMenuItemsResponse: RespVO<DynamicMenuItem[]> =
        await uniCommonService('Api/Sys/ClientKitSys/GetDynamicMenuItems', {
          method: 'POST',
        });

      if (
        dynamicMenuItemsResponse?.code === 0 &&
        dynamicMenuItemsResponse?.statusCode === 200
      ) {
        setDynamicMenuItems(dynamicMenuItemsResponse?.data);
      }
    }
  }, []);

  useAsyncEffect(async () => {
    // hack headerKey
    let originHeaderKey = route?.headerKey;
    let headerKeySet = false;
    let headerKey = route?.headerKey || 'default';
    if (typeof route?.headerKey === 'object') {
      headerKey = route?.headerKey?.defaultKey || 'default';
      headerKeySet = true;
    }

    if (route?.microApp) {
      let microAppHeaderKey =
        route?.headerKeys?.[location?.pathname.replace(route?.path, '')];
      originHeaderKey = microAppHeaderKey;
      headerKey = microAppHeaderKey || 'default';
      headerKeySet = true;
      if (typeof microAppHeaderKey === 'object') {
        headerKey = microAppHeaderKey?.defaultKey || 'default';
        headerKeySet = true;
      }
    }

    if (!isEmptyValues(dynamicMenuItems)) {
      const dynamicMenuItem = dynamicMenuItems?.find(
        (item) =>
          item?.MenuItemUrl ===
          location?.pathname?.replace(`${item?.NavUrl}/`, ''),
      );
      if (!isEmptyValues(dynamicMenuItem)) {
        // component Hint 换key
        let dynamicHeaderKey = getHeaderKeyOfComponentByComponentHint(
          dynamicMenuItem?.ComponentHint,
        );
        headerKey = dynamicHeaderKey ?? 'default';
      } else if (headerKeySet === false) {
        headerKey = 'default';
      }
    }
    setHeaderKey(headerKey);
    setOriginHeaderKey(originHeaderKey);
  }, [location?.pathname, route, dynamicMenuItems]);

  useEffect(() => {
    // 当pathname变更的时候清除searchKeyword
    Emitter.emit(EventConstant.HEADER_SEARCH_PARAM_CLEAR, ['searchKeyword']);
    searchBtnRef.current = false;
  }, [location.pathname]);

  const dispatch: Dispatch = useDispatch();

  // FIXME 默认值
  const [childrenSearchValues, setChildrenSearchValues] =
    useSessionStorageState<any>('searchOpts', {
      defaultValue: {
        dateType: 'month',
        dateRange: parameterDateMock
          ? ['2020-01-01', '2020-06-01']
          : [
              dayjs().startOf('month').format('YYYY-MM-DD'),
              dayjs().endOf('month').format('YYYY-MM-DD'),
            ],
        // hospCodes: ['1'],
        CliDepts: [],
        // FIXME 下面的字段可能需要改动
        // hospCode: '0',
        deptCode: '176',
        textSelectValue: 'PatNo',
        singleDate: dayjs().format('YYYY-MM-DD'),

        ...customDefaultParamsProcessor(customDefaultSearchParam),
      },
    });

  // hack 默认值 院区 & 科室
  // useEffect(() => {
  //   // 院区
  //   if (
  //     globalState?.dictData?.Hospital?.length > 0 &&
  //     !childrenSearchValues?.CliDepts?.length
  //   ) {
  //     setTimeout(() => {
  //       console.log(
  //         'in',
  //         globalState?.dictData?.Hospital?.map((d) => d?.Code),
  //         globalState?.dictData?.CliDepts?.map((d) => d?.Code),
  //       );
  //     }, 50);
  //     setChildrenSearchValues({
  //       ...childrenSearchValues,
  //       hospCodes: globalState?.dictData?.Hospital?.map((d) => d?.Code),
  //       CliDepts: globalState?.dictData?.CliDepts?.map((d) => d?.Code),
  //     });
  //   }
  // }, [globalState?.dictData?.Hospital, childrenSearchValues]);

  const selectMultipleSingleProcessor = (
    searchParams: any,
    headerItems: any[],
    headerDataSource: any[],
  ) => {
    headerItems?.forEach((headerItem) => {
      // 当且仅当是 select的时候
      if (headerItem?.componentName === 'Select') {
        let valueKey =
          headerItem?.props?.formKey ??
          headerItem?.props?.valueKey ??
          headerItem?.props?.dataKey;

        let searchParamsKeyToValue = searchParams?.[valueKey];
        // 不为空的时候
        if (!isEmptyValues(searchParamsKeyToValue)) {
          // 多选
          if (headerItem?.props?.mode === 'multiple') {
            if (!Array.isArray(searchParamsKeyToValue)) {
              searchParams[valueKey] = [searchParamsKeyToValue];
            }
          } else {
            if (Array.isArray(searchParamsKeyToValue)) {
              searchParams[valueKey] = searchParamsKeyToValue?.at(0);
            }
          }
        }
      }

      // 当且仅当是 componentName 包含 Constrained 的时候
      if (headerItem?.componentName?.includes('Constrained')) {
        let valueKey =
          headerItem?.props?.formKey ??
          headerItem?.props?.valueKey ??
          headerItem?.props?.dataKey;

        let searchParamsKeyToValue = searchParams?.[valueKey];
        let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
        // 不为空的时候

        if (
          !isEmptyValues(searchParamsKeyToValue) &&
          headerDataSource &&
          userInfo?.[valueKey?.charAt(0).toUpperCase() + valueKey?.slice(1)]
            ?.length > 0
        ) {
          // 受限数据
          let dataSource = cloneDeep(
            headerItem?.props?.dataKeyGroup
              ? headerDataSource?.[headerItem?.props?.dataKeyGroup]?.[
                  headerItem?.props?.dataKey
                ]?.slice()
              : headerDataSource?.[headerItem?.props?.dataKey]?.slice() || [],
          );
          // value处理 userInfo 需要与valueKey相同（HospCode, MajorPerfDepts, PerfDepts, CliDepts, MedTeams）
          if (dataSource?.length > 0) {
            // 当且仅当 hospCode 会出问题 因为hospCode 可能出现 string / array
            if (valueKey?.toLowerCase() === 'hospcode') {
              if (
                !Array.isArray(searchParamsKeyToValue) &&
                typeof searchParamsKeyToValue === 'string'
              ) {
                searchParamsKeyToValue = [searchParamsKeyToValue];
              }
            }

            searchParams[valueKey] = searchParamsKeyToValue?.filter((v) =>
              dataSource
                ?.filter((data) =>
                  (
                    userInfo?.[
                      valueKey?.charAt(0).toUpperCase() + valueKey?.slice(1)
                    ] || []
                  )?.includes(data.Code),
                )
                ?.some((d) => d.Code === v),
            );
          }
        }
      }
    });
  };

  useEffect(() => {
    Emitter.on(EventConstant.HEADER_SEARCH_PARAM_CLEAR, (clearKeys) => {
      let headerParams = {
        ...childrenSearchValues,
      };

      clearKeys?.forEach((key) => {
        if (headerParams[key]) {
          delete headerParams[key];
        }
      });

      setChildrenSearchValues(headerParams);
    });

    (global?.window as any)?.eventEmitter?.on(
      EventConstant.HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY,
      (data) => {
        let headerParams = {
          ...childrenSearchValues,
          searchByProgrammatically: true,
        };

        let searchNow = data?.['searchNow'] ?? false;
        if (searchNow) {
          delete data?.['searchNow'];
        }

        let initial = data?.['initial'] ?? false;
        if (searchNow) {
          delete data?.['initial'];
        }

        if (data) {
          Object.keys(data).forEach((key) => {
            if (initial) {
              if (isEmptyValues(headerParams[key])) {
                headerParams[key] = data[key];
              }
            } else {
              headerParams[key] = data[key];
            }
          });
        }

        headerRef?.current?.getHeaderItems()?.forEach((item) => {
          // 特殊处理一下 DateRadioPicker 在默认情况下 会出问题 索性直接 处理一下
          if (item?.componentName === 'DateRadioPicker') {
            let dateType = headerParams['dateType'] ?? 'month';
            let transformedDataRange = [];
            headerParams['dateRange']?.forEach((dateItem, index) => {
              if (index === 0) {
                transformedDataRange.push(
                  getDayjsStartOfByType(dateType, dayjs(dateItem)).format(
                    'YYYY-MM-DD',
                  ),
                );
              } else {
                transformedDataRange.push(
                  getDayjsEndOfByType(dateType, dayjs(dateItem)).format(
                    'YYYY-MM-DD',
                  ),
                );
              }
            });

            headerParams['dateRange'] = transformedDataRange;
            searchNow = true;
          }
        });

        if (searchNow) {
          Emitter.emit(EventConstant.HEADER_SEARCH_CLICK, headerParams);
          searchBtnRef.current = true;
        } else {
          setChildrenSearchValues(headerParams);
        }
      },
    );

    return () => {
      Emitter.off(EventConstant.HEADER_SEARCH_PARAM_CLEAR);
      (global?.window as any)?.eventEmitter?.off(
        EventConstant.HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY,
      );
    };
  }, [childrenSearchValues]);

  // 为了保证 切换到radioPicker的时候，默认传的时间是正确的
  useEffect(() => {
    if (headerRef?.current?.getHeaderItems()) {
      (global?.window as any)?.eventEmitter.emit(
        EventConstant.HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY,
        {},
      );
    }
  }, [headerRef?.current?.getHeaderItems()]);

  useEffect(() => {
    // value processor
    // 解析一份 带有range的东西
    if (childrenSearchValues?.searchByProgrammatically !== true) {
      if (isEmptyValues(headerRef?.current?.getHeaderItems())) {
        return;
      }
    }

    let headerItems = headerRef?.current?.getHeaderItems();
    console.log('headerItems', headerItems);
    let processedValues = headerFormValuesPostProcessor(
      headerItems,
      childrenSearchValues,
    );
    console.log('processedValues', processedValues);

    if (!isEmptyValues(childrenSearchValues?.searchByProgrammatically)) {
      delete childrenSearchValues?.searchByProgrammatically;
    }

    processedValues['triggerSource'] =
      searchBtnRef?.current === true ? 'btnClick' : 'other';

    Emitter.emit(
      EventConstant.HEADER_SEARCH_PARAM_CHANGE,
      // TODO hack 一份
      processedValues,
    );
  }, [childrenSearchValues, headerRef?.current?.getHeaderItems()]);

  const searchDataProcess = (data) => {
    // 处理一下date format
    // 这里其实可以拿到headers的
    headerRef?.current?.getHeaderItems()?.forEach((item) => {
      data[item.props?.dataKey] = processSearchData(
        item.componentName,
        data[item.props?.dataKey],
      );
    });
    setChildrenSearchValues(
      headerSearchForceModified === false
        ? cloneDeep(omitBy(data, isNil))
        : omitBy(data, isNil),
    );
  };

  useEffect(() => {
    Emitter.on(EventConstant.HEADER_SEARCH_CLICK, (data) => {
      console.log('HEADER_SEARCH_CLICK', data);
      searchDataProcess(Object.assign({}, data));
    });

    document.getElementById('content')?.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth',
    });

    return () => {
      Emitter.off(EventConstant.HEADER_SEARCH_CLICK);
    };
  }, []);

  console.log('getFieldsValue', form.getFieldsValue());

  return (
    <Layout>
      {headerKey && headers[headerKey]?.length > 0 && (
        <BaseLayoutHeader
          searchBtnRef={searchBtnRef}
          form={form}
          headerKey={originHeaderKey}
          headerRef={headerRef}
          items={headers[headerKey]}
          value={childrenSearchValues || {}}
          onHeaderItemsChange={(
            headerItems: any[],
            headerDataSource: any[],
          ) => {
            let newChildrenSearchValues = cloneDeep(childrenSearchValues);
            selectMultipleSingleProcessor(
              newChildrenSearchValues,
              headerItems,
              headerDataSource,
            );
            setChildrenSearchValues(newChildrenSearchValues);
          }}
        />
      )}
      {React.Children.map(props.children, (child, i) => {
        if (child) {
          return React.cloneElement(child, { ...childrenSearchValues });
        }
      })}
    </Layout>
  );
};

export default BaseLayout;
