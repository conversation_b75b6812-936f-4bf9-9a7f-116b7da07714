/**
 * @file Average 平均值
 * <AUTHOR> Generated by IconPark
 */

/* tslint:disable: max-line-length */
/* eslint-disable max-len */
import React from 'react';
import { ISvgIconProps, IconWrapper } from './runtime';

export default IconWrapper('average', true, (props: ISvgIconProps) => (
  <svg width={props.size} height={props.size} viewBox="0 0 48 48" fill="none">
    <path
      d="M4 4V44H44"
      stroke={props.colors[0]}
      strokeWidth={props.strokeWidth}
      strokeLinecap={props.strokeLinecap}
      strokeLinejoin={props.strokeLinejoin}
    />
    <path
      d="M10 38C10 38 15.3125 4 27 4C38.6875 4 44 38 44 38"
      stroke={props.colors[0]}
      strokeWidth={props.strokeWidth}
      strokeLinecap={props.strokeLinecap}
      strokeLinejoin={props.strokeLinejoin}
    />
    <path
      d="M10 24L44 24"
      stroke={props.colors[0]}
      strokeWidth={props.strokeWidth}
      strokeLinecap={props.strokeLinecap}
      strokeLinejoin={props.strokeLinejoin}
      strokeDasharray="2 6"
    />
  </svg>
));
