import './index.less';
import { Tabs } from 'antd';
import { history } from 'umi';
import { useEffect, useRef, useState } from 'react';
import { useModel } from '@@/plugin-model/useModel';
import { isEmptyValues } from '@uni/utils/src/utils';
import { BasePageProps } from '@uni/commons/src/interfaces';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import CoderTab from '@/pages/analysis/components/coderTab/index';
import CliDeptTab from '@/pages/analysis/components/cliDeptTab/index';
import PieTreeTrend from '@/pages/analysis/components/pieTreeTrend/index';

interface DmrManagementProps extends BasePageProps {}

const CheckResultAnalysis = (props: DmrManagementProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, errorLevel, coders, qualityMonitorLevel } =
    globalState.searchParams;
  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('Category');

  const flagRef = useRef(1);

  useEffect(() => {
    // 参数
    let params = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: isEmptyValues(hospCodes) ? undefined : hospCodes,
      ErrorLevel: errorLevel,
      Coders: coders,
      MonitorLevel: qualityMonitorLevel,
    };
    // 判断是否点击了按钮
    if (flagRef.current === 1) {
      setQiankunGlobalState({
        ...globalState,
        searchParams: {
          ...globalState?.searchParams,
          triggerSource: 'other',
        },
      });
      setTimeout(() => {
        if (flagRef.current === 1) {
          flagRefPlus();
        }
      }, 1000);
    } else {
      if (globalState?.searchParams?.triggerSource == 'btnClick') {
        setTableParams(params);
      }
    }

    // 监听路由变化
    const unListen = history.listen((location, action) => {
      // 在这里执行你需要的操作
      if (location.pathname !== '/main/analysis/hosp') {
        setTimeout(() => {
          flagRef.current = 1;
          setQiankunGlobalState({
            ...globalState,
            searchParams: {
              ...globalState?.searchParams,
              triggerSource: 'other',
            },
          });
        }, 200);
      }
    });

    // 组件卸载时取消监听
    return () => {
      unListen();
    };
  }, [globalState?.searchParams]);

  const flagRefPlus = () => {
    flagRef.current++;
  };

  let tabItems = [
    {
      key: 'Category',
      label: '问题分类统计',
      children: (
        <>
          <PieTreeTrend
            special={true}
            tableParams={tableParams}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: '',
                args: {
                  ...tableParams,
                  ...record?.args,
                },
                detailsUrl: 'Dmr/DmrQcStats/GetQcCards',
                dictData: dictData,
              });
            }}
          />
        </>
      ),
    },
    {
      key: 'Coder',
      label: '编码员问题统计分析',
      children: (
        <>
          <CoderTab
            flagRef={flagRef.current}
            flagRefPlus={flagRefPlus}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: '',
                args: {
                  ...tableParams,
                  ...record?.args,
                },
                detailsUrl: 'Dmr/DmrQcStats/GetQcCards',
                dictData: globalState?.dictData,
              });
            }}
          />
        </>
      ),
    },
    {
      key: 'CliDept',
      label: '科室问题统计分析',
      children: (
        <>
          <CliDeptTab
            flagRef={flagRef.current}
            flagRefPlus={flagRefPlus}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: '',
                args: {
                  ...tableParams,
                  ...record?.args,
                },
                detailsUrl: 'Dmr/DmrQcStats/GetQcCards',
                dictData: globalState?.dictData,
              });
            }}
          />
        </>
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
    </div>
  );
};

export default CheckResultAnalysis;
