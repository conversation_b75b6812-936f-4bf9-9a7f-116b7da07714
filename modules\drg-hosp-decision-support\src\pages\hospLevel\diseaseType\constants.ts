export const NormalStat = [
  {
    contentData: 'PatCnt',
    clickable: true,
    footerYoy: true,
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
  },
  {
    contentData: 'PatRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgRw',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgTotalFee',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgInPeriod',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'MedicineFeeRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'MaterialFeeRatio',
    clickable: true,
    footerYoy: true,
  },
];

export enum ReqActionType {
  BundledADrgCompositionOfHosp = 'HospDrg/BundledADrgCompositionOfHosp',
  MdcCoverageOfHosp = 'HospDrg/MdcCoverageOfHosp',
  ADrgCompositionByHosp = 'HospDrg/ADrgCompositionByHosp',
  ADrgCompositionByCliDept = 'HospDrg/ADrgCompositionByCliDept',
  // ADrgCompositionByMedTeam = 'MedTeamDrg/ADrgCompositionByMedTeam',
  ADrgCompositionOfHosp = 'HospDrg/ADrgCompositionOfHosp',
  // DrgCompositionWithDiseaseOfHosp = '/HospDrg/DrgCompositionWithDiseaseOfHosp'
}

export const DiseaseBmChartSelectOptions = [
  'DeathRatio',
  'AvgRw',
  'DrgCnt',
  'AvgInPeriod',
  'AvgTotalFee',
  'AvgMedicineFee',
  'MedicineFeeRatio',
  'AvgMaterialFee',
  'MaterialFeeRatio',
  'Cei',
  'Tei',
];
