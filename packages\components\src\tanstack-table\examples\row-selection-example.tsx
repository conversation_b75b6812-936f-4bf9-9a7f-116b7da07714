import React, { useState, useEffect } from 'react';
import { RowSelectionState } from '@tanstack/react-table';
import UniTableNG from '../index';

// 示例数据
const sampleData = [
  { id: '1', name: '张三', age: 25, department: '技术部' },
  { id: '2', name: '李四', age: 30, department: '产品部' },
  { id: '3', name: '王五', age: 28, department: '设计部' },
  { id: '4', name: '赵六', age: 32, department: '运营部' },
  { id: '5', name: '钱七', age: 27, department: '技术部' },
];

// 示例列配置
const sampleColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
  },
];

const RowSelectionExample: React.FC = () => {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  // 方法1: 使用 defaultSelectAll 属性自动选中所有行
  const handleDefaultSelectAllExample = () => {
    return (
      <div>
        <h3>方法1: 使用 defaultSelectAll 属性</h3>
        <UniTableNG
          columns={sampleColumns}
          dataSource={sampleData}
          rowKey="id"
          enableRowSelection={true}
          defaultSelectAll={true}
          onRowSelectionChange={(selection) => {
            console.log('当前选中的行:', selection);
            setRowSelection(selection);
          }}
        />
      </div>
    );
  };

  // 方法2: 手动设置 rowSelection 状态
  const handleManualSelectAllExample = () => {
    const [manualRowSelection, setManualRowSelection] = useState<RowSelectionState>({});

    // 选中所有行的函数
    const selectAllRows = () => {
      const allSelected: RowSelectionState = {};
      sampleData.forEach((item) => {
        allSelected[item.id] = true;
      });
      setManualRowSelection(allSelected);
    };

    // 清空所有选择的函数
    const clearAllSelection = () => {
      setManualRowSelection({});
    };

    // 组件挂载时自动选中所有行
    useEffect(() => {
      selectAllRows();
    }, []);

    return (
      <div>
        <h3>方法2: 手动控制 rowSelection</h3>
        <div style={{ marginBottom: 16 }}>
          <button onClick={selectAllRows} style={{ marginRight: 8 }}>
            选中所有行
          </button>
          <button onClick={clearAllSelection}>
            清空选择
          </button>
        </div>
        <UniTableNG
          columns={sampleColumns}
          dataSource={sampleData}
          rowKey="id"
          enableRowSelection={true}
          rowSelection={manualRowSelection}
          onRowSelectionChange={(selection) => {
            console.log('当前选中的行:', selection);
            setManualRowSelection(selection);
          }}
        />
      </div>
    );
  };

  // 方法3: 基于条件选择行
  const handleConditionalSelectExample = () => {
    const [conditionalRowSelection, setConditionalRowSelection] = useState<RowSelectionState>({});

    // 选择特定条件的行（例如：技术部的员工）
    const selectTechDepartment = () => {
      const techSelected: RowSelectionState = {};
      sampleData
        .filter(item => item.department === '技术部')
        .forEach((item) => {
          techSelected[item.id] = true;
        });
      setConditionalRowSelection(techSelected);
    };

    // 选择年龄大于28的员工
    const selectAgeAbove28 = () => {
      const ageSelected: RowSelectionState = {};
      sampleData
        .filter(item => item.age > 28)
        .forEach((item) => {
          ageSelected[item.id] = true;
        });
      setConditionalRowSelection(ageSelected);
    };

    return (
      <div>
        <h3>方法3: 基于条件选择行</h3>
        <div style={{ marginBottom: 16 }}>
          <button onClick={selectTechDepartment} style={{ marginRight: 8 }}>
            选择技术部员工
          </button>
          <button onClick={selectAgeAbove28} style={{ marginRight: 8 }}>
            选择年龄>28的员工
          </button>
          <button onClick={() => setConditionalRowSelection({})}>
            清空选择
          </button>
        </div>
        <UniTableNG
          columns={sampleColumns}
          dataSource={sampleData}
          rowKey="id"
          enableRowSelection={true}
          rowSelection={conditionalRowSelection}
          onRowSelectionChange={(selection) => {
            console.log('当前选中的行:', selection);
            setConditionalRowSelection(selection);
          }}
        />
      </div>
    );
  };

  return (
    <div style={{ padding: 24 }}>
      <h2>TanStack Table Row Selection 示例</h2>
      
      {handleDefaultSelectAllExample()}
      
      <div style={{ margin: '32px 0' }}>
        <hr />
      </div>
      
      {handleManualSelectAllExample()}
      
      <div style={{ margin: '32px 0' }}>
        <hr />
      </div>
      
      {handleConditionalSelectExample()}
      
      <div style={{ marginTop: 32 }}>
        <h4>当前全局选中状态:</h4>
        <pre>{JSON.stringify(rowSelection, null, 2)}</pre>
      </div>
    </div>
  );
};

export default RowSelectionExample;
