import UniEditableTable from '@uni/components/src/table/edittable';
import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  Card,
  Form,
  Modal,
  TableProps,
  message,
  Space,
  Divider,
} from 'antd';
import { useRequest } from '@@/plugin-request/request';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { v4 as uuidv4 } from 'uuid';
import { useModel } from '@@/plugin-model/useModel';
import './index.less';
import { EmployeeItem } from '@/pages/configuration/employees/interfaces';
import { employeesDictColumns } from '@/pages/configuration/employees/columns';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import EmployeeItemAdd from './employee-add';
import _ from 'lodash';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';

const EmployeesConfiguration = () => {
  const ref = useRef<any>();
  const [form] = Form.useForm();

  const { globalState } = useModel('@@qiankunStateForSlave');

  const [employeeAddEdit, setEmployeeAddEdit] = useState(false);

  const [
    employeesDictionaryTableDataSource,
    setEmployeesDictionaryTableDataSource,
  ] = useState([]);

  const [employeesDictionaryColumns, setEmployeesDictionaryColumns] = useState(
    [],
  );
  const [frontPagination, setFrontPagination] = useState({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
  });
  // 前端分页OnChange
  const frontTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setFrontPagination({
      ...frontPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });
  };

  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  useEffect(() => {
    Emitter.onMultiple(
      [
        ConfigurationEvents.EMPLOYEE_SEX_SELECT,
        ConfigurationEvents.EMPLOYEE_JOB_CLASS_SELECT,
        ConfigurationEvents.EMPLOYEE_TITLE_SELECT,
        ConfigurationEvents.EMPLOYEE_STATUS_SELECT,
        ConfigurationEvents.EMPLOYEE_RELATED_DEPT_SELECT,
      ],
      (data) => {
        let currentFormValue = form.getFieldsValue()?.[data?.id];
        if (currentFormValue) {
          // set form value
          Object.keys(data?.values)?.forEach((key) => {
            currentFormValue[key] = data?.values?.[key];
          });
          form.setFieldValue(data?.id, currentFormValue);
        }
      },
    );

    return () => {
      Emitter.offMultiple([
        ConfigurationEvents.EMPLOYEE_SEX_SELECT,
        ConfigurationEvents.EMPLOYEE_JOB_CLASS_SELECT,
        ConfigurationEvents.EMPLOYEE_TITLE_SELECT,
        ConfigurationEvents.EMPLOYEE_STATUS_SELECT,
        ConfigurationEvents.EMPLOYEE_RELATED_DEPT_SELECT,
      ]);
    };
  }, [employeesDictionaryTableDataSource]);

  useEffect(() => {
    employeeDictionaryReq();
  }, []);

  useEffect(() => {
    if (!_.isEmpty(globalState?.dictData)) {
      employeeConfigurationColumnsReq();
    }
  }, [globalState?.dictData]);

  const onEmployeeSave = (rowKey, data) => {
    // table data
    let currentRowData = employeesDictionaryTableDataSource?.find(
      (item) => item?.EmployeeId === data?.EmployeeId,
    );
    if (currentRowData) {
      Object.keys(data)?.forEach((key) => {
        currentRowData[key] = data?.[key];
      });
    }

    setEmployeesDictionaryTableDataSource(
      employeesDictionaryTableDataSource?.slice(),
    );
  };

  const { loading: employeeDictionaryLoading, run: employeeDictionaryReq } =
    useRequest(
      () => {
        return uniCommonService('Api/Sys/HospHierarchySys/GetEmployees', {
          method: 'POST',
          data: {},
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<EmployeeItem[]>) => {
          if (response.code === 0 && response?.statusCode === 200) {
            let tableDataSource = response?.data?.slice();
            setEmployeesDictionaryTableDataSource(
              tableDataSource.map((record) => {
                record['id'] = uuidv4();

                return record;
              }),
            );
          } else {
            setEmployeesDictionaryTableDataSource([]);
          }
        },
      },
    );

  const { run: employeeConfigurationColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/HospHierarchySys/GetEmployees', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setEmployeesDictionaryColumns(
            tableColumnBaseProcessor(
              employeesDictColumns(globalState?.dictData),
              response?.data?.Columns,
            ),
          );
        } else {
          setEmployeesDictionaryColumns([]);
        }
      },
    },
  );

  const { loading: employeeUpsertLoading, run: employeeUpsertReq } = useRequest(
    (values) => {
      let data = {};

      data = {
        ...values,
      };

      return uniCommonService('Api/Sys/HospHierarchySys/UpsertEmployee', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.statusCode === 200 && employeeAddEdit) {
          setEmployeeAddEdit(false);
        }
        employeeDictionaryReq();
        return response;
      },
    },
  );

  const employeeItemAdd = (values: any) => {
    employeeUpsertReq(values);
  };

  const { run: deleteReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        EmployeeId: values.EmployeeId,
      };
      return uniCommonService(`Api/Sys/HospHierarchySys/DeleteEmployee`, {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
        }
      },
      onSuccess: (response, params) => {
        employeeDictionaryReq();
      },
    },
  );

  useEffect(() => {
    Emitter.on(ConfigurationEvents.EMPLOYEE_DELETE, (data) => {
      if (data?.index > -1) {
        deleteReq(data.record);
      }
    });

    return () => {
      Emitter.off(ConfigurationEvents.EMPLOYEE_DELETE);
    };
  }, [employeesDictionaryTableDataSource]);

  console.log('employeesDictionaryColumns', employeesDictionaryColumns);

  return (
    <div className={'employees-configuration-container'}>
      <Card
        title="人员列表"
        extra={
          <Space>
            <Button
              key="add"
              loading={false}
              onClick={(e) => {
                setEmployeeAddEdit(true);
                form.setFieldValue('IsValid', true);
              }}
            >
              新增人员
            </Button>
            <Divider type="vertical" />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: 'Api/Sys/HospHierarchySys/GetEmployees',
                onTableRowSaveSuccess: (newColumns) => {
                  setEmployeesDictionaryColumns(
                    tableColumnBaseProcessor(
                      employeesDictColumns(globalState?.dictData),
                      newColumns,
                    ),
                  );
                },
              }}
            />
          </Space>
        }
      >
        <UniEditableTable
          actionRef={ref}
          id={`employee-dictionary-table`}
          className={'employees-configuration-container'}
          rowKey={'id'}
          scroll={{
            y: 600,
          }}
          bordered={true}
          loading={employeeDictionaryLoading || employeeUpsertLoading}
          forceColumnsUpdate
          widthDetectAfterDictionary
          columns={employeesDictionaryColumns}
          value={employeesDictionaryTableDataSource}
          clickable={false}
          pagination={frontPagination}
          onTableChange={frontTableOnChange}
          recordCreatorProps={false}
          dictionaryData={globalState?.dictData}
          editable={{
            form: form,
            type: 'multiple',
            editableKeys: editableColumnKeys,
            onSave: async (rowKey, data, row) => {
              let employeeUpsertResponse = await employeeUpsertReq(data);
              if (
                employeeUpsertResponse?.code === 0 &&
                employeeUpsertResponse?.statusCode === 200
              ) {
                onEmployeeSave(rowKey, data);
              }
            },
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.save, defaultDoms.cancel];
            },
            onChange: setEditableColumnKeys,
          }}
        />
      </Card>

      <Modal
        title={'新增人员'}
        open={employeeAddEdit}
        onOk={async () => {
          let validateResponse = await form.validateFields();
          if (validateResponse?.errorFields?.length !== 0) {
            employeeItemAdd(form.getFieldsValue());
          }
        }}
        confirmLoading={employeeUpsertLoading}
        onCancel={() => {
          setEmployeeAddEdit(false);
        }}
        destroyOnClose={true}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <EmployeeItemAdd form={form} columns={employeesDictionaryColumns} />
      </Modal>
    </div>
  );
};

export default EmployeesConfiguration;
