import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import './index.less';
import { Drawer, Empty, Input, Tooltip } from 'antd';
import { createPortal } from 'react-dom';
import {
  addDmrHasPreCommentStyle,
  columnNameToLabel,
  containerKeyProcessor,
  deleteDmrHasPreCommentStyle,
  deleteDmrHasPreCommentStyleByFormKey,
  formKeyHasLeastOneCommentItem,
  formKeyHasNoCommentItem,
  isSpecialColumnNameKey,
  ruleCoderFinder,
  tableItemFeeItemSpecialKeyProcessor,
} from './utils';
import { DetailItem, RuleItem, TaskItem } from '../interfaces';
import { isEmptyValues } from '@uni/utils/src/utils';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import cloneDeep from 'lodash/cloneDeep';
import { useRequest } from 'umi';
import { QualityExamineStatus } from '../constant';
import { UniSelect } from '@uni/components/src';
import { useUpdateEffect } from 'ahooks';
import SvgLine from '@/pages/dmr/components/pre-examine/Connector';
import { GridStack } from '@uni/grid/src/core/gridstack';
import { orderBy } from 'lodash';
import { CloseOutlined } from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';

const { TextArea } = Input;

interface DmrCommentDrawerProps {
  containerRef: any;
  hisId?: string;

  dmrGridContainerRef: any;
  open: boolean;
}

interface DmrCommentItem {
  formKey?: string;
  containerKey?: string;
  label?: string;

  x?: number;
  y?: number;

  ruleItem?: any;
}

let commentGridInstance: GridStack = undefined;

const DmrRightCommentContainer = (props: DmrCommentDrawerProps) => {
  const [containerHeight, setContainerHeight] = useState(0);

  const [dmrCommentBaseData, setDmrCommentBaseData] = React.useState<any>({});

  const [dmrCommentItems, setDmrCommentItems] = React.useState<any>([]);

  const [ruleItems, setRuleItems] = React.useState<any[]>([]);

  const [taskId, setTaskId] = useState(undefined);

  const [taskItem, setTaskItem] = useState(undefined);

  const svgLinesContainerRef = useRef(null);

  const dmrContainerScrollTop = useRef(0);

  const dmrContainerRect = document
    ?.getElementById('dmr-content-container')
    ?.getBoundingClientRect();

  const commentContainerRect = document
    ?.getElementById('pre-comment-container')
    ?.getBoundingClientRect();

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      updatePolyLine: () => {
        svgLinesContainerRef?.current?.updatePolyLine();
      },
      getComments: () => {
        return dmrCommentItems;
      },
      addComment: (data: any) => {
        startTask();
        addCommentViaDmrAdd(data);
      },
      clearComment: () => {
        setDmrCommentItems([]);
      },
      // scrollAlongWithDmr: (closestElement) => {
      //   document
      //     ?.querySelector(`div[id^='Pre-${closestElement?.id}'`)
      //     ?.scrollIntoView({
      //       behavior: 'smooth',
      //       block: 'start',
      //       inline: 'nearest',
      //     });
      // },
      onContentScrollForLine: () => {
        if (props?.open === true) {
          svgLinesContainerRef?.current?.onContentScroll();
        }
      },
      onContentScrollForContent: (closestElement: any) => {
        if (props?.open === true) {
          scrollPreCommentWithDmrScroll(closestElement);
        }
      },
    };
  });

  useEffect(() => {
    if (!isEmptyValues(props?.hisId)) {
      initializeTask();
      initCommentGridInstance();
    }
  }, [props?.hisId]);

  // 打开状态变更的时候
  useUpdateEffect(() => {
    const dmrComments = props?.containerRef?.current?.getComments();
    if (props?.open === true) {
      // 当且仅当打开的时候 重新划线 添加 pre-comment highlight css
      svgLinesContainerRef?.current?.setPositions(
        dmrComments?.map((commentItem) => {
          return {
            start: tableItemFeeItemSpecialKeyProcessor(commentItem?.formKey),
            end: `Pre-${commentItem?.formKey}-${commentItem['UniqueId']}`,
          };
        }),
      );

      dmrComments?.forEach((commentItem) => {
        addDmrHasPreCommentStyle(commentItem);
      });

      setTimeout(() => {
        setContainerHeight(props?.dmrGridContainerRef?.current?.clientHeight);
      }, 0);
    }

    if (props?.open === false) {
      // 关闭划线 同时删除所有的 pre-comment highlight css
      deleteDmrHasPreCommentStyle();
      // 清除 没有 detailId 的comment
      setDmrCommentItems(
        dmrCommentItems?.filter((item) => !isEmptyValues(item?.['detailId'])),
      );
    }
  }, [props?.open]);

  useUpdateEffect(() => {
    svgLinesContainerRef?.current?.setPositions(
      dmrCommentItems?.map((commentItem) => {
        return {
          start: tableItemFeeItemSpecialKeyProcessor(commentItem?.formKey),
          end: `Pre-${commentItem?.formKey}-${commentItem?.UniqueId}`,
        };
      }),
    );
  }, [dmrCommentItems]);

  const scrollPreCommentWithDmrScroll = (closestElement: any) => {
    const dmrScrollTop = document.getElementById(
      'dmr-content-container',
    ).scrollTop;

    let commentScrollTop = document.getElementById(
      'pre-comment-container',
    ).scrollTop;

    // 要判定是怎么滚动 上还是下....
    let scrollUp = dmrScrollTop > dmrContainerScrollTop?.current;
    dmrContainerScrollTop.current = dmrScrollTop;

    if (dmrScrollTop === 0) {
      document.getElementById('pre-comment-container').scrollTo({
        top: 0,
        behavior: 'smooth',
      });
      return;
    }

    let yAxis = closestElement.gridstackNode.y;
    let scrollDistance = undefined;
    let minCardYInCommentItem = null;
    if (!isEmptyValues(yAxis)) {
      if (scrollUp) {
        for (const dmrCommentItem of dmrCommentItems?.sort(
          (a, b) => a?.cardY - b?.cardY,
        )) {
          if (dmrCommentItem?.['cardY'] > yAxis) {
            break;
          }
          minCardYInCommentItem = dmrCommentItem;
        }
      } else {
        for (const dmrCommentItem of dmrCommentItems?.sort(
          (a, b) => b?.cardY - a?.cardY,
        )) {
          minCardYInCommentItem = dmrCommentItem;
          if (dmrCommentItem?.['cardY'] <= yAxis) {
            break;
          }
        }
      }

      if (!isEmptyValues(minCardYInCommentItem)) {
        let dmrElement = document.querySelector(
          `#dmr-form-container #dmr-content-container #${
            minCardYInCommentItem?.containerKey ??
            minCardYInCommentItem?.formKey
          }`,
        );

        // 看看 这个element是不是在 viewport里面
        let dmrElementInViewport =
          dmrElement?.getBoundingClientRect()?.bottom >= dmrContainerRect?.top;
        if (dmrElementInViewport === true) {
          scrollDistance = document.getElementById(
            `Pre-${minCardYInCommentItem?.formKey}-${minCardYInCommentItem['UniqueId']}`,
          )?.offsetTop;
        } else {
          // 不在viewport里面的话 用这个item 的底 把这个item以上的都滚上去
          scrollDistance =
            document.getElementById(
              `Pre-${minCardYInCommentItem?.formKey}-${minCardYInCommentItem['UniqueId']}`,
            )?.offsetTop +
            document.getElementById(
              `Pre-${minCardYInCommentItem?.formKey}-${minCardYInCommentItem['UniqueId']}`,
            )?.offsetHeight;
        }
      }

      if (scrollUp) {
        if (commentScrollTop < scrollDistance) {
          document.getElementById('pre-comment-container').scrollTo({
            top: scrollDistance,
            behavior: 'smooth',
          });
        } else if (commentScrollTop < dmrScrollTop) {
          // 滚到 dmrScrollTop 上
          document.getElementById('pre-comment-container').scrollTo({
            top: dmrScrollTop,
            behavior: 'smooth',
          });
        }
      } else {
        document.getElementById('pre-comment-container').scrollTo({
          top: Math.max(dmrScrollTop, scrollDistance),
          behavior: 'smooth',
        });
      }
    }

    /*const commentScrollTop = document.getElementById(
      'pre-comment-container',
    ).scrollTop;
    const dmrScrollTop = document.getElementById(
      'dmr-content-container',
    ).scrollTop;

    let lastInvisibleCommentIndexInComment = null;
    let firstVisibleCommentIndexInComment = null;

    // 要判定是怎么滚动 上还是下....
    let scrollUp = dmrScrollTop > dmrContainerScrollTop?.current;
    dmrContainerScrollTop.current = dmrScrollTop;

    const orderedComments = (
      scrollUp
        ? orderBy(dmrCommentItems, [ 'y', 'x'], ['desc', 'asc'])
        : orderBy(dmrCommentItems, [ 'y', 'x'], ['asc', 'desc'])
    ).filter((item) => {
      return item?.position !== 'HEADER';
    });

    let scrollDistance = dmrScrollTop;

    for (let index = 0; index < orderedComments.length; index++) {
      const commentItem = orderedComments?.at(index);
      const currentCommentItemRect = document
        ?.querySelector(`div[id^='RULE-SCORE-ITEM-${commentItem?.formKey}'`)
        ?.getBoundingClientRect();

      commentItem['top'] = currentCommentItemRect?.top;
      commentItem['bottom'] = currentCommentItemRect?.bottom;

      if (commentItem?.formKey) {
        let dmrElement = document.querySelector(
          `#dmr-form-container #dmr-content-container #${
            commentItem?.containerKey ?? commentItem?.formKey
          }`,
        );
        let commentElement = document?.querySelector(
          `div[id^='Pre-${commentItem?.formKey}'`,
        ) as any;

        if (dmrElement !== null && commentElement !== null) {
          let dmrElementInViewport =
            dmrElement?.getBoundingClientRect()?.top >=
            dmrContainerRect?.top;
          let commentElementInViewport =
            commentElement?.getBoundingClientRect()?.top >=
            commentContainerRect?.top;

          // 表示 comment 应该滚上去
          if (
            dmrElementInViewport === false &&
            commentElementInViewport === true
          ) {
            scrollDistance = Math.max(
              scrollDistance,
              commentElement?.offsetTop + commentElement?.offsetHeight,
            );
          }

          // 表示 comment 应该滚下来
          if (
            dmrElementInViewport === true &&
            commentElementInViewport === false
          ) {
            scrollDistance = Math.min(
              scrollDistance,
              commentElement?.offsetTop - 100,
            );
          }
        }
      }
    }

    if (scrollUp) {
      // 滚轮往下拉
      if (commentScrollTop >= scrollDistance) {
        return;
      }
      // 判定是不是应该显示
    } else {
      // 滚轮往上推
      if (commentScrollTop < scrollDistance) {
        return;
      }
    }

    setTimeout(() => {
      document.getElementById('pre-comment-container').scrollTo({
        top: scrollDistance,
        behavior: 'smooth',
      });
    }, 0);*/
  };

  const initCommentGridInstance = () => {
    commentGridInstance = GridStack.init(
      {
        float: true,
        cellHeight: 42,
        column: 1,
        sizeToContent: true,
        disableResize: true,
        disableDrag: true,
        marginTop: 0,
        marginLeft: 0,
        marginRight: 0,
        marginBottom: 0,
        staticGrid: true,
      },
      document.getElementById('pre-comment-relative-container'),
    );

    commentGridInstance?.on('loaded', (name, callback) => {
      setTimeout(() => {
        console.log('resizeToContent 重算位置 ING');
      }, 500);
    });
  };

  useLayoutEffect(() => {
    if (commentGridInstance !== undefined) {
      commentGridInstance?.batchUpdate();
      commentGridInstance?.removeAll(false);
      let stackLayouts = [];
      dmrCommentItems?.forEach((commentItem) => {
        commentGridInstance?.makeWidget(
          `#Pre-${commentItem?.formKey}-${commentItem['UniqueId']}`,
        );
        stackLayouts.push({
          id: `Pre-${commentItem?.formKey}-${commentItem['UniqueId']}`,
          x: 0,
          y: commentItem?.y ?? 0,
          w: 1,
        });
      });
      commentGridInstance?.load(stackLayouts);
      commentGridInstance?.batchUpdate(false);
    }
  }, [commentGridInstance, dmrCommentItems]);

  const initializeTask = async () => {
    // hisId 换 taskId
    const taskItem: TaskItem = await getDmrCommentTaskReq(props?.hisId);
    await commentRuleCodesReq(taskItem?.TemplateId);
    requestAnimationFrame(() => {
      dmrCommentTaskDetailsReq(taskItem?.TaskId);
    });
  };

  const startTask = async () => {
    if (!isEmptyValues(taskItem)) {
      // 换details 这里换来的details 大概率 是 rule
      // 没有Rule那就 + 几个就行
      if (taskItem?.Status === QualityExamineStatus.Pending) {
        let startReviewResponse = await uniCommonService(
          'Api/Dmr/DmrPreCardQualityExamine/StartReview',
          {
            method: 'POST',
            data: {
              TaskId: taskItem?.TaskId,
            },
          },
        );

        if (
          startReviewResponse?.code === 0 &&
          startReviewResponse?.statusCode === 200
        ) {
          taskItem['Status'] = QualityExamineStatus.Reviewing;
        }
      }
    }
  };

  const { loading: getDmrCommentTaskLoading, run: getDmrCommentTaskReq } =
    useRequest(
      (hisId: string) => {
        let data = {
          hisId: hisId,
        };

        return uniCommonService('Api/Dmr/DmrPreCardQualityExamine/GetTask', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<TaskItem>) => {
          setTaskId(response?.data?.TaskId);
          setTaskItem(response?.data);
          return response?.data;
        },
      },
    );

  const {
    loading: dmrCommentTaskDetailsLoading,
    run: dmrCommentTaskDetailsReq,
  } = useRequest(
    (taskId) => {
      let data = {
        taskId: taskId,
      };

      return uniCommonService('Api/Dmr/DmrPreCardQualityExamine/GetDetails', {
        method: 'POST',
        requestType: 'json',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<DetailItem[]>) => {
        console.log('response', response);

        let newComments = [];
        response?.data?.forEach((detailItem) => {
          let commentItem: any = {};
          Object.assign(commentItem, detailItem);
          commentItem['formKey'] = detailItem?.ColumnName;
          commentItem['containerKey'] = detailItem?.ColumnName;
          commentItem['detailId'] = detailItem?.Id;
          commentItem['originDetailItem'] = cloneDeep(detailItem);
          commentItem['label'] = columnNameToLabel(detailItem?.ColumnName);

          commentItem['UniqueId'] = uuidv4()?.replace('-', '');

          const ruleCode =
            commentItem?.['RuleCode'] ??
            ruleCoderFinder(commentItem?.formKey, ruleItems);
          let ruleItem = ruleItems?.find(
            (ruleItem) => ruleItem?.RuleCode === ruleCode,
          );
          commentItem['ruleItem'] = ruleItem;
          newComments.push(commentItem);
        });

        commentItemOtherFieldsProcessor(newComments);

        setDmrCommentItems(newComments);
      },
    },
  );

  const { loading: commentRuleCodesLoading, run: commentRuleCodesReq } =
    useRequest(
      (templateId) => {
        let data = {
          templateId: templateId,
        };

        return uniCommonService(
          'Api/Dmr/DmrCardQualityExamine/GetRuleWithScores',
          {
            method: 'POST',
            requestType: 'json',
            data: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<RuleItem[]>) => {
          setRuleItems(response?.data);
        },
      },
    );

  const commentItemOtherFieldsProcessor = (comments: any[]) => {
    let instances = props?.dmrGridContainerRef?.current?.getGridStackInstance();

    let contentItems = [];
    let headerItems = [];
    if (!isEmptyValues(instances)) {
      contentItems = instances?.contentGridInstance?.getGridItems();
      headerItems = instances?.headerGridInstance?.getGridItems();
    }

    comments?.forEach((commentItem) => {
      // containerKey 换算
      if (isSpecialColumnNameKey(commentItem?.formKey)) {
        // 特殊处理一下
        commentItem['containerKey'] = containerKeyProcessor(
          commentItem?.formKey,
        )?.containerKey;
      }

      let currentMainDmrItem = contentItems?.find(
        (contentItem) =>
          contentItem?.gridstackNode?.id ===
          (commentItem?.containerKey ?? commentItem?.formKey),
      );

      let currentDmrHeaderItem = headerItems?.find(
        (contentItem) =>
          contentItem?.gridstackNode?.id ===
          (commentItem?.containerKey ?? commentItem?.formKey),
      );

      if (currentMainDmrItem) {
        commentItem['position'] = 'CONTENT';
        commentItem['x'] = currentMainDmrItem?.gridstackNode?.x ?? 0;
        commentItem['cardY'] = currentMainDmrItem?.gridstackNode?.y ?? 0;
        commentItem['y'] = currentMainDmrItem?.gridstackNode?.y ?? 0;
      }

      if (currentDmrHeaderItem) {
        commentItem['position'] = 'HEADER';
        commentItem['x'] = currentDmrHeaderItem?.gridstackNode?.x ?? 0;
        commentItem['cardY'] = currentDmrHeaderItem?.gridstackNode?.y ?? 0;
        commentItem['y'] = currentDmrHeaderItem?.gridstackNode?.y ?? 0;
      }

      // 添加 样式
      if (props?.open === true) {
        addDmrHasPreCommentStyle(commentItem);
      }
    });

    return comments;
  };

  const addCommentViaDmrAdd = (data: any) => {
    let commentItem = {};
    Object.assign(commentItem, data);

    const ruleCode = ruleCoderFinder(data?.formKey, ruleItems);
    let ruleItem = ruleItems?.find(
      (ruleItem) => ruleItem?.RuleCode === ruleCode,
    );

    // if(isEmptyValues(ruleItem)) {
    //   message.error("未找到规则配置，请检查规则")
    //   return;
    // }

    const columnName = tableItemFeeItemSpecialKeyProcessor(data?.formKey);

    commentItem['formKey'] = columnName;
    commentItem['ColumnName'] = columnName;
    commentItem['containerKey'] = columnName;
    commentItem['ruleItem'] = ruleItem;
    commentItem['label'] = columnNameToLabel(data?.formKey);

    commentItem['UniqueId'] = uuidv4()?.replace('-', '');

    let newAddComments = commentItemOtherFieldsProcessor([commentItem]);

    setDmrCommentItems([...dmrCommentItems, ...newAddComments]);

    setTimeout(() => {
      // 调用滚动到相对位置
      let scrollDistance = document.getElementById(
        `Pre-${columnName}-${commentItem['UniqueId']}`,
      )?.offsetTop;
      document.getElementById('pre-comment-container').scrollTo({
        top: scrollDistance,
        behavior: 'smooth',
      });
      // 添加 线
      svgLinesContainerRef?.current?.updatePolyLine();
    }, 200);
  };

  // TODO 根据当前item的Y 来排序 然后估计要同步滚动  数据先不急
  // TODO 明天先上了 新增 删除 修改 等 逻辑
  // TODO 保证 除了接口之外的可用

  const dataRef = React.useRef({});

  const onCommentItemUpdate = (
    formKey: string,
    detailId: number,
    uniqueId: string,
    fieldName: string,
    content: string,
  ) => {
    let data = {
      detailId: detailId,
    };
    data[fieldName] = content;
    // updateCommentItemPropsViaFormKey(formKey, data);
    updateCommentItemPropsViaUniqueId(uniqueId, data);

    svgLinesContainerRef?.current?.updatePolyLine();
  };

  const onCommentItemRuleSelect = (
    commentItem: any,
    ruleCode: string,
    detailId: any,
  ) => {
    let ruleItem = ruleItems?.find((item) => item?.RuleCode === ruleCode);
    commentItem['ruleItem'] = ruleItem;
    if (!isEmptyValues(detailId)) {
      commentItem['detailId'] = detailId;
    }
    setDmrCommentItems(dmrCommentItems?.slice());
  };

  const onCommentItemDelete = async (commentItem: any, index: number) => {
    let newDmrComments = cloneDeep(dmrCommentItems);
    newDmrComments.splice(index, 1);

    setDmrCommentItems(newDmrComments);

    if (formKeyHasNoCommentItem(newDmrComments, commentItem?.formKey)) {
      deleteDmrHasPreCommentStyleByFormKey(commentItem?.formKey);
    }
    // 删除线 & 删除 highlight
    setTimeout(() => {
      svgLinesContainerRef?.current?.updatePolyLine();
    }, 200);

    if (!isEmptyValues(commentItem?.['detailId'])) {
      // 调用接口删除
      let data = {
        taskId: taskId,
        detailId: commentItem?.detailId,
      };
      await uniCommonService('Api/Dmr/DmrPreCardQualityExamine/DeleteDetail', {
        method: 'POST',
        data: data,
      });
    }
  };

  const updateCommentItemPropsViaFormKey = (formKey: string, props: any) => {
    let newComments = cloneDeep(dmrCommentItems);
    let currentComment = newComments?.find((item) => item?.formKey === formKey);
    if (currentComment !== null && currentComment !== undefined) {
      Object.keys(props)?.forEach((key) => {
        currentComment[key] = props?.[key];
      });

      setDmrCommentItems(newComments);
    }
  };

  const updateCommentItemPropsViaUniqueId = (uniqueId: string, props: any) => {
    let newComments = cloneDeep(dmrCommentItems);
    let currentComment = newComments?.find(
      (item) => item?.UniqueId === uniqueId,
    );
    if (currentComment !== null && currentComment !== undefined) {
      Object.keys(props)?.forEach((key) => {
        currentComment[key] = props?.[key];
      });

      setDmrCommentItems(newComments);
    }
  };

  const onMouseEnterCommentItem = (
    event: any,
    key: string,
    uniqueId: string,
  ) => {
    svgLinesContainerRef?.current?.setActiveEndId(`Pre-${key}-${uniqueId}`);
  };

  const onMouseLeaveCommentItem = (
    event: any,
    key: string,
    uniqueId: string,
  ) => {
    svgLinesContainerRef?.current?.setActiveEndId(undefined);
  };

  return (
    <div
      className={'dmr-comment-drawer-container'}
      style={
        props?.open === true
          ? { visibility: 'visible' }
          : { visibility: 'hidden' }
      }
    >
      {createPortal(
        props?.open ? (
          <SvgLine
            detailCommentRef={props?.containerRef}
            containerRef={svgLinesContainerRef}
          />
        ) : null,
        document.body,
        'svg-line-container',
      )}

      <div className={'dmr-comment-drawer-title'}>首页批注</div>
      <div
        id={'pre-comment-container'}
        className={'comment-item-list-container'}
        style={{ '--gridMinHeight': `${containerHeight ?? 0}px` } as any}
      >
        <div
          id={'pre-comment-relative-container'}
          className={'pre-comment-relative-container'}
        >
          {isEmptyValues(dmrCommentItems) && (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}

          {dmrCommentItems.map((commentItem: any, index: number) => {
            return (
              <div
                id={`Pre-${commentItem?.formKey}-${commentItem?.['UniqueId']}`}
                gs-id={`Pre-${commentItem?.formKey}-${commentItem?.['UniqueId']}`}
                gs-w={1}
                className={'grid-stack-item'}
              >
                <div
                  className={'grid-stack-item-content'}
                  onMouseEnter={(event) => {
                    onMouseEnterCommentItem(
                      event,
                      commentItem?.formKey,
                      commentItem?.UniqueId,
                    );
                  }}
                  onMouseLeave={(event) => {
                    onMouseLeaveCommentItem(
                      event,
                      commentItem?.formKey,
                      commentItem?.UniqueId,
                    );
                  }}
                >
                  <CommentItem
                    index={index}
                    taskId={taskId}
                    ruleCode={commentItem?.ruleItem?.RuleCode}
                    dataRef={dataRef}
                    commentItem={commentItem}
                    formKey={commentItem?.formKey}
                    onCommentItemUpdate={onCommentItemUpdate}
                    onCommentItemRuleSelect={onCommentItemRuleSelect}
                    ruleItems={ruleItems}
                    onCommentItemDelete={onCommentItemDelete}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

interface CommentItemProps {
  taskId?: number;
  ruleCode?: string;
  commentItem?: any;
  dataRef: any;
  formKey?: string;
  index?: number;

  ruleItems?: RuleItem[];

  onCommentItemUpdate?: (
    formKey: string,
    detailId: number,
    uniqueId: string,
    fieldName: string,
    content: string,
  ) => void;

  onCommentItemRuleSelect?: (commentItem: any, ruleCode: string) => void;

  onCommentItemDelete?: (commentItem: any, index: number) => void;
}

const CommentItem = (props: CommentItemProps) => {
  const [commentRemarkValue, setCommentRemarkValue] = useState(undefined);

  useUpdateEffect(() => {
    setCommentRemarkValue(props?.commentItem?.Remark);
  }, [props?.commentItem]);

  const upsertDetailComment = async (content: string) => {
    let data = {
      taskId: props?.taskId,
      ruleCode: props?.ruleCode ?? 'Pre-001',
      remark: content,
      // inputScore: scoreValue, 暂时没有 score
      ColumnName: props?.commentItem?.ColumnName,
    };
    if (!isEmptyValues(props?.commentItem?.detailId)) {
      data['detailId'] = props?.commentItem?.detailId;
    }
    let upsertCategoryDetailResponse: RespVO<number> = await uniCommonService(
      'Api/Dmr/DmrPreCardQualityExamine/UpsertRuleDetail',
      {
        method: 'POST',
        data: data,
      },
    );

    if (
      upsertCategoryDetailResponse?.code === 0 &&
      upsertCategoryDetailResponse?.statusCode === 200
    ) {
      props?.onCommentItemUpdate &&
        props?.onCommentItemUpdate(
          props?.formKey,
          upsertCategoryDetailResponse?.data,
          props?.commentItem?.UniqueId,
          'Remark',
          content,
        );
    }
  };

  const upsertRuleCode = async (ruleCode: string) => {
    let data = {
      taskId: props?.taskId,
      ruleCode: ruleCode ?? 'Pre-001',
      remark: commentRemarkValue ?? props?.commentItem?.Remark,
      // inputScore: scoreValue, 暂时没有 score
      ColumnName: props?.commentItem?.ColumnName,
    };
    if (!isEmptyValues(props?.commentItem?.detailId)) {
      data['detailId'] = props?.commentItem?.detailId;
    }
    let upsertRuleCodeResponse: RespVO<number> = await uniCommonService(
      'Api/Dmr/DmrPreCardQualityExamine/UpsertRuleDetail',
      {
        method: 'POST',
        data: data,
      },
    );

    if (
      upsertRuleCodeResponse?.code === 0 &&
      upsertRuleCodeResponse?.statusCode === 200
    ) {
      props?.onCommentItemRuleSelect &&
        props?.onCommentItemRuleSelect(
          props?.commentItem,
          ruleCode,
          upsertRuleCodeResponse?.data,
        );
    }
  };

  return (
    <div className={'dmr-comment-item-container'}>
      <div className={'delete-icon'}>
        <Tooltip title={'删除批注'}>
          <CloseOutlined
            style={{ padding: '2px 5px 2px 10px', cursor: 'pointer' }}
            onClick={() => {
              props?.onCommentItemDelete?.(props?.commentItem, props?.index);
            }}
          />
        </Tooltip>
      </div>
      <div className={'comment-item'}>
        <span className={'comment-item-label'}>关联项：</span>
        <div className={'comment-item-content'}>
          <span>{props?.commentItem?.label?.replace('*', '')}</span>
        </div>
      </div>

      <div className={'comment-item'}>
        <span className={'comment-item-label'}>规则名称：</span>
        <div className={'comment-item-content'}>
          <UniSelect
            placeholder={'请选择规则'}
            dataSource={props?.ruleItems}
            optionValueKey={'RuleCode'}
            showSearch={true}
            allowClear={false}
            disabled={false}
            value={props?.commentItem?.ruleItem?.['RuleCode']}
            optionNameKey={'DisplayErrMsg'}
            onSelect={(value, record) => {
              // 更新 comment ruleItem
              upsertRuleCode(value);
            }}
          />
        </div>
      </div>

      <div className={'comment-item'}>
        <span className={'comment-item-label'}>评审意见：</span>
        <div className={'comment-item-content'}>
          <TextArea
            showCount={false}
            autoSize={true}
            maxLength={-1}
            value={commentRemarkValue ?? props?.commentItem?.Remark}
            style={{ minHeight: 60, resize: 'none' }}
            disabled={false}
            placeholder="请输入评审意见"
            onChange={(event) => {
              setCommentRemarkValue(event?.target?.value);
            }}
            onBlur={(event) => {
              // TODO 添加批注
              upsertDetailComment(event?.target?.value);
            }}
          />
        </div>
      </div>
    </div>
  );
};

interface DmrCommentDrawerTriggerProps {
  drawerContainerRef: any;
}

export const DmrCommentDrawerTrigger = (
  props: DmrCommentDrawerTriggerProps,
) => {
  return <div className={'dmr-comment-drawer-trigger'}></div>;
};

export default DmrRightCommentContainer;
