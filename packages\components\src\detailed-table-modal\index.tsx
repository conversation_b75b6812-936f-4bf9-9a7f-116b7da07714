import { Divider, Modal, Space, TableProps, Tooltip } from 'antd';
import ExportIconBtn from '../backend-export';
import { useRequest } from 'ahooks';
import { uniCommonService } from '@uni/services/src';
import { ReactNode, useEffect, useState } from 'react';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import IconBtn from '../iconBtn';
import UniTable from '../table';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { CloseOutlined, RedoOutlined } from '@ant-design/icons';
import { TableColumnEditButton } from '../table/column-edit';
import { addExportToLastSegment } from '@uni/utils/src/widgets';

export interface IUsedProps {
  title?: string | ReactNode;
  args?: {};
  detailsUrl?: string;
  type?: string;
  detailType?: string;
  dictData?: any;
}

const canRefresh =
  (window as any).externalConfig?.['his']?.devtool?.refresh ?? false;
const canEditColumn =
  (window as any).externalConfig?.['his']?.devtool?.editColumn ?? false;

export interface IDetailTableModalProps {
  // id: string;
  dictData?: any;
  propsColumns?: any[];
  detailAction?: (recrod: any) => void;
  // 用于operation 跳转的title 定制
  operationTitle?: string;
  noDetailBtn?: boolean; // 是否需要跳转详情Icon 默认需要 true不需要
}
/**
 * 一个基于modal + table + btnJumpto 的封装
 * @param props
 * @returns
 */
const DetailTableModal = ({
  // id,
  dictData,
  propsColumns,
  detailAction,
  operationTitle,
  noDetailBtn,
}: IDetailTableModalProps) => {
  const [visible, setVisible] = useState(false);
  const [columns, setColumns] = useState([]);
  const [dataSource, setDataSource] = useState([]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  const [props, setProps] = useState<IUsedProps>(undefined);

  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorter,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    detailsReq({
      url: props.detailsUrl,
      args: {
        current: pagi?.current,
        pageSize: pagi?.pageSize,
      },
    });
  };

  const {
    data,
    loading,
    run: detailsReq,
  } = useRequest(
    (params) => {
      console.log(params);
      return uniCommonService(`Api/${params.url}`, {
        method: 'POST',
        data: {
          ...props.args,
          DtParam: {
            Draw: 1,
            Start: (params.args.current - 1) * params.args.pageSize,
            Length: params.args.pageSize,
            // 都走columns def 初始默认也是
            // columns: [
            //   {
            //     Data: 'OutDate',
            //   },
            //   {
            //     Data: 'ChsDrgCode',
            //   },
            // ],
            // order: [
            //   {
            //     columns: '1',
            //     dir: 'desc',
            //   },
            //   {
            //     columns: '0',
            //     dir: 'desc',
            //   },
            // ],
          },
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      onSuccess(response: RespVO<any>, params) {
        if (response?.code === 0 && response?.statusCode === 200) {
          setDataSource(response?.data?.data);
          setBackPagination({
            ...backPagination,
            total: response?.data?.recordsTotal || 0,
          });
        } else {
          setDataSource([]);
        }
      },
    },
  );

  const {
    data: Columns,
    loading: columnsLoading,
    run: columnsReq,
  } = useRequest(
    (params) => {
      return uniCommonService(`Api/${params.url}`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      onSuccess(response: RespVO<TableColumns>, params) {
        if (response?.code === 0 && response?.statusCode === 200) {
          let data = response?.data?.Columns;
          setColumns(
            propsColumns
              ? [
                  {
                    dataIndex: 'operation',
                    visible: !noDetailBtn ? true : false,
                    width: 40,
                    align: 'center',
                    title: '',
                    fixed: 'left',
                    render: (node, record, index) => {
                      return (
                        <IconBtn
                          title={operationTitle ?? `病例结算详情`}
                          type="details2"
                          className="operation-btn"
                          onClick={(e) => {
                            if (detailAction) {
                              detailAction(record);
                            } else {
                              window.open(
                                `/chs/analysis/cardInfo?hisId=${encodeURIComponent(
                                  record.HisId,
                                )}&type=${props.type}`,
                              );
                            }
                          }}
                        />
                      );
                    },
                  },
                  ...propsColumns,
                ]
              : tableColumnBaseProcessor(
                  [
                    {
                      dataIndex: 'operation',
                      visible: !noDetailBtn ? true : false,
                      width: 40,
                      align: 'center',
                      title: '',
                      fixed: 'left',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            title={operationTitle ?? `病例结算详情`}
                            type="details2"
                            className="operation-btn"
                            onClick={(e) => {
                              if (detailAction) {
                                detailAction(record);
                              } else {
                                window.open(
                                  `/chs/analysis/cardInfo?hisId=${encodeURIComponent(
                                    record.HisId,
                                  )}&type=${props.type}`,
                                );
                              }
                            }}
                          />
                        );
                      },
                    },
                  ],
                  data,
                ),
          );
        } else {
          setColumns(
            propsColumns
              ? [
                  !noDetailBtn && {
                    dataIndex: 'operation',
                    visible: !noDetailBtn ? true : false,
                    width: 40,
                    align: 'center',
                    title: '',
                    fixed: 'left',
                    render: (node, record, index) => {
                      return (
                        <IconBtn
                          title={operationTitle ?? `病例结算详情`}
                          type="details2"
                          className="operation-btn"
                          onClick={(e) => {
                            if (detailAction) {
                              detailAction(record);
                            } else {
                              window.open(
                                `/chs/analysis/cardInfo?hisId=${encodeURIComponent(
                                  record.HisId,
                                )}&type=${props.type}`,
                              );
                            }
                          }}
                        />
                      );
                    },
                  },
                  ...propsColumns,
                ]
              : [],
          );
        }
      },
    },
  );

  // props 处理 通过emitter
  useEffect(() => {
    Emitter.on(
      EventConstant.DETAILS_BTN_CLICK,
      (payload: { id: any } & IUsedProps) => {
        setProps({ dictData, ...payload });
      },
      true,
      true,
    );

    return () => {
      Emitter.off(EventConstant.DETAILS_BTN_CLICK);
    };
  }, []);

  useEffect(() => {
    // 第一次初始化 默认排序
    if (props?.detailsUrl) {
      setVisible(true);
      columnsReq({ url: props.detailsUrl });
      detailsReq({
        url: props.detailsUrl,
        args: { current: 1, pageSize: backPagination?.pageSize },
      });
    }
  }, [props, backPagination?.pageSize]);

  console.log('columns', columns);

  return (
    <Modal
      className="detail-modal"
      title={
        <div className="detail-modal-header d-flex">
          <h3 style={{ marginBottom: 0 }}>{`${props?.title} 病案明细`}</h3>
          <Space.Compact className="btn_space">
            {/* <div className='btn_space'> */}
            {canRefresh && (
              <Tooltip title="刷新">
                <RedoOutlined
                  style={{ width: '32px', height: '32px', lineHeight: '35px' }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setBackPagination({
                      ...backPagination,
                      current: 1,
                    });
                    // 初始化 默认排序
                    detailsReq({
                      url: props.detailsUrl,
                      args: { current: 1, pageSize: backPagination?.pageSize },
                    });
                  }}
                />
              </Tooltip>
            )}
            {/* 默认分页 TODO 数据传入 */}
            {canEditColumn && (
              <TableColumnEditButton
                {...{
                  columnInterfaceUrl: `Api/${props?.detailsUrl}`,
                  onTableRowSaveSuccess: (columns) => {
                    setColumns(
                      propsColumns
                        ? [
                            {
                              dataIndex: 'operation',
                              visible: !noDetailBtn ? true : false,
                              width: 40,
                              align: 'center',
                              title: '',
                              fixed: 'left',
                              render: (node, record, index) => {
                                return (
                                  <IconBtn
                                    title="病例结算详情"
                                    type="details2"
                                    className="operation-btn"
                                    onClick={(e) => {
                                      if (detailAction) {
                                        detailAction(record);
                                      } else {
                                        window.open(
                                          `/chs/analysis/cardInfo?hisId=${encodeURIComponent(
                                            record.HisId,
                                          )}&type=${props.type}`,
                                        );
                                      }
                                    }}
                                  />
                                );
                              },
                            },
                            ...propsColumns,
                          ]
                        : tableColumnBaseProcessor(
                            [
                              {
                                dataIndex: 'operation',
                                visible: !noDetailBtn ? true : false,
                                width: 40,
                                align: 'center',
                                title: '',
                                fixed: 'left',
                                render: (node, record, index) => {
                                  return (
                                    <IconBtn
                                      title="查看结算明细"
                                      type="details2"
                                      className="operation-btn"
                                      onClick={(e) => {
                                        window.open(
                                          `/chs/analysis/cardInfo?hisId=${encodeURIComponent(
                                            record.HisId,
                                          )}&type=${props.type}`,
                                        );
                                      }}
                                    />
                                  );
                                },
                              },
                            ],
                            columns,
                          ),
                    );
                  },
                }}
              />
            )}
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: addExportToLastSegment(`Api/${props?.detailsUrl}`),
                method: 'POST',
                data: props?.args,
                fileName: `${props?.title} 病案明细`,
              }}
              btnDisabled={
                dataSource?.length < 1 ||
                !dataSource ||
                (dataSource?.length === 1 && !dataSource?.at(0))
              }
            />
            <Divider type="vertical" />
            <CloseOutlined
              className="detail-modal-close-x"
              onClick={(e) => {
                e.stopPropagation();
                setVisible(false);
                setProps(undefined);
              }}
            />
          </Space.Compact>
        </div>
      }
      open={visible}
      closable={false}
      width={'80%'}
      footer={null}
      afterClose={() => {
        setBackPagination({
          ...backPagination,
          current: 1,
          total: 0,
          pageSize: 10,
        });
      }}
      onCancel={(event) => {
        event.stopPropagation();
        setVisible(false);
        setProps(undefined);
      }}
      destroyOnClose={true}
      zIndex={60}
    >
      <UniTable
        id={'detail-table'}
        rowKey={'CardId'}
        dictionaryData={dictData}
        widthCalculate
        widthDetectAfterDictionary
        scroll={{ x: 'max-content', y: 530 }}
        loading={loading || columnsLoading}
        columns={columns}
        dataSource={dataSource}
        pagination={backPagination}
        onChange={backTableOnChange}
        toolBarRender={null}
        isBackPagination
      />
    </Modal>
  );
};

export default DetailTableModal;
