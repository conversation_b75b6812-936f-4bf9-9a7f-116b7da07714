import { title } from 'process';
import { IEditableState, ITableState } from './interface';
import update from 'immutability-helper';

export enum TableAction {
  init = 'INIT',
  columnsChange = 'COLUMNS_CHANGE',
  dataPush = 'DATA_PUSH',
  dataUnshift = 'DATA_UNSHIFT',
  dataUnshiftUniq = 'DATAUNSHIFTUNIQ',
  dataUnshiftUniqSimple = 'DATAUNSHIFTUNIQSIMPLE', // 新增：简化版的dataUnshiftUniq，只接受key和data参数
  dataFilt = 'DATA_FILT',
  dataChange = 'DATA_CHANGE',
  multiDataChange = 'MULTI_DATA_CHANGE',
  singleDataChange = 'SINGLE_DATA_CHANGE',
  selectionChange = 'SELECTION_CHANGE',
  sortChange = 'SORT_CHANGE',
  expandChange = 'EXPAND_CHANGE',
  clkChange = 'CLKITEM_CHANGE',
  summaryChange = 'SUMMARY_CHANGE',
  backPaginationChange = 'BACK_PAGINATION_CHANGE',
  dataPagiChange = 'DATA_PAGI_CHANGE',
  titleChange = 'TITLE_CHANGE',
  anyChange = 'ANY_CHANGE', // 慎用 建议只用于一些无伤大雅的数据修改，title，clkItem这些
  // editable
  editableValuesChange = 'EDITABLE_VALUES_CHANGE',
  editableKeysChange = 'EDITABLE_KEYS_CHANGE',
  editableSingleRecordChange = 'EDITABLE_SINGLE_RECORD_CHANGE',
  editableSingleValueChange = 'EDITABLE_SINGLE_VALUE_CHANGE',
}

export enum EditableTableAction {
  editableValuesChange = 'EDITABLE_VALUES_CHANGE',
  editableKeysChange = 'EDITABLE_KEYS_CHANGE',
  editableSingleRecordChange = 'EDITABLE_SINGLE_RECORD_CHANGE',
  editableSingleValueChange = 'EDITABLE_SINGLE_VALUE_CHANGE',
  editableSingleValueUpsert = 'EDITABLE_SINGLE_VALUE_UPSERT',
  editableValueAdd = 'EDITABLE_VALUE_ADD',
  editableValuesAddWithKeys = 'EDITABLE_VALUE_ADD_WITH_KEYS', // values全量替换 key单条新增
  editableValuesDelete = 'EDITABLE_VALUES_DELETE', // 删除指定ID的数据项
  editableReplaceByCustomKey = 'EDITABLE_REPLACE_BY_CUSTOM_KEY', // 根据自定义键替换记录，用于处理ID变更的情况
}

// table init data
const InitTableState: ITableState<any> = {
  columns: [],
  data: [], // 1个Columns 对 1个dataSource 的时候使用
  multiData: {}, // 1个Columns 对 N个dataSource 的时候使用
  total: 0,
  title: '',
  clkItem: null,
  sorter: {},
  selectedKeys: [],
  selectedRecords: [],
  expandedKeys: [],
  expandedRecords: [],
  expandedTypes: [],
  backPagination: {
    current: 1,
    defaultPageSize: 10,
    total: 0,
    pageSizeOptions: ['10', '20', '30', '50'],
  },
  // editable 也一并做进来 不分了 不然太抽象了
  value: [], // 重点 如果是editable Table的话 就不使用data了 而是使用value
  editableKeys: [],
};

// table editable part init data
const InitEditableState: IEditableState<any> = {
  value: [],
  editableKeys: [],
};

const tableReducer = (
  state: ITableState<any>,
  action: { type: TableAction; payload: any },
) => {
  let { type, payload } = action;
  switch (type) {
    case TableAction.init:
      return { ...state, ...payload };
    case TableAction.columnsChange:
      return { ...state, columns: payload.columns };
    case TableAction.dataUnshift:
      return { ...state, data: payload.data.concat([...state.data]) };
    case TableAction.dataUnshiftUniq:
      // payload: data(object), key(匹配的键,唯一id), overWriteBy: {key: 匹配键，value: 匹配值} ?(覆盖规则，没有则新覆盖旧)
      let { key, data } = payload;
      // 匹配不到，直接滚
      if (!data?.[key]) {
        return { ...state };
      }
      if (payload?.overWriteBy) {
        // 有规定覆盖字段规则的话，走规则
        let checkList = state.data?.filter((d) => d?.[key] === data?.[key]);
        if (checkList?.length < 1) {
          // 就是 TableAction.dataUnshift
          return { ...state, data: [payload.data].concat([...state.data]) };
        }
        let result = [
          payload.data,
          ...state.data?.filter((d) => d?.[key] === data?.[key]),
        ]?.filter(
          (d) => d?.[payload?.overWriteBy?.key] === payload?.overWriteBy?.value,
        );
        return {
          ...state,
          // 如果根本没有一个能否匹配覆盖字段规则的，则默认新的覆盖旧的
          data:
            result.length > 0
              ? result.concat(
                  state.data?.filter((d) => d?.[key] !== data?.[key]),
                )
              : [payload.data].concat(
                  state.data?.filter((d) => d?.[key] !== data?.[key]),
                ),
        };
      } else {
        // 默认新的覆盖旧的
        return {
          ...state,
          data: [payload.data].concat(
            state.data?.filter((d) => d?.[key] !== data?.[key]),
          ),
        };
      }
    // 新增：简化版的dataUnshiftUniq，只接受key和data参数
    case TableAction.dataUnshiftUniqSimple:
      // payload: data(object), key(匹配的键,唯一id)
      let { key: simpleKey, data: simpleData } = payload;

      // 参数验证：如果无法获取key或data不包含key对应的值，则不做任何改变
      if (!simpleKey || !simpleData || simpleData[simpleKey] === undefined) {
        return { ...state };
      }

      // 实现：从头部插入数据，并根据key保证唯一性，若已存在相同key的数据则覆盖
      return {
        ...state,
        data: [simpleData].concat(
          state.data?.filter((d) => d?.[simpleKey] !== simpleData?.[simpleKey]),
        ),
      };
    case TableAction.dataPush:
      return { ...state, data: [...state.data].concat(payload.data) };
    case TableAction.dataFilt:
      return {
        ...state,
        data: [...state.data].filter((d) => d[payload.key] !== payload.value),
      };
    case TableAction.dataChange:
      return { ...state, data: payload.data };
    case TableAction.multiDataChange:
      return {
        ...state,
        multiData: { ...state.multiData, ...payload.multiData },
      };
    case TableAction.singleDataChange:
      if (payload.key && payload.value) {
        let index = state.data.findIndex(
          (d) => d[payload.key] === payload.value[payload.key],
        );
        return index !== -1
          ? {
              ...state,
              data: update(state.data, {
                [index]: {
                  $apply: function (d) {
                    return { ...d, ...payload.value };
                  },
                },
              }),
            }
          : { ...state };
      }
      return { ...state };
    case TableAction.dataPagiChange:
      return {
        ...state,
        data: payload.data,
        backPagination: { ...state.backPagination, ...payload.backPagination },
      };
    case TableAction.backPaginationChange:
      return {
        ...state,
        backPagination: { ...state.backPagination, ...payload.backPagination },
      };
    case TableAction.selectionChange:
      return {
        ...state,
        selectedKeys: payload.selectedKeys,
        selectedRecords: payload.selectedRecords,
      };
    case TableAction.sortChange:
      return {
        ...state,
        sorter: payload.sorter,
      };
    case TableAction.clkChange:
      return {
        ...state,
        clkItem: payload.clkItem,
      };
    case TableAction.expandChange:
      return {
        ...state,
        expandedKeys: payload.expandedKeys,
        expandedRecords: payload.expandedRecords,
        expandedTypes: payload?.expandedTypes ?? [],
      };
    case TableAction.summaryChange:
      return {
        ...state,
        summary: payload.summary,
      };
    case TableAction.titleChange:
      return {
        ...state,
        title: payload.title,
      };
    case TableAction.anyChange:
      return {
        ...state,
        ...payload,
      };
    // editable reducer
    case TableAction.editableValuesChange:
      return { ...state, value: payload.value };
    case TableAction.editableKeysChange:
      return { ...state, editableKeys: payload.editableKeys };
    case TableAction.editableSingleRecordChange: // single record change
      if (payload.key && payload.value) {
        let index = state.value.findIndex(
          (d) => d[payload.key] === payload.value[payload.key],
        );
        return index !== -1
          ? {
              ...state,
              value: update(state.value, { [index]: { $set: payload.value } }),
            }
          : { ...state };
      }
      return { ...state };
    case TableAction.editableSingleValueChange: // single value (in one record) change
      if (payload.key && payload.dataIndex && payload.value) {
        let index = state.value.findIndex(
          (d) => d[payload.key] === payload.value[payload.key],
        );

        return index !== -1
          ? {
              ...state,
              value: update(state.value, { [index]: { $set: payload.value } }),
            }
          : { ...state };
      }
      return { ...state };

    default:
      throw new Error('tableReducer需要一个type 或者 type非法');
  }
};

const tableEditPropsReducer = (state, { type, payload }) => {
  switch (type) {
    case EditableTableAction.editableValuesChange:
      return { ...state, value: payload.value };
    case EditableTableAction.editableKeysChange:
      return { ...state, editableKeys: payload.editableKeys };
    case EditableTableAction.editableSingleRecordChange: // single record change
      if (payload.key && payload.value) {
        let index = state.value.findIndex(
          (d) => d[payload.key] === payload.value[payload.key],
        );
        return index !== -1
          ? {
              ...state,
              value: update(state.value, { [index]: { $set: payload.value } }),
            }
          : { ...state };
      }
      return { ...state };
    case EditableTableAction.editableSingleValueChange: // single value (in one record) change
      if (payload.key && payload.dataIndex && payload.value) {
        let index = state.value.findIndex(
          (d) => d[payload.key] === payload.value[payload.key],
        );

        return index !== -1
          ? {
              ...state,
              value: update(state.value, { [index]: { $set: payload.value } }),
            }
          : { ...state };
      }
      return { ...state };
    case EditableTableAction.editableSingleValueUpsert:
      if (payload.value) {
        return {
          ...state,
          value: [...state.value, payload.value],
        };
      }
    case EditableTableAction.editableValueAdd:
      return { ...state, value: payload.value.concat([...state.value]) };
    case EditableTableAction.editableValuesAddWithKeys:
      return {
        ...state,
        value: payload.value,
        editableKeys: [payload.key, ...state.editableKeys],
      };
    case EditableTableAction.editableValuesDelete:
      if (payload.value && Array.isArray(payload.value)) {
        // 从 value 数组中移除指定 ID 的项目
        return {
          ...state,
          value: state.value.filter((item) => !payload.value.includes(item.Id)),
        };
      }
      return { ...state };
    case EditableTableAction.editableReplaceByCustomKey:
      // 根据自定义的keyField和keyValue找到记录并替换
      if (
        payload.keyField &&
        payload.keyValue !== undefined &&
        payload.newRecord
      ) {
        let index = state.value.findIndex(
          (d) => d[payload.keyField] === payload.keyValue,
        );
        return index !== -1
          ? {
              ...state,
              value: update(state.value, {
                [index]: { $set: payload.newRecord },
              }),
            }
          : { ...state };
      }
      return { ...state };
    default:
      throw new Error('tableEditPropsReducer要一个type 或者 type非法');
  }
};

export {
  InitTableState,
  InitEditableState,
  tableReducer,
  tableEditPropsReducer,
};
