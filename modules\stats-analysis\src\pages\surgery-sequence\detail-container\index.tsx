import React, { useEffect, useState, useReducer, Reducer } from 'react';
import { Emitter } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { TableProps } from 'antd';
import { useRequest, useModel } from 'umi';
import UniTable from '@uni/components/src/table';
import { uniCommonService } from '@uni/services/src';
import { InitTableState, TableAction, tableReducer } from '@uni/reducers/src';
import { IReducer, ITableState } from '@uni/reducers/src/interface';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { selectedTableColumnsProcessor } from '@/pages/combine-query/processor';
import { defaultCheckedColumnStateProcessor } from '@/components/queryDetailColumnsSettings/utils';
import DetailColumnsSettingModal from '@uni/components/src/query-detail-columns-settings';
import { DetailColumnSettingContentConstants } from '@/components/queryDetailColumnsSettings/constants';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import _ from 'lodash';
import './index.less';

const TABLENAME = 'OmniCard';
const INITIAL_PAGINATION = {
  current: 1,
  total: 0,
  pageSize: 10,
  pageSizeOptions: ['10', '20'],
  hideOnSinglePage: false,
};

interface Props {
  selectedTags: any[];
  id: string;
  modalVisible?: boolean;
}

const DetailContainer: React.FC<Props> = ({
  selectedTags,
  id,
  modalVisible,
}) => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster');
  const [tableState, dispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);
  const [columnsState, setColumnsState] = useState<Record<string, any>>({});
  const [pagination, setPagination] = useState(INITIAL_PAGINATION);

  // 获取所有列定义
  const { data: allCols } = useRequest(
    () =>
      uniCombineQueryService('Api/Analysis/AnaModelDef/GetRetColSubjectList', {
        params: { TableName: TABLENAME },
      }),
    {
      formatResult: (r) =>
        r.code === 0 && r.statusCode === 200
          ? r.data.map((c) => ({
              ...c,
              data: c.name,
              dataIndex: c.name,
              originTitle: c.title,
            }))
          : [],
    },
  );
  // 获取模板列
  const { data: templateCols, run: reloadTemplate } = useRequest(
    () =>
      uniCombineQueryService(
        'Api/V2/DmrAnalysis/IcdCategoryAnalysis/GetOperDetailsTemplate',
        { method: 'POST' },
      ),
    {
      formatResult: (r) => (r.code === 0 && r.statusCode === 200 ? r.data : []),
    },
  );
  // 获取数据
  const { run: loadData, loading: dataLoading } = useRequest(
    (cols, cur, size) => {
      const req = {
        DtParam: { Draw: 1, Start: (cur - 1) * size, Length: size },
        icdGrouperCols: [
          {
            isMain: searchParams?.IsMain || false,
            groupOption: selectedTags[0],
          },
        ],
        basicArgs: {
          hospCode: searchParams?.hospCodes,
          cliDepts: searchParams?.CliDepts,
          sdate: searchParams?.dateRange?.[0],
          edate: searchParams?.dateRange?.[1],
        },
        outputColumns: cols,
        icdCode: id,
      };
      return uniCombineQueryService(
        'Api/V2/DmrAnalysis/IcdCategoryAnalysis/GetOperDetails',
        { method: 'POST', requestType: 'json', data: req },
      );
    },
    {
      manual: true,
      formatResult: (res) => {
        if (res.code === 0 && res.statusCode === 200) {
          dispatch({
            type: TableAction.dataChange,
            payload: {
              data: res.data.data.map((d) => ({ ...d, uuid: uuidv4() })),
            },
          });
          setPagination((p) => ({ ...p, total: res.data.recordsTotal || 0 }));
        } else {
          dispatch({ type: TableAction.dataChange, payload: { data: [] } });
          setPagination((p) => ({ ...p, total: 0 }));
        }
      },
    },
  );
  // 保存模板
  const { run: saveTemplate } = useRequest(
    (data) =>
      uniCommonService(
        'Api/V2/DmrAnalysis/IcdCategoryAnalysis/SaveOperDetailsTemplate',
        { method: 'POST', data },
      ),
    {
      manual: true,
      formatResult: (r) => {
        if (r.code === 0 && r.statusCode === 200) {
          Emitter.emit(
            `${DetailColumnSettingContentConstants.MODAL_CLOSE}_Surgery`,
          );
          reloadTemplate();
        }
      },
    },
  );

  // 初始化列状态 & Reducer columns
  useEffect(() => {
    if (allCols?.length && Array.isArray(templateCols)) {
      const state = defaultCheckedColumnStateProcessor(allCols, templateCols);
      setColumnsState(state);
      const cols = tableColumnBaseProcessor(
        [],
        allCols.map((c) => ({
          ...c,
          title: state[c.name]?.title || c.title,
        })),
      );
      dispatch({ type: TableAction.columnsChange, payload: { columns: cols } });
    }
  }, [allCols, templateCols]);

  // 请求数据 & 重置分页
  useEffect(() => {
    if (
      searchParams &&
      selectedTags.length &&
      modalVisible &&
      Object.keys(columnsState).length
    ) {
      const outCols = selectedTableColumnsProcessor(columnsState);
      setPagination(INITIAL_PAGINATION);
      loadData(outCols, 1, INITIAL_PAGINATION.pageSize);
    }
  }, [searchParams, selectedTags, id, columnsState, modalVisible]);

  // 通知导出
  useEffect(() => {
    if (modalVisible && Object.keys(columnsState).length) {
      const exportCols = selectedTableColumnsProcessor(columnsState).map(
        (item) => {
          const col = tableState.columns.find((c: any) => c.id === item.Id);
          return { ...item, ExportTitle: item.CustomTitle || col?.title || '' };
        },
      );
      Emitter.emit('SURGERY_DETAIL_EXPORT_DATA', {
        outputColumns: exportCols,
        requestData: {
          icdGrouperCols: [
            {
              isMain: searchParams?.IsMain || false,
              groupOption: selectedTags[0],
            },
          ],
          basicArgs: {
            hospCode: searchParams?.hospCodes,
            cliDepts: searchParams?.CliDepts,
            sdate: searchParams?.dateRange?.[0],
            edate: searchParams?.dateRange?.[1],
          },
          icdCode: id,
          OperTypes: searchParams?.OperTypes,
        },
      });
    }
  }, [columnsState, modalVisible]);

  // 分页切换
  const onTableChange: TableProps<any>['onChange'] = ({
    current,
    pageSize,
  }) => {
    setPagination((p) => ({ ...p, current, pageSize }));
    const outCols = selectedTableColumnsProcessor(columnsState);
    loadData(outCols, current!, pageSize!);
  };

  // 保存列配置
  const onSaveCols = (items) => {
    saveTemplate({
      Subjects: items.map((i) => ({
        SubjectId: i.id,
        ColumnSort: i.order,
        CustomTitle: i.customTitle,
      })),
    });
  };

  return (
    <>
      <UniTable
        id="sequence-table-detail"
        rowKey="id"
        loading={dataLoading}
        columnsState={{ value: columnsState }}
        dictionaryData={dictData}
        className="table-container"
        forceColumnsUpdate
        columns={tableState.columns}
        scroll={{ x: 'max-content' }}
        dataSource={tableState.data}
        onChange={onTableChange}
        pagination={pagination}
      />
      <DetailColumnsSettingModal
        type="Surgery"
        detailColumns={tableState.columns}
        columnState={columnsState}
        onOk={onSaveCols}
      />
    </>
  );
};

export default DetailContainer;
