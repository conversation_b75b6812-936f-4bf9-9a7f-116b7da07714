import {
  CurrencyWidget,
  DecimalWidget,
  IcdeMultipleSelectWidget,
  IcdeSelectWidget,
  ModuleMultipleSelectWidget,
  ModuleSelectWidget,
  OperMultipleSelectWidget,
  OperSelectWidget,
  PercentWidget,
  PermillageWidget,
  TimeScapeRangeWidget,
  TimescapeWidget,
} from '@/pages/combine-query/widgets';
import CombineQueryIcdeSelect from '@/pages/combine-query/components/icde-select';
import { Between } from './combo-table/parser/tokens';

const extraWidgetsLikeConfig = {
  text: {
    operators: ['like', 'not_like', 'starts_with', 'ends_with'],
  },
};

const types = {
  percent: {
    defaultOperator: 'equal',
    widgets: {
      percent: {
        operators: [
          'equal',
          'not_equal',
          'less',
          'less_or_equal',
          'greater',
          'greater_or_equal',
          'between',
          'not_between',
          'is_null',
          'is_not_null',
        ],
      },
    },
  },
  permillage: {
    defaultOperator: 'equal',
    widgets: {
      permillage: {
        operators: [
          'equal',
          'not_equal',
          'less',
          'less_or_equal',
          'greater',
          'greater_or_equal',
          'between',
          'not_between',
          'is_null',
          'is_not_null',
        ],
      },
    },
  },
  decimal: {
    defaultOperator: 'equal',
    widgets: {
      decimal: {
        operators: [
          'equal',
          'not_equal',
          'less',
          'less_or_equal',
          'greater',
          'greater_or_equal',
          'between',
          'not_between',
          'is_null',
          'is_not_null',
        ],
      },
    },
  },
  currency: {
    defaultOperator: 'equal',
    widgets: {
      currency: {
        operators: [
          'equal',
          'not_equal',
          'less',
          'less_or_equal',
          'greater',
          'greater_or_equal',
          'between',
          'not_between',
          'is_null',
          'is_not_null',
        ],
      },
    },
  },
  moduleSelect: {
    defaultOperator: 'equal',
    widgets: {
      moduleSelect: {
        operators: ['equal', 'not_equal', 'is_null', 'is_not_null'],
      },
      moduleMultipleSelect: {
        operators: ['select_any_in'],
      },
      ...extraWidgetsLikeConfig,
    },
  },

  icdeSelect: {
    defaultOperator: 'select_any_in',
    widgets: {
      icdeSelect: {
        operators: ['equal', 'not_equal', 'is_null', 'is_not_null', 'between'],
      },
      icdeMultipleSelect: {
        operators: ['select_any_in'],
      },
      ...extraWidgetsLikeConfig,
    },
  },
  operSelect: {
    defaultOperator: 'select_any_in',
    widgets: {
      operSelect: {
        operators: ['equal', 'not_equal', 'is_null', 'is_not_null', 'between'],
      },
      operMultipleSelect: {
        operators: ['select_any_in'],
      },
      ...extraWidgetsLikeConfig,
    },
  },

  date: {
    defaultOperator: 'between',
    widgets: {
      date: {
        operators: [
          'less',
          'less_or_equal',
          'greater',
          'greater_or_equal',
          'equal',
          'not_equal',
          'is_null',
          'is_not_null',
        ],
        opProps: {
          between: {
            isSpecialRange: true,
            textSeparators: [null, null],
          },
        },
      },
      dateRange: {
        operators: ['between', 'not_between'],
        opProps: {
          between: {
            isSpecialRange: true,
            textSeparators: [null, null],
          },
        },
      },
      ...extraWidgetsLikeConfig,
    },
  },
};

const widgets = {
  percent: PercentWidget,
  permillage: PermillageWidget,
  decimal: DecimalWidget,
  currency: CurrencyWidget,
  moduleSelect: ModuleSelectWidget,
  moduleMultipleSelect: ModuleMultipleSelectWidget,
  icdeSelect: IcdeSelectWidget,
  icdeMultipleSelect: IcdeMultipleSelectWidget,
  operSelect: OperSelectWidget,
  operMultipleSelect: OperMultipleSelectWidget,
  date: TimescapeWidget,
  dateRange: TimeScapeRangeWidget,
};

export default {
  types,
  widgets,
};
