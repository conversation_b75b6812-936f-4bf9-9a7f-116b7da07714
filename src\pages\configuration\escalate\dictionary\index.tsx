import {
  Dropdown,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Spin,
  Tabs,
  Tree,
  Card,
} from 'antd';
import { UniTable } from '@uni/components/src';
import { DownOutlined } from '@ant-design/icons';
import React, { useEffect, useRef, useState } from 'react';
import { dictionaryTableColumns } from '@/pages/configuration/base/columns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { v4 as uuidv4 } from 'uuid';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import {
  ConfigurationDictionaryItem,
  DictionaryItemCompare,
  DictionaryModuleItem,
} from '@/pages/configuration/base/interfaces';
import './index.less';
import { useModel } from '@@/plugin-model/useModel';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { escalateDictionaryTableColumns } from '@/pages/configuration/escalate/columns';
import UniEditableTable from '@uni/components/src/table/edittable';
import { getTreeFilterWithSearchedText } from '@/pages/configuration/utils';
import trim from 'lodash/trim';
import isEmpty from 'lodash/isEmpty';

const EscalateDictionary = () => {
  const [form] = Form.useForm();

  const [rightClickItem, setRightClickItem] = useState(undefined);

  const [moduleTreeData, setModuleTreeData] = useState([]);

  const [treeDataRendered, setTreeDataRendered] = useState([]);

  const [treeExpandedKeys, setTreeExpandedKeys] = useState<React.Key[]>([
    '0-0-0',
  ]);

  const [searchText, setSearchText] = useState('');

  const [treeSelectedItem, setTreeSelectedItem] = useState(undefined);

  const [dictionaryOperateType, setDictionaryOperateType] = useState(undefined);

  const [allModuleLabels, setAllModuleLabels] = useState([]);
  const [allModuleKeys, setAllModuleKeys] = useState([]);

  // globalState
  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateForSlave',
  );

  useEffect(() => {
    dictionaryConfigurationModulesReq();
  }, []);

  const onExpand = (expandedKeys: React.Key[]) => {
    setTreeExpandedKeys(expandedKeys);
  };

  const onSelect = (selectedKeys: React.Key[], info: any) => {
    setTreeSelectedItem(info?.node?.data);
  };

  // search
  const onChange = (e) => {
    const { value } = e.target;
    setSearchText(value);
  };

  useEffect(() => {
    if (!isEmpty(trim(searchText))) {
      const searchedTreeData = getTreeFilterWithSearchedText(
        searchText,
        moduleTreeData,
      );
      // 自动展开
      setTreeExpandedKeys(searchedTreeData.map((d) => d.key));
      setTreeDataRendered(searchedTreeData);
    } else {
      setTreeDataRendered(moduleTreeData);
    }
  }, [moduleTreeData, searchText]);

  const {
    loading: dictionaryConfigurationModulesLoading,
    run: dictionaryConfigurationModulesReq,
  } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetCodeDictionaryModules', {
        method: 'POST',
        data: {
          ModuleGroup: 'Dmr',
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<DictionaryModuleItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          processModuleTree(response?.data);
        } else {
          // TODO 提示信息
        }
      },
    },
  );

  const processModuleTree = (modules: DictionaryModuleItem[]) => {
    let treeData = [];
    let allModuleLabels = [];

    let hasInsertedModuleData = new Set();

    modules
      ?.filter((item) => item.Directories && item.Directories?.length > 0)
      .forEach((item) => {
        allModuleLabels.push(...item.Directories);

        if (
          !treeData.find(
            (treeItem) => treeItem.title === item.Directories?.at(0),
          )
        ) {
          treeData.push({
            title: item?.Directories?.at(0),
            key: item?.Directories?.at(0),
            selectable: false,
            titleRenderer: (nodeData) => {
              return <span>{nodeData?.title}</span>;
            },
          });
        }

        item.Directories?.slice(1)?.forEach((dictionaryItem, index) => {
          let parentTreeData = treeData.find(
            (treeItem) => treeItem.title === item.Directories?.at(0),
          );
          if (parentTreeData) {
            if (!hasInsertedModuleData?.has(item?.Module)) {
              hasInsertedModuleData.add(item?.Module);
              parentTreeData['children'] = [
                ...(parentTreeData['children'] || []),
                {
                  title: dictionaryItem,
                  key: item.Module,
                  data: item,
                },
              ];
            }
          }
        });
      });

    setModuleTreeData(treeData);

    // all labels keys
    setAllModuleKeys(modules?.map((item) => item.Module));
    setAllModuleLabels(allModuleLabels);
  };

  return (
    <div
      id={'escalate-dictionary-configuration-container'}
      className={'escalate-dictionary-configuration-container'}
    >
      <div className="escalate-dictionary-tree-search-container">
        <Input.Search placeholder="搜索..." allowClear onChange={onChange} />
        <Tree.DirectoryTree
          className={'configuration-tree-container'}
          showLine={true}
          showIcon={false}
          switcherIcon={<DownOutlined />}
          expandedKeys={treeExpandedKeys}
          onExpand={onExpand}
          onSelect={onSelect}
          treeData={treeDataRendered}
          titleRender={(nodeData) => {
            // FIXME 写死两层
            if (nodeData.children) {
              return <span>{nodeData?.title}</span>;
            } else {
              return <span>{nodeData?.title}</span>;
            }
          }}
          onRightClick={({ event, node }) => {
            if (event.type === 'contextmenu') {
              setRightClickItem(node);
            }
          }}
        />
      </div>
      <div className={'configuration-table-container'}>
        <Card
          title={
            treeSelectedItem ? (
              <>
                <span className="font-medium-1">
                  {treeSelectedItem?.Directories?.[0]}
                </span>
                &nbsp;&nbsp;
                <span>{treeSelectedItem?.Directories?.[1]}</span>
              </>
            ) : (
              '列表'
            )
          }
        >
          <DictionaryTable selectModule={treeSelectedItem} />
        </Card>
      </div>
    </div>
  );
};

interface DictionaryTableProps {
  selectModule: DictionaryModuleItem;
  dataSource?: any[];
}

export const DictionaryTable = (props: DictionaryTableProps) => {
  const [form] = Form.useForm();
  const ref = useRef<any>();

  const [dictionaryColumns, setDictionaryColumns] = useState<any[]>([]);

  const [dictionaryTableDataSource, setDictionaryTableDataSource] = useState(
    [],
  );

  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  useEffect(() => {
    if (props?.selectModule) {
      if (dictionaryColumns?.length === 0) {
        dictionaryColumnReq();
      }

      dictionaryReq();
      setEditableColumnKeys([]);
    }
  }, [props?.selectModule]);

  useEffect(() => {
    Emitter.onMultiple(
      [
        ConfigurationEvents.ESCALATE_INSURANCE_DICTIONARY_SELECT,
        ConfigurationEvents.ESCALATE_HQMS_DICTIONARY_SELECT,
        ConfigurationEvents.ESCALATE_WT_DICTIONARY_SELECT,
      ],
      (data) => {
        let currentFormValue = form.getFieldsValue()?.[data?.id];
        if (currentFormValue) {
          // set form value
          Object.keys(data?.values)?.forEach((key) => {
            currentFormValue[key] = data?.values?.[key];
          });
          form.setFieldValue(data?.id, currentFormValue);
        }
      },
    );

    return () => {
      Emitter.offMultiple([
        ConfigurationEvents.ESCALATE_INSURANCE_DICTIONARY_SELECT,
        ConfigurationEvents.ESCALATE_HQMS_DICTIONARY_SELECT,
        ConfigurationEvents.ESCALATE_WT_DICTIONARY_SELECT,
      ]);
    };
  }, [dictionaryTableDataSource]);

  const onDictionaryItemSave = (rowKey, data) => {
    console.error('rowKey', data, rowKey, editableColumnKeys);
    // table data
    let currentRowData = dictionaryTableDataSource?.find(
      (item) => item?.CodeDictionaryId === data?.CodeDictionaryId,
    );
    if (currentRowData) {
      Object.keys(data)?.forEach((key) => {
        currentRowData[key] = data?.[key];
      });
    }

    setDictionaryTableDataSource(dictionaryTableDataSource?.slice());
  };

  const { run: dictionaryColumnReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Sys/CodeSys/GetDmrCodeDictionaryAllCompares',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: async (response: RespVO<TableColumns>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          setDictionaryColumns(
            tableColumnBaseProcessor(
              escalateDictionaryTableColumns,
              response?.data?.Columns,
            ),
          );
        } else {
          setDictionaryColumns([]);
        }
      },
    },
  );

  const { loading: dictionaryLoading, run: dictionaryReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Sys/CodeSys/GetDmrCodeDictionaryAllCompares',
        {
          method: 'POST',
          data: {
            Module: props?.selectModule?.Module,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ConfigurationDictionaryItem[]>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          let tableDataSource = response?.data.slice();
          setDictionaryTableDataSource(
            tableDataSource?.map((item) => {
              item['id'] = uuidv4();

              return item;
            }),
          );
        } else {
          setDictionaryTableDataSource([]);
        }
      },
    },
  );

  const {
    loading: dictionaryComparesUpdateLoading,
    run: dictionaryComparesUpdateReq,
  } = useRequest(
    (compareItem) => {
      return uniCommonService(
        'Api/Sys/CodeSys/UpdateDmrCodeDictionaryAllCompare',
        {
          method: 'POST',
          data: {
            Module: props?.selectModule?.Module,
            ...compareItem,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
    },
  );

  return (
    <>
      <UniEditableTable
        actionRef={ref}
        id={`${props?.selectModule?.Module}-dictionary-table`}
        rowKey={'id'}
        scroll={{
          y: 720,
        }}
        bordered={true}
        loading={dictionaryLoading || dictionaryComparesUpdateLoading}
        recordCreatorProps={false}
        columns={dictionaryColumns}
        value={dictionaryTableDataSource}
        clickable={false}
        pagination={false}
        editable={{
          form: form,
          type: 'multiple',
          editableKeys: editableColumnKeys,
          onSave: async (rowKey, data, row) => {
            console.log(rowKey, data, row);
            let dictionaryUpsertResponse = await dictionaryComparesUpdateReq(
              data,
            );
            if (
              dictionaryUpsertResponse?.code === 0 &&
              dictionaryUpsertResponse?.statusCode === 200
            ) {
              onDictionaryItemSave(rowKey, data);
            }
          },
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.save, defaultDoms.cancel];
          },
          onChange: setEditableColumnKeys,
        }}
      />
    </>
  );
};

export default EscalateDictionary;
