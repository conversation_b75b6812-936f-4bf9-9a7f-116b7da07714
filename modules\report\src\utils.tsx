import {
  commonBusinessDomain,
  uniCommonService,
} from '@uni/services/src/commonService';
import { v4 as uuidv4 } from 'uuid';
import { message, notification } from 'antd';
import { isEmptyValues } from '@uni/utils/src/utils';
import { downloadFile, UseDispostionEnum } from '@uni/utils/src/download';
import { ReportMasterItem } from '@/interfaces';
import cloneDeep from 'lodash/cloneDeep';
import IconBtn from '@uni/components/src/iconBtn';
import React from 'react';

export const transformReportColumnsWithDataTypeIntOrderable = (
  reportType: string,
  columns: any[],
) => {
  columns?.forEach((item) => {
    // 这里认为dataType 是int的都能排序当且仅当 统计类报表
    if (reportType === 'StatsReadOnly' || reportType === 'StatsPersist') {
      if (item?.dataType === 'int') {
        item['orderable'] = true;
      }
    }
  });

  return columns;
};

export const extraColumns = (config: any) => {
  return [
    {
      dataIndex: 'directTo',
      visible: true,
      width: 40,
      align: 'center',
      title: '',
      fixed: 'left',
      render: (node, record, index) => {
        return (
          <IconBtn
            title={config?.title}
            type="checkInfo"
            className="operation-btn"
            onClick={(e) => {
              if (record?.HisId) {
                if (config?.reuseTab) {
                  if (config?.type === 'DMR_VIEW_CLICK') {
                    (global?.window as any)?.eventEmitter?.emit(
                      'DMR_AOD_STATUS',
                      {
                        status: true,
                        hisId: record.HisId,
                      },
                    );
                  } else {
                    global['tabAwareWorker'].port.postMessage({
                      type: config?.type,
                      payload: {
                        hisId: encodeURIComponent(record.HisId),
                      },
                    });
                  }
                } else {
                  window.open(
                    `${config?.url}?hisId=${encodeURIComponent(record.HisId)}`,
                  );
                }
              }
            }}
          />
        );
      },
    },
  ];
};

export const downloadReportByBlobId = (blobId?: string, name?: string) => {
  if (blobId) {
    let link = document.createElement('a');
    if (name) {
      link.download = name;
    }
    link.href = `${commonBusinessDomain}/Api/Report/Report/GetArchivedFile?BlobId=${blobId}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// dmr: {
//  reuseTab: true / false
// },
// chs: {
//  reuseTab: true / false
// }

export const masterItemExtraConfig = (extraConfig: any) => {
  if (isEmptyValues(extraConfig)) {
    return {};
  }

  try {
    let extraConfigItem = JSON.parse(extraConfig);
    // 暂时仅有 跳转有用
    if (!isEmptyValues(extraConfigItem?.dmr)) {
      let dmrConfig = extraConfigItem?.dmr;
      if (dmrConfig) {
        return {
          ...extraConfigItem,
          extraColumns: extraColumns({
            title: '查看病案首页',
            url: '/dmr/index',
            reuseTab: dmrConfig?.reuseTab ?? true,
            type: 'DMR_VIEW_CLICK',
          }),
        };
      }
    }

    if (!isEmptyValues(extraConfigItem?.chs)) {
      let chsConfig = extraConfigItem?.chs;
      if (chsConfig) {
        return {
          ...extraConfigItem,
          extraColumns: extraColumns({
            title: '查看结算清单',
            url: '/chs/index',
            reuseTab: chsConfig?.reuseTab ?? true,
            type: 'CHS_VIEW_CLICK',
          }),
        };
      }
    }
  } catch (err) {
    console.error('ExtraConfig parse error: ' + err);
    return {};
  }
};

// Only in stats report
export const reportBackgroundExport = async (
  reportTitle: string,
  reportSettingMasterId?: string,
  reportBaseId?: string,
  reportArgs?: any,
) => {
  if (reportSettingMasterId && (reportBaseId || reportArgs)) {
    let exportResponse = await uniCommonService(`Api/Report/Report/Export`, {
      method: 'POST',
      data: {
        reportSettingMasterId: reportSettingMasterId,
        reportBaseId: reportBaseId,
        ReportArgs: reportArgs,
      },
      requestType: 'json',
    });

    if (exportResponse?.response) {
      downloadFile(
        reportTitle,
        exportResponse?.response,
        UseDispostionEnum.replace,
      );
    } else {
      message.error(`${reportTitle}导出错误`);
    }
  } else {
    message.warn('缺少报表导出字段');
  }
};

// ReportSettingMasterId 顶层
export const reportBackgroundBundleExport = async (
  exportExcelTitle: string,
  exportRequestBody: any,
) => {
  let exportResponse = await uniCommonService(
    `Api/Report/Report/BundleExport`,
    {
      method: 'POST',
      data: {
        ExportFileName: exportExcelTitle,
        ...exportRequestBody,
      },
      requestType: 'json',
    },
  );

  if (exportResponse?.response) {
    message.success('导出成功');
    downloadFile(
      exportExcelTitle,
      exportResponse?.response,
      UseDispostionEnum.replace,
    );
  } else {
    message.error(`${exportExcelTitle}导出错误`);
  }
};

export const reportBackgroundBriefExport = async (
  exportExcelTitle: string,
  exportRequestBody: any,
) => {
  let exportResponse = await uniCommonService(`Api/Report/Report/ExportBrief`, {
    method: 'POST',
    data: {
      ExportFileName: exportExcelTitle,
      ...exportRequestBody,
    },
    requestType: 'json',
  });

  if (exportResponse?.response) {
    message.success('导出成功');
    downloadFile(
      exportExcelTitle,
      exportResponse?.response,
      UseDispostionEnum.replace,
    );
  } else {
    message.error(`${exportExcelTitle}导出错误`);
  }
};

export const showNotification = (description: string, title?: string) => {
  let key = uuidv4();
  notification.error({
    message: '报表校验不通过',
    description: description || '',
    // btn: (
    //   <>
    //     <Button type="primary" size="small" onClick={() => notification.close(key)}>
    //       知道了
    //     </Button>
    //     <Button type="primary" size="small" onClick={() => jumpToPosition()}>
    //       跳转至对应位置
    //     </Button>
    //   </>
    // ),
    key: key,
  });
};

// 报表用表头
export const reportTableGroupNameHeaderProcessor = (
  tableColumns: any[],
  // tableData: any[],
  itemCustomProps?: any,
) => {
  let resultColumns = [];

  tableColumns?.forEach((item, index) => {
    item['width'] =
      index === tableColumns?.length - 1 ? undefined : item?.width ?? 200;

    if (itemCustomProps) {
      item = {
        ...item,
        ...itemCustomProps,
      };
    }
    if (isEmptyValues(item?.groupName)) {
      resultColumns.push(item);
    } else {
      let innestColumnItem = undefined;
      // 根据groupName 构建一套header出来
      item?.groupName?.forEach((groupNameItem, index) => {
        if (index === 0) {
          let currentColumnItem = resultColumns?.find(
            (item) => item?.dataIndex === groupNameItem,
          );
          if (!currentColumnItem) {
            innestColumnItem = {
              dataIndex: groupNameItem,
              title: groupNameItem,
              visible: true,
              children: [],
            };
            resultColumns?.push(innestColumnItem);
          } else {
            innestColumnItem = currentColumnItem;
          }
        } else {
          let parentColumnItem = resultColumns?.find(
            (columnItem) =>
              columnItem?.dataIndex === item?.groupName?.at(index - 1),
          );
          if (parentColumnItem) {
            let currentColumnItem = parentColumnItem?.children?.find(
              (item) => item?.dataIndex === groupNameItem,
            );
            if (!currentColumnItem) {
              innestColumnItem = {
                dataIndex: groupNameItem,
                title: groupNameItem,
                visible: true,
                children: [],
              };

              parentColumnItem['children'] = [
                ...(parentColumnItem['children'] || []),
                innestColumnItem,
              ];
            } else {
              innestColumnItem = currentColumnItem;
            }
          }
        }
      });

      // 放到当前寻找到的最内一层的columnItem 下
      if (innestColumnItem) {
        innestColumnItem['children'] = [
          ...(innestColumnItem['children'] || []),
          item,
        ];

        innestColumnItem['order'] = Math.min(
          ...innestColumnItem['children']?.map((item) => {
            return item?.order ?? 0;
          }),
        );
      }
    }
  });

  console.log('resultColumns', resultColumns);

  return resultColumns;
};

export const findDataIndexInColumns = (dataIndex: string, columns: any[]) => {
  let flattenColumns = flattenColumnsWithoutParent(columns);
  let dataIndexItemIndex = flattenColumns?.findIndex(
    (item) => item?.dataIndex === dataIndex,
  );
  return dataIndexItemIndex + 1;
};

export const flattenColumnsWithoutParent = (columns: any[]) => {
  let result = [];

  columns
    ?.filter((item) => item?.visible !== false)
    ?.forEach((item) => {
      if (item?.children && item?.children?.length > 0) {
        result.push(...flattenColumnsWithoutParent(item?.children));
      } else {
        result.push(item);
      }
    });

  console.log('result', result);

  return result;
};

export const flattenColumns = (columns: any[]) => {
  let result = [];

  columns
    ?.filter((item) => item?.visible !== false)
    ?.forEach((item) => {
      result.push(item);

      if (item?.children && item?.children?.length > 0) {
        result.push(...flattenColumns(item?.children));
      }
    });

  return result;
};

export const tableCustomTitleProcessor = (
  masterItem: ReportMasterItem,
  selectedData?: any,
) => {
  const regex = /\{([a-zA-Z]+)\}/g;

  // 没有customTitle 就用title
  if (isEmptyValues(masterItem?.CustomTitle)) {
    return masterItem?.Title;
  }

  // 有customTitle 但是没有 选中的行/列就 返回去掉了参数的customTitle
  if (isEmptyValues(selectedData)) {
    return masterItem?.CustomTitle?.replaceAll(regex, '');
  }

  // 有customTitle 并且有选中的行/列 就返回customTitle 中的参数替换为 真实数据
  // 获取所有的匹配项目
  let matchColumnItemGroups = masterItem?.CustomTitle.match(regex);
  console.log('matchColumnItemGroups', matchColumnItemGroups);
  if (matchColumnItemGroups?.length === 0) {
    // 没有匹配中的也就返回customTitle
    return masterItem?.CustomTitle;
  } else {
    let tempTitle = masterItem?.CustomTitle;
    console.log('matchColumnItemGroups', matchColumnItemGroups);
    matchColumnItemGroups?.forEach((matchedItem) => {
      let columnKey = matchedItem.replaceAll('{', '').replaceAll('}', '');
      // 如果 数据 存在  从columns 找出来 columnItem
      if (selectedData[columnKey]) {
        tempTitle = tempTitle.replaceAll(matchedItem, selectedData[columnKey]);
      } else {
        // 不存在的话 把这个key 整个替换掉
        tempTitle = tempTitle.replaceAll(matchedItem, '');
      }
    });

    return tempTitle;
  }
};

const dataItemWithColumnModuleProcessor = (
  dictionaryData: any,
  module,
  dataItem,
) => {
  if (dictionaryData?.[module]) {
    let dictionaryItem = dictionaryData?.[module]?.find(
      (item) => item.Code === dataItem,
    );
    if (dictionaryItem) {
      return dictionaryItem?.Name || dataItem;
    }
  }

  return dataItem;
};

export const dataItemToModuleActualData = (
  record: any,
  reportTableColumns: any[],
  globalState: any,
) => {
  let result = cloneDeep(record);
  let flattenedColumns = flattenColumns(reportTableColumns);
  Object.keys(result)?.forEach((key) => {
    let columnItem = flattenedColumns?.find((item) => item?.dataIndex === key);
    if (columnItem) {
      let data = result[key];
      if (columnItem?.dictionaryModule) {
        let currentDictionaryData = globalState;
        if (columnItem?.dictionaryModuleGroup) {
          currentDictionaryData =
            globalState?.[columnItem?.dictionaryModuleGroup];
        }

        result[key] = dataItemWithColumnModuleProcessor(
          currentDictionaryData,
          columnItem?.dictionaryModule,
          data,
        );
      }
    }
  });

  console.log('dataItemToModuleActualData', result);

  return result;
};
