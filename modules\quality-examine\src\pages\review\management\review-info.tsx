import ReviewTable from '@/pages/review/components/review-table';
import React, { useEffect, useRef, useState } from 'react';
import {
  AssignmentItem,
  BatchItem,
  BatchMasterItem,
} from '@/pages/review/interface';
import './index.less';
import { Button, Col, Form, Input, Row } from 'antd';
import Datepicker from '@uni/components/src/picker/datepicker';
import locale from 'antd/es/date-picker/locale/zh_CN';
import dayjs from 'dayjs';
import { UniSelect } from '@uni/components/src';
import { useModel } from 'umi';
import uniqBy from 'lodash/uniqBy';
import { isEmptyValues } from '@uni/utils/src/utils';
import { RevieweeType, RevieweeTypeToLabel } from '@/pages/review/constants';
import { cloneDeep, uniq } from 'lodash';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';

interface ReviewManagementInfoProps {
  containerRef: any;
  taskTableRef: any;
  activeKey: string;
  batchId?: string;
  batchInfo?: BatchItem;
  dmrPreviewContainerRef?: any;
  dmrReviewerContainerRef?: any;
  assignments?: AssignmentItem[];

  tabTitleContainerRef?: any;

  extraConfig?: any;
  tabKey: string;
}

const ReviewManagementInfo = (props: ReviewManagementInfoProps) => {
  const [form] = Form.useForm();

  const [batchMasters, setBatchMasters] = useState<BatchMasterItem[]>([]);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const taskExtraParamsGetter = () => {
    let formValues = form?.getFieldsValue();

    let extraParams = {};
    Object.keys(formValues)?.forEach((key) => {
      if (key === 'Keyword' || key === 'Coder') {
        extraParams[key] = formValues[key]?.trim();
      } else {
        if (!isEmptyValues(formValues[key])) {
          if (Array.isArray(formValues[key])) {
            extraParams[key] = formValues[key];
          } else {
            extraParams[key] = [formValues[key]];
          }
        }
      }
    });

    return extraParams;
  };

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      onRowSelect: (codes: string[], key: string) => {
        form.resetFields();
        form.setFieldValue(key, codes);
        props?.taskTableRef?.current?.freshQueryTable();
      },
      setDefaultFormValue: () => {
        form.setFieldValue('Statuses', 'ReSubmitted');
      },
      getTaskExtraParams: () => {
        return taskExtraParamsGetter();
      },
    };
  });

  useEffect(() => {
    getBatchMasterReq();
  }, []);

  useEffect(() => {
    if (props?.activeKey === 'REVIEW_INFO') {
      if (!isEmptyValues(props?.batchInfo)) {
        props?.taskTableRef?.current?.freshQueryTable();
      }
    }
  }, [props?.batchInfo, props?.activeKey]);

  const { loading: getBatchMasterLoading, run: getBatchMasterReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Sys/QualityExamineSys/GetQualityExamineSettingMasters',
        {
          method: 'GET',
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setBatchMasters(response?.data);
        } else {
          setBatchMasters([]);
        }
      },
    },
  );

  const currentBatchMaster =
    batchMasters?.find(
      (item) => item?.MasterId === props?.batchInfo?.MasterId,
    ) ?? batchMasters?.at(0);

  return (
    <div className={'review-info-container'}>
      <div className={'info-search-container'}>
        <Form
          style={{ width: '100%' }}
          form={form}
          preserve={false}
          colon={false}
        >
          <Form.Item label="病案标识" name="Keyword">
            <Input style={{ width: 260 }} placeholder={'请输入'} />
          </Form.Item>

          <Form.Item label="院区" name="hospCode">
            <UniSelect
              style={{ width: 260 }}
              placeholder={'请选择院区'}
              dataSource={globalState?.dictData?.Hospital || []}
              optionValueKey={'Code'}
              optionNameKey={'Name'}
            />
          </Form.Item>

          <Form.Item label="科室" name="CliDepts">
            <UniSelect
              style={{ width: 260 }}
              placeholder={'请选择科室'}
              dataSource={globalState?.dictData?.CliDepts || []}
              optionValueKey={'Code'}
              optionNameKey={'Name'}
            />
          </Form.Item>

          <Form.Item label="评审状态" name="Statuses">
            <UniSelect
              style={{ width: 260 }}
              placeholder={'请选择评审状态'}
              dataSource={
                props?.tabKey === 'REVIEW_INFO'
                  ? globalState?.dictData?.QualityExamineStatus || []
                  : globalState?.dictData?.QualityExamineStatus?.filter(
                      (item) => {
                        return [
                          'ReSubmitted',
                          'Rejected',
                          'Accepted',
                        ]?.includes(item?.Code);
                      },
                    ) || []
              }
              optionValueKey={'Code'}
              optionNameKey={'Name'}
            />
          </Form.Item>

          {/*<Form.Item label="评审人" name="ReviewerCodes">*/}
          {/*  <UniSelect*/}
          {/*    style={{ width: 260 }}*/}
          {/*    placeholder={'请选择评审人'}*/}
          {/*    dataSource={uniqBy(*/}
          {/*      props?.assignments?.map((item) => ({*/}
          {/*        label: item.ReviewerName,*/}
          {/*        value: item.ReviewerCode,*/}
          {/*      })),*/}
          {/*      'value',*/}
          {/*    )}*/}
          {/*  />*/}
          {/*</Form.Item>*/}

          {/*<Col span={18}>*/}
          {/*  <Form.Item*/}
          {/*    label="被评审人"*/}
          {/*    name="RevieweeCodes">*/}
          {/*    <UniSelect*/}
          {/*      style={{width: 260}}*/}
          {/*      placeholder={"请选择被评审人"}*/}
          {/*      dataSource={revieweeDataSource}*/}
          {/*    />*/}
          {/*  </Form.Item>*/}
          {/*</Col>*/}
          <Form.Item label="审核人" name="ReviewerCodes">
            <UniSelect
              style={{ width: 260 }}
              placeholder={'请选择审核人'}
              dataSource={uniqBy(props?.assignments, 'ReviewerCode')}
              optionValueKey={'ReviewerCode'}
              optionNameKey={'ReviewerName'}
            />
          </Form.Item>

          <Form.Item label="编码员" name="Coder">
            <UniSelect
              style={{ width: 260 }}
              placeholder={'请选择编码员'}
              dataSource={globalState?.dictData?.Coder || []}
              optionValueKey={'Code'}
              optionNameKey={'Name'}
            />
          </Form.Item>

          {RevieweeTypeToLabel?.[currentBatchMaster?.RevieweeType] !==
            undefined && (
            <Form.Item
              label={RevieweeTypeToLabel?.[currentBatchMaster?.RevieweeType]}
              name={`${currentBatchMaster?.RevieweeType}s`}
            >
              <UniSelect
                style={{ width: 260 }}
                placeholder={'请选择'}
                dataSource={uniq(
                  props?.assignments?.filter(
                    (item) =>
                      item?.RevieweeType === currentBatchMaster?.RevieweeType,
                  ) ?? [],
                  'RevieweeCode',
                )}
                optionValueKey={'RevieweeCode'}
                optionNameKey={'RevieweeName'}
              />
            </Form.Item>
          )}
        </Form>

        <Button
          className={'search-btn'}
          type={'primary'}
          onClick={() => {
            // 直接调用查询
            props?.taskTableRef?.current?.freshQueryTable();
          }}
        >
          查询
        </Button>
      </div>

      <ReviewTable
        {...props?.extraConfig}
        id={'dmr-review-management-table'}
        masterId={props?.batchInfo?.MasterId}
        batchId={props?.batchInfo?.BatchId?.toString()}
        batchInfo={props?.batchInfo}
        taskTableRef={props?.taskTableRef}
        dmrPreviewContainerRef={props?.dmrPreviewContainerRef}
        tabTitleContainerRef={props?.tabTitleContainerRef}
        fastSelectShow={false}
        scroll={{ y: 470 }}
        cancelProps={{
          showCancel: true,
          dmrReviewerContainerRef: props?.dmrReviewerContainerRef,
        }}
        taskExtraParams={() => {
          return taskExtraParamsGetter();
        }}
      />
    </div>
  );
};

export default ReviewManagementInfo;
