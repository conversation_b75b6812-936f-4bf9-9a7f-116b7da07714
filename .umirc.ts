import { defineConfig } from 'umi';
import AntdDayjsWebpackPlugin from 'antd-dayjs-webpack-plugin';
import CompressionPlugin from 'compression-webpack-plugin';
import {
  configurationRoutes,
  escalateRoutes,
  institutionRoutes,
  insurConfigurationRoutes,
} from './src/routes';
import { title } from './.umirc.commom';
import { apps } from './apps';

export default defineConfig({
  favicon: '/favicon.ico',
  nodeModulesTransform: {
    type: 'none',
  },
  hash: true,
  ignoreMomentLocale: true,
  chainWebpack(config: any) {
    // 添加额外插件
    config.plugin('moment2dayjs').use(AntdDayjsWebpackPlugin);
    config.plugin('compression-webpack-plugin').use(CompressionPlugin, [
      {
        test: /\.js$|\.html$|\.css$/,
        exclude: /\/public\/*/,
        threshold: 10240,
        algorithm: 'gzip',
        deleteOriginalAssets: false,
      },
    ]);

    config.module
      .rule('mjs$')
      .test(/\.mjs$/)
      .include.add(/node_modules/)
      .end()
      .type('javascript/auto');
  },
  title: title,
  // better xlsx 不兼容 mfsu
  // mfsu: {},
  fastRefresh: {},
  dva: { immer: true, hmr: true, skipModelValidate: false },
  locale: {
    default: 'zh-CN',
    antd: true,
    title: false,
    baseNavigator: true,
    baseSeparator: '-',
  },
  outputPath: 'dist', // build打包生成product时候输出的目录
  publicPath: process.env.NODE_ENV === 'development' ? '/' : '/', // public文件打包的时候的前缀
  define: {
    'process.env.REQUEST_PREFIX': '',
  },
  // 用于权限控制 需要preset-react >= 2.1.0
  access: {
    strictMode: true,
  },
  lessLoader: {
    // golbalVars: {
    //   'root-entry-name': 'default'
    // }
    modifyVars: {
      'root-entry-name': 'default',
    },
  },
  terserOptions: {
    compress: {
      drop_console: true,
      drop_debugger: true,
      pure_funcs: ['console.log', 'console.table'],
    },
  },
  extraBabelPlugins: [
    '@babel/plugin-proposal-optional-chaining', // 支持可选链操作符 ?.
    '@babel/plugin-proposal-nullish-coalescing-operator', // 支持空值合并操作符 ??
    [
      'import',
      {
        libraryName: 'lodash',
        libraryDirectory: '',
        camel2DashComponentName: false,
      },
      'import-lodash',
    ],
    [
      'import',
      { libraryName: '@umijs/hooks', camel2DashComponentName: false },
      'import-@umijs/hooks',
    ],
    [
      'import',
      {
        libraryName: '@ant-design/icons',
        libraryDirectory: '',
        camel2DashComponentName: false,
      },
      'import-@ant-design/icons',
    ],
  ],
  extraBabelIncludes: [
    '@tanstack/react-virtual',
    '@tanstack/virtual-core', // 如果需要编译第三方库
    'react-draggable',
    'react-grid-layout',
  ],
  plugins: [
    require.resolve('./packages/commons/src/plugins/corejs.ts'),
    require.resolve('./packages/commons/src/plugins/inject-env.ts'),
    require.resolve('./packages/commons/src/plugins/dependencies-version.ts'),
  ],
  externals: {},
  // dynamicImport: {},
  devServer: {
    port: 8001,
  },
  routes: [
    {
      path: '/',
      component: '@/layouts/init',
      routes: [
        {
          path: '/error',
          component: '@/pages/404.tsx',
        },
        {
          exact: true,
          path: '/login',
          component: '@/pages/login/index',
        },
        // sso
        {
          exact: true,
          path: '/sso',
          component: '@/pages/singleSignOn/index',
        },
        {
          exact: true,
          path: '/_health',
          component: '@/pages/_health/index',
        },
        {
          exact: true,
          path: '/docs',
          component: '@/pages/docs/index',
        },
        {
          path: '/external',
          exact: true,
          microApp: 'external',
          microAppProps: {
            autoSetLoading: true,
          },
        },
        {
          exact: true,
          path: '/',
          redirect: '/main',
          wrappers: ['@/wrappers/auth'],
        },
        {
          exact: true,
          path: '/main',
          component: '@/pages/main/index',
          wrappers: ['@/wrappers/auth'],
        },

        {
          path: '/*',
          component: '@/layouts/index',
          wrappers: ['@/wrappers/auth'],
          routes: [
            // 系统设置
            ...configurationRoutes,
            ...escalateRoutes,
            ...institutionRoutes,
            ...insurConfigurationRoutes,
            // 子系统
            {
              path: '/dmr/examine',
              exact: true,
              microApp: 'quality-examine',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                // '/management': 'settlementAnalysis',
                // '/statistic': 'settlementAnalysis'
              },
            },
            {
              path: '/dmr',
              exact: true,
              microApp: 'dmrIndex',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                // '/management': 'settlementAnalysis',
                // '/statistic': 'settlementAnalysis'
              },
            },
            {
              path: '/chs',
              exact: true,
              microApp: 'insurance-settlement',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                // DRG页面
                '/analysis/pre/index': 'settlementRequiredAnalysis', // 'settlementAnalysis',
                '/analysis/pre/hosp': 'settlementRequiredAnalysis',
                '/analysis/pre/dept': 'settlementRequiredAnalysis',
                '/analysis/pre/disease': 'settlementRequiredAnalysis',
                '/analysis/pre/fee': 'settlementRequiredAnalysis',

                '/analysis/drg/hosp': 'settlementRequiredWithInsurTypeAnalysis',
                '/analysis/drg/majorPerfDept':
                  'majorPerfDeptSettlementRequiredWithInsurTypeAnalysis',
                '/analysis/drg/dept':
                  'deptSettlementRequiredWithInsurTypeAnalysis',
                '/analysis/drg/medTeam':
                  'medTeamSettlementRequiredWithInsurTypeAnalysis',
                '/analysis/drg/group':
                  'settlementRequiredWithInsurTypeAnalysis',
                '/analysis/drg/pay':
                  'settlementRequiredWithDeptAndInsurTypeAnalysis',

                // '/analysis/drg/payDetail': 'settlementRequiredWithInsurTypeAnalysis',

                '/analysis/important/variableConditions':
                  'settlementRequiredWithInsurTypeAnalysis',

                '/analysis/warning/hosp': 'drgHospOnly',
                '/analysis/warning/dept': 'deptOnly',

                '/analysis/important/hosp':
                  'settlementRequiredWithWardCoderAnalysis',
                '/analysis/important/majorPerfDept':
                  'settlementMarjorPerfDeptRequiredWithWardCoderAnalysis',
                '/analysis/important/dept':
                  'settlementDeptRequiredWithWardCoderAnalysis',
                '/analysis/important/medTeam':
                  'settleMedTeamRequiredWithWardCoderAnalysis',

                // DIP页面
                '/dip/analysis/hosp': 'settlementRequiredWithInsurTypeAnalysis',
                '/dip/analysis/majorPerfDept':
                  'majorPerfDeptSettlementRequiredWithInsurTypeAnalysis',
                '/dip/analysis/dept':
                  'deptSettlementRequiredWithInsurTypeAnalysis',
                '/dip/analysis/group':
                  'settlementRequiredWithInsurTypeAnalysis',
                '/dip/analysis/medTeam':
                  'medTeamSettlementRequiredWithInsurTypeAnalysis',
                '/dip/analysis/pay': 'settlementRequiredWithInsurTypeAnalysis',
                // DIP重点监控
                '/dip/important/hosp':
                  'settlementRequiredWithWardCoderAnalysis',
                '/dip/important/majorPerfDept':
                  'settlementMarjorPerfDeptRequiredWithWardCoderAnalysis',
                '/dip/important/dept':
                  'settlementDeptRequiredWithWardCoderAnalysis',
                '/dip/important/medTeam':
                  'settleMedTeamRequiredWithWardCoderAnalysis',
                '/dip/important/variableConditions':
                  'settlementRequiredWithInsurTypeAnalysis',
                // DIP 在院
                '/dip/warning/hosp': 'drgHospOnly', // 普通dip drgHospOnly 荆州中医 hospOnly
                '/dip/warning/dept': 'deptOnly',
                // DIP 分组明细
                '/dip/exportDetail': 'dipGroupingDetails',
                // 结算清单
                // '/main/management': 'settlementAnalysisManagement',
                '/main/monitor': 'settlementAnalysis',
                '/main/settleInfo': 'settleInfo',
                '/main/match': 'deptChsSettlementAnalysis',
                '/main/qcAnalysis/hosp': 'settlementAnalysis',
                '/main/qcAnalysis/dept': 'deptSettlementAnalysis',
                '/main/details/dept': 'deptSettlementAnalysis',

                '/main/encode/diff': 'deptSettlementAnalysis',
                '/main/encode/details': 'diffDetailsSettlementAnalysis',

                '/main/specialDisease/reportStats':
                  'specialDiseaseImportRecordAnalysis',

                '/main/specialDisease/importRecord':
                  'specialDiseaseImportRecordAnalysis',

                '/analysis/specialDisease/reportStats':
                  'specialDiseaseSettlementAnalysis',
              },
            },
            {
              path: '/qualityControl',
              exact: true,
              microApp: 'quality-control',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/main/analysis/hosp': 'settlementAnalysisWithErrorLevel',
                '/main/analysis/dept': 'deptSettlementAnalysis',
                '/main/analysis/match': 'settlementAnalysis',
                '/main/efficiency/codeValueQuantification':
                  'deptSettlementAnalysisWithPerfDepts',
                // 'settlementAnalysis',
                // 'deptSettlementAnalysis',
                '/main/efficiency/codeWorkerLoad': 'settlementCoderWorker',
                // '/details/dept': 'deptSettlementAnalysis',
                // '/details/casebook': '',

                '/doctor/analysis/hosp':
                  'doctorQualityControlHospSettlementAnalysis',
                '/doctor/analysis/dept':
                  'doctorQualityControlDeptSettlementAnalysis',
              },
            },
            // 能效管理
            {
              path: '/energyEfficientManagement',
              exact: true,
              microApp: 'energy-efficiency-management',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/codeValueQuantification': 'settlementAnalysis',
                '/codeWorkerLoad': 'settlementCoderWorker',
              },
            },
            {
              path: '/uniHqms',
              exact: true,
              microApp: 'hqms',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/score': 'settlementAnalysis',
                '/oper/hosp': 'settlementAnalysis',
                '/oper/dept': 'deptSettlementAnalysis',
                '/oper/medTeam': 'medTeamSettlementAnalysis',
                '/quality/hosp': 'settlementAnalysis',
                '/quality/dept': 'deptSettlementAnalysis',
                '/quality/medTeam': 'medTeamSettlementAnalysis',
                '/operComplicationComposition/hosp': 'settlementAnalysis',
                '/operComplicationComposition/dept': 'deptSettlementAnalysis',
                '/operComplicationComposition/medTeam':
                  'medTeamSettlementAnalysis',
                '/sd/hosp': 'settlementAnalysis',
                '/sd/dept': 'deptSettlementAnalysis',
                '/sd/medTeam': 'medTeamSettlementAnalysis',
                '/tcm/hosp': 'settlementAnalysis',
                '/tcm/dept': 'deptSettlementAnalysis',
                '/tcm/medTeam': 'medTeamSettlementAnalysis',
                '/cmi/hospLevelCmi': 'settlementAnalysis',
                '/cmi/deptLevelCmi': 'deptSettlementAnalysis',
                '/cmi/medTeamLevelCmi': 'medTeamSettlementAnalysis',
              },
            },

            {
              path: '/grade',
              exact: true,
              microApp: 'grade',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/quality/hosp': 'settlementAnalysis',
                '/quality/dept': 'deptSettlementAnalysis',
                '/quality/medTeam': 'medTeamSettlementAnalysis',
                '/sd/hosp': 'settlementAnalysis',
                '/sd/dept': 'deptSettlementAnalysis',
                '/sd/medTeam': 'medTeamSettlementAnalysis',
                '/serviceability/hosp': 'settlementAnalysis',
                '/serviceability/dept': 'deptSettlementAnalysis',
                '/serviceability/medTeam': 'medTeamSettlementAnalysis',
                '/complication/hosp': 'settlementAnalysis',
                '/complication/dept': 'deptSettlementAnalysis',
                '/complication/medTeam': 'medTeamSettlementAnalysis',
              },
            },

            {
              path: '/operationalDataManagement',
              exact: true,
              microApp: 'operational-data-management',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/in/byDay': 'ODM_inpatient',
                '/in/byDayWithWard': 'ODM_inpatient',
                '/in/byDept': 'ODM_dept',
                '/in/hierarchyBed': 'ODM_hierarchyBed',
                '/in/proofread': 'ODM_proofread_dynamic',
                '/in/dailyProofread': 'ODM_dailyProofread',
                '/inHospManagement': 'ODM_inHospManagement',
                '/outPatientManagement': 'ODM_inHospManagement',
                '/out/byDay': 'ODM_inpatient',
                '/out/byDept': 'ODM_opt_dept',
                '/outDoctor/byDay': 'ODM_inpatient',
                '/outDoctor/byDept': 'ODM_opt_dept',
                '/obs/byDay': 'ODM_inpatient',
                '/obs/byDept': 'ODM_obs_dept',
                '/obs/hierarchyBed': 'ODM_hierarchyBed_obs',
                // '/inpatient': 'ODM_inpatient',
                // '/obspatient': 'ODM_common',
                // '/outpatient': 'ODM_common',
                '/workLoad/hospWorkloadItemCheck': 'ODM_workloadItem',
                '/hierarchyBed': 'ODM_hierarchyBed',

                '/hospEmployee': 'ODM_hospEmployee',
              },
            },

            // 医保基金监控
            {
              path: '/medicalInsuranceFundMonitor',
              exact: true,
              microApp: 'medical-insurance-fund-monitor',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/appeal': 'deptSettlementAnalysis',
                '/review/dashboard/prior': 'radioDatePickerTest',
              },
            },

            {
              path: '/tracer',
              exact: true,
              microApp: 'tracer',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/traceRecordList': {
                  defaultKey: 'tracer_traceRecord',
                  url: 'Api/Mr/TraceRecord/GetList',
                },

                '/ward/search': {
                  defaultKey: 'tracer_wardSignined',
                  url: 'Api/Mr/TraceRecord/WardSignedOutList',
                },

                '/mrRoom/search': {
                  defaultKey: 'tracer_mrRoomSignIned',
                  url: 'Api/Mr/TraceRecord/MrRoomSignedInList',
                },
                '/mrRoom/statistic/bySignDate': {
                  defaultKey: 'tracer_signInStatisticsBySignInDate',
                  url: 'Api/Mr/TraceRecord/GetSignInCntBySignInDate',
                },
                '/mrRoom/statistic/byOutDate': {
                  defaultKey: 'tracer_signInStatisticsByOutDate',
                  url: 'Api/Mr/TraceRecord/GetSignInCntByOutDate',
                },
                // 复印 查询
                '/print/search': {
                  defaultKey: 'tracer_printTraceRecord',
                  url: 'Api/Mr/PrintRecord/GetList',
                },
                // 病案室出库
                '/dmrSignOut/search': {
                  defaultKey: 'tracer_dmrSignOut',
                  url: 'Api/Mr/TraceRecord/DmrSignedOutList',
                },
                '/archive/search': {
                  defaultKey: 'tracer_archived',
                  url: 'Api/Mr/TraceRecord/WarehouseSignedInList',
                },
                '/seal/search': 'tracer_sealed',
                // '/seal/register': 'tracer_sealing',
                '/borrow/search': {
                  defaultKey: 'tracer_borrowed',
                  url: 'Api/Mr/BorrowRecord/GetReturnList',
                },
                '/borrow/askForReturnSearch': {
                  defaultKey: 'tracer_askForReturn',
                  url: 'Api/Mr/BorrowingManagement/GetAlerts',
                },
                // '/remind/search': 'tracer_reminded',
                '/askForPaySearch': {
                  defaultKey: 'tracer_askForPay',
                  url: 'Api/Mr/BorrowingManagement/GetAlerts',
                },
                '/mrRoomSignInAnalysis': {
                  defaultKey: 'tracer_anal',
                  url: 'Api/Mr/Stats/MrRoomSignInStats',
                },
                '/borrow/borrowTasks': 'tracer_borrow_tasks',
                '/borrow/userBorrowTasks': 'tracer_user_borrow_tasks',
              },
            },

            {
              path: '/report',
              exact: true,
              microApp: 'report',
              wrappers: ['@/layouts/base-layout'],
              microAppProps: {
                autoSetLoading: true,
              },
            },

            {
              path: '/statsAnalysis',
              exact: true,
              microApp: 'stats-analysis',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/surgerySequence': 'surgerySequence',
                '/diseaseSequence': 'diseaseSequence',
                // '/customDiseaseStats': 'deptSettlementAnalysis',
              },
            },
            // DRG数据管理
            {
              path: '/drgDataManager',
              exact: true,
              microApp: 'drg-data-manager',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/hqmsDataManagement': 'hqmsDataManagement',
                '/maintenanceDataCnt': 'settlementAnalysis',
                '/maintenanceRecordDataCnt': 'settlementAnalysis',
              },
            },
            // DRG医院决策支持
            {
              path: '/drgHospDecisionSupport',
              exact: true,
              microApp: 'drg-hosp-decision-support',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/hqmsDataManagement': 'hqmsDataManagement',
                '/hospLevel/Cmi': 'settlementAnalysis',
                '/hospLevel/diseaseType': 'ADrgComposition',
                '/hospLevel/groupType': 'settlementAnalysis',
                '/hospLevel/operRate': 'settlementAnalysis',
                '/hospLevel/SdComposition': 'settlementAnalysis',
                '/deptLevel/Cmi': 'deptSettlementAnalysis',
                '/deptLevel/diseaseType': 'deptSettlementAnalysis',
                '/deptLevel/groupType': 'deptSettlementAnalysis',
                '/deptLevel/operRate': 'deptSettlementAnalysis',
                '/deptLevel/SdComposition': 'deptSettlementAnalysis',
                '/medTeamLevel/Cmi': 'medTeamSettlementAnalysis',
                // '/majoePerfDept/Cmi': 'DRG_MajorPerfDept',
                '/majoePerfDept/Cmi': 'majorPerfDeptSettlementAnalysis',
                '/medTeamLevel/diseaseType': 'medTeamSettlementAnalysis',
                '/medTeamLevel/groupType': 'medTeamSettlementAnalysis',
                '/detail/hospDrgs': 'drgHospDrg',
                '/detail/hospOper': 'drgHospOper',
                '/detail/hospSd': 'drgHospSd',

                // 疑难
                '/difficultCases/hosp': 'settlementAnalysis',
                '/difficultCases/dept': 'deptSettlementAnalysis',
                '/difficultCases/medTeam': 'medTeamSettlementAnalysis',
                // 外科
                '/surgicalAbility/hosp': 'settlementAnalysis',
                '/surgicalAbility/dept': 'deptSettlementAnalysis',
                '/surgicalAbility/medTeam': 'medTeamSettlementAnalysis',
                // 重点监控
                '/sdComposition/hosp': 'settlementAnalysis',
                '/sdComposition/dept': 'deptSettlementAnalysis',
                '/sdComposition/medTeam': 'medTeamSettlementAnalysis',
                // 医疗质量
                '/medicalQuality/hosp': 'settlementAnalysis',
                '/medicalQuality/dept': 'deptSettlementAnalysis',
                '/medicalQuality/medTeam': 'medTeamSettlementAnalysis',
              },
            },

            {
              path: '/import',
              exact: true,
              microApp: 'info-input',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
            },
            {
              exact: true,
              path: '/reportSys/_health',
              component: '@/pages/_health/index',
            },
            {
              path: '/reportSys',
              exact: true,
              microApp: 'report-sys',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                '/dataManagement': 'settlementAnalysis',
                '/dataFlow': 'settlementAnalysis',
                // '/UserDataCnt': 'settlementAnalysis',
                // 已迁移 20250625
                // '/MaintenanceDataCnt': 'settlementAnalysis',
                // '/MaintenanceRecordDataCnt': 'settlementAnalysis',
                '/insurDataFlow': 'settlementAnalysis',
                '/externalCalcRecord': 'externalCalcRecord',
              },
            },

            {
              path: '/wiki',
              exact: true,
              microApp: 'wiki',
              microAppProps: {
                autoSetLoading: true,
              },
              wrappers: ['@/layouts/base-layout'],
              headerKeys: {
                // '/insur/index': 'wikiSettlement',
                // '/insur/ccMcc': 'wikiSettlement',
                // '/insur/icde': 'wikiSettlement',
                // '/insur/oper': 'wikiSettlement',
              },
            },

            {
              exact: true,
              path: '/personalMessages/index',
              component: '@/pages/message/index',
              wrappers: ['@/layouts/base-layout'],
            },
          ],
        },
      ],
    },
  ],

  // qiankun
  qiankun: {
    master: {
      // 注册子应用信息
      apps: apps.filter((item) => item.bundle === true),
    },
  },

  proxy: {
    // 同cra的setupProxy,代理中转实现dev版本的跨域
    '/common': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/common': '' },
      secure: false, // https的dev后端的话需要配
    },

    // cqj dev
    '/dynDdr': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/dynDdr': '' },
      secure: false, // https的dev后端的话需要配
    },

    // hyp dev
    '/mr': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/mr': '' },
      secure: false, // https的dev后端的话需要配
    },

    '/metaData': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/metaData': '' },
      secure: false, // https的dev后端的话需要配
    },

    '/auth': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/auth': '' },
      secure: false, // https的dev后端的话需要配
    },

    '/hqms': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/hqms': '' },
    },
    '/hqmsAddress': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/hqmsAddress': '' },
      secure: false, // https的dev后端的话需要配
    },
    '/gradeAddress': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/gradeAddress': '' },
      secure: false, // https的dev后端的话需要配
    },

    '/insurAddress': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/insurAddress': '' },
      secure: false, // https的dev后端的话需要配
    },

    '/combinedQuery': {
      target: 'http://************:5181',
      changeOrigin: true,
      pathRewrite: { '^/combinedQuery': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
});
