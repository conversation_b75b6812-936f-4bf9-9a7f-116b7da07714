import { Utils } from '@react-awesome-query-builder/antd';
import {
  getFieldPathLabels,
  truncateString,
} from '@/pages/combine-query/utils';
import last from 'lodash/last';
import omit from 'lodash/omit';
import groupBy from 'lodash/groupBy';
import { List } from 'immutable';
import { isEmptyValues } from '@uni/utils/src/utils';
import { numberTypes } from '@/pages/combine-query/processor';
import { linkDataSource } from '@/pages/combine-query/combo-table/columns';

export const getFieldLabel = (fieldOpts, fieldKey, config) => {
  if (!fieldKey) return null;
  let fieldSeparator = config.settings.fieldSeparator;
  let maxLabelsLength = config.settings.maxLabelsLength;
  let fieldParts = Array.isArray(fieldKey)
    ? fieldKey
    : fieldKey.split(fieldSeparator);
  let label = (fieldOpts && fieldOpts.label) || last(fieldParts);
  label = truncateString(label, maxLabelsLength);
  return label;
};

export const buildOptions = (
  config,
  fields,
  path = null,
  optGroupLabel = null,
) => {
  if (!fields) return null;
  const { fieldSeparator, fieldSeparatorDisplay } = config.settings;
  const prefix = path ? path.join(fieldSeparator) + fieldSeparator : '';

  return Object.keys(fields)
    .map((fieldKey) => {
      const field = fields[fieldKey];
      const label = getFieldLabel(field, fieldKey, config);
      const partsLabels = getFieldPathLabels(prefix + fieldKey, config);
      let fullLabel = partsLabels.join(fieldSeparatorDisplay);
      const altLabel = field.label2;
      const tooltip = field.tooltip;
      const subpath = (path ? path : []).concat(fieldKey);
      const disabled = field.disabled;

      if (field.hideForSelect) return undefined;

      if (field.type == '!struct') {
        return {
          disabled,
          key: fieldKey,
          path: prefix + fieldKey,
          label,
          fullLabel,
          altLabel,
          tooltip,
          items: buildOptions(config, field.subfields, subpath, label),
          extra: omit(field, ['subfields']),
        };
      } else {
        return {
          disabled,
          key: fieldKey,
          path: prefix + fieldKey,
          label,
          fullLabel,
          altLabel,
          tooltip,
          grouplabel: optGroupLabel,
          extra: field,
        };
      }
    })
    .filter((o) => !!o);
};

export const findItemBySelectedPaths = (selectedPath, currentData) => {
  let selectedItem = undefined;

  let fields = currentData?.slice();

  selectedPath
    ?.join('.')
    ?.split('.')
    ?.forEach((key, index) => {
      let selectItem = fields?.find((item) => {
        return (item?.actualKey ?? item?.key) === key;
      });

      console.log('selectItem', selectItem);

      if (selectItem) {
        if (index === selectedPath?.join('.')?.split('.')?.length - 1) {
          selectedItem = selectItem?.extra ?? undefined;
        } else {
          fields = selectItem?.items ?? [];
        }
      }
    });

  return selectedItem;
};

export const searchRecordParentFieldByGroupId = (
  formValues: any,
  groupId: string,
) => {
  let groupParentField = undefined;

  for (let key in formValues) {
    let formItem = formValues[key];
    if (formItem?.groupId === groupId) {
      if (formItem?.parentField) {
        groupParentField = formItem?.parentField;
        break;
      }
    }
  }

  return groupParentField;
};

// 新加一个参数 dictData 用于"查看语法"时的翻译
export const generateQueryFromTable = (formValues, config, dictData = null) => {
  let dataOrder = formValues?.['dataOrder'];

  let groupIdOrder = {};

  let dataSets = [];
  for (let key in omit(formValues, ['dataOrder'])) {
    if (formValues[key]?.['expr']) {
      groupIdOrder[formValues[key]?.groupId] = Math.min(
        groupIdOrder[formValues[key]?.groupId] ?? Number.MAX_SAFE_INTEGER,
        dataOrder[key],
      );

      dataSets.push({
        ...formValues[key],
        order: dataOrder[key],
        groupOrder: groupIdOrder[formValues[key]?.groupId],
      });
    }
  }

  let groupByResult = groupBy(dataSets, 'groupId');

  console.log('groupByResult', groupByResult, groupIdOrder);

  let groupedByDataSets = [];
  for (let groupId in groupByResult) {
    groupedByDataSets.push({
      order: groupIdOrder[groupId],
      conditions: groupByResult[groupId],
      parentField: groupByResult[groupId]?.at(0)?.parentField ?? undefined,
    });
  }
  console.log('groupedByDataSets', groupedByDataSets, config, dictData);
  let queries = [];
  groupedByDataSets
    .sort((a, b) => a?.order - b?.order)
    ?.forEach((tableItem) => {
      // 左括号
      if (tableItem?.conditions?.at(0)?.['left-bracket']) {
        queries.push(tableItem?.conditions?.at(0)?.['left-bracket']);
      }

      if (tableItem?.parentField?.extra?.type === '!group') {
        // 表示为some 存在于parentField.extra.type ==='!group'
        queries.push(
          buildSomeQuery(
            tableItem?.conditions,
            tableItem?.parentField,
            dictData,
          ),
        );
      } else if (tableItem?.conditions?.length === 1) {
        // 表示为单层
        queries.push(
          buildSingleQuery(tableItem?.conditions?.at(0), config, dictData),
        );
      }

      // 右括号
      if (tableItem?.conditions?.at(0)?.['right-bracket']) {
        queries.push(tableItem?.conditions?.at(0)?.['right-bracket']);
      }
      // 连接符
      if (tableItem?.conditions?.at(0)?.['link']) {
        queries.push(tableItem?.conditions?.at(0)?.['link']);
      }
    });

  ignoreLastOperator(queries);

  console.log('dataSets', dataSets, queries, config);

  return queries?.join(' ');
};

const buildSingleQuery = (
  condition: any,
  config: any,
  dictData: any = null,
) => {
  let fieldDef = {
    asyncListValues: undefined,
    fieldSettings: {},
  };
  let wgtDef = config?.widgets?.[condition?.['dataType']];
  let opDef = config?.operators?.[condition['operator']];

  let itemArrays = [];
  if (condition['not'] === true) {
    itemArrays.push('not');
  }

  // 列名称
  itemArrays.push(condition['expr']);
  // 操作符号
  itemArrays.push(condition['operator']);

  if (!isEmptyValues(condition?.['columnValue'])) {
    if (numberTypes?.includes(condition['dataType'])) {
      if (Array.isArray(condition?.['columnValue'])) {
        let columnValue = condition?.['columnValue']?.map((item) => {
          if (typeof item === 'string') {
            return parseFloat(item);
          }

          return item;
        });
        itemArrays.push(JSON.stringify(columnValue));
      } else {
        let columnValue =
          typeof condition?.['columnValue'] === 'string'
            ? parseFloat(condition?.['columnValue'])
            : condition?.['columnValue'];
        itemArrays.push(columnValue);
      }
    } else {
      // 这边判断要不要翻译
      if (dictData) {
        let value = condition?.['columnValue'];
        let dictModule = condition['extra']?.dictionaryModule;
        let dictModuleGroup = condition['extra']?.dictionaryModuleGroup;
        let dict = undefined;
        if (dictModule) {
          if (dictModuleGroup) {
            dict = dictData?.[dictModuleGroup]?.[dictModule];
          } else {
            dict = dictData?.[dictModule];
          }
        }
        if (dict) {
          let tranValue = value?.map((d) => {
            return dict?.find((v) => v.Code === d)?.Name;
          });
          itemArrays.push(JSON.stringify(tranValue));
        } else {
          itemArrays.push(JSON.stringify(condition?.['columnValue']));
        }
      } else {
        itemArrays.push(JSON.stringify(condition?.['columnValue']));
      }
    }
  }

  console.log('itemArrays', itemArrays);

  return itemArrays?.join(' ');
};

const buildSomeQuery = (
  conditions: any,
  parentField: any,
  config: any,
  dictData: any = null,
) => {
  let totalFields = config?.fields;

  let itemArrays = [];

  if (conditions?.some((item) => item?.not === true)) {
    itemArrays.push('not');
  }

  itemArrays.push('some');
  itemArrays.push(parentField?.extra?.expr);

  let conditionItemArrays = [];
  conditionItemArrays.push('(');
  conditions?.forEach((conditionItem, index) => {
    // 列名称
    conditionItemArrays.push(conditionItem['expr']);
    // 操作符号
    conditionItemArrays.push(conditionItem['operator']);
    // 值
    // if (!isEmptyValues(conditionItem['columnValue'])) {
    //   if (numberTypes?.includes(conditionItem['dataType'])) {
    //     conditionItemArrays.push(conditionItem['columnValue']);
    //   } else {
    //     conditionItemArrays.push(JSON.stringify(conditionItem['columnValue']));
    //   }
    // }
    if (!isEmptyValues(conditionItem?.['columnValue'])) {
      if (numberTypes?.includes(conditionItem['dataType'])) {
        if (Array.isArray(conditionItem?.['columnValue'])) {
          let columnValue = conditionItem?.['columnValue']?.map((item) => {
            if (typeof item === 'string') {
              return parseFloat(item);
            }

            return item;
          });
          conditionItemArrays.push(JSON.stringify(columnValue));
        } else {
          let columnValue =
            typeof conditionItem?.['columnValue'] === 'string'
              ? parseFloat(conditionItem?.['columnValue'])
              : conditionItem?.['columnValue'];
          conditionItemArrays.push(columnValue);
        }
      } else {
        // 这边判断要不要翻译
        if (dictData) {
          let value = conditionItem?.['columnValue'];
          let dictModule = conditionItem?.['extra']?.dictionaryModule;
          let dictModuleGroup = conditionItem?.['extra']?.dictionaryModuleGroup;
          let dict = undefined;
          if (dictModule) {
            if (dictModuleGroup) {
              dict = dictData?.[dictModuleGroup]?.[dictModule];
            } else {
              dict = dictData?.[dictModule];
            }
          }
          if (dict) {
            let tranValue = value?.map((d) => {
              return dict?.find((v) => v.Code === d)?.Name;
            });
            conditionItemArrays.push(JSON.stringify(tranValue));
          } else {
            conditionItemArrays.push(
              JSON.stringify(conditionItem?.['columnValue']),
            );
          }
        } else {
          conditionItemArrays.push(
            JSON.stringify(conditionItem?.['columnValue']),
          );
        }
      }
    }

    if (index !== conditions?.length - 1) {
      conditionItemArrays.push('and');
    }
  });
  conditionItemArrays.push(')');
  console.log('conditionItemArrays', conditionItemArrays);
  itemArrays.push(conditionItemArrays?.join(' '));

  return itemArrays?.join(' ');
};

export const valueFormatter = (arrays: any[]) => {
  for (let index = 0; index < arrays.length; index++) {
    let item = arrays[index];

    if (typeof item === 'string') {
      return JSON.stringify(item);
    }

    // 数组的话
    if (Array.isArray(item)) {
      if (item?.length === 0) {
        continue;
      }

      return item?.map((valueItem) => {
        return JSON.stringify(valueItem);
      });
    }
  }

  return undefined;
};

export const filterIsCommonItems = (fields: any[]) => {
  let result = [];

  fields?.forEach((item) => {
    if (item?.extra?.IsCommon === true) {
      result.push(item);
    }

    if (item?.items?.length > 0) {
      result.push(...filterIsCommonItems(item?.items));
    }
  });

  return result;
};

export const filterAllItems = (fields: any[]) => {
  let result = [];

  fields?.forEach((item) => {
    if (item?.items?.length > 0) {
      result.push(...filterAllItems(item?.items));
    } else {
      if (item?.extra?.type !== '!struct' && item?.extra?.type !== '!group') {
        result.push(item);
      }
    }
  });

  return result;
};

export const findSelectItemInDataSource = (dataSources: any, value: string) => {
  if (dataSources?.at(0)?.['dataSourceType']) {
    let findResult = undefined;
    for (let groupItem of dataSources) {
      let groupFindResult = groupItem?.options?.find(
        (item) => item?.value === value,
      );
      if (groupFindResult) {
        findResult = groupFindResult;
        break;
      }
    }

    return findResult;
  } else {
    return dataSources?.find((item) => item?.value === value);
  }
};

export const getOperatorMeta = (config: any, value: string, type: any) => {
  let operatorMeta = config?.operators?.[value];

  let extraMeta =
    config?.types?.[type]?.['widgets']?.[type]?.['opProps']?.[value] ?? {};

  operatorMeta = {
    ...operatorMeta,
    ...extraMeta,
    cardinality: extraMeta?.isSpecialRange ? 1 : operatorMeta?.cardinality ?? 1,
  };

  return operatorMeta;
};

export const ignoreLastOperator = (queries: string[]) => {
  let linkValues = linkDataSource?.map((item) => item?.value);
  if (linkValues?.includes(queries?.[queries?.length - 1])) {
    queries.pop();
  }
};
