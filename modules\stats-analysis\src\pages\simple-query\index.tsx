import { RespVO } from '@uni/commons/src/interfaces';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import { useModel, useRequest } from 'umi';
import { IQuerableItem } from './interface';
import QueryableForm from './components/queryable-form';
import ColumnMoreModal from './components/column-more-modal';
import { Button, Space, Tabs, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import './index.less';
import { TotalSimpleQueryItems } from './constants';
import DetailTable from './components/detail-table';
import _ from 'lodash';
import { SettingOutlined } from '@ant-design/icons';
import { DetailColumnSettingContentConstants } from '@/components/queryDetailColumnsSettings/constants';
import { Emitter } from '@uni/utils/src/emitter';
import { isEmptyValues } from '@uni/utils/src/utils';
import { uniCommonService } from '@uni/services/src/commonService';
import { ExportIconBtn } from '@uni/components/src/index';

const TABLENAME = 'OmniCard';

const SimpleQuery = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedItems, setSelectedItems] = useState<IQuerableItem[]>([]);
  const [activeTab, setActiveTab] = useState('Basic');
  const [queryFormItems, setQueryFormItems] = useState(undefined);

  // 给明细table的columns使用的数据 统一的部分

  // 获取搜索时使用的tree
  const {
    data: queryableList,
    loading: getQueryableListLoading,
    run: getQueryableListReq,
  } = useRequest(
    () => {
      const data = {
        TableName: TABLENAME,
      };
      return uniCombineQueryService(
        'Api/Analysis/AnaModelDef/GetQueryableList',
        {
          params: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<IQuerableItem[]>) => {
        console.log('queryableList', response);
        if (response?.code === 0 && response?.statusCode === 200) {
          return response?.data || [];
        }
        return [];
      },
    },
  );

  // 获取全部可用于展示的subject
  const {
    data: retColSubjectList,
    loading: getRetColSubjectLoading,
    run: getRetColSubjectReq,
  } = useRequest(
    () => {
      const data = {
        TableName: TABLENAME,
      };
      return uniCombineQueryService(
        'Api/Analysis/AnaModelDef/GetRetColSubjectList',
        {
          params: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<IQuerableItem[]>) => {
        console.log('getRetColSubjectReq', response);
        if (response?.code === 0 && response?.statusCode === 200) {
          let tableColumns = _.cloneDeep(response?.data || []);
          // 额外设置几个参数再返回
          tableColumns?.forEach((item, index) => {
            item['dataIndex'] = item?.name;
            item['originTitle'] = item?.title;
          });
          return tableColumns;
        }
        return [];
      },
    },
  );

  // Initialize data and selected items on mount
  useEffect(() => {
    getQueryableListReq();
    getRetColSubjectReq();
  }, []);

  // Set initial selected items when queryableList is loaded
  useEffect(() => {
    if (queryableList && queryableList.length > 0) {
      setSelectedItems(queryableList);
    }
  }, [queryableList]);

  // Form values submit hanlder
  // values === filtered form values
  const handleSubmit = (values: any) => {
    console.log('Form values submitted:', values);
    setQueryFormItems(values);
  };

  const items = [
    {
      key: 'Basic',
      label: '基础信息',
      children: (
        <DetailTable
          tabKey={activeTab}
          queryValue={queryFormItems}
          dictData={globalState?.dictData}
          type="Basic"
          retColSubjectList={retColSubjectList}
        />
      ),
    },
    {
      key: 'Icd',
      label: 'ICD信息',
      children: (
        <DetailTable
          tabKey={activeTab}
          queryValue={queryFormItems}
          dictData={globalState?.dictData}
          type="Icd"
          retColSubjectList={retColSubjectList}
        />
      ),
    },
    {
      key: 'Oper',
      label: '手术信息',
      children: (
        <DetailTable
          tabKey={activeTab}
          queryValue={queryFormItems}
          dictData={globalState?.dictData}
          type="Oper"
          retColSubjectList={retColSubjectList}
        />
      ),
    },
  ];

  return (
    <div className="simple-query-container">
      <QueryableForm items={selectedItems} onSubmit={handleSubmit} />
      <Tabs
        activeKey={activeTab}
        type={'card'}
        items={items}
        onChange={(key) => setActiveTab(key)}
      />
    </div>
  );
};

export default SimpleQuery;
