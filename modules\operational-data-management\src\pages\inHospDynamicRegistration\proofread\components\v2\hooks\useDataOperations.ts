import { useDispatch } from 'umi';
import { isRespErr } from '@/utils/widgets';
import dayjs from 'dayjs';
import _ from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { ReqActionType } from '@/Constants';
import {
  IDeptpatientAmtListItem,
  IStatsDmrCardCntByDeptAndDailyListItem,
} from '../interface';
import {
  TableAction,
  EditableTableAction,
} from '@uni/reducers/src/tableReducer';

/**
 * 比较两个数字类型字段的函数
 */
export const compareNumberFields = (
  baseValue: any,
  compareValue: any,
  fieldName: string,
) => {
  // 如果比较值为null或undefined，跳过比较
  if (compareValue === null || compareValue === undefined) {
    return {
      hasDiscrepancy: false,
      displayValue: baseValue,
    };
  }

  // 如果两个值不同，返回带比较值的显示文本
  if (baseValue !== compareValue) {
    return {
      hasDiscrepancy: true,
      displayValue: `${baseValue}（${compareValue}）`,
    };
  }

  // 否则返回原始值
  return {
    hasDiscrepancy: false,
    displayValue: baseValue,
  };
};

/**
 * 数据操作相关hook
 */
// 需要比较的字段列表 - 提取为常量以便复用
const fieldsToCompare = [
  'InHospCnt',
  'TransInCnt',
  'TransOutCnt',
  'TotalOutHospCnt',
  'OutHospCnt',
  'CuredCnt',
  'ImprovedCnt',
  'NotCuredCnt',
  'DeathCnt',
  'OtherCnt1',
  'OtherCnt2',
  'OtherCnt3',
  'OtherCnt4',
  'OtherCnt5',
  'OtherCnt6',
  'OtherCnt7',
  'OtherCnt8',
];

/**
 * 比较单个记录与compareItem，处理差异标记
 * 提取为公共函数，供其他函数调用
 */
const compareRecordWithItem = (baseRecord: any, compareItem: any) => {
  const result = { ...baseRecord, hasDiscrepancy: false };

  if (!compareItem) return result;

  // 比较每个字段
  fieldsToCompare.forEach((field) => {
    const comparison = compareNumberFields(
      baseRecord[field] ?? 0,
      compareItem[field],
      field,
    );

    // null → 0
    result[field] = baseRecord[field] ?? 0;

    // 如果compareItem[field]有值且有差异，记录比较值
    if (
      compareItem[field] !== null &&
      compareItem[field] !== undefined &&
      comparison.hasDiscrepancy
    ) {
      result[`${field}_compare`] = compareItem[field];
      result.hasDiscrepancy = true;
      result[`${field}_hasDiscrepancy`] = true;
    }
  });

  return result;
};

export const useDataOperations = ({
  TableDispatch,
  EditableDispatch,
  tableParams,
  dictData,
  loadData,
  dataSource,
  cachedCompareData,
  nowEditedRecord, // 添加nowEditedRecord参数
  clearEditedRecord,
}) => {
  const dispatch = useDispatch();

  // 处理单条记录与compareData的比较和合并
  const mergeAndCompareSingleRecord = (
    baseRecord: IDeptpatientAmtListItem,
    compareData: IStatsDmrCardCntByDeptAndDailyListItem[],
  ) => {
    // 查找对应的比较数据
    const compareItem = compareData?.find(
      (item) =>
        item.HospCode === baseRecord.HospCode &&
        item.DeptCode === baseRecord.DeptCode &&
        dayjs(item.ExactDate).format('YYYY-MM-DD') ===
          dayjs(baseRecord.ExactDate).format('YYYY-MM-DD'),
    );

    // 使用公共函数处理对比
    return compareRecordWithItem(baseRecord, compareItem);
  };

  // 通用操作请求
  const reqActionReq = async (
    reqData: any,
    reqType: ReqActionType,
    reqId = undefined,
    skipLoading = false,
    originalRecordKey?: string, // 添加原始记录的key参数
  ) => {
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `DeptInpatientAmt/${reqType}`,
        requestParams: {
          url:
            reqType === ReqActionType.Update && reqId
              ? `Api/Dyn-ddr/DeptInpatientAmt/${reqType}/${reqId.id}`
              : `Api/Dyn-ddr/DeptInpatientAmt/${reqType}`,
          method: 'POST',
          params: reqType === ReqActionType.Update && reqId ? reqId : undefined,
          data: reqData,
          dataType: 'dyn-ddr',
          requestType: reqType === ReqActionType.Import ? 'form' : 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      if (
        reqType === ReqActionType.Update ||
        reqType === ReqActionType.Create
      ) {
        // 关闭编辑状态
        if (!skipLoading) {
          EditableDispatch({
            type: TableAction.editableKeysChange,
            payload: {
              editableKeys: [],
            },
          });
        }

        // 处理更新后的数据
        if (
          res.data &&
          (reqType === ReqActionType.Update || reqType === ReqActionType.Create)
        ) {
          const updatedRecord = res.data;

          // 如果是Create操作，使用传入的originalRecordKey精确定位临时记录
          if (reqType === ReqActionType.Create && originalRecordKey) {
            // 处理更新的记录与compareData比较【1个日期内，1个科室的数据是唯一的】
            const processedRecord = mergeAndCompareSingleRecord(
              updatedRecord,
              cachedCompareData,
            );

            // 更新单条记录，注意这里使用editableReplaceByCustomKey
            EditableDispatch({
              type: EditableTableAction.editableReplaceByCustomKey,
              payload: {
                keyField: 'Id',
                keyValue: originalRecordKey,
                newRecord: processedRecord,
              },
            });

            // message.success('新增成功');
          }
          // 如果是Update操作，直接更新对应ID的记录
          else if (reqType === ReqActionType.Update) {
            // 处理更新的记录与compareData比较
            const processedRecord = mergeAndCompareSingleRecord(
              updatedRecord,
              cachedCompareData,
            );

            // 更新单条记录 editableSingleRecordChange
            EditableDispatch({
              type: EditableTableAction.editableSingleRecordChange,
              payload: {
                key: 'Id',
                value: processedRecord,
              },
            });

            // message.success('更新成功');
          }
        } else {
          // 如果没有返回数据或其他情况，回退到完整重新加载
          loadData(tableParams, skipLoading);
        }
      } else {
        // 其他操作类型，保持原有行为，重新加载所有数据
        loadData(tableParams);
      }
      return true;
    } else if (reqType === ReqActionType.Update) {
      return false;
    }
  };

  // 编辑保存处理
  const editSaveHandler = async (
    recordKey, // 当前record Id
    data, // form values
    needAutoFetch = false,
    skipLoading = false,
  ) => {
    // 处理数据优先级：用户输入 > 自动计算 > 原始数据
    if (recordKey?.toString()?.includes('new')) {
      // new 的情况下 没有originalData
      await reqActionReq(
        {
          ...(nowEditedRecord.current || {}), // 自动计算返回的数据
          ..._.omit(data?.[recordKey?.toString()] ?? data, 'Id'),
          ExactDate: dayjs(
            dataSource?.find((d) => d?.Id === recordKey)?.ExactDate,
          )?.format('YYYY-MM-DD'),
        },
        ReqActionType.Create,
        undefined,
        skipLoading,
        recordKey, // 传入原始recordKey以便精确定位要更新的记录
      );
    } else {
      // 编辑情况
      await reqActionReq(
        {
          ...dataSource?.find(
            (d) => d?.Id?.toString() === recordKey?.toString(),
          ),
          ...(nowEditedRecord.current || {}), // 自动计算返回的数据（优先级次之）
          ...(data?.[recordKey?.toString()] ?? data), // 当前编辑数据（优先级最高）
        },
        ReqActionType.Update,
        {
          id: recordKey?.toString(),
        },
        skipLoading,
      );
    }

    if (needAutoFetch) {
      loadData(tableParams);
    }

    // 清空自动计算的数据，避免影响下次操作
    clearEditedRecord();
  };

  // 合并和比较两个数据源
  const mergeAndCompareData = (
    baseData: IDeptpatientAmtListItem[],
    compareData: IStatsDmrCardCntByDeptAndDailyListItem[],
  ) => {
    // 使用全局定义的fieldsToCompare

    // 创建一个Map来跟踪已处理的compareData项
    const processedCompareItems = new Map();

    // 用于存储所有结果的数组
    const results = [];

    // 处理baseData中的所有项
    baseData.forEach((baseItem) => {
      const baseItemKey = `${baseItem.HospCode}_${baseItem.DeptCode}_${dayjs(
        baseItem.ExactDate,
      ).format('YYYY-MM-DD')}`;

      // 查找对应的比较数据
      const compareItem = compareData.find(
        (item) =>
          item.HospCode === baseItem.HospCode &&
          item.DeptCode === baseItem.DeptCode &&
          dayjs(item.ExactDate).format('YYYY-MM-DD') ===
            dayjs(baseItem.ExactDate).format('YYYY-MM-DD'),
      );

      if (compareItem) {
        // 标记此compareItem已处理
        const compareItemKey = `${compareItem.HospCode}_${
          compareItem.DeptCode
        }_${dayjs(compareItem.ExactDate).format('YYYY-MM-DD')}`;
        processedCompareItems.set(compareItemKey, true);
      }

      // 使用公共函数处理对比
      const result = compareRecordWithItem(baseItem, compareItem);
      results.push(result);
    });

    // 处理compareData中未处理过的项（即baseData中不存在的项）
    compareData.forEach((compareItem) => {
      const compareItemKey = `${compareItem.HospCode}_${
        compareItem.DeptCode
      }_${dayjs(compareItem.ExactDate).format('YYYY-MM-DD')}`;

      // 如果这个compareItem尚未处理过，则创建一个新记录
      if (!processedCompareItems.has(compareItemKey)) {
        // 创建新记录
        const newRecord: any = {
          // 从compareItem复制基本信息
          HospCode: compareItem.HospCode,
          HospName: compareItem.HospName,
          DeptCode: compareItem.DeptCode,
          DeptName: compareItem.DeptName,
          ExactDate: compareItem.ExactDate,

          // 生成唯一ID
          Id: `new-${uuidv4()}`,

          // 设置默认值
          Status: '',
          StatusName: '',
          IsLocked: false,
          Remark: '',
          LastModificationTime: '',
          ApprovedBedsNumber: 0,
          SuppliedBedsNumber: 0,
          StayInCnt: 0,
          StayingCnt: 0,
          CondDangCnt: 0,
          ActualBedDays: 0,
          DscgPatientActualBedDays: 0,
          IsCorrect: false,
          CorrectionTime: '',

          // 标记为有差异
          hasDiscrepancy: true,
        };

        // 基础字段设为0
        fieldsToCompare.forEach((field) => {
          newRecord[field] = 0;
        });

        // 使用公共函数处理对比
        const processedRecord = compareRecordWithItem(newRecord, compareItem);
        results.push(processedRecord);
      }
    });

    // 按日期从小到大排序
    return results.sort((a, b) => {
      return dayjs(a.ExactDate).isAfter(dayjs(b.ExactDate)) ? 1 : -1;
    });
  };

  return {
    reqActionReq,
    editSaveHandler,
    mergeAndCompareData,
    mergeAndCompareSingleRecord,
  };
};
