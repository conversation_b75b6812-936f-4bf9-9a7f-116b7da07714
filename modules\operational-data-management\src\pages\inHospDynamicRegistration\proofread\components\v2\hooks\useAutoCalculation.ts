import { useState, useRef, useEffect } from 'react';
import { useDebounceFn } from 'ahooks';
import _ from 'lodash';
import { isRespErr } from '@/utils/widgets';
import {
  autoCalculateHandler,
  autoComputeRef,
  getCalculationKeys,
} from '@/utils/util';

/**
 * 自动计算hook
 * @param editableTableFormRef 表单引用
 */
export const useAutoCalculation = (editableTableFormRef) => {
  const [autoCalculationKeys, setAutoCalculationKeys] = useState<string[]>([]);
  const isAutoComputeCntInput = useRef<any>(undefined);
  const getCalculationKeysDone = useRef(false);
  // 添加nowEditedRecord引用，用于存储自动计算接口返回的数据
  const nowEditedRecord = useRef<any>(null);

  // 初始化获取需要自动计算的字段
  useEffect(() => {
    const fetchCalculationKeys = async () => {
      let res = await getCalculationKeys('Inpatient');
      if (!isRespErr(res)) {
        isAutoComputeCntInput.current = autoComputeRef(Object.keys(res?.data));
        setAutoCalculationKeys(Object.keys(res?.data));
        getCalculationKeysDone.current = true;
      }
    };
    fetchCalculationKeys();
  }, []);

  // 使用防抖发送自动计算请求
  const { run: autoCalculateDebounceFn } = useDebounceFn(
    async (keys, record, formRef, isAutoComputInputs) => {
      const data = await autoCalculateHandler(
        keys,
        _.omit(record, ['HospCode']),
        formRef,
        'DeptInpatientAmt',
        isAutoComputInputs,
      );
      // 保存接口返回的数据到nowEditedRecord.current
      if (data) {
        nowEditedRecord.current = data;
      }
      return data; // 返回接口响应数据
    },
    { wait: 250 },
  );

  return {
    autoCalculationKeys,
    isAutoComputeCntInput,
    getCalculationKeysDone,
    autoCalculateDebounceFn,
    // 暴露nowEditedRecord引用
    nowEditedRecord,
    resetAutoCompute: () => {
      isAutoComputeCntInput.current = autoComputeRef(autoCalculationKeys);
    },
    // 提供清除nowEditedRecord的方法
    clearEditedRecord: () => {
      nowEditedRecord.current = null;
    },
  };
};
