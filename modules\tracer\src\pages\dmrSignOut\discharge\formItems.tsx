import { InputRef } from 'antd';
import { SigninType } from '@/Constants';
import {
  ProFormDependency,
  ProFormGroup,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Col } from 'antd';
import { DmrSignOutTypeOptions } from './constants';

/**
 * 病案出库表单项
 * @param employeeOpts 操作员选项数据
 * @param barCodeRef 条码输入框引用
 * @returns 表单项配置
 */
export const DmrSignOutFormItems = (
  employeeOpts = [],
  barCodeRef: React.RefObject<InputRef>,
) => [
  // {
  //   name: 'DmrSignOutOperator',
  //   title: '出库操作人',
  //   dataType: 'select',
  //   rules: [{ required: true, message: '请选择出库操作人' }],
  //   dictModuleGroup: 'Mr',
  //   dictModule: 'Employee',
  //   visible: true,
  // },
  // {
  //   name: 'DmrSignOutType',
  //   title: '出库类型',
  //   dataType: 'select',
  //   rules: [{ required: true, message: '请选择出库类型' }],
  //   opts: DmrSignOutTypeOptions,
  //   visible: true,
  // },
  // {
  //   name: 'Destination',
  //   title: (
  //     <span style={{ display: 'inline-block', marginLeft: '11px' }}>
  //       目标地点
  //     </span>
  //   ),
  //   dataType: 'text',
  //   visible: true,
  // },
  // {
  //   name: 'ExpectedReturnDate',
  //   title: '预计归还',
  //   dataType: 'date',
  //   visible: true,
  // },
  // {
  //   name: 'Remark',
  //   title: '备注',
  //   dataType: 'textarea',
  //   visible: true,
  // },
  {
    custom: true,
    dataType: 'FormGroup',
    visible: true,
    render: (
      <ProFormGroup grid>
        <Col flex={'92px'}>
          <ProFormSelect
            name="SignType"
            allowClear={false}
            initialValue={{
              label: SigninType[0].title,
              value: SigninType[0].value,
            }}
            fieldProps={{
              labelInValue: true,
              fieldNames: {
                label: 'title',
                value: 'value',
              },
              style: { width: '92px' },
            }}
            rules={[{ required: true }]}
            options={SigninType as any[]}
          />
        </Col>
        <Col flex={'auto'}>
          <ProFormDependency name={['SignType']}>
            {({ SignType }) => {
              return (
                <ProFormText
                  name="BarCode"
                  placeholder={
                    SignType?.value === 'BarCode'
                      ? '条码号(扫码)'
                      : `请输入${SignType?.label}`
                  }
                  fieldProps={{
                    ref: barCodeRef,
                  }}
                  rules={[{ required: true, message: '请输入病案标识' }]}
                />
              );
            }}
          </ProFormDependency>
        </Col>
      </ProFormGroup>
    ),
  },
];
