import dayjs from 'dayjs';

export const ReportEventConstant = {
  REPORT_ITEM_ADD: 'REPORT_ITEM_ADD',
  REPORT_ITEM_CLICK: 'REPORT_ITEM_CLICK',
  REPORT_ARCHIVE: 'REPORT_ARCHIVE',
  REPORT_DOWNLOAD: 'REPORT_DOWNLOAD',
  REPORT_EXPORT: 'REPORT_EXPORT',

  REPORT_BUNDLE_EXPORT: 'REPORT_BUNDLE_EXPORT',

  REPORT_BRIEF_EXPORT: 'REPORT_BRIEF_EXPORT',

  REPORT_REFRESH: 'REPORT_REFRESH',
  REPORT_LOCK: 'REPORT_LOCK',
  REPORT_UNLOCK: 'REPORT_UNLOCK',
  REPORT_LOAD: 'REPORT_LOAD',
  REPORT_ITEM_CREATED: 'REPORT_ITEM_CREATED',
  REPORT_ITEM_UPDATED: 'REPORT_ITEM_UPDATED',

  REPORT_ITEM_QUERY: 'REPORT_ITEM_QUERY',
  REPORT_ITEM_QUERY_SUBMIT: 'REPORT_ITEM_QUERY_SUBMIT',

  REPORT_MASTER_ITEM_CLICK: 'REPORT_MASTER_ITEM_CLICK',

  REPORT_DETAIL_VISIBLE: 'REPORT_DETAIL_VISIBLE',

  REPORT_DETAIL_UPDATE_SUCCESS: 'REPORT_DETAIL_UPDATE_SUCCESS',

  VALIDATION_EXPORT: 'VALIDATION_EXPORT',
  VALIDATION_DOWNLOAD: 'VALIDATION_DOWNLOAD',

  VALIDATION_DATA_RESET: 'VALIDATION_DATA_RESET',

  STATS_PERSIST_EDIT: 'STATS_PERSIST_EDIT',

  STATS_PERSIST_IMPORT_DOWNLOAD_TEMPLATE:
    'STATS_PERSIST_IMPORT_DOWNLOAD_TEMPLATE',
  STATS_PERSIST_IMPORT_UPLOAD_TEMPLATE: 'STATS_PERSIST_IMPORT_UPLOAD_TEMPLATE',

  STATS_READONLY_CELL_CLICK: 'STATS_READONLY_CELL_CLICK',

  REPORT_GROUP_DATA_CLEAR: 'REPORT_GROUP_DATA_CLEAR',

  REPORT_GROUP_ROW_INITIAL_CLICK: 'REPORT_GROUP_ROW_INITIAL_CLICK',

  REPORT_VALIDATION_ERROR_DATA_EXIST: 'REPORT_VALIDATION_ERROR_DATA_EXIST',

  REPORT_VALIDATION_FETCH: 'REPORT_VALIDATION_FETCH',
};

export const ReportModes = {
  StatsReadOnly: 'StatsReadOnly',
  StatsPersist: 'StatsPersist',
  DetailsReadOnly: 'DetailsReadOnly',
  DetailsPersist: 'DetailsPersist',
  ReportGroupReadOnly: 'ReportGroupReadOnly',
};

export const RelationMode = {
  ByRow: 'ByRow',
  ByColumn: 'ByColumn',
};

export const ReportDataSize = {
  SingleLine: 'SingleLine',
  FixedMultiLine: 'FixedMultiLine',
  VariableMultiLine: 'VariableMultiLine',
  Details: 'Details',
};

export const PeriodicPickerModes = {
  ByDay: {
    name: '按日',
    value: 'date',
  },
  ByMonth: {
    name: '按月',
    value: 'month',
  },
  ByQuarter: {
    name: '按季度',
    value: 'quarter',
  },
  ByYear: {
    name: '按年',
    value: 'year',
  },
};

export const RangePickerModes = {
  date: {
    value: 'date',
    dateProcessor: (dates) => {
      return dates?.map((item) => {
        return dayjs(item).format('YYYY-MM-DD');
      });
    },
  },
  month: {
    value: 'month',
    dateProcessor: (dates) => {
      return [
        dayjs(dates[0]).startOf('month').format('YYYY-MM-DD'),
        dayjs(dates[1]).endOf('month').format('YYYY-MM-DD'),
      ];
    },
  },
  quarter: {
    value: 'quarter',
    dateProcessor: (dates) => {
      return [
        dayjs(dates[0])
          .startOf('quarter' as any)
          .format('YYYY-MM-DD'),
        dayjs(dates[1])
          .endOf('quarter' as any)
          .format('YYYY-MM-DD'),
      ];
    },
  },
  year: {
    value: 'year',
    dateProcessor: (dates) => {
      return [
        dayjs(dates[0]).startOf('year').format('YYYY-MM-DD'),
        dayjs(dates[1]).endOf('year').format('YYYY-MM-DD'),
      ];
    },
  },
  datetime: {
    value: 'date',
    dateProcessor: (dates) => {
      return dates?.map((item) => {
        return dayjs(item).format('YYYY-MM-DD HH:mm:ss');
      });
    },
  },
};

// TODO 怎么判定是开始时间 还是 结束时间  startOf 还是 endOf
export const PickerModes = {
  date: {
    value: 'date',
    dateProcessor: (date) => {
      return dayjs(date).format('YYYY-MM-DD');
    },
  },
  month: {
    value: 'month',
    dateProcessor: (date) => {
      return dayjs(date).startOf('month').format('YYYY-MM-DD');
    },
  },
  quarter: {
    value: 'quarter',
    dateProcessor: (date) => {
      return dayjs(date)
        .startOf('quarter' as any)
        .format('YYYY-MM-DD');
    },
  },
  year: {
    value: 'year',
    dateProcessor: (date) => {
      return dayjs(date).startOf('year').format('YYYY-MM-DD');
    },
  },
  datetime: {
    value: 'date',
    dateProcessor: (date) => {
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    },
  },
};
