import isString from 'lodash/isString';
import { isEmptyValues } from '@uni/utils/src/utils';

const sortDictName =
  (window as any).externalConfig?.['common']?.sortDictName ?? false;

interface SorterItemProcessorProps {}

const getActualSorterItemValue = (
  dictionaryData: any,
  module: string,
  moduleGroup: string,
  value: string,
) => {
  if (sortDictName === false) {
    return value;
  }

  if (isEmptyValues(value)) {
    return value;
  }

  if (isEmptyValues(module)) {
    return value;
  }

  let dictDataWithGrouped =
    (isEmptyValues(moduleGroup)
      ? dictionaryData?.[module]
      : dictionaryData?.[moduleGroup]?.[module]) ?? [];
  let dictDataItem = dictDataWithGrouped?.find((item) => {
    return item?.Code === value;
  });

  if (!isEmptyValues(dictDataItem)) {
    return dictDataItem?.Name ?? value;
  }

  return value;
};

export const SorterItemWithDirectoryProcessor = (
  columns: any[],
  backendPagination: boolean | object,
  infiniteScroll: boolean,
  dictionaryData: any,
) => {
  if (isEmptyValues(dictionaryData)) {
    return columns;
  }

  if (backendPagination || infiniteScroll) {
    return columns;
  }

  columns?.forEach((columnItem) => {
    let sorterItem = SorterItemProcessor(
      columnItem,
      backendPagination,
      infiniteScroll,
      dictionaryData,
    ) as any;

    if (!isEmptyValues(sorterItem?.sortingFn)) {
      columnItem['sortingFn'] = sorterItem?.sortingFn;
    }
  });

  return columns;
};

const SorterItemProcessor = (
  columnItem: any,
  backendPagination: boolean | object,
  infiniteScroll: boolean,
  dictionaryData?: any,
) => {
  let sorter = {};
  if (
    columnItem.sorterType ||
    (!columnItem?.sorterType && columnItem?.orderable)
  ) {
    sorter =
      backendPagination || infiniteScroll
        ? {
            enableSorting: true,
          }
        : {
            enableSorting: true,
            sortingFn: (a, b, columnId) => {
              let aValue = getActualSorterItemValue(
                dictionaryData,
                columnItem?.dictionaryModule,
                columnItem?.dictionaryModuleGroup,
                a[columnItem.dataIndex],
              );

              let bValue = getActualSorterItemValue(
                dictionaryData,
                columnItem?.dictionaryModule,
                columnItem?.dictionaryModuleGroup,
                b[columnItem.dataIndex],
              );

              return isString(aValue) && isString(bValue)
                ? aValue.localeCompare(bValue)
                : aValue - bValue;
            },
          };
  }

  return sorter;
};

export default SorterItemProcessor;
