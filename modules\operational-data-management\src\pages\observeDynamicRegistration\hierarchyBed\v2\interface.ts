export interface HierarchyBedAmtItem {
  Id: number;
  HospCode: string;
  HospName: string;
  HierarchyCode: string;
  HierarchyName: string;
  HierarchyType: string;
  Status: string;
  StatusName: string;
  Sdate: string;
  Edate: string;
  ApprovedBedAmt: number;
  OpenBedAmt: number;
  Reserve1Amt: number;
  Reserve2Amt: number;
  Reserve3Amt: number;
  Reserve4Amt: number;
  Reserve5Amt: number;
  Sort: number;
  IsValid: boolean;
}

export interface UpsertBedAmtParams {
  Id?: number;
  HospCode: string;
  Sdate: string;
  Edate: string;
  ApprovedBedAmt?: number;
  OpenBedAmt?: number;
  Reserve1Amt?: number;
  Reserve2Amt?: number;
  Reserve3Amt?: number;
  Reserve4Amt?: number;
  Reserve5Amt?: number;
  HierarchyCode: string;
  HierarchyName?: string;
  HierarchyType: string;
}

export interface BulkUpdateBedAmtParams {
  HospCode: string[];
  DeptCode: string[];
  Sdate: string;
  Edate: string;
  ApprovedBedsNumber: number;
  SuppliedBedsNumber: number;
}
