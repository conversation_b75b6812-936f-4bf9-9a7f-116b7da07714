import React, { useEffect, useRef, useState } from 'react';
import { useModel } from '@@/plugin-model/useModel';
import { UniTable } from '@uni/components/src';
import { useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { uniCommonService } from '@uni/services/src';
import {
  Row,
  Steps,
  Col,
  Form,
  Input,
  Card,
  Descriptions,
  Button,
  Tag,
  Space,
  Spin,
  Tooltip,
} from 'antd';
import IconBtn from '@uni/components/src/iconBtn';
import { ProForm } from '@ant-design/pro-components';
import { ProDescriptions } from '@ant-design/pro-descriptions';
import { barCodeFormColumns } from './columns';
import { DoubleRightOutlined } from '@ant-design/icons';
const { Search } = Input;

const BarCodeForm = (props) => {
  const [barCode, setBarCode] = useState(null);
  const [barCodeList, setBarCodeList] = useState([]);
  const [recordData, setRecordData] = useState(undefined);

  useEffect(() => {
    if (barCode?.length) {
      getRecordDataReq(barCode);
    }
  }, [barCode]);

  const {
    // data: recordData,
    loading: getRecordDataLoading,
    run: getRecordDataReq,
  } = useRequest(
    (barCode) => {
      return uniCommonService(`Api/Mr/TraceRecord/Get`, {
        method: 'GET',
        dataType: 'mr',
        requestType: 'json',
        params: { BarCode: barCode },
      });
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res.code === 0) {
          setRecordData(res?.data);
          return res?.data;
        }
      },
    },
  );

  useEffect(() => {
    if (barCodeList.length) {
      props.formRef2?.current?.setFieldValue('BarCodes', barCodeList);
    }
  }, [barCodeList]);

  return (
    <>
      <Row gutter={16}>
        <Col span={12}>
          <ProForm layout="vertical" submitter={false}>
            <Form.Item label="病案查询" name="barCode">
              <Search
                placeholder="请输入条码号"
                onSubmit={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  return false;
                }}
                onBlur={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
                onSearch={(value) => {
                  setBarCode(value);
                }}
              />
            </Form.Item>
            <Form.Item>
              <Spin spinning={getRecordDataLoading || false}>
                <Card size="small">
                  <ProDescriptions
                    title={
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}
                      >
                        <span>条码号 {recordData?.BarCode || ''}</span>
                        {recordData?.CanLend === true ? (
                          <Tag color="success" style={{ border: 0 }}>
                            可借阅
                          </Tag>
                        ) : recordData?.CanLend === false ? (
                          <Tooltip title="病案封存/病案未归档/病案已借出">
                            <Tag color="error" style={{ border: 0 }}>
                              不可借阅
                            </Tag>
                          </Tooltip>
                        ) : (
                          <></>
                        )}
                      </div>
                    }
                    size="small"
                    column={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 1 }}
                    dataSource={recordData || {}}
                    columns={barCodeFormColumns}
                  ></ProDescriptions>
                </Card>
              </Spin>
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                disabled={!recordData?.CanLend}
                style={{ width: '100%' }}
                onSubmit={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  return false;
                }}
                onBlur={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  return false;
                }}
                onClick={(values) => {
                  if (!barCodeList.some((item) => item.barCode === barCode)) {
                    setBarCodeList([...barCodeList, { barCode: barCode }]);
                  }
                  setRecordData(undefined);
                  setBarCode(undefined);
                }}
              >
                添加 <DoubleRightOutlined />
              </Button>
            </Form.Item>
          </ProForm>
        </Col>
        <Col span={12}>
          <ProForm layout="vertical" formRef={props.formRef2} submitter={false}>
            <Form.Item
              label="借阅病案列表"
              name="BarCodes"
              rules={[{ required: true }]}
            >
              <UniTable
                id="trace_borrow_single_task"
                rowKey="barCode"
                columns={tableColumnBaseProcessor(
                  [
                    {
                      title: '操作',
                      valueType: 'option',
                      visible: true,
                      align: 'center',
                      order: Number.MAX_VALUE,
                      width: 80,
                      render: (node, record, index, action) => (
                        <>
                          <IconBtn
                            key="delete"
                            type="delete"
                            onClick={() => {
                              setBarCodeList(
                                barCodeList.filter(
                                  (obj) => obj.barCode !== record?.barCode,
                                ),
                              );
                            }}
                          />
                        </>
                      ),
                    },
                  ],
                  [
                    {
                      title: '条码号',
                      visible: true,
                      data: 'barCode',
                    },
                  ],
                )}
                dataSource={barCodeList}
                scroll={{ x: 'max-content' }}
              />
            </Form.Item>
          </ProForm>
        </Col>
      </Row>
    </>
  );
};

export default BarCodeForm;
