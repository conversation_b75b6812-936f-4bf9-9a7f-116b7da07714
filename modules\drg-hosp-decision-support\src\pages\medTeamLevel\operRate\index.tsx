import _ from 'lodash';
import { useState } from 'react';
import { useRequest, useModel } from 'umi';
import { Col, Row, Tabs } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import { OperTrendsBar } from '@/echarts/oper.chart.opts';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { RespVO } from '@uni/commons/src/interfaces';
import SingleColumnTable from '@/components/SingleColumnTable/index';
import { uniCommonService } from '@uni/services/src/commonService';
import Stats from '@/components/stats/index';
import { OperNormalStat } from '@/constants';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import DrawerCardInfo from '@/components/drawerCardInfo/index';

const OperRate = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, MedTeams } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // 这边调bundleData
  const {
    data: BundledData,
    loading: getBundledDataLoading,
    run: getBundledDataReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/v2/Drgs/MedTeamOper/BundledOperRate', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
      MedTeams,
    };
    setTableParams(params);
    getBundledDataReq(params);
  }, [dateRange, hospCodes, MedTeams]);

  let tabItems = [
    {
      key: 'statistic',
      label: '医疗组综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Drgs/MedTeamOper/BundledOperRate`}
              trendApi={`Api/v2/Drgs/MedTeamOper/OperRateTrend`}
              columns={OperNormalStat}
              defaultSelectItem={'OperPatCnt'}
              type="col-xl-8"
              chartHeight={310}
              tabKey={activeKey}
              useGlobalState
              tableParams={tableParams}
            />
          </Col>
          <SingleColumnTable
            title="医疗组外科能力"
            args={{
              api: `Api/v2/Drgs/MedTeamOper/OperRateByMedTeam`,
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="MedTeamName"
            type="table"
            colSpan={{ span: 24 }}
            orderKey="OperPatCnt"
            visibleValueKeys={['MedTeamName', 'TotalCnt', 'OperPatCnt']}
            chart={{
              api: `Api/v2/Drgs/MedTeamOper/OperRateTrend`,
              title: '各级手术变化趋势',
              colSpan: { span: 24 },
              options: (data) => {
                return data
                  ? OperTrendsBar(
                      data?.map((d) => ({
                        ...d,
                        MonthDate: valueNullOrUndefinedReturnDash(
                          d?.MonthDate,
                          'Month',
                        ),
                      })),
                      'MonthDate',
                    )
                  : {};
              },
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName + '手术人次',
                args: {
                  ...tableParams,
                  MedTeams: [record?.MedTeam],
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/OperDetails',

                dictData: dictData, // 传入
              });
            }}
          />
          {/* TODO 各级别术种数量 */}
          <Col></Col>
        </Row>
      ),
    },
    {
      key: 'dept_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <SingleColumnTable
          title="医生外科能力"
          args={{
            api: 'Api/v2/Drgs/SurgeonOper/OperRateBySurgeon',
          }}
          tableParams={tableParams}
          dictData={dictData}
          type="table"
          orderKey="OperPatCnt"
          visibleValueKeys={[
            'SurgeonName',
            // 'TotalCnt',
            'OperPatCnt',
            // 'OperPatRatio',
            'Grade3Or4OperPatCnt',
            'Grade3Or4OperPatRatio',
            'AvgPreOperPeriod',
            'AvgPostOperPeriod',
          ]}
          colSpan={{ span: 24 }}
          select={{
            dataKey: 'SurgeonType',
            valueKey: 'SurgeonType',
            allowClear: false,
            defaultSelect: true,
          }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.SurgeonName + '手术人次',
              args: {
                ...tableParams,
                SurgeonType: record?.SurgeonType?.toLocaleUpperCase(),
                SurgeonCodes: [record?.SurgeonCode],
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/OperDetails',

              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default OperRate;
