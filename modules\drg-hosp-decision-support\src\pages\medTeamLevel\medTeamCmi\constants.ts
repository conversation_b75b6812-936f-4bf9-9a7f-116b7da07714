export const CmiStat = [
  {
    contentData: 'PatCnt',
    clickable: true,
    footerYoy: true,
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
  },
  {
    contentData: 'Cmi',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'Cm',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'DrgCnt',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgInPeriod',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '均次费用',
    contentData: 'AvgTotalFee',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '平均药费',
    dataIndex: 'AvgMedicineFee',
    contentData: 'AvgMedicineFee',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '药占比',
    contentData: 'MedicineFeeRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '平均耗材费',
    contentData: 'AvgMaterialFee',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '材料费占比',
    contentData: 'MaterialFeeRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '费用指数',
    contentData: 'Cei',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '时间指数',
    contentData: 'Tei',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '低风险死亡率',
    contentData: 'LowRiskDeathRatio',
    clickable: true,
    footerYoy: true,
  },
];

const NormalAxisOpts = [
  'PatCnt',
  'Cmi',
  'Cm',
  'DrgCnt',
  'AvgInPeriod',
  'AvgTotalFee',
  'AvgMedicineFee',
  'MedicineFeeRatio',
  'AvgMaterialFee',
  'MaterialFeeRatio',
  'Cei',
  'Tei',
  'LowRiskPatCnt',
  'LowRiskDeathCnt',
  'LowRiskDeathRatio',
];
export const MedTeamAxisOpts = [...NormalAxisOpts];

export const DefaultOpts = {
  xAxis: 'Cmi',
  yAxis: 'AvgTotalFee',
};

export enum ReqActionType {
  // 科级CMI概况
  CmiOfMedTeam = 'MedTeamDrg/CmiOfMedTeam',
  CmiByMedTeam = 'MedTeamDrg/CmiByMedTeam',
  // CmiByMedTeam = 'MedTeamDrg/CmiByMedTeam',
  // CmiByMedTeamDoctor = 'MedTeamDrg/CmiByMedTeamDoctor',
  CmiTrendOfMedTeam = 'MedTeamDrg/CmiTrendOfMedTeam',
  RwDistributionOfMedTeam = 'MedTeamDrg/RwDistributionOfMedTeam',
  ADrgCompositionOfMedTeam = 'MedTeamDrg/ADrgCompositionOfMedTeam',
}
