.group-tree-select {
  &-dropdown {
    // 为父节点添加样式，使其看起来像OptGroup标题
    .ant-select-tree-treenode {
      // 父节点样式 - 只有.ant-select-tree-treenode-switcher-open是父节点
      &.ant-select-tree-treenode-switcher-open {
        > .ant-select-tree-node-content-wrapper {
          color: rgba(0, 0, 0, 0.45);
          font-size: 12px;
          cursor: pointer;

          &:hover {
            background-color: rgba(0, 0, 0, 0.04);
          }
        }
      }

      // 在非第一个父节点上方添加分割线
      &.ant-select-tree-treenode-switcher-open:not(:first-child) {
        position: relative;
        padding-top: 4px;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 1px;
          background-color: #f0f0f0;
        }
      }

      // 子节点样式 - .ant-select-tree-treenode-switcher-close是子节点
      &.ant-select-tree-treenode-switcher-close {
        padding-left: 12px;

        > .ant-select-tree-node-content-wrapper {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }

    // 隐藏展开/收缩图标
    .ant-select-tree-switcher {
      display: none;
      width: 0;
    }

    // 节点样式
    .ant-select-tree-node-content-wrapper {
      // 普通hover状态
      &:hover {
        background-color: #f5f5f5;
      }

      // 选中状态 - 优先级应高于hover状态
      &.ant-select-tree-node-selected {
        position: relative;
        background-color: #e6f7ff !important;
        font-weight: bold !important;

        // 选中节点右侧添加勾选图标
        &::after {
          content: '✓';
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: #1890ff;
          font-weight: bold;
        }
      }
    }
  }

  // 选中标签样式 - 与antd tag保持一致
  .ant-tag.ant-select-selection-item {
    display: flex;
    align-items: center;
    height: 24px;
    margin-top: 2px;
    margin-bottom: 2px;
    padding: 0 4px 0 8px;
    background-color: #f5f5f5;
    border: 1px solid #f0f0f0;
    border-radius: 2px;
    font-size: 14px;

    // 关闭图标样式
    .ant-tag-close-icon {
      display: inline-flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.45);
      font-weight: bold;
      font-size: 10px;
      margin-left: 4px;
      height: 100%;

      svg {
        vertical-align: middle;
      }

      &:hover {
        color: rgba(0, 0, 0, 0.75);
      }
    }
  }

  // 底部按钮样式
  .ant-btn {
    padding: 0 8px;
    height: 24px;
    line-height: 22px;
    font-size: 12px;
    margin-right: 8px;
  }
}
