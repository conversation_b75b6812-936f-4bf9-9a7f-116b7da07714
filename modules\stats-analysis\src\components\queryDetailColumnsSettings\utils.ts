import {
  QueryDetailColumnItem,
  QueryDetailTemplateColumnItem,
} from './interface';
// 明细columns 编辑以及数据处理等函数

export const columnSettingColumnsProcessor = (
  columnData: any[],
  columnState: any,
) => {
  Object.keys(columnState)?.forEach((key) => {
    let columnItem = columnData?.find((item) => item.name === key);
    if (columnItem) {
      columnItem['order'] = columnState?.[key]?.order;
    }
  });

  return columnData
    ?.map((item, index) => {
      if (!item['order']) {
        item['order'] = index;
      }
      item['index'] = index;
      item['key'] = item?.name;

      return item;
    })
    ?.sort((a, b) => a.order - b.order);
};

export const columnsTreeProcessor = (columns, columnsMap) => {
  let columnsTree = [];
  console.log('columns', columns, columnsMap);
  columns?.forEach((columnItem) => {
    if (columnItem?.directories) {
      // 目录树
      const leftDirectories = columnItem?.directories?.slice(0, 1);
      leftDirectories?.forEach((directoryItem, index) => {
        let directoryColumnTreeItem = columnsTree?.find(
          (item) => item?.title === directoryItem,
        );
        if (directoryColumnTreeItem === undefined) {
          directoryColumnTreeItem = {
            title: directoryItem,
            key: directoryItem,
            directory: true,
            directorySort: columnItem?.directorySort ?? 0,
          };
          if (index === 0) {
            columnsTree.push(directoryColumnTreeItem);
          } else {
            // 上级 文件夹
            let parentDirectoryColumnTreeItem = columnsTree?.find(
              (item) => item?.title === leftDirectories[index - 1],
            );
            if (parentDirectoryColumnTreeItem) {
              parentDirectoryColumnTreeItem['children'] = [
                ...(parentDirectoryColumnTreeItem['children'] ?? []),
                directoryColumnTreeItem,
              ];
            }
          }
        }

        if (index === leftDirectories?.length - 1) {
          directoryColumnTreeItem['directorySort'] = Math.max(
            directoryColumnTreeItem['directorySort'],
            columnItem?.directorySort,
          );
          directoryColumnTreeItem['children'] = [
            ...(directoryColumnTreeItem['children'] ?? []),
            {
              title: columnItem?.title ?? columnItem?.originTitle,
              originTitle: columnItem?.originTitle,
              key: columnItem?.name,
              directory: false,
              directories: columnItem?.directories,
              ...(columnsMap?.[columnItem?.name] ?? {}),
              columnSequence: columnItem?.columnSequence ?? 0,
            },
          ]?.sort((a, b) => a?.columnSequence - b?.columnSequence);
        }
      });
      // columnItem?.directories?.forEach((directoryItem, index) => {
      //   let directoryColumnTreeItem = columnsTree?.find(
      //     (item) =>
      //       item?.title === directoryItem ||
      //       item?.key?.split('-')?.[0] === directoryItem,
      //   );
      //   if (directoryColumnTreeItem === undefined) {
      //     directoryColumnTreeItem = {
      //       title: index === 0 ? directoryItem : columnItem?.title,
      //       key:
      //         index === 0
      //           ? directoryItem
      //           : `${directoryItem}-${columnItem?.name}`,
      //       directory: true,
      //       directorySort: columnItem?.directorySort ?? 0,
      //       ...(index !== 0
      //         ? {
      //             originTitle: columnItem?.originTitle,
      //             directories: columnItem?.directories,
      //             ...(columnsMap?.[columnItem?.name] ?? {}),
      //             columnSequence: columnItem?.columnSequence ?? 0,
      //           }
      //         : {}),
      //     };
      //     if (index === 0) {
      //       columnsTree.push(directoryColumnTreeItem);
      //     } else {
      //       // 上级 文件夹
      //       let parentDirectoryColumnTreeItem = columnsTree?.find(
      //         (item) => item?.key === columnItem?.directories[index - 1],
      //       );
      //       if (parentDirectoryColumnTreeItem) {
      //         parentDirectoryColumnTreeItem['children'] = [
      //           ...(parentDirectoryColumnTreeItem['children'] ?? []),
      //           directoryColumnTreeItem,
      //         ];
      //       }
      //     }
      //   }

      //   if (index === columnItem?.directories?.length - 1) {
      //     directoryColumnTreeItem['children'] = [
      //       ...(directoryColumnTreeItem['children'] ?? []),
      //       {
      //         title: columnItem?.title ?? columnItem?.originTitle,
      //         originTitle: columnItem?.originTitle,
      //         key: columnItem?.name,
      //         directory: false,
      //         directories: columnItem?.directories,
      //         ...(columnsMap?.[columnItem?.name] ?? {}),
      //         columnSequence: columnItem?.columnSequence ?? 0,
      //       },
      //     ]?.sort((a, b) => a?.columnSequence - b?.columnSequence);
      //   }
      // });
    } else {
      columnsTree.push({
        title: columnItem?.title ?? columnItem?.originTitle,
        originTitle: columnItem?.originTitle,
        key: columnItem?.name,
        directory: false,
        ...(columnsMap?.[columnItem?.name] ?? {}),

        directorySort:
          columnItem?.directorySort ?? columnItem?.columnSequence ?? 0,
        columnSequence: columnItem?.columnSequence ?? 0,
      });
    }
  });

  console.log('columnsTree', columnsTree);

  return columnsTree?.sort((a, b) => a?.directorySort - b?.directorySort);
};

export function checkSpecialKey(obj) {
  const specialKeys = ['Coder', 'CliDept', 'Chief', 'MedTeam'];

  for (let key of specialKeys) {
    if (obj.hasOwnProperty(key)) {
      return key;
    }
  }

  return null; // 如果没有找到任何特殊键，返回 null
}

// 根据 detailCheckedColumns 显示 明细columns
export const defaultCheckedColumnStateProcessor = (
  columnData: any[],
  detailCheckedColumns: any[],
  showSpecialKey?: string,
) => {
  let columnState = {};

  // 1. 没有detailCheckedColumns 才展示默认的
  // 2025/03/12 逻辑做了修改 没有 detailCheckedColumns 则不显示columns
  columnData?.forEach((item, index) => {
    columnState[item?.name] = {
      show: false,
      fixed: undefined,
      order: item?.columnSequence ?? item?.order ?? index + 1,
      name: item?.name,
      id: item?.id,
    };
  });

  // 2. 有detailCheckedColumns
  detailCheckedColumns?.map((item) => {
    let columnItem = columnData?.find(
      (columnDataItem) => columnDataItem?.id === item?.SubjectId,
    );
    if (columnItem) {
      // state
      columnState[columnItem?.name] = {
        show: true,
        fixed: undefined,
        order:
          item?.ColumnSort ?? columnItem?.columnSequence ?? columnItem?.order,
        name: columnItem?.name,
        id: columnItem?.id,
      };

      // title 变更
      if (item?.CustomTitle) {
        columnItem['title'] = item?.CustomTitle;
        columnState[columnItem?.name]['title'] = item?.CustomTitle;
      }

      if (columnItem?.['customTitle']) {
        columnItem['title'] = columnItem?.['customTitle'];
        columnState[columnItem?.name]['title'] = columnItem?.['customTitle'];
      }
    }
  });
  console.log('columnState', columnState);
  return columnState;
};

export const selectedTableColumnsProcessor = (columnsState: any) => {
  let outputColumns = [];
  Object.keys(columnsState)?.forEach((key) => {
    if (columnsState[key]?.show) {
      let columnItem = {
        Id: columnsState[key]?.id,
        Name: columnsState[key]?.name,
      };

      if (columnsState[key]?.title) {
        columnItem['CustomTitle'] = columnsState[key]?.title;
      }

      if (columnsState[key]?.order) {
        columnItem['columnSort'] = columnsState[key]?.order;
      }

      outputColumns.push(columnItem);
    }
  });

  // 排序
  outputColumns.sort((a, b) => {
    return a?.columnSort - b?.columnSort;
  });

  return outputColumns;
};
