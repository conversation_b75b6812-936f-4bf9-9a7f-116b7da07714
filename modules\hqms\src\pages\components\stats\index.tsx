import { Spin, Col } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import SingleStat from '@uni/components/src/statistic';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';
import './index.less';
import { ColTypeSpan } from './constants';
import { useDeepCompareEffect } from 'ahooks';

export interface IStatsProps {
  level?: string; // 用于 判断selectedTableItem的内部 要使用的值的类型：有 grp, adrg, dept, medTeam, 不填
  useGlobalState?: boolean; // 不填默认使用selectedTableItem 而 非globalState
  selectedTableItem?: {
    // 用于 在XXX波士顿矩阵分析 这类tab下 与左侧table点击联动的item
    VersionedADrgCode?: any;
    VersionedChsDrgCode?: any;
    CliDept?: any;
    MedTeam?: any;
  };
  columns?: any[];
  api?: string;
  type?: string; // col-xl-?
  tabKey?: string; // 用于判断当前tab的
  noClickable?: boolean; // 不准点 用于 在XXX波士顿矩阵分析
  loading?: boolean; // 外部loading
}

const Stats = (props: IStatsProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { insurType, dateRange, hospCodes, CliDepts, MedTeams } =
    globalState?.searchParams;
  const [statsData, setStatsData] = useState([]);
  const [params, setParams] = useState({});

  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedStatItem, setSelectedStatItem] = useState({
    contentData: 'Profit',
    dataType: 'Currency',
    title: '盈亏',
  });

  // stat click
  useEffect(() => {
    Emitter.on(EventConstant.STAT_CLICK, (record) => {
      setSelectedStatItem(record);
    });
    return () => {
      Emitter.off(EventConstant.STAT_CLICK);
    };
  }, [selectedStatItem]);

  useDeepCompareEffect(() => {
    if (dateRange?.length) {
      let tableParams: any = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        insurType,
      };
      // adrg & grp 只会在selectedTableItem时出现
      if (props?.level === 'adrg') {
        if (props?.selectedTableItem?.VersionedADrgCode) {
          tableParams = {
            ...tableParams,
            VersionedADrgCodes: [props?.selectedTableItem?.VersionedADrgCode],
          };
          getSettleCompStatsOfEntityReq(tableParams);
        }
      } else if (props?.level === 'grp') {
        if (props?.selectedTableItem?.VersionedChsDrgCode) {
          tableParams = {
            ...tableParams,
            VersionedChsDrgCodes: [
              props?.selectedTableItem?.VersionedChsDrgCode,
            ],
          };
          getSettleCompStatsOfEntityReq(tableParams);
        }
      }
      // dept & medTeam 都有可能出现 ，并且都为必填
      else if (props?.level === 'dept') {
        if (props?.useGlobalState) {
          if (CliDepts?.length) {
            tableParams = {
              ...tableParams,
              CliDepts,
            };
            getSettleCompStatsOfEntityReq(tableParams);
          }
        } else {
          if (props?.selectedTableItem) {
            tableParams = {
              ...tableParams,
              CliDepts: [props?.selectedTableItem?.CliDept],
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        }
      }
      // 医疗组
      else if (props?.level === 'medTeam') {
        if (props?.useGlobalState) {
          if (MedTeams?.length) {
            tableParams = {
              ...tableParams,
              MedTeams,
            };
            getSettleCompStatsOfEntityReq(tableParams);
          }
        } else {
          if (props?.selectedTableItem) {
            tableParams = {
              ...tableParams,
              MedTeams: [props?.selectedTableItem?.MedTeam],
            };
            getSettleCompStatsOfEntityReq(tableParams);
          }
        }
      }
      // 最后啥都没有的话直接调
      else {
        getSettleCompStatsOfEntityReq(tableParams);
      }
      setParams(tableParams);
    }
  }, [
    insurType,
    props?.level,
    dateRange,
    hospCodes,
    CliDepts,
    MedTeams,
    props?.selectedTableItem,
    props?.useGlobalState,
  ]);

  // 概况
  const {
    data: settleCompStatsOfEntityData,
    loading: getSettleCompStatsOfEntityLoading,
    run: getSettleCompStatsOfEntityReq,
  } = useRequest(
    (data) =>
      uniCommonService(props?.api, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  useEffect(() => {
    getSettleCompStatsOfEntityColumnsReq();
  }, []);

  const {
    data: settleCompStatsOfEntityColumns,
    run: getSettleCompStatsOfEntityColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(props?.api, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      // manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.Columns;
        }
      },
    },
  );

  useEffect(() => {
    if (
      settleCompStatsOfEntityColumns &&
      settleCompStatsOfEntityColumns.length &&
      settleCompStatsOfEntityData?.Stats?.length
    ) {
      let data = [];
      data = props?.columns?.map((stat) => {
        let contentItemColumn,
          footItemColumn,
          value,
          footerValue,
          yoy,
          suffix,
          footerNode;

        contentItemColumn = settleCompStatsOfEntityColumns.filter(
          (col) => col.data === stat.contentData,
        )[0];

        Object.keys(settleCompStatsOfEntityData?.Stats[0]).forEach(
          (key, val) => {
            if (key === stat.contentData) {
              value = settleCompStatsOfEntityData?.Stats[0]?.[key];
            }
          },
        );

        if (
          settleCompStatsOfEntityData?.VerticalCompareStatsResult?.Yoys?.length
        ) {
          Object.keys(
            settleCompStatsOfEntityData.VerticalCompareStatsResult.Yoys[0],
          ).forEach((key, val) => {
            if (key === stat.contentData) {
              yoy =
                settleCompStatsOfEntityData.VerticalCompareStatsResult
                  .Yoys[0]?.[key];
            }
          });
        }
        if (!yoy) {
          suffix = '';
        } else if (yoy > 0) {
          let node = (
            <span>
              <CaretUpOutlined className="font-success" />
              {valueNullOrUndefinedReturnDash(yoy, 'Percent')}
            </span>
          );
          if (stat?.footerYoy) footerNode = node;
          else suffix = node;
        } else {
          let node = (
            <span>
              <CaretDownOutlined className="font-danger" />
              {valueNullOrUndefinedReturnDash(yoy, 'Percent')}
            </span>
          );
          if (stat?.footerYoy) footerNode = node;
          else suffix = node;
        }

        if (stat?.footerData) {
          footItemColumn = settleCompStatsOfEntityColumns.filter(
            (col) => col.data === stat.footerData,
          )[0];
          Object.keys(settleCompStatsOfEntityData?.Stats[0]).forEach(
            (key, val) => {
              if (key === stat.footerData) {
                footerValue = settleCompStatsOfEntityData?.Stats[0]?.[key];
              }
            },
          );
        }

        return {
          ...stat,
          title: stat?.title || contentItemColumn?.title,
          value,
          suffix,
          dataType: stat?.dataType || contentItemColumn?.dataType,
          footerNode,
          footerTitle: stat?.footerTitle || footItemColumn?.title,
          footerValue,
          footerDataType: footItemColumn?.dataType,
          hidden: !stat?.showGrpCnt && !contentItemColumn?.visible,
        };
      });

      setStatsData(data);
    } else {
      setStatsData([]);
    }
  }, [
    props?.columns,
    settleCompStatsOfEntityColumns,
    settleCompStatsOfEntityData,
  ]);

  return (
    <>
      {props?.loading || getSettleCompStatsOfEntityLoading ? (
        <Col span={24}>
          <div className="spin-container">
            <Spin tip="加载中..."></Spin>
          </div>
        </Col>
      ) : (
        (statsData.length > 0 ? statsData : []).map((d) => {
          if (!d?.hidden || d?.visible) {
            return (
              <Col
                key={d?.dataIndex ?? d?.contentData}
                xs={ColTypeSpan[props?.type]?.xs}
                sm={ColTypeSpan[props?.type]?.sm}
                md={ColTypeSpan[props?.type]?.md}
                lg={ColTypeSpan[props?.type]?.lg}
                xl={ColTypeSpan[props?.type]?.xl}
              >
                <Spin spinning={getSettleCompStatsOfEntityLoading ?? false}>
                  <SingleStat
                    loading={getSettleCompStatsOfEntityLoading}
                    className={`${
                      !props?.noClickable &&
                      selectedStatItem?.title === d?.title
                        ? 'active'
                        : ''
                    }`}
                    {...d}
                    clickable={props?.noClickable ? false : d?.clickable}
                    detailType="chsCardInfo"
                    type="drg"
                    args={{
                      ...d?.args,
                      ...params,
                      id: `drg-settle-single-stats-${props?.level}-${
                        d?.dataIndex ?? d?.contentData
                      }`,
                    }}
                  ></SingleStat>
                </Spin>
              </Col>
            );
          }
        })
      )}
    </>
  );
};

export default Stats;
