import {
  CloseOutlined,
  MenuOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
} from '@ant-design/icons';
import { Input, Popconfirm, Tooltip } from 'antd';
import { Emitter } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import React from 'react';
import { SortableHandle } from 'react-sortable-hoc';

const DragHandler = (node) => {
  return SortableHandle(() => <div className={'grab-handle'}>{node}</div>);
};

export const commonMetricColumns = [
  {
    dataIndex: 'PatCnt',
    title: '病人数量',
    visible: true,
    order: 65535,
    orderable: true,
  },
  {
    dataIndex: 'Proportion',
    title: '比例',
    visible: true,
    order: 65536,
    orderable: true,
    dataType: 'Percent',
    render: (node, record, index) => {
      return `${(parseFloat(record?.Proportion) * 100).toFixed(2)}%`;
    },
  },
];

export const metricColumnSettingColumns = [
  {
    key: 'sort',
    dataIndex: '',
    title: '',
    visible: true,
    align: 'center',
    width: '10%',
    readonly: true,
    renderFormItem: ({ index, entity }) => {
      const SortDragHandler = DragHandler(<MenuOutlined />);
      return <SortDragHandler />;
    },
  },
  {
    dataIndex: 'title',
    title: '项目名',
    visible: true,
    align: 'center',
    width: '35%',
    readonly: true,
    renderFormItem: ({ index, entity }) => {
      return <span>{entity['title']}</span>;
    },
  },
  {
    dataIndex: 'customTitle',
    title: '重命名',
    visible: true,
    align: 'center',
    width: '35%',
    renderFormItem: ({ index, entity }) => {
      return (
        <Input.TextArea
          autoSize={{ minRows: 1 }}
          defaultValue={entity['title']}
          placeholder={entity['title']}
        />
      );
    },
  },
  {
    dataIndex: 'operation',
    title: '',
    visible: true,
    align: 'center',
    width: '20%',
    readonly: true,
    renderFormItem: ({ index, entity }) => {
      return (
        <div className={'selected-table-operation-container'}>
          <Tooltip title={'置顶'}>
            <VerticalAlignTopOutlined
              className={'item'}
              onClick={() => {
                Emitter.emit(
                  `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_TOP}#${entity?.tabKey}`,
                  {
                    index: index,
                    entity: entity,
                  },
                );
              }}
            />
          </Tooltip>
          <Tooltip title={'置底'}>
            <VerticalAlignBottomOutlined
              className={'item'}
              onClick={() => {
                Emitter.emit(
                  `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_BOTTOM}#${entity?.tabKey}`,
                  {
                    index: index,
                    entity: entity,
                  },
                );
              }}
            />
          </Tooltip>
          <Tooltip title={'删除'}>
            <CloseOutlined
              className={'item'}
              onClick={() => {
                Emitter.emit(
                  `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_DELETE}#${entity?.tabKey}`,
                  {
                    index: index,
                    entity: entity,
                  },
                );
              }}
            />
          </Tooltip>
        </div>
      );
    },
  },
];
