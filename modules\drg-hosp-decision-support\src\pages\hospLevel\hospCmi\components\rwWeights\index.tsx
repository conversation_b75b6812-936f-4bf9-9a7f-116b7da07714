import CardWithBtns from '@uni/components/src/cardWithBtns/index';
import UniEcharts from '@uni/components/src/echarts/index';
import { Col } from 'antd';
import { CmiPie, CmiLineBar } from '@/echarts/cmi.chart.opts';
import { useEffect, useState } from 'react';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src/commonService';
import { RespVO } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import { UniSelect } from '@uni/components/src/index';
import { areAllPropertiesUndefined } from '@/utils/tools';

const rwRangesData = [
  {
    Code: '0',
    Name: '0<=Rw<1',
  },
  {
    Code: '1',
    Name: '1<=Rw<2',
  },
  {
    Code: '2',
    Name: '2<=Rw<5',
  },
  {
    Code: '3',
    Name: '5<=Rw<10',
  },
  {
    Code: '4',
    Name: '10<=Rw',
  },
];

const RwWeights = ({
  tableParams,
  rwDistributionApi,
  rwDistributionTrendApi,
}) => {
  const [RwRangesSelectedValue, setRwRangesSelectedValue] = useState(['0']);

  // 分布
  const {
    data: ColumnsData,
    loading: getColumnsLoading,
    mutate: mutateColumns,
    run: getColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(rwDistributionApi, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  const {
    data: Data,
    loading: getDataLoading,
    run: getDataReq,
  } = useRequest(
    (data) =>
      uniCommonService(rwDistributionApi, {
        method: 'POST',
        data: {
          ...data,
          RwRangeCode: [],
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 趋势
  const {
    data: TrendColumnsData,
    loading: getTrendColumnsLoading,
    mutate: mutateTrendColumns,
    run: getTrendColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(rwDistributionTrendApi, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  const {
    data: TrendData,
    loading: getTrendDataLoading,
    run: getTrendDataReq,
  } = useRequest(
    (data) =>
      uniCommonService(rwDistributionTrendApi, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // columns
  useEffect(() => {
    if (rwDistributionApi) {
      if (!ColumnsData?.length) getColumnsReq();
    }
  }, [rwDistributionApi]);

  useEffect(() => {
    if (rwDistributionTrendApi) {
      if (!TrendColumnsData?.length) getTrendColumnsReq();
    }
  }, [rwDistributionTrendApi]);

  // data
  useEffect(() => {
    console.log('77777');
    if (!areAllPropertiesUndefined(tableParams)) {
      getDataReq(tableParams);
    }
  }, [tableParams]);

  useEffect(() => {
    console.log('666666666');
    if (!areAllPropertiesUndefined(tableParams)) {
      getTrendDataReq({ ...tableParams, RwRangeCode: RwRangesSelectedValue });
    }
  }, [tableParams, RwRangesSelectedValue]);

  return (
    <>
      <Col xs={24} sm={24} md={24} lg={12} xl={10}>
        <CardWithBtns
          title="RW权重分布"
          content={
            <UniEcharts
              height={250}
              elementId="Pie_RwWeights"
              loading={getDataLoading}
              options={(Data?.length > 0 && CmiPie(Data)) || {}}
            />
          }
          needExport={true}
          exportTitle={'RW权重分布'}
          exportData={Data}
          exportColumns={ColumnsData}
          needModalDetails={true}
          onRefresh={() => {
            getDataReq(tableParams);
          }}
          columnsEditableUrl={rwDistributionApi}
          onColumnChange={(newColumns) => {
            mutateColumns(tableColumnBaseProcessor([], newColumns));
          }}
        />
      </Col>
      <Col xs={24} sm={24} md={24} lg={12} xl={14}>
        <CardWithBtns
          title="RW权重变化趋势"
          content={
            <UniEcharts
              height={250}
              elementId="LineBar_RwWeights"
              loading={getTrendDataLoading}
              options={
                CmiLineBar(
                  TrendData?.map((d) => ({
                    ...d,
                    MonthDate: valueNullOrUndefinedReturnDash(
                      d?.MonthDate,
                      'Month',
                    ),
                  })),
                  ['MonthDate'],
                ) || {}
              }
            />
          }
          extra={
            <UniSelect
              style={{ minWidth: '100px' }}
              disabled={getTrendDataLoading}
              mode={'multiple'}
              allowClear={false}
              placeholder={'请选择'}
              optionValueKey={'Code'}
              optionNameKey={'Name'}
              defaultValue={RwRangesSelectedValue}
              maxTagCount={2}
              dataSource={rwRangesData}
              removeIcon={false}
              onBlur={(res) => {
                console.log('onBlur11111', res);
                getTrendDataReq({
                  ...tableParams,
                  RwRangeCode: RwRangesSelectedValue,
                });
              }}
              onChange={(value) => setRwRangesSelectedValue(value)}
            />
          }
          needExport={true}
          exportTitle={'RW权重变化趋势'}
          exportData={TrendData}
          exportColumns={TrendColumnsData}
          needModalDetails={true}
          onRefresh={() => {
            console.log('onRefresh1111');
            getTrendDataReq({
              ...tableParams,
              RwRangeCode: RwRangesSelectedValue,
            });
          }}
          columnsEditableUrl={rwDistributionTrendApi}
          onColumnChange={(newColumns) => {
            mutateColumns(tableColumnBaseProcessor([], newColumns));
          }}
        />
      </Col>
    </>
  );
};

export default RwWeights;
