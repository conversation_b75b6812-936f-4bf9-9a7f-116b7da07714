export const HospCmiStat = [
  {
    contentData: 'PatCnt',
    clickable: true,
    footerYoy: true,
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
  },
  {
    contentData: 'Cmi',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'Cm',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'DrgCnt',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgInPeriod',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '均次费用',
    contentData: 'AvgTotalFee',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '平均药费',
    dataIndex: 'AvgMedicineFee',
    contentData: 'AvgMedicineFee',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '药占比',
    contentData: 'MedicineFeeRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '平均耗材费',
    contentData: 'AvgMaterialFee',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '材料费占比',
    contentData: 'MaterialFeeRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '费用指数',
    contentData: 'Cei',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '时间指数',
    contentData: 'Tei',
    clickable: true,
    footerYoy: true,
  },
  {
    // title: '低风险死亡率',
    contentData: 'LowRiskDeathRatio',
    clickable: true,
    footerYoy: true,
  },
];

const NormalAxisOpts = [
  'PatCnt',
  'Cmi',
  'Cm',
  'DrgCnt',
  'AvgInPeriod',
  'AvgTotalFee',
  'AvgMedicineFee',
  'MedicineFeeRatio',
  'AvgMaterialFee',
  'MaterialFeeRatio',
  'Cei',
  'Tei',
  'LowRiskPatCnt',
  'LowRiskDeathCnt',
  'LowRiskDeathRatio',
];

export const HospAxisOpts = [...NormalAxisOpts];

export const CliDeptAxisOpts = [...NormalAxisOpts];

export const MedTeamAxisOpts = [...NormalAxisOpts];

export const DoctorAxisOpts = [...NormalAxisOpts];

export const DefaultOpts = {
  xAxis: 'Cmi',
  yAxis: 'AvgTotalFee',
};

export enum ReqActionType {
  // 全院CMI概况
  CmiOfHosp = 'HospCmi/BundledCmi',
  CmiByHosp = 'HospDrg/CmiByHosp',
  CmiByCliDept = 'HospDrg/CmiByCliDept',
  CmiTrendOfHosp = 'HospDrg/CmiTrendOfHosp',
  RwDistributionOfHosp = 'HospDrg/RwDistributionOfHosp',
  ADrgCompositionOfHosp = 'HospDrg/ADrgCompositionOfHosp',
}

export const CmiBmChartSelectOptions = [
  'Cmi',
  'DrgCnt',
  'AvgInPeriod',
  'AvgTotalFee',
  'AvgMedicineFee',
  'MedicineFeeRatio',
  'AvgMaterialFee',
  'MaterialFeeRatio',
  'Cei',
  'Tei',
  'LowRiskDeathRatio',
];
