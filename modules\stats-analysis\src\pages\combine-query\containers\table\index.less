.combine-query-detail-table-container {
  .detail-table-operations {
    display: flex;
    flex-direction: row;
    align-items: center;

    button:not(:first-child) {
      margin-left: 10px;
    }
  }

  tbody {
    margin-bottom: 70px;
  }

  tr {
    width: 100%;
  }
}

.combo-table-detail-summary-container {
  border-bottom: 1px solid #f0f0f0;
  min-height: 50px;
  height: 50px;
  position: sticky;
  bottom: 0;
  background-color: #ffffff;
  z-index: 1000;

  td {
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    padding: 2px 8px !important;
  }

  .summary-item {
    display: flex;
    flex-direction: column;
  }
}
