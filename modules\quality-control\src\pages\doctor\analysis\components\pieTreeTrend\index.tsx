import CardWithBtns from '@uni/components/src/cardWithBtns/index';
import UniEcharts from '@uni/components/src/echarts/index';
import { Col, Row, Space, Tree, Spin, Tag, Input, message } from 'antd';
import { useEffect, useState, useMemo, useRef } from 'react';
import { useModel, useRequest } from 'umi';
import { isEmptyValues } from '@uni/utils/src/utils';
import { uniCommonService } from '@uni/services/src/commonService';
import { RespVO } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { DownOutlined } from '@ant-design/icons';
import _ from 'lodash';
import { SelectedTrendsLine, RuleTypeTreemap } from './chart.opts';
import CardEchart from '@uni/components/src/cardEchart';
import './index.less';
import { ExportColumns } from './columns';
import IconBtn from '@uni/components/src/iconBtn/index';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import React from 'react';

const { Search } = Input;

interface Props {
  tableParams: any;
  detailAction?: any;
}

const PieTreeTrend = ({
  tableParams,
  detailAction,
}: // api
Props) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  const cmrInfoViewRef = useRef(null);

  const [ruleTypeData, setRuleTypeData] = useState([]);

  const [problematicRecordCnt, setProblematicRecordCnt] = useState(null);

  const [clickRule, setClickRule] = useState<any>({});

  const [defaultTreeData, setDefaultTreeData] = useState([]);

  const [trendData, setTrendData] = useState([]);

  const [exportData, setExportData] = useState([]);

  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // tree
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const dataList: { key; title: string }[] = [];

  const {
    loading: getQcStatsMonthlyTrendLoading,
    run: getQcStatsMonthlyTrendReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/Emr/QcResultStats/QcStatsMonthlyTrend', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.length) {
            setTrendData(res?.data);
          } else {
            setTrendData([]);
          }
        }
      },
    },
  );

  const {
    data: qcStatData,
    loading: getQcStatLoading,
    run: getQcStatReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/Emr/QcResultStats/GetQcStats', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.length) {
            if (res.data[0]?.RuleTypeStats?.length) {
              let data = _.orderBy(
                res.data[0]?.['RuleTypeStats'],
                ['RuleTypeCnt'],
                'desc',
              );

              // tree 默认 选中第一个
              setSelectedKeys(['key-0']);
              setClickRule({
                args: {
                  RuleType: data[0].RuleType,
                },
              });

              setProblematicRecordCnt(res.data[0]?.['ProblematicCardCnt']);

              // 展示树
              let treeData = _.map(data, (data1, index1) => {
                let children = [];
                if (data1.SubTypeStats.length !== 0) {
                  children = _.map(
                    _.orderBy(data1.SubTypeStats, ['SubTypeCnt'], 'desc'),
                    (data2, index2) => {
                      return {
                        key: `key-${index1}-${index2}`,
                        title: data2.SubType,
                        name: data2.SubType,
                        value: data2.SubTypeCnt,
                        ratio: data2.SubTypeRate,
                        args: {
                          RuleType: data1.RuleType,
                          SubType: data2.SubType,
                        },
                      };
                    },
                  );
                }
                return {
                  key: `key-${index1}`,
                  title: data1.RuleType,
                  name: data1.RuleType,
                  value: data1.RuleTypeCnt,
                  ratio: data1.RuleTypeRate,
                  children,
                  args: { RuleType: data1.RuleType },
                };
              });
              // treeData = [
              //     {
              //         key: 'key-main',
              //         title: '全部',
              //         name: '全部',
              //         value: res.data[0]?.['ProblematicCardCnt'],
              //         children: treeData,
              //         args: {},
              //     },
              // ];

              // 导出树
              let formatExportData = [];
              _.map(data, (data1, index1) => {
                formatExportData.push({
                  name: data1?.RuleType,
                  value: data1?.RuleTypeCnt,
                  ratio: data1?.RuleTypeRate,
                });
                if (data1?.SubTypeStats?.length) {
                  _.map(
                    _.orderBy(data1?.SubTypeStats, ['SubTypeCnt'], 'desc'),
                    (data2, index2) => {
                      formatExportData.push({
                        name: `${data1?.RuleType}-${data2?.SubType}`,
                        value: data2?.SubTypeCnt,
                        ratio: data2?.SubTypeRate,
                      });
                    },
                  );
                }
              });
              setExportData(formatExportData);

              setDefaultTreeData(treeData);
              // 饼图
              setRuleTypeData(
                _.orderBy(res.data[0].RuleTypeStats, 'RuleTypeCnt', 'desc'),
              );
            } else {
              setDefaultTreeData([]);
              setProblematicRecordCnt(null);
            }

            return res.data[0];
          }
          return res.data;
        }
      },
    },
  );

  // Columns
  const {
    data: QcStatsMonthlyTrendColumnsData,
    mutate: mutateQcStatsMonthlyTrendColumns,
    run: getQcStatsMonthlyTrendColumnsReq,
  } = useRequest(
    () =>
      uniCommonService('Api/Dmr/DmrQcStats/QcStatsMonthlyTrend', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  // columns
  useEffect(() => {
    if (!QcStatsMonthlyTrendColumnsData?.length)
      getQcStatsMonthlyTrendColumnsReq();
  }, []);

  useEffect(() => {
    if (tableParams) getQcStatReq(tableParams);
  }, [tableParams]);

  // tree
  const generateList = (data: any[]) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { key, title } = node;
      dataList.push({ key, title });
      if (node.children) {
        generateList(node.children);
      }
    }
  };
  generateList(defaultTreeData);
  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  const getParentKey = (key: React.Key, tree: any[]): React.Key => {
    let parentKey: React.Key;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    return parentKey!;
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (value?.length === 0) {
      setExpandedKeys([]);
    } else {
      const newExpandedKeys = dataList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            return getParentKey(item.key, defaultTreeData);
          }
          return null;
        })
        .filter((item, i, self) => item && self.indexOf(item) === i);
      setExpandedKeys(newExpandedKeys as React.Key[]);
    }
    setSearchValue(value);
    setAutoExpandParent(true);
  };

  const treeData = useMemo(() => {
    const loop = (data: any[]) =>
      data.map((item) => {
        const strTitle = item.title as string;
        const index = strTitle.indexOf(searchValue);
        const beforeStr = strTitle.substring(0, index);
        const afterStr = strTitle.slice(index + searchValue.length);
        const title =
          index > -1 ? (
            <span>
              {beforeStr}
              <span className="site-tree-search-value">{searchValue}</span>
              {afterStr}
            </span>
          ) : (
            <span>{strTitle}</span>
          );
        if (item.children) {
          return { ...item, title, children: loop(item.children) };
        }

        return {
          ...item,
          title,
        };
      });
    return loop(defaultTreeData);
  }, [searchValue, defaultTreeData]);

  const onSelect = (selectedKeys, info) => {
    setSelectedKeys(selectedKeys);
    setClickRule({
      args: info.selectedNodes[0]?.args,
    });
  };

  useEffect(() => {
    if (
      Object.keys(clickRule)?.length !== 0 &&
      clickRule?.args &&
      Object.keys(clickRule?.args)?.length
    )
      getQcStatsMonthlyTrendReq({ ...tableParams, ...clickRule.args });
  }, [clickRule, tableParams]);

  const SelectedTrendsLineOption = useMemo(() => {
    if (trendData && trendData?.length > 0) {
      return SelectedTrendsLine(trendData, 'YearMonth');
    }
    return {};
  }, [trendData]);

  React.useImperativeHandle(cmrInfoViewRef, () => {
    return {
      onEmrInfoViewClick: (record) => {
        onEmrInfoViewClick(record);
      },
    };
  });

  // 病例页
  const onEmrInfoViewClick = (record: any) => {
    (global?.window as any)?.eventEmitter?.emit('DMR_Index_STATUS', {
      status: true, // 弹窗开启状态
      hisId: record.HisId, // 医嘱ID
    });
  };

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={24} md={12} lg={10} xl={15}>
          <CardWithBtns
            title={
              <>
                <Space>
                  <span>问题变化趋势</span>
                  {/* <Tag color="blue" style={{ borderColor: 'transparent' }}>
                                    问题病例数：{problematicRecordCnt || ''}
                                </Tag> */}
                </Space>
              </>
            }
            // extra={<>
            //     <ExportIconBtn
            //         btnText={'规则分类导出'}
            //         btnType={'text'}
            //         isBackend={false}
            //         frontendObj={{
            //             columns: ExportColumns,
            //             dataSource: exportData || [],
            //             fileName: '规则分类导出',
            //         }}
            //         btnDisabled={exportData?.length === 0}
            //     />
            // </>}
            content={
              <div
                className="check-result-container"
                id="check-result-container"
              >
                <Row gutter={16}>
                  <Col span={8} className="border-right">
                    <Spin spinning={getQcStatLoading}>
                      <div className="left-container">
                        <Search
                          style={{ padding: '0 0px 8px' }}
                          placeholder="搜索关键字"
                          onChange={onChange}
                        />
                        <Tree
                          showLine
                          switcherIcon={
                            <DownOutlined
                              onPointerEnterCapture
                              onPointerLeaveCapture
                            />
                          }
                          blockNode
                          onExpand={onExpand}
                          expandedKeys={
                            expandedKeys.length ? expandedKeys : ['key-main']
                          }
                          selectedKeys={selectedKeys}
                          autoExpandParent={autoExpandParent}
                          treeData={treeData}
                          onSelect={onSelect}
                          height={360}
                          titleRender={(nodeData: any) => {
                            return (
                              <>
                                <Space>
                                  {nodeData?.title}
                                  <Tag>
                                    {nodeData?.value} /{' '}
                                    {(+nodeData?.ratio * 100)?.toFixed(2)}%
                                  </Tag>
                                </Space>
                                <IconBtn
                                  type="details"
                                  style={{ margin: '0 10px' }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (detailAction) {
                                      detailAction(nodeData);
                                    }
                                  }}
                                />
                              </>
                            );
                          }}
                        />
                      </div>
                    </Spin>
                  </Col>
                  <Col span={16}>
                    <UniEcharts
                      elementId={`trend_chart`}
                      height={400}
                      loading={getQcStatsMonthlyTrendLoading || false}
                      options={SelectedTrendsLineOption}
                    />
                  </Col>
                </Row>
              </div>
            }
            needExport={true}
            exportTitle={`${clickRule?.args?.RuleType || ''}-${
              clickRule?.args?.SubType || ''
            } 问题变化趋势`}
            exportData={trendData}
            exportColumns={QcStatsMonthlyTrendColumnsData}
            needModalDetails={true}
            onRefresh={() => {
              getQcStatsMonthlyTrendReq(tableParams);
            }}
          />
        </Col>
        <Col xs={24} sm={24} md={12} lg={10} xl={9}>
          <CardEchart
            title={
              <>
                <Space>
                  <span>问题分布</span>
                  <Tag color="blue" style={{ borderColor: 'transparent' }}>
                    问题病例数：{problematicRecordCnt || ''}
                  </Tag>
                </Space>
              </>
            }
            height={400}
            elementId="treemap"
            loading={getQcStatLoading}
            options={RuleTypeTreemap(defaultTreeData)}
            needExport={true}
            exportTitle={'问题分布'}
            exportData={exportData}
            exportColumns={ExportColumns}
            needModalDetails={true}
            onRefresh={() => {
              getQcStatReq(tableParams);
            }}
          ></CardEchart>
        </Col>
      </Row>
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          console.log('record000', record);
          if (!isEmptyValues(record?.HisId)) {
            cmrInfoViewRef?.current?.onEmrInfoViewClick(record);
          } else {
            message.error('没有可查看的医生明细--HisId为空');
          }
        }}
      />
    </>
  );
};
export default PieTreeTrend;
