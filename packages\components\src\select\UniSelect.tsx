import React, { FC, useEffect, useRef, useState } from 'react';
import { Button, Checkbox, Divider, Space } from 'antd';
import Select from './AntdSelect';
import { v4 as uuidv4 } from 'uuid';
import './index.less';
import { SelectProps } from 'antd/lib/select';
import {
  pinyinInitialSearch,
  searchFunctionGetter,
} from '@uni/utils/src/pinyin';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { isEmpty, isArray } from 'lodash';
import orderBy from 'lodash/orderBy';
import isEqual from 'lodash/isEqual';
import { useUpdateEffect } from 'ahooks';
import { isEmptyValues } from '@uni/utils/src/utils';

const { Option } = Select;

export interface UniSelectProps extends SelectProps {
  formItemId?: string; //用于 病案登记 / 结算清单登记 中的error标识
  width?: number;
  dataSource?: any[];

  dataSourceProcessor?: (dataSource: any[]) => any[];
  optionTitleKey?: string;
  optionNameKey?: string;
  optionValueKey?: string;
  className?: string;
  style?: React.CSSProperties;
  onChange?: (value: any, option?: any) => void;
  onFilterOptions?: (inputValue, option) => boolean;
  onFilterOptionsCapture?: (inputValue, option) => boolean;
  value?: string | string[];
  allowClear?: boolean;
  enablePinyinSearch?: boolean;
  placeholder?: string;
  showSearch?: boolean;
  label?: string;
  enableSelectAll?: boolean;
  enableSelectInternal?: boolean;
  enableSelectSurgery?: boolean;
  enableOptionAllInData?: boolean; // 选项顶部加一个选项 全选 value为 % 在特殊情况下 与其他选项*不*冲突
  onSelectAllCheckboxChange?: (checked: boolean) => void;
  showAction?: ('focus' | 'click')[];
  requiredStatus?: boolean;

  // option label Tooltip
  optionLabelTooltip?: boolean;

  invalidNotDisable?: boolean;
  selectorHideInValid?: boolean;

  leftStartSearch?: boolean;

  [key: string]: any;
}

interface SelectAllDropdownProps {
  containerRef: any;
  formInstance?: any;
  selectorRef: any;
  menu: any;
  mode: string;
  onSelectAll: (checked: boolean, options: any[], selectorKey: string) => void;
  options: any[];
  selectorKey: string;
}

export const SelectAllDropdown = (props: SelectAllDropdownProps) => {
  console.log('SelectAllDropdown', props);

  const [selectedAllChecked, setSelectedAllChecked] = useState(false);

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      onSelectorSearch: (value) => {
        setTimeout(() => {
          let dataSources = props?.selectorRef?.current?.getDisplayOptions();
          let filteredKeys = dataSources?.map((item) => item?.value)?.sort();

          const formValue = props?.formInstance?.current?.getFieldFormatValue(
            props?.selectorKey,
          );

          if (isEmptyValues(formValue)) {
            setSelectedAllChecked(false);
          } else {
            console.log('Checked', isEqual(filteredKeys, formValue));
            setSelectedAllChecked(isEqual(filteredKeys, formValue));
          }
        }, 100);
      },
    };
  });

  return (
    <>
      {props?.menu}
      {(props?.mode === 'multiple' || props?.mode === 'tags') && (
        <>
          <Divider style={{ margin: '8px 0' }} />
          <Space style={{ padding: '0 8px 4px' }}>
            <Checkbox
              checked={selectedAllChecked}
              onChange={(event) => {
                let dataSources =
                  props?.selectorRef?.current?.getDisplayOptions();
                let filteredKeys = dataSources
                  ?.map((item) => item?.value)
                  ?.sort();

                props?.onSelectAll(
                  event?.target?.checked,
                  filteredKeys,
                  props?.selectorKey,
                );
              }}
            >
              全选
            </Checkbox>
          </Space>
        </>
      )}
    </>
  );
};

const UniSelect: FC<UniSelectProps> = ({
  formItemId,
  width,
  className,
  style,
  optionTitleKey,
  optionNameKey = 'name',
  optionValueKey = 'value',
  dataSource,
  onChange,
  onFilterOptions,
  onFilterOptionsCapture,
  enablePinyinSearch = true,
  label,
  dataSourceProcessor,
  enableSelectAll,
  enableSelectInternal,
  enableSelectSurgery,
  enableOptionAllInData,
  onSelectAllCheckboxChange,
  showAction,
  optionLabelTooltip = true,
  requiredStatus = false,
  invalidNotDisable = false,
  selectorHideInValid = false,
  leftStartSearch,
  ...restProps
}: UniSelectProps) => {
  const [selectedValue, setSelectedValue] = useState(undefined);

  const [selectedAllChecked, setSelectedAllChecked] = useState(false);
  const [selectedInternChecked, setSelectedInternChecked] = useState(false);
  const [selectedSurgeryChecked, setSelectedSurgeryChecked] = useState(false);

  const [searchValue, setSearchValue] = useState('');

  const selectorRef = useRef(null);

  let filteredValues = [];

  useEffect(() => {
    setSelectedValue(restProps?.value);
  }, [restProps?.value]);

  const disableInValidOptions = (dataSource: any) => {
    dataSource?.forEach((item) => {
      item['Sort'] = item?.['Sort'] ?? 999;
      if (invalidNotDisable === false && isEmptyValues(item['disabled'])) {
        item['disabled'] = item?.IsValid === false;
      }
    });

    return orderBy(
      dataSource?.filter((item) => {
        if (selectorHideInValid === true) {
          return item?.IsValid === true;
        }

        return true;
      }),
      ['Sort', 'disabled'],
      ['asc', 'asc'],
    );
  };

  const onSelectAllChange = (e: CheckboxChangeEvent) => {
    console.log('selectorRef', selectorRef?.current);

    e?.stopPropagation();
    setSelectedInternChecked(false);
    setSelectedSurgeryChecked(false);
    if (onSelectAllCheckboxChange) {
      onSelectAllCheckboxChange(e.target.checked);
    } else {
      let dataSources = selectorRef?.current?.getDisplayOptions();
      let allValues = [];
      if (e.target.checked) {
        let actualDataSource = dataSources?.filter((item) => {
          if (invalidNotDisable === false) {
            return item?.data?.disabled === false;
          }

          return true;
        });
        allValues = actualDataSource
          ?.map((item) => {
            return item?.value;
          })
          ?.sort();
      } else {
        setSelectedAllChecked(false);
      }

      // // 当出现搜索的时候用filteredValues
      // if(filteredValues?.length > 0) {
      //   allValues = filteredValues;
      // }

      setSelectedValue(allValues);
      onChange && onChange(allValues);

      // filteredValues = [];
    }

    selectorRef?.current?.onContainerBlurOrigin();
  };

  const onSelectInternChange = (e: CheckboxChangeEvent) => {
    e?.stopPropagation();
    // 拿到所有的值
    let dataSources = selectorRef?.current?.getDisplayOptions();
    let allValues = [];
    let list = [];
    setSelectedSurgeryChecked(false);
    if (e.target.checked) {
      let actualDataSource = dataSources?.filter((item) => {
        if (invalidNotDisable === false) {
          return item?.data?.disabled === false;
        }

        return true;
      });
      list = actualDataSource?.filter((item) => {
        return item?.data?.ExtraProperties?.DeptType === '0';
      });
      allValues = list
        ?.map((item) => {
          return item?.value;
        })
        ?.sort();

      if (allValues?.length > 0) {
        setSelectedInternChecked(true);
      }
    } else {
      setSelectedInternChecked(false);
    }
    setSelectedValue(allValues);
    onChange && onChange(allValues);
    selectorRef?.current?.onContainerBlurOrigin();
  };

  const onSelectSurgeryChange = (e: CheckboxChangeEvent) => {
    e?.stopPropagation();
    // 拿到所有的值
    let dataSources = selectorRef?.current?.getDisplayOptions();
    let allValues = [];
    let list = [];
    setSelectedInternChecked(false);
    if (e.target.checked) {
      let actualDataSource = dataSources?.filter((item) => {
        if (invalidNotDisable === false) {
          return item?.data?.disabled === false;
        }

        return true;
      });
      list = actualDataSource?.filter((item) => {
        return item?.data?.ExtraProperties?.DeptType === '1';
      });
      allValues = list
        ?.map((item) => {
          return item?.value;
        })
        ?.sort();

      if (allValues?.length > 0) {
        setSelectedSurgeryChecked(true);
      }
    } else {
      setSelectedSurgeryChecked(false);
    }
    setSelectedValue(allValues);
    onChange && onChange(allValues);
    selectorRef?.current?.onContainerBlurOrigin();
  };

  const onValueChangeAliasSelectAllChecked = (value: string[]) => {
    if (value && value?.length > 0) {
      let actualDataSource = disableInValidOptions(
        dataSourceProcessor
          ? dataSourceProcessor(dataSource || [])
          : dataSource || [],
      );

      // 先行做数量对比
      setSelectedAllChecked(value?.length === actualDataSource?.length);
    } else {
      setSelectedAllChecked(false);
    }
  };

  useUpdateEffect(() => {
    let dataSources = selectorRef?.current?.getDisplayOptions();
    let selectedKeys = dataSources?.map((item) => item?.value)?.sort();

    console.log('selectedKeys', selectedKeys, restProps.value ?? selectedValue);

    if (isEmptyValues(restProps.value ?? selectedValue)) {
      setSelectedAllChecked(false);
      setSelectedSurgeryChecked(false);
      setSelectedInternChecked(false);
    } else {
      setSelectedAllChecked(
        isEqual(selectedKeys, restProps.value ?? selectedValue),
      );
    }
  }, [searchValue]);

  return (
    <div
      id={formItemId}
      style={width ? { width: width } : {}}
      className={'select-container'}
    >
      {label && <label>{label}</label>}
      <Select
        {...restProps}
        enableSelectAll={enableSelectAll ?? false}
        ref={selectorRef}
        id={`${formItemId ?? `Header#${restProps?.valueKey}`}#rc-select`}
        className={`select ${className}`}
        style={{ ...{}, ...style }}
        showSearch
        showAction={showAction ?? ['focus', 'click']}
        allowClear={restProps?.allowClear ?? true}
        value={restProps.value ?? selectedValue}
        dropdownMatchSelectWidth={restProps?.dropdownMatchSelectWidth}
        virtual={restProps?.virtual ?? true}
        getPopupContainer={(trigger) =>
          (restProps?.getPopupContainer &&
            restProps?.getPopupContainer(trigger)) ||
          document.body
        }
        onSearch={(value) => {
          restProps?.onSearch && restProps?.onSearch(value);

          setSearchValue(value);
        }}
        placeholder={restProps?.placeholder || ''}
        dropdownRender={(menu) => {
          return restProps?.dropdownRender ? (
            restProps?.dropdownRender(menu)
          ) : (
            <>
              {menu}
              {enableSelectAll &&
                (restProps?.mode === 'multiple' ||
                  restProps?.mode === 'tags') && (
                  <>
                    <Divider style={{ margin: '8px 0' }} />
                    <Space style={{ padding: '0 8px 4px' }}>
                      <Checkbox
                        checked={selectedAllChecked}
                        onChange={onSelectAllChange}
                      >
                        全选
                      </Checkbox>
                      {enableSelectInternal ? (
                        <>
                          <Checkbox
                            checked={selectedInternChecked}
                            onChange={onSelectInternChange}
                          >
                            内科
                          </Checkbox>
                        </>
                      ) : null}
                      {enableSelectSurgery ? (
                        <>
                          <Checkbox
                            checked={selectedSurgeryChecked}
                            onChange={onSelectSurgeryChange}
                          >
                            外科
                          </Checkbox>
                        </>
                      ) : null}
                    </Space>
                  </>
                )}
            </>
          );
        }}
        filterOption={(inputValue, option) => {
          let filterOptionsResult = true;

          if (onFilterOptionsCapture) {
            return onFilterOptionsCapture(inputValue, option);
          }
          if (onFilterOptions) {
            filterOptionsResult = onFilterOptions(inputValue, option);
          }
          return (
            (filterOptionsResult &&
              option &&
              option?.[optionNameKey]
                ?.toString()
                ?.toLowerCase()
                ?.[searchFunctionGetter(false, leftStartSearch)](
                  inputValue.toLowerCase(),
                )) ||
            option?.[optionValueKey]
              ?.toString()
              ?.toLowerCase()
              ?.[searchFunctionGetter(false, leftStartSearch)](
                inputValue?.toLowerCase(),
              ) ||
            (enablePinyinSearch &&
              pinyinInitialSearch(
                option?.[optionTitleKey || optionNameKey]
                  ?.toString()
                  ?.toLowerCase(),
                inputValue.toLowerCase(),
              ))
          );

          // if(filterResult) {
          //   filteredValues.push(option?.value);
          // }
        }}
        onChange={(value: any, option: any) => {
          setSelectedValue(value);
          onChange && onChange(value, option);

          if (
            enableSelectAll &&
            (restProps?.mode === 'multiple' || restProps?.mode === 'tags') &&
            isEmpty(searchValue)
          ) {
            onValueChangeAliasSelectAllChecked(value);
          }

          if (
            enableSelectInternal &&
            (restProps?.mode === 'multiple' || restProps?.mode === 'tags') &&
            isEmpty(searchValue)
          ) {
            let dataSources = selectorRef?.current?.getDisplayOptions();
            let allValues = dataSources
              ?.filter((item) => {
                if (invalidNotDisable === false) {
                  return item?.data?.disabled === false;
                }

                return true;
              })
              ?.filter((item) => {
                return item?.data?.ExtraProperties?.DeptType === '0';
              })
              ?.map((item) => item?.value)
              ?.sort();

            setSelectedInternChecked(isEqual(allValues, value));
          }
          if (
            enableSelectSurgery &&
            (restProps?.mode === 'multiple' || restProps?.mode === 'tags') &&
            isEmpty(searchValue)
          ) {
            let dataSources = selectorRef?.current?.getDisplayOptions();
            let allValues = dataSources
              ?.filter((item) => {
                if (invalidNotDisable === false) {
                  return item?.data?.disabled === false;
                }

                return true;
              })
              ?.filter((item) => {
                return item?.data?.ExtraProperties?.DeptType === '1';
              })
              ?.map((item) => item?.value)
              ?.sort();

            setSelectedSurgeryChecked(isEqual(allValues, value));
          }
        }}
        options={
          dataSource
            ? enableOptionAllInData
              ? [
                  {
                    title: '全选',
                    label: '全选',
                    Name: '全选',
                    value: '%',
                    Code: '%',
                  },
                  ...disableInValidOptions(
                    dataSourceProcessor
                      ? dataSourceProcessor(dataSource || [])
                      : dataSource || [],
                  )?.map((item) => {
                    return {
                      ...item,
                      title: item[optionTitleKey || optionNameKey],
                      value: item[optionValueKey],
                      label: item?.['label'] ?? item[optionNameKey],
                    };
                  }),
                ]
              : disableInValidOptions(
                  dataSourceProcessor
                    ? dataSourceProcessor(dataSource || [])
                    : dataSource || [],
                )?.map((item) => {
                  return {
                    ...item,
                    title: item[optionTitleKey || optionNameKey],
                    value: item[optionValueKey],
                    label: item?.['label'] ?? item[optionNameKey],
                  };
                })
            : undefined
        }
      >
        {restProps?.children}
      </Select>
    </div>
  );
};

export default React.memo(UniSelect);
