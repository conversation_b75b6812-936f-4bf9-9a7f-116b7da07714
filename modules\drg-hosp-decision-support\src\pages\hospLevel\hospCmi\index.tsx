import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { Tabs, Select, Row, Col } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { uniCommonService } from '@uni/services/src';
import Stats from '@/components/stats/index';
import RwWeights from './components/rwWeights/index';
import SingleColumnTable from '@/components/SingleColumnTable/index';
import DiseaseStructure from './components/diseaseStructure/index';
import BCGMatrixAndTable from '@/components/BCGMatrixAndTable/index';
import BmTable from '@/components/BmTable/index';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';
import {
  CliDeptAxisOpts,
  DefaultOpts,
  DoctorAxisOpts,
  MedTeamAxisOpts,
  CmiBmChartSelectOptions,
} from './constants';
import { HospCmiStat } from './constants';
import { RespVO } from '@uni/commons/src/interfaces';
import { areAllPropertiesUndefined } from '@/utils/tools';

const HospCmi = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster');
  const { dateRange, hospCodes } = searchParams;
  console.log('searchParams123456', searchParams);

  const [tableParams, setTableParams] = useState(undefined);
  const [activeKey, setActiveKey] = useState('statistic');
  const [drawerVisible, setDrawerVisible] = useState(undefined);
  const [selectedDoctorValue, setSelectedDoctorValue] = useState(undefined);

  useEffect(() => {
    setSelectedDoctorValue(dictData?.['DoctorType']?.at(0)?.Code);
  }, [dictData?.['DoctorType']]);

  const {
    data: BundledData,
    loading: getBundledDataLoading,
    run: getBundledDataReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/v2/Drgs/HospCmi/BundledCmi', {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) =>
        res?.code === 0 && res?.statusCode === 200 ? res.data : [],
    },
  );

  useDeepCompareEffect(() => {
    const params = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
    };
    console.log('params123456', params);
    setTableParams(params);
    if (params && !areAllPropertiesUndefined(params)) {
      getBundledDataReq(params);
    }
  }, [dateRange, hospCodes]);

  const renderDoctorTypeSelect = () => (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <label>医生类型：</label>
      <Select
        options={dictData?.['DoctorType']}
        style={{ width: '200px' }}
        fieldNames={{ value: 'Code', label: 'Name' }}
        placeholder="请选择"
        allowClear={false}
        value={selectedDoctorValue}
        onChange={(value) => setSelectedDoctorValue(value)}
      />
    </div>
  );

  const tabItems = [
    {
      key: 'statistic',
      label: '全院综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api="Api/v2/Drgs/HospCmi/BundledCmi"
              trendApi="Api/v2/Drgs/HospCmi/CmiTrend"
              defaultSelectItem="Cmi"
              columns={HospCmiStat}
              type="col-xl-6"
              tabKey={activeKey}
              bundledData={BundledData || []}
              tableParams={tableParams}
              loading={getBundledDataLoading}
            />
          </Col>
          <RwWeights
            tableParams={tableParams}
            rwDistributionApi="Api/v2/Drgs/HospCmi/RwDistribution"
            rwDistributionTrendApi="Api/v2/Drgs/HospCmi/RwDistributionTrend"
          />
          <SingleColumnTable
            title="全院CMI"
            args={{ api: 'Api/v2/Drgs/HospCmi/CmiByHosp' }}
            tableParams={tableParams}
            dictData={dictData}
            category="HospName"
            type="table"
            // visibleValueKeys={['HospName', 'Cmi', 'PatCnt']}
            visibleValueKeys={[]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.HospName,
                args: {
                  Sdate: dateRange?.at(0),
                  Edate: dateRange?.at(1),
                  HospCode: [record?.HospCode],
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
                dictData,
              });
            }}
          />
          <Col span={24}>
            <Row gutter={[16, 16]}>
              <DiseaseStructure
                tableParams={tableParams}
                api="Api/v2/Drgs/HospADrgComposition/BundledADrgComposition"
              />
            </Row>
          </Col>
        </Row>
      ),
    },
    {
      key: 'hosp_deptAnalysis',
      label: '科室综合分析',
      children: (
        <BCGMatrixAndTable
          title="临床科室效率"
          tableTitle="临床科室CMI"
          type="hosp"
          emitter={EventConstant.DEPT_TABLE_ROW_CLICK}
          category="CliDeptName"
          axisOpts={CliDeptAxisOpts}
          defaultAxisOpt={DefaultOpts}
          args={{
            api: 'Api/v2/Drgs/CliDeptCmi/CmiByCliDept',
            extraApiArgs: { CliDepts: [] },
            columns: [],
          }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.CliDeptName,
              args: { ...tableParams, CliDepts: [record?.CliDept] },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
              dictData,
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_medTeamAnalysis',
      label: '医疗组综合分析',
      children: (
        <BCGMatrixAndTable
          title="医疗组效率"
          tableTitle="医疗组CMI"
          type="hosp"
          emitter={EventConstant.MED_TEAM_TABLE_ROW_CLICK}
          category="MedTeamName"
          axisOpts={MedTeamAxisOpts}
          defaultAxisOpt={DefaultOpts}
          args={{
            api: 'Api/v2/Drgs/MedTeamCmi/CmiByMedTeam',
            extraApiArgs: { medTeams: [] },
            columns: [],
          }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.MedTeamName,
              args: { ...tableParams, MedTeams: [record?.MedTeam] },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
              dictData,
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <BCGMatrixAndTable
          title="医生效率"
          tableTitle="医生CMI"
          type="hosp"
          emitter={EventConstant.DOCTOR_TABLE_ROW_CLICK}
          category="DoctorName"
          axisOpts={DoctorAxisOpts}
          defaultAxisOpt={DefaultOpts}
          args={{
            api: 'Api/v2/Drgs/DoctorCmi/CmiByDoctor',
            extraApiArgs: {
              doctorCodes: [],
              DoctorType: (selectedDoctorValue as string)?.toLocaleUpperCase(),
            },
            columns: [],
          }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.DoctorName,
              args: {
                ...tableParams,
                DoctorCodes: [record?.DoctorCode],
                DoctorType: (record?.DoctorType as string)?.toLocaleUpperCase(),
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
              dictData,
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_BmAnalysis',
      label: '标杆值',
      children: (
        <BmTable
          tableOrderKey="Cmi"
          tableParams={tableParams}
          bundleData={BundledData}
          api="Api/v2/Drgs/HospCmi/CmiHospBm"
          BmChartSelectOptions={CmiBmChartSelectOptions}
        />
      ),
    },
  ];

  return (
    <>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={setActiveKey}
        tabBarExtraContent={{
          right:
            activeKey === 'hosp_doctorAnalysis' && renderDoctorTypeSelect(),
        }}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({ hisId: record?.HisId, type: 'drg' });
        }}
      />
      <DrawerCardInfo
        type="DrgAndHqms"
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </>
  );
};

export default HospCmi;
