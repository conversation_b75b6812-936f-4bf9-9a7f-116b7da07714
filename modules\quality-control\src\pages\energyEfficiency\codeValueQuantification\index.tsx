import { MicroAppWithMemoHistory, useModel } from 'umi';

const CodeValueQuantification = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  return (
    <div>
      <MicroAppWithMemoHistory
        name="energy-efficiency-management"
        url="/codeValueQuantification"
        globalState={globalState}
      />
    </div>
  );
};

export default CodeValueQuantification;
