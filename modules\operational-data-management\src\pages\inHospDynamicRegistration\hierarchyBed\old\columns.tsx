import { TimeScape } from '@uni/components/src/date-mask/timescape';
import { ProFormSelect } from '@uni/components/src/pro-form/index';
import { Emitter } from '@uni/utils/src/emitter';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { InputNumber, Select } from 'antd';
import dayjs from 'dayjs';
import { HBedEventConstants } from './constants';

export const HierarchyBedColumns = (hierarchyList) => [
  {
    dataIndex: 'operation',
    title: '',
    valueType: 'option',
    width: 30,
    visible: true,
    fixed: 'left',
  },
  {
    dataIndex: 'HospName',
    title: '院区',
    editable: false,
  },
  {
    dataIndex: 'HierarchyName',
    title: '医疗单元名称',
    editable: (text, record) =>
      typeof record?.Id === 'string' && record?.Id?.startsWith('new'),
    renderFormItem: (props, text, action) => {
      return (
        <Select
          {...props?.fieldProps}
          options={hierarchyList}
          className={'editable-form-item'}
          fieldNames={{
            label: 'Name',
            value: 'Code',
          }}
          labelInValue
        />
      );
    },
    // filterType: 'search',
  },
  {
    dataIndex: 'HierarchyCode',
    editable: false,
  },
  {
    dataIndex: 'Sdate',
    editable: (text, record) =>
      typeof record?.Id === 'string' && record?.Id?.startsWith('new'),
    formItemProps: {
      rules: [{ required: true }],
    },
    fieldProps: {
      className: 'editable-form-item',
      controls: false,
      keyboard: false,
    },
    render: ({ props: { text } }) => {
      return valueNullOrUndefinedReturnDash(text, 'Date');
    },
    renderFormItem: (props, text, action) => {
      console.log('Edate', props, text, action);
      return (
        <TimeScape
          {...(props?.fieldProps,
          {
            className: ['editable-form-item', 'virtual-date'],
            controls: false,
            keyboard: false,
          })}
          container="DONT_STOP_PROPAGATION_WHEN_NEXT_IS_END_OR_START"
          formKey={`hierarchyBedStartDate#${props?.index}`}
          format={'YYYY-MM-DD'}
          updateObj={{
            endDate: dayjs(props?.entity?.Edate).toDate(),
          }}
          onValueChange={(dateString) => {
            console.log('timescape', dateString);
            let currentDate = dayjs(dateString, 'YYYY-MM-DD', true);
            if (currentDate.isValid()) {
              let formatDate = currentDate.format('YYYY-MM-DD');
              Emitter.emit(HBedEventConstants.START_DATE_CHANGE, {
                id: props?.entity?.Id,
                formatDate,
              });
            }
          }}
          timescapeOptions={{
            maxDate: undefined,
            minDate: undefined,
            defaultDate: undefined,
          }}
        />
      );
    },
  },
  {
    dataIndex: 'Edate',
    formItemProps: {
      rules: [{ required: true }],
    },
    fieldProps: {
      className: 'editable-form-item',
      controls: false,
      keyboard: false,
    },
    render: ({ props: { text } }) => {
      return valueNullOrUndefinedReturnDash(text, 'Date');
    },
    renderFormItem: (props, text, action) => {
      console.log('Edate', props, text, action);
      return (
        <TimeScape
          {...(props?.fieldProps,
          {
            className: ['editable-form-item', 'virtual-date'],
            controls: false,
            keyboard: false,
          })}
          container="DONT_STOP_PROPAGATION_WHEN_NEXT_IS_END_OR_START"
          formKey={`hierarchyBedEndDate#${props?.index}`}
          format={'YYYY-MM-DD'}
          updateObj={{
            minDate: dayjs(props?.entity?.Sdate).toDate(),
            defaultDate: dayjs(props?.entity?.Edate).toDate(),
          }}
          onValueChange={(dateString) => {
            console.log('timescape', dateString);
            let currentDate = dayjs(dateString, 'YYYY-MM-DD', true);
            if (currentDate.isValid()) {
              let formatDate = currentDate.format('YYYY-MM-DD');
              Emitter.emit(HBedEventConstants.DATE_CHANGE, {
                id: props?.entity?.Id,
                formatDate,
              });
            }
          }}
          timescapeOptions={{
            maxDate: undefined,
            minDate: undefined,
            defaultDate: undefined,
          }}
        />
      );
    },
  },
  {
    dataIndex: 'ApprovedBedAmt',
    fieldProps: {
      className: 'editable-form-item',
      controls: false,
      keyboard: false,
    },
    valueType: 'digit',
    renderFormItem: (props, text, action) => {
      return (
        <InputNumber
          {...props?.fieldProps}
          id={`#formItem#approvedBedAmt#${props?.index}`}
          className={'editable-form-item'}
          placeholder="请输入"
        />
      );
    },
    normalRenderer: true, // columnItem?.width && !columnItem?.isWidthDetect && !isBackPagination 时 强制进入widthDetect模式
  },
  {
    dataIndex: 'OpenBedAmt',
    fieldProps: {
      className: 'editable-form-item',
      controls: false,
      keyboard: false,
    },
    valueType: 'digit',
    renderFormItem: (props, text, action) => {
      return (
        <InputNumber
          {...props?.fieldProps}
          className={'editable-form-item'}
          placeholder="请输入"
        />
      );
    },
    normalRenderer: true, // columnItem?.width && !columnItem?.isWidthDetect && !isBackPagination 时 强制进入widthDetect模式
  },
  {
    dataIndex: 'Reserve1Amt',
    fieldProps: {
      className: 'editable-form-item',
      controls: false,
      keyboard: false,
    },
    valueType: 'digit',
    renderFormItem: (props, text, action) => {
      return (
        <InputNumber
          {...props?.fieldProps}
          className={'editable-form-item'}
          placeholder="请输入"
        />
      );
    },
    normalRenderer: true, // columnItem?.width && !columnItem?.isWidthDetect && !isBackPagination 时 强制进入widthDetect模式
  },
  {
    dataIndex: 'Reserve2Amt',
    fieldProps: {
      className: 'editable-form-item',
      controls: false,
      keyboard: false,
    },
    valueType: 'digit',
    renderFormItem: (props, text, action) => {
      return (
        <InputNumber
          {...props?.fieldProps}
          className={'editable-form-item'}
          placeholder="请输入"
        />
      );
    },
    normalRenderer: true, // columnItem?.width && !columnItem?.isWidthDetect && !isBackPagination 时 强制进入widthDetect模式
  },
  {
    dataIndex: 'Reserve3Amt',
    fieldProps: {
      className: 'editable-form-item',
      controls: false,
      keyboard: false,
    },
    valueType: 'digit',
    renderFormItem: (props, text, action) => {
      return (
        <InputNumber
          {...props?.fieldProps}
          className={'editable-form-item'}
          placeholder="请输入"
        />
      );
    },
    normalRenderer: true, // columnItem?.width && !columnItem?.isWidthDetect && !isBackPagination 时 强制进入widthDetect模式
  },
  {
    dataIndex: 'Reserve4Amt',
    fieldProps: {
      className: 'editable-form-item',
      controls: false,
      keyboard: false,
    },
    valueType: 'digit',
    renderFormItem: (props, text, action) => {
      return (
        <InputNumber
          {...props?.fieldProps}
          className={'editable-form-item'}
          placeholder="请输入"
        />
      );
    },
    normalRenderer: true, // columnItem?.width && !columnItem?.isWidthDetect && !isBackPagination 时 强制进入widthDetect模式
  },
  {
    dataIndex: 'Reserve5Amt',
    fieldProps: {
      className: 'editable-form-item',
      controls: false,
      keyboard: false,
    },
    valueType: 'digit',
    renderFormItem: (props, text, action) => {
      return (
        <InputNumber
          {...props?.fieldProps}
          className={'editable-form-item'}
          placeholder="请输入"
        />
      );
    },
    normalRenderer: true, // columnItem?.width && !columnItem?.isWidthDetect && !isBackPagination 时 强制进入widthDetect模式
  },
  {
    dataIndex: 'HierarchyType',
    editable: false,
  },
  {
    dataIndex: 'Status',
    editable: false,
  },
  {
    dataIndex: 'StatusName',
    editable: false,
  },
  {
    dataIndex: 'IsLocked',
    editable: false,
  },
  {
    dataIndex: 'LockingTime',
    editable: false,
  },
  {
    dataIndex: 'Remark',
    editable: false,
  },
  {
    dataIndex: 'LastModificationTime',
    editable: false,
  },
];

export const hierarchyModalColumns = [
  {
    dataIndex: 'DeptName',
    title: '科室',
    visible: true,
  },
  {
    dataIndex: 'ExactDate',
    dataType: 'Date',
    title: '日期',
    orderable: true,
    visible: true,
    width: 150,
    align: 'center',
  },
  {
    data: 'ApprovedBedsNumber',
    dataIndex: 'ApprovedBedsNumber',
    title: '核定床位数',
    visible: true,
    children: [
      {
        data: 'Current',
        dataIndex: 'Current',
        align: 'center',
        title: '当前数',
        visible: true,
        render: (text, record) => {
          return record?.ApprovedBedsNumber?.Current;
        },
      },
      {
        data: 'Previous',
        dataIndex: 'Previous',
        align: 'center',
        title: '更新数',
        visible: true,
        render: (text, record) => {
          return record?.ApprovedBedsNumber?.Previous;
        },
      },
    ],
  },
  {
    dataIndex: 'SuppliedBedsNumber',
    title: '开放床位数',
    visible: true,
    children: [
      {
        dataIndex: 'Current',
        align: 'center',
        title: '当前数',
        visible: true,
        render: (text, record) => {
          return record?.SuppliedBedsNumber?.Current;
        },
      },
      {
        dataIndex: 'Previous',
        align: 'center',
        title: '更新数',
        visible: true,
        render: (text, record) => {
          return record?.SuppliedBedsNumber?.Previous;
        },
      },
    ],
  },
  {
    dataIndex: 'IsLocked',
    width: 90,
    title: '是否锁定',
    valueType: 'boolean',
    align: 'center',
    visible: true,
  },
];
