import React, { useEffect, useRef, useState } from 'react';
import { useModel } from '@@/plugin-model/useModel';
import {
  ProForm,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';

const BaseInfoForm = (props) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  return (
    <>
      <ProForm
        layout="horizontal"
        form={props?.form1}
        // formRef={formRef1}
        name="baseInfoForm"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        autoComplete="off"
        submitter={false}
        grid={true}
      >
        <ProFormSelect
          key="Borrower"
          name={['Borrower']}
          rules={[{ required: true }]}
          label="借阅者"
          options={dictData?.Mr?.Employee ?? []}
          fieldProps={{
            fieldNames: { label: 'Name', value: 'Code' },
            options: dictData?.Mr?.Employee ?? [],
          }}
        />
        <ProFormSelect
          key="Purpose"
          name={['Purpose']}
          label="借阅目的"
          rules={[{ required: true }]}
          fieldProps={{
            fieldNames: { label: 'Name', value: 'Code' },
            options: dictData?.Mr?.BorrowPurpose ?? [],
          }}
        />
        <ProFormSelect
          key="BorrowerOrg"
          name={['BorrowerOrg']}
          label="借阅机构"
          rules={[{ required: true }]}
          fieldProps={{
            fieldNames: { label: 'Name', value: 'Code' },
            options: dictData?.Mr?.BorrowerOrg ?? [],
          }}
        />
        <ProFormText key="ContactInfo" name="ContactInfo" label="联络方式" />
        <ProFormTextArea key="Remark" name="Remark" label="备注" />
      </ProForm>
    </>
  );
};

export default BaseInfoForm;
