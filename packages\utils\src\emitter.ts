import EventEmitter from 'eventemitter3';
const eventEmitter = new EventEmitter();

(window as any).eventEmitter = eventEmitter;

const Emitter = {
  // 修改一下on的实现
  on: (
    event: string,
    fn: (data: any) => void,
    singleton: boolean = false,
    replace: boolean = false,
  ) => {
    if (singleton === true) {
      // 当只允许 一个实例
      // 尤其是用于 tabs 这样的情况下 因为多个tab item 重复加载导致 弹多个窗
      // 现在只发现有 DetailTableModal 中可能出现
      let registeredEventNames = eventEmitter.eventNames();
      if (registeredEventNames?.includes(event)) {
        if (replace === true) {
          eventEmitter.off(event);
          eventEmitter.on(event, fn);
        }
        // 跳过注册 以防止 多个弹窗
        console.warn('已经注册：', event);
      } else {
        eventEmitter.on(event, fn);
      }
    } else {
      eventEmitter.on(event, fn);
    }
  },
  onMultiple: (events: string[], fn: (data: any) => void) => {
    events.forEach((event) => {
      eventEmitter.on(event, fn);
    });
  },
  once: (event: string, fn: (data: any) => void) =>
    eventEmitter.once(event, fn),
  off: (event: string) => eventEmitter.off(event),
  offMultiple: (events: string[]) => {
    events.forEach((event) => {
      eventEmitter.off(event);
    });
  },
  emit: (events: string | string[], payload: any = null) => {
    if (typeof events === 'string') {
      eventEmitter.emit(events, payload);
    } else if (Array.isArray(events)) {
      events.forEach((event) => {
        eventEmitter.emit(event, payload);
      });
    }
  },
};
Object.freeze(Emitter);

const EventConstant = {
  LOGOUT: 'LOGOUT',
  TABLE_ROW_CLICK: 'TABLE_ROW_CLICK',
  TABLE_COLUMN_SEARCH_CONFIRM: 'TABLE_COLUMN_SEARCH_CONFIRM',
  TABLE_COLUMN_SEARCH_RESET: 'TABLE_COLUMN_SEARCH_RESET',

  TABLE_COLUMN_EDIT: 'TABLE_COLUMN_EDIT',

  TABLE_COLUMN_SORTER_EDIT: 'TABLE_COLUMN_SORTER_EDIT',

  QUERY_PARAMETER_EDIT: 'QUERY_PARAMETER_EDIT',
  QUERY_PARAMETER_ITEM_EDIT: 'QUERY_PARAMETER_ITEM_EDIT',
  QUERY_PARAMETER_ITEM_DELETE: 'QUERY_PARAMETER_ITEM_DELETE',

  QUERY_PARAMETER_SWITCH_EDIT: 'QUERY_PARAMETER_SWITCH_EDIT',
  QUERY_PARAMETER_RESET: 'QUERY_PARAMETER_RESET',

  MENU_COLLAPSE_CLICK: 'MENU_COLLAPSE_CLICK',
  GRID_WIDTH_CHANGE: 'GRID_WIDTH_CHANGE',

  MENU_CLICK: 'MENU_CLICK',

  TOP_MENU_CLICK: 'TOP_MENU_CLICK',

  STAT_CLICK: 'STAT_CLICK',
  STAT_ON_CLICK_EMITTER: 'STAT_ON_CLICK_EMITTER',

  /**
   *
   */
  HEADER_SELECTOR_CHANGE: 'HEADER_SELECTOR_CHANGE',
  HEADER_SEARCH_CLICK: 'HEADER_SEARCH_CLICK',
  HEADER_SEARCH_PARAM_CHANGE: 'HEADER_SEARCH_PARAM_CHANGE',
  HEADER_SEARCH_PARAM_CLEAR: 'HEADER_SEARCH_PARAM_CLEAR',
  HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY:
    'HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY',

  HIERARCHIES_REQUEST: 'HIERARCHIES_REQUEST',

  // column module
  COLUMN_MODULE: 'COLUMN_MODULE',
  COLUMN_MODULE_SAVE: 'COLUMN_MODULE_SAVE',
  DICT_DATA_CHANGE: 'DICT_DATA_CHANGE',

  LINE_ARROW_UP_DOWN_PRESS: 'LINE_ARROW_UP_DOWN_PRESS',

  EDITABLE_SAVE_SHORTCUT: 'EDITABLE_SAVE_SHORTCUT',
  EDITABLE_CANCEL_SHORTCUT: 'EDITABLE_CANCEL_SHORTCUT',
  EDITABLE_DELETE_SHORTCUT: 'EDITABLE_DELETE_SHORTCUT',
  EDITABLE_LEFT_KEYDOWN_SHORTCUT: 'EDITABLE_LEFT_KEYDOWN_SHORTCUT',
  EDITABLE_RIGHT_KEYDOWN_SHORTCUT: 'EDITABLE_RIGHT_KEYDOWN_SHORTCUT',
  EDITABLE_UP_KEYDOWN_SHORTCUT: 'EDITABLE_UP_KEYDOWN_SHORTCUT',
  EDITABLE_DOWN_KEYDOWN_SHORTCUT: 'EDITABLE_DOWN_KEYDOWN_SHORTCUT',

  SUMMARY_CARD_CLICK: 'SUMMARY_CARD_CLICK',
  SUMMARY_CARD_CLICK_PARENT: 'SUMMARY_CARD_CLICK_PARENT',

  DMR_TABLE_VALUE_CHANGE: 'DMR_TABLE_VALUE_CHANGE',

  DMR_FORM_VALUE_CHANGE: 'FORM_VALUE_CHANGE',
  CHS_FORM_VALUE_CHANGE: 'FORM_VALUE_CHANGE',
  FORM_VALUE_CHANGE: 'FORM_VALUE_CHANGE',

  DMR_FORM_VALUE_RESET_WITH_DEFAULT_VALUE:
    'FORM_VALUE_RESET_WITH_DEFAULT_VALUE',
  CHS_FORM_VALUE_RESET_WITH_DEFAULT_VALUE:
    'FORM_VALUE_RESET_WITH_DEFAULT_VALUE',
  FORM_VALUE_RESET_WITH_DEFAULT_VALUE: 'FORM_VALUE_RESET_WITH_DEFAULT_VALUE',

  DMR_TABLE_NEXT_KEY: 'DMR_TABLE_NEXT_KEY',
  DMR_TABLE_PREVIOUS_KEY: 'DMR_TABLE_PREVIOUS_KEY',

  DMR_ERROR_KEY_NAVIGATE: 'DMR_ERROR_KEY_NAVIGATE',

  DMR_INFO_GET: 'DMR_INFO_GET',

  CHS_TABLE_NEXT_KEY: 'CHS_TABLE_NEXT_KEY',
  CHS_TABLE_PREVIOUS_KEY: 'CHS_TABLE_PREVIOUS_KEY',

  DMR_TABLE_LAYOUT_CHANGE: 'DMR_TABLE_LAYOUT_CHANGE',
  DMR_LEFT_MENU_CLICK: 'DMR_LEFT_MENU_CLICK',
  DMR_LEFT_MENU_CLICK_OFFSET: 'DMR_LEFT_MENU_CLICK_OFFSET',
  DMR_DRAWER_CHANGE: 'DMR_DRAWER_CHANGE',
  DMR_SEARCH_TABLE_HISIDS: 'DMR_SEARCH_TABLE_HISIDS',
  DMR_COMMENT_MODAL: 'DMR_COMMENT_MODAL',
  DMR_TRACE_MODAL: 'DMR_TRACE_MODAL',
  DMR_SIGNIN_FOR_TRACE_MODAL: 'DMR_SIGNIN_FOR_TRACE_MODAL',
  DMR_SEARCH_TABLE_SELECT: 'DMR_SEARCH_TABLE_SELECT',
  DMR_NEXT: 'DMR_NEXT',
  DMR_NEXT_PAGE: 'DMR_NEXT_PAGE',
  DMR_RECORD_LAST_PAGE: 'DMR_RECORD_LAST_PAGE',
  DMR_LAST_RECORD: 'DMR_LAST_RECORD',
  DMR_HEADER_FOCUS: 'DMR_HEADER_FOCUS',

  DMR_DOUBLE_DECK_LAYOUT: 'DMR_DOUBLE_DECK_LAYOUT',

  DMR_ARROW_UP_DOWN_PRESS: 'ARROW_UP_DOWN_PRESS',
  CHS_ARROW_UP_DOWN_PRESS: 'ARROW_UP_DOWN_PRESS',
  ARROW_UP_DOWN_PRESS: 'ARROW_UP_DOWN_PRESS',

  DMR_DELETE_PRESS: 'DELETE_PRESS',
  CHS_DELETE_PRESS: 'DELETE_PRESS',
  DELETE_PRESS: 'DELETE_PRESS',

  DMR_PRE_CHECKBOX_HIDDEN: 'DMR_PRE_CHECKBOX_HIDDEN',

  DMR_TABLE_LAYOUT_CHANGE_MENU: 'TABLE_LAYOUT_CHANGE_MENU',
  DMR_TABLE_LAYOUT_CHANGE_SEPARATOR: 'TABLE_LAYOUT_CHANGE_SEPARATOR',
  DMR_TABLE_LAYOUT_CHANGE_SECTION_SEPARATOR:
    'TABLE_LAYOUT_CHANGE_SECTION_SEPARATOR',
  DMR_TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR:
    'TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR',
  DMR_TABLE_LAYOUT_RESIZE_SEPARATOR: 'TABLE_LAYOUT_RESIZE_SEPARATOR',
  DMR_TABLE_LAYOUT_CHANGE_LINE_NUMBER: 'TABLE_LAYOUT_CHANGE_LINE_NUMBER',
  CHS_TABLE_LAYOUT_CHANGE_MENU: 'TABLE_LAYOUT_CHANGE_MENU',
  CHS_TABLE_LAYOUT_CHANGE_SEPARATOR: 'TABLE_LAYOUT_CHANGE_SEPARATOR',
  CHS_TABLE_LAYOUT_CHANGE_SECTION_SEPARATOR:
    'TABLE_LAYOUT_CHANGE_SECTION_SEPARATOR',
  CHS_TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR:
    'TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR',
  CHS_TABLE_LAYOUT_RESIZE_SEPARATOR: 'TABLE_LAYOUT_RESIZE_SEPARATOR',
  CHS_TABLE_LAYOUT_CHANGE_LINE_NUMBER: 'TABLE_LAYOUT_CHANGE_LINE_NUMBER',
  TABLE_LAYOUT_CHANGE_MENU: 'TABLE_LAYOUT_CHANGE_MENU',
  TABLE_LAYOUT_CHANGE_SEPARATOR: 'TABLE_LAYOUT_CHANGE_SEPARATOR',
  TABLE_LAYOUT_CHANGE_RESET_SEPARATOR: 'TABLE_LAYOUT_CHANGE_RESET_SEPARATOR',
  TABLE_LAYOUT_CHANGE_SECTION_SEPARATOR:
    'TABLE_LAYOUT_CHANGE_SECTION_SEPARATOR',
  TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR:
    'TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR',
  TABLE_LAYOUT_RESIZE_SEPARATOR: 'TABLE_LAYOUT_RESIZE_SEPARATOR',
  TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR:
    'TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR',
  DMR_TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR:
    'TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR',
  CHS_TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR:
    'TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR',
  TABLE_LAYOUT_CHANGE_LINE_NUMBER: 'TABLE_LAYOUT_CHANGE_LINE_NUMBER',

  DMR_HELP_MODAL: 'HELP_MODAL',
  INSURANCE_HELP_MODAL: 'HELP_MODAL',
  HELP_MODAL: 'HELP_MODAL',

  DMR_HEADER_DATA_CHANGE: 'DMR_HEADER_DATA_CHANGE',

  DMR_MODULES_COMPLETE: 'DMR_MODULES_COMPLETE',
  CHS_MODULES_COMPLETE: 'CHS_MODULES_COMPLETE',

  DMR_SEARCH_REFRESH_DATA: 'DMR_SEARCH_REFRESH_DATA',

  CHS_LEFT_MENU_CLICK: 'CHS_LEFT_MENU_CLICK',
  CHS_TABLE_LAYOUT_CHANGE: 'CHS_TABLE_LAYOUT_CHANGE',
  CHS_CONFIGURATION_MENU_EDIT_COMPLETE: 'CHS_CONFIGURATION_MENU_EDIT_COMPLETE',

  CHS_HEADER_FOCUS: 'CHS_HEADER_FOCUS',

  CHS_NEXT: 'CHS_NEXT',
  CHS_NEXT_PAGE: 'CHS_NEXT_PAGE',

  INSURANCE_SEARCH_TABLE_HISIDS: 'INSURANCE_SEARCH_TABLE_HISIDS',

  DMR_COLUMN_CHANGE: 'DMR_COLUMN_CHANGE',
  DMR_MENU_POSITION_RECALCULATE: 'DMR_MENU_POSITION_RECALCULATE',
  DMR_LINE_NUMBER_RECALCULATE: 'DMR_LINE_NUMBER_RECALCULATE',

  DMR_CHSGROUP_AUDIT: 'DMR_CHSGROUP_AUDIT',
  CHS_CHSGROUP_AUDIT: 'CHS_CHSGROUP_AUDIT',

  DMR_ICDE_SELECT_SEARCH: 'DMR_ICDE_SELECT_SEARCH',

  DMR_ICDE_ADD: 'DMR_ICDE_ADD',
  DMR_OPER_ADD: 'DMR_OPER_ADD',

  DMR_ICDE_SELECT_ADD: 'DMR_ICDE_SELECT_ADD',
  DMR_ICDE_COND_ADD: 'DMR_ICDE_COND_ADD',
  DMR_ICDE_OUTCOME_ADD: 'DMR_ICDE_OUTCOME_ADD',
  DMR_ICDE_INPUT_ADD: 'DMR_ICDE_INPUT_ADD',
  DMR_ICDE_REPORT: 'DMR_ICDE_REPORT',
  DMR_ICDE_INSURE_MAIN: 'DMR_ICDE_INSURE_MAIN',
  DMR_ICDE_DELETE: 'DMR_ICDE_DELETE',
  DMR_ICDE_COPY: 'DMR_ICDE_COPY',

  DMR_TCM_ICDE_ADD: 'DMR_TCM_ICDE_ADD',
  DMR_TCM_ICDE_DELETE: 'DMR_TCM_ICDE_DELETE',
  DMR_TCM_ICDE_COPY: 'DMR_TCM_ICDE_COPY',

  DMR_PATHOLOGY_ICDE_ADD: 'DMR_PATHOLOGY_ICDE_ADD',
  DMR_PATHOLOGY_ICDE_SELECT_ADD: 'DMR_PATHOLOGY_ICDE_SELECT_ADD',
  DMR_PATHOLOGY_ICDE_INPUT_ADD: 'DMR_PATHOLOGY_ICDE_INPUT_ADD',
  DMR_PATHOLOGY_ICDE_DELETE: 'DMR_PATHOLOGY_ICDE_DELETE',

  DMR_OPER_SELECT_ADD: 'DMR_OPER_SELECT_ADD',
  DMR_OPER_INSUR_SELECT_ADD: 'DMR_OPER_INSUR_SELECT_ADD',
  DMR_OPER_SELECTOR_SELECT: 'DMR_OPER_SELECTOR_SELECT',
  DMR_OPER_WOUND_ADD: 'DMR_OPER_WOUND_ADD',
  DMR_OPER_LEVEL_ADD: 'DMR_OPER_LEVEL_ADD',
  DMR_OPER_INPUT_ADD: 'DMR_OPER_INPUT_ADD',
  DMR_OPER_INPUT_BLUR: 'DMR_OPER_INPUT_BLUR',
  DMR_OPER_REPORT: 'DMR_OPER_REPORT',
  DMR_OPER_DELETE: 'DMR_OPER_DELETE',
  DMR_OPER_COPY: 'DMR_OPER_COPY',
  DMR_OPER_INSURE_MAIN: 'DMR_OPER_INSURE_MAIN',

  DMR_ICU_ADD: 'DMR_ICU_ADD',
  DMR_ICU_INPUT_ADD: 'DMR_ICU_INPUT_ADD',
  DMR_ICU_CODE_ADD: 'DMR_ICU_CODE_ADD',
  DMR_ICU_TIME_ADD: 'DMR_ICU_TIME_ADD',
  DMR_ICU_DELETE: 'DMR_ICU_DELETE',

  DMR_DEPARTMENT_TRANSFER: 'DMR_DEPARTMENT_TRANSFER',
  DMR_DEPARTMENT_TRANSFER_OK: 'DMR_DEPARTMENT_TRANSFER_OK',
  DMR_DEPARTMENT_TRANSFER_CLOSE: 'DMR_DEPARTMENT_TRANSFER_CLOSE',
  DMR_DEPARTMENT_TRANSFER_INPUT_ADD: 'DMR_DEPARTMENT_TRANSFER_INPUT_ADD',
  DMR_DEPARTMENT_TRANSFER_DELETE: 'DMR_DEPARTMENT_TRANSFER_DELETE',

  DMR_CARD_CHECK_DATA: 'DMR_CARD_CHECK_DATA',
  DMR_CARD_GROUP_DATA: 'DMR_CARD_GROUP_DATA',
  DMR_CARD_DRG_GROUP_DATA: 'DMR_CARD_DRG_GROUP_DATA',
  DMR_CARD_DATA_RESET: 'DMR_CARD_DATA_RESET',

  DMR_CARD_SAVE_REVIEWS: 'DMR_CARD_SAVE_REVIEWS',

  CHS_CARD_DATA_RESET: 'CHS_CARD_DATA_RESET',

  CHS_CARD_SAVE_REVIEWS: 'CHS_CARD_SAVE_REVIEWS',

  CHS_ICDE_ADD: 'CHS_ICDE_ADD',
  CHS_OPER_ADD: 'CHS_OPER_ADD',
  CHS_INPUT_ADD: 'CHS_INPUT_ADD',

  CHS_ICU_ADD: 'CHS_ICU_ADD',
  CHS_ICU_INPUT_ADD: 'CHS_ICU_INPUT_ADD',
  CHS_ICU_CODE_ADD: 'CHS_ICU_CODE_ADD',
  CHS_ICU_TIME_ADD: 'CHS_ICU_TIME_ADD',
  CHS_ICU_DELETE: 'CHS_ICU_DELETE',

  CHS_BLOOD_ADD: 'CHS_BLOOD_ADD',
  CHS_BLOOD_BLDCAT_ADD: 'CHS_BLOOD_BLDCAT_ADD',
  CHS_BLOOD_BLDUNT_ADD: 'CHS_BLOOD_BLDUNT_ADD',
  CHS_BLOOD_DELETE: 'CHS_BLOOD_DELETE',

  CHS_ICDE_SELECT_ADD: 'CHS_ICDE_SELECT_ADD',
  CHS_ICDE_COND_ADD: 'CHS_ICDE_COND_ADD',
  CHS_ICDE_OUTCOME_ADD: 'CHS_ICDE_OUTCOME_ADD',
  CHS_ICDE_INPUT_ADD: 'CHS_ICDE_INPUT_ADD',
  CHS_ICDE_REPORT: 'CHS_ICDE_REPORT',
  CHS_ICDE_INSURE_MAIN: 'CHS_ICDE_INSURE_MAIN',
  CHS_ICDE_DELETE: 'CHS_ICDE_DELETE',
  CHS_ICDE_COPY: 'CHS_ICDE_COPY',

  CHS_OPER_SELECT_ADD: 'CHS_OPER_SELECT_ADD',
  CHS_OPER_INSUR_SELECT_ADD: 'CHS_OPER_INSUR_SELECT_ADD',
  CHS_OPER_ANATYPE_ADD: 'CHS_OPER_ANATYPE_ADD',
  CHS_OPER_WOUND_ADD: 'CHS_OPER_WOUND_ADD',
  CHS_OPER_LEVEL_ADD: 'CHS_OPER_LEVEL_ADD',
  CHS_OPER_INPUT_ADD: 'CHS_OPER_INPUT_ADD',
  CHS_OPER_INPUT_BLUR: 'CHS_OPER_INPUT_BLUR',
  CHS_OPER_REPORT: 'CHS_OPER_REPORT',
  CHS_OPER_DELETE: 'CHS_OPER_DELETE',
  CHS_OPER_COPY: 'CHS_OPER_COPY',
  CHS_OPER_INSURE_MAIN: 'CHS_OPER_INSURE_MAIN',

  CHS_LEFT_MENU_CLICK_OFFSET: 'CHS_LEFT_MENU_CLICK_OFFSET',
  CHS_MENU_POSITION_RECALCULATE: 'CHS_MENU_POSITION_RECALCULATE',

  // DATA_MANAGEMENT
  DATA_MANAGEMENT: 'DATA_MANAGEMENT',

  // valueType
  EDIT_EXPAND_BOOLEAN: 'EDIT_EXPAND_BOOLEAN',

  // operational table
  ODM_HELP_MODAL: 'ODM_HELP_MODAL',
  ODM_GO_LEFT: 'ODM_GO_LEFT',
  ODM_GO_RIGHT: 'ODM_GO_RIGHT',
  ODM_CUSTOM_TABLE_SAVE: 'ODM_CUSTOM_TABLE_SAVE',

  ODM_IN_DAILY_ADD: 'ODM_IN_DAILY_ADD',
  ODM_IN_DAILY_SAVE: 'ODM_IN_DAILY_SAVE',
  ODM_IN_DAILY_CANCEL: 'ODM_IN_DAILY_CANCEL',

  ODM_IN_DEPT_ADD: 'ODM_IN_DEPT_ADD',
  ODM_IN_DEPT_SAVE: 'ODM_IN_DEPT_SAVE',
  ODM_IN_DEPT_CANCEL: 'ODM_IN_DEPT_CANCEL',

  ODM_OUT_DAILY_ADD: 'ODM_OUT_DAILY_ADD',
  ODM_OUT_DAILY_SAVE: 'ODM_OUT_DAILY_SAVE',
  ODM_OUT_DAILY_CANCEL: 'ODM_OUT_DAILY_CANCEL',

  ODM_OUT_DEPT_ADD: 'ODM_OUT_DEPT_ADD',
  ODM_OUT_DEPT_SAVE: 'ODM_OUT_DEPT_SAVE',
  ODM_OUT_DEPT_CANCEL: 'ODM_OUT_DEPT_CANCEL',

  ODM_OBS_DAILY_ADD: 'ODM_OBS_DAILY_ADD',
  ODM_OBS_DAILY_SAVE: 'ODM_OBS_DAILY_SAVE',
  ODM_OBS_DAILY_CANCEL: 'ODM_OBS_DAILY_CANCEL',

  ODM_OBS_DEPT_ADD: 'ODM_OBS_DEPT_ADD',
  ODM_OBS_DEPT_SAVE: 'ODM_OBS_DEPT_SAVE',
  ODM_OBS_DEPT_CANCEL: 'ODM_OBS_DEPT_CANCEL',

  // sned to
  SEND_TO: 'SEND_TO',

  // progress task
  PROGRESS_CHECK_DETAIL: 'PROGRESS_CHECK_DETAIL',
  PROGRESS_DELETE: 'PROGRESS_DELETE',
  PROGRESS_RESTART: 'PROGRESS_RESTART',

  // infinite scroll
  INFINITE_SCROLL_CB_FETCH: 'INFINITE_SCROLL_CB_FETCH',
  INFINITE_SCROLL_FETCH: 'INFINITE_SCROLL_FETCH',
  INFINITE_SCROLL_RELOAD_FETCH: 'INFINITE_SCROLL_RELOAD_FETCH',

  // custom table row click
  ADRG_TABLE_ROW_CLICK: 'ADRG_TABLE_ROW_CLICK',
  DRG_TABLE_ROW_CLICK: 'DRG_TABLE_ROW_CLICK',
  MAJOR_PERF_DEPT_TABLE_ROW_CLICK: 'MAJOR_PERF_DEPT_TABLE_ROW_CLICK',
  HOSP_TABLE_ROW_CLICK: 'HOSP_TABLE_ROW_CLICK',
  PERF_DEPT_TABLE_ROW_CLICK: 'PERF_DEPT_TABLE_ROW_CLICK',
  DEPT_TABLE_ROW_CLICK: 'DEPT_TABLE_ROW_CLICK',
  MED_TEAM_TABLE_ROW_CLICK: 'MED_TEAM_TABLE_ROW_CLICK',
  DOCTOR_TABLE_ROW_CLICK: 'DOCTOR_TABLE_ROW_CLICK',
  DISEASE_STRUCTURE_TABLE_ROW_CLICK: 'DISEASE_STRUCTURE_TABLE_ROW_CLICK',
  // custom echarts mouse event
  ECHARTS_MOUSE_EVENT_CLK: 'ECHARTS_MOUSE_EVENT_CLK',

  // detailsBtn click
  DETAILS_BTN_CLICK: 'DETAILS_BTN_CLICK',
  REFRESH_BTN_CLICK: 'REFRESH_BTN_CLICK',
  SEARCH_DOWN_DRILL: 'SEARCH_DOWN_DRILL',
};

export { Emitter, EventConstant };
