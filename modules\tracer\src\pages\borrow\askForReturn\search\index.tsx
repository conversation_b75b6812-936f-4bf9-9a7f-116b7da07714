import { Reducer, useEffect, useMemo, useReducer } from 'react';
import { Dispatch, useDispatch, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { ExportIconBtn, UniTable } from '@uni/components/src';
import { Card, Col, Divider, Row, Space, TableProps, message } from 'antd';
import { useSafeState } from 'ahooks';
import {
  IModalState,
  IReducer,
  ITableState,
} from '@uni/reducers/src/Interface';
import { SwagBorrowRecordItem } from '../interface';
import { columnsHandler, isRespErr, sortingHandler } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType } from '@/Constants';
import {
  TableAction,
  modalReducer,
  tableReducer,
  InitTableState,
  InitModalState,
  ModalAction,
} from '@uni/reducers/src';
import { SorterResult } from 'antd/lib/table/interface';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { BorrowSearchColumns } from './columns';
import { useTimelineReq } from '@/hooks';
import PatTimeline from '@/components/PatTimeline';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { ITableReq } from '@/Interface';
import { isEmptyValues } from '@uni/utils/src/utils';
import { pickOnlyNeedKeys } from '@uni/utils/src/search-context';

const BorrowSearch = () => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagBorrowRecordItem>, IReducer>
  >(tableReducer, InitTableState);

  const [modalState, modalStateDispatch] = useReducer<
    Reducer<
      IModalState<SwagBorrowRecordItem>,
      IReducer<IModalState<SwagBorrowRecordItem>>
    >
  >(modalReducer, InitModalState);

  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();

  const timeLineReset = () => {
    SearchTableDispatch({
      type: TableAction.clkChange,
      payload: {
        clkItem: null,
      },
    });
    setParams(null);
  };

  const [backPagination, setBackPagination] = useSafeState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    hideOnSinglePage: false,
  });

  const backTableOnChange: TableProps<any>['onChange'] = async (
    pagi,
    filter,
    sorter,
    extra,
  ) => {
    tableReq(
      searchParams,
      pagi.current,
      pagi.pageSize,
      sorter as SorterResult<SwagBorrowRecordItem>,
    );
  };

  // 普通的tableReq
  const tableReq = async (
    params,
    cur = 1,
    size = 10,
    sorter = SearchTable.sorter,
  ) => {
    console.log('fetchParams: ', params);
    let res: ITableReq | string = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: 'BorrowingManagement/GetAlerts',
        requestParams: [
          {
            url: 'Api/Mr/BorrowingManagement/GetAlerts',
            method: 'POST',
            data: {
              ..._.pick(params, ['Borrower', 'contactInfo']),
              IsReturned: params?.isReturned ? true : false,
              CliDept: params?.dutyDept || undefined,
              ExceedDays: parseInt(params?.exceedDays) ?? undefined,

              // customize header parameters
              ...pickOnlyNeedKeys(params, true),

              hospCode: isEmptyValues(params?.hospCode)
                ? []
                : Array.isArray(params?.hospCode)
                ? params?.hospCode
                : [params?.hospCode],

              current: cur,
              pageSize: size,
              sorting: sortingHandler(sorter),
            },
            dataType: 'mr',
          },
        ],
      },
    });
    if (typeof res !== 'string') {
      let total = res?.datas[0]?.total;
      SearchTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res?.datas[0]?.data ?? [],
        },
      });

      // sorter
      if (!_.isEqual(sorter, SearchTable.sorter)) {
        SearchTableDispatch({
          type: TableAction.sortChange,
          payload: { sorter },
        });
      }

      setBackPagination({
        ...backPagination,
        current: cur,
        pageSize: size,
        total: total ?? 0,
      });
    }
  };

  const reqActionReq = async (params: any, reqType: ReqActionType) => {
    if (!params || !reqType) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `BorrowingManagement/${reqType}`,
        requestParams: {
          url: `Api/Mr/BorrowingManagement/${reqType}`,
          method: 'POST',
          data: params,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      message.success('操作成功');
      if (modalState.visible) {
        modalStateDispatch({
          type: ModalAction.init,
        });
      }
      // 催还不需要重新调用
      // timeLineReset();
      // tableReq(searchParams, backPagination.current, backPagination.pageSize);
    }
  };
  // columns 处理，主要用于处理options
  const columnsSolver = useMemo(() => {
    return SearchTable.columns
      ? columnsHandler(
          SearchTable.columns,
          // {
          //   dataIndex: 'option',
          //   title: '操作',
          //   visible: true,
          //   width: 50,
          //   align: 'center',
          //   // valueType: 'option',
          //   fixed: 'right',
          //   render: (text, record: SwagBorrowRecordItem) => [
          //     <Tooltip key="alert" title="催还">
          //       <ShakeOutlined
          //         className="icon_blue-color"
          //         style={{
          //           display: record.IsReturned ? 'none' : 'block',
          //         }}
          //         onClick={(e) => {
          //           e.stopPropagation();
          //           // 催还api
          //           reqActionReq(
          //             { BarCodes: [record.BarCode] },
          //             ReqActionType.createAlerts,
          //           );
          //         }}
          //       />
          //     </Tooltip>,
          //   ],
          // }
        )
      : [];
  }, [SearchTable.columns, searchParams, backPagination]);

  useEffect(() => {
    tableReq(searchParams);
    timeLineReset();
  }, [searchParams]);

  // columns处理
  if (
    columnsList?.['BorrowingManagement/GetAlerts'] &&
    SearchTable.columns.length < 1
  ) {
    SearchTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          BorrowSearchColumns,
          columnsList['BorrowingManagement/GetAlerts'],
        ),
      },
    });
  }

  return (
    <>
      <Card
        title="催还列表"
        extra={
          <Space>
            <Divider type="vertical" />
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: 'Api/Mr/BorrowingManagement/ExportGetAlerts',
                method: 'POST',
                data: {
                  ..._.pick(searchParams, ['Borrower', 'contactInfo']),
                  IsReturned: searchParams?.isReturned ? true : false,
                  SearchableText: searchParams?.searchKeyword,
                  ...searchParams,
                  MaxResultCount: 999999,
                },
                fileName: `病案催还明细-${
                  searchParams?.isReturned ? '已归还' : '未归还'
                }`,
              }}
              btnDisabled={SearchTable.data?.length < 1}
            />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: 'Api/Mr/BorrowingManagement/GetAlerts',
                onTableRowSaveSuccess: (columns) => {
                  // 这个columns 存到dva
                  dispatch({
                    type: 'global/saveColumns',
                    payload: {
                      name: 'BorrowingManagement/GetAlerts',
                      value: columns,
                    },
                  });
                  SearchTableDispatch({
                    type: TableAction.columnsChange,
                    payload: {
                      columns: tableColumnBaseProcessor(
                        BorrowSearchColumns,
                        columns,
                      ),
                    },
                  });
                },
              }}
            />
          </Space>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={18}>
            <UniTable
              id="trace_borrow_search"
              rowKey="RecordId"
              showSorterTooltip={false}
              loading={loadings['BorrowingManagement/GetAlerts'] || false}
              columns={columnsSolver} // columnsHandler
              dataSource={SearchTable.data}
              dictionaryData={dictData}
              pagination={backPagination}
              onChange={backTableOnChange}
              scroll={{ x: 'max-content' }}
              rowClassName={(record) => {
                if (record?.RecordId === SearchTable.clkItem?.RecordId)
                  return 'row-selected';
                return null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (SearchTable.clkItem?.RecordId !== record?.RecordId) {
                      SearchTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
            />
          </Col>
          <Col span={6}>
            <PatTimeline
              item={SearchTable?.clkItem}
              timelineItems={timelineItems}
              loading={loadings['TraceRecord/GetActions']}
            />
          </Col>
        </Row>
      </Card>
    </>
  );
};

export default BorrowSearch;
