import React, { useEffect, useState } from 'react';
import './index.less';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { UniDragEditOnlyTable } from '@uni/components/src';
import { Button, Card, Col, Form, message, Modal, Row } from 'antd';
import { comboQueryColumnValue, comboQueryTableColumns } from './columns';
import { v4 as uuidv4 } from 'uuid';
import { QueryProps } from '@react-awesome-query-builder/ui';
import { Emitter } from '@uni/utils/src/emitter';
import {
  qualityControlRuleEventConstants,
  StatsAnalysisEventConstant,
} from '@/pages/jsonExprRules/newJsonLogicPart/constants';
import omit from 'lodash/omit';
import { queryOperations } from './constants';
import {
  buildOptions,
  generateQueryFromTable,
  bracketsValidation,
  fieldItemsProcessor,
} from './utils';
import {
  toText,
  toTree,
  toMongo,
  toJsonLogic,
} from '@uni/combo-condition-parser';
import { Utils } from '@react-awesome-query-builder/antd';
import { isEmptyValues } from '@uni/utils/src/utils';
import ColumnNameMoreModal from './components/column-more';
import { RespVO } from '@uni/commons/src/interfaces';
import { history, useModel } from 'umi';
import { useRequest } from 'umi';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import { combineQuerySaveParamProcessor } from './processor';
import qs from 'qs';

interface ComboTableProps extends QueryProps {
  onSave: (value: any, doTest?: boolean) => void;
}

const { jsonLogicFormat, loadTree } = Utils;

const tableId = 'combo-query-expression-table';

const ComboTable = (props: ComboTableProps) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  const [form] = Form.useForm();

  const [comboQueryColumns, setComboQueryColumns] = useState<any>([]);

  const [comboQueryDataSource, setComboQueryDataSource] = useState<any>([]);

  const comboQueryColumnProcessor = () => {
    setComboQueryColumns(comboQueryTableColumns(props, comboQueryDataSource));
  };

  useEffect(() => {
    setComboQueryDataSource([
      {
        id: uuidv4(),
        groupId: uuidv4(),
      },
    ]);

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_RESET,
      () => {
        // TODO 二次确认框
        setComboQueryDataSource([
          {
            id: uuidv4(),
            groupId: uuidv4(),
          },
        ]);
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
      (payload) => {
        if (payload?.DisplayExprStructure === 'customized') {
          setComboQueryDataSource(
            JSON.parse(payload?.DisplayExpr)?.sort(
              (a, b) => a?.order ?? 65535 - b?.order ?? 65535,
            ),
          );
        }
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_RESET,
      );
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
      );
    };
  }, []);

  useEffect(() => {
    console.log('comboQueryDataSourceChanged', comboQueryDataSource);
    // 条件组内的item删除
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ITEM_DELETE,
      (payload) => {
        let currentComboQueryDataSources = comboQueryDataSource.slice();
        let waitForDeleteItem = form.getFieldValue(payload?.recordId);

        currentComboQueryDataSources.splice(payload?.index, 1);

        // TODO 同样实现一套新增这个机制
        let groupItem = currentComboQueryDataSources?.find(
          (item) => item?.groupId === payload?.groupId,
        );

        // 表示当前不是一行 至少2行
        if (groupItem && waitForDeleteItem?.rowSpan > 1) {
          // 表示是group的item 需要把除了中间的三个都赋值到当前这个上面
          if (waitForDeleteItem?.rowSpan !== 0) {
            groupItem['rowSpan'] = waitForDeleteItem?.rowSpan - 1;
            form.setFieldValue(
              [groupItem?.id, 'rowSpan'],
              groupItem['rowSpan'],
            );

            let noCopyDataIndex = comboQueryColumnValue(
              undefined,
              undefined,
              undefined,
              [],
            )?.map((item) => item?.dataIndex);

            Object.keys(omit(waitForDeleteItem, noCopyDataIndex))?.forEach(
              (key) => {
                form.setFieldValue(
                  [groupItem?.id, key],
                  waitForDeleteItem[key],
                );
              },
            );
          }
        }

        // 删除了一个组内的需要 rowSpan - 1
        if (waitForDeleteItem?.rowSpan === 0) {
          groupItem['rowSpan'] = groupItem['rowSpan'] - 1;
          form.setFieldValue([groupItem?.id, 'rowSpan'], groupItem['rowSpan']);
        }

        setComboQueryDataSource(
          currentComboQueryDataSources?.map((item) => {
            return {
              ...item,
              ...form.getFieldValue(item?.id),
              rowSpan: item?.rowSpan, // item的才是对的 form的还没更新
            };
          }),
        );
      },
    );
    // 条件组内的item添加
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ITEM_ADD,
      (payload) => {
        let currentComboQueryDataSources = comboQueryDataSource.slice();
        let groupItem = currentComboQueryDataSources?.find(
          (item) => item?.groupId === payload?.groupId,
        );
        let addItem = {
          id: uuidv4(),
          groupId: payload?.groupId,
          rowSpan: 0,
          parentField: form.getFieldValue([payload?.recordId, 'parentField']),
        };
        currentComboQueryDataSources.splice(payload?.index + 1, 0, addItem);
        // 修改之前那个rowSpan
        groupItem['rowSpan'] = currentComboQueryDataSources?.filter(
          (item) => item?.groupId === payload?.groupId,
        )?.length;
        form.setFieldValue([groupItem?.id, 'rowSpan'], groupItem['rowSpan']);

        setComboQueryDataSource(
          currentComboQueryDataSources?.map((item) => {
            return {
              ...item,
              ...form.getFieldValue(item?.id),
            };
          }),
        );
      },
    );

    // 条件 删除
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_DELETE,
      ({ recordId, groupId, index }) => {
        console.log('STATS_ANALYSIS_TABLE_CONDITION_DELETE', groupId, index);
        let currentComboQueryDataSources = comboQueryDataSource.slice();

        setComboQueryDataSource(
          currentComboQueryDataSources?.filter(
            (item) => item?.groupId !== groupId,
          ),
        );
      },
    );

    // 条件 增加
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ADD,
      ({ recordId, groupId, index, offset }) => {
        console.log('STATS_ANALYSIS_TABLE_CONDITION_ADD', recordId, index);
        let currentComboQueryDataSources = comboQueryDataSource.slice();
        let addItem = {
          id: uuidv4(),
          groupId: uuidv4(),
        };
        currentComboQueryDataSources.splice(index + 1 + offset, 0, addItem);

        // 对于上一条数据如果连接符不存在就设定为AND
        if (currentComboQueryDataSources?.length > 1) {
          let previousData =
            currentComboQueryDataSources?.[
              currentComboQueryDataSources?.length - 2
            ];
          let previousLink = form.getFieldValue([previousData?.id, 'link']);
          if (isEmptyValues(previousLink)) {
            form.setFieldValue([previousData?.id, 'link'], 'and');
            previousData['link'] = 'and';
          }
        }

        setComboQueryDataSource(currentComboQueryDataSources);
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE,
      (payload) => {
        console.log(
          'StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE',
          payload,
        );
        onSaveClick(payload?.doTest);
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_CONDITION_ADD,
      (payload) => {
        if (!payload) {
          onConditionAddClick();
        }
        let index = payload?.index;
        // 当且仅当是最后一个的时候才 + 1行
        if (index === comboQueryDataSource?.length - 1) {
          onConditionAddClick();
        }
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_VIEW_TEXT,
      () => {
        onOperationClick('VIEW_TEXT');
      },
    );

    return () => {
      Emitter.offMultiple([
        StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_DELETE,
        StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ADD,
        StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ITEM_DELETE,
        StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ITEM_ADD,
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE,
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_CONDITION_ADD,
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_VIEW_TEXT,
      ]);
    };
  }, [comboQueryDataSource]);

  useEffect(() => {
    comboQueryColumnProcessor();
  }, [props, comboQueryDataSource]);

  const onSaveClick = async (doTest?: boolean) => {
    let translationResult = await jsonLogicTranslation();
    let result = undefined;
    if (
      translationResult?.success === false ||
      translationResult?.jsonLogicExpression === undefined
    ) {
      message.error('规则条件不合法，请检查规则条件');
      return;
    }

    if (translationResult?.success === true) {
      result = {
        DisplayExpr: tableExpressionConvert(),
        Expr: translationResult?.jsonLogicExpression,
      };

      if (doTest) {
        Emitter.emit(
          qualityControlRuleEventConstants.OPEN_TEST_MODAL,
          translationResult?.jsonLogicExpression,
        );
      } else {
        Emitter.emit(qualityControlRuleEventConstants.RETURN_EXPR, result);
      }
    } else {
      message.error('规则条件不合法，请检查规则条件');
    }
  };

  const { loading: combineQuerySaveLoading, run: combineQuerySaveReq } =
    useRequest(
      (
        id,
        treeExpression,
        expression,
        combineQueryTitle,
        columnsState,
        selectedMetricsState,
      ) => {
        let data = {};
        if (id) {
          data['id'] = id;
        }
        // TODO isPublic 可以为false
        data['isPublic'] = true;
        data['title'] = combineQueryTitle;
        // expr -> mongo expression
        data['expr'] = JSON.stringify(expression);
        // display Expr -> table data
        data['displayExpr'] = JSON.stringify(treeExpression);
        data['DisplayExprStructure'] = 'Row';

        return uniCombineQueryService(
          'Api/DmrAnalysis/ComboQueryTemplate/save',
          {
            method: 'POST',
            requestType: 'json',
            data: combineQuerySaveParamProcessor(
              data,
              columnsState,
              selectedMetricsState,
            ),
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<string>) => {
          return response;
        },
      },
    );

  const jsonLogicTranslation = async () => {
    let formValues = form?.getFieldsValue(true);
    let validateResponse = await form.validateFields();
    if (validateResponse?.errorFields?.length > 0) {
      return;
    }

    let bracketsValidationResult = bracketsValidation(formValues);
    if (bracketsValidationResult === false) {
      message.error('请检查括号数量是否匹配');
      return {
        success: false,
        treeExpression: undefined,
        jsonLogicExpression: undefined,
      };
    }
    let tableQuery = generateQueryFromTable(formValues, props);
    const { tree, lexErrors, parseErrors } = toTree(tableQuery);
    const {
      logic: jsonLogic,
      lexErrors: jsonLogicErrors,
      parseErrors: jsonLogicParseErrors,
    } = toJsonLogic(tableQuery);

    if (lexErrors?.length > 0 || parseErrors?.length > 0) {
      console.log('lexErrors', lexErrors);
      console.log('parseErrors', parseErrors);
      // TODO 埋点 ?
      message.error('转换条件树出现错误，请检查规则条件是否正确');
      return {
        success: false,
        treeExpression: undefined,
        jsonLogicExpression: undefined,
      };
    }

    if (jsonLogicErrors?.length > 0 || jsonLogicParseErrors?.length > 0) {
      console.log('jsonLogicErrors', jsonLogicErrors);
      console.log('jsonLogicParseErrors', jsonLogicErrors);
      // TODO 埋点 ?
      message.error('转换条件树出现错误，请检查规则条件是否正确');
      return {
        success: false,
        treeExpression: undefined,
        jsonLogicExpression: undefined,
      };
    }
    try {
      const comboQueryTree = loadTree(tree);

      const previousQuery = jsonLogicFormat(comboQueryTree, props);
      const logicQuery = jsonLogic;
      console.log('logicQuery', comboQueryTree, previousQuery, logicQuery);

      return {
        success: true,
        treeExpression: comboQueryTree,
        jsonLogicExpression: logicQuery,
      };
    } catch (comboQueryTreeError) {
      message.error('转换条件树出现错误，请检查规则条件是否正确');
      return {
        success: false,
        treeExpression: undefined,
        jsonLogicExpression: undefined,
      };
    }
  };

  const tableExpressionConvert = () => {
    let dataOrder = form.getFieldValue('dataOrder');
    let convertedDataSource = [];

    comboQueryDataSource?.forEach((item) => {
      if (item?.id) {
        let values = form.getFieldValue(item?.id);

        let convertedItem = {
          ...omit(values, ['operatorMeta', 'parentField']),
          order: dataOrder?.[item?.id],
        };
        convertedItem['parentFieldPath'] = values['parentField']?.path;

        convertedDataSource.push(convertedItem);
      }
    });

    return convertedDataSource?.sort(
      (a, b) => a?.order ?? 65535 - b?.order ?? 65535,
    );
  };

  const onOperationClick = async (type) => {
    switch (type) {
      case 'QUERY': {
        let translationResult = await jsonLogicTranslation();
        console.log('translationResult', translationResult);
        // if (translationResult?.success && translationResult?.jsonLogicExpression) {
        //   Emitter.emit(
        //     [
        //       StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY,
        //       StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS,
        //     ],
        //     JSON.stringify(translationResult?.jsonLogicExpression),
        //   );
        // }
        break;
      }
      case 'RESET': {
        setComboQueryDataSource([
          {
            id: uuidv4(),
            groupId: uuidv4(),
          },
        ]);

        break;
      }
      case 'VIEW_TEXT': {
        let formValues = form?.getFieldsValue(true);
        // let validateResponse = await form.validateFields();
        // if (validateResponse?.errorFields?.length !== 0) {
        //     return;
        // }

        let translationResult = await jsonLogicTranslation();
        console.log('translationResult', translationResult);

        let bracketsValidationResult = bracketsValidation(formValues);
        if (bracketsValidationResult === false) {
          message.error('请检查括号数量是否匹配');
          return;
        }

        console.log('beforeTableQuery', formValues, props);
        let tableQuery = generateQueryFromTable(formValues, props, dictData);
        console.log('tableQuery', tableQuery);

        const { text, lexErrors, parseErrors } = toText(tableQuery);
        console.log('toText', text, lexErrors, parseErrors);
        if (lexErrors?.length > 0 || parseErrors?.length > 0) {
          console.log('lexErrors', lexErrors);
          console.log('parseErrors', parseErrors);
          // TODO 埋点 ?
          message.error('转换条件树出现错误，请联系管理员');
          return;
        }
        Modal.success({
          title: '查询条件的语法',
          content: text,
        });
        break;
      }
      default:
        break;
    }
  };

  const onConditionAddClick = () => {
    let dataSources = comboQueryDataSource.concat({
      id: uuidv4(),
      groupId: uuidv4(),
    });

    // 对于上一条数据如果连接符不存在就设定为AND
    if (dataSources?.length > 1) {
      let previousData = dataSources?.[dataSources?.length - 2];
      let previousLink = form.getFieldValue([previousData?.id, 'link']);
      if (isEmptyValues(previousLink)) {
        form.setFieldValue([previousData?.id, 'link'], 'and');
        previousData['link'] = 'and';
      }
    }

    setComboQueryDataSource(dataSources);
  };

  return (
    <div
      id={'combo-query-expression-table-container'}
      className={`combo-query-expression-table-container  ${
        comboQueryDataSource?.length === 0 ? 'table-empty' : ''
      }`}
    >
      <UniDragEditOnlyTable
        form={form}
        key={tableId}
        id={tableId}
        tableId={tableId}
        bordered={true}
        forceColumnsUpdate={true}
        scroll={{
          y: 300,
          // x: 'max-content',
        }}
        pagination={false}
        className={`combo-query-table-container`}
        dataSource={comboQueryDataSource}
        rowKey={'id'}
        allowDragging={false}
        onValuesChange={(recordList) => {
          setComboQueryDataSource(recordList);
        }}
        columns={comboQueryColumns}
      />

      <ColumnNameMoreModal
        mainForm={form}
        fields={fieldItemsProcessor(buildOptions(props, props?.fields), {
          ...props,
          queryType: 'table',
        })}
      />

      {/* <div className={'operations-container'}>
        {queryOperations.map((item) => {
          return (
            <Button
              onClick={() => {
                onOperationClick(item?.key);
              }}
              className={'operation-item'}
            >
              {item.label}
            </Button>
          );
        })}
      </div> */}
    </div>
  );
};

export default ComboTable;
