//    ○ 评审人：评审病案数量，目标数量，待评审数量，评审中数量，已评审数量，错误病案数量，平均分数
//     ○ 被评审人：被评审病案数量，已评审数量，错误病案数量，平均分数
//     ○ 编码员：被评审病案数量，已评审数量，错误病案数量，平均分数
import ReviewProgressBar from '@/pages/review/management/progress';

const reviewManagementRejectResolveProgressColumnItem = {
  dataIndex: 'ReviewedTaskCnt',
  title: '返修进度',
  visible: true,
  align: 'center',
  render: (node, record, index) => {
    return (
      <div>
        <ReviewProgressBar
          taskCount={record?.['TaskCnt']}
          targetCount={record?.['TargetTaskCnt']}
          items={[
            {
              label: '待整改',
              count: record?.['RejectedTaskCnt'],
              color: '#1464f8',
            },
            {
              label: '待复核',
              count: record?.['ReSubmittedTaskCnt'],
              color: '#605fba',
            },
            {
              label: '返修通过',
              count: record?.['AcceptedTaskCnt'],
              color: '#18ba56',
            },
          ]}
        />
      </div>
    );
  },
};

const reviewManagementStatsScoreColumnItem = {
  dataIndex: 'AvgScore',
  title: '平均分数',
  visible: true,
  width: 150,
  align: 'center',
  render: (node, record, index) => {
    return <span>{record?.['AvgScore']?.toFixed(2)}</span>;
  },
};

const reviewManagementStatsReviewerBasicColumns = [
  {
    dataIndex: 'ReviewedTaskCnt',
    title: '已评审数量',
    visible: true,
    align: 'center',
  },
];

const reviewManagementStatsBasicColumns = [
  {
    dataIndex: 'RejectedTaskCnt',
    title: '待整改数量',
    visible: true,
    width: 150,
    align: 'center',
  },
];

export const reviewManagementReviewerColumns = (
  enableResolve: boolean = false,
  enableScore: boolean = false,
) =>
  [
    {
      dataIndex: 'ReviewerCode',
      title: '评审人Code',
      visible: false,
    },
    {
      dataIndex: 'ReviewerName',
      title: '评审人',
      width: 150,
      visible: true,
      align: 'center',
    },
    {
      dataIndex: 'TaskCnt',
      title: '评审病案数量',
      width: 150,
      visible: true,
      align: 'center',
    },
    {
      dataIndex: 'TargetTaskCnt',
      title: '目标数量',
      width: 150,
      visible: true,
      align: 'center',
    },
    {
      dataIndex: 'progress',
      title: '评审进度',
      visible: true,
      align: 'center',
      render: (node, record, index) => {
        return (
          <div>
            <ReviewProgressBar
              taskCount={record?.['TaskCnt']}
              targetCount={record?.['TargetTaskCnt']}
              items={[
                {
                  label: '待评审',
                  count: record?.['PendingTaskCnt'],
                  color: '#3ba1ff',
                },
                {
                  label: '评审中',
                  count: record?.['ReviewingTaskCnt'],
                  color: '#F0D362',
                },
                {
                  label: '已评审',
                  count: record?.['ReviewedTaskCnt'],
                  color: '#18ba56',
                },
                {
                  label: '距离目标',
                  count: record?.['TargetTaskCnt'] - record?.['TaskCnt'],
                  color: 'transparent',
                },
              ]}
            />
          </div>
        );
      },
    },
    enableResolve === true
      ? reviewManagementRejectResolveProgressColumnItem
      : null,
    ...reviewManagementStatsReviewerBasicColumns,
    ...reviewManagementStatsBasicColumns,
    enableScore === true ? reviewManagementStatsScoreColumnItem : null,
  ]?.filter((item) => item !== null);
export const reviewManagementAuditeeColumns = (
  enableScore: boolean = false,
  revieweeNameTitle: string,
) =>
  [
    {
      dataIndex: 'RevieweeCode',
      title: '被评审人Code',
      visible: false,
    },
    {
      dataIndex: 'RevieweeName',
      title: revieweeNameTitle,
      visible: true,
      align: 'center',
    },
    {
      dataIndex: 'TaskCnt',
      title: '被评审病案数量',
      visible: true,
      align: 'center',
    },
    ...reviewManagementStatsBasicColumns,
    enableScore === true ? reviewManagementStatsScoreColumnItem : null,
  ]?.filter((item) => item !== null);
export const reviewManagementCoderColumns = (
  enableResolve: boolean = false,
  enableScore: boolean = false,
) =>
  [
    {
      dataIndex: 'Coder',
      title: 'Coder',
      visible: false,
    },
    {
      dataIndex: 'CoderName',
      title: '编码员',
      visible: true,
      align: 'center',
    },
    {
      dataIndex: 'TaskCnt',
      title: '被评审病案数量',
      visible: true,
      align: 'center',
    },
    enableResolve === true
      ? reviewManagementRejectResolveProgressColumnItem
      : null,
    ...reviewManagementStatsBasicColumns,
    enableScore === true ? reviewManagementStatsScoreColumnItem : null,
  ]?.filter((item) => item !== null);
