import {
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import { Dispatch, useDispatch, useRequest, useSelector } from 'umi';
import BedModificationModal from '../../../components/BedModificationModal';
import TemplateDownloadModal from '../../../components/TemplateDownloadModal';
import { useModel } from '@@/plugin-model/useModel';
import UniEditableTable, {
  EditableFormInstance,
} from '@uni/components/src/table/edittable';
import IconBtn from '@uni/components/src/iconBtn';
import {
  Button,
  Card,
  Checkbox,
  Divider,
  Dropdown,
  InputNumber,
  Modal,
  Popconfirm,
  Space,
  Table,
  Tooltip,
  Upload,
  message,
  Tabs,
  Badge,
} from 'antd';
import { useAsyncEffect, useDebounceFn, useSafeState } from 'ahooks';
import { v4 as uuidv4 } from 'uuid';
import { SwagInpatientAmtItem } from '../interface';
import {
  findUnlockedRecordHandler,
  isRespErr,
  summaryCountHandler,
} from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType, shortcuts } from '@/Constants';
import {
  CommonOdmEventConstants,
  InpatientColumnsCantEdit,
} from '../constants';
import './index.less';
import dayjs from 'dayjs';
import CorrectionModal from '@/components/CorrectionModal';
import { InFixedColumnsDaily } from '../columns';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { ProForm, ProFormInstance } from '@uni/components/src/pro-form';
import {
  IReducer,
  IModalState,
  ITableState,
  IEditableState,
} from '@uni/reducers/src/interface';
import {
  InitTableState,
  InitEditableState,
  TableAction,
  modalReducer,
  tableReducer,
  tableEditPropsReducer,
  EditableTableAction,
} from '@uni/reducers/src';
import { ActionType } from '@uni/components/src/table';
import {
  BulkChangeFormItems,
  CreateFormItems,
  DownloadTemplateFormItems,
} from './formItems';
import { ModalAction } from '@uni/reducers/src/modalReducer';
import { UseDispostionEnum, downloadFile } from '@uni/utils/src/download';
import {
  DownloadOutlined,
  InfoCircleFilled,
  UploadOutlined,
  ExclamationCircleFilled,
  ExclamationCircleOutlined,
  IssuesCloseOutlined,
} from '@ant-design/icons';
import { useHotkeys } from 'react-hotkeys-hook';
import ShortcutsHelpModal from '@/components/ShortcutsHelpModal';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { isEmptyValues } from '@uni/utils/src/utils';
import CalendarStats from '@/components/calendar/index';
import { uniCommonService } from '@uni/services/src/commonService';
import {
  autoCalculateHandler,
  autoComputeRef,
  getCalculationKeys,
} from '@/utils/util';

const autoSkipFocusKeys =
  (window as any).externalConfig?.['operationalDataManagement']
    ?.skipFocusKeys ?? [];

const useV2Page =
  (window as any).externalConfig?.['operationalDataManagement']?.useV2Page ??
  false;

const EditByDate = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { searchParams, dictData } = globalState;

  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  const [autoCalculationKeys, setAutoCalculationKeys] = useState([]);
  const getCalculationKeysDone = useRef(false);

  // table state
  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<SwagInpatientAmtItem>, IReducer>
  >(tableReducer, InitTableState);

  // table edit state
  const [EditableState, EditableDispatch] = useReducer<
    Reducer<IEditableState<SwagInpatientAmtItem>, IReducer>
  >(tableEditPropsReducer, InitEditableState);
  // 当用户点击sort或者filter导致此时数据与原本数据不同时保存
  const sortedFilteredTable = useRef([]);
  const [sorterInfo, setSorterInfo] = useState<any>({});
  // pro table action
  const editableTableActionRef = useRef<ActionType>();
  // pro table form
  const editableTableFormRef =
    useRef<EditableFormInstance<SwagInpatientAmtItem>>();

  // modal state
  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer>
  >(modalReducer, {
    visible: false,
    record: undefined,
    dataSource: [],
  });

  // create modal form
  const [modalForm] = ProForm.useForm<ProFormInstance>();

  const [modalInitValue, setModalInitValue] = useState(undefined);

  //
  const createChangeableState = useRef({
    hospCode: undefined,
    deptCode: undefined,
    exactDate: undefined,
  });

  // summary validate result
  const [summaryResult, setSummaryResult] = useState({
    IsValid: true,
    Message: '',
  });

  // checked 只显示审核错误数据
  const [onlyDiffed, setOnlyDiffed] = useState(false);
  const [editableFinalValue, setEditableFinalValue] = useState([]);

  // flag 用于判断用户有没有手动输入过 可以多个值
  const isAutoComputeCntInput = useRef(undefined);
  // 保存自动计算接口返回的数据
  const nowEditedRecord = useRef(null);

  // tab active key
  const [activeKey, setActiveKey] = useState('1');
  const [needBadge, setNeedBadge] = useState(false);

  // 当用户没保存就点击查询时的modal的checkbox状态
  const [isShowModalChecked, setIsShowModalChecked] = useState(false);

  // correction modal state
  const [correctionModalVisible, setCorrectionModalVisible] = useState(false);
  const [correctionRecord, setCorrectionRecord] = useState<any>(null);

  // 编辑时查询 / 离开页面的提示
  const ModalConfirmConfig = {
    title: '当前编辑尚未完成，是否保存？',
    icon: <ExclamationCircleOutlined />,
    content: (
      <>
        <div>
          <Checkbox
            onChange={(e) => setIsShowModalChecked(e.target.checked)}
            style={{ marginTop: 20 }}
          >
            是否要查询？
          </Checkbox>
        </div>

        <Space style={{ marginTop: 20, float: 'right' }}>
          <Tooltip title="将不会进行任何操作">
            <Button
              onClick={() => {
                Emitter.emit(
                  CommonOdmEventConstants.SEARCH_PARAMS_CHANGED_WHEN_EDITING,
                  { closeType: 'close' },
                );
              }}
            >
              关闭
            </Button>
          </Tooltip>
          <Tooltip title="将取消编辑，所有未保存内容将消失">
            <Button
              onClick={() => {
                Emitter.emit(
                  CommonOdmEventConstants.SEARCH_PARAMS_CHANGED_WHEN_EDITING,
                  { closeType: 'cancel' },
                );
              }}
            >
              取消
            </Button>
          </Tooltip>
          <Tooltip title="将保存编辑内容">
            <Button
              type="primary"
              onClick={() => {
                Emitter.emit(
                  CommonOdmEventConstants.SEARCH_PARAMS_CHANGED_WHEN_EDITING,
                  { closeType: 'needSave' },
                );
              }}
            >
              保存
            </Button>
          </Tooltip>
        </Space>
      </>
    ),
    okButtonProps: {
      style: { display: 'none' },
    },
    cancelButtonProps: {
      style: { display: 'none' },
    },
  };

  // summary 处理
  const handleSummary = (data) => {
    // 处理summary
    let summary = summaryCountHandler(data);
    TableDispatch({
      type: TableAction.summaryChange,
      payload: { summary: summary ?? {} },
    });
  };

  //  table data Req
  const tableReq = async (params, fromType = undefined) => {
    let res: any = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: 'DailyInpatientAmt',
        requestParams: [
          {
            url: 'Api/Dyn-ddr/DailyInpatientAmt/GetList',
            method: 'POST',
            data: {
              ...params,
            },
            dataType: 'dyn-ddr',
          },
        ],
      },
    });
    if (res.datas && res.datas.length > 0) {
      console.log(res.datas[0]?.data);
      // 保存数据
      TableDispatch({
        type: TableAction.dataChange,
        payload: { data: res.datas[0]?.data ?? [] },
      });
      EditableDispatch({
        type: EditableTableAction.editableValuesChange,
        payload: { value: res.datas[0]?.data ?? [] },
      });
    }
  };

  // 根据onlydiffed 与 editable.value 变化finalvalue
  useEffect(() => {
    if (onlyDiffed) {
      setEditableFinalValue(EditableState.value.filter((d) => d.Remark));
    } else {
      setEditableFinalValue(EditableState.value);
    }
  }, [onlyDiffed, EditableState.value]);
  // 根据sorterInfo finalvalue 变化 sortedFilteredTable
  useEffect(() => {
    //
    let sortedFilteredData = [...editableFinalValue];
    // Handle sort
    if (sorterInfo.columnKey && sorterInfo.order) {
      sortedFilteredData.sort((a, b) => {
        return _.isString(a[sorterInfo.columnKey])
          ? sorterInfo.order === 'ascend'
            ? a[sorterInfo.columnKey]?.localeCompare(b[sorterInfo.columnKey])
            : b[sorterInfo.columnKey]?.localeCompare(a[sorterInfo.columnKey])
          : sorterInfo.order === 'ascend'
          ? a[sorterInfo.columnKey] - b[sorterInfo.columnKey]
          : b[sorterInfo.columnKey] - a[sorterInfo.columnKey];
      });
    }
    // Update the ref
    sortedFilteredTable.current = sortedFilteredData;
    // handle Summary
    handleSummary(editableFinalValue);
  }, [sorterInfo, editableFinalValue]);

  const [nowAllInput, setNowAllInput] = useSafeState({
    inputs: [],
    clkTarget: undefined,
  });
  // 自动聚焦
  // 1. 存储该行所有的inputs
  const getAllInputs = (target = null, type = 'update') => {
    setTimeout(() => {
      let allInputs = document
        .querySelector('#odm_in_patients_amts')
        .querySelectorAll('input');
      if (!allInputs?.length) {
        setNowAllInput({
          inputs: [],
          clkTarget: undefined,
        });
      } else {
        // 编辑情况下 要过滤掉所有inputs中disabled的
        // 新增情况下 不应该做过滤 因为有先后编辑顺序
        if (type === 'create') {
          setNowAllInput({
            inputs: Array.from(allInputs),
            clkTarget: target,
          });
        } else {
          setNowAllInput({
            inputs: Array.from(allInputs)?.filter(
              (input) => input.disabled === false,
            ) as any,
            clkTarget: target,
          });
        }
      }
    }, 0);
  };
  // 2. 存储后导致rerender 并根据用户点击的栏位给对应焦点
  useEffect(() => {
    if (nowAllInput?.inputs?.length > 0) {
      for (let i = 0; i < nowAllInput?.inputs?.length; i++) {
        const element = nowAllInput?.inputs?.[i];
        // 如果有用户之前操作（双击 / 刚才所在行的特定列）的 焦点 则直接 强 走焦点
        if (nowAllInput.clkTarget) {
          if (
            element?.id?.split('_')?.at(1) ===
            (nowAllInput.clkTarget?.dataIndex || nowAllInput.clkTarget?.data)
          ) {
            element.focus();
            break;
          }
        } else {
          // 没有默认焦点 则根据autoSkipFocusKeys来过滤焦点 找到 第一个 不是autoSkipFocusKeys中的列 为焦点
          if (
            !element?.disabled &&
            autoSkipFocusKeys?.findIndex(
              (key) => key === element?.getAttribute('data-index'),
            ) === -1
          ) {
            element.focus();
            break;
          }
        }
      }
    }
  }, [nowAllInput, autoSkipFocusKeys]);

  // 模板操作 dropdown
  const templateDropdown = useMemo(() => {
    return [
      {
        label: (
          <Button
            key="getTemplate"
            type="text"
            icon={<DownloadOutlined />}
            disabled={!modalInitValue?.ExactDate || !modalInitValue?.HospCode}
            onClick={(e) => {
              ModalStateDispatch({
                type: ModalAction.change,
                payload: {
                  visible: true,
                  record: null,
                  actionType: 'Download',
                },
              });
              modalForm.setFieldsValue({
                fileName: `住院动态_每日_${dayjs(
                  modalInitValue?.ExactDate,
                )?.format('YYYY-MM-DD')}`,
              } as any);
            }}
          >
            模板
          </Button>
        ),
        key: 'download',
      },
      {
        label: (
          <Upload
            fileList={[]}
            accept={'.xlsx'}
            customRequest={async ({ file }) => {
              const formData = new FormData();
              formData.append('File', file);
              let res = await reqActionReq(formData, ReqActionType.Import);
            }}
          >
            <Button type="text" icon={<UploadOutlined />}>
              导入
            </Button>
          </Upload>
        ),
        key: 'import',
      },
    ];
  }, [modalInitValue]);

  // 那些 操作类的req 都在这里，比如：审核，锁定，解锁，
  const reqActionReq = async (
    reqData: any,
    reqType: ReqActionType,
    reqId = undefined,
  ) => {
    // 当使用V2页面时，如果是UpdateBedAmts接口，需要替换为BulkUpdateBedAmt
    const actionType = reqType;

    // 根据useV2Page确定API路径前缀
    const apiPrefix = 'Api/Dyn-ddr/DailyInpatientAmt/';

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `DailyInpatientAmt/${reqType}`,
        requestParams: {
          url:
            reqType === ReqActionType.DeptInpatientInitialize
              ? reqType
              : reqType === ReqActionType.Update && reqId
              ? `${apiPrefix}${actionType}/${reqId.id}`
              : `${apiPrefix}${actionType}`,
          method: 'POST',
          params: reqType === ReqActionType.Update && reqId ? reqId : undefined,
          data: reqData, // 数据统一放到调接口之前自己处理
          dataType: 'dyn-ddr',
          requestType: reqType === ReqActionType.Import ? 'form' : 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      // 审核 单独处理
      if (reqType === ReqActionType.Validate) {
        message.success('审核完成');
        setSummaryResult(
          res.data?.SummaryResults?.at(0) ?? { IsValid: true, Message: '' },
        );
        tableReq(modalInitValue, reqType);
      }
      // 特殊：initialize 初始化
      else if (reqType === ReqActionType.DeptInpatientInitialize) {
        editableTableFormRef.current?.setFieldsValue({
          [`${Object.keys(editableTableFormRef.current?.getFieldsValue())?.at(
            0,
          )}`]: res.data,
        });
      }
      // 新增
      else if (reqType === ReqActionType.Create) {
        // 先cancel
        editableTableActionRef.current.cancelEditable(
          Object.keys(editableTableFormRef.current?.getFieldsValue())
            ?.at(0)
            ?.includes('new')
            ? Object.keys(editableTableFormRef.current?.getFieldsValue())?.at(0)
            : parseInt(
                Object.keys(editableTableFormRef.current?.getFieldsValue())?.at(
                  0,
                ),
              ),
        );
        // 直接走tableApi
        tableReq(modalInitValue, reqType);
      }
      // 编辑 单独处理(自动打开下一行)
      else if (reqType === ReqActionType.Update) {
        EditableDispatch({
          type: EditableTableAction.editableSingleRecordChange,
          payload: {
            key: 'Id',
            value: res.data,
          },
        });
      }
      // 其他各种操作
      else {
        message.success('操作成功');
        tableReq(modalInitValue, reqType);
      }

      // modal 处理
      if (ModalState.visible) {
        ModalStateDispatch({
          type: ModalAction.init,
        });
      }
    }
  };

  // 统一处理报表的save与update 因为 可触发的方式太多
  // 并且 处理 update情况下的 键盘控制换行
  const editSaveHandler = async (recordKey, data, needAutoFetch = false) => {
    // 获取原始数据（从editableFinalValue中根据recordKey查找）
    const originalData = recordKey?.toString()?.includes('new')
      ? {} // 新建记录没有原始数据
      : editableFinalValue.find(
          (item) => item.Id?.toString() === recordKey?.toString(),
        ) || {};

    // 根据优先级拼接数据
    const mergedData = recordKey?.toString()?.includes('new')
      ? await reqActionReq(
          {
            // 优先级：data > nowEditedRecord.current > originalData
            ...(nowEditedRecord.current || {}), // 自动计算返回的数据
            ..._.omit(data?.[recordKey?.toString()] ?? data, 'Id'),
            HospCode:
              data?.[recordKey?.toString()]?.HospName?.value ??
              data?.[recordKey?.toString()]?.HospName ??
              data?.HospName?.value ??
              data?.HospName,
            ExactDate: modalInitValue?.ExactDate,
            DeptCode:
              data?.[recordKey?.toString()]?.DeptName?.value ??
              data?.DeptName?.value,
          },
          ReqActionType.Create,
        )
      : await reqActionReq(
          {
            // 优先级：data > nowEditedRecord.current > originalData
            ...(originalData || {}), // 原始数据（优先级最低）
            ...(nowEditedRecord.current || {}), // 自动计算返回的数据（优先级次之）
            ...(data?.[recordKey?.toString()] ?? data), // 当前编辑数据（优先级最高）
          },
          ReqActionType.Update,
          {
            id: recordKey?.toString(),
          },
        );

    if (needAutoFetch) {
      setModalInitValue({ ...modalInitValue, needFetch: true });
    }

    // 清空自动计算的数据，避免影响下次操作
    nowEditedRecord.current = null;
  };

  // columns editable handler
  // 修改处理方式：按照不能编辑的进行过滤 && 根据config配置的name来判断哪些格子需要算法
  const columnsEditHandler = (cols) => {
    let autoComputeKeys = autoCalculationKeys;
    return cols.map((d) => {
      return InpatientColumnsCantEdit?.findIndex((key) => key === d.data) ===
        -1 && d?.columnType === 'Int32'
        ? {
            renderFormItem: (text, props, dom) => {
              return (
                <InputNumber
                  style={{ width: '100%', minWidth: '60px' }}
                  size="small"
                  // bordered={false}
                  data-Index={d?.dataIndex}
                  disabled={!props?.record?.DeptName}
                  precision={d?.columnType === 'Decimal' ? 2 : 0}
                  keyboard={false}
                  controls={false}
                  {...{ index: props.dataIndex }}
                  onChange={(value) => {
                    // 做个特殊处理 在用户输入后，自动计算的触发置false
                    if (
                      autoComputeKeys?.findIndex(
                        (key) => key === d?.dataIndex,
                      ) !== -1
                    ) {
                      isAutoComputeCntInput.current = {
                        ...(isAutoComputeCntInput.current || {}),
                        [d?.dataIndex]: false,
                      };
                    }
                  }}
                />
              );
            },
            ...d,
            editable: true,
            onCell: (record) => ({
              onDoubleClick: () => {
                Emitter.emit(CommonOdmEventConstants.DBCLK_ON_EDITABLE_ITEM, {
                  record,
                  target: d,
                });
              },
            }),
          }
        : {
            ...d,
            editable: false,
            onCell: (record) => ({
              onDoubleClick: () => {
                if (!record.IsLocked) {
                  Emitter.emit(CommonOdmEventConstants.DBCLK_ON_EDITABLE_ITEM, {
                    record,
                    target: undefined,
                  });
                }
              },
            }),
          };
    });
  };

  // 处理columns 代码比较老 & 要可以实时进入下一行编辑等 写的比较长
  useEffect(() => {
    if (
      columnsList['DailyInpatientAmt'] &&
      dictData.DynDepts?.length > 0 &&
      getCalculationKeysDone.current
    ) {
      TableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: tableColumnBaseProcessor(
            InFixedColumnsDaily(
              dictData.Hospital,
              dictData.DynDepts,
              EditableState.value,
            ),
            columnsEditHandler(columnsList['DailyInpatientAmt']),
            'local',
          ),
        },
      });
    }
  }, [
    columnsList?.['DailyInpatientAmt'],
    dictData.DynDepts,
    editableFinalValue,
    nowAllInput, // 给columnsEditHandler使用
  ]);

  const [columnsSolver, setColumnsSolver] = useState([]);
  useEffect(() => {
    setColumnsSolver([
      ...TableState.columns,
      {
        dataIndex: 'option',
        title: '操作',
        visible: true,
        width: 40,
        align: 'center',
        valueType: 'option',
        fixed: 'right',
        render: (text, record: SwagInpatientAmtItem) => (
          <Space size={20}>
            {!record?.IsLocked && (
              <IconBtn
                type="lock"
                title="锁定"
                className="inpatients_blue-color"
                openPop={true}
                popOnConfirm={() => {
                  reqActionReq([record.Id], ReqActionType.Lock);
                }}
              />
            )}
            {record?.IsLocked && (
              <IconBtn
                type="unlock"
                title="解锁"
                className="inpatients_blue-color"
                openPop={true}
                popOnConfirm={() => {
                  reqActionReq([record.Id], ReqActionType.Unlock);
                }}
              />
            )}
          </Space>
        ),
      },
    ]);
  }, [TableState.columns]);

  // table data 的2个变化方式
  useEffect(() => {
    if (
      (searchParams && searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(modalInitValue) && searchParams?.singleDate)
    ) {
      if (EditableState.editableKeys.length > 0) {
        Modal.confirm(ModalConfirmConfig);
      }
      setModalInitValue({
        ExactDate: searchParams?.singleDate ?? searchParams?.dateRange?.at(1),
        HospCode:
          searchParams?.hospCodes ??
          (isEmptyValues(searchParams?.hospCode)
            ? []
            : Array.isArray(searchParams.hospCode)
            ? searchParams.hospCode
            : [searchParams.hospCode]),
        Sdate:
          searchParams?.singleDate ??
          searchParams?.dateRange?.at(0) ??
          dayjs()?.format('YYYY-MM-DD'),
        Edate:
          searchParams?.singleDate ??
          searchParams?.dateRange?.at(1) ??
          dayjs()?.format('YYYY-MM-DD'),
        needFetch: EditableState.editableKeys.length > 0 ? false : true,
      });
    }

    // 处理calendar点击跳转
    Emitter.on(CommonOdmEventConstants.CALENDAR_CARD_CLK, (record) => {
      (global?.window as any)?.eventEmitter?.emit(
        EventConstant.HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY,
        {
          ...{
            singleDate: dayjs(record.ExactDate)?.format('YYYY-MM-DD'),
          },
          searchNow: true,
        },
      );

      setActiveKey('1');
    });

    return () => {
      Emitter.off(CommonOdmEventConstants.CALENDAR_CARD_CLK);
    };
  }, [globalState]);

  useEffect(() => {
    if (
      modalInitValue?.ExactDate &&
      modalInitValue?.HospCode &&
      modalInitValue?.needFetch
    ) {
      setSummaryResult({ IsValid: true, Message: '' });
      tableReq(modalInitValue);
    }
  }, [modalInitValue]);

  // 处理doubleClk && 离开页面时的操作
  useEffect(() => {
    Emitter.on(
      CommonOdmEventConstants.DBCLK_ON_EDITABLE_ITEM,
      ({ record, target }) => {
        console.log('islocked', record);
        if (record.IsLocked) {
          message.warning('请先解锁');
          return;
        }

        if (EditableState.editableKeys?.length > 0) {
          if (EditableState.editableKeys?.at(0) === record.Id) {
            return;
          } else {
            // 保存 并跳转到对应的行
            Emitter.emit(EventConstant.ODM_CUSTOM_TABLE_SAVE, {
              targetKey: record.Id,
              dataIndex: target?.dataIndex,
            });
          }
        } else {
          EditableDispatch({
            type: EditableTableAction.editableKeysChange,
            payload: {
              editableKeys: [record.Id],
            },
          });
          getAllInputs(target);
        }
      },
    );

    const handleBeforeUnload = (event) => {
      if (EditableState?.editableKeys?.length > 0) {
        event.preventDefault();
        event.returnValue = '你确定要离开此页面？ 系统可能不会保存您所做的更改';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      Emitter.off(CommonOdmEventConstants.DBCLK_ON_EDITABLE_ITEM);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [EditableState.editableKeys]);

  // 处理刚进页面时就调getcalculation 判断哪些需要后端计算
  useAsyncEffect(async () => {
    let res = await getCalculationKeys('Inpatient');
    if (!isRespErr(res)) {
      isAutoComputeCntInput.current = autoComputeRef(Object.keys(res?.data));

      setAutoCalculationKeys(Object.keys(res?.data));
      getCalculationKeysDone.current = true;
    }
  }, []);

  // hotkeys
  shortcuts
    ?.filter((item) => item?.onlyForHint !== true)
    ?.map((item) => {
      useHotkeys(item.key, item.callback, {
        enableOnFormTags: true,
        enabled: item?.enabled,
        preventDefault: true,
        enableOnContentEditable: true,
        ...(item?.options ?? {}),
      });
    });

  // 快捷键测试
  useEffect(() => {
    // table 内置 快捷键 save 监听 [ctrl+s 默认不打开下一行编辑]
    Emitter.on(
      EventConstant.EDITABLE_SAVE_SHORTCUT,
      (record: { needAutoFetch: boolean }) => {
        // 重置 isAutoComputeCntInput
        isAutoComputeCntInput.current = autoComputeRef(autoCalculationKeys);
        let rowKey = Object.keys(
          editableTableFormRef.current?.getFieldsValue(),
        )?.at(0);
        let data: any = editableTableFormRef.current.getFieldsValue()?.[rowKey];
        if (rowKey?.includes('new')) {
          // TODO 做什么特殊处理？
        } else {
          EditableDispatch({
            type: EditableTableAction.editableKeysChange,
            payload: {
              editableKeys: [],
            },
          });
        }
        // 接口隐式调用
        editSaveHandler(rowKey, data, record?.needAutoFetch || false);
      },
    );

    // 外部自定义 save 监听 这边需要默认打开下一行
    Emitter.on(
      EventConstant.ODM_CUSTOM_TABLE_SAVE,
      (record: { targetKey: string; dataIndex: string }) => {
        // 重置 isAutoComputeCntInput
        isAutoComputeCntInput.current = autoComputeRef(autoCalculationKeys);

        let rowKey = Object.keys(
          editableTableFormRef.current?.getFieldsValue(),
        )?.at(0);
        let data: any = editableTableFormRef.current.getFieldsValue()?.[rowKey];
        // 如果没有rowKey 则返回
        if (!rowKey) return;
        // 如果rowKey 包含 new 开头的那就是新建的 特殊处理
        if (rowKey?.includes('new')) {
          // TODO 做什么特殊处理？
        } else {
          // 做一个处理 如果有target 则跳转到对应target的位置
          // 没有则正常逻辑 往下走
          if (record?.targetKey) {
            EditableDispatch({
              type: EditableTableAction.editableKeysChange,
              payload: {
                editableKeys: [record?.targetKey],
              },
            });
          } else {
            // 直接进行换行操作
            let rowIndex = sortedFilteredTable.current.findIndex(
              (d) => d.Id?.toString() === rowKey?.toString(),
            );
            let findEditOrEnd = false;
            while (!findEditOrEnd) {
              if (
                rowIndex !== -1 &&
                rowIndex < sortedFilteredTable.current.length - 1
              ) {
                if (sortedFilteredTable.current?.[rowIndex + 1]?.IsLocked) {
                  rowIndex++;
                } else {
                  findEditOrEnd = true;
                  EditableDispatch({
                    type: EditableTableAction.editableKeysChange,
                    payload: {
                      editableKeys: [
                        sortedFilteredTable.current?.[rowIndex + 1].Id,
                      ],
                    },
                  });
                }
              } else {
                findEditOrEnd = true;
                EditableDispatch({
                  type: EditableTableAction.editableKeysChange,
                  payload: {
                    editableKeys: [],
                  },
                });
              }
            }
          }
        }

        // 聚焦
        getAllInputs(
          record?.dataIndex ? { dataIndex: record?.dataIndex } : undefined,
        );
        // 接口隐式调用
        editSaveHandler(rowKey, data);
      },
    );

    // table 内置 快捷键 cancel 监听
    Emitter.on(
      EventConstant.EDITABLE_CANCEL_SHORTCUT,
      (record: { needAutoFetch: boolean }) => {
        // 重置 isAutoComputeCntInput
        isAutoComputeCntInput.current = autoComputeRef(autoCalculationKeys);
        editableTableFormRef.current.resetFields();
        editableTableActionRef.current.cancelEditable(
          Object.keys(editableTableFormRef.current?.getFieldsValue())
            ?.at(0)
            ?.includes('new')
            ? Object.keys(editableTableFormRef.current?.getFieldsValue())?.at(0)
            : parseInt(
                Object.keys(editableTableFormRef.current?.getFieldsValue())?.at(
                  0,
                ),
              ),
        );
        // 清空自动计算的记录
        nowEditedRecord.current = null;
        // reFetch
        if (record.needAutoFetch) {
          setModalInitValue({ ...modalInitValue, needFetch: true });
        }
      },
    );

    // table 内置 快捷键 ↑ ↓ ← → 监听 (每个做成单独的eventConstants 方便之后修改)
    // 先只处理 上 下
    Emitter.on(EventConstant.EDITABLE_UP_KEYDOWN_SHORTCUT, () => {
      // 1. 先判断能不能往上走
      let rowKey = Object.keys(
        editableTableFormRef.current?.getFieldsValue(),
      )?.at(0);
      let rowIndex = sortedFilteredTable.current?.findIndex(
        (item) => item.Id?.toString() === rowKey,
      );
      // 自身就在顶部
      if (rowIndex <= 0) return;
      // 找到第一条不是isLocked的record的index
      let firstNotIsLockedIndex = findUnlockedRecordHandler(
        sortedFilteredTable.current,
        rowIndex,
        'UP',
      );
      if (firstNotIsLockedIndex === -1) return;

      // 2. 抓 焦点dom 的 dataIndex / key
      let dataIndex = document.activeElement.getAttribute('data-index');

      Emitter.emit(EventConstant.ODM_CUSTOM_TABLE_SAVE, {
        targetKey: sortedFilteredTable.current?.[firstNotIsLockedIndex]?.Id,
        dataIndex,
      });
    });

    Emitter.on(EventConstant.EDITABLE_DOWN_KEYDOWN_SHORTCUT, () => {
      // 1. 先判断能不能往上走
      let rowKey = Object.keys(
        editableTableFormRef.current?.getFieldsValue(),
      )?.at(0);
      let rowIndex = sortedFilteredTable.current?.findIndex(
        (item) => item.Id?.toString() === rowKey,
      );
      if (rowIndex === -1 || rowIndex >= sortedFilteredTable.current.length - 1)
        return;
      // 找到第一条不是isLocked的record的index
      let firstNotIsLockedIndex = findUnlockedRecordHandler(
        sortedFilteredTable.current,
        rowIndex,
        'DOWN',
      );
      if (firstNotIsLockedIndex === -1) return;

      // 2. 抓 焦点dom 的 dataIndex / key
      let dataIndex = document.activeElement.getAttribute('data-index');

      Emitter.emit(EventConstant.ODM_CUSTOM_TABLE_SAVE, {
        targetKey: sortedFilteredTable.current?.[firstNotIsLockedIndex]?.Id,
        dataIndex,
      });
    });

    // 左移右移focus框
    Emitter.onMultiple(
      [EventConstant.ODM_GO_LEFT, EventConstant.ODM_GO_RIGHT],
      ({ event, indexOffset }) => {
        // 先抓index
        let index = -1;
        let { inputs } = nowAllInput;
        for (let i = 0; i < inputs?.length; i++) {
          if (inputs[i] === document.activeElement) index = i;
        }
        // go left
        if (
          event.key === 'ArrowLeft' ||
          (event.shiftKey && event.key === 'Tab')
        ) {
          if (index !== inputs.length - 1 && index > 0) {
            inputs[index - 1].focus();
          }
        }
        // go right
        else {
          if (index !== -1 && index < inputs.length - 1) {
            inputs[index + 1].focus();
          } else if (index === inputs.length - 1) {
            Emitter.emit(EventConstant.ODM_CUSTOM_TABLE_SAVE);
          } else {
            // -1
            console.error('-1了，寄');
          }
        }
      },
    );

    // 编辑状态下 点击重新查询等其他莫名其妙的操作时
    Emitter.on(
      CommonOdmEventConstants.SEARCH_PARAMS_CHANGED_WHEN_EDITING,
      ({ closeType }) => {
        if (closeType === 'needSave') {
          // 保存关闭
          Emitter.emit(EventConstant.EDITABLE_SAVE_SHORTCUT, {
            needAutoFetch: isShowModalChecked,
          });
          Modal.destroyAll();
        } else if (closeType === 'cancel') {
          // 取消关闭
          Emitter.emit(EventConstant.EDITABLE_CANCEL_SHORTCUT, {
            needAutoFetch: isShowModalChecked,
          });
          Modal.destroyAll();
        } else {
          // 单纯的关闭
          Modal.destroyAll();
        }
        setIsShowModalChecked(false);
      },
    );

    return () => {
      Emitter.off(EventConstant.EDITABLE_SAVE_SHORTCUT);
      Emitter.off(EventConstant.ODM_CUSTOM_TABLE_SAVE);
      Emitter.off(EventConstant.EDITABLE_CANCEL_SHORTCUT);
      Emitter.off(EventConstant.EDITABLE_UP_KEYDOWN_SHORTCUT);
      Emitter.off(EventConstant.EDITABLE_DOWN_KEYDOWN_SHORTCUT);
      Emitter.offMultiple([
        EventConstant.ODM_GO_LEFT,
        EventConstant.ODM_GO_RIGHT,
      ]);
      Emitter.off(CommonOdmEventConstants.SEARCH_PARAMS_CHANGED_WHEN_EDITING);
    };
  }, [
    modalInitValue,
    editableFinalValue,
    nowAllInput,
    isShowModalChecked,
    autoCalculationKeys,
  ]);

  // 后端计算 debounce
  const { run: autoCalculateDebounceFn } = useDebounceFn(
    async (keys, record, formRef, isAutoComputInputs) => {
      const data = await autoCalculateHandler(
        keys,
        record,
        formRef,
        'DailyInpatientAmt',
        isAutoComputInputs,
      );
      // 保存接口返回的数据到 nowEditedRecord.current
      if (data) {
        nowEditedRecord.current = data;
      }
      return data; // 返回接口响应数据
    },
    { wait: 250 },
  );

  // tabs items
  const tabsItems = [
    {
      label: <Space>每日住院登记</Space>,
      key: '1',
      forceRender: true,
      children: (
        <UniEditableTable
          id="odm_in_patients_amts"
          className="odm_main_table"
          rowKey="Id"
          forceColumnsUpdate
          enableShortcuts
          bordered
          dictionaryData={dictData}
          showSorterTooltip={false}
          loading={
            loadings?.['DailyInpatientAmt'] ??
            loadings?.[`DailyInpatientAmt/${ReqActionType.Initialize}`] ??
            loadings?.[`DailyInpatientAmt/${ReqActionType.Create}`] ??
            loadings?.[`DailyInpatientAmt/${ReqActionType.Update}`] ??
            false
          }
          columns={columnsSolver} // columnsHandler
          scroll={{ x: 'max-content', y: `calc(100vh - 470px)` }}
          widthDetectAfterDictionary={true}
          size="small"
          pagination={false}
          actionRef={editableTableActionRef}
          editableFormRef={editableTableFormRef}
          value={editableFinalValue}
          onTableChange={(pagination, filters, sorter) => {
            setSorterInfo(sorter);
          }}
          rowClassName={(record) => {
            if (
              record?.Id?.toString() ===
              EditableState.editableKeys?.at(0)?.toString()
            ) {
              return 'editing-row';
            }
            return record?.Remark ? 'remarked-row' : '';
          }}
          editable={{
            type: 'single',
            editableKeys: EditableState.editableKeys,
            onValuesChange: (record, dataSource) => {
              // 这里，新建 && 当科室/日期 有值 且 变化的时候，调Initialize
              if (record?.Id?.toString()?.includes('new')) {
                let initializeReqData = {
                  hospCode: record?.HospName?.value ?? record?.HospName,
                  deptCode: record?.DeptName?.value,
                  exactDate: record?.ExactDate,
                };
                // 如果是院区变化 则必须将科室置空 让用户重新选择
                if (
                  (createChangeableState.current?.hospCode &&
                    initializeReqData?.hospCode !==
                      createChangeableState.current?.hospCode) ||
                  !initializeReqData?.deptCode
                ) {
                  editableTableFormRef.current.setFieldValue(
                    [
                      `${Object.keys(
                        editableTableFormRef.current?.getFieldsValue(),
                      )?.at(0)}`,
                      'DeptName',
                    ],
                    undefined,
                  );
                } else {
                  // 然后是判断要不要调接口
                  let noNeedChange = _.isEqual(
                    createChangeableState.current,
                    initializeReqData,
                  );

                  if (!noNeedChange) {
                    reqActionReq(
                      initializeReqData,
                      ReqActionType.DeptInpatientInitialize,
                    );
                  }
                }
                createChangeableState.current = initializeReqData;
              }

              // 判断是否要自动计算
              if (autoCalculationKeys?.length > 0) {
                // 调接口
                autoCalculateDebounceFn(
                  autoCalculationKeys,
                  record,
                  editableTableFormRef,
                  isAutoComputeCntInput.current,
                );
              }
            },
            onChange: (editableKeys, editableRows) => {
              EditableDispatch({
                type: EditableTableAction.editableKeysChange,
                payload: { editableKeys },
              });
            },
            actionRender: (row, config, defaultDom) => [
              <IconBtn
                key="correct"
                type="edit"
                title="修正"
                className="inpatients_blue-color"
                customIcon={<IssuesCloseOutlined />}
                onClick={() => {
                  let rowKey = Object.keys(
                    editableTableFormRef.current?.getFieldsValue(),
                  )?.at(0);
                  let formValue: any =
                    editableTableFormRef.current.getFieldsValue()?.[rowKey];
                  let dataRecord = editableFinalValue?.find(
                    (item) => item?.Id?.toString() === rowKey,
                  );
                  // 按优先级拼接数据：formValue > nowEditedRecord.current > dataRecord
                  setCorrectionRecord({
                    ...dataRecord,
                    ...(nowEditedRecord.current || {}),
                    ...formValue,
                  });
                  setCorrectionModalVisible(true);
                }}
              />,
            ],
            onCancel: async (rowKey, data, row) => {
              return new Promise((resolve) => {
                resolve(true);
                getAllInputs();
              });
            },
          }}
          recordCreatorProps={false}
          summary={() =>
            columnsSolver?.length > 0 && TableState?.summary ? (
              <Table.Summary fixed>
                <Table.Summary.Row
                  className={summaryResult?.IsValid ? '' : 'remarked-row'}
                >
                  {columnsSolver
                    ?.filter((d) => d.visible)
                    ?.map((d, i, arr) => {
                      if (i === 0) {
                        return (
                          <Table.Summary.Cell
                            index={i}
                            key={'summary'}
                            colSpan={1}
                          >
                            <Tooltip title={summaryResult?.Message}>
                              <InfoCircleFilled
                                className="inpatients_red-color"
                                style={{
                                  display: !summaryResult?.IsValid
                                    ? 'inline-block'
                                    : 'none',
                                  marginRight: !summaryResult?.IsValid
                                    ? '10px'
                                    : 0,
                                }}
                              />
                            </Tooltip>
                            总计
                          </Table.Summary.Cell>
                        );
                      } else if (
                        d?.columnType === 'Int32' ||
                        d?.columnType === 'Decimal'
                      ) {
                        return (
                          <Table.Summary.Cell index={i} key={d?.dataIndex}>
                            <div style={{ textAlign: 'right' }}>
                              {TableState.summary[d?.dataIndex]}
                            </div>
                          </Table.Summary.Cell>
                        );
                      } else {
                        return (
                          <Table.Summary.Cell index={i} key={d?.dataIndex}>
                            {<p style={{ visibility: 'hidden' }}>-</p>}
                          </Table.Summary.Cell>
                        );
                      }
                    })}
                </Table.Summary.Row>
              </Table.Summary>
            ) : null
          }
        />
      ),
    },
    {
      label: (
        <Space>
          <span>本月数据</span>
          <Badge
            count={
              needBadge ? (
                <ExclamationCircleFilled style={{ color: '#f5222d' }} />
              ) : (
                ''
              )
            }
            // offset={[0, -10]}
          ></Badge>
        </Space>
      ),
      key: '2',
      forceRender: true,
      children: (
        <CalendarStats
          modalInitValue={modalInitValue}
          activeKey={activeKey}
          apiUrl="Api/Dyn-ddr/InpatientAmts/GetStats"
          setNeedBadge={setNeedBadge}
        />
      ),
    },
  ];

  return (
    <>
      {/* <Prompt
        when={EditableState?.editableKeys?.length > 0}
        message="你确定要离开此页面？ 系统可能不会保存您所做的更改"
      /> */}

      {/* 以前的part */}
      <div
        id={'detail-table-persist-container'}
        className={'detail-table-persist-container'}
      >
        <Card bodyStyle={{ paddingTop: '0px' }}>
          <Tabs
            activeKey={activeKey}
            items={tabsItems}
            onTabClick={(key) => {
              if (EditableState.editableKeys?.length > 0) {
                // message.warning('请先完成当前编辑');
                Modal.confirm(ModalConfirmConfig);
                return;
              }
              // 做一个提示 如果当前在编辑的话 则
              setActiveKey(key);
            }}
            tabBarExtraContent={
              activeKey === '1' && (
                <>
                  <Space>
                    <Checkbox
                      onChange={(e) => {
                        setOnlyDiffed(e.target.checked);
                      }}
                      disabled={EditableState.editableKeys?.length > 0}
                    >
                      仅显示审核错误
                    </Checkbox>
                    {EditableState?.editableKeys?.length > 0 && (
                      <>
                        <Button
                          type="primary"
                          onClick={(e) => {
                            Emitter.emit(EventConstant.EDITABLE_SAVE_SHORTCUT);
                          }}
                        >
                          保存
                        </Button>
                        <Popconfirm
                          title={'取消后，未保存的数据将丢失'}
                          onConfirm={(e) => {
                            Emitter.emit(
                              EventConstant.EDITABLE_CANCEL_SHORTCUT,
                            );
                          }}
                        >
                          <Button>取消</Button>
                        </Popconfirm>
                      </>
                    )}
                    <Button
                      key="updateBedAmt"
                      disabled={
                        EditableState?.value?.length === 0 ||
                        EditableState?.editableKeys?.length > 0
                      }
                      loading={loadings[`DailyInpatientAmt/BulkUpdateBedAmt`]}
                      onClick={() => {
                        ModalStateDispatch({
                          type: ModalAction.change,
                          payload: {
                            visible: true,
                            record: null,
                            actionType: 'Batch',
                          },
                        });
                        modalForm.setFieldsValue(modalInitValue as any);
                      }}
                    >
                      修改床位
                    </Button>

                    <Dropdown
                      menu={{ items: templateDropdown }}
                      disabled={
                        loadings[`DailyInpatientAmt/${ReqActionType.Import}`] ||
                        EditableState?.editableKeys?.length > 0
                      }
                    >
                      <Button
                        loading={
                          loadings[`DailyInpatientAmt/${ReqActionType.Import}`]
                        }
                      >
                        导入
                      </Button>
                    </Dropdown>

                    <Popconfirm
                      key="review"
                      title="确定审核？"
                      onConfirm={() => {
                        reqActionReq(modalInitValue, ReqActionType.Validate);
                      }}
                      disabled={EditableState?.value?.length === 0}
                    >
                      <Button
                        disabled={
                          EditableState?.value?.length === 0 ||
                          EditableState?.editableKeys?.length > 0
                        }
                        loading={
                          loadings[
                            `DailyInpatientAmt/${ReqActionType.Validate}`
                          ]
                        }
                      >
                        审核
                      </Button>
                    </Popconfirm>
                    <Popconfirm
                      key="lock"
                      title="确定锁定？"
                      onConfirm={() => {
                        reqActionReq(
                          modalInitValue,
                          ReqActionType.BulkLock,
                          true,
                        );
                      }}
                      disabled={EditableState?.value?.length === 0}
                    >
                      <Button
                        disabled={
                          EditableState?.value?.length === 0 ||
                          EditableState?.editableKeys?.length > 0
                        }
                        loading={
                          loadings[
                            `DailyInpatientAmt/${ReqActionType.BulkLock}`
                          ]
                        }
                      >
                        锁定
                      </Button>
                    </Popconfirm>
                    <Popconfirm
                      key="unlock"
                      title="确定解锁？"
                      onConfirm={() => {
                        reqActionReq(modalInitValue, ReqActionType.BulkUnlock);
                      }}
                      okButtonProps={{
                        loading:
                          loadings[
                            `DailyInpatientAmt/${ReqActionType.BulkUnlock}`
                          ],
                      }}
                      disabled={EditableState?.value?.length === 0}
                    >
                      <Button
                        disabled={
                          EditableState?.value?.length === 0 ||
                          EditableState?.editableKeys?.length > 0
                        }
                        loading={
                          loadings[
                            `DailyInpatientAmt/${ReqActionType.BulkUnlock}`
                          ]
                        }
                      >
                        解锁
                      </Button>
                    </Popconfirm>
                    <Button
                      key="multiCreate"
                      type="primary"
                      disabled={
                        !modalInitValue?.ExactDate ||
                        !modalInitValue?.HospCode ||
                        EditableState?.editableKeys?.length > 0
                      }
                      loading={
                        loadings?.[
                          `DailyInpatientAmt/${ReqActionType.Initialize}`
                        ]
                      }
                      onClick={(e) => {
                        reqActionReq(modalInitValue, ReqActionType.Initialize);
                      }}
                    >
                      批量新建
                    </Button>
                    <Button
                      key="create"
                      disabled={
                        !modalInitValue?.ExactDate ||
                        !modalInitValue?.HospCode ||
                        EditableState?.editableKeys?.length > 0
                      }
                      onClick={(e) => {
                        editableTableActionRef.current.addEditRecord(
                          {
                            Id: `new-${uuidv4()}`,
                            ExactDate: modalInitValue?.ExactDate,
                            HospName:
                              modalInitValue?.HospCode?.length === 1
                                ? modalInitValue?.HospCode?.at(0)
                                : undefined,
                          },
                          { position: 'top' },
                        );
                        getAllInputs(null, 'create');
                      }}
                    >
                      单条新建
                    </Button>
                    <Divider type="vertical" />
                    <IconBtn
                      type="keybord"
                      onClick={() => Emitter.emit(EventConstant.ODM_HELP_MODAL)}
                    />
                    <TableColumnEditButton
                      {...{
                        columnInterfaceUrl:
                          'Api/Dyn-ddr/DailyInpatientAmt/GetList',
                        onTableRowSaveSuccess: (columns) => {
                          // 这个columns 存到dva
                          dispatch({
                            type: 'global/saveColumns',
                            payload: {
                              name: 'DailyInpatientAmt',
                              value: columns,
                            },
                          });
                          TableDispatch({
                            type: TableAction.columnsChange,
                            payload: {
                              columns: tableColumnBaseProcessor(
                                InFixedColumnsDaily(
                                  dictData.Hospital,
                                  dictData.DynDepts,
                                  EditableState.value,
                                ),
                                columnsEditHandler(columns),
                              ),
                            },
                          });
                        },
                      }}
                    />
                  </Space>
                </>
              )
            }
          />
        </Card>
      </div>
      {/* 批量修改床位数据Modal */}
      <BedModificationModal
        visible={ModalState.visible && ModalState.actionType === 'Batch'}
        hospList={dictData.Hospital}
        deptList={dictData.DynDepts}
        apiUrl={'Api/Dyn-ddr/InpatientAmts/BulkUpdateBedAmt'}
        onCancel={() => {
          ModalStateDispatch({ type: ModalAction.init });
        }}
        onOk={async (values, apiResponse) => {
          // 请求成功后调用tableReq刷新数据
          await tableReq(modalInitValue, 'BulkUpdateBedAmt');
        }}
        initialValues={modalInitValue}
      />

      {/* 模板下载Modal */}
      <TemplateDownloadModal
        visible={ModalState.visible && ModalState.actionType === 'Download'}
        apiUrl="Api/Dyn-ddr/DailyInpatientAmt/GetTemplate"
        initialValues={{
          ...modalInitValue,
          fileName: `住院动态_每日_${dayjs(modalInitValue?.ExactDate)?.format(
            'YYYY-MM-DD',
          )}`,
        }}
        onCancel={() => {
          ModalStateDispatch({ type: ModalAction.init });
        }}
        onSuccess={async () => {
          message.success('模板下载成功');
        }}
      />

      <CorrectionModal
        visible={correctionModalVisible}
        onCancel={(hasFetched) => {
          setCorrectionModalVisible(false);
          if (hasFetched) {
            EditableDispatch({
              type: EditableTableAction.editableKeysChange,
              payload: { editableKeys: [] },
            });
            tableReq(modalInitValue);
            nowEditedRecord.current = null;
          }
        }}
        record={correctionRecord}
        deptName={correctionRecord?.DeptName}
        exactDate={correctionRecord?.ExactDate}
        columns={TableState.columns}
        apiType="InpatientAmts"
      />

      <ShortcutsHelpModal shortcuts={shortcuts} />
    </>
  );
};

export default EditByDate;
