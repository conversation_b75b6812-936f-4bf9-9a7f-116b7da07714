import { ReviewTypeFastSelectContainer } from '@/pages/review/components/header';
import UniTableNG from '@uni/components/src/tanstack-table';
import { isEmptyValues } from '@uni/utils/src/utils';
import React, { useEffect, useRef, useState } from 'react';
import { TableProps } from 'antd';
import { useUpdateEffect } from 'ahooks';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns, TableResp } from '@uni/commons/src/interfaces';
import {
  BatchItem,
  TaskItem,
  TaskStatusSummary,
} from '@/pages/review/interface';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { dmrReviewBasicColumns } from '@/pages/review/columns';
import { useModel } from 'umi';
import { summaryKeyProcessor } from '@/pages/review/utils';
import { v4 as uuidv4 } from 'uuid';

interface ReviewTableProps {
  id: string;
  masterId?: string | number;
  className?: string;

  interfaceUrl?: string;

  scroll?: any;
  batchId?: string;
  batchInfo?: BatchItem;
  taskTableRef: any;
  dmrPreviewContainerRef: any;
  extraColumns?: any[];
  extraHiddenColumns?: any[];
  taskExtraParams?: () => any;

  summaryInfo?: TaskStatusSummary;
  onSummarySelectKeysFilter?: (item) => boolean;

  fastSelectShow?: boolean;
  cancelProps?: {
    showCancel: boolean;
    dmrReviewerContainerRef: any;
  };

  reviewProps?: {
    showAccept?: boolean;
    dmrReviewerContainerRef: any;
    showOtherExamineTask?: boolean;
  };

  fastSelectDefaultKey?: string;

  noBatchIdInData?: boolean;
  noMasterIdInData?: boolean;

  tabTitleContainerRef?: any;

  extraLoading?: boolean;

  overrideBasicColumns?: any[];
}

export const getUserEmployeeCode = () => {
  let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
  return userInfo?.EmployeeCode;
};

const ReviewTable = (props: ReviewTableProps) => {
  const [tasks, setTasks] = useState<TaskItem[]>([]);

  const dmrReviewSummaryContainerRef = useRef(null);

  const [taskTableColumns, setTaskTableColumns] = useState<any>([]);

  const [sorterFilter, setSorterFilter] = useState({});

  const { globalState } = useModel('@@qiankunStateFromMaster');

  React.useImperativeHandle(props?.taskTableRef, () => {
    return {
      getFastSelectKeyToStatuses: () => {
        let data = {};
        summaryKeyProcessor(
          dmrReviewSummaryContainerRef?.current?.selectedKey,
          data,
        );

        return data;
      },
      getTaskSize: () => {
        return tasks?.length ?? 0;
      },
      freshQueryTable: () => {
        let pagination = {
          ...backPagination,
          current: 1,
          total: 0,
        };
        setBackPagination(pagination);

        tasksReq(1, backPagination.pageSize);
      },
      updateCurrentTaskId: async (taskId: number) => {
        let currentTaskItem: TaskItem = await getTaskItem(taskId);
        if (!isEmptyValues(currentTaskItem)) {
          // 更新当前的taskItem
          let currentTaskIndexInData = tasks?.findIndex(
            (item) => item.TaskId === taskId,
          );

          if (currentTaskIndexInData > -1) {
            tasks[currentTaskIndexInData] = {
              ...(tasks[currentTaskIndexInData] ?? {}),
              ...currentTaskItem,
            };
            setTasks([...tasks]);
          }
        }

        return currentTaskItem;
      },
      queryTasksCurrent: () => {
        tasksReq(backPagination?.current, backPagination.pageSize);
      },
      setTaskTableColumns: (columns: any[]) => {
        setTaskTableColumns(
          tableColumnBaseProcessor(
            props?.overrideBasicColumns ??
              dmrReviewBasicColumns(
                props?.dmrPreviewContainerRef,
                props?.extraHiddenColumns ?? [],
                props?.cancelProps?.showCancel,
                props?.reviewProps?.showAccept,
                props?.cancelProps?.dmrReviewerContainerRef,
              ),
            columns?.concat(props?.extraColumns ?? []),
          ),
        );
      },
    };
  });

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 50,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  useEffect(() => {
    tasksColumnReq();
  }, []);

  useUpdateEffect(() => {
    if (!isEmptyValues(props?.batchInfo)) {
      setBackPagination({
        ...backPagination,
        current: 1,
      });
      tasksReq(1, backPagination.pageSize);
    }
  }, [props?.batchInfo]);

  const { loading: tasksColumnsLoading, run: tasksColumnReq } = useRequest(
    () => {
      return uniCommonService(
        props?.interfaceUrl ?? 'Api/Dmr/DmrCardQualityExamine/GetTasks',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          setTaskTableColumns(
            tableColumnBaseProcessor(
              props?.overrideBasicColumns ??
                dmrReviewBasicColumns(
                  props?.dmrPreviewContainerRef,
                  props?.extraHiddenColumns ?? [],
                  props?.cancelProps?.showCancel,
                  props?.reviewProps?.showAccept,
                  props?.cancelProps?.dmrReviewerContainerRef,
                ),
              response?.data?.Columns?.concat(props?.extraColumns ?? []),
            ),
          );
        } else {
          setTaskTableColumns([]);
        }
      },
    },
  );

  const { loading: tasksLoading, run: tasksReq } = useRequest(
    (current, pageSize) => {
      let data = {
        DtParam: {
          Draw: 1,
          Start: (current - 1) * pageSize,
          Length: pageSize,
        },
        BatchId: props?.batchInfo?.BatchId,
        MasterId: props?.masterId,
        ...(props?.taskExtraParams() ?? {}),
      };

      summaryKeyProcessor(
        dmrReviewSummaryContainerRef?.current?.selectedKey,
        data,
      );

      if (props?.noBatchIdInData === true) {
        delete data['BatchId'];
      }

      if (props?.noMasterIdInData === true) {
        delete data['MasterId'];
      }

      if (!isEmptyValues(sorterFilter)) {
        delete global['tableParameters'];
        data['DtParam'] = {
          ...data['DtParam'],
          ...sorterFilter,
        };
      }

      return uniCommonService(
        props?.interfaceUrl ?? 'Api/Dmr/DmrCardQualityExamine/GetTasks',
        {
          method: 'POST',
          data: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableResp<any, TaskItem>>) => {
        return response;
      },
      onSuccess: (response: RespVO<TableResp<any, TaskItem>>, params: any) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          if (response?.cancelled === true) {
            return;
          }

          if (isEmptyValues(response?.data?.data)) {
            // 跳出 不设定值
            if (params[0] === 1) {
              setTasks([]);
            }
            return;
          }

          let currentData = tasks?.slice();
          if (params[0] === 1) {
            currentData = [];
            document.getElementById('tanstack-table-container').scrollTo({
              behavior: 'smooth',
              top: 0,
            });
          }

          let tableDataSource = response?.data?.data.slice();
          currentData = currentData?.concat(
            tableDataSource.map((item) => {
              item['id'] = uuidv4();

              return item;
            }),
          );
          setTasks(currentData);

          setBackPagination({
            ...backPagination,
            total: response?.data?.recordsFiltered || 0,
          });

          props?.tabTitleContainerRef?.current?.setTotalCount(
            response?.data?.recordsFiltered ?? 0,
          );
        } else {
          setTasks([]);
          setBackPagination({
            ...backPagination,
            current: 1,
            total: 0,
          });

          props?.tabTitleContainerRef?.current?.setTotalCount(0);
        }
      },
    },
  );

  const { loading: tasksFullLoading, run: tasksFullReq } = useRequest(
    () => {
      let data = {
        DtParam: {
          Draw: 1,
          Start: 0,
          Length: tasks?.length,
        },
        BatchId: props?.batchInfo?.BatchId,
        MasterId: props?.masterId,
        ...(props?.taskExtraParams() ?? {}),
      };

      summaryKeyProcessor(
        dmrReviewSummaryContainerRef?.current?.selectedKey,
        data,
      );

      if (props?.noBatchIdInData === true) {
        delete data['BatchId'];
      }

      if (props?.noMasterIdInData === true) {
        delete data['MasterId'];
      }

      if (!isEmptyValues(sorterFilter)) {
        delete global['tableParameters'];
        data['DtParam'] = {
          ...data['DtParam'],
          ...sorterFilter,
        };
      }

      return uniCommonService(
        props?.interfaceUrl ?? 'Api/Dmr/DmrCardQualityExamine/GetTasks',
        {
          method: 'POST',
          data: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableResp<any, TaskItem>>) => {
        return response;
      },
    },
  );

  const { run: getTaskItem } = useRequest(
    (taskId: string) => {
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetTask', {
        method: 'POST',
        data: {
          TaskId: taskId,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TaskItem>) => {
        return response?.data;
      },
    },
  );

  return (
    <>
      {props?.fastSelectShow !== false && (
        <ReviewTypeFastSelectContainer
          defaultSelectedKey={props?.fastSelectDefaultKey}
          containerRef={dmrReviewSummaryContainerRef}
          summaryInfo={props?.summaryInfo}
          onSummarySelectKeysFilter={props?.onSummarySelectKeysFilter}
          onSummarySwitch={() => {
            setBackPagination({
              ...backPagination,
              current: 1,
            });
            // TODO 还要判定是否 === 1 来是不是重置一套
            tasksReq(1, backPagination.pageSize);
          }}
        />
      )}
      <UniTableNG
        id={props?.id}
        className={props?.className ?? ''}
        rowKey={'id'}
        scroll={props?.scroll ?? { x: 'max-content', y: 380 }}
        loading={
          tasksLoading || tasksColumnsLoading || (props?.extraLoading ?? false)
        }
        columns={taskTableColumns}
        dataSource={tasks}
        backendPagination={true}
        dictionaryData={globalState?.dictData}
        virtualized={false}
        widthCalculate={true}
        infiniteScroll={true}
        bottomReachMargin={200}
        infiniteScrollPageSize={backPagination?.pageSize}
        infiniteScrollSorterFilter={(sorter: any, filter: any) => {
          let sorterFilter = {
            order: [],
            columns: [],
          };

          if (!isEmptyValues(sorter)) {
            if (sorter?.order) {
              let orderItem = taskTableColumns?.find(
                (item) =>
                  item.dataIndex === (sorter?.columnKey || sorter?.field),
              );

              if (!isEmptyValues(orderItem)) {
                sorterFilter?.columns.push({
                  Data: sorter?.columnKey || sorter?.field,
                });
                sorterFilter?.order.push({
                  column: 0,
                  dir: sorter?.order === 'ascend' ? 'asc' : 'desc',
                });
              }
            }
          }

          if (!isEmptyValues(filter)) {
          }

          let pagination = {
            ...backPagination,
            current: 1,
            total: 0,
          };

          setSorterFilter(sorterFilter);
          setBackPagination(pagination);

          setTimeout(() => {
            tasksReq(1, backPagination.pageSize);
          }, 0);
        }}
        fetchNextPage={(pageIndex, pageSize) => {
          if (tasks?.length >= backPagination?.total) {
            return;
          }

          let pagination = {
            ...backPagination,
            current: backPagination?.current + 1,
            total: 0,
          };
          setBackPagination(pagination);

          tasksReq(pagination?.current, backPagination.pageSize);

          return [];
        }}
      />
    </>
  );
};

export default ReviewTable;
