import IconBtn from '@uni/components/src/iconBtn/index';
import { Emitter } from '@uni/utils/src/emitter';
import { Space, Button } from 'antd';
import { EVENT_BORROW_TASK } from '../../constants';

export const DetailsTableColumns = [
  {
    data: 'xuhao',
    dataIndex: 'xuhao',
    title: '序号',
    visible: true,
    align: 'center',
    render: (text, record, index) => {
      return index + 1;
    },
    order: 2,
  },
  // {
  //   dataIndex: 'BarCode',
  //   data: 'BarCode',
  //   visible: true,
  //   title: '条码',
  // },
  // {
  //   dataIndex: 'PatNo',
  //   data: 'PatNo',
  //   visible: true,
  //   title: '病案号',
  // },
  // {
  //   dataIndex: 'PatName',
  //   data: 'PatName',
  //   visible: true,
  //   title: '姓名',
  // },
  // {
  //   dataIndex: 'HospCode',
  //   data: 'HospCode',
  //   visible: true,
  //   title: '院区',
  //   dictionaryModule: 'Hospital',
  //   dictionaryModuleGroup: null,
  // },
  // {
  //   dataIndex: 'InDate',
  //   data: 'InDate',
  //   visible: true,
  //   title: '入院日期',
  //   dataType: 'DateByDash',
  // },
  // {
  //   dataIndex: 'OutDate',
  //   data: 'OutDate',
  //   visible: true,
  //   title: '出院日期',
  //   dataType: 'DateByDash',
  // },
  // {
  //   dataIndex: 'OutDept',
  //   data: 'OutDept',
  //   visible: true,
  //   title: '出院科室',
  // },
  // {
  //   dataIndex: 'BorrowerName',
  //   data: 'BorrowerName',
  //   visible: false,
  //   title: '借阅人',
  // },
  // {
  //   dataIndex: 'BorrowDate',
  //   data: 'BorrowDate',
  //   visible: false,
  //   title: '借阅日期',
  //   dataType: 'DateByDash',
  // },
  // {
  //   dataIndex: 'Purpose',
  //   data: 'Purpose',
  //   visible: false,
  //   title: '借阅目的',
  // },
  // {
  //   dataIndex: 'Reason',
  //   data: 'Reason',
  //   visible: false,
  //   title: '申请原因',
  // },
  // {
  //   dataIndex: 'Status',
  //   data: 'Status',
  //   visible: false,
  //   title: '申请状态',
  //   dictionaryModule: 'ApplicationStatus',
  //   dictionaryModuleGroup: null,
  // },
  {
    dataIndex: 'IsAccessible',
    data: 'IsAccessible',
    // visible: true,
    // title: '是否可取',
    render: (node, record, index) => {
      return (
        <span>
          {record?.['IsAccessible'] === true
            ? '是'
            : record?.['IsAccessible'] === false
            ? '否'
            : '未定义'}
        </span>
      );
    },
  },
  {
    dataIndex: 'IsPrepared',
    data: 'IsPrepared',
    // visible: true,
    // title: '已处理',
    render: (value) => (value ? '是' : '否'),
  },
  {
    dataIndex: 'IsReturned',
    data: 'IsReturned',
    // visible: true,
    // title: '是否已归还',
    render: (value) => (value ? '是' : '否'),
  },
  {
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    fixed: 'right',
    order: Number.MAX_VALUE,
    width: 80,
    render: (node, record, index, action) => (
      <Space size="middle">
        {!record?.IsReturned && (
          <IconBtn
            key="return"
            type="undo"
            title="归还"
            openPop={true}
            popTitle="归还该病历"
            popOnConfirm={() => {
              Emitter.emit(EVENT_BORROW_TASK.SINGLE_RETURN, record);
            }}
          />
        )}
      </Space>
    ),
  },
];
