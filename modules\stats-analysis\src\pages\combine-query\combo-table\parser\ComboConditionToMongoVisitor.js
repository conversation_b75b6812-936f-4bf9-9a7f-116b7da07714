import { every, isEmpty, uniq, fromPairs } from 'lodash';
import parser from './parser.js';
import {
  isSimpleObject,
  toPariSimpleObject,
  mapSimpleObjectKey,
  trimPrefixIfNeeded,
} from './utilities.js';

const BaseCstVisitorWithDefaults =
  parser.getBaseCstVisitorConstructorWithDefaults();

const defaultParams = {
  insideNot: false,
};

const getNegativeOperator = (operator) => {
  switch (operator) {
    case 'Equal':
      return 'NotEqual';
    case 'NotEqual':
      return 'Equal';
    case 'LessOrEqual':
      return 'Greater';
    case 'Less':
      return 'GreaterOrEqual';
    case 'GreaterOrEqual':
      return 'Less';
    case 'Greater':
      return 'LessOrEqual';
    case 'IsNull':
      return 'IsNotNull';
    case 'IsNotNull':
      return 'IsNull';
    default:
      return null;
  }
};

const toMongoBody = (operator, value) => {
  switch (operator) {
    case 'Equal':
      return value;
    case 'NotEqual':
      return { $ne: value };
    case 'LessOrEqual':
      return { $lte: value };
    case 'Less':
      return { $lt: value };
    case 'GreaterOrEqual':
      return { $gte: value };
    case 'Greater':
      return { $gt: value };
    case 'Like':
      return { $regex: value };
    case 'NotLike':
      return { $not: { $regex: value } };
    case 'Between':
      return { $gte: value[0], $lte: value[1] };
    case 'NotBetween':
      return { $not: { $gte: value[0], $lte: value[1] } };
    case 'IsNull':
      return null;
    case 'IsNotNull':
      return { $ne: null };
    case 'SelectAnyIn':
      return { $in: [...value] };
    case 'NotSelectAnyIn':
      return { $nin: [...value] };
  }
};

const toAnd = (childrenMongos) => {
  if (isEmpty(childrenMongos)) {
    return {};
  }
  if (childrenMongos.length === 1) {
    return childrenMongos[0];
  }

  if (every(childrenMongos, isSimpleObject)) {
    const pairs = childrenMongos.map((child) => toPariSimpleObject(child));
    const ks = pairs.map((pair) => pair[0]);
    if (uniq(ks).length === ks.length) {
      const mongo = fromPairs(pairs);
      return mongo;
    }
  }
  return { $and: [...childrenMongos] };
};

class ComboConditionToMongoVisitor extends BaseCstVisitorWithDefaults {
  constructor() {
    super();
    this.validateVisitor();
  }

  subject(ctx) {
    var subjectLiteral = ctx.Identifier.map((i) => i.image).join('.');
    return subjectLiteral;
  }

  value(ctx) {
    if (!ctx.array) {
      var literal = ctx[Object.keys(ctx)[0]][0].image;
      return JSON.parse(literal);
    } else {
      return ctx.array[0].children.value.map((v) => this.visit(v));
    }
  }

  atomRule(ctx, params) {
    const subjectLiteral = this.visit(ctx.subject);
    const value = this.visit(ctx.value);
    const mongoField = subjectLiteral;
    const operator = ctx.Operator[0].tokenType.name;

    if (params.insideNot) {
      const negatedOperator = getNegativeOperator(operator);
      if (negatedOperator) {
        const mongoBody = toMongoBody(negatedOperator, value);
        const mongo = { [mongoField]: mongoBody };
        return mongo;
      } else {
        const mongoBody = toMongoBody(operator, value);
        const mongo = { [mongoField]: { $not: mongoBody } };
        return mongo;
      }
    } else {
      const mongoBody = toMongoBody(operator, value);
      const mongo = { [mongoField]: mongoBody };
      return mongo;
    }
  }

  someRule(ctx, params) {
    const subjectLiteral = this.visit(ctx.subject);
    const childrenMongos = ctx.atomRule.map((u) =>
      mapSimpleObjectKey(
        this.visit(u, { ...params, insideNot: false }),
        (v, k) => trimPrefixIfNeeded(k, subjectLiteral),
      ),
    );

    if (params.insideNot) {
      const mongo = {
        [subjectLiteral]: {
          $not: { $elemMatch: toAnd(childrenMongos) },
        },
      };
      return mongo;
    } else {
      const mongo = {
        [subjectLiteral]: { $elemMatch: toAnd(childrenMongos) },
      };
      return mongo;
    }
  }

  notRule(ctx, params) {
    params.insideNot = true;
    // not is handled in atomRule & someRule respectively
    const childMongo = this.visit(ctx.atomRule || ctx.someRule, params);
    return childMongo;
  }

  parenthesisRule(ctx, params) {
    const mongo = this.visit(ctx.rule, params);
    return mongo;
  }

  unaryRule(ctx, params) {
    params = { ...params };

    if (ctx.atomRule) {
      return this.visit(ctx.atomRule, params);
    } else if (ctx.notRule) {
      return this.visit(ctx.notRule, params);
    } else if (ctx.someRule) {
      return this.visit(ctx.someRule, params);
    } else if (this.parenthesisRule) {
      return this.visit(ctx.parenthesisRule, params);
    }
  }

  andRule(ctx, params) {
    if (ctx.unaryRule.length === 1) return this.visit(ctx.unaryRule[0], params);

    const childrenMongos = ctx.unaryRule.map((unaryRule) =>
      this.visit(unaryRule, params),
    );

    const mongo = toAnd(childrenMongos);
    return mongo;
  }

  orRule(ctx, params) {
    if (ctx.andRule.length === 1) return this.visit(ctx.andRule[0], params);
    const childrenMongos = ctx.andRule.map((andRule) =>
      this.visit(andRule, params),
    );
    const mongo = { $or: [...childrenMongos] };
    return mongo;
  }

  rule(ctx, params) {
    params = params || { ...defaultParams };
    const mongo = this.visit(ctx.orRule, params);
    return mongo;
  }
}

export default ComboConditionToMongoVisitor;
