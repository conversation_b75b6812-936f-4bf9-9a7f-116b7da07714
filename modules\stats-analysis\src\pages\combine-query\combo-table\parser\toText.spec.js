import { expect } from 'chai';
import { toText } from './converters.js';

describe('Test toText()', () => {
  it('Atom rule', () => {
    const input = `PatSex select_any_in ["1", "2"])`;
    const { text, lexErrors, parseErrors } = toText(input);
    expect(text).not.to.be.undefined;
    console.log(text, '\n');
  });

  it('Real-world usecases', () => {
    const demo1 = `some IcdeDscgs (IcdeDscgs.IcdeName like "人工流产") and
not some IcdeDscgs (IcdeDscgs.IcdeCode select_any_in ["O04.902","O04.900x001"])
`;

    const demo2 = `some IcdeDscgs (IcdeDscgs.IcdeCode select_any_in ["N84.001"]) and
 some Opers (Opers.OperCode select_any_in ["68.2917"]) and
 Fee.MedfeeSumamt less 7000
`;

    const demo3 = `some IcdeDscgs (IcdeDscgs.IcdeCode select_any_in ["N84.001"]) and
 some Opers (Opers.OperCode select_any_in ["68.2917"]) and
 Fee.MedfeeSumamt less 7000
`;

    const demo4 = ` OutDate between ["2023-11-01", "2023-11-30"] and
  OutDept select_any_in ["5131", "5202"] and
  some IcdeDscgs (IcdeDscgs.IcdeCode select_any_in ["Z34.900x001"]) and
  some IcdeDscgs (IcdeDscgs.IcdeCode select_any_in ["O36.0"] or IcdeDscgs.IcdeName like "胎儿畸形")
  `;

    const demo5 = ` OutDate between ["2023-11-01", "2023-11-30"] and
some IcdeDscgs ( IcdeDscgs.IcdeName like "人工流产") and
not some IcdeDscgs (IcdeDscgs.IsMain equal true and IcdeDscgs.IcdeCode select_any_in ["Z34.900x001"])
`;

    const demo6 = ` OutDate between ["2023-11-01", "2023-11-30"] and
some IcdeDscgs ( IcdeDscgs.IcdeCode between ["N97.0", "N97.902"] and IcdeDscgs.IsMain equal true) and
not some Opers (Opers.OperCode select_any_in ["69.9200x004", "69.9200x004"])
`;

    const demo7 = ` OutDate between ["2023-11-01", "2023-11-30"] and
  OutDept select_any_in ["5131", "5202"] and
  (some IcdeDscgs ( IcdeDscgs.IcdeCode between ["Q89.000", "Q89.901"])
  or some IcdeDscgs ( IcdeDscgs.IcdeCode between ["O35.000", "O35.900x001"])
  or some IcdeDscgs ( IcdeDscgs.IcdeName like "胎儿畸形" )
  or some IcdeDscgs ( IcdeDscgs.IcdeName like "死胎" )
  or some IcdeDscgs ( IcdeDscgs.IcdeName like "死产" ))
`;

    for (const input of [demo1, demo2, demo3, demo4, demo5, demo6, demo7]) {
      const { text, lexErrors, parseErrors } = toText(input);
      expect(text).not.to.be.undefined;
      console.log(text, '\n');
    }
  });
});
