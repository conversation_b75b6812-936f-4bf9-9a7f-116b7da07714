import React, { useEffect, useState } from 'react';
import ReportReadonlyHeader from '@/components/query-header';
import DetailsReportReadonlyTable from '@/tables/detail-readonly';
import './index.less';
import { Emitter } from '@uni/utils/src/emitter';
import { ReportEventConstant, ReportModes } from '@/constants';
import { ReportMasterItem } from '@/interfaces';
import StatsReportPersistTable from '@/tables/stats-persist';
import StatsReportReadonlyTable from '@/tables/stats-readonly';
import DetailsReportPersistTable from '@/tables/detail-persist';
import { Drawer } from 'antd';
import ReportInfo from '@/components/report-info';
import ReportGroupReadonlyTable from '@/tables/report-group-readonly';

const ReportContentContainer = () => {
  const [masterItem, setMasterItem] = useState<ReportMasterItem>();
  const [reportInfoVisible, setReportInfoVisible] = useState(false);

  useEffect(() => {
    Emitter.on(ReportEventConstant.REPORT_MASTER_ITEM_CLICK, (masterItem) => {
      setMasterItem(masterItem);
    });

    Emitter.on(ReportEventConstant.REPORT_DETAIL_VISIBLE, (status) => {
      setReportInfoVisible(status);
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_MASTER_ITEM_CLICK);
      Emitter.off(ReportEventConstant.REPORT_DETAIL_VISIBLE);
    };
  }, []);

  return (
    <div id={'report-content-container'} className={'content-container'}>
      {masterItem?.ReportMode?.toLowerCase()?.indexOf('readonly') >= 0 && (
        <ReportReadonlyHeader masterItem={masterItem} />
      )}

      {masterItem?.ReportMode === ReportModes.DetailsPersist && (
        <DetailsReportPersistTable masterItem={masterItem} />
      )}

      {masterItem?.ReportMode === ReportModes.DetailsReadOnly && (
        <DetailsReportReadonlyTable masterItem={masterItem} />
      )}

      {masterItem?.ReportMode === ReportModes.StatsPersist && (
        <StatsReportPersistTable masterItem={masterItem} />
      )}

      {masterItem?.ReportMode === ReportModes.StatsReadOnly && (
        <StatsReportReadonlyTable masterItem={masterItem} />
      )}

      {masterItem?.ReportMode === ReportModes.ReportGroupReadOnly && (
        <ReportGroupReadonlyTable masterItem={masterItem} />
      )}

      <Drawer
        placement="right"
        mask={true}
        open={reportInfoVisible}
        className={'report-info-modal-container'}
        onClose={() => {
          setReportInfoVisible(false);
        }}
        title={`详细信息`}
        getContainer={false}
      >
        <ReportInfo reportMasterItem={masterItem} />
      </Drawer>
    </div>
  );
};

export default ReportContentContainer;
