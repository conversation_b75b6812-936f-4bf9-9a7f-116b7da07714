@import '~@uni/commons/src/style/variables.less';

.check-result-container {
  .ant-tag {
    border-radius: 4px;
  }

  .ant-pro-card-body {
    padding: 0;
  }

  .ant-pro-table-list-toolbar-title {
    font-size: 17px;
    font-weight: 600;
  }

  .border-right {
    border-right: 1px solid @card-border-color;
  }

  .left-container {
    .ant-tree {
      .ant-tree-node-selected {
        font-size: 14px;
        font-weight: 500;
        color: @text-color;
        background-color: @blue-border-color;
        border-radius: @border-radius-sm;
      }

      .ant-tree-treenode {
        .ant-tree-switcher {
          line-height: 34px;
        }
        .ant-tree-node-content-wrapper {
          padding: 5px 0px 5px 8px;
          .ant-tree-title {
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }
  }
}
