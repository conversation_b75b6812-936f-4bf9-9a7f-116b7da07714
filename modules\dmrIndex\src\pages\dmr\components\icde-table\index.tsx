import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import {
  UniDmrDragEditOnlyTable,
  UniDragEditTable,
  UniTable,
} from '@uni/components/src';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { dmrIcdeReport, dmrOperationReport } from '@/pages/dmr/network/save';
import { icdeColumns } from '@/pages/dmr/columns';
import { Form, Modal } from 'antd';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import cloneDeep from 'lodash/cloneDeep';
import { isEmptyValues } from '@uni/utils/src/utils';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';
import { mainIcdeOutComeOtherPlugin } from '@/pages/dmr/plugins/outcome-other';

interface IcdeDragTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];

  underConfiguration?: boolean;

  onChange?: (value: any) => void;
}

interface IcdeItem {
  IcdeId?: number;
  id?: number | string;

  IcdeNameLabel?: string;
  IcdeName?: string;
  IcdeCode?: string;
  IcdeCond?: string;
  IcdeCondLabel?: string;

  IcdeOutcome?: string;

  IsReported?: boolean;

  UniqueId?: string;
}

const icdeCopyKeys =
  (window as any).externalConfig?.['dmr']?.icdeCopyKeys ?? [];

const copyKeys = !isEmptyValues(icdeCopyKeys)
  ? icdeCopyKeys
  : ['IcdeCond', 'IcdeOutcome'];

const icdeOperFirstMain =
  (window as any).externalConfig?.['dmr']?.icdeOperFirstMain ?? false;

const icdeCopyFocusKey =
  (window as any).externalConfig?.['dmr']?.icdeCopyFocusKey ?? undefined;
const icdeDeleteConfirm =
  (window as any).externalConfig?.['dmr']?.icdeDeleteConfirm ?? false;
const icdeMoveMainConfirm =
  (window as any).externalConfig?.['dmr']?.icdeMoveMainConfirm ?? false;

const hotKeyToEvents = {
  ADD: (event) => {
    Emitter.emit(EventConstant.DMR_ICDE_ADD, event.target.id);
  },
  COPY: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);
    let index = parseInt(indexString);

    if (index >= 0) {
      Emitter.emit(EventConstant.DMR_ICDE_COPY, {
        id: undefined,
        index: index,
      });
    }
  },
  DELETE: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_ICDE_DELETE, index);
    }
  },
  SCROLL_LEFT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: -100,
      behavior: 'smooth',
    });
  },
  SCROLL_RIGHT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: 100,
      behavior: 'smooth',
    });
  },
  SCROLL_UP: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: -50,
      behavior: 'smooth',
    });
  },
  SCROLL_DOWN: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: 50,
      behavior: 'smooth',
    });
  },
  UP: (event) => {
    Emitter.emit(getArrowUpDownEventKey('diagnosisTable'), {
      event: event,
      type: 'UP',
      trigger: 'hotkey',
    });
  },
  DOWN: (event) => {
    console.log('DOWN', event);
    Emitter.emit(getArrowUpDownEventKey('diagnosisTable'), {
      event: event,
      type: 'DOWN',
      trigger: 'hotkey',
    });
  },
};

const clearKeysMap = {
  // 仅用于联动删除使用
  IcdeCode: [
    'IcdeName',
    'IcdeCode',
    'InsurName',
    'InsurCode',
    'HqmsName',
    'HqmsCode',
    'IcdeExtra',
  ],
};

const IcdeDragTable = (props: IcdeDragTableProps) => {
  const itemRef = React.useRef<any>();

  const [form] = Form.useForm();
  const actionRef = useRef<any>();

  const icdeDataSource = Form.useWatch('diagnosisTable', props?.form) ?? [];

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    icdeDataSource?.length,
  );

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  useEffect(() => {
    updateRowSelectionState();
    setTableDataSourceSize(icdeDataSource?.length);
  }, [icdeDataSource]);

  useEffect(() => {
    // 当表格的数量改变的时候 重新计算 IsReportedTrueCount
    let icdeTableData = props?.form?.getFieldValue('diagnosis-table');
    let IsReportedTrueCount = 0;
    icdeTableData?.forEach((item) => {
      if (item?.IsReported === true) {
        IsReportedTrueCount++;
      }
    });
    form.setFieldValue('IsReportedTrueCount', IsReportedTrueCount);
  }, [tableDataSourceSize]);

  const lineUpDownEvents = {
    LINE_UP: (event) => {
      console.log('LINE_UP', event);
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let focusId = undefined;
      if (index === 0) {
        focusId = itemId;
      } else {
        ids[2] = index - 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('diagnosisTable'), {
        oldIndex: index,
        newIndex: index - 1,
        focusId: focusId,
      });
    },
    LINE_DOWN: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let dataSourceSize = props?.form?.getFieldValue('diagnosis-table').length;
      let focusId = undefined;
      if (index === dataSourceSize - 1) {
        focusId = itemId;
      } else {
        ids[2] = index + 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('diagnosisTable'), {
        oldIndex: index,
        newIndex: index + 1,
        focusId: focusId,
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      { ...hotKeyToEvents, ...lineUpDownEvents }?.[item?.type],
      {
        preventDefault: true,
        enabled: true,
        enableOnFormTags: true,
        enableOnContentEditable: true,
      },
    );
  });

  useEffect(() => {
    let columns = mergeColumnsInDmrTable(
      props?.columns,
      icdeColumns,
      'IcdeDragTable',
    );

    setTableColumns(columns);
  }, [props?.columns]);

  const removeSwitchOnKeyDown = () => {
    document
      ?.getElementById('diagnosisTable')
      ?.querySelectorAll('button[id*=IsReported]')
      ?.forEach((item) => {
        item?.removeEventListener('keydown', () => {});
      });
  };

  useEffect(() => {
    setTimeout(() => {
      waitFocusElementRefocusBySelector(waitFocusId);
    }, 100);
  }, [waitFocusId]);

  useEffect(() => {
    // delete事件
    Emitter.on(getDeletePressEventKey('diagnosisTable'), (itemId) => {
      // key 包含 index 和 其他的东西
      console.log('diagnosisTable', itemId);
      let itemIds = itemId?.split('#');
      let index = parseInt(itemIds?.at(2));
      let key = itemIds?.at(1);

      let clearKeys = [key];
      if (clearKeysMap[key]) {
        clearKeys = clearKeysMap[key];
      }
      clearValuesByKeys(clearKeys, index);

      // 定位到当前这个
      setTimeout(() => {
        document.getElementById(itemId)?.focus();
      }, 100);
    });

    Emitter.on(EventConstant.DMR_ICDE_ADD, (focusId?: string) => {
      let rowData = {
        id: Math.round(Date.now() / 1000),
        IsReported: true,
        UniqueId: uuidv4(),
      };

      let tableData = props?.form?.getFieldValue('diagnosis-table');

      tableData.splice(tableData.length, 0, rowData);

      // TODO 设定主诊为第一个
      if (icdeOperFirstMain) {
        setFirstItemIsMainIcde(tableData);
      }

      props?.form?.setFieldValue('diagnosis-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('diagnosis-table');

      setWaitFocusId(
        `div[id=diagnosisTable] tbody > tr:nth-child(${tableData?.length}) > td input`,
      );
      setTableDataSourceSize(tableData?.length);
    });

    Emitter.on(EventConstant.DMR_ICDE_INSURE_MAIN, (data) => {
      let index = data?.index;
      if (index > -1) {
        let tableData = props?.form?.getFieldValue('diagnosis-table');

        tableData
          ?.filter((item) => item?.id !== 'ADD')
          .forEach((item, rowDataIndex) => {
            let currentFormValue =
              form.getFieldsValue()?.[item?.id]?.['IsMain'];
            if (index === rowDataIndex) {
              item['IsMain'] = currentFormValue;
              form.setFieldValue([item?.id, 'IsMain'], currentFormValue);

              // 同步勾选 上报
              if (currentFormValue === true) {
                item['IsReported'] = true;
                form.setFieldValue([item?.id, 'IsReported'], true);
              }
            } else {
              item['IsMain'] = false;
              form.setFieldValue([item?.id, 'IsMain'], false);
            }
          });

        props?.form?.setFieldValue('diagnosis-table', cloneDeep(tableData));
        triggerFormValueChangeEvent('diagnosis-table');

        // 更新IsReportedCount
        let IsReportedTrueCount = 0;
        tableData?.forEach((item) => {
          if (item?.IsReported === true) {
            IsReportedTrueCount++;
          }
        });
        form.setFieldValue('IsReportedTrueCount', IsReportedTrueCount);
      }
    });

    Emitter.on(EventConstant.DMR_ICDE_REPORT, (checked) => {
      let IsReportedTrueCount = form.getFieldValue('IsReportedTrueCount');
      if (checked) {
        IsReportedTrueCount++;
      } else {
        IsReportedTrueCount--;
      }
      form.setFieldValue('IsReportedTrueCount', IsReportedTrueCount);
    });

    // 处理全选/反选事件
    Emitter.on(`DMR_ROW_SELECTION_SELECT_ALL_diagnosisTable`, (data) => {
      const tableData = props?.form?.getFieldValue('diagnosis-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 批量更新所有行的选中状态
      validRows.forEach((row) => {
        form.setFieldValue([row.id, 'RowSelection'], data.checked);
      });

      // 触发表单值变化事件
      triggerFormValueChangeEvent('diagnosis-table');

      // 通知选中状态更新
      updateRowSelectionState();
    });

    // 处理单个行选择变化事件
    Emitter.on(`DMR_ROW_SELECTION_ITEM_CHANGE_diagnosisTable`, (data) => {
      // 延迟更新状态，确保表单值已经更新
      setTimeout(() => {
        updateRowSelectionState();
      }, 150);
    });

    // 处理批量删除事件
    Emitter.on(`DMR_BATCH_DELETE_diagnosisTable`, () => {
      const tableData = props?.form?.getFieldValue('diagnosis-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 获取选中的行索引
      const selectedIndexes = [];
      validRows.forEach((row, index) => {
        const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
        if (rowSelectionValue === true) {
          selectedIndexes.push(index);
        }
      });

      if (selectedIndexes.length > 0) {
        // 从后往前删除，避免索引变化问题
        const sortedIndexes = selectedIndexes.sort((a, b) => b - a);
        let newTableData = [...tableData];

        sortedIndexes.forEach((index) => {
          newTableData.splice(index, 1);
        });

        // TODO 设定主诊为第一个
        if (icdeOperFirstMain && newTableData.length > 0) {
          setFirstItemIsMainIcde(newTableData);
        }

        // 更新form
        props?.form?.setFieldValue('diagnosis-table', cloneDeep(newTableData));
        triggerFormValueChangeEvent('diagnosis-table');

        setTableDataSourceSize(newTableData?.length);

        // 延迟更新选择状态
        setTimeout(() => {
          updateRowSelectionState();
        }, 100);
      }
    });

    Emitter.on(EventConstant.DMR_ICDE_DELETE, (index) => {
      if (icdeDeleteConfirm) {
        Modal.confirm({
          title: `确定删除第${index + 1} 条诊断数据？`,
          content: '',
          onOk: () => {
            onIcdeItemDelete(index);
          },
          getContainer: () => document.getElementById('dmr-main-container'),
        });
      } else {
        onIcdeItemDelete(index);
      }
    });

    Emitter.on(EventConstant.DMR_ICDE_COPY, (payload) => {
      let tableData = props?.form?.getFieldValue('diagnosis-table');
      let currentCopyItem = payload?.['id']
        ? form.getFieldValue(payload?.['id'])
        : tableData?.[payload?.index];
      let index = payload?.index;

      let copiedItem = {
        id: Math.round(Date.now() / 1000),
        IsReported: true,
        UniqueId: uuidv4(),
      };

      copyKeys?.forEach((key) => {
        copiedItem[key] = currentCopyItem[key];
      });

      tableData.splice(index + 1, 0, copiedItem);

      // TODO 设定主诊为第一个
      if (icdeOperFirstMain) {
        setFirstItemIsMainIcde(tableData);
      }

      if (!isEmptyValues(icdeCopyFocusKey)) {
        setWaitFocusId(
          `div[id=diagnosisTable] tbody > tr:nth-child(${
            index + 2
          }) > td input[id*=${icdeCopyFocusKey}]`,
        );
      } else {
        setWaitFocusId(
          `div[id=diagnosisTable] tbody > tr:nth-child(${
            index + 2
          }) > td input`,
        );
      }
      setTableDataSourceSize(tableData?.length);

      // 更新form
      props?.form?.setFieldValue('diagnosis-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('diagnosis-table');
    });

    Emitter.on(getArrowUpDownEventKey('diagnosisTable'), (payload) => {
      let type = payload?.type;
      const icdeDataSource = props?.form?.getFieldValue('diagnosis-table');
      console.log('payload', payload);
      if (
        payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
        payload?.trigger === 'hotkey'
      ) {
        // 表示是 下拉框 需要定制
        return;
      }

      payload?.event?.stopPropagation();

      let { nextIndex, activePaths } = calculateNextIndex(type);
      if (type === 'UP') {
        if (nextIndex < 0) {
          nextIndex = undefined;
        }
      }

      if (type === 'DOWN') {
        if (nextIndex > icdeDataSource?.length - 1) {
          nextIndex = undefined;
        }
      }

      if (nextIndex !== undefined) {
        activePaths[2] = nextIndex.toString();
        document.getElementById(activePaths?.join('#'))?.focus();
      }
    });

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('diagnosisTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      resizeObserver?.disconnect();

      Emitter.off(EventConstant.DMR_ICDE_ADD);
      Emitter.off(EventConstant.DMR_ICDE_DELETE);
      Emitter.off(EventConstant.DMR_ICDE_INSURE_MAIN);
      Emitter.off(EventConstant.DMR_ICDE_REPORT);
      Emitter.off(EventConstant.DMR_ICDE_COPY);
      Emitter.off(`DMR_ROW_SELECTION_SELECT_ALL_diagnosisTable`);
      Emitter.off(`DMR_ROW_SELECTION_ITEM_CHANGE_diagnosisTable`);
      Emitter.off(`DMR_BATCH_DELETE_diagnosisTable`);

      Emitter.off(getDeletePressEventKey('diagnosisTable'));
      Emitter.off(getArrowUpDownEventKey('diagnosisTable'));
    };
  }, []);

  const onIcdeItemDelete = (index: number) => {
    if (index > -1) {
      let tableData = props?.form?.getFieldValue('diagnosis-table');
      tableData.splice(index, 1);

      // TODO 设定主诊为第一个
      if (icdeOperFirstMain) {
        setFirstItemIsMainIcde(tableData);
      }

      // 更新form
      props?.form?.setFieldValue('diagnosis-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('diagnosis-table');

      // 删除的时候 给出当前那个选中的
      // 当前选中的意思是表格中的上一个存在即是上表格中上一个的最后一个
      // 表格中不存在即写第0个的icdeName 建议写死
      let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
      if (dataItems?.length > 0) {
        setWaitFocusId(
          `div[id=diagnosisTable] tbody > tr:nth-child(${
            index >= dataItems.length - 1 ? dataItems.length : index + 1
          }) > td input`,
        );
      }
      setTableDataSourceSize(tableData?.length);
    }
  };

  const clearValuesByKeys = (keys, index) => {
    const icdeDataSource = props?.form?.getFieldValue('diagnosis-table');
    let formItemId = icdeDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue('diagnosis-table');
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue('diagnosis-table', cloneDeep(tableData));
    triggerFormValueChangeEvent('diagnosis-table');
  };

  const setFirstItemIsMainIcde = (tableData: any[]) => {
    let firstItem = tableData?.[0];
    tableData?.forEach((item) => {
      item['IsMain'] = false;
      form.setFieldValue([item?.id, 'IsMain'], false);
    });
    if (firstItem) {
      firstItem['IsMain'] = true;
      form.setFieldValue([firstItem?.id, 'IsMain'], true);
      form.setFieldValue([firstItem?.id, 'IsReported'], true);
    }
  };

  // 更新行选择状态
  const updateRowSelectionState = () => {
    const tableData = props?.form?.getFieldValue('diagnosis-table') || [];
    const validRows = tableData.filter((row) => row.id !== 'ADD');

    if (validRows.length === 0) {
      Emitter.emit('DMR_ROW_SELECTION_STATE_UPDATE', {
        tableId: 'diagnosisTable',
        allSelected: false,
        indeterminate: false,
      });
      return;
    }

    const selectedRows = validRows.filter((row) => {
      const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
      return rowSelectionValue === true;
    });

    const allSelected =
      selectedRows.length === validRows.length && validRows.length > 0;
    const indeterminate =
      selectedRows.length > 0 && selectedRows.length < validRows.length;
    const hasSelection = selectedRows.length > 0;

    Emitter.emit(`DMR_ROW_SELECTION_STATE_UPDATE_diagnosisTable`, {
      allSelected: allSelected,
      indeterminate: indeterminate,
    });

    // 通知批量删除按钮状态
    Emitter.emit(`DMR_ROW_SELECTION_BATCH_UPDATE_diagnosisTable`, {
      hasSelection: hasSelection,
    });
  };

  return (
    <UniDmrDragEditOnlyTable
      {...props}
      extraFormItemKeys={['IsReportedTrueCount']}
      formItemContainerClassName={'form-content-item-container'}
      itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
      form={form}
      key={props?.id}
      id={props?.id}
      tableId={props?.id}
      formKey={'diagnosisTable'}
      forceColumnsUpdate={props?.underConfiguration ?? false}
      scroll={{
        x: 'max-content',
      }}
      pagination={false}
      className={`table-container ${props?.className || ''}`}
      dataSource={(props?.form?.getFieldValue('diagnosis-table') ?? [])
        ?.filter((item) => item?.id !== 'ADD')
        ?.map((item) => {
          if (!item['id']) {
            item['id'] = Math.round(Date.now() / 1000);
          }

          return item;
        })
        ?.concat({
          id: 'ADD',
        })}
      rowKey={'id'}
      onValuesChange={(recordList, changedValues) => {
        // setIcdeDataSource(recordList)

        // Object.keys(changedValues)?.forEach((key) => {
        //   let item = changedValues?.[key];
        //   Object.keys(item)?.forEach((itemKey) => {
        //     if (itemKey === 'IcdeCode') {
        //       // // TODO 设定主诊为第一个
        //       // if (icdeOperFirstMain) {
        //       //   setFirstItemIsMainIcde(recordList);
        //       // }
        //     }
        //   });
        // });

        props?.form?.setFieldValue('diagnosis-table', recordList);
        triggerFormValueChangeEvent('diagnosis-table');
      }}
      onDragEndPre={(active, over) => {
        return new Promise((resolve, reject) => {
          if (icdeMoveMainConfirm === false) {
            resolve(true);
          } else {
            if (active.newIndex === 0) {
              const tableData = props?.form?.getFieldValue('diagnosis-table');
              let activeItem = tableData?.at(active.oldIndex);
              Modal.confirm({
                title: `确定移动${
                  isEmptyValues(activeItem?.IcdeCode)
                    ? '此条数据'
                    : `诊断编码: ${activeItem.IcdeCode}`
                }到主诊？`,
                content: '',
                okText: '确定',
                cancelText: '取消',
                onOk: () => {
                  resolve(true);
                },
                onCancel: () => {
                  resolve(false);
                },
                getContainer: () =>
                  document.getElementById('dmr-main-container'),
              });
            } else {
              resolve(true);
            }
          }
        });
      }}
      onDragExtra={(tableData) => {
        if (icdeOperFirstMain) {
          setFirstItemIsMainIcde(tableData);
        }
      }}
      onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
        // setIcdeDataSource(tableData);

        props?.form?.setFieldValue('diagnosis-table', cloneDeep(tableData));
        if (focusId) {
          setTimeout(() => {
            waitFocusElementRefocus(focusId);
          }, 100);
        }
        triggerFormValueChangeEvent('diagnosis-table');
      }}
      columns={tableColumns}
      enableRowSelection={true}
    />
  );
};

export default React.memo(IcdeDragTable);
