import parser from './parser.js';
import {
  toAtomRule,
  toNotRule,
  toSomeRule,
  toConjunctionRule,
  toRuleFromChildRule,
} from './treeFactory.js';

const BaseCstVisitorWithDefaults =
  parser.getBaseCstVisitorConstructorWithDefaults();

class ComboConditionToTreeVisitor extends BaseCstVisitorWithDefaults {
  constructor() {
    super();
    this.validateVisitor();
  }

  subject(ctx) {
    return ctx.Identifier.map((i) => i.image).join('.');
  }

  value(ctx) {
    if (!ctx.array) {
      var literal = ctx[Object.keys(ctx)[0]][0].image;
      return JSON.parse(literal);
    } else {
      return ctx.array[0].children.value.map((v) => this.visit(v));
    }
  }

  atomRule(ctx) {
    const rule = toAtomRule(
      this.visit(ctx.subject),
      ctx.Operator[0].image,
      this.visit(ctx.value),
    );

    return rule;
  }

  notRule(ctx) {
    const childRule = ctx.atomRule
      ? this.visit(ctx.atomRule)
      : this.visit(ctx.someRule);
    const rule = toNotRule(childRule);
    return rule;
  }

  someRule(ctx) {
    const childrenRules = ctx.atomRule.map((r) => this.visit(r));
    const rule = toSomeRule(this.visit(ctx.subject), childrenRules);
    return rule;
  }

  parenthesisRule(ctx) {
    return this.visit(ctx.rule);
  }

  unaryRule(ctx) {
    if (ctx.atomRule) {
      return this.visit(ctx.atomRule);
    } else if (ctx.notRule) {
      return this.visit(ctx.notRule);
    } else if (ctx.someRule) {
      return this.visit(ctx.someRule);
    } else if (ctx.parenthesisRule) {
      return this.visit(ctx.parenthesisRule);
    }
  }

  andRule(ctx) {
    if (ctx.unaryRule.length === 1) return this.visit(ctx.unaryRule[0]);
    const childrenRules = ctx.unaryRule.map((r) => this.visit(r));
    const rule = toConjunctionRule(childrenRules, 'AND');
    return rule;
  }

  orRule(ctx) {
    if (ctx.andRule.length === 1) return this.visit(ctx.andRule[0]);
    const childrenRules = ctx.andRule.map((r) => this.visit(r));
    const rule = toConjunctionRule(childrenRules, 'OR');
    return rule;
  }

  rule(ctx) {
    const childRule = this.visit(ctx.orRule);
    const rule = toRuleFromChildRule(childRule);
    return rule;
  }
}

export default ComboConditionToTreeVisitor;
