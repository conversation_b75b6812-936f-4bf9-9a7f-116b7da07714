import React, { useCallback } from 'react';
import { Tooltip, InputNumber } from 'antd';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { TableAction } from '@uni/reducers/src/tableReducer';
import { createActionColumn } from '../columns';
import { isEmptyValues } from '@uni/utils/src/utils';

/**
 * 表格列配置hook
 * @param handleDetailClick 处理详情点击
 * @param autoCalculationKeys 自动计算字段
 * @param isAutoComputeCntInput 自动计算输入状态
 */
export const useTableColumns = (
  handleDetailClick: any,
  autoCalculationKeys: string[],
  isAutoComputeCntInput: React.MutableRefObject<any>,
  handleDbClick: any,
) => {
  // 单元格编辑配置
  const columnsEditHandler = useCallback(
    (cols: any[]) => {
      return cols.map((column) => {
        if (column.columnType === 'Int32' || column.columnType === 'Decimal') {
          return {
            ...column,
            editable: true,
            valueType: 'digit',
            normalRenderer: true, // 强制设置成 normalRenderer
            render: (text: any, record: any) => {
              let currentColumn = column;
              if (!isEmptyValues(record[`${currentColumn?.data}_compare`])) {
                return (
                  <div>
                    <span>{record[currentColumn?.data]}</span>
                    &nbsp;
                    {record[`${currentColumn?.data}_hasDiscrepancy`] && (
                      <Tooltip title="点击查看详情">
                        <span
                          style={{ color: 'red', cursor: 'pointer' }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDetailClick(
                              record,
                              currentColumn?.data,
                              currentColumn.title,
                            );
                          }}
                        >
                          ({record[`${currentColumn?.data}_compare`]})
                        </span>
                      </Tooltip>
                    )}
                  </div>
                );
              }
              return text;
            },
            renderFormItem: ({ fieldProps }: any, props: any) => {
              let currentColumn = column;
              return (
                <InputNumber
                  {...fieldProps}
                  // style={{ width: '100px', minWidth: '60px' }}
                  size="small"
                  data-index={currentColumn.data}
                  disabled={!props?.record?.ExactDate}
                  precision={currentColumn.columnType === 'Decimal' ? 2 : 0}
                  keyboard={false}
                  controls={false}
                  {...{ index: currentColumn?.data }}
                  style={{
                    maxWidth: props?.record[
                      `${currentColumn?.data}_hasDiscrepancy`
                    ]
                      ? '90px'
                      : 'auto',
                  }}
                  addonAfter={
                    props?.record[`${currentColumn?.data}_hasDiscrepancy`] && (
                      <span style={{ color: 'red' }}>
                        ({props?.record[`${currentColumn?.data}_compare`]})
                      </span>
                    )
                  }
                  onChange={(value) => {
                    // 做个特殊处理 在用户输入后，自动计算的触发置false
                    console.log(
                      'autoCalculationKeys',
                      autoCalculationKeys,
                      isAutoComputeCntInput.current,
                      currentColumn?.data,
                    );
                    if (
                      autoCalculationKeys?.findIndex(
                        (key) => key === currentColumn?.data,
                      ) !== -1
                    ) {
                      isAutoComputeCntInput.current = {
                        ...(isAutoComputeCntInput.current || {}),
                        [currentColumn?.data]: false,
                      };
                    }
                  }}
                />
              );
            },
            onCell: (record: any) => ({
              onDoubleClick: () => {
                handleDbClick(record, column);
              },
            }),
          };
        }
        return {
          ...column,
          editable: false,
          // onCell: (record: any) => ({
          //   onDoubleClick: () => {
          //     if (!record.IsLocked) {
          //       handleDbClick(record);
          //     }
          //   },
          // }),
        };
      });
    },
    [
      handleDetailClick,
      autoCalculationKeys,
      isAutoComputeCntInput,
      handleDbClick,
    ],
  );

  // 表头下拉配置
  const dropDownItems = [
    {
      label: '列设置',
      key: 'columns',
    },
  ];

  const handleDropDownClick = useCallback(
    (key: string, TableDispatch: any) => {
      if (key === 'columns') {
        (global?.window as any)?.eventEmitter?.emit(
          EventConstant.TABLE_COLUMN_EDIT,
          {
            columnInterfaceUrl: 'Api/Dyn-ddr/DeptInpatientAmt/GetListByDepts',
            onTableRowSaveSuccess: (newColumns: any[]) => {
              TableDispatch({
                type: TableAction.columnsChange,
                payload: {
                  columns: tableColumnBaseProcessor(
                    createActionColumn,
                    columnsEditHandler(newColumns),
                    'local',
                  ),
                },
              });
            },
          },
        );
      }
    },
    [columnsEditHandler],
  );

  return {
    columnsEditHandler,
    dropDownItems,
    handleDropDownClick,
  };
};
