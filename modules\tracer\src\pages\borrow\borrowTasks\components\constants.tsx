import {
  WarningOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { Tooltip, Button } from 'antd';
import { Emitter } from '@uni/utils/src/emitter';
import React from 'react';
import IconBtn from '@uni/components/src/iconBtn/index';

/**
 * 借阅模块的常量配置
 */
export const BorrowColumns = [
  // {
  //   data: 'isCorrect',
  //   dataIndex: 'isCorrect',
  //   visible: true,
  //   align: 'center',
  //   render: (text: string, record: any) => {
  //     return (
  //       <>
  //         {record?.errMsg ? (
  //           <Tooltip title={record?.errMsg?.join('。/n')}>
  //             <WarningOutlined />
  //           </Tooltip>
  //         ) : (
  //           <CheckCircleOutlined />
  //         )}
  //       </>
  //     );
  //   },
  //   order: 1,
  // },
  {
    data: 'xuhao',
    dataIndex: 'xuhao',
    title: '序号',
    visible: true,
    align: 'center',
    render: (text, record, index) => {
      return index + 1;
    },
    order: 2,
  },
];

// 自定义事件，用于删除表格中的一行数据
export const EVENT_BORROW_DELETE = 'EVENT_BORROW_DELETE_ROW';

export const BorrowColumnsWithOperation = [
  ...BorrowColumns,
  {
    data: 'operation',
    dataIndex: 'operation',
    title: '操作',
    visible: true,
    fixed: 'right',
    width: 60,
    align: 'center',
    render: (text, record) => {
      return (
        <>
          <IconBtn
            type="delete"
            openPop
            title="删除"
            popOnConfirm={(e) => {
              e.stopPropagation();
              Emitter.emit(EVENT_BORROW_DELETE, record);
            }}
          />
        </>
      );
    },
  },
];
