import { Spin, Col, Row } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import SingleStat from '@uni/components/src/statistic';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';
import './index.less';
import { ColTypeSpan } from './constants';
// import DrawerCardInfo from '@/pages/drg/components/drawerCardInfo';
import { useDeepCompareEffect } from 'ahooks';
import CardEchart from '@uni/components/src/cardEchart';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { StatsSelectedTrendsLineOption } from './echarts.opts';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';

export interface IStatsProps {
  level?: string; // 用于 判断selectedTableItem的内部 要使用的值的类型：有 grp, adrg, majorPerfDept, dept, medTeam, 不填
  useGlobalState?: boolean; // 不填默认使用selectedTableItem 而 非globalState
  tableParams?: any; // 如果有这个 就不走内部的searchParams
  defaultSelectItem?: string; // 默认stat选择的项目 没用大概率是默认空
  selectedTableItem?: {
    // 用于 在XXX波士顿矩阵分析 这类tab下 与左侧table点击联动的item
    VersionedADrgCode?: any;
    VersionedSdCode?: any;
    SdCode?: any;
    OperComplicationCode?: any;
    DrgCode?: any;
    CliDept?: any;
    MedTeam?: any;
    MajorPerfDept?: any;
  };
  extraApiArgs?: any; // 额外的api参数 如果使用 tableParams 则才使用 extraApiArgs 不然走 selectedTableItem
  columns?: any[];
  api?: string;
  trendApi?: string;
  type?: string; // col-xl-?
  tabKey?: string; // 用于判断当前tab的
  noClickable?: boolean; // 不准点 用于 在XXX波士顿矩阵分析
  loading?: boolean; // 外部loading

  chartHeight?: any;
  canEditColumn?: boolean;
}

const Stats = (props: IStatsProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { insurType, dateRange, hospCodes, MajorPerfDepts, CliDepts, MedTeams } =
    globalState?.searchParams;

  const { canEditColumn } = props || {};
  const [statsData, setStatsData] = useState([]);
  const [params, setParams] = useState({});

  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedStatItem, setSelectedStatItem] = useState({
    contentData: '',
    dataType: '',
    title: '',
  });

  // stat click
  useEffect(() => {
    Emitter.on(EventConstant.STAT_CLICK, (record) => {
      setSelectedStatItem(record);
    });
    return () => {
      Emitter.off(EventConstant.STAT_CLICK);
    };
  }, [selectedStatItem]);

  useEffect(() => {
    if (statsData?.length) {
      if (props?.defaultSelectItem) {
        setSelectedStatItem(
          statsData?.find(
            (data) => data?.contentData === props?.defaultSelectItem,
          ),
        );
      } else {
        setSelectedStatItem(statsData[0]);
      }
    }
  }, [props?.defaultSelectItem, statsData]);

  useDeepCompareEffect(() => {
    // 有tableParams 就只走tableParams
    if (!isEmptyValues(props?.tableParams)) {
      let tableParams = {};
      if (props?.extraApiArgs) {
        tableParams = { ...props?.tableParams, ...props?.extraApiArgs };
      } else if (props?.level === 'adrg') {
        tableParams = {
          ...props?.tableParams,
          VersionedADrgCodes: props?.selectedTableItem?.VersionedADrgCode
            ? [props?.selectedTableItem?.VersionedADrgCode]
            : [],
        };
        getSettleCompStatsOfEntityReq(tableParams);
      } else if (props?.level === 'operComp') {
        tableParams = {
          ...props?.tableParams,
          OperComplicationCodes: props?.selectedTableItem?.OperComplicationCode
            ? [props?.selectedTableItem?.OperComplicationCode]
            : [],
        };
        getSettleCompStatsOfEntityReq(tableParams);
      } else if (props?.level === 'sd') {
        tableParams = {
          ...props?.tableParams,
          SdCodes: props?.selectedTableItem?.SdCode
            ? [props?.selectedTableItem?.SdCode]
            : [],
        };
        getSettleCompStatsOfEntityReq(tableParams);
      } else {
        getSettleCompStatsOfEntityReq(props?.tableParams);
      }
      setParams(tableParams);
    } else if (dateRange?.length) {
      let tableParams: any = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
      };

      if (props?.level === 'adrg') {
        if (props?.selectedTableItem?.VersionedADrgCode) {
          tableParams = {
            ...tableParams,
            VersionedADrgCodes: props?.selectedTableItem?.VersionedADrgCode
              ? [props?.selectedTableItem?.VersionedADrgCode]
              : [],
          };
        }
        getSettleCompStatsOfEntityReq(tableParams);
      } else if (props?.level === 'grp') {
        if (props?.selectedTableItem?.DrgCode) {
          tableParams = {
            ...tableParams,
            VersionedDrgCodes: props?.selectedTableItem?.DrgCode
              ? [props?.selectedTableItem?.DrgCode]
              : [],
          };
          getSettleCompStatsOfEntityReq(tableParams);
        }
      } else if (props?.level === 'sd') {
        if (props?.selectedTableItem?.VersionedSdCode) {
          tableParams = {
            ...tableParams,
            VersionedSdCodes: props?.selectedTableItem?.VersionedSdCode
              ? [props?.selectedTableItem?.VersionedSdCode]
              : [],
          };
          getSettleCompStatsOfEntityReq(tableParams);
        }
      } else if (props?.level === 'operComp') {
        if (props?.selectedTableItem?.OperComplicationCode) {
          tableParams = {
            ...tableParams,
            OperComplicationCodes: props?.selectedTableItem
              ?.OperComplicationCode
              ? [props?.selectedTableItem?.OperComplicationCode]
              : [],
          };
          getSettleCompStatsOfEntityReq(tableParams);
        }
      }
      // majorperfdept & dept & medTeam 都有可能出现 ，并且都为必填
      else if (props?.level === 'majorPerfDept') {
        if (props?.useGlobalState) {
          if (MajorPerfDepts?.length) {
            tableParams = {
              ...tableParams,
              MajorPerfDepts,
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        } else {
          if (props?.selectedTableItem) {
            tableParams = {
              ...tableParams,
              MajorPerfDepts: [props?.selectedTableItem?.MajorPerfDept],
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        }
      } else if (props?.level === 'dept') {
        if (props?.useGlobalState) {
          if (CliDepts?.length) {
            tableParams = {
              ...tableParams,
              CliDepts,
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        } else {
          if (props?.selectedTableItem) {
            tableParams = {
              ...tableParams,
              CliDepts: [props?.selectedTableItem?.CliDept],
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        }
      }
      // 医疗组
      else if (props?.level === 'medTeam') {
        if (props?.useGlobalState) {
          if (MedTeams?.length) {
            tableParams = {
              ...tableParams,
              MedTeams,
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        } else {
          if (props?.selectedTableItem) {
            tableParams = {
              ...tableParams,
              MedTeams: [props?.selectedTableItem?.MedTeam],
            };
            getSettleCompStatsOfEntityReq(tableParams);
          }
        }
      }
      // 最后啥都没有的话直接调
      else {
        getSettleCompStatsOfEntityReq(tableParams);
      }
      // adrg & grp 只会在selectedTableItem时出现

      setParams(tableParams);
    }
  }, [
    props?.level,
    dateRange,
    hospCodes,
    MajorPerfDepts,
    CliDepts,
    MedTeams,
    props?.selectedTableItem,
    props?.useGlobalState,
    props?.tableParams,
    props?.extraApiArgs,
  ]);

  // Stats
  const {
    data: settleCompStatsOfEntityData,
    loading: getSettleCompStatsOfEntityLoading,
    run: getSettleCompStatsOfEntityReq,
  } = useRequest(
    (data) =>
      uniCommonService(props?.api, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  const {
    data: settleCompStatsOfEntityColumns,
    mutate: settleCompStatsOfEntityColumnsMutate,
    run: getSettleCompStatsOfEntityColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(props?.api, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      // manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.Columns;
        }
      },
    },
  );

  // Stats数据处理
  useEffect(() => {
    if (
      settleCompStatsOfEntityColumns &&
      settleCompStatsOfEntityColumns.length &&
      settleCompStatsOfEntityData?.length
    ) {
      let data = [];
      data = props?.columns?.map((stat) => {
        let contentItemColumn,
          footItemColumn,
          value,
          footerValue,
          yoy,
          suffix,
          footerNode,
          rightFootValue;

        contentItemColumn = settleCompStatsOfEntityColumns.filter(
          (col) => col.data === stat.contentData,
        )[0];

        value = settleCompStatsOfEntityData[0]?.[`${stat.contentData}`];
        // yoy
        yoy = settleCompStatsOfEntityData[0]?.[`${stat.contentData}Yoy`];

        if (!yoy) {
          suffix = '';
        } else if (yoy > 0) {
          let node = (
            <span>
              <CaretUpOutlined className="font-success" />
              {valueNullOrUndefinedReturnDash(yoy, 'Percent')}
            </span>
          );
          if (stat?.footerYoy) footerNode = node;
          else suffix = node;
        } else {
          let node = (
            <span>
              <CaretDownOutlined className="font-danger" />
              {valueNullOrUndefinedReturnDash(yoy, 'Percent')}
            </span>
          );
          if (stat?.footerYoy) footerNode = node;
          else suffix = node;
        }

        if (stat?.footerData) {
          footItemColumn = settleCompStatsOfEntityColumns.filter(
            (col) => col.data === stat.footerData,
          )[0];
          Object.keys(settleCompStatsOfEntityData[0]).forEach((key, val) => {
            if (key === stat.footerData) {
              // footerValue = val;
              footerValue = settleCompStatsOfEntityData[0]?.[key];
            }
          });
        }

        if (stat?.rightFootValue) {
          let rightFootContentColumn = settleCompStatsOfEntityColumns.filter(
            (col) => col.data === stat.rightFootValue,
          )[0];

          Object.keys(settleCompStatsOfEntityData[0]).forEach((key, val) => {
            if (key === stat.rightFootValue) {
              let value = settleCompStatsOfEntityData[0]?.[key];
              rightFootValue = valueNullOrUndefinedReturnDash(
                value,
                rightFootContentColumn?.dataType,
              );
            }
          });
        }
        console.log('rightFootValue6', stat?.rightFootValue, rightFootValue);

        return {
          ...stat,
          title: stat?.title || contentItemColumn?.title,
          value,
          suffix,
          dataType: stat?.dataType || contentItemColumn?.dataType,
          footerNode,
          footerTitle: stat?.footerTitle || footItemColumn?.title || '同比',
          footerValue,
          footerDataType: footItemColumn?.dataType,
          hidden: !stat?.showGrpCnt && !contentItemColumn?.visible,
          rightFootValue,
        };
      });

      setStatsData(data);
    } else {
      setStatsData([]);
    }
  }, [
    props?.columns,
    settleCompStatsOfEntityColumns,
    settleCompStatsOfEntityData,
  ]);

  // 趋势
  const {
    data: settleCompStatsTrendData,
    loading: getSettleCompStatsTrendLoading,
    run: getSettleCompStatsTrendReq,
  } = useRequest(
    (data) =>
      uniCommonService(props?.trendApi, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          console.log('data', res.data);
          if (res.data?.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 趋势 Columns
  const { data: settleCompStatsTrendColumnsData } = useRequest(
    () =>
      uniCommonService(props?.trendApi, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  useEffect(() => {
    if (Object.keys(params)?.length) {
      getSettleCompStatsTrendReq(params);
    }
  }, [params]);

  const SettleCompStatsSelectedTrendsLineOptionResult = useMemo(() => {
    if (settleCompStatsTrendData && settleCompStatsTrendData?.length > 0) {
      return StatsSelectedTrendsLineOption(
        settleCompStatsTrendData,
        'MonthDate',
        selectedStatItem,
      );
    }

    return {};
  }, [selectedStatItem, settleCompStatsTrendData]);

  return (
    <>
      {globalState?.userInfo?.Roles?.includes('Admin') && (
        <div
          style={{
            position: 'absolute',
            top: '-25px',
            width: '45%',
            marginBottom: '-8px',
            display: 'flex',
            justifyContent: 'flex-end',
          }}
        >
          {canEditColumn && (
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: props?.api,
                onTableRowSaveSuccess: (newColumns) => {
                  settleCompStatsOfEntityColumnsMutate(newColumns);
                },
              }}
            />
          )}
        </div>
      )}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={24} md={24} lg={12} xl={11}>
          <Row gutter={[16, 16]} id="hosp-stats-list-card-all">
            {props?.loading || getSettleCompStatsOfEntityLoading ? (
              <Col span={24}>
                <div className="spin-container">
                  <Spin tip="加载中..."></Spin>
                </div>
              </Col>
            ) : (
              (statsData.length > 0 ? statsData : []).map((d) => {
                if (!d?.hidden || d?.visible) {
                  return (
                    <Col
                      key={d?.dataIndex ?? d?.contentData}
                      xs={ColTypeSpan[props?.type]?.xs}
                      sm={ColTypeSpan[props?.type]?.sm}
                      md={ColTypeSpan[props?.type]?.md}
                      lg={ColTypeSpan[props?.type]?.lg}
                      xl={ColTypeSpan[props?.type]?.xl}
                    >
                      <Spin
                        spinning={getSettleCompStatsOfEntityLoading ?? false}
                      >
                        <SingleStat
                          loading={getSettleCompStatsOfEntityLoading}
                          className={`${
                            !props?.noClickable &&
                            selectedStatItem?.title === d?.title
                              ? 'active'
                              : ''
                          }`}
                          {...d}
                          clickable={props?.noClickable ? false : d?.clickable}
                          detailType="chsCardInfo"
                          type="drg"
                          args={{
                            ...d?.args,
                            ...params,
                            id: `drg-settle-single-stats-${props?.level}-${
                              d?.dataIndex ?? d?.contentData
                            }`,
                          }}
                        ></SingleStat>
                      </Spin>
                    </Col>
                  );
                }
              })
            )}
          </Row>
        </Col>
        <Col xs={24} sm={24} md={24} lg={12} xl={13}>
          <CardEchart
            title="月度变化趋势"
            height={
              props?.chartHeight === 'auto'
                ? document
                    .getElementById('hosp-stats-list-card-all')
                    ?.getBoundingClientRect()?.height -
                  50 -
                  24
                : 430
            }
            dictData={globalState.dictData}
            elementId="Trend"
            loading={getSettleCompStatsTrendLoading}
            options={SettleCompStatsSelectedTrendsLineOptionResult}
            needExport={true}
            exportTitle={'月度变化趋势'}
            exportData={settleCompStatsTrendData}
            exportColumns={settleCompStatsTrendColumnsData}
            needModalDetails={true}
            onRefresh={() => {
              getSettleCompStatsTrendReq(params);
            }}
          ></CardEchart>
        </Col>
      </Row>
    </>
  );
};

export default Stats;
