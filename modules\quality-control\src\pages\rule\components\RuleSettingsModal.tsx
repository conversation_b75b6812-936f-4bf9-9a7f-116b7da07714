import { useEffect, useState } from 'react';
import {
  Modal,
  Button,
  Space,
  Switch,
  Radio,
  message,
  Tooltip,
  Typography,
  Select,
} from 'antd';
import UniTable from '@uni/components/src/table';
import { UniSelect } from '@uni/components/src';
import { isEmptyValues } from '@uni/utils/src/utils';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';

enum QualityCheckCategory {
  Custom = '0',
  CodeReview = '9',
}

interface RuleSettingsModalProps {
  visible: boolean;
  record: any;
  cliDepts: any[];
  onClose: (hasChanges: boolean) => void;
  selectErrorLevelDataSource: any[];
  onUpdateRule: (data: any) => void;
  onUpdateCategory: (data: { ruleCode: string; checkCategory: string }) => void;
  onUpdatePick: (data: any) => void;
}

const RuleSettingsModal: React.FC<RuleSettingsModalProps> = ({
  visible,
  record,
  cliDepts,
  onClose,
  selectErrorLevelDataSource,
  onUpdateRule,
  onUpdateCategory,
  onUpdatePick,
}) => {
  const [localRecord, setLocalRecord] = useState<any>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [cliDeptList, setCliDeptList] = useState<any[]>([]);
  const [selectedCliDepts, setSelectedCliDepts] = useState<string[]>([]);

  // UpdateQualityCheckCategory
  const { run: updateQualityCheckCategoryReq } = useRequest(
    (data: { ruleCode: string; checkCategory: string }) =>
      uniCommonService(`Api/Sys/QualitySys/UpdateQualityCheckCategory`, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      onSuccess: (res: RespVO<any>, params) => {
        setHasChanges(true);
      },
      onError: (res: RespVO<any>) => {
        message.error('修改失败，请联系管理员');
      },
    },
  );

  // 更新科室白名单
  const { run: updateCliDeptPicks } = useRequest(
    (data: { RuleCode: string; PickMode: string; CliDepts: string[] }) =>
      uniCommonService(`Api/Sys/QualitySys/UpdateQualityCheckCliDeptPicks`, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      onSuccess: () => {
        // message.success('科室白名单更新成功');
        setHasChanges(true);
      },
      onError: () => {
        message.error('科室白名单更新失败');
      },
    },
  );

  useEffect(() => {
    if (cliDepts?.length > 0) {
      setCliDeptList(cliDepts);
    }
  }, [cliDepts]);

  useEffect(() => {
    setLocalRecord(record);
    // 获取已选中的科室白名单
    if (record?.CliDeptPicks) {
      const whitePicks = record.CliDeptPicks.filter(
        (item) => item.PickMode === 'White',
      );
      setSelectedCliDepts(whitePicks.map((item) => item.CliDept));
    } else {
      setSelectedCliDepts([]);
    }
  }, [record]);

  if (!localRecord) return null;

  const handleClose = () => {
    onClose(hasChanges);
    setTimeout(() => {
      setHasChanges(false);
    }, 20);
  };

  console.log('picksColumns record', record);

  const picksColumns = () => {
    return [
      {
        dataIndex: 'Description',
        title: '描述详情',
        visible: true,
      },
      {
        width: 250,
        title: '规则等级',
        visible: true,
        dataIndex: 'ErrorLevel',
        render: (_, record, index) => {
          console.log('render record', _, record, record?.['tooltipLabel']);
          return (
            <Tooltip
              color={'#eb5757'}
              title={record?.['tooltipLabel']}
              open={!isEmptyValues(record?.['tooltipLabel'])}
            >
              <UniSelect
                style={
                  isEmptyValues(record?.['tooltipLabel'])
                    ? { border: '1px solid #d9d9d9' }
                    : { border: '1px solid #eb5757' }
                }
                allowClear={false}
                placeholder={'请选择'}
                optionValueKey={'Code'}
                optionNameKey={'Name'}
                optionLabelProp={'title'}
                bordered={false}
                defaultValue={record?.IsValid ? _ : '0'}
                showSearch={false}
                dataSource={selectErrorLevelDataSource}
                onChange={(value) => {
                  setHasChanges(true);
                  onUpdatePick({
                    RuleCode: localRecord.RuleCode,
                    ...localRecord.Picks[index],
                    ErrorLevel: value,
                    IsValid: value !== '0', // 当value为'0'时IsValid为false，否则为true
                  });
                }}
              />
            </Tooltip>
          );
        },
      },
      // {
      //   visible: true,
      //   title: '是否有效',
      //   align: 'center',
      //   width: 100,
      //   dataIndex: 'IsValid',
      //   render: (_, record, index) => {
      //     return (
      //       <Switch
      //         defaultChecked={_ || false}
      //         onChange={(value) => {
      //           setHasChanges(true);
      //           onUpdatePick({
      //             RuleCode: localRecord.RuleCode,
      //             ...localRecord.Picks[index],
      //             IsValid: value,
      //           });
      //         }}
      //       />
      //     );
      //   },
      // },
    ];
  };

  return (
    <Modal
      title={
        <Typography.Text
          ellipsis={{ tooltip: localRecord?.DisplayErrMsg }}
          style={{ maxWidth: 'calc(100% - 30px)', display: 'inline-block' }}
        >
          {localRecord?.DisplayErrMsg}
        </Typography.Text>
      }
      open={visible}
      onCancel={handleClose}
      destroyOnClose
      footer={[
        <Button key="close" onClick={handleClose}>
          关闭
        </Button>,
      ]}
    >
      <Space direction="vertical" style={{ width: '100%', gap: '16px' }}>
        {/* <div>
          <span style={{ marginRight: '8px' }}>是否有效：</span>
          <Switch
            checked={localRecord?.IsValid}
            onChange={(value) => {
              setHasChanges(true);
              onUpdateRule({
                RuleCode: localRecord?.RuleCode,
                IsValid: value,
              });
              setLocalRecord((prev) => ({
                ...prev,
                IsValid: value,
              }));
            }}
          />
        </div> */}

        <div>
          <span style={{ marginRight: '8px' }}>科室白名单：</span>
          <Select
            style={{ width: '380px' }}
            mode="multiple"
            showSearch
            placeholder="请选择科室白名单"
            maxTagCount="responsive"
            options={cliDeptList}
            fieldNames={{ label: 'Name', value: 'Code' }}
            value={selectedCliDepts}
            autoClearSearchValue={false}
            filterOption={(input, option) =>
              (option?.Name ?? '')
                .toLowerCase()
                .includes(input.toLowerCase()) ||
              (option?.Code ?? '')
                .toLowerCase()
                .includes(input.toLowerCase()) ||
              pinyinInitialSearch(
                option?.Name?.toString()?.toLowerCase(),
                input.toLowerCase(),
              )
            }
            onChange={(value) => {
              setHasChanges(true);
              setSelectedCliDepts(value);
              updateCliDeptPicks({
                RuleCode: localRecord?.RuleCode,
                PickMode: 'White',
                CliDepts: value,
              });
            }}
          />
        </div>

        <div>
          <span style={{ marginRight: '8px' }}>监控类别：</span>
          <Radio.Group
            value={
              localRecord?.RuleCode?.startsWith('9-')
                ? localRecord?.CheckCategory
                : QualityCheckCategory.Custom
            }
            disabled={!localRecord?.RuleCode?.startsWith('9-')}
            onChange={(e) => {
              updateQualityCheckCategoryReq({
                ruleCode: localRecord?.RuleCode,
                checkCategory: e.target.value,
              });
              setLocalRecord((prev) => ({
                ...prev,
                CheckCategory: e.target.value,
              }));
            }}
          >
            <Radio value={QualityCheckCategory.Custom}>首页规则</Radio>
            <Radio value={QualityCheckCategory.CodeReview}>编码规则</Radio>
          </Radio.Group>
        </div>

        {localRecord?.Picks?.length > 0 && (
          <div>
            <span style={{ marginBottom: '8px', display: 'block' }}>
              规则等级设置：
            </span>
            <UniTable
              id={`picks-table-in-form-${localRecord?.RuleCode}`}
              rowKey={'RuleTemplate'}
              columns={picksColumns()}
              dataSource={localRecord?.Picks || []}
              size="small"
            />
          </div>
        )}
      </Space>
    </Modal>
  );
};

export default RuleSettingsModal;
