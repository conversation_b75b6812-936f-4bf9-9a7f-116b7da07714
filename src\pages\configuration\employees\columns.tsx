import { EmployeeSelect } from '@/pages/configuration/employees/employee-select';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import React from 'react';
import { SexSelect } from '@/pages/configuration/employees/sex-select';
import { Switch, Space, Tag } from 'antd';
import IconBtn from '@uni/components/src/iconBtn';

const operations = [
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 90,
    render: (node, record, index, action) => (
      <Space size={'middle'}>
        <IconBtn
          key="edit"
          type="edit"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        />
        <IconBtn
          key="delete"
          type="delete"
          openPop={true}
          popOnConfirm={() => {
            Emitter.emit(ConfigurationEvents.EMPLOYEE_DELETE, {
              index,
              record,
            });
          }}
        />
      </Space>
    ),
  },
];

export const employeesDictColumns = (dictData) => [
  {
    dataIndex: 'Code',
    visible: true,
    readonly: true,
    align: 'center',
    filterType: 'search',
  },
  {
    dataIndex: 'Name',
    visible: true,
    align: 'center',
    filterType: 'search',
  },
  {
    dataIndex: 'Sex',
    filterType: 'filter',
    renderFormItem: ({ dictionaryModule, index, entity }) => {
      return (
        <SexSelect
          moduleKey={dictionaryModule ?? 'XB'}
          parentId={'employee-dictionary-table'}
          onSexSelect={(values) => {
            Emitter.emit(ConfigurationEvents.EMPLOYEE_SEX_SELECT, {
              id: entity?.id,
              values: {
                Sex: values,
              },
              index: index,
            });
          }}
        />
      );
    },
  },
  {
    dataIndex: 'JobClass',
    align: 'center',
    filterType: 'filter',
    renderFormItem: ({ dictionaryModule, index, entity }) => {
      return (
        <EmployeeSelect
          moduleKey={dictionaryModule ?? 'JobClass'}
          parentId={'employee-dictionary-table'}
          onEmployeeSelect={(values) => {
            Emitter.emit(ConfigurationEvents.EMPLOYEE_JOB_CLASS_SELECT, {
              id: entity?.id,
              values: {
                JobClass: values,
              },
              index: index,
            });
          }}
        />
      );
    },
  },
  {
    dataIndex: 'EmployeeTitle',
    align: 'center',
    filterType: 'filter',
    renderFormItem: ({ dictionaryModule, index, entity }) => {
      return (
        <EmployeeSelect
          moduleKey={dictionaryModule ?? 'EmployeeTitle'}
          parentId={'employee-dictionary-table'}
          onEmployeeSelect={(values) => {
            Emitter.emit(ConfigurationEvents.EMPLOYEE_TITLE_SELECT, {
              id: entity?.id,
              values: {
                EmployeeTitle: values,
              },
              index: index,
            });
          }}
        />
      );
    },
  },
  {
    dataIndex: 'EmployeeStatus',
    align: 'center',
    filterType: 'filter',
    renderFormItem: ({ dictionaryModule, index, entity }) => {
      return (
        <EmployeeSelect
          moduleKey={dictionaryModule ?? 'EmployeeStatus'}
          parentId={'employee-dictionary-table'}
          onEmployeeSelect={(values) => {
            Emitter.emit(ConfigurationEvents.EMPLOYEE_STATUS_SELECT, {
              id: entity?.id,
              values: {
                EmployeeStatus: values,
              },
              index: index,
            });
          }}
        />
      );
    },
  },
  {
    dataIndex: 'CertificateCode',
    align: 'center',
    filterType: 'search',
  },
  {
    dataIndex: 'ChsStaffCode',
    align: 'center',
    filterType: 'search',
  },
  // {
  //   dataIndex: 'NamePy',
  //   visible: false,
  //   align: 'center',
  // },
  // {
  //   dataIndex: 'NameWb',
  //   visible: false,
  //   align: 'center',
  // },
  // {
  //   dataIndex: 'NameCustom',
  //   visible: false,
  // },
  // {
  //   dataIndex: 'NamePyAll',
  //   visible: false,
  // },
  // {
  //   dataIndex: 'Remark',
  //   visible: false,
  // },
  {
    dataIndex: 'IsValid',
    align: 'center',
    readonly: true,
    render: (node, record, index) => {
      return <span>{record['IsValid'] ? '是' : '否'}</span>;
    },
    renderFormItem: ({ index, entity }, props) => {
      return <Switch {...props?.fieldProps} defaultChecked={entity?.IsValid} />;
    },
  },
  {
    dataIndex: 'RelatedCliDepts',
    align: 'center',
    readonly: true,
    // 移除 valueType: 'tags'，使用自定义render函数
    render: (text, record, index) => {
      // 使用dictData?.[dictionaryModule]作为字典数据源
      // dictionaryModule已作为参数直接传入
      const dict =
        dictData?.[
          record?.['RelatedCliDepts']?.dictionaryModule || 'CliDepts'
        ] || [];

      if (!text || text.length === 0) {
        return <>-</>;
      }

      // 将code转换为name
      const deptNames = [text].flat(1).map((code) => {
        const dept = dict?.find((dept) => dept.Code === code);
        return dept ? dept.Name : code; // 如果找不到对应的科室，则返回原始code
      });

      return (
        <>
          {deptNames.map((name) => (
            <Tag key={name}>{name}</Tag>
          ))}
        </>
      );
    },
    renderFormItem: ({ dictionaryModule, index, entity }) => {
      return (
        <EmployeeSelect
          moduleKey={dictionaryModule ?? 'CliDepts'}
          mode={'multiple'}
          parentId={'employee-dictionary-table'}
          onEmployeeSelect={(values) => {
            Emitter.emit(ConfigurationEvents.EMPLOYEE_RELATED_DEPT_SELECT, {
              id: entity?.id,
              values: {
                RelatedCliDepts: values,
              },
              index: index,
            });
          }}
        />
      );
    },
  },
  ...operations,
];

export interface EmployeeSwitchProps {
  value?: any;
  onChange?: (value: any) => void;
}

export const EmployeeSwitch = (props: EmployeeSwitchProps) => {
  return <span>{props?.value ? '是' : '否'}</span>;
};
