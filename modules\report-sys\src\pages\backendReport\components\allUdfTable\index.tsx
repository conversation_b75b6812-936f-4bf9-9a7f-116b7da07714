import { useEffect, useRef, useState } from 'react';
import { Button, Modal, Space, message, Divider } from 'antd';
import { useRequest } from 'umi';
import { useEventEmitter, useSafeState } from 'ahooks';
import { uniCommonService } from '@uni/services/src';
import { UniTable } from '@uni/components/src';
import IconBtn from '@uni/components/src/iconBtn';
import { BackendReportReqActionType, UdfDefaultValue } from '../../constants';
import { isRespErr } from '@/utils/widgets';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { RespVO } from '@uni/commons/src/interfaces';
import _ from 'lodash';
import { ProForm, ProFormInstance } from '@uni/components/src/pro-form';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { PythonFormItems } from '../../formItems';
import BlobFileHandler from '@/components/BlobFileHandler';
import {
  BlobFileContentType,
  BlobFileTypeAccept,
} from '@/components/BlobFileHandler/constants';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { TableColumns } from '@uni/services/src/interfaces';
import { useModel } from '@@/plugin-model/useModel';

interface AllUdfTableProps {
  columns: any[];
}

const AllUdfTable = () => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  const [processedColumns, setProcessedColumns] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalRecord, setModalRecord] = useState(null);
  const [fileList, setFileList] = useSafeState([]);
  const [pythonFormItems, setPythonFormItems] = useSafeState([]);
  const event$ = useEventEmitter<any>();

  const proformRef = useRef<ProFormInstance>();
  const [proForm] = ProForm.useForm<ProFormInstance>();

  // Fetch column definitions
  const {
    data: columnsData,
    loading: columnsLoading,
    run: fetchColumns,
  } = useRequest(
    () =>
      uniCommonService(BackendReportReqActionType.GetUdfConfigs, {
        method: 'POST',
        data: {},
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<TableColumns>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.Columns || [];
        }
        return [];
      },
      onSuccess: (data) => {
        // Process columns with the returned column definitions
        if (data) {
          const processed = tableColumnBaseProcessor(
            [
              {
                dataIndex: 'UdfType',
                filterType: 'filter',
                filters: dictData?.UdfType?.map((d) => ({
                  text: d.Name,
                  value: d.Code,
                })),
              },
            ],
            data,
          ).concat({
            dataIndex: 'option',
            title: '操作',
            visible: true,
            width: 90,
            align: 'center',
            valueType: 'option',
            fixed: 'right',
            render: (text, record: any) => (
              <Space size={10}>
                {record.UdfType !== 'Python' &&
                  record.UdfType !== 'Http' &&
                  record.UdfType !== 'Sql' && (
                    <IconBtn type="edit" onClick={() => handleEdit(record)} />
                  )}
                <IconBtn
                  type="delete"
                  openPop={true}
                  popOnConfirm={() => handleDelete(record)}
                />
              </Space>
            ),
          });
          setProcessedColumns(processed);
        }
        // Trigger data fetch after columns are ready
        fetchTableData();
      },
    },
  );

  // Fetch table data (without column definitions)
  const {
    data: tableData = [],
    loading: tableLoading,
    run: fetchTableData,
  } = useRequest(
    () =>
      uniCommonService(BackendReportReqActionType.GetUdfConfigs, {
        method: 'POST',
        data: {}, // Fetch all types
      }),
    {
      manual: true, // Don't run automatically, wait for columns
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data || [];
        }
        return [];
      },
    },
  );

  // Get UDF config
  const { run: getUdfConfig } = useRequest(
    (udfConfigId: string) =>
      uniCommonService(BackendReportReqActionType.GetUdfConfig, {
        method: 'POST',
        data: { udfConfigId },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        return res;
      },
      onSuccess: (res) => {
        if (!isRespErr(res)) {
          setModalRecord(res.data);
          setModalVisible(true);
        }
      },
      onError: () => {
        message.error('获取配置详情失败');
      },
    },
  );

  // Delete UDF config
  const { run: deleteUdfConfig } = useRequest(
    (udfConfigId: string) =>
      uniCommonService(BackendReportReqActionType.DeleteUdfConfig, {
        method: 'POST',
        data: { udfConfigId },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        return res;
      },
      onSuccess: (res) => {
        if (!isRespErr(res)) {
          message.success('删除成功');
          fetchTableData(); // Refresh data
        }
      },
      onError: () => {
        message.error('删除失败');
      },
    },
  );

  // Upsert UDF config
  const { run: upsertUdfConfig } = useRequest(
    (data) =>
      uniCommonService(BackendReportReqActionType.UpsertUdfConfig, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        return res;
      },
      onSuccess: (res) => {
        if (!isRespErr(res)) {
          message.success('操作成功');
          setModalVisible(false);
          setModalRecord(null);
          fetchTableData(); // Refresh data
        }
      },
    },
  );

  // Generate form items based on columns
  const generateFormItems = () => {
    if (!columnsData) return [];

    return columnsData
      .filter((col) => col.visible !== false && col.data !== 'option')
      .map((col) => {
        console.log('col', col);
        // Base form item configuration
        const formItem = {
          name: col.data,
          label: col.title || col.data,
          required: !!col.required,
          placeholder: `请输入${col.title || col.data}`,
        };

        // Handle special case for string type with dictionary module
        if (col.dataType?.toLowerCase() === 'string' && col.dictionaryModule) {
          // Get options from dictionary based on module and group
          const options = col.dictionaryModuleGroup
            ? dictData?.[col.dictionaryModuleGroup]?.[col.dictionaryModule]
            : dictData?.[col.dictionaryModule];

          // 过滤掉 Python、Http、Sql 选项
          const filteredOptions = options?.filter?.(
            (option) => !['Python', 'Http', 'Sql'].includes(option.Code),
          );

          return {
            ...formItem,
            dataType: 'select',
            fieldProps: {
              options: filteredOptions || [],
            },
          };
        }

        // Map other data types to appropriate form field types
        switch (col.dataType?.toLowerCase()) {
          case 'boolean':
            return {
              ...formItem,
              dataType: 'switch',
            };
          case 'int32':
          case 'int64':
          case 'decimal':
          case 'double':
          case 'float':
            return {
              ...formItem,
              dataType: 'number',
            };
          case 'datetime':
          case 'date':
            return {
              ...formItem,
              dataType: 'date',
            };
          case 'textarea':
          case 'multilinetext':
            return {
              ...formItem,
              dataType: 'textarea',
            };
          default:
            return {
              ...formItem,
              dataType: 'text',
            };
        }
      });
  };

  // Handle edit action
  const handleEdit = (record) => {
    getUdfConfig(record?.Id);
  };

  // Handle delete action
  const handleDelete = (record) => {
    deleteUdfConfig(record?.Id);
  };

  // Handle add action
  const handleAdd = () => {
    setModalRecord(null);
    setModalVisible(true);
  };

  // Handle modal cancel
  const handleModalCancel = () => {
    setModalVisible(false);
    setModalRecord(null);
    if (proformRef.current) {
      proformRef.current.resetFields();
    }
  };

  // Handle modal ok
  const handleModalOk = () => {
    if (proformRef.current) {
      proformRef.current.validateFields().then((values) => {
        console.log('submitData', values);
        upsertUdfConfig(values);
      });
    }
  };

  // Reload all data
  const handleReload = () => {
    fetchColumns();
  };

  // Initial data load
  useEffect(() => {
    fetchColumns();
  }, []);

  // Set form values when modal record changes
  useEffect(() => {
    if (modalRecord && proformRef.current) {
      let { UdfParams } = modalRecord;
      const udfType = modalRecord.UdfType;

      if (udfType === 'Python') {
        proformRef.current.setFieldsValue({
          ...modalRecord,
          UdfParams: UdfParams?.map((d) => ({ value: d })),
        });
      } else {
        proformRef.current.setFieldsValue(modalRecord);
      }
    }
  }, [modalRecord]);

  // Form items
  const formItems = generateFormItems();

  return (
    <>
      <UniTable
        id="all_backend_report_table"
        rowKey="Id"
        showSorterTooltip={false}
        extraToolBar={[
          <Button key="reload" onClick={handleReload}>
            数据重载
          </Button>,
          <Button key="create" type="primary" onClick={handleAdd}>
            新建
          </Button>,
          <Divider key="create" type="vertical" />,
          <TableColumnEditButton
            {...{
              columnInterfaceUrl: BackendReportReqActionType.GetUdfConfigs,
              onTableRowSaveSuccess: (newColumns) => {
                const processed = tableColumnBaseProcessor(
                  [],
                  newColumns,
                ).concat({
                  dataIndex: 'option',
                  title: '操作',
                  visible: true,
                  width: 90,
                  align: 'center',
                  valueType: 'option',
                  fixed: 'right',
                  render: (text, record: any) => (
                    <Space size={10}>
                      {record.UdfType !== 'Python' &&
                        record.UdfType !== 'Http' &&
                        record.UdfType !== 'Sql' && (
                          <IconBtn
                            type="edit"
                            onClick={() => handleEdit(record)}
                          />
                        )}
                      <IconBtn
                        type="delete"
                        openPop={true}
                        popOnConfirm={() => handleDelete(record)}
                      />
                    </Space>
                  ),
                });
                setProcessedColumns(processed);
              },
            }}
          />,
        ]}
        loading={columnsLoading || tableLoading}
        forceColumnsUpdate
        dictionaryData={dictData}
        columns={processedColumns}
        dataSource={tableData}
        scroll={{ x: 'max-content' }}
      />

      <Modal
        title={modalRecord ? `编辑配置` : `新建配置`}
        open={modalVisible}
        maskClosable={false}
        style={{ top: 20 }}
        width={600}
        forceRender
        onCancel={handleModalCancel}
        onOk={handleModalOk}
      >
        <ProFormContainer
          preserve={false}
          form={proForm}
          formRef={proformRef}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          autoFocusFirstInput
          searchOpts={formItems}
        />
      </Modal>
    </>
  );
};

export default AllUdfTable;
