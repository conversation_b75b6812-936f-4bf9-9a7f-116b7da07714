# TanStack Table Row Selection 使用指南

## 概述

TanStack Table 组件现在支持行选择功能，你可以通过多种方式来控制行的选择状态。

## 新增的 Props

```typescript
interface TanstackTableProps {
  // 启用行选择功能
  enableRowSelection?: boolean;
  
  // 行选择状态（受控）
  rowSelection?: RowSelectionState;
  
  // 行选择状态变化回调
  onRowSelectionChange?: (rowSelection: RowSelectionState) => void;
  
  // 默认选中所有行
  defaultSelectAll?: boolean;
}
```

## 使用方法

### 方法1: 使用 `defaultSelectAll` 自动选中所有行

这是最简单的方法，组件会在数据加载后自动选中所有行：

```tsx
<UniTableNG
  columns={columns}
  dataSource={dataSource}
  rowKey="id"
  enableRowSelection={true}
  defaultSelectAll={true}
  onRowSelectionChange={(selection) => {
    console.log('选中的行:', selection);
  }}
/>
```

### 方法2: 手动控制 `rowSelection` 状态

通过外部状态控制行选择：

```tsx
const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

// 选中所有行
const selectAllRows = () => {
  const allSelected: RowSelectionState = {};
  dataSource.forEach((item) => {
    allSelected[item.id] = true; // 假设 rowKey 是 'id'
  });
  setRowSelection(allSelected);
};

// 清空所有选择
const clearAllSelection = () => {
  setRowSelection({});
};

return (
  <div>
    <button onClick={selectAllRows}>选中所有</button>
    <button onClick={clearAllSelection}>清空选择</button>
    
    <UniTableNG
      columns={columns}
      dataSource={dataSource}
      rowKey="id"
      enableRowSelection={true}
      rowSelection={rowSelection}
      onRowSelectionChange={setRowSelection}
    />
  </div>
);
```

### 方法3: 基于条件选择行

根据特定条件选择行：

```tsx
const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

// 选择满足条件的行
const selectByCondition = (condition: (item: any) => boolean) => {
  const selected: RowSelectionState = {};
  dataSource
    .filter(condition)
    .forEach((item) => {
      selected[item.id] = true;
    });
  setRowSelection(selected);
};

// 示例：选择状态为 'active' 的行
const selectActiveRows = () => {
  selectByCondition(item => item.status === 'active');
};

return (
  <div>
    <button onClick={selectActiveRows}>选择活跃行</button>
    
    <UniTableNG
      columns={columns}
      dataSource={dataSource}
      rowKey="id"
      enableRowSelection={true}
      rowSelection={rowSelection}
      onRowSelectionChange={setRowSelection}
    />
  </div>
);
```

## RowSelectionState 类型

```typescript
type RowSelectionState = {
  [key: string]: boolean;
}

// 示例
const rowSelection: RowSelectionState = {
  '1': true,  // 选中 id 为 '1' 的行
  '2': false, // 未选中 id 为 '2' 的行
  '3': true,  // 选中 id 为 '3' 的行
};
```

## 注意事项

1. **必须设置 `rowKey`**: 确保为表格设置正确的 `rowKey` 属性，这是行选择功能正常工作的前提。

2. **启用行选择**: 必须设置 `enableRowSelection={true}` 来启用行选择功能。

3. **数据变化处理**: 当 `dataSource` 发生变化时，如果使用 `defaultSelectAll={true}`，组件会自动重新选中所有新数据。

4. **性能考虑**: 对于大量数据，建议使用受控的 `rowSelection` 状态而不是 `defaultSelectAll`，以获得更好的性能控制。

## 完整示例

```tsx
import React, { useState, useEffect } from 'react';
import { RowSelectionState } from '@tanstack/react-table';
import UniTableNG from './path/to/UniTableNG';

const MyComponent = () => {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  
  const dataSource = [
    { id: '1', name: '张三', status: 'active' },
    { id: '2', name: '李四', status: 'inactive' },
    { id: '3', name: '王五', status: 'active' },
  ];
  
  const columns = [
    { title: '姓名', dataIndex: 'name', key: 'name' },
    { title: '状态', dataIndex: 'status', key: 'status' },
  ];
  
  // 组件挂载时选中所有行
  useEffect(() => {
    const allSelected: RowSelectionState = {};
    dataSource.forEach((item) => {
      allSelected[item.id] = true;
    });
    setRowSelection(allSelected);
  }, []);
  
  return (
    <UniTableNG
      columns={columns}
      dataSource={dataSource}
      rowKey="id"
      enableRowSelection={true}
      rowSelection={rowSelection}
      onRowSelectionChange={(selection) => {
        console.log('选中的行:', selection);
        setRowSelection(selection);
      }}
    />
  );
};
```

这样就可以实现将所有数据的 rowSelection 设置为 true 的功能了！
