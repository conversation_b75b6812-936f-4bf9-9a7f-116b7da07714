import React, {
  Fragment,
  useCallback,
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
} from 'react';
import {
  useDebounce,
  useDebounceFn,
  useDeepCompareEffect,
  useMutationObserver,
  useThrottleFn,
  useUpdateEffect,
} from 'ahooks';
import { uniCommonService } from '@uni/services/src';
import { v4 as uuidv4 } from 'uuid';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import _ from 'lodash';

export enum ScrollDirection {
  UP = 'up',
  DOWN = 'down',
  LEFT = 'left',
  RIGHT = 'right',
}

function isAtTop(dom): boolean {
  return dom.scrollTop <= 0;
}

function isAtBottom(dom): boolean {
  return dom.scrollTop + 10 >= dom.scrollHeight - dom.clientHeight;
}

export interface InfiniteScrollDemoProps {
  id: string;
  scrollFetchObj: {
    url: string;
    method?: string;
  };
  reqData?: any; // 调接口要用的参数
  dtParams?: any; // 传给DtParams用的sorter filter length 等 table传给后端时常用的值 会覆盖默认值
  scrollDom: Element; // 目前只能吃Dom document?.querySelector
  searchedValue?: any; // 搜索时的结果
  searchedKey?: string; // 搜索的key
  dataSource: any[]; // 数据源
  setDataSource: Function; // 把dataSource传到父组件
  setLoading?: Function; // 把loading传到父组件
}
// TODO 这个应该是个hook 不是component
const InfiniteScrollDemo = ({
  id,
  scrollFetchObj,
  reqData,
  dtParams,
  scrollDom,
  searchedKey,
  searchedValue,
  setLoading,
  ...restProps
}: InfiniteScrollDemoProps) => {
  // 给父组件的内容
  // useImperativeHandle(ref, () => ({
  //     getDataSource: () => {
  //       return dataSource
  //     }
  //   }));

  // 滚动加载 记录初始的start
  const [infiniteScrollPagi, setInfiniteScrollPagi] = useState({
    Draw: 1,
    Length: 50,
    Start: 0,
    End: 50,
    Total: 0,
  });
  // 缓存调接口之前的scrollHeight 与direction
  const domCachedScrollHeight = useRef({ number: 0, direction: undefined });
  // 暂存 重置时调接口后用户搜索的项目所对应的index
  const searchedItemIndex = useRef(-1);

  const [infiniteLoading, setInfiniteLoading] = useState(false);

  // 这边只存 最新获取的
  const [dataSource, setDataSource] = useState([]);

  // fetch
  const handleScrollFetch = async ({ direction = null, DtParam }) => {
    setInfiniteLoading(true);
    if (setLoading) {
      setLoading(true);
    }
    let response = undefined;
    response = await uniCommonService(scrollFetchObj.url, {
      method: scrollFetchObj?.method ?? 'POST',
      data:
        scrollFetchObj?.method ?? 'POST' === 'POST'
          ? { ...reqData, DtParam: { ...DtParam, ...dtParams } }
          : undefined,
      params:
        scrollFetchObj?.method === 'GET'
          ? { ...reqData, DtParam: { ...DtParam, ...dtParams } }
          : undefined,
    });

    if (response) {
      if (response.code === 0 && response?.statusCode === 200) {
        let resDataSource = response?.data?.data.slice();

        if (!direction) {
          if (searchedValue && searchedKey) {
            // 这边可以获得 用户搜素的那条的index
            let userFindIndex = resDataSource?.findIndex(
              (d) => d?.[searchedKey] === searchedValue,
            );
            console.log(resDataSource, userFindIndex);
            searchedItemIndex.current = userFindIndex;
          }

          // 重置时就用DtParam覆盖
          setInfiniteScrollPagi({
            ...infiniteScrollPagi,
            ...DtParam,
            Length: 50,
            End: DtParam?.Start + DtParam?.Length,
            Total: response?.data?.recordsTotal,
          });
          restProps?.setDataSource(
            resDataSource.map((item) => {
              item['uuidv4'] = uuidv4();
              return item;
            }),
          );
        } else if (direction === ScrollDirection.DOWN) {
          restProps?.setDataSource([
            ...restProps?.dataSource,
            ...resDataSource.map((item) => {
              item['uuidv4'] = uuidv4();
              return item;
            }),
          ]);

          // 下滚动加载分页记录
          setInfiniteScrollPagi({
            ...infiniteScrollPagi,
            Length: 50,
            End: infiniteScrollPagi.End + infiniteScrollPagi?.Length,
            Total: response?.data?.recordsTotal,
          });
        } else if (direction === ScrollDirection.UP) {
          restProps?.setDataSource([
            ...resDataSource.map((item) => {
              item['uuidv4'] = uuidv4();
              return item;
            }),
            ...restProps?.dataSource,
          ]);
          // 上滚动加载分页记录
          setInfiniteScrollPagi({
            ...infiniteScrollPagi,
            Start:
              infiniteScrollPagi.Start - infiniteScrollPagi.Length > 0
                ? infiniteScrollPagi.Start - infiniteScrollPagi.Length
                : 0,
            Length: 50,
            End:
              infiniteScrollPagi?.End ??
              (infiniteScrollPagi.Start - infiniteScrollPagi.Length > 0
                ? infiniteScrollPagi.Start - infiniteScrollPagi.Length
                : 0) + 50,
            Total: response?.data?.recordsTotal,
          });
        }
        // setDataSource([
        //   ...resDataSource.map((item) => {
        //     item['uuidv4'] = uuidv4();
        //     return item;
        //   }),
        // ]);
      } else {
      }
      setTimeout(() => {
        setInfiniteLoading(false);
        if (setLoading) {
          setLoading(false);
        }
      }, 10);
    }
  };

  // scroll fetch
  useEffect(() => {
    Emitter.on(
      EventConstant.INFINITE_SCROLL_FETCH,
      // direction: 方向, cachedScrollHeight: fetch之前先缓存一个scrollHeight
      ({ direction, cachedScrollHeight }) => {
        // 向下滚动
        if (
          direction === ScrollDirection.DOWN &&
          infiniteScrollPagi.End < infiniteScrollPagi.Total
        ) {
          domCachedScrollHeight.current = {
            number: cachedScrollHeight,
            direction: ScrollDirection.DOWN,
          };
          handleScrollFetch({
            direction,
            DtParam: {
              Draw: 1,
              Start: infiniteScrollPagi.End,
              Length: infiniteScrollPagi.Length,
              End: infiniteScrollPagi.End + infiniteScrollPagi?.Length,
            },
          });
        }
        // 向上滚动 要用到cachedScrollHeight
        else if (
          direction === ScrollDirection.UP &&
          infiniteScrollPagi.Start !== 0
        ) {
          domCachedScrollHeight.current = {
            number: cachedScrollHeight,
            direction: ScrollDirection.UP,
          };
          handleScrollFetch({
            direction,
            DtParam: {
              Draw: 1,
              Start:
                infiniteScrollPagi.Start - infiniteScrollPagi.Length > 0
                  ? infiniteScrollPagi.Start - infiniteScrollPagi.Length
                  : 0,
              Length:
                infiniteScrollPagi.Start - infiniteScrollPagi.Length > 0
                  ? infiniteScrollPagi.Length
                  : infiniteScrollPagi.Start,
            },
          });
        }
      },
    );

    return () => {
      Emitter.off(EventConstant.INFINITE_SCROLL_FETCH);
    };
  }, [infiniteScrollPagi]);

  // search fetch
  // 这里只走搜索后的查询 此时先把数据清空 loading
  const handleSearchFetch = (data) => {
    // 此时先把数据清空 保证addNode只会是50条
    restProps?.setDataSource([]);

    // 获取到DtParams之后走handleScrollFetch
    let DtParam = { ...data };
    let Length =
      DtParam.Start + infiniteScrollPagi.Length > DtParam.Length
        ? DtParam.Length -
          DtParam.Start +
          (DtParam.Start - infiniteScrollPagi.Length > 0
            ? infiniteScrollPagi.Length
            : DtParam.Start)
        : infiniteScrollPagi.Length +
          (DtParam.Start - infiniteScrollPagi.Length > 0
            ? infiniteScrollPagi.Length
            : DtParam.Start);
    // 这里不做set操作
    handleScrollFetch({
      DtParam: {
        Draw: 1,
        Start:
          DtParam.Start - infiniteScrollPagi.Length > 0
            ? DtParam.Start - infiniteScrollPagi.Length
            : 0,
        Length:
          // 前50~后50
          Length,
        End: DtParam.Start + infiniteScrollPagi.Length,
      },
    });
  };

  // init & search & reload
  const handleInitSearchFetch = () => {
    domCachedScrollHeight.current = { number: 0, direction: undefined };
    let tempSearchedValue = reqData?.Code ?? searchedValue; //优先级是reqData的Code
    if (tempSearchedValue) {
      // TODO 先简单的把 handleSearchFetch 丢出去让外部使用 // handleSearchFetch
      Emitter.emit(EventConstant.INFINITE_SCROLL_CB_FETCH, {
        searchedValue: tempSearchedValue,
        reqData,
        cb: (data) => handleSearchFetch(data),
        isdId: id, // 唯一值 用于多个infinitescroll时使用
      });
    } else {
      handleScrollFetch({
        DtParam: {
          Draw: 1,
          Start: 0,
          End: 50,
          Length: infiniteScrollPagi.Length,
        },
      });
    }
  };

  // init & search fetch
  // reload fetch
  useDeepCompareEffect(() => {
    console.log('reqdata', reqData);
    // hack
    let tempReqData = {
      canFetch: true,
      ...reqData,
    };
    if (tempReqData?.canFetch) {
      handleInitSearchFetch();
    }

    Emitter.on(EventConstant.INFINITE_SCROLL_RELOAD_FETCH, () => {
      handleInitSearchFetch();
    });

    return () => {
      Emitter.off(EventConstant.INFINITE_SCROLL_RELOAD_FETCH);
    };
  }, [reqData]);

  // 滚动监听感觉可以优化
  useEffect(() => {
    let dom = scrollDom;
    const domScrollListener = () => {
      // 这里做滚动加载
      if (infiniteLoading || (!isAtBottom(dom) && !isAtTop(dom))) return;
      if (isAtBottom(dom)) {
        Emitter.emit(EventConstant.INFINITE_SCROLL_FETCH, {
          direction: ScrollDirection.DOWN,
          cachedScrollHeight: dom.scrollHeight,
        });
      }

      // top 需要在加载完数据后 通过 新的scrollHeight - 老的scrollHeight 来定位滚动条 不然页面显示会有问题
      // 算法：scrollTop + 新的scrollHeight - 老的scrollHeight (向下也可以这么算，不过没必要)
      if (isAtTop(dom)) {
        Emitter.emit(EventConstant.INFINITE_SCROLL_FETCH, {
          direction: ScrollDirection.UP,
          cachedScrollHeight: dom.scrollHeight,
        });
      }
    };

    var throttled = _.throttle(domScrollListener, 400);
    dom?.addEventListener('scroll', throttled);

    return () => {
      dom?.removeEventListener('scroll', throttled);
    };
  }, [infiniteLoading, scrollDom]);

  // mutationObserver 切记rowKey会变化该监听结果
  useMutationObserver(
    (mutationList: MutationRecord[]) => {
      // 大前提：remove优先于add
      // console.log('mutationList', mutationList);
      // 判断：重置情况
      // 重置的时候因为uuidv4 一定会remove+add
      // 所有remove都是tr 所有add也都是tr
      if (
        // mutationList?.at(0)?.removedNodes &&
        mutationList?.at(0)?.removedNodes?.length > 0 &&
        mutationList?.every(
          (d) =>
            d.addedNodes?.[0]?.nodeName === 'TR' ||
            d.removedNodes?.[0]?.nodeName === 'TR',
        )
      ) {
        let addNodeList = mutationList?.filter((d) => d.addedNodes?.length > 0);
        let dom = scrollDom;

        // 如果有搜索内容
        // TODO  其实应该用data-row-key进行判断
        if (searchedItemIndex.current > 0) {
          console.log(searchedItemIndex, mutationList, addNodeList);

          dom?.scrollTo({
            top: (
              addNodeList?.at(searchedItemIndex.current)?.addedNodes?.[0] as any
            )?.offsetTop as number,
            behavior: 'instant',
          });
          // reset
          searchedItemIndex.current = -1;
        } else {
          dom?.scrollTo({
            top: 10,
            behavior: 'instant',
          });
        }
      }
      // 判断：滚动情况
      // 只有addNodes && 所有都是 tr 元素 就是滚动
      if (
        // mutationList?.at(0)?.addedNodes &&
        mutationList?.at(0)?.addedNodes?.length > 0 &&
        mutationList?.every((d) => d.addedNodes?.[0]?.nodeName === 'TR') &&
        domCachedScrollHeight.current?.direction === ScrollDirection.UP
      ) {
        // 计算原本行的位置 滚过去
        // scrollTop + 新的scrollHeight - 老的scrollHeight (向下也可以这么算，不过没必要)
        let dom = scrollDom;
        dom?.scrollTo({
          top:
            dom.scrollTop +
            dom.scrollHeight -
            domCachedScrollHeight.current.number,
          behavior: 'instant',
        });
        // reset
        domCachedScrollHeight.current = { number: 0, direction: undefined };
      }
    },
    scrollDom,
    {
      subtree: true,
      childList: true,
    },
  );

  return <></>;
};

export default InfiniteScrollDemo;
