import React, { useEffect, useState } from 'react';
import {
  useRequest,
  useModel,
  useLocation,
  Dispatch,
  Location,
  history,
} from 'umi';
import { RespVO, UserInfo } from '@uni/commons/src/interfaces';
import { getUserInfo, login } from '@uni/services/src/userService';
import { Button, Form, Tabs, Input, message, Spin } from 'antd';
import jwt_decode from 'jwt-decode';
import Constants from '@/constants';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  UserOutlined,
  UserAddOutlined,
  LockOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { homePageProcessor, modeProcessor } from '@/processors';
import { uniCommonService } from '@uni/services/src';

const SingleSignOn = () => {
  const location: Location = useLocation();
  const { initialState, setInitialState, refresh }: any =
    useModel('@@initialState');
  // 用户信息给到 子应用
  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateForSlave',
  );
  const [loading, setLoading] = useState(false);

  // sso

  // 1. employeeCode
  const { loading: ssoLoading, run: ssoReq } = useRequest(
    (code) => {
      setLoading(true);

      return uniCommonService('Api/Account/User/LoginByEmployeeCode', {
        method: 'POST',
        data: {
          EmployeeCode: code,
          ClientId: 'UniDmrWeb',
          ClientSecret: '75BDC6F7-87B9-C8FF-6FD8-2B865A7C82E6',
        },
      });
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => res,
      onSuccess(response, params) {
        console.log(response);
        handleSSOCompelete(response);
      },
    },
  );

  // 2. thridParty
  const { loading: thirdPartyLoading, run: thirdPartyReq } = useRequest(
    (data) => {
      setLoading(true);

      return uniCommonService('Api/Account/User/ThirdPartySso', {
        method: 'POST',
        params: {
          ...data,
          ClientId: 'UniDmrWeb',
          ClientSecret: '75BDC6F7-87B9-C8FF-6FD8-2B865A7C82E6',
        },
      });
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => res,
      onSuccess(response, params) {
        console.log(response);
        handleSSOCompelete(response);
      },
    },
  );

  const handleSSOCompelete = async (response) => {
    if (response?.code === 0) {
      if (response?.data) {
        if (response?.data?.access_token) {
          localStorage.setItem(
            'uni-connect-token',
            response?.data?.access_token,
          );

          if (response?.data?.refresh_token) {
            localStorage.setItem(
              'uni-refresh-token',
              response?.data?.refresh_token,
            );
          }

          let userBaseInfo = jwt_decode(response?.data?.access_token);
          // get userInfo
          let userInfo = await getUserProfile();
          homePageProcessor(userInfo, userInfo?.Preferences?.Menu);
          modeProcessor(userInfo, userInfo?.Preferences?.PublicMode);

          let mainState = {
            ...initialState,
            token: response?.data?.access_token,
            userBaseInfo: userBaseInfo,
            userInfo: userInfo,
          };
          setInitialState(mainState);

          // 设定子应用state
          setQiankunGlobalState({
            ...globalState,
            userInfo: mainState,
          });

          if (response?.data?.access_token) {
            // 有自定义homePage
            if (!isEmptyValues(userInfo?.Preferences?.HomePage?.Default)) {
              // 跳转到homePage
              history.push(userInfo?.Preferences?.HomePage?.Default);
            } else {
              // history.push('/main');
              history.push('/systemConfiguration/permissions/menu');
            }
          } else {
            history.push('/error?code=403');
          }
        } else {
          message.error('单点登录出现错误，请联系管理员');
        }
      } else {
        message.error('单点登录出现错误，请联系管理员');
      }
    } else {
      console.log('sso', response);
      if (response?.errorBody) {
        message.error(
          response?.errorBody ?? response?.errorBody?.error_description,
        );
      }
      history.push('/error?code=403');
    }
    setLoading(false);
  };

  const getUserProfile = async () => {
    let response: RespVO<UserInfo> = await getUserInfo();

    if (response?.code === 0) {
      if (response?.statusCode === 200) {
        return response?.data;
      }
    }

    return {};
  };

  useEffect(() => {
    if (location?.query?.token || location?.query?.Token) {
      console.log('thirdPartyReq', location);
      thirdPartyReq(location.query);
    } else {
      if (location?.query?.code) {
        ssoReq(location.query?.code);
      }
    }
  }, [location]);

  return (
    <div className="example">
      <Spin
        size="large"
        spinning={loading}
        className="w-100"
        style={{ width: '100%', padding: '30px 50px' }}
      />
    </div>
  );
};

export default SingleSignOn;
