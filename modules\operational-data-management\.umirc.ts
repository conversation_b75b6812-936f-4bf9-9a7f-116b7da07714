import { name } from './package.json';
import {
  slaveCommonConfig,
  extraBabelIncludes,
  extraWebPackPlugin,
} from '../../.umirc.commom';

export default {
  base: name,
  publicPath: '/operationalDataManagement/',
  outputPath: '../../dist/operationalDataManagement',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  dva: {
    immer: true,
    hmr: true,
  },

  qiankun: {
    master: {
      // 注册子应用信息
      apps: [
        {
          name: 'report',
          entry:
            process.env.NODE_ENV === 'production'
              ? '/report/'
              : '//localhost:8006',
        },
      ],
    },
    slave: {},
  },

  plugins: [
    require.resolve('@uni/commons/src/plugins/qiankun.ts'),
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/in/byDay',
    },
    {
      path: '/workLoad/medicalTechWorkload',
      exact: true,
      component: '@/pages/medicalTechWorkload/index',
    },
    {
      path: '/workLoad/hospWorkloadItemCheck',
      exact: true,
      component: '@/pages/medLab/index',
    },
    {
      path: '/workLoad/workloadItemCheck',
      exact: true,
      component: '@/pages/workloadItem/index',
    },

    // 要用的part
    {
      path: '/in/byDay',
      exact: true,
      component: '@/pages/inHospDynamicRegistration/editByDate/index',
    },
    {
      path: '/in/byDayWithWard',
      exact: true,
      component: '@/pages/inHospDynamicRegistration/editByDateWithWard/index',
    },
    {
      path: '/in/byDept',
      exact: true,
      component: '@/pages/inHospDynamicRegistration/editByDept/index',
    },
    {
      path: '/in/byWard',
      exact: true,
      component: '@/pages/inHospDynamicRegistration/editByWard/index',
    },
    {
      path: '/in/hierarchyBed',
      exact: true,
      component: '@/pages/inHospDynamicRegistration/hierarchyBed/index',
    },
    {
      path: '/in/proofread',
      exact: true,
      component: '@/pages/inHospDynamicRegistration/proofread/index',
    },
    {
      path: '/in/dailyProofread',
      exact: true,
      component: '@/pages/inHospDynamicRegistration/dailyProofread/index',
    },

    {
      path: '/obs/byDay',
      exact: true,
      component: '@/pages/observeDynamicRegistration/editByDate/index',
    },
    {
      path: '/obs/byDept',
      exact: true,
      component: '@/pages/observeDynamicRegistration/editByDept/index',
    },
    {
      path: '/obs/hierarchyBed',
      exact: true,
      component: '@/pages/observeDynamicRegistration/hierarchyBed/index',
    },

    {
      path: '/out/byDay',
      exact: true,
      component: '@/pages/outPatientDynamicRegistration/editByDate/index',
    },
    {
      path: '/out/byDept',
      exact: true,
      component: '@/pages/outPatientDynamicRegistration/editByDept/index',
    },

    {
      path: '/outDoctor/byDay',
      exact: true,
      component: '@/pages/outPatientDoctorDynamicRegistration/editByDate/index',
    },
    {
      path: '/outDoctor/byDept',
      exact: true,
      component: '@/pages/outPatientDoctorDynamicRegistration/editByDept/index',
    },
    // 住院动态管理
    {
      path: '/inHospManagement',
      exact: true,
      component: '@/pages/inHospDynamicManagement/index',
    },
    // 门急动态管理
    {
      path: '/outPatientManagement',
      exact: true,
      component: '@/pages/outPatientManagement/index',
    },
  ],

  proxy: {
    // 同cra的setupProxy,代理中转实现dev版本的跨域
    '/dynDdr': {
      target: 'http://172.16.3.152:5181', // 'http://172.16.3.152:5181', // http://172.16.3.217:8001/swagger/index.html
      changeOrigin: true,
      pathRewrite: { '^/dynDdr': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
};
