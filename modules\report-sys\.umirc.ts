import { name } from './package.json';

import { slaveCommonConfig } from '../../.umirc.commom';

export default {
  base: name,
  publicPath: '/reportSys/',
  outputPath: '../../dist/reportSys',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  dva: {
    immer: true,
    hmr: true,
  },

  qiankun: { slave: {} },

  plugins: [
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/selfDefinedReport',
    },
    {
      path: '/selfDefinedReport',
      exact: true,
      component: '@/pages/selfDefinedReport/index',
    },

    {
      path: '/statDeptsSetting',
      exact: true,
      component: '@/pages/statisticDeptsSetting/index',
    },

    {
      path: '/reportTree',
      exact: true,
      component: '@/pages/reportSettingTree/index',
    },

    {
      path: '/dataManagement',
      exact: true,
      component: '@/pages/dataManagement/index',
    },

    {
      path: '/dataFlow',
      exact: true,
      component: '@/pages/dataFlow/index',
    },
    // {
    //   path: '/UserDataCnt',
    //   exact: true,
    //   component: '@/pages/UserDataCnt/index',
    // },
    // {
    //   path: '/MaintenanceDataCnt',
    //   exact: true,
    //   component: '@/pages/MaintenanceDataCnt/index',
    // },
    // {
    //   path: '/MaintenanceRecordDataCnt',
    //   exact: true,
    //   component: '@/pages/MaintenanceRecordDataCnt/index',
    // },
    {
      path: '/dataReload',
      exact: true,
      component: '@/pages/dataReload/index',
    },

    {
      path: '/insurDataFlow',
      exact: true,
      component: '@/pages/insurDataFlow/index',
    },

    {
      path: '/backendReport',
      exact: true,
      component: '@/pages/backendReport/index',
    },

    {
      path: '/InterfaceSet',
      exact: true,
      component: '@/pages/InterfaceSet/index',
    },

    {
      path: '/rulesScore',
      exact: true,
      component: '@/pages/rulesScore/index',
    },

    {
      path: '/jsonExprRules',
      exact: true,
      component: '@/pages/jsonExprRules/index',
    },
    // 新版质控规则配置
    {
      path: '/qualityControlRuleConfiguration',
      exact: true,
      component: '@/pages/qualityControlRuleConfiguration/index',
    },

    {
      path: '/calendar',
      exact: true,
      component: '@/pages/calendar/index',
    },

    {
      path: '/globalConfiguration/moduleMenu',
      exact: true,
      component: '@/pages/moduleMenuConfig/index',
    },
    {
      path: '/globalConfiguration/menuOrder',
      exact: true,
      component: '@/pages/moduleMenuConfig/order/index',
    },

    {
      path: '/systemInfo',
      exact: true,
      component: '@/pages/systemInfo/index',
    },
    {
      path: '/settingColumnDefs',
      exact: true,
      component: '@/pages/settingColumnDefs/index',
    },

    // insur 支付审核规则配置
    {
      path: '/insurPayRuleSettings',
      exact: true,
      component: '@/pages/abnormalCaseRule/index',
    },
    // externalCalcRecord
    {
      path: '/externalCalcRecord',
      exact: true,
      component: '@/pages/externalCalcRecords/dmr',
    },
    {
      path: '/clientApiKey',
      exact: true,
      component: '@/pages/clientApiKey/index',
    },
    // recurring job
    {
      path: '/recurringJob',
      exact: true,
      component: '@/pages/recurringJob/index',
    },
    // 人工质控业务配置
    {
      path: '/qualityExamine',
      exact: true,
      component: '@/pages/qualityExamineSys/index',
    },
    // DictModule配置
    {
      path: '/dictModule',
      exact: true,
      component: '@/pages/dictModule/index',
    },
    // kpi配置
    {
      path: '/kpiSettings',
      exact: true,
      component: '@/pages/kpiSettings/index',
    },
  ],

  proxy: {
    '/common': {
      target: 'http://172.16.3.152:5181',
      changeOrigin: true,
      pathRewrite: { '^/common': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
};
