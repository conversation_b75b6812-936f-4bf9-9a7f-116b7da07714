export enum BlobFileMetadataReqAction {
  UploadBlobFile = 'Api/Sys/BlobFileMetadata/UploadBlobFile',
  DownloadBlobFile = 'Api/Sys/BlobFileMetadata/DownloadBlobFile',
  GetBlobFileMetadatas = 'Api/Sys/BlobFileMetadata/GetBlobFileMetadatas',
  EditBlobFileMetadata = 'Api/Sys/BlobFileMetadata/EditBlobFileMetadata',
  RemoveBlobFileMetadata = 'Api/Sys/BlobFileMetadata/RemoveBlobFileMetadata',
}

export enum BlobFileContentType {
  '/selfDefinedReport_ExportArgs' = 'reportExportTemplate',
  '/selfDefinedReport_ExportBriefArgs' = 'reportExportBriefTemplate',
  '/selfDefinedReport_ArchiveArgs' = 'reportArchiveTemplate',
  '/backendReport_Python' = 'reportPythonTemplate',
  // '/backendReport_Http' = 'reportHttpnTemplate',
  '/backendReport_Sql' = 'reportSqlTemplate',
}

export enum BlobFileTypeAccept {
  ExportArgs = '.xlsx',
  ArchiveArgs = '.docx',
  Python = '.py',
  // Http = '.'
}

export const BlobFileColumns = [
  {
    dataIndex: 'Id',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'BlobId',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'FileContentType',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'CreatorId',
    visible: false,
  },
  {
    dataIndex: 'Title',
    title: '标题',
    visible: true,
  },
  {
    dataIndex: 'OriginalFileName',
    title: '原本文件名',
    readonly: true,
    visible: true,
  },
  {
    dataIndex: 'CreatorName',
    title: '创建人',
    readonly: true,
    visible: true,
  },
  {
    dataIndex: 'CreationTime',
    title: '创建时间',
    dataType: 'dateTime',
    readonly: true,
    visible: true,
    order: 1,
  },
];
