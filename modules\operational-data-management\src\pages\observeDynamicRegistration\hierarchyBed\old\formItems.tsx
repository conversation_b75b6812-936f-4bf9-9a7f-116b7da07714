import { ProFormDependency, ProFormSelect } from '@uni/components/src/pro-form';
import dayjs from 'dayjs';

export const HierarchyBedFormItems = (hospList, hierarchyList) => [
  {
    name: 'hospCode',
    title: '院区',
    dataType: 'select',
    rules: [{ required: true }],
    visible: true,
    opts: hospList,
  },
  {
    name: 'DateRange',
    title: '起止时间',
    dataType: 'dateRange',
    transform: (values) => {
      return {
        Sdate: values ? dayjs(values[0]).format('YYYY-MM-DD') : undefined,
        Edate: values ? dayjs(values[1]).format('YYYY-MM-DD') : undefined,
      };
    },
    fieldProps: {
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
    },
  },
  {
    name: 'approvedBedAmt',
    title: '核定床位数',
    dataType: 'number',
    visible: true,
  },
  {
    name: 'openBedAmt',
    title: '开放床位数',
    dataType: 'number',
    visible: true,
  },
  {
    name: 'hierarchyCode',
    title: '医疗单元名称',
    dataType: 'custom',
    visible: true,
    render: (
      <ProFormDependency name={['hospCode']}>
        {({ hospCode }) => {
          return (
            <ProFormSelect
              {...{
                key: 'hierarchyCode',
                name: 'hierarchyCode',
                label: '医疗单元名称',
                // initialValue: dateRange,
                transform: (value) => {
                  return {
                    hierarchyCode: value ? value?.value : undefined,
                    hierarchyName: value ? value?.label : undefined,
                  };
                },
                fieldProps: {
                  placeholder: '请选择',
                  options: hierarchyList,
                  //   ?.filter(
                  //     (d) => d.HospCode === hospCode && d.HierarchyType === '1',
                  //   ),
                  fieldNames: {
                    label: 'Name',
                    value: 'Code',
                  },
                  labelInValue: true,
                },
              }}
            />
          );
        }}
      </ProFormDependency>
    ),
  },
];
