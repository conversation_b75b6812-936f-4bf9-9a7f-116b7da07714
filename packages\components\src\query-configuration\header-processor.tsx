// argDefs :
// {
//     name: '<PERSON>rrow<PERSON>',
//     title: '借阅者',
//     dataType: 'select',
//     rules: [{ required: true }],
//     // opts: borrowerOpts,
//     dictModuleGroup: 'Mr',
//     dictModule: 'Employee',
//     visible: true,
//   },

// headerItems:
// {
//       label: '借阅者',
//       componentName: 'Select',
//       needFetch: false,
//       props: {
//         dataKeyGroup: 'Mr',
//         dataKey: 'Employee',
//         valueKey: 'Borrower',
//         mode: 'single',
//         placeholder: '请填写借阅人',
//         optionValueKey: 'Code',
//         optionNameKey: 'Name',
//         dataSourcePreProcess: (searchValue, dataSource) => {
//           //TODO
//           return dataSource;
//         },
//       },
//     },

import { isEmptyValues } from '@uni/utils/src/utils';
import dayjs from 'dayjs';
import { ColumnTypeToFormItemTypeMap } from './columns';
import {
  argDefToFormItemTransformer,
  typePropertiesProcessor,
} from './processor';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import cloneDeep from 'lodash/cloneDeep';
import upperFirst from 'lodash/upperFirst';
import lowerFirst from 'lodash/lowerFirst';
import omit from 'lodash/omit';

export const ColumnTypeToComponentNameMap = {
  string: 'Input',
  int: 'InputNumber',
  decimal: 'InputNumber',
  datetime: 'DatePicker',
  bool: 'Switch',
  textarea: 'Input',
  checkbox: 'Checkbox',
};

export function getDayjsEndOfByType(pickerType: string, dayjsInstance: any) {
  switch (pickerType) {
    case 'year':
      return dayjsInstance.endOf('year');
    case 'quarter':
      return dayjsInstance.endOf('quarter');
    case 'month':
      return dayjsInstance.endOf('month');
  }

  return dayjsInstance;
}

export function getDayjsStartOfByType(pickerType: string, dayjsInstance: any) {
  switch (pickerType) {
    case 'year':
      return dayjsInstance.startOf('year');
    case 'quarter':
      return dayjsInstance.startOf('quarter');
    case 'month':
      return dayjsInstance.startOf('month');
  }

  return dayjsInstance;
}

// form value processor
// TODO POST PROCESSOR
export const headerFormValuesPostProcessor = (argDefs: any[], values: any) => {
  let processedArgs = {};

  argDefs?.forEach((argDefItem) => {
    let key = argDefItem?.props?.valueKey || argDefItem?.props?.dataKey;
    console.log('argDefItem', argDefItem, argDefItem?.argDefinition);
    if (argDefItem?.argDefinition) {
      if (values[key] === undefined || values[key] === null) {
        // capitalize key
        processedArgs[upperFirst(key)] = undefined;
        // lower first key
        processedArgs[lowerFirst(key)] = undefined;
        // key
        processedArgs[key] = undefined;
      } else {
        // capitalize key
        processedArgs[upperFirst(key)] = values[key];
        // lower first key
        processedArgs[lowerFirst(key)] = values[key];
        // key
        processedArgs[key] = values[key];
      }

      // 存在argDef
      if (
        [
          'dateRange',
          'datetime',
          'date',
          'dateTime',
          'int',
          'number',
        ]?.includes(argDefItem?.argDefinition?.ColumnType) &&
        argDefItem?.argDefinition?.ValuePickMode?.toLowerCase() === 'range'
      ) {
        if (values[key] === undefined || values[key] === null) {
          Object.keys(argDefItem?.argDefinition)?.forEach((key) => {
            if (key.includes('RangeColumnName')) {
              // capitalize key
              processedArgs[upperFirst(argDefItem?.argDefinition?.[key])] =
                undefined;
              // lower first key
              processedArgs[lowerFirst(argDefItem?.argDefinition?.[key])] =
                undefined;
              // key
              processedArgs[argDefItem?.argDefinition?.[key]] = undefined;
            }
          });
        } else {
          if (Array.isArray(values[key])) {
            values[key]?.forEach((valueItem, index) => {
              processedArgs[
                argDefItem?.argDefinition?.[`RangeColumnName${index}`]
              ] = valueItem;
            });
          }
        }
      }
    } else {
      // 对于dateRange 做一套 真 特殊处理 //TODO 暂未详细验证 不建议放到其他分支 仅浙妇保的 patch
      if (key === 'dateRange') {
        // 这里的Sdate Edate 也是从dateRange里面出来
        processedArgs['Sdate'] = values?.['dateRange']?.at(0);
        processedArgs['Edate'] = values?.['dateRange']?.at(1);
      }
    }
  });

  console.log('processedArgs', values, argDefs, processedArgs);

  return {
    ...values,
    ...omit(processedArgs, ['editedKeys']),
    customizeParamType: values?.['customizeParamType'],
  };
};

// header processor start

export const getHeaderFormItemsByInterfaceUrlArgDefAndMergeWithExistOpts =
  async (
    interfaceUrl: string,
    extraRequest: any = {},
    existOpts: any[],
    setSearchOpts: any,
    setSuccessDone: any,
  ) => {
    let currentInterfaceUrlArgDefsResp: RespVO<any> = await uniCommonService(
      interfaceUrl,
      {
        method: 'POST',
        headers: {
          'Retrieve-Arg-Definitions': 1,
        },
        ...extraRequest,
      },
    );

    if (
      currentInterfaceUrlArgDefsResp?.code === 0 &&
      currentInterfaceUrlArgDefsResp?.statusCode === 200
    ) {
      let extraConfig: any = {};
      try {
        extraConfig = JSON.parse(
          currentInterfaceUrlArgDefsResp?.data?.ExtraConfig ?? '{}',
        );
      } catch (error) {
        console.log(
          'getHeaderFormItemsByInterfaceUrlArgDefAndMergeWithExistOpts Error',
          error,
        );
      }

      let items = argDefToHeaderFormItems(
        currentInterfaceUrlArgDefsResp?.data?.ArgDefs,
        extraConfig?.DefaultArgs ?? {},
      )
        .concat((existOpts ?? [])?.filter((item) => item?.custom === true))
        .sort(
          (a: any, b: any) => (a?.ColumnSort ?? 999) - (b?.ColumnSort ?? 999),
        );

      setSearchOpts(items);
      // 成功才设置 防止多次调用
      setSuccessDone(true);
    } else {
      setSearchOpts(existOpts);
    }
  };

export const argDefToHeaderFormItems = (
  argDefItems: any[],
  defaultArgs: any,
) => {
  let headerFormItems = [];

  argDefItems
    ?.sort((a, b) => (a.ColumnSort ?? 0) - (b.ColumnSort ?? 0))
    ?.filter((item) => item?.IsVisible === true)
    ?.forEach((argDefItem) => {
      let headerFormItem = {
        label: argDefItem?.ColumnTitle,
        required: !argDefItem?.IsNullable,
        props: {
          errorTooltip: true,
          valueKey: argDefItem?.ColumnName,
          placeholder: `请填写${argDefItem?.ColumnTitle}`,
          formProps: {},
          size: 'middle',
        },
        argDefinition: argDefItem,
      };

      headerFormItem['componentName'] = componentNameTransformer(argDefItem);

      otherPropsTransformer(argDefItem, headerFormItem);
      miscProcessorTransformer(headerFormItem);
      specialKeysProcessorTransformer(headerFormItem);

      // default args processor
      defaultArgsProcessor(argDefItem, headerFormItem, defaultArgs);

      headerFormItems.push(headerFormItem);
    });

  console.log('headerFormItems', headerFormItems);

  return headerFormItems;
};

export const componentNameTransformer = (argDefItem: any) => {
  let componentName =
    ColumnTypeToComponentNameMap[
      typePropertiesProcessor(argDefItem.ColumnType)
    ];

  // select
  if (componentName === 'Input' && !isEmptyValues(argDefItem?.DictModule)) {
    let extraInputConfig: any = {};
    try {
      extraInputConfig = JSON.parse(argDefItem?.ExtraInputConfig ?? {});
    } catch (err) {
      extraInputConfig = {};
    }
    if (!isEmptyValues(extraInputConfig)) {
      if (!isEmptyValues(extraInputConfig?.dependencyFormKeys)) {
        return 'DependencySelect';
      }
    }
    return 'Select';
  }

  // 时间段
  if (argDefItem?.ValuePickMode === 'Range') {
    if (componentName === 'DatePicker') {
      return 'RangePicker';
    }

    if (componentName === 'InputNumber') {
      return 'InputNumberRange';
    }
  }

  return componentName;
};
export const otherPropsTransformer = (argDefItem: any, headerFormItem: any) => {
  // needFetch
  if (headerFormItem?.componentName !== 'Select') {
    headerFormItem['needFetch'] = false;
  }

  // dataKey
  headerFormItem['props']['dataKey'] = headerFormItem['props']['valueKey'];

  // select 多选
  if (headerFormItem?.componentName === 'Select') {
    // select
    if (argDefItem?.DictModule) {
      headerFormItem['props']['dataKey'] = argDefItem?.DictModule;
    }
    if (argDefItem?.DictModuleGroup) {
      headerFormItem['props']['dataKeyGroup'] = argDefItem?.DictModuleGroup;
    }

    if (argDefItem.ValuePickMode === 'MultipleValue') {
      headerFormItem['props']['mode'] = 'multiple';
    } else {
      headerFormItem['props']['mode'] = 'single';
    }

    headerFormItem['props']['optionValueKey'] = 'Code';
    headerFormItem['props']['optionNameKey'] = 'Name';

    headerFormItem['needFetch'] = !['DynDepts', 'Hierarchies']?.includes(
      headerFormItem['props']['dataKey'],
    );
  }

  if (headerFormItem?.componentName === 'DependencySelect') {
    let extraInputConfig: any = {};
    try {
      extraInputConfig = JSON.parse(argDefItem?.ExtraInputConfig ?? {});
    } catch (err) {
      extraInputConfig = {};
    }
    headerFormItem['props']['dependencyFormKeys'] =
      extraInputConfig?.dependencyFormKeys;
  }

  // 天数range选择
  if (headerFormItem?.componentName === 'InputNumberRange') {
    headerFormItem['props']['placeholder'] = ['请填写天数', '请填写天数'];
  }

  if (
    headerFormItem?.componentName === 'DatePicker' ||
    headerFormItem?.componentName === 'RangePicker' ||
    headerFormItem?.componentName === 'DateRadioPicker'
  ) {
    headerFormItem['props']['picker'] =
      argDefItem?.ColumnCustomType?.toLowerCase() ?? 'date';
    delete headerFormItem['props']['placeholder'];
  }

  // 能不能 清空
  if (headerFormItem?.required !== true) {
    headerFormItem['props']['allowClear'] = true;
  }

  if (headerFormItem?.required !== true) {
    headerFormItem['props']['allowClear'] = true;
  }
};

export const miscProcessorTransformer = (headerFormItem: any) => {
  if (headerFormItem?.componentName === 'Switch') {
    headerFormItem['props']['className'] = 'header_switch';
  }

  // 时间选择
  if (headerFormItem?.componentName === 'DatePicker') {
    headerFormItem['props']['valuePreProcess'] = (value) => {
      return value ? dayjs(value) : value;
    };

    headerFormItem['props']['postProcess'] = (
      currentValue,
      selectedValue,
      extra = undefined,
    ) => {
      return selectedValue
        ? dayjs(selectedValue).format('YYYY-MM-DD')
        : selectedValue;
    };
  }

  // 时间段选择
  if (headerFormItem?.componentName === 'RangePicker') {
    headerFormItem['props']['valuePreProcess'] = (value) => {
      return value?.map((item) => {
        if (item) {
          return dayjs(item);
        }

        return item;
      });
    };

    headerFormItem['props']['postProcess'] = (
      currentValue,
      selectedValue,
      extra = undefined,
    ) => {
      return selectedValue?.map((item, index) => {
        if (item) {
          if (index === 0) {
            return getDayjsStartOfByType(extra?.picker, dayjs(item)).format(
              'YYYY-MM-DD',
            );
          } else {
            return getDayjsEndOfByType(extra?.picker, dayjs(item)).format(
              'YYYY-MM-DD',
            );
          }
        }

        return item;
      });
    };
  }

  // 时间 radio选择
  if (headerFormItem?.componentName === 'DateRadioPicker') {
    headerFormItem['props']['valuePreProcess'] = (values) => {
      if (values?.length === 2) {
        values[1] = values?.at(1)?.map((item) => {
          if (item) {
            return dayjs(item);
          }
          return item;
        });
      }
      return values;
    };

    headerFormItem['props']['postProcess'] = (
      currentValues,
      changedValues,
      extra = undefined,
    ) => {
      return extra?.valueKeys?.map((d, i) => {
        if (d === 'dateRange') {
          return {
            [d]: changedValues?.[d]?.map((item, index) => {
              if (item) {
                if (index === 0) {
                  return getDayjsStartOfByType(
                    extra?.picker === 'byValue'
                      ? changedValues?.['dateType']
                      : extra?.picker,
                    dayjs(item),
                  ).format('YYYY-MM-DD');
                } else {
                  return getDayjsEndOfByType(
                    extra?.picker === 'byValue'
                      ? changedValues?.['dateType']
                      : extra?.picker,
                    dayjs(item),
                  ).format('YYYY-MM-DD');
                }
              }

              return item;
            }),
          };
        }
        // 其他的直接返回
        return { [d]: changedValues?.[d] };
      });
    };
  }
};

export const specialKeysProcessorTransformer = (headerFormItem: any) => {
  // 特定valueKey hospCodes 同时 componentName = Select
  if (
    headerFormItem?.componentName === 'Select' &&
    (headerFormItem?.props?.valueKey === 'hospCodes' ||
      headerFormItem?.props?.valueKey === 'hospCode')
  ) {
    headerFormItem['props']['dataSourcePreProcess'] = (
      searchValue,
      dataSource,
    ) => {
      if (
        searchValue['regionCodes'] &&
        searchValue['regionCodes']?.length > 0
      ) {
        dataSource = dataSource?.filter((item) =>
          searchValue['regionCodes'].includes(item?.infos?.RegionCode),
        );
      }

      if (searchValue['hospTypes'] && searchValue['hospTypes']?.length > 0) {
        dataSource = dataSource?.filter((item) =>
          searchValue['hospTypes'].includes(item?.infos?.HospType),
        );
      }

      if (
        searchValue['hospClasses'] &&
        searchValue['hospClasses']?.length > 0
      ) {
        dataSource = dataSource?.filter((item) =>
          searchValue['hospClasses'].includes(item?.infos?.HospClass),
        );
      }

      return dataSource;
    };
  }

  // 示踪通用科室 对于单个的匹配
  // tracerCliDeptsWithHospitalCode
  if (
    headerFormItem?.componentName === 'Select' &&
    headerFormItem?.props?.valueKey === 'CliDepts' &&
    headerFormItem?.props?.dataKey === 'Hierarchies' &&
    headerFormItem?.props?.mode === 'single'
  ) {
    headerFormItem['props']['dataSourcePreProcess'] = (
      searchValue,
      dataSource,
    ) => {
      if (searchValue['hospCode']) {
        return dataSource?.filter(
          (item) =>
            searchValue?.['hospCode']?.includes(item.HospCode) &&
            item.HierarchyType === '1',
        );
      } else {
        return dataSource?.filter((item) => item.HierarchyType === '1');
      }
    };
  }

  // 示踪通用科室 对于单个的匹配
  // cliDeptsWithHospitalCode
  if (
    headerFormItem?.componentName === 'Select' &&
    headerFormItem?.props?.valueKey === 'dynDept' &&
    headerFormItem?.props?.dataKey == 'DynDepts'
  ) {
    headerFormItem['props']['dataSourcePreProcess'] = (
      searchValue,
      dataSource,
    ) => {
      if (searchValue['hospCodes'] && searchValue['hospCodes']?.length > 0) {
        return dataSource?.filter((item) =>
          searchValue['hospCodes'].includes(item?.ExtraProperties?.HospCode),
        );
      } else {
        return [];
      }
    };
  }

  if (
    headerFormItem?.componentName === 'Select' &&
    headerFormItem?.props?.valueKey === 'CliDepts' &&
    headerFormItem?.props?.dataKey === 'Hierarchies' &&
    headerFormItem?.props?.mode === 'multiple'
  ) {
    headerFormItem['props']['dataSourcePreProcess'] = (
      searchValue,
      dataSource,
    ) => {
      if (searchValue['hospCodes'] && searchValue['hospCodes']?.length > 0) {
        return dataSource?.filter(
          (item) =>
            searchValue['hospCodes'].includes(item.HospCode) &&
            item.HierarchyType === '1',
        );
      } else {
        return dataSource?.filter((item) => item.HierarchyType === '1');
      }
    };
  }
};

export const defaultArgsProcessor = (
  argDefItem: any,
  headerFormItem: any,
  defaultArgs: any,
) => {
  let dataKey = argDefItem?.ColumnName;
  if (dataKey) {
    if (defaultArgs?.[dataKey] !== undefined) {
      headerFormItem['props']['formProps']['initialValue'] =
        defaultArgs?.[dataKey];
    }
  }
};
