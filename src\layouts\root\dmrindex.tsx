import { Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { MicroAppWithMemoHistory } from 'umi';
import { isEmptyValues } from '@uni/utils/src/utils';
import { history } from 'umi';

interface DmrIndexData {
  hisId: string; // 医嘱ID
  status: boolean; // 弹窗开启状态
  [key: string]: any; // 其他属性

  //   Modal其他属性
  modalAttribute?: {
    className?: string;
    mask?: boolean;
    width?: string;
    closable?: boolean;
    maskTransitionName?: string;
    transitionName?: string;
  };
}

export const DoctorMedicalRecordIndex = () => {
  const dmrIndexContainerRef = React.useRef(null);

  const dmrIndexModalRef = React.useRef(null);
  // 开启弹窗
  const [open, setOpen] = useState(false);
  // 数据
  const [dmrIndexData, setDmrIndexData] = useState<DmrIndexData>();

  useEffect(() => {
    (global?.window as any)?.eventEmitter?.on(
      'DMR_Index_STATUS',
      (data: DmrIndexData) => {
        setOpen(data?.status ?? false);
        if (!isEmptyValues(data?.hisId)) {
          setDmrIndexData(data);
        }
      },
    );

    return () => {
      (global?.window as any)?.eventEmitter?.off('DMR_Index_STATUS');
    };
  }, []);

  useEffect(() => {
    history.listen((location, action) => {
      console.log('DoctorMedicalRecordIndex History Listen', location, action);
      //   用户点击浏览器前进后退按钮
      if (action === 'POP') {
        let IndexStatus = (
          document?.querySelector('#dmr-index-container .ant-modal-wrap') as any
        )?.style?.display;
        if (IndexStatus !== undefined && IndexStatus !== 'none') {
          setOpen(false);
          dmrIndexModalRef?.current?.closeAllModal();
        }
      }
    });
  }, []);

  const onCancel = () => {
    setOpen(false);
    dmrIndexModalRef?.current?.closeAllModal();
    // 关闭 Modal的时候，恢复滚动条
    document.getElementById('content').style.overflowY = 'auto';
  };

  return (
    <div
      id={'dmr-index-container'}
      className={`dmr-index-container ${dmrIndexData?.modalAttribute?.className}`}
    >
      <Modal
        className={''}
        title=""
        open={open}
        footer={false}
        zIndex={90}
        mask={dmrIndexData?.modalAttribute?.mask ?? true}
        closable={dmrIndexData?.modalAttribute?.closable ?? true}
        width={dmrIndexData?.modalAttribute?.width ?? '100%'}
        getContainer={() => document.getElementById('dmr-index-container')}
        onCancel={() => onCancel()}
        // 去除 CSS 动画
        maskTransitionName={dmrIndexData?.modalAttribute?.maskTransitionName}
        transitionName={dmrIndexData?.modalAttribute?.transitionName}
      >
        <MicroAppWithMemoHistory
          name="external"
          url="/dmr"
          dmrIndexProps={{
            dmrContainer: dmrIndexModalRef,
            extra: {
              type: 'AlwaysOnDisplay',
              refresh: true,
              isFullScreen: true,
              commonContainerStyle: {
                marginTop: 36,
                height: 'calc(100% - 56px)',
                marginBottom: 20,
              },
              leftContainerShow: false,
              queryHeaderShow: false,
              containerRef: dmrIndexContainerRef,
              ...dmrIndexData,
            },
          }}
        />
      </Modal>
    </div>
  );
};
