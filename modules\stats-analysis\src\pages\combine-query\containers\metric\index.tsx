import React, {
  useContext,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { ExportIconBtn, UniTable } from '@uni/components/src';
import {
  Button,
  Card,
  Divider,
  Form,
  message,
  Modal,
  Space,
  TableProps,
  Tag,
} from 'antd';
import { useRequest } from 'umi';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import { RespVO, TableResp } from '@uni/commons/src/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import MetricsItemsSelector from './components';
import {
  columnSettingColumnsProcessor,
  metricParamsProcessor,
  responseSelectedMetricsProcessor,
  tableToEchartsTableColumnsProcessor,
} from '../../processor';
import './index.less';
import {
  CombineQueryDetail,
  MetricsAggregateItem,
  MetricsGroupItem,
  MetricsMetricItem,
} from '@/pages/combine-query/interfaces';
import ColumnSetting from '@uni/components/src/column-setting';
import { downloadFile } from '@uni/utils/src/download';
import { isEmptyValues } from '@uni/utils/src/utils';
import ColumnTemplateTitleAdd from '@/pages/combine-query/containers/column-save';
import CombineQueryMetricColumnSettings from '@/pages/combine-query/containers/metric/metric-columns';
import { useModel } from 'umi';
import ConfigToImg from '@uni/components/src/config2Img';
import { EmptyWrapper } from '@/pages/combine-query';
import { exportExcel } from '@uni/utils/src/excel-export';
import _, { cloneDeep } from 'lodash';
import { CombineQueryContext } from '@/pages/combine-query/combo-table-ng';
import { dateRangeValueProcessor } from '@uni/components/src/date-range-with-type';
import isEqual from 'lodash/isEqual';
import { commonMetricColumns } from '@/pages/combine-query/containers/metric/metric-columns/columns';

interface CombineQueryMetricsProps {
  detailContainerRef: any;
  nextGeneration?: boolean;
  tableRef?: any;
  tableName: string;

  needCardContainer?: boolean;

  tableHeight?: number;
  tabContainer?: any;
}

export interface SelectedMetricItem {
  itemKey?: string;
  id: string;
  customTitle?: string;
  columnSort?: number;
}

const externalStatsAnalysisConfig = (window as any).externalConfig?.[
  'statsAnalysis'
];

const expressionEmptySearch = true;

const CombineQueryMetrics = (props: CombineQueryMetricsProps) => {
  const [form] = Form.useForm();

  const combineQueryContext = useContext(CombineQueryContext);

  const [query, setQuery] = useState(undefined);

  const [metricsTableDataSource, setMetricsTableDataSource] = useState([]);

  const [metricsTableColumns, setMetricsTableColumns] = useState([]);

  const fastAggregateTriggered = React.useRef(false);

  // items
  const [aggregateItems, setAggregateItems] = useState<MetricsAggregateItem[]>(
    [],
  );
  const [groupItems, setGroupItems] = useState<MetricsGroupItem[]>([]);
  const [metricItems, setMetricItems] = useState<MetricsMetricItem[]>([]);

  const [selectedMetricsState, setSelectedMetricsState] = useState<{
    [name: string]: SelectedMetricItem[];
  }>({
    aggregate: [],
    metric: [],
    group: [],
  });

  const [combineQueryDetail, setCombineQueryDetail] =
    useState<CombineQueryDetail>({});

  const [combineQueryTitle, setCombineQueryTitle] = useState('');

  const [
    combineQueryMetricsColumnTemplateId,
    setCombineQueryMetricsColumnTemplateId,
  ] = useState(undefined);

  const [
    combineQueryMetricsColumnTemplateTitle,
    setCombineQueryMetricsColumnTemplateTitle,
  ] = useState('');

  const { globalState } = useModel('@@qiankunStateFromMaster');

  useImperativeHandle(props?.tableRef, () => ({
    fastAggregable: (data) => {
      // let currentGroupItems = selectedMetricsState?.group ?? [];
      let currentGroupItems = [];
      let clickedGroupItemId = data?.id;
      currentGroupItems.push(clickedGroupItemId);

      // 反查一套 group 列出来
      let groupItem = groupItems?.find((item) => {
        return item?.id === clickedGroupItemId;
      });

      let defaultType = 'Default';
      if (!isEmptyValues(groupItem?.allowedGroupOptions)) {
        defaultType = groupItem?.allowedGroupOptions?.at(0);
      }

      let groups = Array.from(new Set(currentGroupItems));

      setSelectedMetricsState({
        ...selectedMetricsState,
        group: groups?.map((item) => {
          return {
            id: `${item}#${defaultType}`,
            // columnSort: data?.columnSequence,
            columnSort: -1,
          };
        }),
      });

      fastAggregateTriggered.current = true;
    },

    getTableDataSource: () => {
      return metricsTableDataSource;
    },

    getTableColumns: () => {
      return metricsTableColumns;
    },

    getAggregationColumns: () => {
      return aggregateItems;
    },

    openColumnCustomizer: (data?: any) => {
      Emitter.emit(
        StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_CUSTOMIZER_CHANGE,
        {
          status: true,
          extra: data,
        },
      );
    },
    saveColumnTemplate: () => {
      Emitter.emit(
        StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_TEMPLATE_TITLE_ADD,
        {
          originTitle: combineQueryMetricsColumnTemplateTitle,
          type: 'save',
        },
      );
    },
    saveAsColumnTemplate: () => {
      Emitter.emit(
        StatsAnalysisEventConstant.STATS_ANALYSIS_METRICL_TEMPLATE_SAVE_AS,
        {
          originTitle: combineQueryMetricsColumnTemplateTitle,
          type: 'saveAs',
        },
      );
    },
    getExportDataConfig: () => {
      return {
        isBackend: false,
        frontendObj: {
          columns: metricsTableColumns
            ?.map((item) => {
              item['exportable'] = true;
              return item;
            })
            ?.filter((columnItem) => columnItem.title !== ''),
          dataSource: metricsTableDataSource,
          fileName: `组合查询统计数据-${
            combineQueryContext?.combineQueryDetail?.Title ||
            combineQueryTitle ||
            combineQueryDetail?.Title ||
            '未命名'
          }`,
          dictionaryData: globalState?.dictData,
        },
      };
    },
  }));

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_COMPLETE,
      (data) => {
        // if(data?.columns) {
        //   setMetricsTableColumns(data?.columns);
        // }

        if (data?.metricsState) {
          setSelectedMetricsState(data?.metricsState);
        }
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS,
      (query) => {
        setQuery(undefined);
        setTimeout(() => {
          setQuery(query);
        }, 0);
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
      (data: CombineQueryDetail) => {
        setCombineQueryDetail(data);
        setSelectedMetricsState(responseSelectedMetricsProcessor(data));
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_STATS_COMBINE_QUERY_TITLE,
      (title: string) => {
        setCombineQueryTitle(title);
      },
    );

    // 查询模板点击后，明细模板的点击要清空
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_CLICK,
      (item) => {
        setCombineQueryMetricsColumnTemplateTitle(undefined);
      },
    );

    Emitter.on(StatsAnalysisEventConstant.STATS_ANALYSIS_RESET, () => {
      // 清除数据
      let pagination = {
        ...backPagination,
        current: 1,
        pageSize: 20,
        total: 0,
      };
      setCombineQueryDetail({});
      setCombineQueryTitle('');
      setBackPagination(pagination);
      setMetricsTableDataSource([]);
    });

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS,
      );

      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
      );

      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_STATS_COMBINE_QUERY_TITLE,
      );

      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_CLICK);

      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_RESET);
    };
  }, []);

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.METRIC_COLUMN_TEMPLATE_CLICK,
      (item) => {
        if (item?.Id) {
          if (item?.Id === 'DEFAULT') {
            let currentDetail = Object.assign({}, combineQueryDetail);
            currentDetail['AggregationOutputColumns'] = [];
            currentDetail['GrouperCols'] = [];
            currentDetail['MetricCols'] = [];

            setCombineQueryDetail(currentDetail);
            setSelectedMetricsState({
              aggregate: [],
              metric: [],
              group: [],
            });
            setCombineQueryMetricsColumnTemplateId(undefined);
            setCombineQueryMetricsColumnTemplateTitle('常用列');
            setTimeout(() => {
              onMetricsSelected();
            }, 0);
          } else {
            metricsColumnsTemplatedGetReq(item?.Id);
          }
          setCombineQueryMetricsColumnTemplateId(item?.Id);
          setCombineQueryMetricsColumnTemplateTitle(item?.Title);
        }
      },
    );

    return () => {
      Emitter.off(StatsAnalysisEventConstant.METRIC_COLUMN_TEMPLATE_CLICK);
    };
  }, [combineQueryDetail]);

  useEffect(() => {
    Emitter.on(
      `${StatsAnalysisEventConstant.STATS_ANALYSIS_TITLE_UPDATE}-metric`,
      (item) => {
        if (item?.Id === combineQueryMetricsColumnTemplateId) {
          setCombineQueryMetricsColumnTemplateTitle(item?.Title);
        }
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.METRIC_COLUMN_TEMPLATE_DELETE_SUCCESS,
      (id) => {
        if (id === combineQueryMetricsColumnTemplateId) {
          setCombineQueryMetricsColumnTemplateId(undefined);
          // setCombineQueryMetricsColumnTemplateTitle('');
        }
      },
    );

    return () => {
      Emitter.off(
        `${StatsAnalysisEventConstant.STATS_ANALYSIS_TITLE_UPDATE}-metric`,
      );
      Emitter.off(
        StatsAnalysisEventConstant.METRIC_COLUMN_TEMPLATE_DELETE_SUCCESS,
      );
    };
  }, [combineQueryMetricsColumnTemplateId]);

  useEffect(() => {
    Emitter.emit(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_STATE,
      selectedMetricsState,
    );

    if (fastAggregateTriggered.current === true) {
      fastAggregateTriggered.current = false;
      return;
    }

    // 选中之后请求接口刷新表格
    onMetricsSelected();
  }, [selectedMetricsState]);

  useEffect(() => {
    let tableColumns = [];
    let groupTableColumns = [];

    const anaGroupOptions = globalState?.dictData?.['AnaGroupOption'];

    selectedMetricsState?.aggregate?.forEach((item) => {
      let idOptions = item?.id?.split('#');
      let selectItem = aggregateItems?.find(
        (aggregateItem) => aggregateItem?.id === idOptions?.at(0),
      );
      if (selectItem) {
        let aggregateItem =
          selectItem?.aggregationNames?.[idOptions?.at(1)] || {};

        tableColumns.push({
          ...selectItem,
          ...aggregateItem,

          originTitle: selectItem?.title,
          order: item?.columnSort ?? 0,
          title: item?.customTitle || aggregateItem?.title,

          tagTitle: item?.customTitle || aggregateItem?.title,
          itemType: 'aggregate',
          itemKey: item?.id,
        });
      }
    });

    selectedMetricsState?.group?.forEach((item) => {
      let idOptions = item?.id?.split('#');
      let selectItem = groupItems?.find(
        (groupItem) => groupItem?.id === idOptions?.at(0),
      );
      if (selectItem) {
        let extraLabel =
          idOptions?.at(1) === 'Default'
            ? ''
            : `-${
                anaGroupOptions?.find((item) => item.Code === idOptions?.at(1))
                  ?.Name
              }`;

        groupTableColumns.push({
          ...selectItem,

          originTitle: selectItem?.title,
          order: item?.columnSort ?? 0,
          title: item?.customTitle ?? `${selectItem.title}${extraLabel}`,

          tagTitle: item?.customTitle || `${selectItem.title}${extraLabel}`,
          itemType: 'group',
          itemKey: item?.id,
        });
      }
    });

    selectedMetricsState?.metric?.forEach((item) => {
      let selectItem = metricItems?.find(
        (aggregateItem) => aggregateItem?.id === item?.id,
      );
      if (selectItem) {
        tableColumns.push({
          ...selectItem,

          originTitle: selectItem?.title,
          order: item?.columnSort ?? 0,
          title: item?.customTitle || selectItem?.title,

          tagTitle: selectItem?.title,
          itemType: 'metric',
          itemKey: item?.id,
        });
      }
    });

    console.error('tableColumns', tableColumns, groupTableColumns);

    setMetricsTableColumns([
      ...groupTableColumns
        ?.map((item) => {
          // item['width'] = 100;
          item['dataIndex'] = item['name'];

          return item;
        })
        ?.sort((a, b) => a?.order - b?.order),
      ...tableColumns
        ?.map((item) => {
          // item['width'] = 100;
          item['dataIndex'] = item['name'];

          return item;
        })
        ?.sort((a, b) => a?.order - b?.order),
      ...commonMetricColumns,
    ]);
  }, [aggregateItems, metricItems, groupItems, selectedMetricsState]);

  useEffect(() => {
    let activeKey = props?.tabContainer?.current?.getActiveKey();
    if (activeKey === 'COMBO_METRIC') {
      let pagination = {
        ...backPagination,
        current: 1,
        pageSize: 20,
        total: 0,
      };

      if (expressionEmptySearch === false && isEmptyValues(query)) {
        return;
      }

      // if (
      //   query !== undefined &&
      //   (selectedMetricsState?.aggregate?.length > 0 ||
      //     selectedMetricsState?.group?.length > 0 ||
      //     selectedMetricsState?.metric?.length > 0)
      // ) {
      combineQueryMetricsReq(query, pagination.current, pagination.pageSize);
      // }

      setBackPagination(pagination);
      setMetricsTableDataSource([]);
    }
  }, [query]);

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TAB_SWITCH_METRIC_QUERY,
      (data: any) => {
        let instantQuery = true;
        if (data?.queryWhenUpdate === true) {
          if (data?.query) {
            if (isEqual(data?.query, query)) {
              instantQuery = false;
            }
          }
        }

        if (data?.query) {
          setQuery(data?.query);
        }

        let detailTableSize =
          props?.detailContainerRef?.current?.getTableDataSize();
        if (detailTableSize <= 0) {
          return;
        }

        if (expressionEmptySearch === false && isEmptyValues(data?.query)) {
          return;
        }

        // if (
        //   selectedMetricsState?.aggregate?.length > 0 ||
        //   selectedMetricsState?.group?.length > 0 ||
        //   selectedMetricsState?.metric?.length > 0
        // ) {
        if (instantQuery === false) {
          return;
        }

        let pagination = {
          ...backPagination,
          current: 1,
          pageSize: 20,
          total: 0,
        };

        combineQueryMetricsReq(
          data?.query,
          pagination.current,
          pagination.pageSize,
        );

        setBackPagination(pagination);
        setMetricsTableDataSource([]);
        // }
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_TAB_SWITCH_METRIC_QUERY,
      );
    };
  }, [query, selectedMetricsState]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 20,
    pageSizeOptions: ['20', '50', '100'],
    hideOnSinglePage: false,
  });

  const [frontPagination, setFrontPagination] = useState({
    current: 1,
    pageSize: 20,
    pageSizeOptions: ['20', '50', '100'],
  });
  // 前端分页OnChange
  const frontTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setFrontPagination({
      ...frontPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });
  };

  const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    if (expressionEmptySearch === false && isEmptyValues(query)) {
      return;
    }

    combineQueryMetricsReq(query, pagi.current, pagi.pageSize);
  };

  const { loading: combineQueryMetricsLoading, run: combineQueryMetricsReq } =
    useRequest(
      (query, current, pageSize) => {
        let data = {
          DtParam: {
            Draw: 1,
            Start: (current - 1) * pageSize,
            Length: pageSize,
          },
        };

        if (!isEmptyValues(query)) {
          data['expr'] = query;
        }

        data = {
          ...data,
          ...metricParamsProcessor(selectedMetricsState),
        };

        // 当且仅当有context然后form中能拿到值的时候才追加
        if (!isEmptyValues(combineQueryContext)) {
          let extraArgs = combineQueryContext?.basicArgForm?.getFieldsValue();
          if (!isEmptyValues(extraArgs)) {
            let processedExtraArgs = dateRangeValueProcessor(
              extraArgs,
              props?.nextGeneration === true,
            );
            if (!isEmptyValues(processedExtraArgs)) {
              data['BasicArgs'] = processedExtraArgs;
            }
          }
        }

        return uniCombineQueryService('Api/DmrAnalysis/ComboQuery/GetStats', {
          method: 'POST',
          requestType: 'json',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<TableResp<any, any>>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setMetricsTableDataSource(response?.data?.data);

            setBackPagination({
              ...backPagination,
              total: response?.data?.recordsTotal || 0,
            });
          } else {
            setMetricsTableDataSource([]);
          }
        },
      },
    );

  useEffect(() => {
    combineQueryAggregateItemsReq();
    combineQueryGroupItemsReq();
    combineQueryMetricItemsReq();
  }, []);

  const {
    loading: combineQueryAggregatesLoading,
    run: combineQueryAggregateItemsReq,
  } = useRequest(
    () => {
      let data = {
        TableName: props?.tableName,
      };

      return uniCombineQueryService(
        'Api/Analysis/AnaModelDef/GetAggregatingList',
        {
          params: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<MetricsAggregateItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setAggregateItems(response?.data);
        } else {
          setAggregateItems([]);
        }
      },
    },
  );

  const { loading: combineQueryGroupsLoading, run: combineQueryGroupItemsReq } =
    useRequest(
      () => {
        let data = {
          TableName: props?.tableName,
        };

        return uniCombineQueryService(
          'Api/Analysis/AnaModelDef/GetGrouperList',
          {
            params: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<MetricsGroupItem[]>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setGroupItems(response?.data);
          } else {
            setGroupItems([]);
          }
        },
      },
    );

  const {
    loading: combineQueryMetricItemsLoading,
    run: combineQueryMetricItemsReq,
  } = useRequest(
    () => {
      let data = {
        TableName: props?.tableName,
      };

      return uniCombineQueryService('Api/Analysis/AnaModelDef/GetMetricList', {
        params: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<MetricsMetricItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setMetricItems(response?.data);
        } else {
          setMetricItems([]);
        }
      },
    },
  );

  const cardExtra = () => {
    return (
      <Space>
        {props?.nextGeneration !== true && (
          <>
            <Button
              key="button"
              loading={metricsColumnsTemplateSaveLoading}
              onClick={() =>
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_METRICL_TEMPLATE_SAVE_AS,
                  {
                    originTitle: combineQueryMetricsColumnTemplateTitle,
                    type: 'saveAs',
                  },
                )
              }
            >
              另存为模板
            </Button>
            <Button
              key="button"
              loading={metricsColumnsTemplateSaveLoading}
              onClick={() =>
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_TEMPLATE_TITLE_ADD,
                  {
                    originTitle: combineQueryMetricsColumnTemplateTitle,
                    type: 'save',
                  },
                )
              }
            >
              保存列模板
            </Button>
          </>
        )}
        <CombineQueryMetricColumnSettings
          nextGeneration={props?.nextGeneration ?? false}
          tableName={props?.tableName}
          form={form}
          metricsState={selectedMetricsState}
          aggregateItems={aggregateItems?.slice()}
          groupItems={groupItems?.slice()}
          metricItems={metricItems?.slice()}
        />
        {props?.nextGeneration !== true && (
          <>
            <Divider type="vertical" />
            {/* 画图按钮 */}
            {externalStatsAnalysisConfig?.['drawChart'] && (
              <ConfigToImg
                tableProps={{
                  dataSource: metricsTableDataSource,
                  columns: metricsTableColumns,
                  dictionaryData: globalState?.dictData,
                }}
              />
            )}
            <ExportIconBtn
              isBackend={false}
              frontendObj={{
                columns: metricsTableColumns?.filter(
                  (columnItem) => columnItem.title !== '',
                ),
                dataSource: metricsTableDataSource,
                fileName: `组合查询统计数据-${
                  combineQueryTitle || combineQueryDetail?.Title || '未命名'
                }`,
                dictionaryData: globalState?.dictData,
              }}
              btnDisabled={metricsTableDataSource?.length < 1}
            />
          </>
        )}
      </Space>
    );
  };

  // 这个是保存
  const saveMetricsColumnsTemplate = async (id, title) => {
    message.success('列模板保存中....');
    let outputColumns = [];
    let data = {};

    let metricParams = metricParamsProcessor(selectedMetricsState);
    data['AggregationOutputColumns'] = metricParams?.outputColumns;
    data['GrouperCols'] = metricParams?.GrouperCols;

    if (id) {
      data['id'] = id;
    }

    data['Title'] = title ?? combineQueryMetricsColumnTemplateTitle;
    // TODO ？？？不能为空
    data['Expr'] = '1';
    data['DisPlayExpr'] = '1';

    metricsColumnsTemplatedSaveReq(data);
  };

  // 这个是另存为
  const saveAsMetricsColumnsTemplate = async (title) => {
    message.success('列模板另存为中....');
    let outputColumns = [];
    let data = {};

    let metricParams = metricParamsProcessor(selectedMetricsState);
    data['AggregationOutputColumns'] = metricParams?.outputColumns;
    data['GrouperCols'] = metricParams?.GrouperCols;

    data['Title'] = title ?? combineQueryMetricsColumnTemplateTitle;
    // todo ？？？不能为空
    data['Expr'] = '1';
    data['DisPlayExpr'] = '1';

    metricsColumnsTemplatedSaveReq(data);
  };

  const {
    loading: metricsColumnsTemplateGetLoading,
    run: metricsColumnsTemplatedGetReq,
  } = useRequest(
    (id) => {
      return uniCombineQueryService('Api/DmrAnalysis/ComboQueryTemplate/Get', {
        params: {
          id: id,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<CombineQueryDetail>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let currentDetail = Object.assign({}, combineQueryDetail);
          currentDetail['AggregationOutputColumns'] =
            response?.data?.AggregationOutputColumns;
          currentDetail['GrouperCols'] = response?.data?.GrouperCols;
          currentDetail['MetricCols'] = response?.data?.MetricCols;

          setCombineQueryDetail(currentDetail);
          setSelectedMetricsState(
            responseSelectedMetricsProcessor(currentDetail),
          );
          setTimeout(() => {
            onMetricsSelected();
          }, 0);
        }
      },
    },
  );

  const {
    loading: metricsColumnsTemplateSaveLoading,
    run: metricsColumnsTemplatedSaveReq,
  } = useRequest(
    (data) => {
      return uniCombineQueryService(
        'Api/DmrAnalysis/ComboQueryTemplate/SaveStatOutputTemplate',
        {
          method: 'POST',
          requestType: 'json',
          data: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<string>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('保存成功');
          setCombineQueryMetricsColumnTemplateId(response?.data);
          Emitter.emit(
            StatsAnalysisEventConstant.METRIC_COLUMN_TEMPLATE_SAVE_SUCCESS,
            response?.data,
          );
          Emitter.emit(
            StatsAnalysisEventConstant.METRIC_COLUMN_TEMPLATE_EXPAND,
          );

          return 'success';
        } else {
          message.error('保存失败');

          return 'fail';
        }
      },
      onSuccess: (data, params) => {
        // 保存 另存为 在这边更新title
        if (data === 'success') {
          setCombineQueryMetricsColumnTemplateTitle(params?.at(0)?.Title);
        }
      },
    },
  );

  const onMetricsSelected = () => {
    let pagination = {
      ...backPagination,
      current: 1,
      pageSize: 20,
      total: 0,
    };

    if (expressionEmptySearch === false && isEmptyValues(query)) {
      return;
    }

    let detailTableSize =
      props?.detailContainerRef?.current?.getTableDataSize();
    if (detailTableSize <= 0) {
      return;
    }

    // if (
    //   selectedMetricsState?.aggregate?.length > 0 ||
    //   selectedMetricsState?.group?.length > 0 ||
    //   selectedMetricsState?.metric?.length > 0
    // ) {
    combineQueryMetricsReq(query, pagination?.current, pagination?.pageSize);
    // }

    setBackPagination(pagination);
    setMetricsTableDataSource([]);
  };

  const getExportCaptionByColumns = () => {
    let exportCaption = {};
    metricsTableColumns?.forEach((item) => {
      exportCaption[item?.dataIndex] = item?.title;
    });

    return exportCaption;
  };

  const TableWrapperContainer =
    props?.needCardContainer === false ? EmptyWrapper : Card;

  const headerHeight =
    (document.querySelector('#combine-metrics-table .ant-table-header') as any)
      ?.offsetHeight ?? 0;

  return (
    <>
      <TableWrapperContainer
        id={'combined-query-metrics-container'}
        title={
          <Space>
            <span>统计数据</span>
            <Tag>
              {combineQueryMetricsColumnTemplateTitle
                ? `模板：${combineQueryMetricsColumnTemplateTitle}`
                : '无模板'}
            </Tag>
          </Space>
        }
        extra={cardExtra()}
      >
        <UniTable
          id={'combine-metrics-table'}
          rowKey={'id'}
          columns={metricsTableColumns}
          scroll={{
            y:
              props?.nextGeneration === true
                ? props?.tableHeight - 32 - 16 - headerHeight ?? 500
                : 200,
          }}
          dataSource={metricsTableDataSource}
          loading={combineQueryMetricsLoading}
          clickable={false}
          pagination={frontPagination}
          onChange={frontTableOnChange}
          // pagination={backPagination}
          // onChange={backTableOnChange}
          forceColumnsUpdate={true}
          dictionaryData={globalState?.dictData}
          // toolBarRender={() => [
          //   <Button disabled={metricsTableDataSource?.length === 0} key="button" type="primary" onClick={() => exportStatsTable()}>
          //     导出
          //   </Button>,
          // ]}
        />
      </TableWrapperContainer>

      <ColumnTemplateTitleAdd
        eventName={[
          StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_TEMPLATE_TITLE_ADD,
          StatsAnalysisEventConstant.STATS_ANALYSIS_METRICL_TEMPLATE_SAVE_AS,
        ]}
        onTemplateTitleFillIn={(title, type) => {
          if (type === 'save') {
            saveMetricsColumnsTemplate(
              combineQueryMetricsColumnTemplateId,
              title,
            );
          } else if (type === 'saveAs') {
            saveAsMetricsColumnsTemplate(title);
          }
        }}
      />
    </>
  );
};

export default CombineQueryMetrics;
