.icde-insur-separate-container {
  width: 100%;

  .ant-row {
    width: 100%;
  }

  .ant-col {
    padding: 0 8px;

    &:first-child {
      padding-left: 0;
    }

    &:last-child {
      padding-right: 0;
    }
  }

  // 主表格样式
  .icde-main-table {
    .ant-table-thead > tr > th {
      background-color: #f5f5f5;
      font-weight: 600;
    }
  }

  // 医保表格样式
  .icde-insur-table {
    .ant-table-thead > tr > th {
      background-color: #e6f7ff;
      font-weight: 600;
    }

    // 医保表格默认只读样式
    &.readonly {
      .ant-table-tbody > tr > td {
        background-color: #fafafa;
      }

      .ant-input {
        background-color: #fafafa;
        cursor: not-allowed;
      }

      .ant-select-selector {
        background-color: #fafafa;
        cursor: not-allowed;
      }
    }
  }
}