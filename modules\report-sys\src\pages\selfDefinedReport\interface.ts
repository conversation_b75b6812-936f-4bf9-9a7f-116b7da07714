export interface SwagReportingSettingMasterItems {
  ReportSettingMasterId: string;
  LoadDataSettingId: string;
  RetrieveMetadataSettingId: string;
  ValidateSettingId: string;
  PersistSettingId: string;
  ExportSettingId: string;
  ArchiveSettingId: string;
  AppCode: string;
  Title: string;
  CustomTitle: string;
  Remark: string;
  IsPeriodic: boolean;
  AllowedDateGranularity: string[];
  DataSize: string;
  IsReadonly: boolean;
  EnablePersist: boolean;
  EnableValidate: boolean;
  EnableArchive: boolean;
  EnableSubmit: boolean;
  EnableExport: boolean;
  UseBasicArgs: boolean;
  IsPublic: boolean;
  MenuItemUrl: string;
  Slug: string;
  MenuDirectories: string[];
  MenuSort: number;
}

export type DataItem = {
  name: string;
  state: string;
};

export type ReportElementType = {
  name: string;
  value: number;
  hardcore?: { [key: string]: any }; // 只在新增的时候使用
};

// 报表
export type DefaultReportValueType = {
  CustomTitle?: string;
  DataSize?: string;
  IsPeriodic?: boolean;
  AllowedDateGranularity?: string;
  IsReadonly?: boolean;
  EnableLoadData?: boolean;
  EnablePersist?: boolean;
  EnableValidate?: boolean;
  EnableArchive?: boolean;
  EnableSubmit?: boolean;
  EnableRetrieveMetadata?: boolean;
  EnableExport?: boolean;
  EnableExportBrief?: boolean;
  EnableImport?: boolean;
  IsPublic?: boolean;
  IsHidden?: boolean;
  ReportMode?: string;
};

export type ReportType = {
  name: string;
  title: string;
  types: ReportElementType[];
  defaultValues: DefaultReportValueType;
  columnsDefaultValues?: DefaultColumnsValueType;
  canSwitchMode?: boolean;
};

// 报表columns
export type DefaultColumnsValueType = {
  TableName?: string;
  ColumnType?: string;
  ColumnCustomType?: string;
  ColumnPrecision?: number;
  ColumnScale?: number;
  ColumnName?: string;
  ColumnTitle?: string;
  ColumnDescription?: string;
  ColumnSort?: number;
  GroupName?: string;
  ColumnLength?: string;
  DictModuleGroup?: string;
  DictModule?: string;
  IsNullable?: boolean;
  IsSignificant?: boolean;
  IsReadOnly?: boolean;
  IsVisible?: boolean;
  IsExportable?: boolean;
  IsCommon?: boolean;
  ParentColumnName?: string;
  ExtraInputConfig?: string;
};

export type ColumnsType = {
  name: string;
  defaultValues: DefaultColumnsValueType;
};

// 报表args
export type DefaultArgsValueType = {
  ColumnType?: string;
  ColumnCustomType?: string;
  ColumnPrecision?: number;
  ColumnScale?: number;
  DefaultValueExpr?: string;
  MinValueExpr?: string;
  MaxValueExpr?: string;
  ColumnLength?: string;
  DictModuleGroup?: string;
  DictModule?: string;
};
// 报表columns
export type ArgsType = {
  name: string;
  code: string[];
  defaultValues: DefaultArgsValueType;
};

// 报表relationModes
export type DefaultRelationModesValueType = {
  RowParamPicks?: string;
  SrcColumn?: string;
  SrcColumnParamValue?: string;
  IsDefaultParam?: boolean;
  RelationMode?: string;
};

export type RelationModeType = {
  name: string;
  title: string;
  defaultValues: DefaultRelationModesValueType;
};

export type ReportUploadMetaData = {
  IsMasterExisted?: boolean;
  IsTableExisted?: boolean;
  ExistedTableName?: string;
  Title?: string;
  Id?: string;
  [key: string]: any;
};
