import {
  ProForm,
  ProFormSwitch,
  ProFormDependency,
  ProFormSelect,
  ProFormTextArea,
} from '@uni/components/src/pro-form';
import _ from 'lodash';
import { useEventEmitter, useSafeState } from 'ahooks';
import Blob<PERSON>ileHandler from '@/components/BlobFileHandler';
import {
  BlobFileTypeAccept,
  BlobFileContentType,
} from '@/components/BlobFileHandler/constants';

const ExportFormItems = ({ editValueObj }) => {
  const event$ = useEventEmitter<any>();
  const [fileList, setFileList] = useSafeState([]);

  return (
    <ProForm.Group
      title="导出配置"
      colProps={{
        span: 12,
      }}
    >
      <ProFormSwitch
        name={['MasterArgs', 'EnableExport']}
        label="导出配置"
        initialValue={editValueObj?.Master?.ExportSettingId ? true : false}
        colProps={{
          span: 4,
        }}
      />

      {/* <ProFormSwitch
        name={['MasterArgs', 'ExportArgsShow']}
        label="导出配置"
        initialValue={Boolean(editValueObj?.Master?.ExportSettingId) || false}
        colProps={{
          span: 4,
        }}
      /> */}
      <ProFormDependency name={['MasterArgs', 'EnableExport']}>
        {({ MasterArgs: { EnableExport } }) => {
          if (EnableExport) {
            return (
              <>
                <ProFormSelect
                  name={['ExportArgs', 'ExportTemplate']}
                  label="导出模板"
                  initialValue={
                    editValueObj?.ExportSetting?.ExportTemplate || undefined
                  }
                  colProps={{
                    span: 8,
                  }}
                  fieldProps={{
                    placeholder: '请选择导出模板',
                    open: false,
                    dropdownRender: () => <></>,
                    options: fileList,
                    fieldNames: {
                      label: 'Title',
                      value: 'BlobId',
                    },
                    onClick: (e) => {
                      event$.emit('selfDefinedReport_ExportArgs');
                    },
                  }}
                />
                <ProFormTextArea
                  name={['ExportArgs', 'UdfPostScript']}
                  label="后处理配置"
                  colProps={{
                    span: 11,
                  }}
                />
              </>
            );
          }
        }}
      </ProFormDependency>
      <BlobFileHandler
        event$={event$}
        accepts={BlobFileTypeAccept.ExportArgs}
        fileContentType={BlobFileContentType['/selfDefinedReport_ExportArgs']}
        formNamePath={['ExportArgs', 'ExportTemplate']}
        setParentFileList={setFileList}
      />
    </ProForm.Group>
  );
};

export default ExportFormItems;
