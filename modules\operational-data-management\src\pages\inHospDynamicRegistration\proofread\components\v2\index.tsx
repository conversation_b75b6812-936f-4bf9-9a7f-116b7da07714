import React, {
  useReducer,
  useState,
  useEffect,
  useRef,
  useCallback,
} from 'react';
import { useModel, useRequest, useDispatch, useSelector } from 'umi';
import { useDebounceFn } from 'ahooks';
import './styles.less';
import {
  Card,
  Space,
  message,
  Button,
  Checkbox,
  Dropdown,
  MenuProps,
  Divider,
  Tooltip,
  Popconfirm,
  Input,
} from 'antd';
import { v4 as uuidv4 } from 'uuid';
import _ from 'lodash';
import UniEditableTable, {
  EditableFormInstance,
} from '@uni/components/src/table/edittable';
import { uniCommonService } from '@uni/services/src/commonService';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  tableReducer,
  tableEditPropsReducer,
  InitTableState,
  InitEditableState,
  TableAction,
  EditableTableAction,
} from '@uni/reducers/src';
import { isRespErr, RespType } from '@/utils/widgets';
import { SettingOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { useHotkeys } from 'react-hotkeys-hook';
import DetailModal from './DetailModal';
import { useSorterAndFilter } from './hooks/useSorterAndFilter';
import { useInputFocus } from './hooks/useInputFocus';
import { useAutoCalculation } from './hooks/useAutoCalculation';
import { useKeyboardShortcuts } from './hooks/useKeyboardShortcuts';
import { useDataOperations } from './hooks/useDataOperations';
import { useTableColumns } from './hooks/useTableColumns';
import ShortcutsHelpModal from '@/components/ShortcutsHelpModal';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ReqActionType, shortcuts } from '@/Constants';
import { createActionColumn } from './columns';
import { DailyProofreadEventConstants } from './constants';

const DailyProofread = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { searchParams, dictData } = globalState;
  let { hospCode, dateRange, dynDepts } = searchParams;
  const dispatch = useDispatch();
  const loadings = useSelector((state) => state.global.loadings);

  // 创建状态和引用
  const [tableParams, setTableParams] = useState<any>(undefined);
  const [onlyDiffed, setOnlyDiffed] = useState(false);
  const [finalDataSource, setFinalDataSource] = useState<any[]>([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [detailModalData, setDetailModalData] = useState<any>(null);
  const [detailModalTitle, setDetailModalTitle] = useState('');

  // 添加搜索相关状态
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [filteredDataSource, setFilteredDataSource] = useState<any[]>([]);

  // 缓存上次请求的参数和compareData结果
  const [lastParams, setLastParams] = useState<any>(null);
  const [cachedCompareData, setCachedCompareData] = useState<any>(null);

  // 创建ref来存储最新的columnArgMap和tableParams
  const columnArgMapRef = useRef<any>(null);
  const tableParamsRef = useRef<any>(undefined);

  // 表格状态管理
  const [TableState, TableDispatch] = useReducer(tableReducer, InitTableState);
  const [EditableState, EditableDispatch] = useReducer(
    tableEditPropsReducer,
    InitEditableState,
  );

  // 表格引用
  const editableTableActionRef = useRef<any>();
  const editableTableFormRef = useRef<EditableFormInstance<any>>();

  console.log('outside', columnArgMapRef.current, tableParams);

  // 处理点击详情
  const handleDetailClick = (
    record: any,
    fieldName: string,
    columnTitle: string,
  ) => {
    // 使用ref.current获取最新的columnArgMap和tableParams
    console.log(
      'columnArgMap',
      record,
      fieldName,
      columnTitle,
      columnArgMapRef.current,
      tableParamsRef.current,
    );
    let extraParam = columnArgMapRef.current?.[fieldName];
    // 准备API调用参数
    const params = {
      Sdate: dayjs(record?.ExactDate)?.format('YYYY-MM-DD'),
      Edate: dayjs(record?.ExactDate)?.format('YYYY-MM-DD'),
      HospCode: record.HospCode,
      DeptCode: record.DeptCode,
      ...(extraParam?.Name ? { [extraParam.Name]: extraParam.Value } : {}),
      isInHosp: fieldName?.includes('InHosp'),
    };

    // 设置模态框数据和标题
    setDetailModalData(params);
    setDetailModalTitle(
      `${record.DeptName} - ${dayjs(record.ExactDate).format(
        'YYYY-MM-DD',
      )} - ${columnTitle}详情`,
    );
    setDetailModalVisible(true);
  };

  // 处理双击事件
  const handleDbClick = (record: any, target?: any) => {
    if (record.IsLocked) {
      message.warning('请先解锁');
      return;
    }

    if (EditableState.editableKeys?.length > 0) {
      if (EditableState.editableKeys?.at(0) === record.Id) {
        return;
      } else {
        // 保存并跳转到对应行
        saveAndFocus(record.Id, target?.dataIndex);
      }
    } else {
      EditableDispatch({
        type: EditableTableAction.editableKeysChange,
        payload: {
          editableKeys: [record.Id],
        },
      });
      getAllInputs(target);
    }
  };

  // 加载数据的函数
  const loadData = async (params: any, skipLoading: boolean = false) => {
    // 获取基础数据和对比数据
    let baseData, compareData;

    // 检查params是否与上次相同
    const isSameParams = _.isEqual(params, lastParams);

    // 获取baseData
    if (skipLoading) {
      // 使用直接调用API的方式，不触发loading状态
      const baseResp = await uniCommonService(
        'Api/Dyn-ddr/DeptInpatientAmt/GetListByDepts',
        {
          method: 'POST',
          data: params,
        },
      );
      baseData = !isRespErr(baseResp)
        ? baseResp?.data?.map((d: any) => ({ ...d, uuid: uuidv4() }))
        : null;
    } else {
      // 使用原有的方式，会触发loading状态
      baseData = await fetchBaseData(params);
    }

    // 获取compareData
    if (isSameParams && cachedCompareData) {
      // 如果参数相同且有缓存，直接使用缓存数据
      compareData = cachedCompareData;
      console.log('使用缓存的compareData');
    } else {
      // 否则重新获取compareData
      if (skipLoading) {
        const compareResp = await uniCommonService(
          'Api/Dyn-ddr/DmrCardQuery/StatsDmrCardCntByDeptAndDaily',
          {
            method: 'POST',
            data: {
              ...params,
              DtParam: {
                Draw: 1,
                Start: 0,
                Length: 3000,
              },
            },
          },
        );
        compareData = !isRespErr(compareResp) ? compareResp?.data : null;
      } else {
        compareData = await fetchCompareData(params);
      }

      // 缓存新的params和compareData
      if (compareData) {
        setLastParams(_.cloneDeep(params));
        setCachedCompareData(compareData);
      }
    }

    if (baseData && compareData) {
      // 合并数据，找出差异
      const mergedData = mergeAndCompareData(baseData, compareData);
      console.log('mergedData', mergedData);
      // 更新表格数据
      TableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: mergedData,
        },
      });

      EditableDispatch({
        type: EditableTableAction.editableValuesChange,
        payload: {
          value: mergedData,
        },
      });
    } else {
      message.error('获取数据失败');
    }
  };

  // 自定义hooks
  const { sorterInfo, setSorterInfo, sortedFilteredTable } = useSorterAndFilter(
    searchKeyword ? filteredDataSource : finalDataSource,
  );
  const { nowAllInput, getAllInputs } = useInputFocus();
  const {
    autoCalculationKeys,
    isAutoComputeCntInput,
    autoCalculateDebounceFn,
    resetAutoCompute,
    clearEditedRecord,
    nowEditedRecord,
  } = useAutoCalculation(editableTableFormRef);
  const {
    reqActionReq,
    editSaveHandler,
    mergeAndCompareData,
    mergeAndCompareSingleRecord,
  } = useDataOperations({
    TableDispatch,
    EditableDispatch,
    tableParams,
    dictData,
    loadData,
    dataSource: finalDataSource,
    cachedCompareData,
    nowEditedRecord, // 传递nowEditedRecord到useDataOperations
    clearEditedRecord,
  });

  // 获取表格列配置
  const tableColumns = useTableColumns(
    handleDetailClick,
    autoCalculationKeys,
    isAutoComputeCntInput,
    handleDbClick,
  );

  // 搜索过滤函数
  const filterDataByKeyword = useCallback(
    (keyword: string, dataSource: any[]) => {
      if (!keyword || !dataSource) {
        return dataSource || [];
      }

      const filteredData = dataSource.filter((item) => {
        // 搜索科室编码或科室名称
        const deptCode = (item.DeptCode || '').toString().toLowerCase();
        const deptName = (item.DeptName || '').toString().toLowerCase();
        const lowerKeyword = keyword.toLowerCase();

        return (
          deptCode.includes(lowerKeyword) || deptName.includes(lowerKeyword)
        );
      });

      return filteredData;
    },
    [],
  );

  // 使用debounce处理搜索
  const { run: debouncedSearch } = useDebounceFn(
    (value: string) => {
      setSearchKeyword(value);
      const filtered = filterDataByKeyword(value, finalDataSource);
      setFilteredDataSource(filtered);
    },
    { wait: 300 },
  );

  // 保存并聚焦
  const saveAndFocus = (targetKey: string, dataIndex?: string) => {
    resetAutoCompute();
    let rowKey = Object.keys(
      editableTableFormRef.current?.getFieldsValue(),
    )?.at(0);
    let data = editableTableFormRef.current?.getFieldsValue()?.[rowKey];

    if (!rowKey) return;

    if (!rowKey?.includes('new')) {
      EditableDispatch({
        type: EditableTableAction.editableKeysChange,
        payload: {
          editableKeys: [targetKey],
        },
      });
    }

    getAllInputs(dataIndex ? { dataIndex } : undefined);

    editSaveHandler(rowKey, data);
  };

  // 快捷键注册 - 在组件顶层注册
  // 注册所有快捷键
  shortcuts
    ?.filter((item) => item?.onlyForHint !== true)
    ?.forEach((item) => {
      useHotkeys(item.key, item.callback, {
        enableOnFormTags: true,
        enabled: item?.enabled,
        preventDefault: true,
        enableOnContentEditable: true,
        ...(item?.options ?? {}),
      });
    });

  // 使用键盘快捷键
  useKeyboardShortcuts({
    editableTableFormRef,
    editableTableActionRef,
    resetAutoCompute,
    clearEditedRecord, // 传递clearEditedRecord
    EditableState,
    EditableDispatch,
    sortedFilteredTable,
    getAllInputs,
    nowAllInput,
    editSaveHandler,
  });

  useEffect(() => {
    console.log('nowAllInput changed', nowAllInput);
  }, [nowAllInput]);

  // 查询列-参数映射接口 Api/Dyn-ddr/DmrCardQuery/InpatientAmtColumnArgMap
  const {
    data: columnArgMap,
    loading: columnArgMapLoading,
    run: fetchColumnArgMap,
  } = useRequest(
    () =>
      uniCommonService('Api/Dyn-ddr/DmrCardQuery/InpatientAmtColumnArgMap', {
        method: 'POST',
      }),
    {
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          return res?.data;
        }
        return null;
      },
    },
  );

  // 当columnArgMap变化时，更新ref
  useEffect(() => {
    if (columnArgMap) {
      columnArgMapRef.current = columnArgMap;
    }
  }, [columnArgMap]);

  // 当tableParams变化时，更新ref
  useEffect(() => {
    if (tableParams) {
      tableParamsRef.current = tableParams;
    }
  }, [tableParams]);

  // 获取列定义
  const { loading: columnsLoading, run: fetchColumns } = useRequest(
    () =>
      uniCommonService('Api/Dyn-ddr/DeptInpatientAmt/GetListByDepts', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          TableDispatch({
            type: TableAction.columnsChange,
            payload: {
              columns: tableColumnBaseProcessor(
                createActionColumn,
                tableColumns.columnsEditHandler(res?.data?.Columns),
                'local',
              ),
            },
          });
        }
        return null;
      },
    },
  );

  // 获取基础数据列表
  const {
    data: fetchedBaseData,
    loading: baseDataLoading,
    run: fetchBaseData,
  } = useRequest(
    (params) =>
      uniCommonService('Api/Dyn-ddr/DeptInpatientAmt/GetListByDepts', {
        method: 'POST',
        data: params,
      }),
    {
      manual: true,
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          return res?.data?.map((d: any) => ({ ...d, uuid: uuidv4() }));
        }
        return null;
      },
    },
  );

  // 获取对比数据列表
  const {
    data: fetchedCompareData,
    loading: compareDataLoading,
    run: fetchCompareData,
  } = useRequest(
    (params) =>
      uniCommonService(
        'Api/Dyn-ddr/DmrCardQuery/StatsDmrCardCntByDeptAndDaily',
        {
          method: 'POST',
          data: {
            ...params,
            DtParam: {
              Draw: 1,
              Start: 0,
              Length: 3000,
            },
          },
        },
      ),
    {
      manual: true,
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          return res?.data;
        }
        return null;
      },
    },
  );

  // 当searchParams变化时，重新获取数据
  useEffect(() => {
    if (
      (searchParams && searchParams?.triggerSource === 'btnClick') ||
      (searchParams?.dateRange &&
        searchParams?.dynDepts?.length > 0 &&
        !tableParams)
    ) {
      const params = {
        Sdate: searchParams?.dateRange?.at(0),
        Edate: searchParams?.dateRange?.at(1),
        HospCode: searchParams?.hospCode,
        DeptCode: searchParams?.dynDepts,
      };

      setTableParams(params);
      loadData(params);
    }
  }, [searchParams]);

  // 当onlyDiffed变化时，过滤数据
  useEffect(() => {
    let filteredData = EditableState.value;

    // 首先应用差异过滤
    if (onlyDiffed) {
      filteredData = filteredData.filter((d) => d.hasDiscrepancy);
    }

    setFinalDataSource(filteredData);

    // 然后应用搜索过滤
    const searchFiltered = filterDataByKeyword(searchKeyword, filteredData);
    setFilteredDataSource(searchFiltered);
  }, [onlyDiffed, EditableState.value, filterDataByKeyword, searchKeyword]);

  // 当搜索关键词变化时，重新过滤数据
  useEffect(() => {
    const filtered = filterDataByKeyword(searchKeyword, finalDataSource);
    setFilteredDataSource(filtered);
  }, [searchKeyword, finalDataSource, filterDataByKeyword]);

  useEffect(() => {
    Emitter.on(DailyProofreadEventConstants.ROW_EDIT, (record) => {
      EditableDispatch({
        type: TableAction.editableKeysChange,
        payload: {
          editableKeys: [record.Id],
        },
      });
      getAllInputs();
    });

    Emitter.on(DailyProofreadEventConstants.ROW_LOCK, ({ ids, actionType }) => {
      reqActionReq(ids, actionType);
    });
    Emitter.on(
      DailyProofreadEventConstants.ROW_UNLOCK,
      ({ ids, actionType }) => {
        reqActionReq(ids, actionType);
      },
    );

    return () => {
      Emitter.off(DailyProofreadEventConstants.ROW_EDIT);
      Emitter.off(DailyProofreadEventConstants.ROW_LOCK);
      Emitter.off(DailyProofreadEventConstants.ROW_UNLOCK);
    };
  }, []);

  return (
    <Card
      title={<Space>住院登记校对</Space>}
      extra={
        <Space>
          {/* {EditableState.editableKeys.length !== 0 && (
            <Button
              type="primary"
              onClick={() => {
                // 保存编辑的数据
                if (EditableState.editableKeys.length > 0) {
                  const rowKey = Object.keys(
                    editableTableFormRef.current?.getFieldsValue(),
                  )[0];
                  const data = editableTableFormRef.current?.getFieldsValue();
                  editSaveHandler(rowKey, data);
                }
              }}
              disabled={EditableState.editableKeys.length === 0}
            >
              保存
            </Button>
          )} */}
          {/* <Button
            disabled={EditableState.editableKeys.length !== 0}
            onClick={() => {
              // 刷新数据
              if (tableParams) {
                loadData(tableParams);
              }
            }}
          >
            刷新
          </Button> */}
          <Input.Search
            placeholder="搜索科室编码或名称"
            style={{ width: 220 }}
            onChange={(e) => debouncedSearch(e.target.value)}
            allowClear
          />
          <Checkbox
            onChange={(e) => {
              setOnlyDiffed(e.target.checked);
            }}
          >
            仅显示有差异数据
          </Checkbox>
          <Divider type="vertical" />
          <Tooltip title="表格配置">
            <Button
              type="text"
              shape="circle"
              icon={<SettingOutlined className="infinity_rotate" />}
              onClick={() => {
                tableColumns.handleDropDownClick('columns', TableDispatch);
              }}
            />
          </Tooltip>
        </Space>
      }
    >
      <UniEditableTable
        id="uniqueProofreadTable_v2"
        rowKey="Id"
        columns={TableState.columns}
        value={searchKeyword ? filteredDataSource : finalDataSource}
        bordered
        forceColumnsUpdate
        enableShortcuts
        size="small"
        showSorterTooltip={false}
        loading={baseDataLoading || compareDataLoading || columnsLoading}
        actionRef={editableTableActionRef}
        editableFormRef={editableTableFormRef}
        rowClassName={(record) => {
          return record.hasDiscrepancy ? 'remarked-row' : '';
        }}
        dictionaryData={dictData}
        scroll={{ x: 'max-content' }}
        // pagination={false}
        // widthCalculate={true}
        widthDetectAfterDictionary
        onTableChange={(pagination, filters, sorter) => {
          setSorterInfo(sorter);
        }}
        recordCreatorProps={false}
        editable={{
          type: 'single',
          editableKeys: EditableState.editableKeys,
          onChange: (editableKeys) => {
            EditableDispatch({
              type: EditableTableAction.editableKeysChange,
              payload: { editableKeys },
            });
          },
          actionRender: (record, config, defaultDoms) => {
            return [
              <a
                onClick={() => {
                  const rowKey = Object.keys(
                    editableTableFormRef.current?.getFieldsValue(),
                  )[0];
                  const data = editableTableFormRef.current?.getFieldsValue();
                  editSaveHandler(rowKey, data);
                }}
              >
                保存
              </a>,
              defaultDoms.cancel,
            ];
          },
          onValuesChange: (record, dataSource) => {
            // 编辑值变化时处理自动计算
            if (autoCalculationKeys?.length > 0) {
              autoCalculateDebounceFn(
                autoCalculationKeys,
                record,
                editableTableFormRef,
                isAutoComputeCntInput.current,
              );
            }
          },
        }}
      />

      {/* 详情模态框 */}
      <DetailModal
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        apiData={detailModalData}
        title={detailModalTitle}
      />

      {/* 快捷键帮助弹窗 */}
      <ShortcutsHelpModal shortcuts={shortcuts} />
    </Card>
  );
};

export default DailyProofread;
