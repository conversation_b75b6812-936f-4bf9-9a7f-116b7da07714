export interface IDeptpatientAmtListItem {
  HospCode: string;
  DeptCode: string;
  ExactDate: string;
  Sort: number;
  Id: number;
  Status: string;
  StatusName: string;
  IsLocked: boolean;
  Remark: string;
  LastModificationTime: string;
  ApprovedBedsNumber: number;
  SuppliedBedsNumber: number;
  StayInCnt: number;
  InHospCnt: number;
  TransInCnt: number;
  TransOutCnt: number;
  TotalOutHospCnt: number;
  OutHospCnt: number;
  DeathCnt: number;
  StayingCnt: number;
  CuredCnt: number;
  ImprovedCnt: number;
  NotCuredCnt: number;
  CondDangCnt: number;
  ActualBedDays: number;
  DscgPatientActualBedDays: number;
  IsCorrect: boolean;
  CorrectionTime: string;
  OtherCnt1: number;
  OtherCnt2: number;
  OtherCnt3: number;
  OtherCnt4: number;
  OtherCnt5: number;
  OtherCnt6: number;
  OtherCnt7: number;
  OtherCnt8: number;
  HospName: string;
  DeptName: string;
}

export interface IStatsDmrCardCntByDeptAndDailyListItem {
  HospCode: string;
  HospName: string;
  DeptCode: string;
  DeptName: string;
  ExactDate: string; // 保持ISO时间字符串格式
  InHospCnt: number;
  TransInCnt: number;
  TransOutCnt: number;
  TotalOutHospCnt: number;
  OutHospCnt: number;
  CuredCnt: number;
  ImprovedCnt: number;
  NotCuredCnt: number;
  DeathCnt: number;
  OtherCnt1: number;
  OtherCnt2: number;
  OtherCnt3: number;
  OtherCnt4: number;
  OtherCnt5: number;
  OtherCnt6: number;
  OtherCnt7: number;
  OtherCnt8: number;
}
