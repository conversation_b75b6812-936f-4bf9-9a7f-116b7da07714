import { But<PERSON>, Popconfirm, Tooltip } from 'antd';
import {
  UndoOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
  DeleteOutlined,
  PlusOutlined,
  EyeOutlined,
  CloseOutlined,
  AuditOutlined,
  PrinterOutlined,
  FileTextOutlined,
  CloudDownloadOutlined,
  CarryOutOutlined,
  KeyOutlined,
  BugOutlined,
  UnorderedListOutlined,
  ProjectOutlined,
  ForwardOutlined,
} from '@ant-design/icons';
import { RecordIcon } from '@uni/components/src/custom-icon/icon';
import './index.less';

const DrillDownIcon = () => <ForwardOutlined rotate={90} />;

const iconList = [
  {
    type: 'undo',
    title: '撤销',
    icon: <UndoOutlined />,
  },
  {
    type: 'checkInfo',
    title: '查看病案首页',
    icon: <RecordIcon />,
  },
  {
    type: 'check',
    title: '查看',
    icon: <EyeOutlined />,
  },
  {
    type: 'edit',
    title: '编辑',
    icon: <EditOutlined />,
  },
  {
    type: 'delete',
    title: '删除',
    icon: <DeleteOutlined />,
  },
  {
    type: 'lock',
    title: '锁定',
    icon: <LockOutlined />,
  },
  {
    type: 'unlock',
    title: '解锁',
    icon: <UnlockOutlined />,
  },
  {
    type: 'destroy',
    title: '作废',
    icon: <CloseOutlined />,
  },
  {
    type: 'copy',
    title: '复制当前条目',
    icon: <PlusOutlined />,
  },
  {
    type: 'review',
    title: '审核',
    icon: <AuditOutlined />,
  },
  {
    type: 'print',
    title: '打印',
    icon: <PrinterOutlined />,
  },
  {
    type: 'details',
    title: '明细',
    icon: <FileTextOutlined />,
  },
  {
    type: 'details2',
    title: '明细',
    icon: <UnorderedListOutlined />,
  },
  {
    type: 'download',
    title: '下载',
    icon: <CloudDownloadOutlined />,
  },
  {
    type: 'key',
    title: '修改密码',
    icon: <KeyOutlined />,
  },
  {
    type: 'test',
    title: '测试',
    icon: <BugOutlined />,
  },
  {
    type: 'accept',
    title: '接受',
    icon: <CarryOutOutlined />,
  },
  {
    type: 'add',
    title: '新增',
    icon: <PlusOutlined />,
  },
  {
    type: 'log',
    title: '查看日志',
    icon: <ProjectOutlined />,
  },
  {
    type: 'keybord',
    title: '查看快捷键',
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          x="4"
          y="18"
          width="40"
          height="24"
          rx="2"
          stroke="#757575"
          stroke-width="4"
          stroke-linejoin="round"
        />
        <circle cx="14" cy="24" r="2" fill="#757575" />
        <circle cx="16" cy="30" r="2" fill="#757575" />
        <circle cx="10" cy="30" r="2" fill="#757575" />
        <circle cx="20" cy="24" r="2" fill="#757575" />
        <circle cx="22" cy="30" r="2" fill="#757575" />
        <circle cx="26" cy="24" r="2" fill="#757575" />
        <circle cx="28" cy="30" r="2" fill="#757575" />
        <circle cx="32" cy="24" r="2" fill="#757575" />
        <circle cx="34" cy="30" r="2" fill="#757575" />
        <circle cx="38" cy="24" r="2" fill="#757575" />
        <path
          d="M17 36H31"
          stroke="#757575"
          stroke-width="4"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M33 18V13.125C33 12.5727 33.4477 12.125 34 12.125H39C39.5523 12.125 40 11.6773 40 11.125V6"
          stroke="#757575"
          stroke-width="4"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    ),
  },
  {
    type: 'drillDown',
    title: '下钻',
    icon: <DrillDownIcon />,
  },
  {
    type: 'checkDoctorInfo',
    title: '查看医生首页',
    icon: <RecordIcon />,
  },
];

export type IconBtnProps = {
  type: string;

  customIcon?: React.ReactNode;

  title?: string | React.ReactNode;
  className?: string;
  style?: any;
  onClick?: Function;

  openPop?: boolean;
  popTitle?: string | React.ReactNode;
  popOnConfirm?: Function;
  popOnCancel?: Function;

  btnDisabled?: boolean;
};

const IconBtn = ({
  type,
  customIcon,
  title,
  className,
  style,
  onClick,
  openPop = false,
  popTitle,
  popOnConfirm,
  popOnCancel,
  btnDisabled,
}: IconBtnProps) => {
  const current = iconList.find((item) => item.type === type);
  // @ts-ignore
  return (
    <Popconfirm
      disabled={openPop ? !openPop : true}
      key={type}
      title={
        popTitle || title || current?.title
          ? `确定要${popTitle || title || current?.title}？`
          : '确定吗？'
      }
      onConfirm={(e) => {
        popOnConfirm && popOnConfirm(e);
      }}
      onCancel={(e) => {
        popOnCancel && popOnCancel(e);
      }}
    >
      <Tooltip title={title || current?.title}>
        <a
          className={className}
          style={style || {}}
          disabled={btnDisabled}
          onClick={(e) => {
            e.stopPropagation();
            if (!btnDisabled) {
              onClick && onClick(e);
            }
          }}
        >
          {customIcon ?? current?.icon}
        </a>
      </Tooltip>
    </Popconfirm>
  );
};

export default IconBtn;
