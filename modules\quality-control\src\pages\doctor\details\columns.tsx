import { Tag } from 'antd';
import { v4 as uuidv4 } from 'uuid';

export const CasebookColumns = [
  { dataIndex: 'PatNo', title: '病案号', width: 90 },
  { dataIndex: 'PatName', width: 80 },
  { dataIndex: 'OutDate', width: 130 },

  { dataIndex: 'CliDept', width: 190 },

  {
    dataIndex: 'ReviewResults',
    title: '审核结果',
    width: 'auto',
    render: (text, record) => {
      return record?.ReviewResults.map((item) => (
        <Tag key={uuidv4()} style={{ margin: '5px 5px 0 0' }}>
          {item.ErrMsg}
        </Tag>
      ));
    },
  },

  { dataIndex: 'Coder', width: 100 },
  { dataIndex: 'CodeTime', width: 130 },
];
