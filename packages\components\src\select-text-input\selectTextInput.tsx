import { Form, Select, Input, Space } from 'antd';
import { FC, useEffect } from 'react';
import _ from 'lodash';
import './index.less';

interface UniSelectTextInputFormProps {
  form: any;
  selectFormKey?: string;
  inputFormKey?: string;
  postProcess?: (currentValue, selectedValue, extra) => any;
}

export interface IUniSelectTextInputProps extends UniSelectTextInputFormProps {
  selectOptions: { label: string; value: string }[];
  selectWidth: number;
  value?: [string, string]; // [select value, input value]
  placeholder?: [string, string]; // [select placeholder, input placeholder]
  status?: '' | 'error' | 'warning';
  requiredStatus?: boolean;
  extra?: any;
  onChange?: (value: any) => void;
  onChangeValueKeys?: (values: any) => void;
}

const SelectTextInput: FC<IUniSelectTextInputProps> = ({
  value,
  selectWidth,
  selectOptions,
  placeholder,
  status,
  requiredStatus,
  onChange,
  onChangeValueKeys,
  extra,
}) => {
  // 获取选择器的值
  let selectValue =
    extra?.form && extra?.valueKeys
      ? Form.useWatch(extra?.valueKeys?.at(0), extra?.form)
      : value?.at(0);

  // 获取输入框的值
  let inputValue =
    extra?.form && extra?.valueKeys
      ? Form.useWatch(extra?.valueKeys?.at(1), extra?.form)
      : value?.at(1);

  // 根据是否在表单中使用，设置不同的包装器组件
  let SelectWrapper = (props: any) => <>{props.children}</>;
  let InputWrapper = (props: any) => <>{props.children}</>;

  if (extra?.form) {
    SelectWrapper = Form.Item;
    InputWrapper = Form.Item;
  }
  return (
    <div className="select-text-input d-flex">
      <Space className="select-text-input-content">
        {/* Select 选择器部分 */}
        <SelectWrapper name={extra?.valueKeys?.at(0)}>
          <Select
            style={{ width: selectWidth || 85 }}
            value={selectValue}
            placeholder={placeholder?.at(0) || '请选择'}
            options={selectOptions}
            onChange={(selectedValue) => {
              // 如果在表单中使用，则设置表单值
              if (extra?.form && extra?.valueKeys?.at(0)) {
                extra.form.setFieldValue(
                  extra?.valueKeys?.at(0),
                  selectedValue,
                );

                // 调用外部onChange，值的格式根据是否有inputValue决定
                onChange?.(
                  inputValue
                    ? [selectedValue, inputValue]
                    : [selectedValue, ''],
                );
              } else {
                // 独立使用时，调用onChangeValueKeys传递结构化数据
                onChangeValueKeys?.({
                  selectValue: selectedValue,
                  inputValue: inputValue || '',
                });
              }
            }}
            status={
              status ??
              (requiredStatus
                ? selectValue
                  ? ''
                  : 'error'
                : ('' as '' | 'error' | 'warning'))
            }
          />
        </SelectWrapper>

        {/* Input 输入框部分 */}
        <InputWrapper name={extra?.valueKeys?.at(1)} style={{ width: '100%' }}>
          <Input
            value={inputValue}
            placeholder={placeholder?.at(1) || '请输入'}
            allowClear
            onChange={(e) => {
              const newInputValue = e.target.value;

              // 只调用外部onChange，不主动设置表单值
              if (extra?.form && extra?.valueKeys?.at(1)) {
                extra.form.setFieldValue(
                  extra?.valueKeys?.at(1),
                  newInputValue,
                );
                onChange?.([selectValue, newInputValue]);
              } else {
                onChangeValueKeys?.({
                  selectValue: selectValue || '',
                  inputValue: newInputValue,
                });
              }
            }}
            status={
              status ??
              (requiredStatus
                ? inputValue
                  ? ''
                  : 'error'
                : ('' as '' | 'error' | 'warning'))
            }
          />
        </InputWrapper>
      </Space>
    </div>
  );
};

export default SelectTextInput;
