import { useEffect } from 'react';
import { Card, Tabs } from 'antd';
import { useSafeState } from 'ahooks';
import _ from 'lodash';
import { useModel } from '@@/plugin-model/useModel';
import { UdfType } from './constants';
import EditableTableWithAddModal from './components/editableTableWithAddModal';
import AllUdfTable from './components/allUdfTable';
import {
  BackendHttpColumns,
  BackendPythonColumns,
  BackendSqlColumns,
} from './columns';
import { HttpFormItems, SqlFormItems } from './formItems';

const BackendReport = () => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  const [pythonColumns, setPythonColumns] = useSafeState([]);
  const [httpColumns, setHttpColumns] = useSafeState([]);
  const [sqlColumns, setSqlColumns] = useSafeState([]);

  const [activeKey, setActiveKey] = useSafeState('0');

  const [httpFormItems, setHttpFormItems] = useSafeState([]); // 特殊处理
  const [sqlFormItems, setSqlFormItems] = useSafeState([]); // 特殊处理

  useEffect(() => {
    setHttpFormItems(HttpFormItems(dictData?.UdfTriggers));
    setSqlFormItems(SqlFormItems(dictData?.UdfTriggers));
    setPythonColumns(BackendPythonColumns(dictData?.UdfTriggers));
    setHttpColumns(BackendHttpColumns(dictData?.UdfTriggers));
    setSqlColumns(BackendSqlColumns(dictData?.UdfTriggers));
  }, [dictData?.UdfTriggers]);

  return (
    <Card title="Udf管理">
      <Tabs
        activeKey={activeKey}
        onTabClick={(key) => setActiveKey(key)}
        items={[
          {
            key: '0',
            label: '全部',
            children: <AllUdfTable />,
          },
          {
            key: '1',
            label: UdfType.Python,
            children: (
              <>
                <EditableTableWithAddModal
                  udfType={UdfType.Python}
                  columns={pythonColumns}
                  formItems={null}
                />
              </>
            ),
          },
          {
            key: '2',
            label: UdfType.Http,
            children: (
              <EditableTableWithAddModal
                udfType={UdfType.Http}
                columns={httpColumns}
                formItems={httpFormItems}
              />
            ),
          },
          {
            key: '3',
            label: UdfType.Sql,
            children: (
              <EditableTableWithAddModal
                udfType={UdfType.Sql}
                columns={sqlColumns}
                formItems={sqlFormItems}
              />
            ),
          },
        ]}
      />
    </Card>
  );
};
export default BackendReport;
