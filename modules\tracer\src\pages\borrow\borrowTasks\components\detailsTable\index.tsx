import React, { useEffect, useState } from 'react';
import { UniTable } from '@uni/components/src';
import { useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { uniCommonService } from '@uni/services/src';
import { Button, Divider, message, Modal, Space } from 'antd';
import { DetailsTableColumns } from './columns';
import { Emitter } from '@uni/utils/src/emitter';
import { EVENT_BORROW_TASK } from '../../constants';
import dayjs from 'dayjs';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';
import { BorrowTaskColumns } from '../../columns';
import { handleMrActionApi } from '@/utils/widgets';

const DetailsTable = ({ printReqLoading }) => {
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);
  const [tableColumns, setTableColumns] = useState([]);
  const [tableData, setTableData] = useState([]);

  const { loading: getTableDataLoading, run: getTableDataReq } = useRequest(
    (data) => {
      return uniCommonService(
        'Api/Mr/BorrowApplication/GetApplicationWithRecords',
        {
          method: 'POST',
          data,
        },
      );
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res.code === 0) {
          setTableData(res?.data);
        } else {
          setTableData([]);
        }
      },
    },
  );

  const { run: getTableColumnsReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Mr/BorrowApplication/GetApplicationWithRecords',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0) {
          setTableColumns(
            tableColumnBaseProcessor(
              DetailsTableColumns,
              response?.data?.Columns || [],
            ),
          );
        } else {
          setTableColumns([]);
        }
      },
    },
  );

  // 病案室：归还
  const { run: returnReq } = useRequest(
    (record) => {
      console.log('returnReq', record);
      return uniCommonService('Api/Mr/Borrowing/Return', {
        method: 'POST',
        requestType: 'json',
        data: {
          BarCode: record?.BarCode,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          // StatusCode 黄神的单独处理
          let result = handleMrActionApi(response.data);
          result?.isCorrect
            ? message.success('归还成功')
            : message.error(result?.errMsg?.join('。/n'));

          getTableDataReq({ ApplicationId: params?.at(0)?.ApplicationId });
        }
      },
    },
  );

  useEffect(() => {
    // 注册事件监听
    Emitter.on(EVENT_BORROW_TASK.VIEW_DETAILS, (record) => {
      setCurrentRecord(record);
      setDetailsModalVisible(true);
      if (record?.Id) {
        getTableColumnsReq();
        getTableDataReq({ ApplicationId: record?.Id });
      }
    });

    Emitter.on(EVENT_BORROW_TASK.SINGLE_RETURN, (record) => {
      returnReq(record);
    });

    // 清理事件监听
    return () => {
      Emitter.off(EVENT_BORROW_TASK.VIEW_DETAILS);
      Emitter.off(EVENT_BORROW_TASK.SINGLE_RETURN);
    };
  }, [currentRecord]);

  // 拦截columns点击导致的sorter变化
  useEffect(() => {
    if (!tableColumns?.length || !tableData?.length) return;

    let tableHeaders = null;
    const handleHeaderClick = (e) => {
      console.log('qqqqqq', handleHeaderClick);
      e.stopPropagation();
      e.preventDefault();
      return false;
    };

    // 给个容错时间 内部的rendercolumns处理时间
    setTimeout(() => {
      tableHeaders = document.querySelectorAll(
        '#trace_borrow_tasks_detail_table .ant-table-thead th',
      );

      console.log('tableHeaders', tableHeaders);

      tableHeaders.forEach((header) => {
        header.addEventListener('click', handleHeaderClick, true); // 使用捕获阶段
      });
    }, 500);

    return () => {
      tableHeaders.forEach((header) => {
        header.removeEventListener('click', handleHeaderClick, true);
      });
    };
  }, [tableColumns, tableData]);

  return (
    <Modal
      title={
        <Space>
          <span>{`借阅人：${currentRecord?.BorrowerName || ''} 借阅日期：${
            dayjs(currentRecord?.BorrowDate)?.format('YYYY-MM-DD') || ''
          }`}</span>
          <Divider type="vertical" />
          <TableColumnEditButton
            {...{
              columnInterfaceUrl:
                'Api/Mr/BorrowApplication/GetApplicationWithRecords',
              onTableRowSaveSuccess: (newColumns) => {
                setTableColumns(
                  tableColumnBaseProcessor(DetailsTableColumns, newColumns),
                );
              },
            }}
          />
        </Space>
      }
      open={detailsModalVisible}
      footer={[
        ['10'].includes(currentRecord?.Status) && (
          <Button
            key="print"
            onClick={() => {
              Emitter.emit(EVENT_BORROW_TASK.PRINT_APPLICATION, currentRecord);
            }}
            loading={printReqLoading}
          >
            打印
          </Button>
        ),
        <Button
          key="submit"
          onClick={() => {
            setDetailsModalVisible(false);
          }}
        >
          确定
        </Button>,
      ]}
      cancelButtonProps={{
        style: { display: 'none' },
      }}
      okType={'default'}
      onCancel={() => {
        setDetailsModalVisible(false);
      }}
      onOk={() => {
        setDetailsModalVisible(false);
      }}
      width={1000}
    >
      <UniTable
        id="trace_borrow_tasks_detail_table"
        rowKey="PatNo"
        forceColumnsUpdate={true}
        loading={getTableDataLoading}
        columns={tableColumns?.map((d) => ({
          ...d,
          defaultSortOrder:
            d?.orderable && d?.orderPriority !== 0 && d?.orderMode
              ? d?.orderMode
              : null,
          sorter: false,
          orderable: false,
          orderPriority: 0,
          orderMode: null,
          showSorterTooltip: false,
        }))}
        dataSource={tableData}
        scroll={{ x: 'max-content' }}
      />
    </Modal>
  );
};

export default DetailsTable;
