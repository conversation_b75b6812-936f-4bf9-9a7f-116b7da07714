import { createToken, Lex<PERSON> } from 'chevrotain';

const WhiteSpace = createToken({
  name: 'WhiteSpace',
  pattern: /\s+/,
  group: Lexer.SKIPPED,
});

const Identifier = createToken({
  name: 'Identifier',
  pattern: /[a-zA-Z_\u4e00-\u9eff][\w\u4e00-\u9eff]*/,
});

const Keyword = createToken({
  name: 'Keyword',
  pattern: Lexer.NA,
  longer_alt: Identifier,
});

const Operator = createToken({
  name: 'Operator',
  pattern: Lexer.NA,
  longer_alt: Identifier,
});

const Conjunction = createToken({
  name: 'Conjunction',
  pattern: Lexer.NA,
  longer_alt: Identifier,
});

const LSquare = createToken({ name: 'LSquare', pattern: /\[/ });

const RSquare = createToken({ name: 'RSquare', pattern: /]/ });

const LParenthesis = createToken({ name: 'LParenthesis', pattern: /\(/ });

const RParenthesis = createToken({ name: 'RParenthesis', pattern: /\)/ });

const Comma = createToken({ name: 'Comma', pattern: /,/ });

const Dot = createToken({ name: 'Dot', pattern: /\./ });

const BooleanLiteral = createToken({
  name: 'BooleanLiteral',
  pattern: Lexer.NA,
  longer_alt: Identifier,
});

const TrueLiteral = createToken({
  name: 'TrueLiteral',
  pattern: /true/,
  categories: BooleanLiteral,
});

const FalseLiteral = createToken({
  name: 'FalseLiteral',
  pattern: /false/,
  categories: BooleanLiteral,
});

const NumberLiteral = createToken({
  name: 'Number',
  pattern: /\-?\d+\.?\d*/,
});

const StringLiteral = createToken({
  name: 'StringLiteral',
  pattern: /"(?:[^\\"]|\\.)*"/,
});

const Equal = createToken({
  name: 'Equal',
  pattern: /equal/,
  categories: Operator,
});

const NotEqual = createToken({
  name: 'NotEqual',
  pattern: /not_equal/,
  categories: Operator,
});

const Less = createToken({
  name: 'Less',
  pattern: /less/,
  categories: Operator,
});

const LessOrEqual = createToken({
  name: 'LessOrEqual',
  pattern: /less_or_equal/,
  categories: Operator,
});

const Greater = createToken({
  name: 'Greater',
  pattern: /greater/,
  categories: Operator,
});

const GreaterOrEqual = createToken({
  name: 'GreaterOrEqual',
  pattern: /greater_or_equal/,
  categories: Operator,
});

const Like = createToken({
  name: 'Like',
  pattern: /like/,
  categories: Operator,
});

const NotLike = createToken({
  name: 'NotLike',
  pattern: /not_like/,
  categories: Operator,
});

const StartsWith = createToken({
  name: 'StartsWith',
  pattern: /starts_with/,
  categories: Operator,
});

const EndsWith = createToken({
  name: 'EndsWith',
  pattern: /ends_with/,
  categories: Operator,
});

const Between = createToken({
  name: 'Between',
  pattern: /between/,
  categories: Operator,
});

const NotBetween = createToken({
  name: 'NotBetween',
  pattern: /not_between/,
  categories: Operator,
});

const IsNull = createToken({
  name: 'IsNull',
  pattern: /is_null/,
  categories: Operator,
});

const IsNotNull = createToken({
  name: 'IsNotNull',
  pattern: /is_not_null/,
  categories: Operator,
});

const SelectAnyIn = createToken({
  name: 'SelectAnyIn',
  pattern: /select_any_in/,
  categories: Operator,
});

const NotSelectAnyIn = createToken({
  name: 'NotSelectAnyIn',
  pattern: /not_select_any_in/,
  categories: Operator,
});

const Some = createToken({
  name: 'Some',
  pattern: /some/,
  categories: Keyword,
});

const And = createToken({
  name: 'And',
  pattern: /and/,
  categories: Conjunction,
});

const Or = createToken({ name: 'Or', pattern: /or/, categories: Conjunction });

const Not = createToken({
  name: 'Not',
  pattern: /not/,
  categories: Keyword,
});

const allTokens = [
  WhiteSpace,

  LSquare,
  RSquare,
  LParenthesis,
  RParenthesis,
  Comma,
  Dot,
  BooleanLiteral,
  TrueLiteral,
  FalseLiteral,
  Equal,
  NotEqual,
  LessOrEqual,
  Less,
  GreaterOrEqual,
  Greater,
  Like,
  NotLike,
  StartsWith,
  EndsWith,
  Between,
  NotBetween,
  IsNull,
  IsNotNull,
  SelectAnyIn,
  NotSelectAnyIn,
  Some,
  And,
  Or,
  Not,

  Keyword,
  Operator,
  Conjunction,

  Identifier,
  NumberLiteral,
  StringLiteral,
];

export {
  WhiteSpace,
  Keyword,
  Operator,
  Conjunction,
  LSquare,
  RSquare,
  LParenthesis,
  RParenthesis,
  Comma,
  Dot,
  BooleanLiteral,
  TrueLiteral,
  FalseLiteral,
  NumberLiteral,
  StringLiteral,
  Identifier,
  Equal,
  NotEqual,
  Less,
  LessOrEqual,
  Greater,
  GreaterOrEqual,
  Like,
  NotLike,
  StartsWith,
  EndsWith,
  Between,
  NotBetween,
  IsNull,
  IsNotNull,
  SelectAnyIn,
  NotSelectAnyIn,
  Some,
  And,
  Or,
  Not,
  allTokens,
};
