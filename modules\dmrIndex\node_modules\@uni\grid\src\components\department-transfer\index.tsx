import React, { ReactNode, useEffect, useState } from 'react';
import './index.less';
import {
  Form,
  InputNumber,
  Modal,
  notification,
  Space,
  Tag,
  Tooltip,
} from 'antd';
import { departmentTransferColumns } from '../../common/columns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { tableHotKeys } from '../../common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '../../utils';
import dayjs from 'dayjs';
import isEqual from 'lodash/isEqual';
import { isEmptyValues } from '@uni/utils/src/utils';
import UniEditableTable from '@uni/components/src/table/edittable';
import cloneDeep from 'lodash/cloneDeep';
import Overflow from 'rc-overflow';
//@ts-ignore
import { useModel } from '@@/plugin-model/useModel';
import { v4 as uuidv4 } from 'uuid';
import GridItemContext from '@uni/commons/src/grid-context';
import { UniDmrDragEditOnlyTable } from '@uni/components/src';
import { mergeColumnsInDepartmentTransferTable } from './utils';
import { useAsyncEffect } from 'ahooks';

interface DepartmentTransferInputProps {
  index: number;
  disabled?: boolean;

  formKey: string;
  form?: any;

  value?: any;
  onChange?: (value: any) => void;
}

export const showNotification = (description: string | ReactNode) => {
  let key = uuidv4();
  notification.warning({
    message: '转科表格数据校验不通过',
    description: description,
    key: key,
  });
};

export const DepartmentTransferFieldInput = (
  props: DepartmentTransferInputProps,
) => {
  const [value, setValue] = useState(props?.value);

  const formValue = Form.useWatch(props?.formKey, props?.form);

  useEffect(() => {
    setValue(formValue ?? props?.value);
  }, [formValue, props?.value]);

  return (
    <InputNumber
      id={`formItem#${props?.formKey}#${props?.index}#departmentTransferTable`}
      className={'department-transfer-input'}
      bordered={true}
      value={props?.value}
      min={1}
      precision={0}
      controls={false}
      keyboard={false}
      contentEditable={true}
      disabled={props?.disabled ?? false}
      onChange={(event) => {
        props?.onChange && props?.onChange(event);
      }}
      // onChange={(event) => {
      //   setValue(event);
      // }}
    />
  );
};

interface DepartmentTransferProps {
  form: any;

  formKey?: string;

  onChange?: (value) => any;
}

interface DepartmentTransferTableProps {
  form: any;
  formKey?: string;
  open: boolean;

  onChange?: (value) => any;
  columns?: any[];
}

const hotKeyToEvents = {
  ADD: (event) => {
    Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER, event.target.id);
  },
  SAVE: (event) => {
    Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER_OK, event);
  },
  DELETE: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER_DELETE, index);
    }
  },
  SCROLL_UP: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#department-transfer'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: -50,
      behavior: 'smooth',
    });
  },
  SCROLL_DOWN: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#department-transfer'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: 50,
      behavior: 'smooth',
    });
  },
  UP: (event) => {
    Emitter.emit(getArrowUpDownEventKey('transferTable'), {
      event: event,
      type: 'UP',
      trigger: 'hotkey',
    });
  },
  DOWN: (event) => {
    console.log('DOWN', event);
    Emitter.emit(getArrowUpDownEventKey('transferTable'), {
      event: event,
      type: 'DOWN',
      trigger: 'hotkey',
    });
  },
  LINE_UP: (event) => {},
  LINE_DOWN: (event) => {},
};

const DepartmentTransferTable = (props: DepartmentTransferTableProps) => {
  const [form] = Form.useForm();

  const okStatusRef = React.useRef(false);

  const departmentTransferDataSource =
    Form.useWatch('departmentTransferTable', props?.form) ?? [];

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    departmentTransferDataSource?.length,
  );

  const [_, setForceUpdate] = useState<number>(0);

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  const context = React.useContext(GridItemContext);

  const tableOnlyAddIconTrigger =
    context?.externalConfig?.tableOnlyAddIconTrigger ?? false;

  useEffect(() => {
    let columns = mergeColumnsInDepartmentTransferTable(
      context?.configurableDataIndex,
      props?.columns,
      departmentTransferColumns(tableOnlyAddIconTrigger),
    );

    setTableColumns(columns);
  }, [props?.columns]);

  useEffect(() => {
    if (props?.open === true) {
      okStatusRef.current = false;
    }
  }, [props?.open]);

  const onTableDataSourceEmpty = (currentDataSource: any[]) => {
    if (currentDataSource?.length === 0 && okStatusRef?.current === false) {
      let addItem = {
        Id: Math.round(Date.now() / 1000),
      };

      if (props?.form?.getFieldValue('InDept')) {
        addItem['OutCliDept'] = props?.form?.getFieldValue('InDept');
      }

      if (props?.form?.getFieldValue('InDate')) {
        if (
          tableColumns?.filter(
            (item) =>
              item?.dataIndex === 'TransferInDate' && item?.visible === true,
          )?.length > 0
        ) {
          addItem['TransferInDate'] = props?.form
            ?.getFieldValue('InDate')
            .slice(0, 10);
        }

        if (
          tableColumns?.filter(
            (item) =>
              item?.dataIndex === 'TransferOutDate' && item?.visible === true,
          )?.length > 0
        ) {
          addItem['TransferOutDate'] = props?.form
            ?.getFieldValue('InDate')
            .slice(0, 10);
        }

        addItem['InDeptHours'] = 1;
      }

      if (addItem?.['OutCliDept']) {
        setWaitFocusId(`formItem#InCliDept#0#Input#departmentTransferTable`);
      } else {
        setWaitFocusId(`formItem#OutCliDept#0#Input#departmentTransferTable`);
      }
      currentDataSource.push(addItem);
    }
  };

  useEffect(() => {
    // 当为空的时候 沿用原有新增的逻辑
    onTableDataSourceEmpty(departmentTransferDataSource);
    let departmentTransferTableOtherSource = props?.form?.getFieldValue(
      'department-transfer-table',
    );
    if (
      !isEqual(departmentTransferDataSource, departmentTransferTableOtherSource)
    ) {
      props?.form?.setFieldValue(
        'department-transfer-table',
        cloneDeep(departmentTransferDataSource),
      );
      setTableDataSourceSize(0);
    }
    setTableDataSourceSize(departmentTransferDataSource?.length);
    setForceUpdate((value) => value + 1);
  }, [departmentTransferDataSource, tableColumns]);

  const inDateDaysCalculator = (data: any, tableData: any[]) => {
    if (data.key === 'TransferInDate') {
      if (data?.index === 0) {
        if (
          tableData?.at(data?.index)?.['TransferInDate'] &&
          props?.form.getFieldValue('InDate')
        ) {
          let currentDate = dayjs(
            tableData?.at(data?.index)?.['TransferInDate'],
          );
          let previousDate = dayjs(props?.form.getFieldValue('InDate'));
          if (currentDate?.isValid() && previousDate?.isValid()) {
            let diffDay = currentDate
              .startOf('day')
              .diff(previousDate.startOf('day'), 'day');
            tableData[data?.index]['InDeptHours'] =
              diffDay > 0 ? diffDay : diffDay === 0 ? 1 : 0;
          }
        }
      } else {
        if (
          tableData?.at(data?.index)?.['TransferInDate'] &&
          tableData?.at(data?.index - 1)?.['TransferInDate']
        ) {
          let currentDate = dayjs(
            tableData?.at(data?.index)?.['TransferInDate'],
          );
          let previousDate = dayjs(
            tableData?.at(data?.index - 1)?.['TransferInDate'],
          );
          if (currentDate?.isValid() && previousDate?.isValid()) {
            let diffDay = currentDate
              .startOf('day')
              .diff(previousDate.startOf('day'), 'day');
            tableData[data?.index - 1]['InDeptHours'] =
              diffDay > 0 ? diffDay : diffDay === 0 ? 1 : 0;
          }
        }
      }
    }
  };

  const outDateDaysCalculator = (data: any, tableData: any[]) => {
    if (data.key === 'TransferOutDate') {
      if (data?.index === 0) {
        if (
          tableData?.at(data?.index)?.['TransferOutDate'] &&
          props?.form.getFieldValue('InDate')
        ) {
          let currentDate = dayjs(
            tableData?.at(data?.index)?.['TransferOutDate'],
          );
          let previousDate = dayjs(props?.form.getFieldValue('InDate'));
          if (currentDate?.isValid() && previousDate?.isValid()) {
            let diffDay = currentDate
              .startOf('day')
              .diff(previousDate.startOf('day'), 'day');
            tableData[data?.index]['InDeptHours'] =
              diffDay > 0 ? diffDay : diffDay === 0 ? 1 : 0;
          }
        }
      } else {
        if (
          tableData?.at(data?.index)?.['TransferOutDate'] &&
          tableData?.at(data?.index - 1)?.['TransferOutDate']
        ) {
          let currentDate = dayjs(
            tableData?.at(data?.index)?.['TransferOutDate'],
          );
          let previousDate = dayjs(
            tableData?.at(data?.index - 1)?.['TransferOutDate'],
          );
          if (currentDate?.isValid() && previousDate?.isValid()) {
            let diffDay = currentDate
              .startOf('day')
              .diff(previousDate.startOf('day'), 'day');
            tableData[data?.index]['InDeptHours'] =
              diffDay > 0 ? diffDay : diffDay === 0 ? 1 : 0;
          }
        }
      }
    }
  };

  useEffect(() => {
    setTimeout(() => {
      waitFocusElementRefocus(waitFocusId);
    }, 100);
  }, [waitFocusId]);

  useEffect(() => {
    Emitter.on(getDeletePressEventKey('transferTable'), (itemId) => {
      // key 包含 index 和 其他的东西
      console.log('department-transfer', itemId);
      let itemIds = itemId?.split('#');
      let index = parseInt(itemIds?.at(2));
      let key = itemIds?.at(1);

      clearValuesByKeys([key], index);
    });

    Emitter.on(EventConstant.DMR_DEPARTMENT_TRANSFER, () => {
      let tableData = props?.form?.getFieldValue('department-transfer-table');

      let addItem = {
        Id: Math.round(Date.now() / 1000),
      };

      if (tableData[tableData.length - 1]?.InCliDept) {
        addItem['OutCliDept'] = tableData[tableData.length - 1]?.InCliDept;
      }

      tableData.splice(tableData.length, 0, addItem);

      props?.form?.setFieldValue(
        'department-transfer-table',
        cloneDeep(tableData),
      );

      if (addItem?.['OutCliDept']) {
        setWaitFocusId(
          `formItem#InCliDept#${
            tableData?.length - 2
          }#Input#departmentTransferTable`,
        );
      } else {
        setWaitFocusId(
          `formItem#OutCliDept#${
            tableData?.length - 2
          }#Input#departmentTransferTable`,
        );
      }

      setTableDataSourceSize(tableData?.length);
    });

    Emitter.on(EventConstant.DMR_DEPARTMENT_TRANSFER_INPUT_ADD, (data) => {
      let tableData = props?.form?.getFieldValue('department-transfer-table');

      tableData[data?.index][data.key] = data?.value;

      if (data.key === 'TransferInDate') {
        inDateDaysCalculator(data, tableData);
      }

      if (data.key === 'TransferOutDate') {
        outDateDaysCalculator(data, tableData);
      }

      props?.form?.setFieldValue(
        'department-transfer-table',
        cloneDeep(tableData),
      );
      setTableDataSourceSize(-1);
      requestAnimationFrame(() => {
        setTableDataSourceSize(tableData?.length);
      });
    });

    Emitter.on(EventConstant.DMR_DEPARTMENT_TRANSFER_DELETE, (index) => {
      let tableData = props?.form?.getFieldValue('department-transfer-table');
      // if (tableData?.length <= 2) {
      //   return;
      // }
      if (index > -1) {
        tableData.splice(index, 1);

        let previousId = `formItem#OutCliDept#${
          tableData?.length - 2
        }#departmentTransferTable`;

        if (tableData?.length > 1) {
          setWaitFocusId(previousId);
        } else {
          // setWaitFocusId("department-transfer-modal-content");
          (
            document
              .getElementById('department-transfer-modal-content')
              .closest('div[class*=ant-modal-wrap]') as any
          )?.focus();
        }

        props?.form?.setFieldValue(
          'department-transfer-table',
          cloneDeep(tableData),
        );

        setTableDataSourceSize(tableData?.length);
      }
    });

    // 提交
    Emitter.on(EventConstant.DMR_DEPARTMENT_TRANSFER_OK, (event) => {
      let departmentTransferDataSource = props?.form?.getFieldValue(
        'department-transfer-table',
      );
      // 显式保存
      // 当有存在说 部分字段未填 提示
      let valid = true;
      let invalidQueryKeys = [];
      let firstErrorElement = null;
      departmentTransferDataSource
        ?.filter((item) => item.id !== 'ADD')
        ?.forEach((item, index) => {
          if (
            tableColumns?.filter(
              (item) =>
                item?.dataIndex === 'OutCliDept' && item?.visible === true,
            )?.length > 0
          ) {
            if (isEmptyValues(item?.OutCliDept)) {
              valid = false;
              invalidQueryKeys.push(
                "div[class*='select-container dmr-select-container'] input[id*=OutCliDept]",
              );
            }
          }

          if (
            tableColumns?.filter(
              (item) =>
                item?.dataIndex === 'InCliDept' && item?.visible === true,
            )?.length > 0
          ) {
            if (isEmptyValues(item?.InCliDept)) {
              valid = false;
              invalidQueryKeys.push(
                "div[class*='select-container dmr-select-container'] input[id*=InCliDept]",
              );
            }
          }

          if (
            tableColumns?.filter(
              (item) =>
                item?.dataIndex === 'TransferInDate' && item?.visible === true,
            )?.length > 0
          ) {
            if (isEmptyValues(item?.TransferInDate)) {
              valid = false;
              invalidQueryKeys.push('input[id*=TransferInDate]');
            }
          }

          if (
            tableColumns?.filter(
              (item) =>
                item?.dataIndex === 'TransferOutDate' && item?.visible === true,
            )?.length > 0
          ) {
            if (isEmptyValues(item?.TransferOutDate)) {
              valid = false;
              invalidQueryKeys.push('input[id*=TransferOutDate]');
            }
          }

          if (
            tableColumns?.filter(
              (item) =>
                item?.dataIndex === 'InDeptHours' && item?.visible === true,
            )?.length > 0
          ) {
            if (isEmptyValues(item?.InDeptHours)) {
              valid = false;
              invalidQueryKeys.push('input[id*=InDeptHours]');
            }
          }

          if (invalidQueryKeys?.length > 0) {
            if (firstErrorElement === null) {
              firstErrorElement = invalidQueryKeys?.at(0);
            }
            borderMissingItem(item?.id, invalidQueryKeys);
          }
        });

      if (firstErrorElement !== null) {
        document.querySelector(firstErrorElement)?.focus();
        document.querySelector(firstErrorElement)?.click();
      }

      if (!valid) {
        showNotification('请检查转科信息表格是否都已填写');
        return;
      }

      if (valid) {
        let departmentTransfers = departmentTransferDataSource?.slice();

        triggerFormValueChangeEvent('department-transfer-table');

        // dataSource
        props?.form?.setFieldValue(
          'departmentTransferTable',
          departmentTransfers,
        );
        props?.form?.setFieldValue(
          'department-transfer-table',
          departmentTransfers,
        );

        Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER_CLOSE);
        okStatusRef.current = true;

        event['target']['id'] = 'department-transfer-container';
        Emitter.emit(EventConstant.DMR_TABLE_NEXT_KEY, {
          event: event,
          indexOffset: 1,
        });
      }
    });

    Emitter.on(getArrowUpDownEventKey('transferTable'), (payload) => {
      let type = payload?.type;

      console.log('payload', payload);

      if (
        payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
        payload?.trigger === 'hotkey'
      ) {
        // 表示是 下拉框 需要定制
        return;
      }

      payload?.event?.stopPropagation();

      let { nextIndex, activePaths } = calculateNextIndex(type);
      if (type === 'UP') {
        if (nextIndex < 0) {
          nextIndex = undefined;
        }
      }

      if (type === 'DOWN') {
        if (nextIndex > departmentTransferDataSource?.length - 1) {
          nextIndex = undefined;
        }
      }

      if (nextIndex !== undefined) {
        activePaths[2] = nextIndex.toString();
        document.getElementById(activePaths?.join('#'))?.focus();
      }
    });

    return () => {
      Emitter.off(EventConstant.DMR_DEPARTMENT_TRANSFER);
      Emitter.off(EventConstant.DMR_DEPARTMENT_TRANSFER_DELETE);
      Emitter.off(EventConstant.DMR_DEPARTMENT_TRANSFER_OK);
      Emitter.off(EventConstant.DMR_DEPARTMENT_TRANSFER_INPUT_ADD);

      Emitter.off(getDeletePressEventKey('transferTable'));
      Emitter.off(getArrowUpDownEventKey('transferTable'));
    };
  }, [tableColumns]);

  const borderMissingItem = (id: string, queryKeys: string[]) => {
    let lineElement = document.querySelector(`tr[data-row-key='${id}']`);
    if (lineElement) {
      let borderErrorElements = lineElement.querySelectorAll(
        queryKeys.join(','),
      );
      if (borderErrorElements) {
        let errorElements = [].slice.call(borderErrorElements);
        errorElements?.forEach((errorElement) => {
          errorElement?.closest('td > div')?.classList?.add('border-error');
        });
      }
    }
  };

  const clearValuesByKeys = (keys, index) => {
    const icdeDataSource = props?.form?.getFieldValue(
      'department-transfer-table',
    );
    let formItemId = icdeDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue('department-transfer-table');
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue(
      'department-transfer-table',
      cloneDeep(tableData),
    );
  };

  const itemRef = React.useRef<any>();

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(item.key, hotKeyToEvents[item?.type], {
      preventDefault: true,
      enabled: true,
      enableOnFormTags: true,
      enableOnContentEditable: true,
    });
  });

  return (
    <div
      ref={mergeRefs([itemRef, ...hotKeyRefs])}
      id={'department-transfer-modal-content'}
    >
      <UniDmrDragEditOnlyTable
        {...props}
        formItemContainerClassName={'form-content-item-container'}
        itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
        form={form}
        key={'department-transfer'}
        id={'department-transfer'}
        tableId={'departmentTransferTable'}
        formKey={'departmentTransferTable'}
        scroll={{
          x: 'max-content',
          y: 376,
        }}
        pagination={false}
        className={`table-container`}
        dataSource={(
          props?.form?.getFieldValue('department-transfer-table') ?? []
        )
          ?.filter((item) => item?.id !== 'ADD')
          ?.map((item) => {
            if (!item['id']) {
              item['id'] = Math.round(Date.now() / 1000);
            }

            return item;
          })
          ?.concat({
            id: 'ADD',
          })}
        rowKey={'id'}
        onValuesChange={(recordList, changedValues) => {
          props?.form?.setFieldValue('department-transfer-table', recordList);
          triggerFormValueChangeEvent('department-transfer-table');
        }}
        onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
          props?.form?.setFieldValue(
            'department-transfer-table',
            cloneDeep(tableData),
          );
          if (focusId) {
            setTimeout(() => {
              waitFocusElementRefocus(focusId);
            }, 100);
          }
        }}
        columns={tableColumns}
      />

      {/*<UniEditableTable*/}
      {/*  id={'department-transfer'}*/}
      {/*  size={'small'}*/}
      {/*  rowKey={'Id'}*/}
      {/*  scroll={{ y: 330 }}*/}
      {/*  pagination={false}*/}
      {/*  columns={tableColumns}*/}
      {/*  value={departmentTransferDataSource}*/}
      {/*  bordered={true}*/}
      {/*  clickable={false}*/}
      {/*  recordCreatorProps={false}*/}
      {/*  editable={{*/}
      {/*    form: form,*/}
      {/*    type: 'multiple',*/}
      {/*    editableKeys,*/}
      {/*    actionRender: (row, config, defaultDoms) => {*/}
      {/*      return [];*/}
      {/*    },*/}
      {/*    onValuesChange: (record, recordList) => {*/}
      {/*      console.error('table onValuesChange', record, recordList);*/}
      {/*    },*/}
      {/*    onChange: setEditableRowKeys,*/}
      {/*  }}*/}
      {/*/>*/}
    </div>
  );
};

export const DepartmentTransfer = (props: DepartmentTransferProps) => {
  const [departmentTransferAdd, setDepartmentTransferAdd] = useState(false);

  const departmentTransferDataSource =
    Form.useWatch('departmentTransferTable', props?.form) ?? [];

  useEffect(() => {
    Emitter.on(EventConstant.DMR_DEPARTMENT_TRANSFER_CLOSE, () => {
      setDepartmentTransferAdd(false);
    });
    return () => {
      Emitter.off(EventConstant.DMR_DEPARTMENT_TRANSFER_CLOSE);
    };
  }, []);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const renderItem = (item: any) => {
    return <Tag>{item.Name}</Tag>;
  };

  const renderRest = (items: any[]) => {
    return (
      <Tooltip
        title={<Space split=",">{items.map((item) => item.Name)}</Space>}
        getPopupContainer={(triggerNode) =>
          document.getElementById('dmr-content-grid-layout')
        }
      >
        <Tag>+{items.length}...</Tag>
      </Tooltip>
    );
  };

  const overFlowDataProcessor = () => {
    let selectedData =
      departmentTransferDataSource
        ?.filter((item) => !!item.InCliDept)
        ?.map((item) => {
          return item.InCliDept;
        }) ?? [];

    const modelData = globalState?.dictData?.['CliDepts'];
    let cliDeptNames = [];
    if (!isEmptyValues(modelData) && !isEmptyValues(selectedData)) {
      selectedData?.forEach((selectedItem) => {
        let cliDeptItem = modelData?.find(
          (item) => item?.Code === selectedItem,
        );
        if (cliDeptItem) {
          cliDeptNames.push(cliDeptItem);
        }
      });
    }

    return cliDeptNames;
  };

  return (
    <div className={'department-transfer-container'}>
      <div
        id={'department-transfer-container'}
        className={'tags-container'}
        onClick={() => {
          setDepartmentTransferAdd(true);
        }}
      >
        <Overflow
          data={overFlowDataProcessor()}
          renderItem={renderItem}
          renderRest={renderRest}
          maxCount={'responsive'}
        />
      </div>

      <Modal
        title="转科信息"
        open={departmentTransferAdd}
        width={1000}
        okText={'保存'}
        maskClosable={false}
        cancelButtonProps={{
          style: {
            display: 'none',
          },
        }}
        onOk={(event) => {
          Emitter.emit(EventConstant.DMR_DEPARTMENT_TRANSFER_OK, event);
        }}
        onCancel={(event) => {
          setDepartmentTransferAdd(false);
          event['target']['id'] = 'department-transfer-container';
          Emitter.emit(EventConstant.DMR_TABLE_NEXT_KEY, {
            event: event,
            indexOffset: 1,
          });
        }}
        className={'department-transfer-modal-container'}
        destroyOnClose={true}
        getContainer={document.getElementById('dmr-content-grid-layout')}
      >
        <DepartmentTransferTable {...props} open={departmentTransferAdd} />
      </Modal>
    </div>
  );
};
