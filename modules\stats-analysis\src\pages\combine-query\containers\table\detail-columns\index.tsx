import './index.less';
import {
  Anchor,
  Button,
  Modal,
  Switch,
  Tooltip,
  Tag,
  Form,
  Collapse,
} from 'antd';
import React, {
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import Search from 'antd/es/input/Search';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { ColumnItem } from '@uni/commons/src/interfaces';
import {
  pinyinInitialSearch,
  searchFunctionGetter,
} from '@uni/utils/src/pinyin';
import { cloneDeep } from 'lodash';
import { Emitter } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { UniDragEditTable } from '@uni/components/src';
import { columnSettingColumns } from '@/pages/combine-query/containers/table/columns';
import { arrayMoveImmutable } from '@/pages/combine-query/utils';
import _ from 'lodash';
import { isEmptyValues } from '@uni/utils/src/utils';
import groupBy from 'lodash/groupBy';

const { CheckableTag } = Tag;

const externalStatsAnalysisConfig = (window as any).externalConfig?.[
  'statsAnalysis'
];

const tagBaseStyle = externalStatsAnalysisConfig?.['tagBaseStyle'] ?? {};

interface CombineQueryDetailColumnSettingsProps {
  nextGeneration: boolean;
  columnsMap?: any;
  columns: ColumnItem[];
  treeData?: any[];
  onColumnSelect: (columnMap) => void;
  onDefaultColumnSelect?: (selectedKeys: string[]) => void;

  extraData?: any;
  columnTreeContainerRef?: any;
}

const CombineQueryDetailColumnSettings = (
  props: CombineQueryDetailColumnSettingsProps,
) => {
  const [form] = Form.useForm();

  const [
    combineQueryDetailColumnSettingModalVisible,
    setCombineQueryDetailColumnSettingModalVisible,
  ] = React.useState(false);

  const [
    combineQueryDetailColumnSettingModalExtraData,
    setCombineQueryDetailColumnSettingModalExtraData,
  ] = React.useState<any>({});

  const columnTreeContainerRef = useRef(null);

  const mergedColumnsMap = !isEmptyValues(
    combineQueryDetailColumnSettingModalExtraData?.columnsMap,
  )
    ? combineQueryDetailColumnSettingModalExtraData?.columnsMap
    : props?.columnsMap;

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_COLUMN_CUSTOMIZER_CHANGE,
      (data) => {
        setCombineQueryDetailColumnSettingModalVisible(data?.status);
        setCombineQueryDetailColumnSettingModalExtraData(data?.extraData);
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_COLUMN_CUSTOMIZER_CHANGE,
      );
    };
  }, []);

  const columnsTreeProcessor = () => {
    let columnsTree = [];

    props?.columns?.forEach((columnItem) => {
      if (columnItem?.directories) {
        const leftDirectories = columnItem?.directories?.slice(0, 1);
        leftDirectories?.forEach((directoryItem, index) => {
          let directoryColumnTreeItem = columnsTree?.find(
            (item) => item?.title === directoryItem,
          );
          if (directoryColumnTreeItem === undefined) {
            directoryColumnTreeItem = {
              title: directoryItem,
              key: directoryItem,
              directory: true,
              directorySort: columnItem?.directorySort ?? 0,
            };
            if (index === 0) {
              columnsTree.push(directoryColumnTreeItem);
            } else {
              // 上级 文件夹
              let parentDirectoryColumnTreeItem = columnsTree?.find(
                (item) => item?.title === leftDirectories[index - 1],
              );
              if (parentDirectoryColumnTreeItem) {
                parentDirectoryColumnTreeItem['children'] = [
                  ...(parentDirectoryColumnTreeItem['children'] ?? []),
                  directoryColumnTreeItem,
                ];
              }
            }
          }

          if (index === leftDirectories?.length - 1) {
            directoryColumnTreeItem['directorySort'] = Math.max(
              directoryColumnTreeItem['directorySort'],
              columnItem?.directorySort,
            );
            directoryColumnTreeItem['children'] = [
              ...(directoryColumnTreeItem['children'] ?? []),
              {
                title: columnItem?.title ?? columnItem?.originTitle,
                originTitle: columnItem?.originTitle,
                key: columnItem?.name,
                directory: false,
                directories: columnItem?.directories,
                ...(mergedColumnsMap?.[columnItem?.name] ?? {}),
                columnSequence: columnItem?.columnSequence ?? 0,
              },
            ]?.sort((a, b) => a?.columnSequence - b?.columnSequence);
          }
        });
      } else {
        columnsTree.push({
          title: columnItem?.title ?? columnItem?.originTitle,
          originTitle: columnItem?.originTitle,
          key: columnItem?.name,
          directory: false,
          ...(mergedColumnsMap?.[columnItem?.name] ?? {}),

          directorySort:
            columnItem?.directorySort ?? columnItem?.columnSequence ?? 0,
          columnSequence: columnItem?.columnSequence ?? 0,
        });
      }
    });

    console.log('columnsTree', columnsTree);

    return columnsTree?.sort((a, b) => a?.directorySort - b?.directorySort);
  };

  // useEffect(() => {
  //   columnsTreeProcessor()
  // }, [props?.columns]);

  const columnMapProcessor = (selectedItems) => {
    // reset columnMap
    let currentColumnMap = Object.assign({}, mergedColumnsMap);
    Object.keys(currentColumnMap)?.forEach((item) => {
      currentColumnMap[item]['show'] = false;
    });
    selectedItems?.forEach((item) => {
      if (item?.key) {
        if (currentColumnMap?.[item?.key]) {
          currentColumnMap[item?.key]['show'] = true;
          currentColumnMap[item?.key]['order'] = item?.order ?? 0;

          currentColumnMap[item?.key]['textOverflowType'] =
            item?.textOverflowType ?? 'none';
        }
      }

      // 自定义标题
      if (item?.customTitle) {
        if (item?.title !== item?.customTitle) {
          Emitter.emit('COLUMN_SETTING_TITLE_EDIT', {
            columnKey: item?.key,
            title: item?.customTitle,
          });
        }
      }
    });

    console.log('currentColumnMap', currentColumnMap);
    // 'COLUMN_SETTING_TITLE_EDIT' 会改变外部detailColumns导致列被初始化 做个setTimeout容错
    // TODO 修改COLUMN_SETTING_TITLE_EDIT的写法
    setTimeout(() => {
      props?.onColumnSelect && props?.onColumnSelect(currentColumnMap);
    }, 0);
  };

  return (
    <div
      id={'combine-query-column-setting-container'}
      className={'combine-query-column-setting-container'}
    >
      {props?.nextGeneration === false && (
        <Tooltip title={'明细列设置'}>
          <Button
            key="table_column_setting"
            onClick={() => {
              setCombineQueryDetailColumnSettingModalVisible(
                !combineQueryDetailColumnSettingModalVisible,
              );
            }}
          >
            明细列设置
          </Button>
        </Tooltip>
      )}

      <Modal
        title={
          combineQueryDetailColumnSettingModalExtraData?.type ===
          'DEFAULT_COLUMN_EDIT'
            ? `常用列设置`
            : `明细列设置`
        }
        width={
          combineQueryDetailColumnSettingModalExtraData?.type ===
          'DEFAULT_COLUMN_EDIT'
            ? 800
            : 1600
        }
        open={combineQueryDetailColumnSettingModalVisible}
        className={'detail-columns-setting-container'}
        destroyOnClose={true}
        closable={true}
        onOk={() => {
          console.log(
            'form.getFieldsValue',
            form.getFieldValue('selectedItems'),
          );

          let mergedSelectedItems =
            form.getFieldValue('selectedItems') ??
            columnTreeContainerRef?.current?.getSelectedItems();

          columnMapProcessor(mergedSelectedItems);
          setCombineQueryDetailColumnSettingModalVisible(false);

          if (
            combineQueryDetailColumnSettingModalExtraData?.type ===
            'DEFAULT_COLUMN_EDIT'
          ) {
            props?.onDefaultColumnSelect &&
              props?.onDefaultColumnSelect?.(
                mergedSelectedItems?.map((item) => {
                  return item?.id;
                }),
              );
          }
        }}
        onCancel={() => {
          setCombineQueryDetailColumnSettingModalVisible(false);
        }}
        getContainer={() =>
          // document.getElementById('combine-query-column-setting-container')
          document.getElementById('combo-table-ng-container')
        }
      >
        <Form form={form} preserve={false}>
          <Form.Item hidden={true} name={'selectItems'} />
          <Form.Item hidden={true} name={'columnMap'} />

          <DetailColumnSettingContent
            {...props}
            columnTreeContainerRef={columnTreeContainerRef}
            columnsMap={mergedColumnsMap}
            extraData={combineQueryDetailColumnSettingModalExtraData}
            treeData={columnsTreeProcessor()}
          />
        </Form>
      </Modal>
    </div>
  );
};

const DetailColumnSettingContent = (
  props: CombineQueryDetailColumnSettingsProps,
) => {
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  useEffect(() => {
    let checkedKeys = [];
    if (props?.columnsMap) {
      Object.keys(props?.columnsMap)?.forEach((key) => {
        if (props?.columnsMap?.[key]?.show) {
          checkedKeys.push(key);
        }
      });
    }

    setCheckedKeys(checkedKeys);
  }, [props?.columnsMap]);

  return (
    <div className={'detail-columns-setting-info-container'}>
      <DetailColumnsSettingTree
        columnTreeContainerRef={props?.columnTreeContainerRef}
        treeData={props?.treeData}
        checkKeys={checkedKeys}
        extraData={props?.extraData}
      />
      {props?.extraData?.type !== 'DEFAULT_COLUMN_EDIT' && (
        <div className={'detail-columns-separator'} />
      )}

      {props?.extraData?.type !== 'DEFAULT_COLUMN_EDIT' && (
        <SelectedColumnTable treeData={props?.treeData} />
      )}
    </div>
  );
};

interface DetailColumnsSettingTreeProps {
  treeData?: any[];
  checkKeys?: string[];
  extraData?: any;
  columnTreeContainerRef: any;
}

export const DetailColumnsSettingTree = (
  props: DetailColumnsSettingTreeProps,
) => {
  const [keyword, setKeyword] = useState('');

  const [selectedAnchor, setSelectedAnchor] = useState<string>('');

  const [selectedItems, setSelectedItems] = useState<any[]>([]);

  useImperativeHandle(props?.columnTreeContainerRef, () => {
    return {
      getSelectedItems: () => {
        return selectedItems;
      },
    };
  });

  useEffect(() => {
    if (props?.checkKeys?.length > 0) {
      let selectedItems = [];
      props?.treeData?.forEach((item) => {
        item?.children?.forEach((childItem) => {
          if (
            props?.checkKeys?.find(
              (checkKey) => checkKey === childItem?.key,
            ) !== undefined
          ) {
            selectedItems.push(childItem);
          }
        });
      });

      // 初始化获取selectedItems的时候，获取完了先排序好
      setSelectedItems(selectedItems.sort((a, b) => a?.order - b?.order));
    }
  }, [props?.checkKeys]);

  useEffect(() => {
    setSelectedAnchor(props?.treeData?.at(0)?.key);
  }, [props?.treeData]);

  useEffect(() => {
    Emitter.emit(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT,
      selectedItems,
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_DESELECT,
      (item) => {
        onItemClicked(item, false);
      },
    );

    return () => {
      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_DESELECT);
    };
  }, [selectedItems]);

  const onItemClicked = (item, checked) => {
    if (checked) {
      if (
        selectedItems.find(
          (selectedItem) => selectedItem?.key === item?.key,
        ) === undefined
      ) {
        setSelectedItems([...selectedItems, item]);
      }
    } else {
      let selectedIndex = selectedItems.findIndex(
        (selectedItem) => selectedItem?.key === item?.key,
      );
      if (selectedIndex > -1) {
        selectedItems.splice(selectedIndex, 1);
        setSelectedItems(selectedItems?.slice());
      }
    }
  };

  const onItemsClicked = (items, checked) => {
    let currentItems = selectedItems?.slice();
    if (checked) {
      items?.forEach((item) => {
        if (
          currentItems.find(
            (selectedItem) => selectedItem?.key === item?.key,
          ) === undefined
        ) {
          currentItems?.push(item);
        }
      });
    } else {
      items?.forEach((item) => {
        let selectedIndex = currentItems.findIndex(
          (selectedItem) => selectedItem?.key === item?.key,
        );
        if (selectedIndex > -1) {
          currentItems.splice(selectedIndex, 1);
        }
      });
    }

    setSelectedItems(currentItems);
  };

  const elementIsVisibleInViewport = () => {
    props?.treeData
      ?.map((item) => {
        return document.getElementById(item?.key);
      })
      ?.filter((item) => item)
      ?.forEach((el) => {
        const elementRect = el.getBoundingClientRect();
        const containerRect = document
          .getElementById('detail-tree-container')
          .getBoundingClientRect();
        let visible =
          containerRect.top - (elementRect.top - 10) > 0 &&
          containerRect.top - elementRect.top <= elementRect.height;

        if (visible) {
          setSelectedAnchor(el.id);
        }
      });
  };

  return (
    <div
      className={'detail-columns-tree-container'}
      style={
        props?.extraData?.type === 'DEFAULT_COLUMN_EDIT'
          ? { width: '100%' }
          : {}
      }
    >
      <div className={'flex-row-center'} style={{ margin: '0px 20px' }}>
        <Search
          value={keyword}
          className={'detail-columns-search'}
          style={{ width: '100%' }}
          placeholder="请输入列名称"
          onChange={(event) => {
            setKeyword(event.target.value);
          }}
        />
        {props?.extraData?.type !== 'DEFAULT_COLUMN_EDIT' && (
          <Button
            className={'reset'}
            ghost={true}
            onClick={() => {
              // TODO 重置
              let selectedItems = [];
              props?.treeData?.forEach((item) => {
                item?.children?.forEach((childItem) => {
                  if (
                    props?.checkKeys?.find(
                      (checkKey) => checkKey === childItem?.key,
                    ) !== undefined
                  ) {
                    selectedItems.push(childItem);
                  }
                });
              });
              setSelectedItems(selectedItems);
              Emitter.emit(
                StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_RESET,
              );
            }}
          >
            重置
          </Button>
        )}
      </div>

      <div className={'detail-columns-tree-content'}>
        <div className="toc-affix">
          <ul id="demo-toc" className="toc">
            {props?.treeData
              ?.filter((item) => item?.children?.length > 0)
              ?.map((item) => {
                // console.log('anchor', item?.key, selectedAnchor);

                return (
                  <li key={item?.key} title={item?.title}>
                    <a
                      className={item?.key === selectedAnchor ? 'selected' : ''}
                      onClick={() => {
                        setSelectedAnchor(item?.key);
                        document.getElementById(item?.key)?.scrollIntoView({
                          behavior: 'smooth',
                        });
                      }}
                    >
                      {item?.title}
                    </a>
                  </li>
                );
              })}
          </ul>
        </div>

        <div
          id={'detail-tree-container'}
          className={'detail-tree-container'}
          onScroll={(event) => {
            let scrollToBottom =
              document
                .getElementById('detail-tree-container')
                .getBoundingClientRect()?.height +
                document.getElementById('detail-tree-container')?.scrollTop ===
              document.getElementById('detail-tree-container')?.scrollHeight;
            if (scrollToBottom) {
              setSelectedAnchor(
                props?.treeData?.at(props?.treeData?.length - 1)?.key,
              );
              return;
            }
            elementIsVisibleInViewport();
          }}
        >
          {cloneDeep(props?.treeData).map((treeItem) => {
            return (
              <TreeItem
                key={treeItem?.key}
                itemKey={treeItem?.key}
                title={treeItem?.title}
                selected={selectedItems}
                items={treeItem?.children ?? []}
                keyword={keyword}
                onItemClicked={onItemClicked}
                onItemsClicked={onItemsClicked}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

interface TreeItemProps {
  itemKey: string;
  title: string;
  selected: any[];
  items: any[];
  keyword?: string;
  onItemClicked: (item, checked) => void;
  onItemsClicked: (items, checked) => void;
}

export const TreeItem = (props: TreeItemProps) => {
  const filteredItems = props?.items
    ?.filter((item) =>
      props?.keyword
        ? item?.title?.[searchFunctionGetter(false)](props?.keyword) ||
          pinyinInitialSearch(item?.title, props?.keyword)
        : true,
    )
    ?.sort((a, b) => (a?.columnSequence ?? 0) - (b?.columnSequence ?? 0));

  const allItemKeys = filteredItems?.map((item) => item?.key);
  let checkedKeys = props?.selected?.map((item) => item?.key);

  let allChecked =
    allItemKeys.filter((v) => checkedKeys.indexOf(v) == -1)?.length === 0;
  let checkedKeysSize = checkedKeys.filter(
    (v) => allItemKeys.indexOf(v) > -1,
  )?.length;

  console.log('filteredItems', filteredItems);

  const hasDoubleDirectoriesItems = filteredItems?.filter((item) => {
    return item?.directories?.length > 1;
  });

  const doubleDirectoriesGroupedBySubDirectory = groupBy(
    hasDoubleDirectoriesItems,
    (item) => {
      return item?.directories?.slice(1)?.at(0);
    },
  );

  console.log(
    'doubleDirectoriesGroupedBySubDirectory',
    doubleDirectoriesGroupedBySubDirectory,
  );

  return (
    <>
      {filteredItems?.length > 0 && (
        <div id={props?.itemKey} className={'tree-item-container'}>
          <div className={'tree-header-container'}>
            <div className={'label-container'}>
              <span className={'title'}>{props?.title}</span>
              <span style={{ fontSize: 12, marginLeft: 3, marginBottom: 2 }}>
                <span className={'selected-number'}>{checkedKeysSize}</span>/
                {props?.items?.length}
              </span>
            </div>
            <div className={'flex-row-center'}>
              <span style={{ fontSize: 12 }}>全选</span>
              <Switch
                size={'small'}
                checkedChildren={<CheckOutlined />}
                unCheckedChildren={<CloseOutlined />}
                checked={allChecked}
                onChange={(checked) => {
                  props?.onItemsClicked(filteredItems, checked);
                }}
              />
            </div>
          </div>

          <div className={'items-container tag-container'}>
            {filteredItems
              ?.filter((item) => {
                if (isEmptyValues(props?.keyword)) {
                  return item?.directories?.length === 1;
                }

                return true;
              })
              ?.filter((item) => !isEmptyValues(item?.title))
              ?.map((tagItem) => {
                return (
                  <CheckableTag
                    style={tagBaseStyle}
                    className={
                      props?.selected?.find(
                        (item) => item?.key === tagItem?.key,
                      )
                        ? 'tag-item-checked'
                        : 'tag-item'
                    }
                    key={tagItem?.key}
                    checked={props?.selected?.find(
                      (item) => item?.key === tagItem?.key,
                    )}
                    onChange={(checked) => {
                      props.onItemClicked(tagItem, checked);
                    }}
                  >
                    <div
                      className={'flex-row-center'}
                      style={{
                        padding: props?.selected?.find(
                          (item) => item?.key === tagItem?.key,
                        )
                          ? 0
                          : '0 8px',
                      }}
                    >
                      {props?.selected?.find(
                        (item) => item?.key === tagItem?.key,
                      ) && <CheckOutlined className={'tick'} />}

                      {tagItem?.title}
                    </div>
                  </CheckableTag>
                );
              })}
          </div>
          {isEmptyValues(props?.keyword) &&
            !isEmptyValues(doubleDirectoriesGroupedBySubDirectory) && (
              <div className={'collapse-container'}>
                <Collapse bordered={false} expandIconPosition={'end'}>
                  {Object.keys(doubleDirectoriesGroupedBySubDirectory)?.map(
                    (key) => {
                      const groupKeys =
                        doubleDirectoriesGroupedBySubDirectory?.[key]?.map(
                          (item) => item?.key,
                        );
                      let currentGroupSelectedKeySize = checkedKeys.filter(
                        (v) => groupKeys.indexOf(v) > -1,
                      )?.length;

                      return (
                        <Collapse.Panel
                          header={
                            <>
                              <span className={'title'}>{key}</span>
                              <span
                                style={{
                                  fontSize: 12,
                                  marginLeft: 3,
                                  marginBottom: 2,
                                }}
                              >
                                <span className={'selected-number'}>
                                  {currentGroupSelectedKeySize}
                                </span>
                                /
                                {
                                  doubleDirectoriesGroupedBySubDirectory?.[key]
                                    ?.length
                                }
                              </span>
                            </>
                          }
                          key={key}
                        >
                          <div className={'panel-item-container tag-container'}>
                            {doubleDirectoriesGroupedBySubDirectory?.[key]?.map(
                              (tagItem) => {
                                return (
                                  <CheckableTag
                                    style={tagBaseStyle}
                                    className={
                                      props?.selected?.find(
                                        (item) => item?.key === tagItem?.key,
                                      )
                                        ? 'tag-item-checked'
                                        : 'tag-item'
                                    }
                                    key={tagItem?.key}
                                    checked={props?.selected?.find(
                                      (item) => item?.key === tagItem?.key,
                                    )}
                                    onChange={(checked) => {
                                      props.onItemClicked(tagItem, checked);
                                    }}
                                  >
                                    <div
                                      className={'flex-row-center'}
                                      style={{
                                        padding: props?.selected?.find(
                                          (item) => item?.key === tagItem?.key,
                                        )
                                          ? 0
                                          : '0 8px',
                                      }}
                                    >
                                      {props?.selected?.find(
                                        (item) => item?.key === tagItem?.key,
                                      ) && <CheckOutlined className={'tick'} />}

                                      {tagItem?.title}
                                    </div>
                                  </CheckableTag>
                                );
                              },
                            )}
                          </div>
                        </Collapse.Panel>
                      );
                    },
                  )}
                </Collapse>
              </div>
            )}
        </div>
      )}
    </>
  );
};

interface SelectedColumnTableProps {
  treeData: any[];
}

const SelectedColumnTable = (props: SelectedColumnTableProps) => {
  const [form] = Form.useForm();

  const mainFormInstance = Form.useFormInstance();

  const [selectedItems, setSelectedItems] = useState([]);
  const [selectedItemsOrder, setSelectedItemsOrder] = useState({});
  // now order
  const nowOrder = useRef(1);

  useEffect(() => {
    form.resetFields();

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_RESET,
      () => {
        form.resetFields();
      },
    );

    return () => {
      Emitter.offMultiple([
        StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_RESET,
      ]);
    };
  }, []);

  // 更新最终保存时的columns
  useEffect(() => {
    console.log('mainSet', selectedItems);
    mainFormInstance.setFieldValue('selectedItems', selectedItems);
  }, [selectedItems]);

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_TOP,
      (data) => {
        if (data?.index) {
          let currentItem = selectedItems[data?.index];
          let currentSelectedItemOrders = selectedItems?.map(
            (item) => item?.order,
          );

          if (currentItem) {
            selectedItems?.splice(data?.index, 1);

            let currentSelectedItems = [currentItem, ...selectedItems]?.map(
              (item, index) => {
                item['order'] = currentSelectedItemOrders[index];
                return item;
              },
            );
            setSelectedItems(currentSelectedItems);
            console.log(
              'STATS_ANALYSIS_COLUMN_SELECT_TOP.updateSelectedItemOrder',
              currentSelectedItems,
            );
            updateSelectedItemOrder(currentSelectedItems);
          }
        }
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_BOTTOM,
      (data) => {
        let currentItem = selectedItems[data?.index];
        let currentSelectedItemOrders = selectedItems?.map(
          (item) => item?.order,
        );

        if (currentItem) {
          selectedItems?.splice(data?.index, 1);

          let currentSelectedItems = [...selectedItems, currentItem]?.map(
            (item, index) => {
              item['order'] = currentSelectedItemOrders[index];
              return item;
            },
          );

          setSelectedItems(currentSelectedItems);
          updateSelectedItemOrder(currentSelectedItems);
        }
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_DELETE,
      (data) => {
        let currentItem = selectedItems[data?.index];
        if (currentItem) {
          Emitter.emit(
            StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_DESELECT,
            currentItem,
          );
        }
      },
    );

    return () => {
      Emitter.offMultiple([
        StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT,
        StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_TOP,
        StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_BOTTOM,
        StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT_DELETE,
      ]);
    };
  }, [selectedItems]);

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT,
      (selectItems) => {
        let items = cloneDeep(selectItems);

        items?.forEach((item) => {
          if (selectedItemsOrder[item?.name]) {
            item['order'] = selectedItemsOrder[item?.name];
          } else {
            // 新增时没有order / 默认进来的items(外面已经排过序，所以order一样)
            // 弃用 columnsSequence
            // console.log('in:', item, nowOrder.current);
            item['order'] = nowOrder.current;
            nowOrder.current += 1;
          }

          item['textOverflowType'] = item?.textOverflowType ?? 'none';
        });

        setSelectedItems(
          items
            ?.slice()
            ?.sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
            ?.slice(),
        );
        updateSelectedItemOrder(items);
      },
    );

    return () => {
      Emitter.offMultiple([
        StatsAnalysisEventConstant.STATS_ANALYSIS_COLUMN_SELECT,
      ]);
    };
  }, [selectedItemsOrder]);

  const updateSelectedItemOrder = (items: any[]) => {
    let currentSelectedItemsOrder = {};
    items?.forEach((item) => {
      currentSelectedItemsOrder[item?.name] = item?.order;
    });
    setSelectedItemsOrder(currentSelectedItemsOrder);
  };

  return (
    <div className={'selected-table-container'}>
      <UniDragEditTable
        {...props}
        bordered={false}
        form={form}
        key={'column-setting-selected-table'}
        id={'column-setting-selected-table'}
        tableId={'column-setting-selected-table'}
        scroll={{
          y: 400,
        }}
        controlled
        pagination={false}
        className={`table-container`}
        dataSource={selectedItems}
        rowKey={'id'}
        onTableDataSourceOrderChange={(tableData, oldIndex, newIndex) => {
          // 不是交换 而是把order重排 TODO: 如果用重复的order还要处理下？
          // console.log(
          //   'mainFormInstance',
          //   mainFormInstance.getFieldValue('selectedItems'),
          //   tableData,
          // );
          let formInstance = mainFormInstance.getFieldValue('selectedItems');
          // let previousOrders = mainFormInstance
          //   .getFieldValue('selectedItems')
          //   ?.map((d) => d.order);

          let currentItems = tableData?.map((d, i) => ({
            ...d,
            order: formInstance?.at(i).order,
            customTitle:
              formInstance?.find((v) => v.id === d.id)?.customTitle ??
              undefined, // 这里要用外部的customTitle替换
          }));

          // 交换oldIndex 和 newIndex 的order
          // let currentItems = mainFormInstance.getFieldValue('selectedItems');
          // let tempOldOrder = currentItems[oldIndex].order;
          // let tempNewOrder = currentItems[newIndex].order;

          // currentItems[oldIndex].order = tempNewOrder;
          // currentItems[newIndex].order = tempOldOrder;

          // const newData = arrayMoveImmutable(
          //   [...currentItems],
          //   oldIndex,
          //   newIndex,
          // ).filter((el) => !!el);

          setSelectedItems(currentItems);
          console.log(
            'onTableDataSourceOrderChange.updateSelectedItemOrder',
            currentItems,
          );
          updateSelectedItemOrder(currentItems);
        }}
        onValuesChange={(recordList) => {
          // setSelectedItems(recordList);
          // setSelectedItems(currentItems);
          // console.log('tableDataOPnvc: ', recordList);
          // 这边set的时候要跟selectedItems合并
          mainFormInstance.setFieldValue('selectedItems', recordList);
        }}
        columns={columnSettingColumns}
      />
    </div>
  );
};
export default CombineQueryDetailColumnSettings;
