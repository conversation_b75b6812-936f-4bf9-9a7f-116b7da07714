import React from 'react';
import { Form, Input, Space } from 'antd';
import { UniSelect } from '@uni/components/src';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import fuzzysort from 'fuzzysort';
import FieldCascader from '@/pages/combine-query/components/field-cascader/FieldCascader';
import { fieldItemsProcessor } from '@/pages/combine-query/utils';
import {
  buildOptions,
  findItemBySelectedPaths,
  getOperatorMeta,
} from '@/pages/combine-query/combo-table/utils';
import OperatorSelector from '@/pages/combine-query/combo-table/components/operator';
import ItemValueWidget from '@/pages/combine-query/combo-table/components/item-value';
import ComboQueryNotValue from '@/pages/combine-query/combo-table/components/not-value';
import IconBtn from '@uni/components/src/iconBtn';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import ParentField from '@/pages/combine-query/combo-table/components/parent-field';
import { ConditionGroupPrefix } from '@/pages/combine-query/combo-table/components/condition-misc';
import ComboQueryTableItemOperation from '@/pages/combine-query/combo-table/components/item-operations';
import {
  ColumnNameCascader,
  ColumnNameCommonSelector,
} from '@/pages/combine-query/combo-table/components/column-name';
import { isEmptyValues } from '@uni/utils/src/utils';
import { CombineQueryFieldItem } from '@/pages/combine-query/interfaces';
import { BracketInput } from '@/pages/combine-query/combo-table/components/bracket-input';
import ComboQueryTableConditionOperation from './components/condition-operations';

const cascaderDictionaryName =
  (window as any).externalConfig?.['statsAnalysis']?.cascaderDictionaryName ??
  true;

const defaultFormFields = {
  validateTrigger: false,
};

export const linkDataSource = [
  {
    value: 'and',
    name: '并且',
  },
  {
    value: 'or',
    name: '或者',
  },
];

const nonAddCell = (record, index) => {
  if (record?.id === 'ADD') {
    return {
      colSpan: 0,
    };
  }

  return {};
};

const groupCellByGroupId = (record, index) => {
  return {
    rowSpan: record?.rowSpan ?? 1,
  };
};

export const bracketInputRestrictKeyDown = (
  event,
  extraAllowKeys?: string[],
) => {
  // 目前仅接受 0-9
  // extraKeys可以接受  .+-等其他操作
  let allowedKeys = [
    'ArrowLeft',
    'ArrowRight',
    'Shift',
    'Control',
    'Enter',
    'Backspace',
    'Tab',
    'Alt',
  ]?.concat(extraAllowKeys ?? []);
  let currentEventKey = event?.key;
  console.log('bracketInputRestrictKeyDown', event);

  // 当且仅有Shift 和 Control的时候
  if (currentEventKey === 'Shift' || currentEventKey === 'Control') {
    event?.preventDefault();
  }

  if (!allowedKeys?.includes(currentEventKey)) {
    event?.preventDefault();
  }
};

export const combineQueryHiddenColumns = [
  {
    dataIndex: 'parentFieldPath',
    title: 'parentFieldPath',
    visible: false,
    hidden: true,
    renderColumnFormItem: () => {},
  },
  {
    dataIndex: 'allowedOperators',
    title: '操作符',
    visible: false,
    hidden: true,
    renderColumnFormItem: () => {},
  },
  {
    dataIndex: 'dataType',
    title: '当前组件在operator下的类型',
    visible: false,
    hidden: true,
    renderColumnFormItem: () => {},
  },
  {
    dataIndex: 'groupId',
    title: '组ID',
    visible: false,
    hidden: true,
    renderColumnFormItem: () => {},
  },
  {
    dataIndex: 'expr',
    title: '表达式',
    visible: false,
    hidden: true,
    renderColumnFormItem: () => {},
  },
  {
    dataIndex: 'extra',
    title: 'fieldItem',
    visible: false,
    hidden: true,
    renderColumnFormItem: () => {},
  },
  {
    dataIndex: 'rowSpan',
    title: '行占比',
    visible: false,
    hidden: true,
    renderColumnFormItem: () => {},
  },
  {
    dataIndex: 'virtualKeys',
    title: '虚拟keys',
    visible: false,
    hidden: true,
    renderColumnFormItem: () => {},
  },
];

// 条件值 与 行操作 合并
export const comboQueryColumnValue = (
  config: any,
  fixed: string | undefined,
  fields: any,
  dataSource: any[],
) => {
  let width = 300;

  return [
    {
      dataIndex: 'operatorMeta',
      title: '操作符metadata',
      visible: false,
      hidden: true,
    },
    {
      dataIndex: 'columnName',
      title: '列名',
      visible: true,
      fixed: fixed,
      width: '22%',
      formFieldProps: {
        ...defaultFormFields,
      },
      renderColumnFormItem: (node, record, index, dataIndex, form) => {
        return (
          <ColumnNameCommonSelector
            recordId={record['id']}
            dataIndex={dataIndex}
            config={config}
            fields={fields}
          />
        );
      },
      onCell: nonAddCell,
    },

    {
      dataIndex: 'operator',
      title: '运算符',
      visible: true,
      fixed: fixed,
      width: '10%',
      formFieldProps: {
        ...defaultFormFields,
      },
      renderColumnFormItem: (
        node,
        record,
        index,
        dataIndex,
        form,
        conditionId,
      ) => {
        console.log('operator record', record);
        return (
          <OperatorSelector
            recordId={record['id']}
            conditionId={conditionId}
            dataIndex={dataIndex}
            fields={fields}
            config={config}
          />
        );
      },
      onCell: nonAddCell,
    },

    {
      dataIndex: 'columnValue',
      title: '条件值',
      visible: true,
      fixed: fixed,
      formFieldProps: {
        ...defaultFormFields,
        rules: [
          ({ getFieldValue }) => ({
            validator(rule, value) {
              let recordId = rule?.field?.split('.')?.at(0);
              let operator = getFieldValue([recordId, 'operator']);
              let selectedPath = getFieldValue([recordId, 'columnName']);
              let selectedItem = findItemBySelectedPaths(selectedPath, fields);
              let operatorMeta = getOperatorMeta(
                config,
                operator,
                selectedItem?.type,
              );
              // 没选择操作符的时候
              if (operator === undefined) {
                return Promise.resolve();
              } else {
                // 获取操作符的相关内容
                if (operatorMeta) {
                  if (operatorMeta?.cardinality === 0) {
                    // 表示不用value像是isNull 或者 is not null
                    return Promise.resolve();
                  } else if (operatorMeta?.cardinality === 1) {
                    if (isEmptyValues(value)) {
                      return Promise.reject(new Error('请输入条件值'));
                    }
                  } else {
                    if (Array.isArray(value)) {
                      if (value?.length !== operatorMeta?.cardinality) {
                        return Promise.reject(
                          new Error('请检查条件值是否都已填'),
                        );
                      } else {
                        for (let valueItem of value) {
                          if (isEmptyValues(valueItem)) {
                            return Promise.reject(
                              new Error('请检查条件值是否都已填'),
                            );
                          }
                        }
                      }
                    } else {
                      return Promise.reject(
                        new Error('条件参数不正确，请检查'),
                      );
                    }
                  }

                  return Promise.resolve();
                }
              }
            },
          }),
        ],
      },
      renderColumnFormItem: (
        node,
        record,
        index,
        dataIndex,
        form,
        conditionId,
        conditionIndex,
      ) => {
        return (
          <ItemValueWidget
            recordId={record['id']}
            conditionId={conditionId}
            dataIndex={dataIndex}
            fields={fields}
            config={config}
            index={index}
            conditionIndex={conditionIndex}
            recordGroupId={record?.['groupId']}
            dataSource={dataSource}
          />
        );
      },
      onCell: nonAddCell,
    },
  ];
};

export const comboQueryTableColumns = (
  config: any,
  dataSource: any[],
  existWidthConfig: any,
) => {
  console.log('comboQueryTableColumns', config);

  const fields = fieldItemsProcessor(buildOptions(config, config?.fields), {
    ...config,
    queryType: 'table',
  });

  console.log('comboQueryTableColumnsFields', fields);

  let columns = [
    ...combineQueryHiddenColumns,
    // 新增 外部条件操作
    {
      dataIndex: 'coditionOperation',
      title: '操作',
      visible: true,
      fixed: 'left',
      width: '5%',
      align: 'center',
      render: (node, record, index) => {
        console.log('node', node);
        return (
          <ComboQueryTableConditionOperation
            recordId={record?.id}
            index={index}
            groupId={record?.groupId}
            dataSource={dataSource}
          />
        );
      },
      onCell: groupCellByGroupId,
    },
    {
      dataIndex: 'left-bracket',
      title: '左括号',
      visible: true,
      fixed: 'left',
      width: '5%',
      align: 'center',
      formFieldProps: {
        rules: [
          ({ getFieldValue }) => ({
            validator(rule, value) {
              const regex = /[\u4e00-\u9fa5，。？！【】（）《》——……]/;
              if (regex.test(value)) {
                return Promise.reject(new Error('存在中文符号'));
              } else {
                return Promise.resolve();
              }
            },
          }),
        ],
      },
      renderColumnFormItem: (node, record, index, dataIndex, form) => {
        return (
          <BracketInput
            form={form}
            dataIndex={dataIndex}
            recordId={record?.id}
            regex={/^\(*$/}
            chineseBracket={'（'}
            fillStr={'('}
          />
        );
      },
      onCell: groupCellByGroupId,
    },

    {
      dataIndex: 'not',
      title: '非',
      visible: true,
      fixed: 'left',
      width: '6%',
      align: 'center',
      renderColumnFormItem: (node, record, index) => {
        return <ComboQueryNotValue />;
      },
      onCell: groupCellByGroupId,
    },

    {
      dataIndex: 'parentField',
      title: '条件组',
      width: '8%',
      visible: true,
      align: 'center',
      renderColumnFormItem: (node, record, index) => {
        return <ParentField recordId={record?.id} fields={fields} />;
      },
      onCell: groupCellByGroupId,
    },

    ...comboQueryColumnValue(config, undefined, fields, dataSource),

    // {
    //   dataIndex: 'operation',
    //   title: '行操作',
    //   visible: true,
    //   fixed: 'right',
    //   width: '5%',
    //   align: 'center',
    //   render: (node, record, index) => {
    //     return (
    //       <ComboQueryTableItemOperation
    //         recordId={record?.id}
    //         index={index}
    //         groupId={record?.groupId}
    //       />
    //     );
    //   },
    //   onCell: nonAddCell,
    // },

    {
      dataIndex: 'right-bracket',
      title: '右括号',
      visible: true,
      fixed: 'right',
      width: '5%',
      align: 'center',
      formFieldProps: {
        rules: [
          ({ getFieldValue }) => ({
            validator(rule, value) {
              const regex = /[\u4e00-\u9fa5，。？！【】（）《》——……]/;
              if (regex.test(value)) {
                return Promise.reject(new Error('存在中文付号'));
              } else {
                return Promise.resolve();
              }
            },
          }),
        ],
      },
      renderColumnFormItem: (node, record, index, dataIndex, form) => {
        return (
          <BracketInput
            form={form}
            dataIndex={dataIndex}
            recordId={record?.id}
            regex={/^\)*$/}
            chineseBracket={'）'}
            fillStr={')'}
          />
        );
      },
      onCell: groupCellByGroupId,
    },

    {
      dataIndex: 'link',
      title: '连接符',
      visible: true,
      fixed: 'right',
      width: '9%',
      align: 'center',
      renderColumnFormItem: (node, record, index, dataIndex, form) => {
        return (
          <UniSelect
            dataSource={linkDataSource}
            placeholder="请选择"
            allowClear={true}
            onChange={(value, option) => {
              form.setFieldValue([record?.id, dataIndex], value);

              // 新增一行
              setTimeout(() => {
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_CONDITION_ADD,
                  { index: index },
                );
              }, 0);
            }}
          />
        );
      },
      onCell: groupCellByGroupId,
    },
  ];

  // 算一套特定宽
  const containerWidth =
    ((
      document?.querySelector(
        '#combo-query-expression-table-container .uni-drag-edit-table-container',
      ) as any
    )?.offsetWidth ?? 0) - 19;

  return columns?.map((item: any) => {
    if (!isEmptyValues(existWidthConfig?.[item?.dataIndex])) {
      item['width'] = existWidthConfig?.[item?.dataIndex];
      return item;
    }

    if (isEmptyValues(item['width'])) {
      return item;
    }

    item['width'] =
      (containerWidth * parseInt(item?.width.replace('%', ''))) / 100;
    return item;
  });
};
