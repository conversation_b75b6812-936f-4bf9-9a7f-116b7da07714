import './index.less';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import CombineQueryNGLeftContainer from '@/pages/combine-query/combo-table-ng/components/left-container';
import {
  Badge,
  Button,
  Divider,
  Dropdown,
  Form,
  Input,
  message,
  Select,
  Spin,
  Tabs,
  Tooltip,
  Radio,
  Checkbox,
} from 'antd';
import {
  CombineQueryTitle,
  operations,
  queryOperations,
} from '@/pages/combine-query';
import throttle from 'lodash/throttle';
import { Emitter } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import { history } from 'umi';
import { mongodbFormat } from '@/pages/combine-query/custom/mongoDb';
import { RespVO } from '@uni/commons/src/interfaces';
import { useRequest } from 'umi';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import {
  combineQuerySaveParamProcessor,
  fieldsProcessorByDirectories,
} from '@/pages/combine-query/processor';
import { useLocation } from 'umi';
import loadedConfig from '@/pages/combine-query/config';
import {
  Actions,
  Builder,
  BuilderProps,
  Config,
  Fields,
  ImmutableTree,
  JsonTree,
  Utils,
} from '@react-awesome-query-builder/antd';
import {
  CombineQueryDetail,
  CombineQueryFieldItem,
} from '@/pages/combine-query/interfaces';
import { loadSubjects } from '@/pages/combine-query/combo-table/parser';
import qs from 'qs';
import { v4 as uuidv4 } from 'uuid';
import ComboTable from '@/pages/combine-query/combo-table';
import { ExportIconBtn } from '@uni/components/src';
import CombineQueryTable from '@/pages/combine-query/containers/table';
import CombineQueryMetrics from '@/pages/combine-query/containers/metric';
import { TemplateSelector } from '@/pages/combine-query/combo-table-ng/components/template-selector';
import {
  DownOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  UpOutlined,
  FileExcelOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import DateRangeWithType from '@uni/components/src/date-range-with-type';
import ConfigToImg from '@uni/components/src/config2Img';
import { UniTableProps } from '@uni/components/src/table';
import { useModel } from 'umi';
import { ComboQueryHeaderOperationsV3 } from '@/pages/combine-query/combo-table-ng/v3/header';
import { ComboQueryTabContainer } from '@/pages/combine-query/combo-table-ng/v3/tab-container';
import MemoryCardOne from '@uni/commons/src/icon/MemoryCardOne';
import Prescription from '@uni/commons/src/icon/Prescription';
import SettingTwo from '@uni/commons/src/icon/SettingTwo';
import { isEmptyValues } from '@uni/utils/src/utils';

const externalStatsAnalysisConfig = (window as any).externalConfig?.[
  'statsAnalysis'
];

const comboQueryVersion = externalStatsAnalysisConfig?.version ?? 3;

interface CombineQueryContextItem {
  basicArgForm?: any;
  combineQueryDetail?: any;
}

const defaultValue: CombineQueryContextItem = {};

export const CombineQueryContext = createContext(defaultValue);

const {
  queryBuilderFormat,
  jsonLogicFormat,
  sqlFormat,
  getTree,
  checkTree,
  loadTree,
  uuid,
  loadFromJsonLogic,
} = Utils;

const initValue: JsonTree = { id: uuidv4(), type: 'group' };
const initTree: ImmutableTree = checkTree(loadTree(initValue), loadedConfig);

const tableName = 'OmniCard';

export const metricDetailTableExtraOperations = [
  {
    type: 'COLUMN_CUSTOMIZE',
    label: '列配置',
    icon: <SettingTwo theme="outline" size="20" fill="#575757" />,
  },
  {
    type: 'SAVE_COLUMN',
    label: '保存',
    icon: <MemoryCardOne theme="outline" size="20" fill="#575757" />,
  },
  {
    type: 'SAVE_AS_COLUMN',
    label: '另存为',
    icon: <Prescription theme="outline" size="20" fill="#575757" />,
  },
];

const ComboTableNG = () => {
  const location = useLocation();

  const [basicArgForm] = Form.useForm();

  const comboTableRef = useRef(null);
  const detailTableRef = useRef(null);
  const metricTableRef = useRef(null);

  const templateSelectorContainerRef = useRef(null);

  const [state, setState] = useState({
    tree: initTree,
    config: loadedConfig,
  });

  const [selectedMetricsState, setSelectedMetricsState] = useState({
    aggregate: [],
    metric: [],
    group: [],
  });

  const [columnsState, setColumnState] = useState<any>({});

  const [combineQueryTitle, setCombineQueryTitle] = useState('未命名');

  const [combineQueryId, setCombineQueryId] = useState(undefined);

  const [queryFields, setQueryFields] = useState<Fields>({});

  const [virtualKeys, setVirtualKeys] = useState<any>({});

  const [combineQueryDetail, setCombineQueryDetail] =
    useState<CombineQueryDetail>(undefined);

  const [inFullViewMode, setInFullViewMode] = useState(false);

  useEffect(() => {
    combineQueryFieldsReq();
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_VIRTUAL_ITEM,
      (payload) => {
        setVirtualKeys({
          ...virtualKeys,
          ...payload,
        });
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_STATE,
      (data) => {
        setSelectedMetricsState(data);
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_COLUMNS_STATE,
      (data) => {
        setColumnState(data);
      },
    );

    // 查询模板点击
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_CLICK,
      (item) => {
        if (item?.Id) {
          const searchParam = qs.stringify({
            id: item?.Id,
          });

          // 这边先清空条件输入部分 目前出现了相同位置相同类型时候值不会被覆盖的bug（但是是组合查询版本问题，本地最新版本不会出现这个bug）
          Emitter.emit(
            StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_RESET,
          );

          history.replace(`${location?.pathname}?${searchParam}`);
        }
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_STATE,
      );
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_COLUMNS_STATE,
      );

      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_CLICK);

      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_VIRTUAL_ITEM);
    };
  }, []);

  useEffect(() => {
    // 表格换tree
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_TO_TREE,
      (comboQueryTree) => {
        setState({
          tree: comboQueryTree,
          config: state.config,
        });
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_TO_TREE,
      );
    };
  }, [state.config]);

  useEffect(() => {
    Emitter.on(
      `${StatsAnalysisEventConstant.STATS_ANALYSIS_TITLE_UPDATE}-template`,
      (item) => {
        if (item?.Id === combineQueryId) {
          setCombineQueryTitle(item?.Title);
        }
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_DELETE_SUCCESS,
      (id) => {
        if (id === combineQueryId) {
          setCombineQueryId(undefined);
          history.replace(`${location?.pathname}`);
        }
      },
    );

    return () => {
      Emitter.off(
        `${StatsAnalysisEventConstant.STATS_ANALYSIS_TITLE_UPDATE}-template`,
      );
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_DELETE_SUCCESS,
      );
    };
  }, [combineQueryId]);

  useEffect(() => {
    if (Object.keys(queryFields)?.length > 0) {
      // location
      if (location.search) {
        let urlParam = qs.parse(window.location.search, {
          ignoreQueryPrefix: true,
        });

        if (urlParam.id) {
          setCombineQueryId(urlParam?.id);
          Emitter.emit(
            StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_EXPAND,
          );

          // 获取并转换到界面上
          combineQuerySavedTemplatedGetReq(urlParam?.id);
        }
      }
    }
  }, [queryFields, location.search]);

  // title 变更的时候通知其他组件
  useEffect(() => {
    Emitter.emit(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TITLE,
      combineQueryTitle,
    );
    Emitter.emit(
      StatsAnalysisEventConstant.STATS_ANALYSIS_STATS_COMBINE_QUERY_TITLE,
      combineQueryTitle,
    );
  }, [combineQueryTitle]);

  // 获取全tree
  const { loading: combineQueryFieldsLoading, run: combineQueryFieldsReq } =
    useRequest(
      () => {
        let data = {
          TableName: tableName,
        };

        return uniCombineQueryService(
          'Api/Analysis/AnaModelDef/GetQueryableList',
          {
            params: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<CombineQueryFieldItem[]>) => {
          console.log('response?.data: ', response);
          if (response?.code === 0 && response?.statusCode === 200) {
            loadSubjects(response?.data); // parser init
            let fieldKeySort = fieldsProcessorByDirectories(response?.data);
            // console.log('fieldKeySort: ', fieldKeySort);
            setQueryFields(fieldKeySort?.fieldTree);
          } else {
            setQueryFields({});
          }
        },
      },
    );

  const {
    loading: combineQuerySavedTemplateGetLoading,
    run: combineQuerySavedTemplatedGetReq,
  } = useRequest(
    (id) => {
      return uniCombineQueryService('Api/DmrAnalysis/ComboQueryTemplate/Get', {
        params: {
          id: id,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<CombineQueryDetail>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setCombineQueryTitle(response?.data?.Title);
          setCombineQueryDetail(response?.data);
          Emitter.emit(
            StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
            response?.data,
          );

          // tree
          // setTimeout(() => {
          //   Emitter.emit(
          //     [
          //       StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY,
          //       StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS,
          //     ],
          //     response?.data?.Expr,
          //   );
          // }, 0);
        } else {
          message.warn('获取当前模板失败');
          setCombineQueryId(undefined);
        }
      },
    },
  );

  const onOperationClick = (type) => {
    switch (type) {
      case 'NEW': {
        // TODO 是不是+一个在 新建 前面 提示框 表示 被修改过 并保存？
        setCombineQueryTitle('未命名');
        setCombineQueryId(undefined);
        // setState({
        //   tree: loadTree(initValue),
        //   config: state.config,
        // });

        Emitter.emit(
          StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_RESET,
        );
        history.replace(`${location?.pathname}`);
        Emitter.emit(StatsAnalysisEventConstant.STATS_ANALYSIS_RESET);
        break;
      }
      case 'SAVE': {
        Emitter.emit(
          StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE,
          {
            id: combineQueryId,
            combineQueryTitle: combineQueryTitle,
            columnsState: columnsState,
            selectedMetricsState: selectedMetricsState,
          },
        );
        break;
      }
      case 'SAVE_AS': {
        Emitter.emit(
          StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE,
          {
            id: undefined,
            combineQueryTitle: combineQueryTitle,
            columnsState: columnsState,
            selectedMetricsState: selectedMetricsState,
          },
        );
        break;
      }
      default:
        break;
    }
  };

  const ComboTableViewMore = () => {
    return (
      <div className={'view-more-container'}>
        <div
          className={'view-more-btn'}
          onClick={() => {
            setInFullViewMode(!inFullViewMode);
          }}
        >
          {inFullViewMode ? <UpOutlined /> : <DownOutlined />}
        </div>
      </div>
    );
  };

  return (
    <div id={'combo-table-ng-container'} className={'combo-table-ng-container'}>
      <CombineQueryNGLeftContainer />
      <div
        className={`combo-table-ng-content-container ${
          comboQueryVersion === 3
            ? 'combo-table-ng-content-container-v3-override'
            : ''
        }`}
      >
        <Spin
          spinning={
            combineQuerySavedTemplateGetLoading || combineQueryFieldsLoading
          }
        >
          <div
            id={'query-rule-ng-table'}
            className={`query-rule-ng-container ${
              inFullViewMode
                ? 'query-rule-ng-container-full-mode'
                : 'query-rule-ng-container-less-mode'
            }`}
          >
            {/*TODO 顶上有个basicArgs */}

            <div
              className={
                'combo-table-header-container combo-query-ng-header-container'
              }
              style={comboQueryVersion === 3 ? { borderBottom: 'none' } : {}}
            >
              <CombineQueryTitle
                title={combineQueryTitle}
                onTitleChange={(value) => {
                  setCombineQueryTitle(value);
                }}
              />

              <div className={'flex-row-center'}>
                {comboQueryVersion === 3 && (
                  <ComboQueryHeaderOperationsV3
                    onOperationClick={(key: string) => {
                      onOperationClick(key);
                    }}
                  />
                )}

                {comboQueryVersion === 2 && (
                  <>
                    <div className={'header-args-container'}>
                      <DateRangeWithType
                        form={basicArgForm}
                        needFormWrapper={true}
                        enableDateFormatTypeSelector={true}
                      />
                    </div>
                    <div className={'header-operations-container'}>
                      <Tooltip title={'查看语法'}>
                        <InfoCircleOutlined
                          style={{ margin: '0px 10px' }}
                          onClick={() => {
                            comboTableRef?.current?.onOperationClick(
                              'VIEW_TEXT',
                            );
                          }}
                        />
                      </Tooltip>

                      <div className={'operations-container'}>
                        {operations.map((item) => {
                          return (
                            <Button
                              onClick={() => {
                                onOperationClick(item.key);
                              }}
                              className={'operation-item'}
                            >
                              {item.label}
                            </Button>
                          );
                        })}
                      </div>

                      <Button
                        style={{ marginRight: 10 }}
                        onClick={() => {
                          comboTableRef?.current?.onOperationClick('RESET');
                        }}
                      >
                        重置
                      </Button>

                      <Button
                        style={{ marginRight: 10 }}
                        type={'primary'}
                        onClick={() => {
                          comboTableRef?.current?.onOperationClick('QUERY');
                        }}
                      >
                        查询
                      </Button>
                    </div>
                  </>
                )}
              </div>
            </div>

            {comboQueryVersion === 2 && (
              <>
                <ComboTable
                  inFullViewMode={inFullViewMode}
                  tableRef={comboTableRef}
                  nextGeneration={true}
                  {...loadedConfig}
                  fields={queryFields}
                  value={state.tree}
                  onChange={() => {}}
                  renderBuilder={() => {
                    return null;
                  }}
                />

                <ComboTableViewMore />
              </>
            )}
          </div>
          {comboQueryVersion === 3 && (
            <ComboQueryTabContainer
              templateSelectorContainerRef={templateSelectorContainerRef}
              combineQueryDetail={combineQueryDetail}
              comboTableRef={comboTableRef}
              basicArgForm={basicArgForm}
              queryFields={queryFields}
              state={state}
              detailTableRef={detailTableRef}
              metricTableRef={metricTableRef}
            />
          )}
          {comboQueryVersion === 2 && (
            <div className={'metric-detail-container'}>
              <CombineQueryContext.Provider
                value={{
                  basicArgForm: basicArgForm,
                  combineQueryDetail: combineQueryDetail,
                }}
              >
                <MetricDetailTabContainer
                  comboTableRef={comboTableRef}
                  detailTableRef={detailTableRef}
                  metricTableRef={metricTableRef}
                />
              </CombineQueryContext.Provider>
            </div>
          )}
        </Spin>
      </div>
    </div>
  );
};

interface MetricDetailExtraContentProps {
  templateSelectorContainerRef?: any;
  currentActiveKey: string;
  onOperationClick?: (operationType: string) => void;
  onGetExtraTableProps?: () => UniTableProps<any>;

  disableSpecialOperator?: boolean;
  onTemplateSelect?: (templateId: string) => void;

  templateIdSyncRef?: any;

  contentContainer?: any;
  detailTableContainerRef?: any;
  comboTableRef?: any;
}

interface MetricDetailTabContainerProps {
  comboTableRef: React.MutableRefObject<any>;
  detailTableRef: React.MutableRefObject<any>;
  metricTableRef: React.MutableRefObject<any>;
}

const MetricDetailTabContainer = (props: MetricDetailTabContainerProps) => {
  const [activeKey, setActiveKey] = useState('DETAIL');

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const tableHeight =
    document.getElementById('combo-table-ng-container')?.offsetHeight -
    document.getElementById('query-rule-ng-table')?.offsetHeight -
    42 -
    10 -
    6 -
    5;

  return (
    <Tabs
      tabBarExtraContent={
        <MetricDetailExtraContent
          currentActiveKey={activeKey}
          onGetExtraTableProps={() => {
            if (activeKey === 'METRIC' && props?.metricTableRef?.current) {
              return {
                dataSource:
                  props?.metricTableRef?.current?.getTableDataSource(),
                columns: props?.metricTableRef?.current?.getTableColumns(),
                dictionaryData: globalState?.dictData,
              } as any;
            }

            return {} as any;
          }}
          onOperationClick={(type) => {
            let operatorRef = null;
            if (activeKey === 'DETAIL') {
              operatorRef = props?.detailTableRef;
            }

            if (activeKey === 'METRIC') {
              operatorRef = props?.metricTableRef;
            }

            if (operatorRef) {
              switch (type) {
                case 'COLUMN_CUSTOMIZE':
                  operatorRef?.current?.openColumnCustomizer();
                  break;
                case 'SAVE_COLUMN':
                  operatorRef?.current?.saveColumnTemplate();
                  break;
                case 'SAVE_AS_COLUMN':
                  operatorRef?.current?.saveAsColumnTemplate();
                  break;
                case 'EXPORT':
                  return operatorRef?.current?.getExportDataConfig();
                case 'ICDE_EXPORT':
                case 'OPER_EXPORT':
                case 'OPER_GROUP_EXPORT':
                  return operatorRef?.current?.getExportDataConfig(type);
                default:
                  break;
              }
            }
          }}
        />
      }
      onChange={async (activeKey) => {
        setActiveKey(activeKey);
        let expression = await props?.comboTableRef?.current?.getExpression();
        // 触发 table 数据查询
        if (activeKey === 'METRIC') {
          Emitter.emit(
            StatsAnalysisEventConstant.STATS_ANALYSIS_TAB_SWITCH_METRIC_QUERY,
            { query: expression },
          );
        }

        if (activeKey === 'DETAIL') {
          Emitter.emit(
            StatsAnalysisEventConstant.STATS_ANALYSIS_TAB_SWITCH_DETAIL_QUERY,
            { query: expression },
          );
        }
      }}
    >
      <Tabs.TabPane
        tab={
          <MetricDetailTitle
            type={'DETAIL'}
            label={'明细'}
            eventName={
              StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TAB_COUNT
            }
          />
        }
        key="DETAIL"
      >
        <div id={'detail-data-container'} className={'detail-data-container'}>
          <CombineQueryTable
            tableRef={props?.detailTableRef}
            tableName={tableName}
            needCardContainer={false}
            nextGeneration={true}
            infiniteScroll={true}
            tableHeight={tableHeight}
          />
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane
        tab={<MetricDetailTitle type={'METRIC'} label={'统计'} />}
        key="METRIC"
      >
        <div className={'group-data-container'}>
          <CombineQueryMetrics
            tableRef={props?.metricTableRef}
            tableName={tableName}
            needCardContainer={false}
            nextGeneration={true}
            tableHeight={tableHeight}
          />
        </div>
      </Tabs.TabPane>
    </Tabs>
  );
};

interface MetricDetailTitleProps {
  type: string;
  label: string;
  eventName?: string;
}

export const MetricDetailTitle = (props: MetricDetailTitleProps) => {
  const [totalCount, setTotalCount] = React.useState(0);

  useEffect(() => {
    Emitter.on(props?.eventName, (payload) => {
      setTotalCount(payload?.total ?? 0);
    });

    return () => {
      Emitter.off(props?.eventName);
    };
  }, []);

  return (
    <span className={'title-label'}>
      {props?.label}
      <Badge
        overflowCount={1000000}
        count={totalCount}
        size={'default'}
        style={{
          backgroundColor: '#E6F7FF',
          color: '#1464f8',
          marginLeft: 10,
        }}
      />
    </span>
  );
};

const extraContentKeyMap = {
  NORMAL: {
    queryUrl: 'Api/DmrAnalysis/ComboQuery/GetDetails',
    exportKey: 'EXPORT',
  },
  ICDE: {
    label: '诊断明细',
    checkLabel: '过滤诊断',
    queryUrl: 'Api/DmrAnalysis/ComboQuery/GetIcdeDetails',
    exportKey: 'ICDE_EXPORT',
    filterUnwindItemsKey: 'icde',
  },
  OPER: {
    label: '手术明细',
    checkLabel: '过滤手术',
    queryUrl: 'Api/DmrAnalysis/ComboQuery/GetOperDetails',
    exportKey: 'OPER_EXPORT',
    filterUnwindItemsKey: 'oper',
  },
  OPER_GROUP: {
    label: '手术组明细',
    checkLabel: '过滤手术组',
    queryUrl: 'Api/DmrAnalysis/ComboQuery/GetOperGroupDetails',
    exportKey: 'OPER_GROUP_EXPORT',
    filterUnwindItemsKey: 'oper_group',
  },
};

export const MetricDetailExtraContent = (
  props: MetricDetailExtraContentProps,
) => {
  const [selectedItemId, setSelectedItemId] = React.useState(null);

  const [detailSelectedValue, setDetailSelectedValue] =
    React.useState('NORMAL');

  const [detailSelectedValueOptions, setDetailSelectedValueOptions] =
    React.useState([]);

  const [detailIcdeOperFilterUnwindItems, setDetailIcdeOperFilterUnwindItems] =
    React.useState<any>({
      icde: true,
      oper: true,
      oper_group: true,
    });

  const combineQueryContext = useContext(CombineQueryContext);

  const combineQueryDetail = combineQueryContext?.combineQueryDetail;

  React.useImperativeHandle(props?.contentContainer, () => {
    return {
      setDetailSelectedValueExternal: (selectedValue: string) => {
        console.log('selectedValue', selectedValue);
        setDetailSelectedValue(selectedValue);
      },

      setDetailSelectedValueOptionsExternal: (options: string[]) => {
        setDetailSelectedValueOptions(options);
      },

      getCurrentSelectedDetailTypeItem: () => {
        return extraContentKeyMap?.[detailSelectedValue];
      },
      getExtraContentParams: () => {
        if (
          !isEmptyValues(
            extraContentKeyMap?.[detailSelectedValue]?.['filterUnwindItemsKey'],
          )
        ) {
          return {
            FilterUnwindItems:
              detailIcdeOperFilterUnwindItems?.[
                extraContentKeyMap?.[detailSelectedValue]?.[
                  'filterUnwindItemsKey'
                ]
              ],
          };
        }

        return {};
      },
    };
  });

  const onDetailRadioChange = (event: any) => {
    setDetailSelectedValue(event.target.value);
    setTimeout(() => {
      let tableDataSize =
        props?.detailTableContainerRef?.current?.getTableDataSize();
      if (tableDataSize > 0) {
        // 表示有数据 重新查询
        props?.comboTableRef?.current?.onOperationClick('QUERY');
      }
    }, 0);
  };

  const onDetailCheckBoxChange = (checked: boolean, key: string) => {
    setDetailIcdeOperFilterUnwindItems({
      ...detailIcdeOperFilterUnwindItems,
      [key]: checked,
    });

    if (detailSelectedValue !== 'NORMAL') {
      setTimeout(() => {
        let tableDataSize =
          props?.detailTableContainerRef?.current?.getTableDataSize();
        if (tableDataSize > 0) {
          // 表示有数据 重新查询
          props?.comboTableRef?.current?.onOperationClick('QUERY');
        }
      }, 0);
    }
  };

  return (
    <div className={'metric-detail-extra-content-container'}>
      {/*radio*/}
      {props?.disableSpecialOperator !== true &&
        props?.currentActiveKey === 'DETAIL' && (
          <Radio.Group
            style={{ display: 'flex' }}
            onChange={onDetailRadioChange}
            optionType={'button'}
            buttonStyle={'solid'}
            value={detailSelectedValue}
            className={'flex-row-center'}
          >
            <Radio className={'normal-radio-item'} value={'NORMAL'}>
              病案明细
            </Radio>
            {detailSelectedValueOptions?.map((key) => {
              return (
                <div className={'icde-oper-radio-item'}>
                  <Radio value={key}>{extraContentKeyMap?.[key]?.label}</Radio>
                  <Checkbox
                    checked={
                      detailIcdeOperFilterUnwindItems?.[
                        extraContentKeyMap?.[key]?.filterUnwindItemsKey
                      ] ?? false
                    }
                    onChange={(event) => {
                      onDetailCheckBoxChange(
                        event.target.checked,
                        extraContentKeyMap?.[key]?.filterUnwindItemsKey,
                      );
                    }}
                  >
                    {extraContentKeyMap?.[key]?.checkLabel}
                  </Checkbox>
                </div>
              );
            })}
          </Radio.Group>
        )}

      <div
        className={'flex-row-center'}
        style={{ justifyContent: 'flex-end', flex: 1 }}
      >
        <TemplateSelector
          templateIdSyncRef={props?.templateIdSyncRef}
          templateSelectorContainerRef={props?.templateSelectorContainerRef}
          comboDetailTitle={combineQueryDetail?.Title}
          templateSubjectOutputColumns={
            combineQueryDetail?.SubjectOutputColumns
          }
          templateAggregationOutputColumns={
            combineQueryDetail?.AggregationOutputColumns
          }
          currentActiveKey={props?.currentActiveKey}
          onTemplateIdChange={(templateId) => {
            setSelectedItemId(templateId);
            props?.onTemplateSelect && props?.onTemplateSelect?.(templateId);
          }}
        />

        <div className={'flex-row-center'}>
          <div className={'extra-operations-container'}>
            {metricDetailTableExtraOperations
              ?.filter((item) => {
                if (props?.disableSpecialOperator === true) {
                  return item?.type === 'COLUMN_CUSTOMIZE';
                }

                return true;
              })
              ?.map((operationItem) => {
                if (comboQueryVersion === 3) {
                  return (
                    <Tooltip title={operationItem?.label}>
                      <div
                        className={`extra-operation-item-icon ${
                          operationItem?.type === 'SAVE_COLUMN' &&
                          ['DEFAULT', 'COMBO_TEMPLATE']?.includes(
                            selectedItemId,
                          )
                            ? 'extra-operation-item-disabled'
                            : ''
                        }`}
                        onClick={() => {
                          if (operationItem?.type === 'SAVE_COLUMN') {
                            if (
                              ['DEFAULT', 'COMBO_TEMPLATE']?.includes(
                                selectedItemId,
                              )
                            ) {
                              return;
                            }
                          }
                          props?.onOperationClick &&
                            props.onOperationClick(operationItem?.type);
                        }}
                      >
                        {operationItem?.icon}
                      </div>
                    </Tooltip>
                  );
                }

                return (
                  <Button
                    className={'extra-operation-item'}
                    onClick={() => {
                      if (operationItem?.type === 'SAVE_COLUMN') {
                        if (
                          ['DEFAULT', 'COMBO_TEMPLATE']?.includes(
                            selectedItemId,
                          )
                        ) {
                          return;
                        }
                      }
                      props?.onOperationClick &&
                        props.onOperationClick(operationItem?.type);
                    }}
                  >
                    {operationItem?.label}
                  </Button>
                );
              })}
          </div>

          {props?.disableSpecialOperator !== true && (
            <div className={'export-operation-separator'} />
          )}

          {props?.disableSpecialOperator !== true &&
            externalStatsAnalysisConfig?.['drawChart'] &&
            props?.currentActiveKey === 'METRIC' && (
              <ConfigToImg
                getExtraTableProps={() => {
                  return props?.onGetExtraTableProps();
                }}
              />
            )}

          {props?.disableSpecialOperator !== true && (
            <ExportIconBtn
              tooltipProps={{
                title: '导出表格数据',
              }}
              getExternalExportConfig={() => {
                if (props?.onOperationClick) {
                  return props.onOperationClick(
                    extraContentKeyMap?.[detailSelectedValue]?.exportKey ??
                      'EXPORT',
                  );
                }

                return {};
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ComboTableNG;
