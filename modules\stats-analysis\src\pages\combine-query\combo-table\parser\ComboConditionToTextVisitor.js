import parser from './parser.js';
import {
  fromAndRule,
  fromOrRule,
  fromAtomRule,
  fromNotAtomRule,
  fromNotSomeRule,
  fromParenthesisRule,
  fromSomeRule,
} from './textFactory.js';

const BaseCstVisitorWithDefaults =
  parser.getBaseCstVisitorConstructorWithDefaults();

const defaultParams = {
  insideSome: false,
  insideSomeMultipleChildren: false,
  insideNot: false,
  insideAnd: false,
  notResolved: false,
  indent: 0,
};

class ComboConditionToTextVisitor extends BaseCstVisitorWithDefaults {
  constructor() {
    super();
    this.validateVisitor();
  }

  subject(ctx) {
    var subjectLiteral = ctx.Identifier.map((i) => i.image).join('.');
    return subjectLiteral;
  }

  value(ctx) {
    if (!ctx.array) {
      var literal = ctx[Object.keys(ctx)[0]][0].image;
      return JSON.parse(literal);
    } else {
      return ctx.array[0].children.value.map((v) => this.visit(v));
    }
  }

  atomRule(ctx, params) {
    const subjectLiteral = this.visit(ctx.subject);
    const operator = ctx.Operator[0].tokenType.name;
    const value = this.visit(ctx.value);
    const text = fromAtomRule(subjectLiteral, operator, value, params);
    return text;
  }

  someRule(ctx, params) {
    params.insideSome = true;
    params.insideSomeMultipleChildren = ctx.atomRule.length > 1;

    const subjectLiteral = this.visit(ctx.subject);
    const childrenTexts = ctx.atomRule.map((r) => this.visit(r, params));
    const text = fromSomeRule(subjectLiteral, childrenTexts, params);
    return text;
  }

  notRule(ctx, params) {
    params.insideNot = true;
    params.notResolved = false;

    if (ctx.atomRule) {
      const text = fromNotAtomRule(this.visit(ctx.atomRule, params), params);
      return text;
    } else {
      const text = fromNotSomeRule(this.visit(ctx.someRule, params), params);
      return text;
    }
  }

  parenthesisRule(ctx, params) {
    const text = fromParenthesisRule(this.visit(ctx.rule));
    return text;
  }

  unaryRule(ctx, params) {
    params = { ...params };

    if (ctx.atomRule) {
      return this.visit(ctx.atomRule, params);
    } else if (ctx.notRule) {
      return this.visit(ctx.notRule, params);
    } else if (ctx.someRule) {
      return this.visit(ctx.someRule, params);
    } else if (ctx.parenthesisRule) {
      return this.visit(ctx.parenthesisRule, params);
    }
  }

  andRule(ctx, params) {
    if (ctx.unaryRule.length === 1) return this.visit(ctx.unaryRule[0], params);
    params.insideAnd = true;
    const childrenTexts = ctx.unaryRule.map((r) => this.visit(r, params));
    const text = fromAndRule(childrenTexts, params);
    return text;
  }

  orRule(ctx, params) {
    if (ctx.andRule.length === 1) return this.visit(ctx.andRule[0], params);
    params.insideOr = true;
    const childrenTexts = ctx.andRule.map((r) => this.visit(r, params));
    const text = fromOrRule(childrenTexts, params);
    return text;
  }

  rule(ctx, params) {
    params = params || { ...defaultParams };
    const text = this.visit(ctx.orRule, params);
    return text;
  }
}

export default ComboConditionToTextVisitor;
