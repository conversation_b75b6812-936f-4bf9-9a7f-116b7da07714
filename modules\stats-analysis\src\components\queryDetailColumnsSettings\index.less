@import '~@uni/commons/src/style/variables.less';
@import './toc.less';

.detail-modal {
  overflow-y: hidden;
  .ant-modal-header {
    padding: 0 0 0 24px;
    //   padding-left: 24px;
    height: 54px;
    line-height: 54px;
    .detail-modal-header {
      justify-content: space-between;
      align-items: baseline;
      height: 54px;
      line-height: 54px;
      .btn_space {
        display: flex;
        align-items: center;
        height: 54px;

        .detail-modal-close-x {
          width: 54px;
          height: 54px;
          line-height: 58px;
        }
      }
    }
  }
}

.combine-query-column-setting-container {
  display: flex;
  flex-direction: column;
  // margin-left: 10px;
}

.detail-columns-setting-container {
  display: flex;
  flex-direction: column;
  height: 600px;

  ::-webkit-scrollbar {
    width: 6px;
    background-color: transparent;
    position: absolute;
    z-index: 100;
    right: 0;
  }
  ::-webkit-scrollbar-thumb {
    position: absolute;
    z-index: 101;
    border-radius: 20px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: rgb(189, 189, 189);
  }
  ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    background-color: #f5f5f5;
  }

  .ant-modal-body {
    padding: 0px !important;
  }

  .detail-columns-setting-info-container {
    display: flex;
    flex-direction: row;
    height: 500px;

    .detail-columns-tree-container {
      padding: 20px 0px;
      width: 60%;
      display: flex;
      flex-direction: column;
      height: 100%;

      .detail-columns-search {
        .ant-input {
          border-right: none !important;
        }

        .ant-btn {
          border-left: none !important;
        }
      }

      .reset {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 20px;
        background: #feeff0;
        color: #f5222d;
        border: none;
      }

      .detail-columns-tree-content {
        display: flex;
        flex-direction: row;
        margin-top: 20px;
        height: calc(100% - 32px - 20px);
        padding: 0px 5px 0px 20px;

        .detail-tree-container {
          display: flex;
          flex-direction: column;
          height: 100%;
          overflow-y: auto;
          margin-left: 10px;
          padding-right: 15px;
          flex: 1;

          .tree-item-container:not(:first-child) {
            margin-top: 10px;
          }

          .tree-item-container {
            display: flex;
            flex-direction: column;

            .tree-header-container {
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: space-between;

              .label-container {
                display: flex;
                flex-direction: row;
                align-items: flex-end;

                .title {
                  font-size: 15px;
                  font-weight: bold;
                }

                .selected-number {
                  color: #0958d9;
                }
              }

              button:not(:first-child) {
                margin-left: 5px !important;
              }
            }

            .items-container {
              display: flex;
              flex-wrap: wrap;
              column-gap: 5px;
              row-gap: 10px;
              margin: 10px 5px;

              .tag-item {
                border: 1px solid #e4e4e4;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: rgba(0, 0, 0, 0.85);
              }

              .tag-item-checked {
                border: 1px solid #91caff;
                background: #e6f4ff !important;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #578fe6;

                .tick {
                  stroke-width: 40;
                  stroke: #0958d9;
                  color: #0958d9;
                  font-size: 12px;
                  margin-right: 4px;
                  font-weight: bold;
                }
              }

              .ant-tag-checkable:active {
                border: 1px solid #91caff;
                background: #e6f4ff !important;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #578fe6;
              }
            }

            .collapse-container {
              display: flex;
              flex-direction: column;

              .selected-number {
                color: #0958d9;
              }

              .ant-collapse-borderless {
                background-color: transparent;
              }

              .ant-collapse-header {
                padding: 4px 6px;
              }

              .ant-collapse-content {
                border-top: 1px solid #e4e4e4;
              }

              .ant-collapse-content-box {
                padding: 0px;
              }

              .ant-collapse-item {
                border: 1px solid #e4e4e4;
                border-radius: 6px;
                width: 95%;
                margin-bottom: 10px;
              }

              .panel-item-container {
                display: flex;
                flex: 1;
                width: 100%;
                flex-wrap: wrap;
                column-gap: 5px;
                row-gap: 10px;
                margin: 10px 5px;
              }
            }
          }
        }
      }
    }

    .detail-columns-separator {
      height: 100%;
      width: 1px;
      min-width: 1px;
      background: #f0f0f0;
    }

    .selected-table-container {
      padding: 20px;
      width: 50%;
      display: flex;
      flex-direction: column;
      height: 100%;

      .selected-header-container {
        display: flex;
        flex-direction: row;
      }

      .selected-table-operation-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .item {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 8px;
          color: #1890ff;
          cursor: pointer;
          stroke-width: 40;
          stroke: #1890ff;
        }
      }

      textarea {
        resize: none;
      }
    }
  }
}
