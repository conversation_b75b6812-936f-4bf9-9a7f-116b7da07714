.stats-table-persist-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  // padding: 10px 10px;
  height: 100%;

  .operations-container {
    display: flex;
    height: 50px;
    min-height: 50px;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;

    .operation-item:not(:last-child) {
      margin-right: 10px;
    }
  }

  .title {
    font-size: 24px;
    font-weight: bold;
  }

  .report-content-container {
    display: flex;
    flex: 1;
    flex-direction: column;

    .ant-table-body {
      min-height: var(--tableMinHeight);
    }

    .ant-pro-card-body {
      padding-left: 0px !important;
      padding-right: 0px !important;
    }
  }
}
