export const ValueType = {
  Date: { valueType: 'date' },
  Currency: { valueType: 'money' },
  _Counted: { valueType: '_Counted' },
};

export enum ReqActionType {
  barCode = 'BarCode',
  patAdmit = 'PatAdmit',
  patRevertAdmit = 'PatRevertAdmit',

  // 病区病案（batch/single）
  wardSignOut = 'WardSignOut',
  wardRevertSignOut = 'WardRevertSignOut',

  // 病案（batch/single）
  mrRoomSignIn = 'MrRoomSignIn',
  batchMrRoomSignIn = 'batchMrRoomSignIn',
  mrRoomRevertSignIn = 'MrRoomRevertSignIn',
  mrRoomRRevertSubmit = 'MrRoomRRevertSubmit',
  selectiveMrRoomSignIn = 'SelectiveMrRoomSignIn', // 择期

  // 归档（batch/single）
  warehouseSignIn = 'WarehouseSignIn',
  warehouseRevertSignin = 'WarehouseRevertSignin',

  // 封存（batch/single）
  seal = 'Seal',
  unSeal = 'Unseal',
  updateScanInfo = 'UpdateScanInfo',

  // 借阅（batch/single）
  lend = 'Lend',
  return = 'Return',
  getAlerts = 'GetAlerts',
  createAlerts = 'CreateAlerts', // 催缴 / 催还
  borrowRecordPrint = 'BorrowRecordPrint', // 后端导出借阅单打印
}

export const AutoRegister = 'AutoRegister';

// columns front
export const MockColumns = [
  {
    dataIndex: 'RecordId',
    title: '编号',
    visible: true,
  },
  {
    dataIndex: 'OutDept',
    title: 'OutDept',
    visible: true,
  },
  {
    dataIndex: 'PatName',
    title: 'PatName',
    visible: true,
  },
  {
    dataIndex: 'PatNo',
    title: 'PatNo',
    visible: true,
  },
  {
    dataIndex: 'WareHouseNo',
    title: 'WareHouseNo',
    visible: true,
  },
];

// 签收时候可选的入参类型
export const SigninType = [
  {
    title: '条码号',
    value: 'BarCode',
  },
  {
    title: '病案号',
    value: 'PatNo',
  },
  {
    title: '住院号',
    value: 'PatAdmNo',
  },
  {
    title: '上架号',
    value: 'TrackNo',
  },
];

export const SpecialSortingHandleKeys = [
  {
    originalKey: 'IsRegistered',
    targetKey: 'RegisterDate',
  },
  {
    originalKey: 'IsSignedIn',
    targetKey: 'DmrSignInDate',
  },
];
