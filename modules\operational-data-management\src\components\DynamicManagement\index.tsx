import { Reducer, useEffect, useReducer, useState, useMemo } from 'react';
import { useModel } from '@@/plugin-model/useModel';
import { Card, Checkbox, Divider, Space, Drawer, Table, Tooltip } from 'antd';
import { v4 as uuidv4 } from 'uuid';
import _ from 'lodash';
import { isRespErr, RespType } from '@/utils/widgets';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  IReducer,
  IModalState,
  ITableState,
} from '@uni/reducers/src/interface';
import {
  InitTableState,
  TableAction,
  modalReducer,
  tableReducer,
} from '@uni/reducers/src';
import { ModalAction } from '@uni/reducers/src/modalReducer';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src/commonService';
import { UniTable } from '@uni/components/src/index';
import { Emitter } from '@uni/utils/src/emitter';
import { CommonOdmEventConstants } from '../../pages/inHospDynamicRegistration/constants';
import { isEmptyValues } from '@uni/utils/src/utils';
import { DynamicManagementProps, IDynamicManagementItem } from './types';
import './style.less';

const { Summary } = Table;

function hasDecimalPart(value) {
  // 转换为数值
  const num = Number(value);

  // 排除 NaN，并检查小数部分
  return !isNaN(num) && num % 1 !== 0;
}

const DynamicManagement: React.FC<DynamicManagementProps> = ({
  pageTitle,
  apiBasePath,
  detailBtnEventKey,
  columns,
  id,
  EditByDeptComponent,
}) => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster');

  const [tableParams, setTableParams] = useState(undefined);

  // table state
  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);

  // modal state
  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer>
  >(modalReducer, {
    visible: false,
    record: undefined,
    dataSource: [],
  });

  // checked 只显示审核错误数据
  const [onlyDiffed, setOnlyDiffed] = useState(false);
  const [finalDatasource, setFinalDatasource] = useState([]);

  // 存储后端返回的汇总数据
  const [summaryData, setSummaryData] = useState({});

  // columns fetch
  const {
    data: ColumnsData,
    loading: ColumnsReqLoading,
    run: ColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(`Api/Dyn-ddr/${apiBasePath}/GetList`, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          console.log(res);
          TableDispatch({
            type: TableAction.columnsChange,
            payload: {
              columns: tableColumnBaseProcessor(
                columns,
                res?.data?.Columns,
                'local',
              ),
            },
          });
        } else {
          return null;
        }
      },
    },
  );

  // data fetch
  const {
    data: DataListData,
    loading: DataListReqLoading,
    run: DataListReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Dyn-ddr/${apiBasePath}/GetList`, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          console.log(res);
          TableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: res?.data?.Items?.map((d) => ({ ...d, uuid: uuidv4() })),
            },
          });
        } else {
          return null;
        }
      },
    },
  );

  // summary fetch
  const {
    data: SummaryData,
    loading: SummaryReqLoading,
    run: SummaryReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Dyn-ddr/${apiBasePath}/GetSummary`, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          console.log('Summary data:', res);
          setSummaryData(res?.data || {});
        } else {
          return null;
        }
      },
    },
  );

  // searchParams + emitter
  // 事件处理
  useEffect(() => {
    // 监听打开drawer的事件
    Emitter.on(detailBtnEventKey, (record) => {
      ModalStateDispatch({
        type: ModalAction.change,
        payload: {
          record,
          visible: true,
        },
      });
    });

    // 监听关闭drawer的事件
    Emitter.on(CommonOdmEventConstants.DRAWER_CLOSE_CONFIRM, () => {
      ModalStateDispatch({
        type: ModalAction.init,
      });
    });

    return () => {
      Emitter.off(detailBtnEventKey);
      Emitter.off(CommonOdmEventConstants.DRAWER_CLOSE_CONFIRM);
    };
  }, [detailBtnEventKey]);

  // searchParams处理
  useEffect(() => {
    if (
      (searchParams && searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(tableParams) && searchParams?.dateRange?.length > 0)
    ) {
      const requestData = {
        Sdate: searchParams?.dateRange?.at(0),
        Edate: searchParams?.dateRange?.at(1),
        HospCode: searchParams?.hospCodes,
      };

      setTableParams(requestData);

      // 同时调用数据和汇总接口
      DataListReq(requestData);
      SummaryReq(requestData);
    }
  }, [searchParams]);

  useEffect(() => {
    if (onlyDiffed) {
      setFinalDatasource(TableState.data.filter((d) => !d.IsValid));
    } else {
      setFinalDatasource(TableState.data);
    }
  }, [onlyDiffed, TableState.data]);

  return (
    <>
      <Card
        title={
          <Space>
            {pageTitle}
            <Checkbox
              onChange={(e) => {
                setOnlyDiffed(e.target.checked);
              }}
            >
              仅显示审核错误
            </Checkbox>
          </Space>
        }
        extra={
          <Space>
            <Divider type="vertical" />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: `Api/Dyn-ddr/${apiBasePath}/GetList`,
                onTableRowSaveSuccess: (newColumns) => {
                  TableDispatch({
                    type: TableAction.columnsChange,
                    payload: {
                      columns: tableColumnBaseProcessor(columns, newColumns),
                    },
                  });
                },
              }}
            />
          </Space>
        }
      >
        <UniTable
          id={id}
          rowKey="uuid"
          loading={DataListReqLoading || SummaryReqLoading}
          columns={TableState.columns}
          dataSource={finalDatasource}
          forceColumnsUpdate
          dictionaryData={dictData}
          scroll={{ x: 'max-content' }}
          // pagination={false}
          rowClassName={(record) => {
            return !record?.IsValid ? 'remarked-row' : '';
          }}
          summary={() => (
            <Summary fixed>
              <Summary.Row>
                {TableState.columns
                  ?.filter((column) => column.visible)
                  .map((column: any, index) => {
                    if (index === 0) {
                      return (
                        <Summary.Cell index={0} key={'summary'} colSpan={1}>
                          <div style={{ textAlign: 'center' }}>总计</div>
                        </Summary.Cell>
                      );
                    }
                    const value = summaryData[column.dataIndex as string];
                    return (
                      <Summary.Cell index={index} key={column.dataIndex}>
                        <div style={{ textAlign: 'right' }}>
                          {typeof value === 'number'
                            ? column?.columnType === 'Decimal'
                              ? value.toFixed(column?.scale ?? 2)
                              : value
                            : ''}
                        </div>
                      </Summary.Cell>
                    );
                  })}
              </Summary.Row>
            </Summary>
          )}
        />
      </Card>
      <Drawer
        title={
          <Space>
            <span>日期范围：{searchParams?.dateRange?.join(' - ')}</span>
            <span>
              科室：{ModalState?.record?.DeptName ?? ModalState?.record?.Dept}
            </span>
          </Space>
        }
        width={'calc(100vw - 100px)'}
        onClose={() => {
          // 发出drawer关闭请求事件，让EditByDept处理
          Emitter.emit(CommonOdmEventConstants.DRAWER_CLOSE_REQUEST);
        }}
        open={ModalState.visible}
        destroyOnClose
      >
        <EditByDeptComponent
          propsSearchParams={{
            Sdate: searchParams?.dateRange?.at(0),
            Edate: searchParams?.dateRange?.at(1),
            HospCode: searchParams?.hospCodes?.at(0),
            DeptCode: ModalState?.record?.DeptCode,
            needFetch: true,
          }}
        />
      </Drawer>
    </>
  );
};

export default DynamicManagement;
