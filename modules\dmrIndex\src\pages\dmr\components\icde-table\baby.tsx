import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import {
  UniDmrDragEditOnlyTable,
  UniDragEditTable,
  UniTable,
} from '@uni/components/src';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { dmrIcdeReport, dmrOperationReport } from '@/pages/dmr/network/save';
import { babyIcdeColumns } from '@/pages/dmr/columns';
import { Form, Modal } from 'antd';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import cloneDeep from 'lodash/cloneDeep';
import { isEmptyValues } from '@uni/utils/src/utils';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';

interface BabyIcdeDragTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];

  underConfiguration?: boolean;

  onChange?: (value: any) => void;

  formKeyPrefix?: string[];
}

interface IcdeItem {
  IcdeId?: number;
  id?: number | string;

  IcdeNameLabel?: string;
  IcdeName?: string;
  IcdeCode?: string;
  IcdeCond?: string;
  IcdeCondLabel?: string;

  IcdeOutcome?: string;

  UniqueId?: string;
}

const icdeCopyKeys =
  (window as any).externalConfig?.['dmr']?.icdeCopyKeys ?? [];

const copyKeys = !isEmptyValues(icdeCopyKeys)
  ? icdeCopyKeys
  : ['IcdeCond', 'IcdeOutcome'];

const icdeCopyFocusKey =
  (window as any).externalConfig?.['dmr']?.icdeCopyFocusKey ?? undefined;
const icdeDeleteConfirm =
  (window as any).externalConfig?.['dmr']?.icdeDeleteConfirm ?? false;

const clearKeysMap = {
  // 仅用于联动删除使用
  IcdeCode: ['IcdeName', 'IcdeCode'],
};

const BabyIcdeDragTable = (props: BabyIcdeDragTableProps) => {
  const itemRef = React.useRef<any>();

  const babyIcdeTableContainerRef = React.useRef(null);

  const [form] = Form.useForm();
  const actionRef = useRef<any>();

  const icdeDataSource =
    Form.useWatch([...props?.formKeyPrefix, 'icdeTable'], props?.form) ?? [];

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    icdeDataSource?.length,
  );

  const hotKeyToEvents = {
    ADD: (event) => {
      babyIcdeTableContainerRef?.current?.babyIcdeAdd();
    },
    COPY: (event) => {
      let id = event?.target?.id;
      let indexString = id?.split('#')?.at(2);
      let index = parseInt(indexString);

      if (index >= 0) {
        babyIcdeTableContainerRef?.current?.babyIcdeCopy({
          id: undefined,
          index: index,
        });
      }
    },
    DELETE: (event) => {
      let id = event?.target?.id;
      let indexString = id?.split('#')?.at(2);

      let index = parseInt(indexString);

      if (index > -1) {
        babyIcdeTableContainerRef?.current?.babyIcdeDelete(index);
      }
    },
    SCROLL_LEFT: (event) => {
      let tableBody = document.querySelector(
        "div[id='formItem#babyDiagnosisTable'] div[class='ant-table-body']",
      );
      tableBody?.scrollBy({
        left: -100,
        behavior: 'smooth',
      });
    },
    SCROLL_RIGHT: (event) => {
      let tableBody = document.querySelector(
        "div[id='formItem#babyDiagnosisTable'] div[class='ant-table-body']",
      );
      tableBody?.scrollBy({
        left: 100,
        behavior: 'smooth',
      });
    },
    SCROLL_UP: (event) => {
      let tableBody = document.querySelector(
        "div[id='formItem#babyDiagnosisTable'] div[class='ant-table-body']",
      );
      tableBody?.scrollBy({
        top: -50,
        behavior: 'smooth',
      });
    },
    SCROLL_DOWN: (event) => {
      let tableBody = document.querySelector(
        "div[id='formItem#babyDiagnosisTable'] div[class='ant-table-body']",
      );
      tableBody?.scrollBy({
        top: 50,
        behavior: 'smooth',
      });
    },
    UP: (event) => {
      Emitter.emit(getArrowUpDownEventKey('babyDiagnosisTable'), {
        event: event,
        type: 'UP',
        trigger: 'hotkey',
      });
    },
    DOWN: (event) => {
      console.log('DOWN', event);
      Emitter.emit(getArrowUpDownEventKey('babyDiagnosisTable'), {
        event: event,
        type: 'DOWN',
        trigger: 'hotkey',
      });
    },
  };

  useEffect(() => {
    setTableDataSourceSize(icdeDataSource?.length);
  }, [icdeDataSource]);

  const lineUpDownEvents = {
    LINE_UP: (event) => {
      console.log('LINE_UP', event);
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let focusId = undefined;
      if (index === 0) {
        focusId = itemId;
      } else {
        ids[2] = index - 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('babyDiagnosisTable'), {
        oldIndex: index,
        newIndex: index - 1,
        focusId: focusId,
      });
    },
    LINE_DOWN: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let dataSourceSize = props?.form?.getFieldValue([
        ...props?.formKeyPrefix,
        'icde-table',
      ]).length;
      let focusId = undefined;
      if (index === dataSourceSize - 1) {
        focusId = itemId;
      } else {
        ids[2] = index + 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('babyDiagnosisTable'), {
        oldIndex: index,
        newIndex: index + 1,
        focusId: focusId,
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      { ...hotKeyToEvents, ...lineUpDownEvents }?.[item?.type],
      {
        preventDefault: true,
        enabled: true,
        enableOnFormTags: true,
        enableOnContentEditable: true,
      },
    );
  });

  // useEffect(() => {
  //   let columns = mergeColumnsInDmrTable(
  //     [],
  //     babyIcdeColumns,
  //     'BabyIcdeDragTable',
  //   );
  //
  //   setTableColumns(columns);
  // }, [props?.columns]);

  useEffect(() => {
    setTimeout(() => {
      waitFocusElementRefocusBySelector(waitFocusId);
    }, 100);
  }, [waitFocusId]);

  useEffect(() => {
    // delete事件
    Emitter.on(getDeletePressEventKey('babyDiagnosisTable'), (itemId) => {
      // key 包含 index 和 其他的东西
      console.log('babyDiagnosisTable', itemId);
      let itemIds = itemId?.split('#');
      let index = parseInt(itemIds?.at(2));
      let key = itemIds?.at(1);

      let clearKeys = [key];
      if (clearKeysMap[key]) {
        clearKeys = clearKeysMap[key];
      }
      clearValuesByKeys(clearKeys, index);

      // 定位到当前这个
      setTimeout(() => {
        document.getElementById(itemId)?.focus();
      }, 100);
    });

    Emitter.on(getArrowUpDownEventKey('babyDiagnosisTable'), (payload) => {
      let type = payload?.type;
      const icdeDataSource = props?.form?.getFieldValue([
        ...props?.formKeyPrefix,
        'icde-table',
      ]);
      console.log('payload', payload);
      if (
        payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
        payload?.trigger === 'hotkey'
      ) {
        // 表示是 下拉框 需要定制
        return;
      }

      payload?.event?.stopPropagation();

      let { nextIndex, activePaths } = calculateNextIndex(type);
      if (type === 'UP') {
        if (nextIndex < 0) {
          nextIndex = undefined;
        }
      }

      if (type === 'DOWN') {
        if (nextIndex > icdeDataSource?.length - 1) {
          nextIndex = undefined;
        }
      }

      if (nextIndex !== undefined) {
        activePaths[2] = nextIndex.toString();
        document.getElementById(activePaths?.join('#'))?.focus();
      }
    });

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('babyDiagnosisTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      resizeObserver?.disconnect();

      Emitter.off(getDeletePressEventKey('babyDiagnosisTable'));
      Emitter.off(getArrowUpDownEventKey('babyDiagnosisTable'));
    };
  }, []);

  React.useImperativeHandle(babyIcdeTableContainerRef, () => {
    return {
      babyIcdeAdd: onBabyIcdeAdd,
      babyIcdeDelete: onBabyIcdeItemDelete,
      babyIcdeCopy: onBabyIcdeItemCopy,
    };
  });

  const onBabyIcdeAdd = () => {
    let rowData = {
      id: Math.round(Date.now() / 1000),
      UniqueId: uuidv4(),
    };

    let tableData =
      props?.form?.getFieldValue([...props?.formKeyPrefix, 'icde-table']) ?? [];

    tableData.splice(tableData.length, 0, rowData);

    props?.form?.setFieldValue(
      [...props?.formKeyPrefix, 'icde-table'],
      cloneDeep(tableData),
    );
    triggerFormValueChangeEvent();

    setWaitFocusId(
      `div[id=BabyContent-${
        parseInt(props?.formKeyPrefix?.at(1)) + 1
      }] tbody > tr:nth-child(${tableData?.length}) > td input`,
    );
    setTableDataSourceSize(tableData?.length);
  };

  const onBabyIcdeItemDelete = (index) => {
    if (icdeDeleteConfirm) {
      Modal.confirm({
        title: `确定删除第${index + 1} 条新生儿诊断数据？`,
        content: '',
        zIndex: 10055,
        getContainer: () => document.getElementById('baby-form-container'),
        onOk: () => {
          onIcdeItemDelete(index);
        },
      });
    } else {
      onIcdeItemDelete(index);
    }
  };

  const onBabyIcdeItemCopy = (payload: any) => {
    let tableData = props?.form?.getFieldValue([
      ...props?.formKeyPrefix,
      'icde-table',
    ]);
    let currentCopyItem = payload?.['id']
      ? form.getFieldValue(payload?.['id'])
      : tableData?.[payload?.index];
    let index = payload?.index;

    let copiedItem = {
      id: Math.round(Date.now() / 1000),
      UniqueId: uuidv4(),
    };

    copyKeys?.forEach((key) => {
      copiedItem[key] = currentCopyItem[key];
    });

    tableData.splice(index + 1, 0, copiedItem);

    if (!isEmptyValues(icdeCopyFocusKey)) {
      setWaitFocusId(
        `div[id=BabyContent-${
          parseInt(props?.formKeyPrefix?.at(1)) + 1
        }] tbody > tr:nth-child(${
          index + 2
        }) > td input[id*=${icdeCopyFocusKey}]`,
      );
    } else {
      setWaitFocusId(
        `div[id=BabyContent-${
          parseInt(props?.formKeyPrefix?.at(1)) + 1
        }] tbody > tr:nth-child(${index + 2}) > td input`,
      );
    }
    setTableDataSourceSize(tableData?.length);

    // 更新form
    props?.form?.setFieldValue(
      [...props?.formKeyPrefix, 'icde-table'],
      cloneDeep(tableData),
    );
    triggerFormValueChangeEvent();
  };

  const onIcdeItemDelete = (index: number) => {
    if (index > -1) {
      let tableData = props?.form?.getFieldValue([
        ...props?.formKeyPrefix,
        'icde-table',
      ]);
      tableData.splice(index, 1);

      // 更新form
      props?.form?.setFieldValue(
        [...props?.formKeyPrefix, 'icde-table'],
        cloneDeep(tableData),
      );
      triggerFormValueChangeEvent();

      // 删除的时候 给出当前那个选中的
      // 当前选中的意思是表格中的上一个存在即是上表格中上一个的最后一个
      // 表格中不存在即写第0个的icdeName 建议写死
      let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
      if (dataItems?.length > 0) {
        setWaitFocusId(
          `div[id=BabyContent-${
            parseInt(props?.formKeyPrefix?.at(1)) + 1
          }] tbody > tr:nth-child(${
            index >= dataItems.length - 1 ? dataItems.length : index + 1
          }) > td input`,
        );
      }
      setTableDataSourceSize(tableData?.length);
    }
  };

  const clearValuesByKeys = (keys, index) => {
    const icdeDataSource = props?.form?.getFieldValue([
      ...props?.formKeyPrefix,
      'icde-table',
    ]);
    let formItemId = icdeDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue([
      ...props?.formKeyPrefix,
      'icde-table',
    ]);
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue(
      [...props?.formKeyPrefix, 'icde-table'],
      cloneDeep(tableData),
    );
    triggerFormValueChangeEvent();
  };

  return (
    <UniDmrDragEditOnlyTable
      {...props}
      formItemContainerClassName={'form-content-item-container'}
      itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
      form={form}
      key={`${props?.id}-${parseInt(props?.formKeyPrefix?.at(1)) + 1}`}
      id={`${props?.id}-${parseInt(props?.formKeyPrefix?.at(1)) + 1}`}
      tableId={`${props?.id}-${parseInt(props?.formKeyPrefix?.at(1)) + 1}`}
      formKey={'babyDiagnosisTable'}
      forceColumnsUpdate={props?.underConfiguration ?? false}
      scroll={{
        x: 'max-content',
      }}
      pagination={false}
      className={`table-container ${props?.className || ''}`}
      // dataSource={(props?.form?.getFieldValue([...props?.formKeyPrefix, 'icde-table']) ?? [])
      //   ?.filter((item) => item?.id !== 'ADD')
      //   ?.map((item) => {
      //     if (!item['id']) {
      //       item['id'] = Math.round(Date.now() / 1000);
      //     }
      //
      //     return item;
      //   })
      //   ?.concat({
      //     id: 'ADD',
      //   })}
      dataSource={(
        props?.form?.getFieldValue([...props?.formKeyPrefix, 'icde-table']) ??
        []
      )
        ?.filter((item) => item?.id !== 'ADD')
        ?.map((item) => {
          if (!item['id']) {
            item['id'] = Math.round(Date.now() / 1000);
          }

          return item;
        })
        ?.concat({
          id: 'ADD',
        })}
      rowKey={'id'}
      onValuesChange={(recordList, changedValues) => {
        // setIcdeDataSource(recordList)

        props?.form?.setFieldValue(
          [...props?.formKeyPrefix, 'icde-table'],
          recordList,
        );
        triggerFormValueChangeEvent();
      }}
      onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
        // setIcdeDataSource(tableData);

        props?.form?.setFieldValue(
          [...props?.formKeyPrefix, 'icde-table'],
          cloneDeep(tableData),
        );
        if (focusId) {
          setTimeout(() => {
            waitFocusElementRefocus(focusId);
          }, 100);
        }
        triggerFormValueChangeEvent();
      }}
      columns={babyIcdeColumns(babyIcdeTableContainerRef)}
    />
  );
};

export default React.memo(BabyIcdeDragTable);
