import React, { useEffect, useState } from 'react';
import { Card, Col, Row, Statistic, Divider } from 'antd';
import { Link, useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import CardWithBtns from '@uni/components/src/cardWithBtns';
import {
  TableColumns,
  BasePageProps,
  RespVO,
} from '@uni/commons/src/interfaces';
import UniEcharts from '@uni/components/src/echarts';
import CardEchart from '@uni/components/src/cardEchart';
import {
  qcResultOfEntityPieOption,
  qcResultOfEntityVerticalBarOption,
} from './chart.opts';
import _ from 'lodash';
import { useModel } from '@@/plugin-model/useModel';

const QcRuleTypeAnalCard = (props) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [clickItem, setClickItem] = useState<any>({
    RuleType: null,
    RuleTypeCnt: null,
    RuleTypeRat: null,
    Details: [],
  });

  const [ruleTypeAnalData, setRuleTypeAnalData] = useState<any>({
    ProblematicRecordCnt: null,
    TotalRecordCnt: null,
    Reviews: [],
  });

  // 常见问题分析
  const {
    data: qcRuleTypeAnalData,
    loading: getQcRuleTypeAnalLoading,
    run: getQcRuleTypeAnalReq,
  } = useRequest(
    () =>
      uniCommonService(props.api, {
        method: 'POST',
        data: {
          Sdate: props.Sdate,
          Edate: props.Edate,
          HospCode: props.hospitalCodes,
          CliDepts: props.cliDepts,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.at(0)?.['Reviews'].length)
            setClickItem(
              _.orderBy(res.data[0]['Reviews'], ['RuleTypeCnt'], 'desc')[0],
            );
          setRuleTypeAnalData({
            ProblematicRecordCnt: res.data[0]?.['ProblematicRecordCnt'],
            TotalRecordCnt: res.data[0]?.['TotalRecordCnt'],
            Reviews: res.data[0]?.['Reviews'],
          });
        }
      },
    },
  );

  const {
    data: qcRuleTypeAnalCols,
    loading: getQcRuleTypeAnalColsLoading,
    run: getQcRuleTypeAnalColsReq,
  } = useRequest(
    () =>
      uniCommonService(props.api, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  useEffect(() => {
    getQcRuleTypeAnalColsReq();
  }, []);

  useEffect(() => {
    if (props?.Sdate && props?.Edate) {
      if (props?.type === 'hosp' && props?.hospitalCodes?.length) {
        getQcRuleTypeAnalReq();
      }
      if (props?.type === 'dept' && props?.cliDepts?.length) {
        getQcRuleTypeAnalReq();
      }
    }
  }, [props.Sdate, props.Edate, props.cliDepts, props.hospitalCodes]);

  return (
    <>
      <Col span={14}>
        <CardWithBtns
          title={'常见问题分析'}
          content={
            <UniEcharts
              height={250}
              elementId="Pie"
              loading={getQcRuleTypeAnalLoading}
              options={
                (ruleTypeAnalData.Reviews &&
                  qcResultOfEntityPieOption(
                    ruleTypeAnalData.Reviews,
                    'RuleType',
                  )) ||
                {}
              }
              mouseEvents={{
                eventName: 'click',
                query: 'series',
                handler: (event) => {
                  setClickItem(event.data);
                },
              }}
            />
          }
          needExport={true}
          exportTitle={'常见问题分析'}
          exportData={ruleTypeAnalData?.Reviews}
          exportColumns={tableColumnBaseProcessor(
            [],
            [
              {
                data: 'RuleType',
                title: '分类名称',
                visible: true,
                name: 'RuleType',
              },
              {
                data: 'RuleTypeCnt',
                title: '例数',
                visible: true,
              },
              {
                data: 'RuleTypeRate',
                title: '占比',
                visible: true,
              },
            ],
          )}
          needModalDetails={true}
          onRefresh={() => {
            getQcRuleTypeAnalReq();
          }}
        />
      </Col>
      <Col span={10}>
        <CardWithBtns
          title={
            <>
              <span style={{ fontSize: '13px' }}>常见问题分析 </span>
              {clickItem.RuleType}
            </>
          }
          content={
            <UniEcharts
              height={250}
              elementId="VerticalBar"
              loading={getQcRuleTypeAnalLoading}
              options={
                (clickItem &&
                  clickItem.Details.length &&
                  qcResultOfEntityVerticalBarOption(
                    clickItem.Details,
                    'SubType',
                  )) ||
                {}
              }
            />
          }
          needExport={true}
          exportTitle={`常见问题分析-${clickItem.RuleType}`}
          exportData={clickItem.Details}
          exportColumns={tableColumnBaseProcessor(
            [],
            [
              {
                data: 'SubType',
                title: '类别名称',
                visible: true,
              },
              {
                data: 'SubTypeCnt',
                title: '例数',
                visible: true,
              },
              {
                data: 'SubTypeRate',
                title: '占比',
                visible: true,
              },
            ],
          )}
          dictData={globalState.dictData}
          needModalDetails={true}
          onRefresh={() => {
            getQcRuleTypeAnalReq();
          }}
        />
      </Col>
    </>
  );
};
export default QcRuleTypeAnalCard;
