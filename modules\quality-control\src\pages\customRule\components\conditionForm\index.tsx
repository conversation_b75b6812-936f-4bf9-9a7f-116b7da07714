import {
  ProForm,
  ProFormCheckbox,
  ProFormDependency,
  ProFormDigit,
  ProFormDigitRange,
  ProFormGroup,
  ProFormRadio,
  ProFormSelect,
  ProFormSwitch,
  ProFormSegmented,
} from '@uni/components/src/pro-form';
import './index.less';
import { useRequest } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import _ from 'lodash';
import { mockConditionTemplate, mockExtraConditions } from './constants';
import { recurrenceFindOne } from '../../utils';
import { Col, Form, Row, Button, message, Tooltip, Tabs } from 'antd';
import DebounceSelect from '../debounceSelect';
import SelectFormList from '../selectFormList';
import {
  qualityControlRuleEventConstants,
  StatsAnalysisEventConstant,
} from '../../constants';
import Select from '@uni/components/src/select/AntdSelect';
import { findContentsByKey, generateExpression } from '../../utils';
import { generateJsonLogicExpression, inputJsonLogicEnum } from './utils';
import { useEffect, useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import CustomizedJsonLogic from '../customizedJsonLogicModal/index';
// import { parseScript } from 'meriyah';
// {...props}
// optionLabelProp={'Code'}
// dictionaryModuleGroup={
//   (props?.fieldDefinition as any)?.dictionaryModuleGroup
// }
// parentId={'combined-query-container'}

const ConditionForm = ({ formMapRef, modalState }) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [userInputs, setUserInputs] = useState({
    minAge: undefined,
    maxAge: undefined,
    minDays: undefined,
    maxDays: undefined,
    minBabyAge: undefined,
    maxBabyAge: undefined,
  });

  const [tabActiveKey, setTabActiveKey] = useState('default');

  // debounce select
  // Api/Dmr/DmrSearch/IcdeUnionCategory?Keyword=&SkipCount=0&MaxResultCount=100&IsOtp=true
  // Api/Dmr/DmrSearch/IcdeUnionCategory?Keyword=y&SkipCount=0&MaxResultCount=100&IsOtp=true
  // 诊断编码
  async function fetchIcdeSelect(keyword: string): Promise<any[]> {
    if (!keyword) return;
    return await uniCommonService('Api/Dmr/DmrSearch/IcdeUnionCategory', {
      params: {
        IsOtp: true,
        KeyWord: keyword,
        SkipCount: 0,
        MaxResultCount: 100,
      },
    });
  }

  // 病理（ + 肿瘤）诊断
  async function fetchIcdePathosSelect(keyword: string): Promise<any[]> {
    if (!keyword) return;
    return await uniCommonService('Api/Dmr/DmrSearch/Icde', {
      params: {
        IsPathoAndMor: true,
        KeyWord: keyword,
        SkipCount: 0,
        MaxResultCount: 100,
      },
    });
  }

  // Api/Dmr/DmrSearch/OperUnionCategory?Keyword=&SkipCount=0&HasInsurCompare=true&HasHqmsCompare=true&MaxResultCount=100
  async function fetchOperSelect(keyword: string): Promise<any[]> {
    if (!keyword) return;
    return await uniCommonService('Api/Dmr/DmrSearch/OperUnionCategory', {
      params: {
        HasInsurCompare: true,
        HasHqmsCompare: true,
        KeyWord: keyword,
        SkipCount: 0,
        MaxResultCount: 100,
      },
    });
  }

  useEffect(() => {
    setTabActiveKey(modalState?.record?.DisplayExprStructure ?? 'default');
  }, [modalState?.record?.DisplayExprStructure]);

  return (
    <>
      {/* <ProFormSelect
        name="conditionTemplate"
        label="规则模板选择"
        placeholder="请选择规则模板"
        rules={[
          {
            required: true,
            message: '请选择规则模板',
          },
        ]}
        options={mockConditionTemplate as any[]}
        fieldProps={{
          fieldNames: {
            label: 'name',
            value: 'value',
            options: 'children',
          },
          onChange: (value) => {
            // 赋值默认值给List
            let node = recurrenceFindOne(value, 'value', mockConditionTemplate);
            if (node?.result?.contents?.length > 0) {
              let itemIndex = node?.result?.contents?.findIndex(
                (content) => content?.contentType === 'multi',
              );
              if (itemIndex > -1) {
                formMapRef?.current
                  ?.at(0)
                  ?.current.setFieldValue(
                    [
                      value,
                      itemIndex === 0 ? 'A' : 'B',
                      node?.result?.contents?.at(itemIndex)?.type,
                    ],
                    [{ value: undefined }, { value: undefined }],
                  );
              }
            }
          },
        }}
        colProps={{ span: 8 }}
      /> */}
      {/* <Row>
        <Col span={24}>
          <ProFormSegmented
            name="DisplayStructure"
            label="模板类型选择"
            valueEnum={{
              default: '默认模板',
              customized: '自定义',
            }}
            initialValue="default"
          />
        </Col>
      </Row> */}

      <ProFormDependency name={['conditionTemplate']}>
        {({ conditionTemplate }) => {
          let node = recurrenceFindOne(
            conditionTemplate,
            'value',
            mockConditionTemplate,
          );
          console.log(node);
          if (conditionTemplate && node?.isFind) {
            return (
              <div className="condition_components_container">
                <Tabs
                  size="small"
                  tabBarExtraContent={
                    <Button
                      type="primary"
                      // style={{
                      //   position: 'absolute',
                      //   right: '20px',
                      //   top: '20px',
                      // }}
                      onClick={() => {
                        // 测试按钮也需要做一个根据tab得判断
                        if (tabActiveKey === 'default') {
                          let key = formMapRef?.current
                            ?.at(0)
                            ?.current.getFieldsValue()?.conditionTemplate;
                          console.log('key', key);
                          let diagOperData = formMapRef?.current
                            ?.at(0)
                            ?.current.getFieldsValue()?.[key];
                          let extraData = formMapRef?.current
                            ?.at(0)
                            ?.current.getFieldsValue()?.extra;

                          let jsonLogicExpr = generateExpression(
                            key,
                            diagOperData,
                            extraData,
                          );
                          if (!jsonLogicExpr) {
                            message.error('请检查条件配置');
                          } else {
                            // OPEN_TEST_MODAL
                            Emitter.emit(
                              qualityControlRuleEventConstants.OPEN_TEST_MODAL,
                              jsonLogicExpr,
                            );
                          }
                        } else {
                          // 自定义
                          Emitter.emit(
                            StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE,
                            { doTest: true },
                          );
                        }
                      }}
                    >
                      测试
                    </Button>
                  }
                  onTabClick={(key) => {
                    Emitter.emit(
                      qualityControlRuleEventConstants.ACTIVE_TAB_CHANGE,
                      key,
                    );
                    setTabActiveKey(key);
                  }}
                  activeKey={tabActiveKey}
                  items={[
                    {
                      label: '默认模板',
                      key: 'default',
                      children: (
                        <div>
                          {/* 必选条件 */}
                          {node?.result?.contents?.length > 0 && (
                            <div className="condition_required_items_container">
                              <ProFormGroup>
                                <Col
                                  span={24}
                                  style={{
                                    display: 'flex',
                                    alignItems: 'baseline',
                                  }}
                                >
                                  <label
                                    className="required_label"
                                    style={{ width: '84px' }}
                                  >
                                    必选条件：
                                  </label>
                                  <Row style={{ width: 'calc(100% - 84px)' }}>
                                    {node?.result?.contents?.map(
                                      (item, index) => {
                                        if (item.contentType === 'none') {
                                          return (
                                            <Col
                                              span={index === 0 ? 10 : 10}
                                              style={{ marginLeft: '10px' }}
                                              key={index}
                                            >
                                              <label
                                                style={{
                                                  display: 'block',
                                                  paddingTop: '5px',
                                                }}
                                              >
                                                {item.name}
                                              </label>
                                            </Col>
                                          );
                                        } else if (
                                          item.contentType === 'multi'
                                        ) {
                                          return (
                                            <Col
                                              span={10}
                                              key={index}
                                              style={{
                                                display: 'flex',
                                                alignItems: 'baseline',
                                                marginLeft: '10px',
                                              }}
                                            >
                                              <label
                                                style={{
                                                  width: `${
                                                    item.name?.length * 14 + 14
                                                  }px`,
                                                  paddingTop: '2px',
                                                }}
                                              >{`${item.name}：`}</label>
                                              <SelectFormList
                                                item={item}
                                                itemIndex={index}
                                                nodeResult={node?.result}
                                              />
                                            </Col>
                                          );
                                        } else {
                                          console.log('item', item);
                                          return (
                                            <Col
                                              span={index === 0 ? 10 : 11}
                                              style={{
                                                marginLeft: '10px',
                                                display: 'flex',
                                                alignItems: 'baseline',
                                              }}
                                              key={index}
                                            >
                                              {item?.nameSelectable && (
                                                <>
                                                  <Form.Item
                                                    // noStyle
                                                    name={
                                                      index === 0
                                                        ? [
                                                            node?.result?.value,
                                                            'A',
                                                            'title',
                                                          ]
                                                        : [
                                                            node?.result?.value,
                                                            'B',
                                                            'title',
                                                          ]
                                                    }
                                                    label={item?.name}
                                                    labelCol={{
                                                      flex: `${
                                                        item.name?.length * 14
                                                      }px`,
                                                    }}
                                                    initialValue={
                                                      item?.defaultNameValue
                                                    }
                                                  >
                                                    <Select
                                                      options={item.nameOpts}
                                                      fieldNames={{
                                                        label: 'name',
                                                      }}
                                                      style={{ width: '100px' }}
                                                      bordered={false}
                                                    />
                                                  </Form.Item>
                                                  ：
                                                </>
                                              )}
                                              <Form.Item
                                                label={
                                                  !item?.nameSelectable &&
                                                  `${item.name}：`
                                                }
                                                name={
                                                  index === 0
                                                    ? [
                                                        node?.result?.value,
                                                        'A',
                                                        item.type,
                                                      ]
                                                    : [
                                                        node?.result?.value,
                                                        'B',
                                                        item.type,
                                                      ]
                                                }
                                                labelCol={
                                                  item?.nameSelectable
                                                    ? { flex: '114px' }
                                                    : {
                                                        flex: `${
                                                          item.name?.length *
                                                            14 +
                                                          14
                                                        }px`,
                                                      }
                                                }
                                                wrapperCol={{ flex: 'auto' }}
                                                hidden={
                                                  item.contentType === 'none'
                                                }
                                                style={
                                                  item?.nameSelectable
                                                    ? {
                                                        width: `calc(100% - 114px - ${
                                                          item.name?.length * 14
                                                        }px)`,
                                                      }
                                                    : { width: '100%' }
                                                }
                                              >
                                                <DebounceSelect
                                                  placeholder={`请输入${
                                                    item.type === 'diag'
                                                      ? '诊断'
                                                      : item.type ===
                                                        'diagPathos'
                                                      ? '病理诊断'
                                                      : '手术'
                                                  }编码或名称`}
                                                  fetchOptions={
                                                    item.type === 'diag'
                                                      ? fetchIcdeSelect
                                                      : item.type ===
                                                        'diagPathos'
                                                      ? fetchIcdePathosSelect
                                                      : fetchOperSelect
                                                  }
                                                  fieldNames={{
                                                    label: 'Name',
                                                    value: 'Code',
                                                  }}
                                                  optionLabelProp="value"
                                                  mode={'multiple'}
                                                />
                                              </Form.Item>
                                            </Col>
                                          );
                                        }
                                      },
                                    )}
                                  </Row>
                                </Col>
                              </ProFormGroup>
                            </div>
                          )}
                          {/* 补充条件 */}
                          <div className="condition_components_extra_condition_checkbox_container">
                            <ProFormCheckbox.Group
                              name="extraConditionOpts"
                              label="补充条件："
                              labelCol={{ flex: '84px' }}
                              wrapperCol={{ flex: 'auto' }}
                              options={mockExtraConditions}
                            />
                          </div>

                          <ProFormDependency name={['extraConditionOpts']}>
                            {({ extraConditionOpts }) => {
                              if (extraConditionOpts?.length > 0) {
                                return (
                                  <div className="condition_components_extra_input_container">
                                    <ProFormGroup>
                                      {extraConditionOpts?.findIndex(
                                        (d) => d === 'gender',
                                      ) > -1 && (
                                        <ProFormSelect
                                          label="性别："
                                          name={['extra', 'PatSex']}
                                          options={
                                            globalState?.dictData?.Dmr?.XB
                                          }
                                          fieldProps={{
                                            allowClear: true,
                                            mode: 'multiple',
                                            fieldNames: {
                                              label: 'Name',
                                              value: 'Code',
                                            },
                                          }}
                                          colProps={{ span: 8 }}
                                          labelCol={{ flex: '45px' }}
                                          wrapperCol={{ flex: 'auto' }}
                                        />
                                      )}
                                      {extraConditionOpts?.findIndex(
                                        (d) => d === 'age',
                                      ) > -1 && (
                                        <Col span={8} className="in_period_col">
                                          <ProFormDigitRange
                                            label={
                                              <label className="in_period_label">
                                                年龄(岁)
                                                <Tooltip title="X <= 岁 < Y">
                                                  <QuestionCircleOutlined
                                                    style={{
                                                      color:
                                                        'rgba(0, 0, 0, 0.45)',
                                                      marginLeft: '5px',
                                                    }}
                                                  />
                                                </Tooltip>
                                              </label>
                                            }
                                            name={['extra', 'PatAge']}
                                            colProps={{ span: 24 }}
                                            // tooltip={`X <= 岁 < Y`}
                                            fieldProps={{
                                              min: 1,
                                              precision: 0,
                                              controls: false,
                                              // onChange: (value) => {
                                              //   setUserInputs({
                                              //     ...userInputs,
                                              //     minAge: value?.at(0),
                                              //     maxAge: value?.at(1),
                                              //   });
                                              // },
                                            }}
                                            labelCol={{ flex: '83px' }}
                                            wrapperCol={{ flex: 'auto' }}
                                          />
                                        </Col>
                                      )}
                                      {extraConditionOpts?.findIndex(
                                        (d) => d === 'babyAge',
                                      ) > -1 && (
                                        <Col span={8} className="in_period_col">
                                          <ProFormDigitRange
                                            label={
                                              <label className="in_period_label">
                                                不足一周岁年龄(天)
                                                <Tooltip title="0 <= 天数 < 365">
                                                  <QuestionCircleOutlined
                                                    style={{
                                                      color:
                                                        'rgba(0, 0, 0, 0.45)',
                                                      marginLeft: '5px',
                                                    }}
                                                  />
                                                </Tooltip>
                                              </label>
                                            }
                                            name={['extra', 'NwbAge']}
                                            colProps={{ span: 24 }}
                                            fieldProps={{
                                              min: 0,
                                              max: 365,
                                              precision: 0,
                                              controls: false,
                                              // onChange: (value) => {
                                              //   setUserInputs({
                                              //     ...userInputs,
                                              //     minBabyAge: value?.at(0),
                                              //     maxBabyAge: value?.at(1),
                                              //   });
                                              // },
                                            }}
                                            labelCol={{ flex: '153px' }}
                                            wrapperCol={{ flex: 'auto' }}
                                          />
                                        </Col>
                                      )}
                                      {extraConditionOpts?.findIndex(
                                        (d) => d === 'dischargeType',
                                      ) > -1 && (
                                        <ProFormSelect
                                          label="离院方式："
                                          name={['extra', 'OutType']}
                                          options={
                                            globalState?.dictData?.Dmr?.LYFS
                                          }
                                          fieldProps={{
                                            allowClear: false,
                                            mode: 'multiple',
                                            fieldNames: {
                                              label: 'Name',
                                              value: 'Code',
                                            },
                                          }}
                                          colProps={{ span: 8 }}
                                          labelCol={{ flex: '70px' }}
                                          wrapperCol={{ flex: 'auto' }}
                                        />
                                      )}
                                      {extraConditionOpts?.findIndex(
                                        (d) => d === 'inHospitalDays',
                                      ) > -1 && (
                                        <Col span={8} className="in_period_col">
                                          <ProFormDigitRange
                                            label={
                                              <label className="in_period_label">
                                                住院天数
                                                <Tooltip title="1 <= 住院天数 < Y">
                                                  <QuestionCircleOutlined
                                                    style={{
                                                      color:
                                                        'rgba(0, 0, 0, 0.45)',
                                                      marginLeft: '5px',
                                                    }}
                                                  />
                                                </Tooltip>
                                              </label>
                                            }
                                            name={['extra', 'InPeriod']}
                                            className="in_period_item"
                                            fieldProps={{
                                              min: 1,
                                              precision: 0,
                                              controls: false,
                                              // onChange: (value) => {
                                              //   setUserInputs({
                                              //     ...userInputs,
                                              //     minDays: value?.at(0),
                                              //     maxDays: value?.at(1),
                                              //   });
                                              // },
                                            }}
                                            colProps={{ span: 24 }}
                                            labelCol={{ flex: '89px' }}
                                            wrapperCol={{ flex: 'auto' }}
                                          />
                                        </Col>
                                      )}
                                      {extraConditionOpts?.findIndex(
                                        (d) => d === 'cliDept',
                                      ) > -1 && (
                                        <ProFormSelect
                                          label="出院科室："
                                          name={['extra', 'OutDept']}
                                          options={
                                            globalState?.dictData?.CliDepts
                                          }
                                          fieldProps={{
                                            allowClear: true,
                                            mode: 'multiple',
                                            fieldNames: {
                                              label: 'Name',
                                              value: 'Code',
                                            },
                                          }}
                                          colProps={{ span: 8 }}
                                          labelCol={{ flex: '70px' }}
                                          wrapperCol={{ flex: 'auto' }}
                                        />
                                      )}
                                    </ProFormGroup>
                                  </div>
                                );
                              }
                            }}
                          </ProFormDependency>
                        </div>
                      ),
                    },
                    {
                      label: '自定义',
                      key: 'customized',
                      forceRender: true,
                      children: (
                        <div style={{ marginTop: 5 }}>
                          <CustomizedJsonLogic ModalState={modalState} />
                        </div>
                      ),
                    },
                  ]}
                />
              </div>
            );
          }
          return <></>;
        }}
      </ProFormDependency>
    </>
  );
};

export default ConditionForm;
