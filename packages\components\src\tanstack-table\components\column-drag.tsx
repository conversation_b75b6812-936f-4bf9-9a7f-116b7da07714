import {
  restrictToParentElement,
  restrictToVerticalAxis,
} from '@dnd-kit/modifiers';
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  DragCancelEvent,
  DragEndEvent,
  DragMoveEvent,
  DragOverEvent,
  DragStartEvent,
} from '@dnd-kit/core/dist/types';
import React from 'react';
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { CommonRow, CommonRowProps } from './row-cell';
import classNames from 'classnames';
import isEqual from 'lodash/isEqual';
import { AutoScrollOptions } from '@dnd-kit/core/dist/hooks/utilities';
import { rectIntersection } from '../rectIntersection';

export const TableWrapperProps = [
  'onDragStart',
  'onDragMove',
  'onDragOver',
  'onDragEnd',
  'onDragCancel',
  'autoScroll',

  'onHeaderDragStart',
  'onHeaderDragMove',
  'onHeaderDragOver',
  'onHeaderDragEnd',
  'onHeaderDragCancel',
];

export interface SortableTableWrapperProps {
  children?: React.ReactNode;

  onDragStart?(event: DragStartEvent): void;
  onDragMove?(event: DragMoveEvent): void;
  onDragOver?(event: DragOverEvent): void;
  onDragEnd?(event: DragEndEvent, newData?: any[], focusId?: string): void;
  onDragCancel?(event: DragCancelEvent): void;

  onDragExtra?: (data: any[]) => void;
  onDragEndPre?: (active: any, over: any) => Promise<boolean>;

  autoScroll?: boolean | AutoScrollOptions;
}

interface SortableBodyWrapperProps {
  items: string[];

  children?: React.ReactNode;
}

interface SortableRowProps extends CommonRowProps {}

export const SortableTableWrapper = (props: SortableTableWrapperProps) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // https://docs.dndkit.com/api-documentation/sensors/pointer#activation-constraints
        distance: 1,
      },
    }),
  );

  return (
    <DndContext
      sensors={sensors}
      // collisionDetection={closestCenter}
      // collisionDetection={verticalCollisionDetection}
      collisionDetection={rectIntersection}
      modifiers={[restrictToVerticalAxis, restrictToParentElement]}
      onDragEnd={props?.onDragEnd}
      onDragMove={props?.onDragMove}
      onDragStart={props?.onDragStart}
      autoScroll={props?.autoScroll}
    >
      {props?.children}
    </DndContext>
  );
};

export const SortableBodyWrapper = (props: SortableBodyWrapperProps) => {
  return (
    <SortableContext
      items={props?.items}
      strategy={verticalListSortingStrategy}
    >
      {props?.children}
    </SortableContext>
  );
};

const sortableRowPropsEqual = (
  oldProps: SortableRowProps,
  newProps: SortableRowProps,
) => {
  // 为了保证如果不存在 数据的情况下 ADD必须出现
  if (newProps?.row?.original?.id === 'ADD') {
    return false;
  }
  return isEqual(oldProps?.row?.original, newProps?.row?.original);
};

export const SortableRow = React.memo((props: SortableRowProps) => {
  console.log('SortableRow', props);

  let canRowSortableResult = props?.canRowSortable
    ? props?.canRowSortable(props?.row)
    : true;

  // 当且仅当是ADD的时候 不 使用sortable
  if (props?.row?.original?.id === 'ADD' || !canRowSortableResult) {
    return <CommonRow {...props} />;
  }

  let hasSortHandle = false;

  React.Children.forEach(props?.children, (child) => {
    if ((child as React.ReactElement).key?.toString()?.includes('-sort')) {
      hasSortHandle = true;
    }
  });

  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props?.row?.original?.id,
  });
  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(
      transform && { ...transform, scaleY: 1, x: 0 },
    ),
    transition,
    ...(isDragging && props?.className !== 'ant-table-placeholder'
      ? { position: 'relative', zIndex: 999 }
      : {}),
  };

  if (hasSortHandle && props?.rowBaseSort !== true) {
    return (
      <tr
        data-row-key={props?.row?.original?.[props?.rowKey]}
        {...props}
        ref={setNodeRef}
        style={style}
        {...attributes}
        key={props?.row?.original?.[props?.rowKey]}
        data-index={props?.row.index}
        className={classNames(
          'ant-table-row',
          typeof props?.rowClassName === 'string'
            ? props?.rowClassName
            : props?.rowClassName
            ? props?.rowClassName(props?.row?.original, props?.row?.index)
            : '',
          props?.className,
        )}
        {...props?.virtualizeProps}
        tabIndex={-1}
      >
        {React.Children.map(props?.children, (child) => {
          if (
            (child as React.ReactElement).key?.toString()?.includes('-sort')
          ) {
            return React.cloneElement(child as React.ReactElement, {
              children: (
                <div
                  ref={setActivatorNodeRef}
                  style={{
                    touchAction: 'none',
                    cursor: 'move',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  className={props?.formItemContainerClassName ?? ''}
                  {...listeners}
                >
                  {child}
                </div>
              ),
            });
          }
          return child;
        })}
      </tr>
    );
  } else {
    return (
      <tr
        data-row-key={props?.row?.original?.[props?.rowKey]}
        {...props}
        ref={setNodeRef}
        style={{ ...style, cursor: 'grab' }}
        {...attributes}
        {...listeners}
        key={props?.row?.original?.[props?.rowKey]}
        data-index={props?.row.index}
        className={classNames(
          'ant-table-row',
          typeof props?.rowClassName === 'string'
            ? props?.rowClassName
            : props?.rowClassName
            ? props?.rowClassName(props?.row?.original, props?.row?.index)
            : '',
          props?.className,
        )}
        {...props?.virtualizeProps}
        tabIndex={-1}
      >
        {props?.children}
      </tr>
    );
  }
}, sortableRowPropsEqual);
