import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useReducer,
  Reducer,
} from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import {
  Button,
  message,
  Card,
  Switch,
  Col,
  Progress,
  Row,
  Spin,
  Tag,
  Space,
  Form,
  Modal,
  Tabs,
  Tooltip,
  Divider,
} from 'antd';
import { Link, useRequest } from 'umi';
import UniTable from '@uni/components/src/table';
import { RespVO, TableResp } from '@uni/commons/src/interfaces';
import _ from 'lodash';
import { useModel } from '@@/plugin-model/useModel';
import {
  InitTableState,
  TableAction,
  modalReducer,
  tableReducer,
} from '@uni/reducers/src';
import {
  IReducer,
  IModalState,
  ITableState,
} from '@uni/reducers/src/interface';
import './index.less';
import { StatsAnalysisEventConstant, DiseaseTags } from '@/constants';
import { uniStatsSequenceService } from '@uni/services/src';

import {
  CombineQueryDetail,
  DetailColumnItem,
  MetricsAggregateItem,
  MetricsGroupItem,
  MetricsMetricItem,
} from '../combine-query/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import MetricColumnSettings from '@/components/queuryMetricColumnsSettings';
import { responseSelectedMetricsProcessor } from '../combine-query/processor';
import IconBtn from '@uni/components/src/iconBtn';
import DetailContainer from './detail-container';
import { ModalAction } from '@uni/reducers/src/modalReducer';
import { useLocalStorageState } from 'ahooks';
import ExpandableTable from '@/components/sequenceByCliDepts/index';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import {
  defaultSelectedMetricsProcessor,
  metricParamsProcessor,
} from '@/components/queuryMetricColumnsSettings/utils';
import { SettingOutlined } from '@ant-design/icons';
import { DetailColumnSettingContentConstants } from '@/components/queryDetailColumnsSettings/constants';
import { ExportIconBtn } from '@uni/components/src/index';
import { isEmptyValues } from '@uni/utils/src/utils';

const { CheckableTag } = Tag;

const TABLE_NAME = 'OmniCard';
export interface SelectedMetricItem {
  itemKey: string;
  id: string;
  customTitle?: string;
  columnSort?: number;
}

const DiseaseSequence = (props: any) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let searchParams = globalState?.searchParams;
  let dictData = globalState?.dictData;
  const [requestParams, setRequestParams] = useState<any>({});
  const [selectedTags, setSelectedTags] = useState(['Category']);

  // 获取初始化columns时使用的参数？
  const [operQueryDetail, setOperQueryDetail] = useState<CombineQueryDetail>(
    {},
  );

  // 表格列配置状态
  const [tableColumnsState, setTableColumnsState] = useState<any[]>([]);

  const [detailExportData, setDetailExportData] = useState({
    outputColumns: [],
    requestData: {},
  });
  // metric
  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    hideOnSinglePage: false,
  });

  // modal state
  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer>
  >(modalReducer, {
    visible: false,
    record: undefined,
    dataSource: [],
  });

  // tag change
  const handleChange = (tag: string, checked: boolean) => {
    // const nextSelectedTags = checked ? [tag] : [tag];
    setSelectedTags([tag]);
  };

  // metric part
  const [form] = Form.useForm();
  const [selectedMetricsState, setSelectedMetricsState] = useState<{
    aggregate: any[];
    metric: any[];
    group: any[];
  }>({
    aggregate: [],
    metric: [],
    group: [],
  });

  // activeKey
  const [activeTabKey, setActiveTabKey] = useState('1');
  // items
  const [aggregateItems, setAggregateItems] = useState<MetricsAggregateItem[]>(
    [],
  );
  const [groupItems, setGroupItems] = useState<MetricsGroupItem[]>([]);
  const [metricItems, setMetricItems] = useState<MetricsMetricItem[]>([]);

  // groupCol for second tab
  const [groupCols, setGroupCols] = useState([]);

  // 获取table data 接口
  const {
    data: icdeStats,
    loading: icdeStatsReqLoading,
    run: icdeStatsReq,
  } = useRequest(
    (reqData, current, pageSize) => {
      let data: any = {
        DtParam: {
          Draw: 1,
          Start: (current - 1) * pageSize,
          Length: pageSize,
        },
        icdGrouperCol: {
          isMain: searchParams?.IsMain ?? false,
          groupOption: reqData?.selectedTags?.at(0),
        },
        basicArgs: {
          hospCode: searchParams?.hospCodes,
          cliDepts: searchParams?.CliDepts,
          sdate: searchParams?.dateRange?.at(0) ?? null,
          edate: searchParams?.dateRange?.at(1) ?? null,
        },
        FilterColumn: 'PatCnt', // 跟filterNumber一起使用
        FilterNumber: searchParams?.FilterNumber
          ? parseInt(searchParams?.FilterNumber)
          : 10,
      };

      data = {
        ...data,
        ...metricParamsProcessor(selectedMetricsState),
      };

      // if (fetchId === 'hosp' && data.hasOwnProperty('GrouperCols')) {
      //   data.GrouperCols = [];
      // }

      return uniStatsSequenceService(
        'Api/V2/DmrAnalysis/IcdCategoryAnalysis/GetIcdeStats',
        {
          method: 'POST',
          requestType: 'json',
          data: data,
        },
      );
    },
    {
      manual: true,
      // fetchKey: (fetchId) => fetchId,
      formatResult: (response: RespVO<any>) => {
        // 这个接口会同时返回columns和data 为了确保columns正确 得从这边获取columns
        if (response?.code === 0 && response?.statusCode === 200)
          return response?.data;
        else return { Item1: {}, Item2: {} };
      },
      onSuccess: (data, params) => {
        const { Item1, Item2 } = data;
        TableDispatch({
          type: TableAction.dataChange,
          payload: {
            data: (Item1?.data || [])?.map((d) => ({
              ...d,
              uuid: uuidv4(),
            })),
          },
        });
        // if (res && params?.at(0) === 'hosp') {
        //   // column set
        //   // if (!TableState?.columns?.length) {
        //   //   // TableDispatch({
        //   //   //   type: TableAction.columnsChange,
        //   //   //   payload: {
        //   //   //     columns: tableColumnBaseProcessor([], res?.columns),
        //   //   //   },
        //   //   // });
        //   // }

        // }
      },
    },
  );

  // 3个list接口,进入页面就调用：
  // 1. 获取 聚合 字段
  const {
    loading: operQueryAggregatesLoading,
    run: operQueryAggregateItemsReq,
  } = useRequest(
    () => {
      let data = {
        TableName: TABLE_NAME,
      };

      return uniCombineQueryService(
        'Api/Analysis/AnaModelDef/GetAggregatingList',
        {
          params: data,
        },
      );
    },
    {
      formatResult: (response: RespVO<MetricsAggregateItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setAggregateItems(response?.data);
        } else {
          setAggregateItems([]);
        }
      },
    },
  );
  // 2. 获取 分组维度 字段
  const { loading: operQueryGroupsLoading, run: operQueryGroupItemsReq } =
    useRequest(
      () => {
        let data = {
          TableName: TABLE_NAME,
        };
        return uniCombineQueryService(
          'Api/Analysis/AnaModelDef/GetGrouperList',
          {
            params: data,
          },
        );
      },
      {
        //   manual: true,
        formatResult: (response: RespVO<MetricsGroupItem[]>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setGroupItems(response?.data);
          } else {
            setGroupItems([]);
          }
        },
      },
    );

  // 2. 获取 预设 字段
  const { loading: operQueryMetricItemsLoading, run: operQueryMetricItemsReq } =
    useRequest(
      () => {
        let data = {
          TableName: TABLE_NAME,
        };
        return uniCombineQueryService(
          'Api/Analysis/AnaModelDef/GetMetricList',
          {
            params: data,
          },
        );
      },
      {
        //   manual: true,
        formatResult: (response: RespVO<MetricsMetricItem[]>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setMetricItems(response?.data);
          } else {
            setMetricItems([]);
          }
        },
      },
    );

  // 3. 获取后端保存的 当前显示的 columnsIdList
  // Item1 = SubjectId → Aggregates, MetricId → Metric; Item2 = SubjectId → yGroups
  const {
    data: currentColumnsTemplate,
    loading: columnsTemplateReqLoading,
    run: columnsTemplateReq,
  } = useRequest(
    () => {
      return uniCombineQueryService(
        'Api/V2/DmrAnalysis/IcdCategoryAnalysis/GetIcdeStatsTemplate',
        {
          method: 'POST',
        },
      );
    },
    {
      formatResult: (response: RespVO<DetailColumnItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setSelectedMetricsState(
            defaultSelectedMetricsProcessor(response.data),
          );
          return response?.data;
        } else {
          return [];
        }
      },
    },
  );

  // 保存模板（columns）
  const { loading: saveTemplateReqLoading, run: saveTemplateReq } = useRequest(
    (data) => {
      return uniCombineQueryService(
        'Api/V2/DmrAnalysis/IcdCategoryAnalysis/SaveIcdeStatsTemplate',
        {
          method: 'POST',
          data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<DetailColumnItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          columnsTemplateReq();
        } else {
          return [];
        }
      },
    },
  );

  const metricStateToRequestProcessor = (metricState: any) => {
    const result = {
      Aggregates: [],
      Metrics: [],
      Groupers: [],
    };

    // 处理聚合指标
    metricState?.aggregate?.forEach((item) => {
      const [subjectId, aggregationOperator] = item.id.split('#');
      result.Aggregates.push({
        ..._.omit(item, ['id', 'Id']),
        SubjectId: subjectId,
        AggregationOperator: aggregationOperator,
      });
    });

    // 处理普通指标
    metricState?.metric?.forEach((item) => {
      result.Metrics.push({
        ..._.omit(item, ['id', 'Id']),
        MetricId: item.id,
        ColumnTitle: item?.ColumnTitle || item?.customTitle,
      });
    });

    // 处理分组指标
    metricState?.group?.forEach((item) => {
      const [subjectId, anaGroupOption] = item.id.split('#');
      result.Groupers.push({
        ..._.omit(item, ['id', 'Id']),
        SubjectId: subjectId,
        AnaGroupOption: anaGroupOption,
      });
    });

    return result;
  };

  // 用来处理一系列用户对modal操作时的响应
  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_COMPLETE,
      (data) => {
        // if(data?.columns) {
        //   setMetricsTableColumns(data?.columns);
        // }
        // 这里是用户最终保存的columns结果 saveTemplateReq
        console.log(
          'metricStateToRequestProcessor(data?.metricsState)',
          metricStateToRequestProcessor(data?.metricsState),
        );
        saveTemplateReq(metricStateToRequestProcessor(data?.metricsState));
        console.log('metricColumnSelectComplete', data);
        // if (data?.metricsState) {
        //   setSelectedMetricsState(data?.metricsState);
        // }
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.METRIC_COLUMN_TEMPLATE_CLICK,
      (item) => {
        if (item?.Id) {
          //   metricsColumnsTemplatedGetReq(item?.Id); // 获取默认的columns的列表？
          //   setCombineQueryMetricsColumnTemplateId(item?.Id); // 应该是原本组合查询的统计模板id
        }
      },
    );

    Emitter.on(StatsAnalysisEventConstant.STATS_ANALYSIS_RESET, () => {
      // 清除数据
      let pagination = {
        ...backPagination,
        current: 1,
        pageSize: 10,
        total: 0,
      };
      setOperQueryDetail({});
      //   setCombineQueryTitle('');
      setBackPagination(pagination);
      TableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: [],
        },
      });
    });

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_COMPLETE,
      );
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS,
      );
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_STATS_COMBINE_QUERY_TITLE,
      );
      Emitter.off(StatsAnalysisEventConstant.METRIC_COLUMN_TEMPLATE_CLICK);
      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_RESET);
    };
  }, []);

  useEffect(() => {
    let newTableColumns = [];
    let groupTableColumns = [];

    const anaGroupOptions = dictData?.['AnaGroupOption'];

    console.log('selectedMetricsState', selectedMetricsState, anaGroupOptions);

    selectedMetricsState?.aggregate?.forEach((item) => {
      let selectItem = aggregateItems?.find(
        (aggregateItem) => aggregateItem?.id === item?.SubjectId,
      );
      if (selectItem) {
        let aggregateItem =
          selectItem?.aggregationNames?.[item?.AggregationOperator] || {};

        newTableColumns.push({
          ...selectItem,
          ...aggregateItem,

          originTitle: selectItem?.title,
          order: item?.ColumnSort ?? 0,
          title: item?.CustomTitle || aggregateItem?.title,

          tagTitle: item?.CustomTitle || aggregateItem?.title,
          itemType: 'aggregate',
          itemKey: item?.SubjectId,
          Id: `${item?.SubjectId}#${item?.AggregationOperator}`, // special
          id: `${item?.SubjectId}#${item?.AggregationOperator}`,
        });
      }
    });

    selectedMetricsState?.metric?.forEach((item) => {
      let selectItem = metricItems?.find(
        (metricItem) => metricItem?.id === item?.MetricId,
      );
      if (selectItem) {
        newTableColumns.push({
          ...selectItem,

          originTitle: selectItem?.title,
          order: item?.ColumnSort ?? 0,
          title: item?.CustomTitle || selectItem?.title,

          tagTitle: selectItem?.title,
          itemType: 'metric',
          itemKey: item?.MetricId,
          Id: item?.MetricId,
          id: item?.MetricId,
        });
      }
    });

    selectedMetricsState?.group?.forEach((item) => {
      let selectItem = groupItems?.find(
        (groupItem) => groupItem?.id === item?.SubjectId,
      );
      if (selectItem) {
        let extraLabel =
          item?.AnaGroupOption === 'Default'
            ? ''
            : `-${
                anaGroupOptions?.find(
                  (opt) => opt.Code === item?.AnaGroupOption,
                )?.Name
              }`;
        groupTableColumns.push({
          ...selectItem,

          originTitle: selectItem?.title,
          order: item?.ColumnSort ?? 0,
          title: item?.CustomTitle ?? `${selectItem.title}${extraLabel}`,

          tagTitle: item?.CustomTitle || `${selectItem.title}${extraLabel}`,
          itemType: 'group',
          itemKey: item?.SubjectId,
          Id: `${item?.SubjectId}#${item?.AnaGroupOption}`, // special
          id: `${item?.SubjectId}#${item?.AnaGroupOption}`,
        });
      }
    });

    // 更新状态，使其可在其他useEffect中访问
    setTableColumnsState(newTableColumns);
    setGroupCols(groupTableColumns);
  }, [aggregateItems, metricItems, groupItems, selectedMetricsState]);

  // 单独处理列配置，监听searchParams?.IsMain变化
  useEffect(() => {
    TableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: [
          // 固定写死的，根据IsMain条件动态设置
          {
            title: '编码',
            dataIndex: searchParams?.IsMain
              ? 'MainIcde_IcdeCode'
              : 'IcdeDscgs_IcdeCode',
            key: searchParams?.IsMain
              ? 'MainIcde_IcdeCode'
              : 'IcdeDscgs_IcdeCode',
            visible: true,
          },
          {
            title: '名称',
            dataIndex: searchParams?.IsMain
              ? 'MainIcde_IcdeName'
              : 'IcdeDscgs_IcdeName',
            key: searchParams?.IsMain
              ? 'MainIcde_IcdeName'
              : 'IcdeDscgs_IcdeName',
            visible: true,
          },
          {
            title: '构成比',
            dataIndex: 'Proportion',
            key: 'Proportion',
            dataType: 'percent',
            visible: true,
          },
          ...tableColumnsState
            ?.map((item) => {
              item['dataIndex'] = item['name'];
              item['orderable'] = false;
              return item;
            })
            ?.sort((a, b) => a?.order - b?.order),
        ],
      },
    });
  }, [searchParams?.IsMain, tableColumnsState]);

  // 接口数据处理
  useEffect(() => {
    if (
      (globalState?.searchParams &&
        globalState?.searchParams?.triggerSource === 'btnClick') ||
      isEmptyValues(requestParams)
    ) {
      let tableParams = {
        ...searchParams,
      };
      setRequestParams(tableParams);
    }
  }, [globalState?.searchParams]);
  // 重新调接口？应该是改变了参数的时候
  useEffect(() => {
    let pagination = {
      ...backPagination,
      current: 1,
      pageSize: 10,
      total: 0,
    };
    if (
      // searchParams &&
      selectedTags
      // &&
      // (selectedMetricsState?.aggregate?.length > 0 ||
      //   selectedMetricsState?.group?.length > 0 ||
      //   selectedMetricsState?.metric?.length > 0)
    ) {
      icdeStatsReq(
        { ...searchParams, selectedTags },
        pagination.current,
        pagination.pageSize,
      );
    }

    setBackPagination(pagination);
    // TableDispatch({ type: TableAction.dataChange, payload: { data: [] } });
  }, [requestParams, selectedTags, selectedMetricsState]);

  // 监听modal内部的数据用于导出
  useEffect(() => {
    Emitter.on('DISEASE_DETAIL_EXPORT_DATA', (data) => {
      setDetailExportData(data);
    });

    return () => {
      Emitter.off('DISEASE_DETAIL_EXPORT_DATA');
    };
  }, []);

  let tabItems = [
    {
      label: '病种顺位统计',
      key: '1',
      children: (
        <Card>
          <div style={{ padding: '0 25px', marginBottom: '20px' }}>
            <Spin spinning={false}>
              <div className="tags-container">
                {DiseaseTags &&
                  DiseaseTags.map((tag) => (
                    <CheckableTag
                      className="check-rule-tags"
                      key={tag?.Code}
                      checked={selectedTags.indexOf(tag?.Code) > -1}
                      onChange={(checked) => handleChange(tag?.Code, checked)}
                    >
                      {tag.Name}
                    </CheckableTag>
                  ))}
              </div>
            </Spin>
          </div>
          <UniTable
            id="sequence-table"
            rowKey={'id'}
            loading={icdeStatsReqLoading || operQueryMetricItemsLoading}
            className="table-container"
            forceColumnsUpdate={true}
            columns={TableState?.columns}
            scroll={{ x: 'max-content' }}
            dataSource={TableState?.data}
            // onChange={backTableOnChange}
            // pagination={backPagination}
            dictionaryData={dictData}
          />
        </Card>
      ),
    },
    {
      label: '科室单列统计',
      key: '2',
      children: (
        <Card>
          <div style={{ padding: '0 25px', marginBottom: '20px' }}>
            <Spin spinning={false}>
              <div className="tags-container">
                {DiseaseTags &&
                  DiseaseTags.map((tag) => (
                    <CheckableTag
                      className="check-rule-tags"
                      key={tag?.Code}
                      checked={selectedTags.indexOf(tag?.Code) > -1}
                      onChange={(checked) => handleChange(tag?.Code, checked)}
                    >
                      {tag.Name}
                    </CheckableTag>
                  ))}
              </div>
            </Spin>
          </div>
          <ExpandableTable
            tableData={icdeStats?.Item2?.data || []}
            columns={TableState?.columns || []}
            expandableDataIndex="OutDept"
            fetchLoading={icdeStatsReqLoading}
            dictData={dictData}
            groupTableColumns={groupCols}
            filterNumber={searchParams?.FilterNumber}
          />
        </Card>
      ),
    },
  ];

  return (
    <Card bodyStyle={{ padding: '0px 12px 12px' }}>
      <Tabs
        items={tabItems}
        activeKey={activeTabKey}
        onChange={(key) => {
          setActiveTabKey(key);
        }}
        tabBarExtraContent={
          <Space>
            <Button
              onClick={() => {
                // detailsReq({ ...searchParams, selectedTags }, 0, 10);
                ModalStateDispatch({
                  type: ModalAction.change,
                  payload: {
                    ...ModalState,
                    visible: true,
                    record: undefined,
                  },
                });
              }}
            >
              查看明细
            </Button>
            <Divider type="vertical" />
            <MetricColumnSettings
              nextGeneration={false}
              form={form}
              tableName={TABLE_NAME}
              metricsState={selectedMetricsState}
              // totalSelectItems={{
              //   aggregateItems: aggregateItems?.slice(),
              //   groupItems: groupItems?.slice(),
              //   metricItems: metricItems?.slice(),
              // }}
              aggregateItems={aggregateItems?.slice()}
              groupItems={groupItems?.slice()}
              metricItems={metricItems?.slice()}
              hideGroupTab={true}
            />
            {activeTabKey === '1' && (
              <ExportIconBtn
                isBackend={false}
                frontendObj={{
                  columns: TableState?.columns?.map((item) => {
                    return {
                      ...item,
                      exportable: item?.visible ? true : false,
                    };
                  }),
                  dictionaryData: dictData,
                  dataSource: TableState?.data,
                  fileName: `疾病顺位统计-${
                    DiseaseTags?.find(
                      (tag) => tag?.Code === selectedTags?.at(0),
                    )?.Name
                  }`,
                }}
                btnDisabled={TableState?.data?.length < 1}
              />
            )}
            {activeTabKey === '2' && (
              <ExportIconBtn
                isBackend={false}
                frontendObj={{
                  columns: [
                    ...groupCols
                      ?.map((item) => ({
                        ...item,
                        dataIndex: item.name,
                        data: item.name,
                        exportable: item?.visible ? true : false,
                      }))
                      ?.sort((a, b) => a?.order - b?.order),
                    ...TableState?.columns?.map((item) => {
                      return {
                        ...item,
                        exportable: item?.visible ? true : false,
                      };
                    }),
                  ],
                  dictionaryData: dictData,
                  dataSource: icdeStats?.Item2?.data,
                  fileName: `病种顺位科室单列统计-${
                    DiseaseTags?.find(
                      (tag) => tag?.Code === selectedTags?.at(0),
                    )?.Name
                  }`,
                }}
                btnDisabled={icdeStats?.Item2?.data?.length < 1}
              />
            )}
          </Space>
        }
      />
      {/* 查看明细part */}
      <Modal
        open={ModalState?.visible}
        width={900}
        title={
          <Space>
            <span>明细列表</span>
            <Tooltip title={'列配置'}>
              <Button
                type="text"
                shape="circle"
                icon={<SettingOutlined className="infinity_rotate" />}
                onClick={() => {
                  Emitter.emit(
                    `${
                      DetailColumnSettingContentConstants.MODAL_OPEN
                    }_${'Disease'}`,
                    {
                      status: true,
                    },
                  );
                }}
              />
            </Tooltip>
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: 'Api/V2/DmrAnalysis/IcdCategoryAnalysis/ExportGetIcdeDetails',
                method: 'POST',
                data: {
                  ...detailExportData.requestData,
                  outputColumns: detailExportData.outputColumns,
                },
                fileName: `病种顺位明细-${
                  DiseaseTags?.find((tag) => tag?.Code === selectedTags?.at(0))
                    ?.Name
                }-${dayjs().format('YYYY-MM-DD')}`,
              }}
              btnDisabled={detailExportData.outputColumns?.length === 0}
            />
          </Space>
        }
        onCancel={() => {
          ModalStateDispatch({
            type: ModalAction?.init,
          });
        }}
        forceRender
        cancelButtonProps={{ style: { display: 'none' } }}
        okButtonProps={{ style: { display: 'none' } }}
        onOk={() => {
          ModalStateDispatch({
            type: ModalAction?.init,
          });
        }}
      >
        <DetailContainer
          title={ModalState?.record?.title}
          id={
            ModalState?.record?.MainOper_OperCode ??
            ModalState?.record?.Opers_OperCode
          }
          selectedTags={selectedTags}
          modalVisible={ModalState?.visible}
        />
      </Modal>
    </Card>
  );
};

export default DiseaseSequence;
