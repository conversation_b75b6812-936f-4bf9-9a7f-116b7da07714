import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, DatePicker, Row, Col } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs, { Dayjs } from 'dayjs';
import { Typography } from 'antd';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src/commonService';
import { isRespErr, RespType } from '@/utils/widgets';
import { UniTable } from '@uni/components/src';
import { ProForm } from '@uni/components/src/pro-form';
import './index.less';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import _ from 'lodash';
const { Text } = Typography;

interface CorrectionResponse {
  Id: number;
  Before: {
    HospCode: string;
    DeptCode: string;
    ExactDate: string;
    Sort: number;
    Id: number;
    HospName: string;
    DeptName: string;
    Status: string;
    StatusName: string;
    IsLocked: boolean;
    Remark: string;
    LastModificationTime: string;
    ApprovedBedsNumber: number;
    SuppliedBedsNumber: number;
    StayInCnt: number;
    InHospCnt: number;
    TransInCnt: number;
    TransOutCnt: number;
    TotalOutHospCnt: number;
    OutHospCnt: number;
    DeathCnt: number;
    StayingCnt: number;
    CuredCnt: number;
    ImprovedCnt: number;
    NotCuredCnt: number;
    CondDangCnt: number;
    ActualBedDays: number;
    DscgPatientActualBedDays: number;
    IsCorrect: boolean;
    CorrectionTime: string;
    OtherCnt1: number;
    OtherCnt2: number;
    OtherCnt3: number;
    OtherCnt4: number;
    OtherCnt5: number;
    OtherCnt6: number;
    OtherCnt7: number;
    OtherCnt8: number;
  };
  After: {
    HospCode: string;
    DeptCode: string;
    ExactDate: string;
    Sort: number;
    Id: number;
    HospName: string;
    DeptName: string;
    Status: string;
    StatusName: string;
    IsLocked: boolean;
    Remark: string;
    LastModificationTime: string;
    ApprovedBedsNumber: number;
    SuppliedBedsNumber: number;
    StayInCnt: number;
    InHospCnt: number;
    TransInCnt: number;
    TransOutCnt: number;
    TotalOutHospCnt: number;
    OutHospCnt: number;
    DeathCnt: number;
    StayingCnt: number;
    CuredCnt: number;
    ImprovedCnt: number;
    NotCuredCnt: number;
    CondDangCnt: number;
    ActualBedDays: number;
    DscgPatientActualBedDays: number;
    IsCorrect: boolean;
    CorrectionTime: string;
    OtherCnt1: number;
    OtherCnt2: number;
    OtherCnt3: number;
    OtherCnt4: number;
    OtherCnt5: number;
    OtherCnt6: number;
    OtherCnt7: number;
    OtherCnt8: number;
  };
}

interface Props {
  visible: boolean;
  onCancel: (hasFetched) => void;
  record: any;
  deptName: string;
  exactDate: string;
  columns: any[];
  apiType: string;
}

interface CompareDataType {
  field: string;
  before: any;
  after: any;
}

const CorrectionModal: React.FC<Props> = ({
  visible,
  onCancel,
  record,
  deptName,
  exactDate,
  apiType,
  columns = [],
}) => {
  const [form] = ProForm.useForm();
  const [renderColumns, setRenderColumns] = useState([]);
  const [compareData, setCompareData] = useState<any[]>([]);
  const exactDateDayjs = dayjs(exactDate);
  const [hasFetched, setHasFetched] = useState(false);

  const { loading: correctionLoading, run: correctionReq } = useRequest(
    (data) =>
      uniCommonService(`Api/Dyn-ddr/${apiType}/Correct`, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespType<CorrectionResponse[]>) => {
        if (!isRespErr(res)) {
          try {
            if (!res.data?.length) {
              return null;
            }
            setHasFetched(true);
            const processedData = res.data.flatMap((data) => {
              if (!data?.Before || !data?.After) {
                return [];
              }

              const result = {};
              let tempColumns = _.cloneDeep(columns);
              let index = tempColumns?.findIndex(
                (d) => d.dataIndex === 'ExactDate',
              );
              tempColumns[index] = {
                ...tempColumns[index],
                visible: true,
              };
              tempColumns.forEach((col) => {
                if (
                  col.visible &&
                  col.title &&
                  col.dataIndex &&
                  col.dataIndex !== 'FrontStatus' &&
                  col.dataIndex !== 'Status' &&
                  col.dataIndex !== 'StatusName' &&
                  col.dataIndex !== 'DeptName' &&
                  col.dataIndex !== 'HospName'
                ) {
                  result[`before${col.dataIndex}`] =
                    data.Before[col.dataIndex] ?? '-';
                  result[`after${col.dataIndex}`] =
                    data.After[col.dataIndex] ?? '-';
                }
              });

              return [
                {
                  ...result,
                  key: `${data.After.ExactDate}_${data.After.Id}`,
                },
              ];
            });

            let tempColumns = _.cloneDeep(columns);
            let index = tempColumns?.findIndex(
              (d) => d.dataIndex === 'ExactDate',
            );
            tempColumns[index] = {
              ...tempColumns[index],
              visible: true,
            };
            const processedColumns = tempColumns
              .filter((col) => {
                return (
                  col.visible &&
                  col.title &&
                  col.dataIndex &&
                  col.dataIndex !== 'FrontStatus' &&
                  col.dataIndex !== 'Status' &&
                  col.dataIndex !== 'StatusName' &&
                  col.dataIndex !== 'DeptName' &&
                  col.dataIndex !== 'HospName'
                );
              })
              ?.map((col, index) => {
                return {
                  ...col,
                  render: (_, record) => {
                    if (col.dataIndex === 'ExactDate') {
                      return valueNullOrUndefinedReturnDash(
                        record[`before${col.dataIndex}`],
                        'date',
                      );
                    }
                    const beforeValue = record[`before${col.dataIndex}`];
                    const afterValue = record[`after${col.dataIndex}`];
                    return beforeValue === afterValue ? (
                      beforeValue
                    ) : (
                      <>
                        {beforeValue || 0}
                        <span> → </span>
                        <Text type="danger">{afterValue}</Text>
                      </>
                    );
                  },
                };
              });

            console.log('processedColumns', processedColumns, processedData);
            setRenderColumns(processedColumns);
            setCompareData(processedData);

            return res.data;
          } catch (error) {
            console.error('数据处理异常:', error);
            return null;
          }
        }
        return null;
      },
    },
  );

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        exactDate: exactDateDayjs,
      });
    } else {
      setCompareData([]);
      form.resetFields();
    }
  }, [visible, exactDate]);

  const handleFormSubmit = async (values: any) => {
    if (!record) return;

    const params = {
      ...record,
      edate: dayjs(values.endDate)?.format('YYYY-MM-DD'),
    };
    await correctionReq(params);
  };

  // const compareColumns: ColumnsType<CompareDataType> = [
  //   {
  //     title: '字段',
  //     dataIndex: 'field',
  //     key: 'field',
  //     width: '30%',
  //   },
  //   {
  //     title: '值',
  //     key: 'value',
  //     width: '70%',
  //     render: (_: any, record: CompareDataType) => {
  //       if (record.changed) {
  //         return (
  //           <>
  //             <Text>{record.before}</Text>
  //             <Text> → </Text>
  //             <Text type="danger">{record.after}</Text>
  //           </>
  //         );
  //       }
  //       return <Text>{record.before}</Text>;
  //     },
  //   },
  // ];

  console.log('compareData', compareData);

  return (
    <Modal
      title="数据修正"
      className="correction-modal"
      open={visible}
      onCancel={() => {
        onCancel(hasFetched);
        setTimeout(() => {
          setHasFetched(false);
        }, 100);
      }}
      width={800}
      destroyOnClose
      footer={[
        <Button
          key="close"
          onClick={() => {
            onCancel(hasFetched);
            setTimeout(() => {
              setHasFetched(false);
            }, 100);
          }}
        >
          关闭
        </Button>,
      ]}
    >
      <ProForm form={form} onFinish={handleFormSubmit} submitter={false}>
        <Row gutter={24}>
          <Col span={6}>
            <ProForm.Item label="科室">
              <span>{deptName}</span>
            </ProForm.Item>
          </Col>
          <Col span={6}>
            <ProForm.Item
              label="起始日期"
              name="exactDate"
              initialValue={exactDateDayjs}
            >
              <span>{exactDateDayjs.format('YYYY-MM-DD')}</span>
            </ProForm.Item>
          </Col>
          <Col span={6}>
            <ProForm.Item
              label="结束日期"
              name="endDate"
              rules={[
                { required: true, message: '请选择结束日期' },
                {
                  validator: async (_, value: Dayjs) => {
                    if (!value || !exactDateDayjs) {
                      return Promise.resolve();
                    }
                    if (value.isBefore(exactDateDayjs, 'day')) {
                      return Promise.reject('结束日期不能早于起始日期');
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <DatePicker
                disabledDate={(current) =>
                  (current && current.valueOf() < exactDateDayjs.valueOf()) ||
                  hasFetched
                }
              />
            </ProForm.Item>
          </Col>
          <Col span={6} style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              htmlType="submit"
              disabled={hasFetched}
              loading={correctionLoading}
            >
              修正
            </Button>
          </Col>
        </Row>
      </ProForm>
      <UniTable
        rowKey="key"
        id="compare-table"
        columns={renderColumns}
        dataSource={compareData}
        scroll={{ x: 'max-content' }}
      />
    </Modal>
  );
};

export default CorrectionModal;
