import isString from 'lodash/isString';
import { isEmptyValues } from '@uni/utils/src/utils';

const sortDictName =
  (window as any).externalConfig?.['common']?.sortDictName ?? false;

interface SorterItemProcessorProps {
  columnItem: any;
  sortedInfo: any;
  backendPagination: boolean | object;
  dictionaryData?: any;
}

export const MAX_COLUMN_MULTIPLE = 1000;

export const processDefaultSortedInfo = (
  columns,
  isBackendPagination = false,
) => {
  // columnKey: "InDate"
  // field: "InDate"
  // order: "ascend"
  let sortedInfo: any = {};
  let defaultSortedColumns = columns?.filter((columnItem) => {
    return (
      columnItem?.orderPriority > 0 && !isEmptyValues(columnItem?.orderMode)
    );
  });

  if (defaultSortedColumns?.length > 1) {
    sortedInfo = defaultSortedColumns?.map((columnItem) => {
      // 支持多个 + multiple 了=
      return {
        columnKey: columnItem?.dataIndex,
        field: columnItem?.dataIndex,
        order: columnItem?.orderMode,
        column: {
          sorter: {
            multiple: MAX_COLUMN_MULTIPLE - columnItem?.orderPriority,
          },
        },
      };
    });
  } else if (defaultSortedColumns?.length === 1) {
    defaultSortedColumns?.forEach((columnItem) => {
      // 支持多个 + multiple 了
      sortedInfo['columnKey'] = columnItem?.dataIndex;
      sortedInfo['field'] = columnItem?.dataIndex;
      sortedInfo['order'] = columnItem?.orderMode;
    });
  }

  return sortedInfo;
};

const getActualSorterItemValue = (
  dictionaryData: any,
  module: string,
  moduleGroup: string,
  value: string,
) => {
  if (sortDictName === false) {
    return value;
  }

  if (isEmptyValues(value)) {
    return value;
  }

  if (isEmptyValues(module)) {
    return value;
  }

  let dictDataWithGrouped =
    (isEmptyValues(moduleGroup)
      ? dictionaryData?.[module]
      : dictionaryData?.[moduleGroup]?.[module]) ?? [];
  let dictDataItem = dictDataWithGrouped?.find((item) => {
    return item?.Code === value;
  });

  if (!isEmptyValues(dictDataItem)) {
    return dictDataItem?.Name ?? value;
  }

  return value;
};

const SorterItemProcessor = (props: SorterItemProcessorProps) => {
  const sorterItemSetter = (sorterItem: any) => {
    return props.backendPagination
      ? {
          sorter: {
            multiple:
              isEmptyValues(props?.columnItem?.orderPriority) &&
              props?.columnItem?.orderPriority !== 0
                ? null
                : MAX_COLUMN_MULTIPLE - props?.columnItem?.orderPriority,
          },
          sortOrder: !isEmptyValues(sorterItem)
            ? sorterItem?.columnKey === props.columnItem?.dataIndex
              ? sorterItem?.order
              : null
            : props.columnItem?.defaultSortOrder || null,
        }
      : {
          sorter: {
            compare: (a, b) => {
              let aValue = getActualSorterItemValue(
                props?.dictionaryData,
                props?.columnItem?.dictionaryModule,
                props?.columnItem?.dictionaryModuleGroup,
                a[props.columnItem.dataIndex],
              );

              let bValue = getActualSorterItemValue(
                props?.dictionaryData,
                props?.columnItem?.dictionaryModule,
                props?.columnItem?.dictionaryModuleGroup,
                b[props.columnItem.dataIndex],
              );

              return isString(aValue) && isString(bValue)
                ? aValue.localeCompare(bValue)
                : aValue - bValue;
            },
            multiple:
              isEmptyValues(props?.columnItem?.orderPriority) &&
              props?.columnItem?.orderPriority !== 0
                ? null
                : MAX_COLUMN_MULTIPLE - props?.columnItem?.orderPriority,
          },
          // 新增 sortedOrder
          sortOrder: !isEmptyValues(sorterItem)
            ? sorterItem?.columnKey === props.columnItem?.dataIndex
              ? sorterItem?.order
              : null
            : props.columnItem?.defaultSortOrder || null,
        };
  };

  let sorter = {};
  if (
    props.columnItem.sorterType ||
    (!props.columnItem?.sorterType && props.columnItem?.orderable) ||
    props.columnItem?.defaultSortOrder
  ) {
    if (Array.isArray(props?.sortedInfo)) {
      let sorterItem = props?.sortedInfo?.find(
        (item) => item?.columnKey === props?.columnItem?.dataIndex,
      );
      sorter = sorterItemSetter(sorterItem);
    } else {
      sorter = sorterItemSetter(
        isEmptyValues(props?.sortedInfo) ? null : props?.sortedInfo,
      );
    }
  }
  return {
    ...props.columnItem,
    ...sorter,
  };
};

export default SorterItemProcessor;
