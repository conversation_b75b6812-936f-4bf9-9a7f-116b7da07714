import { Form, Input, Switch } from 'antd';
import React from 'react';
import { UniSelect } from '@uni/components/src';
import { icdeTypes } from '@/pages/configuration/base/icde/constants';
import { cliDeptTypes } from '@/pages/configuration/base/cliDepts/constants';
import './index.less';
import { useModel } from '@@/plugin-model/useModel';
import Datepicker from '@uni/components/src/picker/datepicker';
import dayjs from 'dayjs';

interface EmployeeAddItemProps {
  form: any;
  columns?: any[]; // 添加columns参数
  editIndex?: number;
}

const EmployeeItemAdd = (props: EmployeeAddItemProps) => {
  const { globalState } = useModel('@@qiankunStateForSlave');

  const dictData = globalState?.dictData;

  return (
    <Form
      className={'employee-add-container'}
      form={props?.form}
      preserve={false}
    >
      <Form.Item
        label="职员工号"
        name="Code"
        rules={[
          {
            required: true,
            message: '请填写工号',
          },
        ]}
      >
        <Input disabled={props?.editIndex !== undefined} />
      </Form.Item>

      <Form.Item
        label="职工姓名"
        name="Name"
        rules={[
          {
            required: true,
            message: '请填写姓名',
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item label="性别" name="Sex">
        <UniSelect
          dataSource={dictData?.['XB']}
          placeholder={'请选择'}
          optionNameKey={'Name'}
          optionValueKey={'Code'}
        />
      </Form.Item>

      <Form.Item label="职工类型" name="JobClass">
        <UniSelect
          dataSource={dictData?.['JobClass']}
          placeholder={'请选择'}
          optionNameKey={'Name'}
          optionValueKey={'Code'}
        />
      </Form.Item>

      <Form.Item label="职工职称" name="EmployeeTitle">
        <UniSelect
          dataSource={dictData?.['EmployeeTitle']}
          placeholder={'请选择'}
          optionNameKey={'Name'}
          optionValueKey={'Code'}
        />
      </Form.Item>

      <Form.Item label="职工状态" name="EmployeeStatus">
        <UniSelect
          dataSource={dictData?.['EmployeeStatus']}
          placeholder={'请选择'}
          optionNameKey={'Name'}
          optionValueKey={'Code'}
        />
      </Form.Item>

      <Form.Item label="职业证书编号" name="CertificateCode">
        <Input />
      </Form.Item>

      <Form.Item label="结算清单上报用编号" name="ChsStaffCode">
        <Input />
      </Form.Item>

      <Form.Item label="关联科室" name="RelatedCliDepts">
        {(() => {
          // 查找columns中RelatedCliDepts的配置
          const relatedCliDeptsColumn = props.columns?.find(
            (column) => column.dataIndex === 'RelatedCliDepts',
          );

          // 使用与columns.tsx中相同的逻辑获取字典数据源
          const deptsDictData =
            dictData?.[relatedCliDeptsColumn?.dictionaryModule || 'CliDepts'] ||
            [];

          return (
            <UniSelect
              dataSource={deptsDictData}
              placeholder={'请选择科室'}
              optionNameKey={'Name'}
              optionValueKey={'Code'}
              mode="multiple"
              showSearch={true}
              allowClear={true}
            />
          );
        })()}
      </Form.Item>

      <Form.Item label="是否有效" name="IsValid">
        <Switch defaultChecked={props?.form?.getFieldValue('IsValid')} />
      </Form.Item>
    </Form>
  );
};

export default EmployeeItemAdd;
