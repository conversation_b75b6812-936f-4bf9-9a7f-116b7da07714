import {
  Dropdown,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Spin,
  Tabs,
  Tree,
  Card,
  Space,
  Button,
  Radio,
  Switch,
  Upload,
} from 'antd';
import { UniTable } from '@uni/components/src';
import React, { useEffect, useState, useRef } from 'react';
import {
  dictionaryTableColumns,
  insurDictionaryTableColumns,
} from '@/pages/configuration/components/dict-base-columns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { v4 as uuidv4 } from 'uuid';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import {
  ConfigurationDictionaryItem,
  DictionaryItemCompare,
  DictionaryModuleItem,
} from '@/pages/configuration/base/interfaces';
import './index.less';
import DictionaryItemAdd from '@/pages/configuration/components/dictionary/dictionary-item';
import DictionaryAdd from '@/pages/configuration/base/dictionary/components/dictionary-add';
import { useModel } from '@@/plugin-model/useModel';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { DownOutlined, UploadOutlined } from '@ant-design/icons';
import {
  getMinusCompareCollectionsWithDoubleCollections,
  getTreeFilterWithSearchedText,
} from '@/pages/configuration/utils';
import cloneDeep from 'lodash/cloneDeep';
import trim from 'lodash/trim';
import isEmpty from 'lodash/isEmpty';
import ConfigExcelTemplateHandler from '@/components/configExcelTemplateHandler';
import UniEditableTable from '@uni/components/src/table/edittable';

interface DictionaryConfigurationProps {
  moduleGroup: string;
}

const DictionaryConfiguration = (props?: DictionaryConfigurationProps) => {
  const [form] = Form.useForm();

  const [rightClickItem, setRightClickItem] = useState(undefined);

  const [moduleTreeData, setModuleTreeData] = useState([]);

  const [treeDataRendered, setTreeDataRendered] = useState([]);

  const [treeExpandedKeys, setTreeExpandedKeys] = useState<React.Key[]>([
    '0-0-0',
  ]);

  const [searchText, setSearchText] = useState('');

  const [treeSelectedItem, setTreeSelectedItem] = useState(undefined);

  const [dictionaryOperateType, setDictionaryOperateType] = useState(undefined);

  const [allModuleLabels, setAllModuleLabels] = useState([]);
  const [allModuleKeys, setAllModuleKeys] = useState([]);

  // excel template download/upload part
  const [needReload, setNeedReload] = useState(false);

  // globalState
  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateForSlave',
  );

  const rootItems = [
    {
      key: 'ADD',
      label: (
        <a
          onClick={() => {
            setDictionaryOperateType('ADD');
            form.setFieldsValue({
              ParentFolder: rightClickItem?.key,
            });
          }}
        >
          新增字典库
        </a>
      ),
    },
  ];

  const childEditItems = [
    {
      key: 'EDIT',
      label: (
        <a
          onClick={(event) => {
            event.preventDefault();
            setDictionaryOperateType('EDIT');
            form.setFieldsValue({
              ...rightClickItem,
              ParentFolder: rightClickItem?.Directories?.at(0),
              ModuleName: rightClickItem?.Directories?.at(1),
            });
          }}
        >
          修改名称
        </a>
      ),
    },
    {
      key: 'DELETE',
      label: (
        <Popconfirm
          title="确定删除？"
          placement={'top'}
          getPopupContainer={(triggerNode) =>
            document.getElementById('dictionary-configuration-container')
          }
          onConfirm={async () => {
            Emitter.emit(
              ConfigurationEvents.DICTIONARY_CONFIGURATION_GLOBAL_UPDATE,
              {
                module: rightClickItem,
                tableData: [],
              },
            );
          }}
        >
          <a>删除字典库</a>
        </Popconfirm>
      ),
    },
  ];

  useEffect(() => {
    dictionaryConfigurationModulesReq();
  }, []);

  useEffect(() => {
    Emitter.on(
      ConfigurationEvents.DICTIONARY_CONFIGURATION_GLOBAL_UPDATE,
      async (data) => {
        updateGlobalState(
          data?.module?.Module,
          props.moduleGroup,
          data?.tableData.filter((item) => item.id !== 'ADD'),
        );
      },
    );

    return () => {
      Emitter.off(ConfigurationEvents.DICTIONARY_CONFIGURATION_GLOBAL_UPDATE);
    };
  }, [globalState]);

  const updateGlobalState = (module, moduleGroup, tableData) => {
    let dictData = globalState?.dictData;
    if (moduleGroup) {
      let moduleDictData = globalState?.dictData?.[moduleGroup];
      if (tableData && tableData?.length > 0) {
        moduleDictData[module] = tableData;
      } else {
        delete moduleDictData[module];
      }
      dictData[moduleGroup] = moduleDictData;
    } else {
      if (tableData && tableData?.length > 0) {
        dictData[module] = tableData;
      } else {
        delete dictData[module];
      }
    }

    setQiankunGlobalState({
      ...globalState,
      dictData: dictData,
    });
  };

  const onExpand = (expandedKeys: React.Key[]) => {
    setTreeExpandedKeys(expandedKeys);
  };

  const onSelect = (selectedKeys: React.Key[], info: any) => {
    setTreeSelectedItem(info?.node?.data);
  };

  // search
  const onChange = (e) => {
    const { value } = e.target;
    setSearchText(value);
  };

  useEffect(() => {
    if (!isEmpty(trim(searchText))) {
      const searchedTreeData = getTreeFilterWithSearchedText(
        searchText,
        moduleTreeData,
      );
      // 自动展开
      setTreeExpandedKeys(searchedTreeData.map((d) => d.key));
      setTreeDataRendered(searchedTreeData);
    } else {
      setTreeDataRendered(moduleTreeData);
    }
  }, [moduleTreeData, searchText]);

  const {
    loading: dictionaryConfigurationModulesLoading,
    run: dictionaryConfigurationModulesReq,
  } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetCodeDictionaryModules', {
        method: 'POST',
        data: {
          ModuleGroup: props?.moduleGroup,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<DictionaryModuleItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          processModuleTree(response?.data);
        } else {
          // TODO 提示信息
        }
      },
    },
  );

  const processModuleTree = (modules: DictionaryModuleItem[]) => {
    let treeData = [];
    let allModuleLabels = [];

    let hasInsertedModuleData = new Set();

    modules
      ?.filter((item) => item.Directories && item.Directories?.length > 0)
      .forEach((item) => {
        allModuleLabels.push(...item.Directories);

        if (
          !treeData.find(
            (treeItem) => treeItem.title === item.Directories?.at(0),
          )
        ) {
          treeData.push({
            title: item?.Directories?.at(0),
            key: item?.Directories?.at(0),
            selectable: false,
            titleRenderer: (nodeData) => {
              return (
                <Dropdown
                  menu={{ items: rootItems }}
                  trigger={['contextMenu']}
                  placement="bottomLeft"
                >
                  <span>{nodeData?.title}</span>
                </Dropdown>
              );
            },
          });
        }

        item.Directories?.slice(1)?.forEach((dictionaryItem, index) => {
          let parentTreeData = treeData.find(
            (treeItem) => treeItem.title === item.Directories?.at(0),
          );
          if (parentTreeData) {
            if (!hasInsertedModuleData?.has(item?.Module)) {
              hasInsertedModuleData.add(item?.Module);
              parentTreeData['children'] = [
                ...(parentTreeData['children'] || []),
                {
                  title: dictionaryItem,
                  key: item.Module,
                  data: item,
                },
              ];
            }
          }
        });
      });

    setModuleTreeData(treeData);

    // all labels keys
    setAllModuleKeys(modules?.map((item) => item.Module));
    setAllModuleLabels(allModuleLabels);
  };

  const onDictionaryAddEdit = async (values: any) => {
    let currentModule = {
      ...rightClickItem,
      Module: values?.Module,
      Directories: [values?.ParentFolder, values?.ModuleName],
    };

    // 更新这个tree
    let currentTreeItem = moduleTreeData?.find(
      (item) => item?.key === values?.ParentFolder,
    );

    if (dictionaryOperateType === 'ADD') {
      currentModule['NotExisted'] = true;
      if (currentTreeItem) {
        currentTreeItem['children'] = [
          ...(currentTreeItem['children'] || []),
          {
            key: values?.Module,
            title: values?.ModuleName,
            data: {
              DirectorySort: currentTreeItem['children']?.length,
              ...currentModule,
              ModuleGroup: 'Insur',
            },
          },
        ];

        setModuleTreeData(cloneDeep(moduleTreeData));
      }

      Emitter.emit(
        ConfigurationEvents.DICTIONARY_CONFIGURATION_ADD,
        currentModule,
      );
    }

    if (dictionaryOperateType === 'EDIT') {
      // 更新tree
      let waitForUpdateModule = currentTreeItem['children']?.find(
        (item) => item?.key === rightClickItem?.Module,
      );
      if (waitForUpdateModule) {
        waitForUpdateModule['key'] = currentModule?.Module;
        waitForUpdateModule['title'] = values?.ModuleName;

        waitForUpdateModule['data'] = {
          ...waitForUpdateModule['data'],
          ...currentModule,
          ModuleGroup: 'Insur',
        };

        setModuleTreeData(cloneDeep(moduleTreeData));

        if (!waitForUpdateModule?.data?.NotExisted) {
          dictionaryConfigurationModulesEditReq(waitForUpdateModule?.data);
        }
      }

      Emitter.emit(
        ConfigurationEvents.DICTIONARY_CONFIGURATION_EDIT,
        currentModule,
      );
    }

    setDictionaryOperateType(undefined);

    setRightClickItem(undefined);
  };

  const {
    loading: dictionaryConfigurationModulesEditLoading,
    run: dictionaryConfigurationModulesEditReq,
  } = useRequest(
    (module) => {
      return uniCommonService('Api/Sys/CodeSys/UpdateCodeDictionaryModule', {
        method: 'POST',
        data: {
          Module: module?.Module,
          Directories: module?.Directories,
          DirectorySort: module?.DirectorySort,
          ModuleGroup: props.moduleGroup,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        return response;
      },
    },
  );

  return (
    <div
      id={'dictionary-configuration-container'}
      className={'dictionary-configuration-container'}
    >
      <div className="dictionary-tree-search-container">
        <h3>字典库列表</h3>
        <Input.Search placeholder="搜索..." allowClear onChange={onChange} />
        <Tree.DirectoryTree
          height={480}
          className={'configuration-tree-container'}
          showLine={true}
          showIcon={false}
          switcherIcon={<DownOutlined />}
          // defaultExpandedKeys={['0-0-0']}
          expandedKeys={treeExpandedKeys}
          onExpand={onExpand}
          onSelect={onSelect}
          treeData={treeDataRendered}
          titleRender={(nodeData) => {
            // FIXME 写死两层
            if (nodeData.children) {
              return (
                <Dropdown
                  menu={{ items: rootItems }}
                  trigger={['contextMenu']}
                  placement="bottomLeft"
                >
                  <span>{nodeData?.title}</span>
                </Dropdown>
              );
            } else {
              return (
                <Dropdown
                  menu={{ items: childEditItems }}
                  trigger={['contextMenu']}
                  placement="bottomLeft"
                >
                  <span>{nodeData?.title}</span>
                </Dropdown>
              );
            }
          }}
          onRightClick={({ event, node }) => {
            if (event.type === 'contextmenu') {
              setRightClickItem(node);
            }
          }}
        />
      </div>

      <div className={'configuration-table-container'}>
        <DictionaryTable
          dictionaryGroup={props.moduleGroup}
          selectModule={treeSelectedItem}
          needReload={needReload}
          setNeedReload={setNeedReload}
        />
      </div>

      <Modal
        title={dictionaryOperateType === 'ADD' ? '新增字典库' : '修改字典库'}
        open={dictionaryOperateType}
        onOk={() => {
          onDictionaryAddEdit(form.getFieldsValue());
        }}
        onCancel={() => {
          setDictionaryOperateType(undefined);
        }}
        destroyOnClose={true}
        getContainer={document.getElementById(
          'dictionary-configuration-container',
        )}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <DictionaryAdd
          form={form}
          parentFolders={moduleTreeData?.map((item) => item.key)}
          allModuleLabels={allModuleLabels}
          allModuleKeys={allModuleKeys}
          operateType={dictionaryOperateType}
        />
      </Modal>
    </div>
  );
};

interface DictionaryTableProps {
  selectModule: DictionaryModuleItem;
  dictionaryGroup: string;
  dataSource?: any[];
  needReload?: boolean;
  setNeedReload?: Function;
}

export const DictionaryTable = (props: DictionaryTableProps) => {
  const [form] = Form.useForm();
  const [dictionaryItemAdd, setDictionaryItemAdd] = useState(false);
  const [dictionaryColumns, setDictionaryColumns] = useState<any[]>([]);
  const [dictionaryTableDataSource, setDictionaryTableDataSource] = useState(
    [],
  );
  const ref = useRef<any>();
  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);
  const [treeSelectedItem, setTreeSelectedItem] = useState(undefined);
  useEffect(() => {
    setTreeSelectedItem(props.selectModule);
  }, [props.selectModule]);

  useEffect(() => {
    if (props?.selectModule || props?.needReload) {
      if (props?.selectModule?.NotExisted) {
        let tableDataSource = [];
        setDictionaryTableDataSource(tableDataSource);
      } else {
        console.log('inhere');
        dictionaryReq();
      }
      props?.setNeedReload(false);
    }
  }, [props?.selectModule, props?.needReload]);

  const { run: dictionaryColumnReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetCodeDictionaries', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<TableColumns>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          if (props?.dictionaryGroup === 'Insur') {
            setDictionaryColumns(
              tableColumnBaseProcessor(
                insurDictionaryTableColumns,
                response?.data?.Columns,
              ),
            );
          } else {
            setDictionaryColumns(
              tableColumnBaseProcessor(
                dictionaryTableColumns,
                response?.data?.Columns,
              ),
            );
          }
        } else {
          setDictionaryColumns([]);
        }
      },
    },
  );

  useEffect(() => {
    dictionaryColumnReq();
  }, []);

  const { loading: dictionaryLoading, run: dictionaryReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetCodeDictionaries', {
        method: 'POST',
        data: {
          Module: props?.selectModule?.Module,
          ModuleGroup: props?.dictionaryGroup,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ConfigurationDictionaryItem[]>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          let tableDataSource = response?.data.slice();
          setDictionaryTableDataSource(
            tableDataSource.map((item) => {
              item['id'] = uuidv4();
              return item;
            }),
          );
        } else {
          setDictionaryTableDataSource([]);
        }
      },
    },
  );

  const { loading: dictionaryItemDeleteLoading, run: dictionaryItemDeleteReq } =
    useRequest(
      (values) => {
        return uniCommonService('Api/Sys/CodeSys/DeleteCodeDictionary', {
          method: 'POST',
          data: {
            CodeDictionaryId: values?.CodeDictionaryId,
            Module: props?.selectModule?.Module,
            ModuleGroup: props?.dictionaryGroup,
          },
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<number>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            message.success('删除成功');
          }
        },
        onSuccess: (response, params: any) => {
          dictionaryReq();
        },
      },
    );

  const { loading: dictionaryItemUpsertLoading, run: dictionaryItemUpsertReq } =
    useRequest(
      (values) => {
        return uniCommonService('Api/Sys/CodeSys/UpsertCodeDictionary', {
          method: 'POST',
          data: {
            Module: props?.selectModule?.Module,
            ModuleGroup: props?.dictionaryGroup,
            ...values,
            Directories: props?.selectModule?.Directories,
            DirectorySort: props?.selectModule?.DirectorySort,
          },
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<number>) => {
          if (response?.statusCode === 200) {
            setDictionaryItemAdd(false);
            dictionaryReq();
          }
        },
      },
    );

  const onDictionaryItemAdd = async (values: any) => {
    await dictionaryItemUpsertReq(values);
    form.resetFields();
  };

  useEffect(() => {
    Emitter.on(ConfigurationEvents.BASE_DICT_DELETE, (data) => {
      if (data?.index > -1) {
        dictionaryItemDeleteReq(data.record);
      }
    });

    return () => {
      Emitter.off(ConfigurationEvents.BASE_DICT_DELETE);
    };
  }, [dictionaryTableDataSource]);

  const {
    loading: setCodeDictionaryDefaultLoading,
    run: setCodeDictionaryDefaultReq,
  } = useRequest(
    (values) => {
      let data = {};
      data = {
        ...values,
        Module: props?.selectModule?.Module,
        ModuleGroup: props?.dictionaryGroup,
      };

      return uniCommonService('Api/Sys/CodeSys/SetCodeDictionaryDefault', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
    },
  );

  return (
    <>
      <div
        id={'dictionary-table-container'}
        className={'dictionary-table-container'}
      >
        <div className="d-flex" style={{ justifyContent: 'space-between' }}>
          <h3>
            {props.selectModule ? (
              <>
                <span>{props.selectModule?.Directories?.[0]}</span>
                &nbsp;—&nbsp;
                <span>{treeSelectedItem?.Directories?.[1]}</span>
              </>
            ) : (
              '列表'
            )}
          </h3>
          <div style={{ marginBottom: '0.5em' }}>
            <Space>
              {treeSelectedItem && (
                <ConfigExcelTemplateHandler
                  downloadTemplateApiObj={{
                    apiUrl: 'Api/Sys/CodeSys/GetCodeDictionaryExcelTemplate',
                  }}
                  downloadPostData={{
                    ...treeSelectedItem,
                    moduleGroup: treeSelectedItem?.moduleGroup,
                    exportName: treeSelectedItem?.Directories?.join('-') || '',
                  }}
                  uploadXlsxApiObj={{
                    apiUrl: 'Api/Sys/CodeSys/UploadCodeDictionaryExcelFile',
                    onSuccess: () => {
                      if (treeSelectedItem?.NotExisted) {
                        setTreeSelectedItem({
                          ...treeSelectedItem,
                          NotExisted: false,
                        });
                      } else {
                        props.setNeedReload(true);
                      }
                    },
                  }}
                  uploadPostData={{
                    ...treeSelectedItem,
                    moduleGroup: treeSelectedItem?.moduleGroup,
                  }}
                  cleanBeforeUpsert
                />
              )}
              {treeSelectedItem && (
                <Button
                  key="add"
                  loading={false}
                  onClick={(e) => {
                    setDictionaryItemAdd(true);
                    form.setFieldValue('IsValid', true);
                  }}
                >
                  新增字典项
                </Button>
              )}
            </Space>
          </div>
        </div>
        <UniEditableTable
          id={`${props?.selectModule?.Module}-dictionary-table`}
          rowKey={'id'}
          scroll={{
            y: 480,
            // y: document.getElementById('content')?.offsetHeight - 20 - 32,
            x: 'max-content',
          }}
          bordered={true}
          loading={
            dictionaryLoading ||
            dictionaryItemUpsertLoading ||
            dictionaryItemDeleteLoading
          }
          columns={dictionaryColumns}
          value={dictionaryTableDataSource}
          clickable={false}
          pagination={false}
          actionRef={ref}
          recordCreatorProps={false}
          editable={{
            form: form,
            type: 'multiple',
            editableKeys: editableColumnKeys,
            onSave: async (rowKey, data, row) => {
              setCodeDictionaryDefaultReq(data);
              dictionaryItemUpsertReq(data);
            },
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.save, defaultDoms.cancel];
            },
            onChange: setEditableColumnKeys,
          }}
        />
      </div>

      <Modal
        title={`新增字典项`}
        open={dictionaryItemAdd}
        onOk={() => {
          onDictionaryItemAdd(form.getFieldsValue());
        }}
        onCancel={() => {
          setDictionaryItemAdd(false);
        }}
        destroyOnClose={true}
        getContainer={document.getElementById('dictionary-table-container')}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <DictionaryItemAdd form={form} />
      </Modal>
    </>
  );
};

export default DictionaryConfiguration;
