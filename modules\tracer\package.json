{"name": "tracer", "private": true, "scripts": {"start": "umi dev", "build": "umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "dependencies": {"@ant-design/pro-components": "2.3.58", "@ant-design/pro-descriptions": "2.0.49", "@uni/commons": "workspace:^", "@uni/components": "workspace:^", "@uni/services": "workspace:^", "@uni/utils": "workspace:^", "ahooks": "3.7.1", "mammoth": "^1.9.0", "qs": "^6.11.0", "react": "18.x", "react-dom": "18.x", "react-image-gallery": "^1.2.9", "react-to-print": "^2.14.13", "umi": "3.5.34"}, "devDependencies": {"@types/react": "18.x", "@types/react-dom": "18.x", "@umijs/plugin-qiankun": "^2.42.0", "@umijs/preset-react": "1.x", "@umijs/test": "3.5.34", "babel-loader": "^8.2.5", "babel-plugin-import": "^1.13.5", "typescript": "^4.1.2", "yorkie": "^2.0.0"}}