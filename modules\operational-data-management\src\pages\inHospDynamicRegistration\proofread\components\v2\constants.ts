/**
 * 日报校对页面常量定义
 */

// 通用事件常量
export const CommonOdmEventConstants = {
  DBCLK_ON_EDITABLE_ITEM: 'odm:dbclk_on_editable_item',
  CALENDAR_CARD_CLK: 'odm:calendar_card_click',
  SEARCH_PARAMS_CHANGED_WHEN_EDITING: 'odm:search_params_changed_when_editing',
  DRAWER_CLOSE_REQUEST: 'odm:drawer_close_request',
  DRAWER_CLOSE_CONFIRM: 'odm:drawer_close_confirm',
};

// 日报校对专用事件常量
export const DailyProofreadEventConstants = {
  // 行操作事件
  ROW_EDIT: 'daily_proofread:row_edit', // 编辑行
  ROW_LOCK: 'daily_proofread:row_lock', // 锁定行
  ROW_UNLOCK: 'daily_proofread:row_unlock', // 解锁行
  DATA_REFRESH: 'daily_proofread:data_refresh', // 数据刷新
  SAVE_SUCCESS: 'daily_proofread:save_success', // 保存成功
  SAVE_FAILED: 'daily_proofread:save_failed', // 保存失败

  // 表格操作事件
  TABLE_CHANGE: 'daily_proofread:table_change', // 表格变化
  COLUMN_SETTING: 'daily_proofread:column_setting', // 列设置

  // 快捷键事件
  KEY_SAVE: 'daily_proofread:key_save', // 保存快捷键
  KEY_CANCEL: 'daily_proofread:key_cancel', // 取消快捷键
  KEY_NAV_UP: 'daily_proofread:key_nav_up', // 向上导航
  KEY_NAV_DOWN: 'daily_proofread:key_nav_down', // 向下导航
  KEY_NAV_LEFT: 'daily_proofread:key_nav_left', // 向左导航
  KEY_NAV_RIGHT: 'daily_proofread:key_nav_right', // 向右导航
};

// 导出详情列 - 用于DetailModal
export const detailColumns = [];
