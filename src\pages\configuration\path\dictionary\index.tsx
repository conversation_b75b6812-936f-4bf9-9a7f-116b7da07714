import {
  Dropdown,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Spin,
  Tabs,
  Tree,
  Card,
  Space,
  Button,
  Radio,
  Switch,
  Upload,
} from 'antd';
import { UniTable } from '@uni/components/src';
import React, { useEffect, useState, useRef } from 'react';
import { comparesDictColumns } from '@/pages/configuration/path/columns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { v4 as uuidv4 } from 'uuid';
import { useRequest, useSelector } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import {
  ConfigurationDictionaryItem,
  DictionaryItemCompare,
  DictionaryModuleItem,
} from '@/pages/configuration/base/interfaces';
import './index.less';
import DictionaryRelationAdd from '@/pages/configuration/base/dictionary/components/relation-add';
import { useModel } from '@@/plugin-model/useModel';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { DownOutlined, UploadOutlined } from '@ant-design/icons';
import {
  getMinusCompareCollectionsWithDoubleCollections,
  getTreeFilterWithSearchedText,
} from '@/pages/configuration/utils';
import cloneDeep from 'lodash/cloneDeep';
import trim from 'lodash/trim';
import isEmpty from 'lodash/isEmpty';
import ConfigExcelTemplateHandler from '@/components/configExcelTemplateHandler';
import UniEditableTable from '@uni/components/src/table/edittable';
import {
  dmrMetaDataService,
  insurMetaDataService,
  metaDataService,
} from '@uni/services/src';
import { useUpdateEffect } from 'ahooks';
import ComparesAdd from '@/pages/configuration/path/components/compares/add';
import { isEmptyValues } from '@uni/utils/src/utils';

const Event = 'DmrDict';

interface DictionaryConfigurationProps {}

const DictionaryConfiguration = (props?: DictionaryConfigurationProps) => {
  const moduleGroup = 'Dmr';

  const [form] = Form.useForm();

  const [rightClickItem, setRightClickItem] = useState(undefined);

  const [moduleTreeData, setModuleTreeData] = useState([]);

  const [treeDataRendered, setTreeDataRendered] = useState([]);

  const [treeExpandedKeys, setTreeExpandedKeys] = useState<React.Key[]>([
    '0-0-0',
  ]);

  const [searchText, setSearchText] = useState('');

  const [treeSelectedItem, setTreeSelectedItem] = useState(undefined);

  const [dictionaryOperateType, setDictionaryOperateType] = useState(undefined);

  const [allModuleLabels, setAllModuleLabels] = useState([]);
  const [allModuleKeys, setAllModuleKeys] = useState([]);

  // excel template download/upload part
  const [needReload, setNeedReload] = useState(false);

  // globalState
  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateForSlave',
  );

  useEffect(() => {
    dictionaryConfigurationModulesReq();
  }, []);

  useEffect(() => {
    Emitter.on(
      ConfigurationEvents.DICTIONARY_CONFIGURATION_GLOBAL_UPDATE,
      async (data) => {
        updateGlobalState(
          data?.module?.Module,
          moduleGroup,
          data?.tableData.filter((item) => item.id !== 'ADD'),
        );
      },
    );

    return () => {
      Emitter.off(ConfigurationEvents.DICTIONARY_CONFIGURATION_GLOBAL_UPDATE);
    };
  }, [globalState]);

  const updateGlobalState = (module, moduleGroup, tableData) => {
    let dictData = globalState?.dictData;
    if (moduleGroup) {
      let moduleDictData = globalState?.dictData?.[moduleGroup];
      if (tableData && tableData?.length > 0) {
        moduleDictData[module] = tableData;
      } else {
        delete moduleDictData[module];
      }
      dictData[moduleGroup] = moduleDictData;
    } else {
      if (tableData && tableData?.length > 0) {
        dictData[module] = tableData;
      } else {
        delete dictData[module];
      }
    }

    setQiankunGlobalState({
      ...globalState,
      dictData: dictData,
    });
  };

  const onExpand = (expandedKeys: React.Key[]) => {
    setTreeExpandedKeys(expandedKeys);
  };

  const onSelect = (selectedKeys: React.Key[], info: any) => {
    setTreeSelectedItem(info?.node?.data);

    if (info?.node?.data) {
      Emitter.emit(ConfigurationEvents.DMR_DICT_GET, info?.node?.data);
      Emitter.emit(
        ConfigurationEvents.DMR_DICT_SELECT_MODULE,
        info?.node?.data?.Module,
      );
    }
  };

  // search
  const onChange = (e) => {
    const { value } = e.target;
    setSearchText(value);
  };

  useEffect(() => {
    if (!isEmpty(trim(searchText))) {
      const searchedTreeData = getTreeFilterWithSearchedText(
        searchText,
        moduleTreeData,
      );
      // 自动展开
      setTreeExpandedKeys(searchedTreeData.map((d) => d.key));
      setTreeDataRendered(searchedTreeData);
    } else {
      setTreeDataRendered(moduleTreeData);
    }
  }, [moduleTreeData, searchText]);

  const {
    loading: dictionaryConfigurationModulesLoading,
    run: dictionaryConfigurationModulesReq,
  } = useRequest(
    () => {
      return uniCommonService(
        'Api/Sys/CodeSys/GetDmrCodeDictionaryModulesWithCompareCnt',
        {
          method: 'POST',
          data: {
            ModuleGroup: 'Dmr',
          },
        },
      );
    },
    {
      manual: true,
      formatResult: async (response: RespVO<DictionaryModuleItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          processModuleTree(response?.data);
        } else {
          // TODO 提示信息
        }
      },
    },
  );

  const processModuleTree = (modules: DictionaryModuleItem[]) => {
    let treeData = [];
    let allModuleLabels = [];

    let hasInsertedModuleData = new Set();

    modules
      ?.filter((item) => item.Directories && item.Directories?.length > 0)
      .forEach((item) => {
        allModuleLabels.push(...item.Directories);

        if (
          !treeData.find(
            (treeItem) => treeItem.title === item.Directories?.at(0),
          )
        ) {
          treeData.push({
            title: item?.Directories?.at(0),
            key: item?.Directories?.at(0),
            // compareCnt:item?.CompareCnt,
            selectable: false,
            titleRenderer: (nodeData) => {
              return <span>{nodeData?.title}</span>;
            },
          });
        }

        item.Directories?.slice(1)?.forEach((dictionaryItem, index) => {
          let parentTreeData = treeData.find(
            (treeItem) => treeItem.title === item.Directories?.at(0),
          );
          if (parentTreeData) {
            if (!hasInsertedModuleData?.has(item?.Module)) {
              hasInsertedModuleData.add(item?.Module);
              parentTreeData['children'] = [
                ...(parentTreeData['children'] || []),
                {
                  title: dictionaryItem,
                  key: item.Module,
                  data: item,
                },
              ];
            } else {
              let childModuleItem = parentTreeData['children']?.find(
                (childItem) => childItem?.key === item.Module,
              );
              let compareCount =
                (childModuleItem['data']?.CompareCnt ?? 0) +
                (item?.CompareCnt ?? 0);
              childModuleItem['data']['CompareCnt'] = compareCount;
            }
          }
        });
      });

    setModuleTreeData(treeData);

    // all labels keys
    setAllModuleKeys(modules?.map((item) => item.Module));
    setAllModuleLabels(allModuleLabels);
  };

  const onDictionaryAddEdit = async (values: any) => {
    let currentModule = {
      ...rightClickItem,
      Module: values?.Module,
      Directories: [values?.ParentFolder, values?.ModuleName],
    };

    // 更新这个tree
    let currentTreeItem = moduleTreeData?.find(
      (item) => item?.key === values?.ParentFolder,
    );

    if (dictionaryOperateType === 'ADD') {
      currentModule['NotExisted'] = true;
      if (currentTreeItem) {
        currentTreeItem['children'] = [
          ...(currentTreeItem['children'] || []),
          {
            key: values?.Module,
            title: values?.ModuleName,
            data: {
              DirectorySort: currentTreeItem['children']?.length,
              ...currentModule,
              ModuleGroup: 'Dmr',
            },
          },
        ];

        setModuleTreeData(cloneDeep(moduleTreeData));
        setTreeExpandedKeys([currentModule?.Directories?.at(0)]);
      }

      Emitter.emit(
        ConfigurationEvents.DICTIONARY_CONFIGURATION_ADD,
        currentModule,
      );
    }

    if (dictionaryOperateType === 'EDIT') {
      // 更新tree
      let waitForUpdateModule = currentTreeItem['children']?.find(
        (item) => item?.key === rightClickItem?.Module,
      );
      if (waitForUpdateModule) {
        waitForUpdateModule['key'] = currentModule?.Module;
        waitForUpdateModule['title'] = values?.ModuleName;

        waitForUpdateModule['data'] = {
          ...waitForUpdateModule['data'],
          ...currentModule,
          ModuleGroup: 'Dmr',
        };

        setModuleTreeData(cloneDeep(moduleTreeData));

        if (!waitForUpdateModule?.data?.NotExisted) {
          dictionaryConfigurationModulesEditReq(waitForUpdateModule?.data);
        }
      }

      Emitter.emit(
        ConfigurationEvents.DICTIONARY_CONFIGURATION_EDIT,
        currentModule,
      );
    }

    setDictionaryOperateType(undefined);

    setRightClickItem(undefined);
  };

  const {
    loading: dictionaryConfigurationModulesEditLoading,
    run: dictionaryConfigurationModulesEditReq,
  } = useRequest(
    (module) => {
      return uniCommonService('Api/Sys/CodeSys/UpdateCodeDictionaryModule', {
        method: 'POST',
        data: {
          Module: module?.Module,
          Directories: module?.Directories,
          DirectorySort: module?.DirectorySort,
          ModuleGroup: moduleGroup,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        return response;
      },
    },
  );

  return (
    <div
      id={'dictionary-configuration-container'}
      className={'dictionary-configuration-container'}
    >
      <div>
        <div className="dictionary-tree-search-container">
          <h3>字典库列表</h3>
          <Input.Search placeholder="搜索..." allowClear onChange={onChange} />
          <Tree.DirectoryTree
            height={480}
            className={'configuration-tree-container'}
            showLine={true}
            showIcon={false}
            switcherIcon={<DownOutlined />}
            // defaultExpandedKeys={['0-0-0']}
            expandedKeys={treeExpandedKeys}
            onExpand={onExpand}
            onSelect={onSelect}
            treeData={treeDataRendered}
            titleRender={(nodeData) => {
              // FIXME 写死两层
              if (nodeData.children) {
                return <>{nodeData?.title}</>;
              } else {
                return (
                  <>
                    {nodeData?.title}({nodeData?.data?.CompareCnt || 0})
                  </>
                );
              }
            }}
            onRightClick={({ event, node }) => {
              if (event.type === 'contextmenu') {
                setRightClickItem(node);
              }
            }}
          />
        </div>
      </div>
      <div className={'configuration-table-container'}>
        <DictionaryTable
          dictionaryGroup={moduleGroup}
          selectModule={treeSelectedItem}
          needReload={needReload}
          setNeedReload={setNeedReload}
        />
      </div>
    </div>
  );
};

interface DictionaryTableProps {
  selectModule: DictionaryModuleItem;
  dictionaryGroup: string;
  dataSource?: any[];
  needReload?: boolean;
  setNeedReload?: Function;
}

export const DictionaryTable = (props: DictionaryTableProps) => {
  // globalState
  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateForSlave',
  );

  const [form] = Form.useForm();

  const [dictionaryColumns, setDictionaryColumns] = useState<any[]>([]);

  const [dictionaryTableDataSource, setDictionaryTableDataSource] = useState(
    [],
  );

  const [treeSelectedItem, setTreeSelectedItem] = useState(undefined);

  const [dmrDictionaryAdd, setDmrDictionaryAdd] = useState(false);

  const ref = useRef<any>();
  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  const {
    loading: dictionaryAllComparesLoading,
    run: dictionaryAllComparesReq,
  } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetDmrCodeDictionaryCompares', {
        method: 'POST',
        data: {
          Module: props?.selectModule?.Module,
          ModuleGroup: props?.dictionaryGroup,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ConfigurationDictionaryItem[]>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          let tableDataSource = response?.data.slice();
          setDictionaryTableDataSource(
            tableDataSource.map((item) => {
              item['id'] = uuidv4();
              return item;
            }),
          );
        } else {
          setDictionaryTableDataSource([]);
        }
      },
    },
  );

  useEffect(() => {
    if (props?.selectModule || props?.needReload) {
      if (props?.selectModule?.NotExisted) {
        let tableDataSource = [];
        setDictionaryTableDataSource(tableDataSource);
      } else {
        console.log('inhere');
        dictionaryAllComparesReq();
      }

      // setCurrentAddEditModule(props?.selectModule);

      props?.setNeedReload(false);
    }
  }, [props?.selectModule, props?.needReload]);

  const { run: dictionaryAllComparesColumnReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetDmrCodeDictionaryCompares', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          return response.data?.Columns;
        } else {
          return [];
        }
      },
      onSuccess: (data, params) => {
        setDictionaryColumns(
          tableColumnBaseProcessor(
            comparesDictColumns(props?.selectModule?.Module),
            data,
          ),
        );
      },
    },
  );

  // useEffect(() => {
  //   dictionaryAllComparesColumnReq();
  // }, []);

  useUpdateEffect(() => {
    if (!isEmptyValues(props?.selectModule)) {
      dictionaryAllComparesColumnReq();
    }
  }, [props?.selectModule, globalState]);

  const { loading: dictionaryItemUpsertLoading, run: dictionaryItemUpsertReq } =
    useRequest(
      (values) => {
        let data = {};
        data = {
          ...values,
          Directories: props?.selectModule?.Directories,
          DirectorySort: props?.selectModule?.DirectorySort,
          Module: props?.selectModule?.Module,
          ModuleGroup: props?.dictionaryGroup,
        };

        return uniCommonService(
          'Api/Sys/CodeSys/UpsertDmrCodeDictionaryCompare',
          {
            method: 'POST',
            data: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<number>) => {
          if (response?.statusCode === 200) {
            setDmrDictionaryAdd(false);
            dictionaryAllComparesReq();
          }
        },
      },
    );

  useEffect(() => {
    setTreeSelectedItem(props.selectModule);
  }, [props.selectModule]);

  const onDmrDictionaryRelationAdd = async (values: any) => {
    dictionaryItemUpsertReq(values);
    form.resetFields();
  };

  const { loading: dictionaryItemDeleteLoading, run: dictionaryItemDeleteReq } =
    useRequest(
      (values) => {
        return uniCommonService(
          'Api/Sys/CodeSys/DeleteDmrCodeDictionaryCompare',
          {
            method: 'POST',
            data: {
              compareId: values.CompareId,
              Module: props?.selectModule?.Module,
              ModuleGroup: props?.dictionaryGroup,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<number>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            message.success('删除成功');
          }
        },
        onSuccess: (response, params: any) => {
          dictionaryAllComparesReq();
        },
      },
    );

  useEffect(() => {
    Emitter.onMultiple([`${ConfigurationEvents.DMR_SELECT_CODE}`], (data) => {
      let currentFormValue = form.getFieldsValue()?.[data?.id];
      if (currentFormValue) {
        // set form value
        Object.keys(data?.values)?.forEach((key) => {
          currentFormValue[key] = data?.values?.[key];
        });
        form.setFieldValue(data?.id, currentFormValue);
      }
    });

    Emitter.on(ConfigurationEvents.DMR_ITEM_DELETE, (data) => {
      if (data?.index > -1) {
        dictionaryItemDeleteReq(data.record);
      }
    });

    return () => {
      Emitter.offMultiple([`${ConfigurationEvents.DMR_SELECT_CODE}`]);
      Emitter.off(ConfigurationEvents.DMR_ITEM_DELETE);
    };
  }, [dictionaryTableDataSource]);

  const getMetaData = async (module, moduleGroup) => {
    let response = await metaDataService(module, moduleGroup);

    if (response?.code === 0) {
      if (response?.statusCode === 200) {
        // setState(response?.data);
        let dictData = globalState?.dictData;
        let moduleDictData = dictData[moduleGroup] || {};
        moduleDictData[module] = response?.data;
        dictData[moduleGroup] = moduleDictData;
        setQiankunGlobalState({
          ...globalState,
          dictData: dictData,
        });
        return;
      }
    }
  };

  useEffect(() => {
    Emitter.on(ConfigurationEvents.DMR_DICT_GET, async (data) => {
      getMetaData(data.Module, 'Dmr');
    });
    return () => {
      Emitter.off(ConfigurationEvents.DMR_DICT_GET);
    };
  }, [globalState]);

  return (
    <>
      <div
      // id={'dictionary-table-container'}
      // className={'dictionary-table-container'}
      >
        <div className="d-flex" style={{ justifyContent: 'space-between' }}>
          <h3>
            {props.selectModule ? (
              <>
                <span>{props.selectModule?.Directories?.[0]}</span>
                &nbsp;—&nbsp;
                <span>{treeSelectedItem?.Directories?.[1]}</span>
              </>
            ) : (
              '列表'
            )}
          </h3>
          <div style={{ marginBottom: '0.5em' }}>
            <Space>
              {treeSelectedItem && (
                <ConfigExcelTemplateHandler
                  downloadTemplateApiObj={{
                    apiUrl:
                      'Api/Sys/CodeSys/GetDmrCodeDictionaryCompareExcelTemplate',
                  }}
                  downloadPostData={{
                    ...treeSelectedItem,
                    moduleGroup: treeSelectedItem?.moduleGroup,
                    exportName: treeSelectedItem?.Directories?.join('-') || '',
                  }}
                  uploadXlsxApiObj={{
                    apiUrl:
                      'Api/Sys/CodeSys/UploadDmrCodeDictionaryCompareExcelFile',
                    onSuccess: () => {
                      if (treeSelectedItem?.NotExisted) {
                        setTreeSelectedItem({
                          ...treeSelectedItem,
                          NotExisted: false,
                        });
                      } else {
                        props.setNeedReload(true);
                      }
                    },
                  }}
                  uploadPostData={{
                    ...treeSelectedItem,
                    moduleGroup: treeSelectedItem?.moduleGroup,
                  }}
                  cleanBeforeUpsert
                />
              )}
              {treeSelectedItem && (
                <Button
                  key="add"
                  loading={false}
                  onClick={(e) => {
                    setDmrDictionaryAdd(true);
                    form.setFieldValue('IsValid', true);
                  }}
                >
                  新增字典项
                </Button>
              )}
            </Space>
          </div>
        </div>
        <UniEditableTable
          rowKey={'id'}
          loading={
            dictionaryItemUpsertLoading ||
            dictionaryAllComparesLoading ||
            dictionaryItemDeleteLoading
          }
          columns={dictionaryColumns}
          value={dictionaryTableDataSource}
          forceColumnsUpdate={true}
          clickable={false}
          pagination={false}
          actionRef={ref}
          id={`dict-dictionary-table`}
          className={'dict-dictionary-table'}
          scroll={{
            y: 480,
            // y: document.getElementById('content')?.offsetHeight - 20 - 32,
            x: 'max-content',
          }}
          bordered={true}
          recordCreatorProps={false}
          editable={{
            form: form,
            type: 'multiple',
            editableKeys: editableColumnKeys,
            onSave: async (rowKey, data, row) => {
              dictionaryItemUpsertReq(data);
            },
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.save, defaultDoms.cancel];
            },
            onChange: setEditableColumnKeys,
          }}
        />

        <Modal
          title="新增字典项"
          open={dmrDictionaryAdd}
          onOk={() => {
            onDmrDictionaryRelationAdd(form.getFieldsValue());
          }}
          onCancel={() => {
            setDmrDictionaryAdd(false);
          }}
          destroyOnClose={true}
          getContainer={document.getElementById('dict-dictionary-table')}
          okButtonProps={{
            htmlType: 'submit',
          }}
        >
          <ComparesAdd
            form={form}
            type={'dict'}
            selectedModuleKey={props?.selectModule?.Module}
          />
        </Modal>
      </div>
    </>
  );
};

export default DictionaryConfiguration;
