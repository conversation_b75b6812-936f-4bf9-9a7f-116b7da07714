import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useReducer,
  Reducer,
} from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { insurMetaDataService } from '@uni/services/src';
import { v4 as uuidv4 } from 'uuid';

import {
  Button,
  message,
  Card,
  Switch,
  Col,
  Progress,
  Row,
  Spin,
  TableProps,
  Tooltip,
  Menu,
  Table,
  Tag,
  Badge,
  Statistic,
  Divider,
  Collapse,
  List,
  Descriptions,
  Space,
  Form,
} from 'antd';
import { Link, useRequest, useModel } from 'umi';
import { ExportIconBtn, UniSelect } from '@uni/components/src';
import UniTable from '@uni/components/src/table';
import { uniCommonService } from '@uni/services/src';
import {
  DictionaryItem,
  BasePageProps,
  RespVO,
  TableColumns,
  TableCardResp,
  TableResp,
} from '@uni/commons/src/interfaces';
import _ from 'lodash';
import { SaveOutlined } from '@ant-design/icons';
import {
  InitTableState,
  TableAction,
  modalReducer,
  tableReducer,
  tableEditPropsReducer,
  EditableTableAction,
} from '@uni/reducers/src';
import {
  IReducer,
  IModalState,
  ITableState,
} from '@uni/reducers/src/interface';
import './index.less';
import { StatsAnalysisEventConstant, SurgeryTags } from '@/constants';
import { uniStatsSequenceService } from '@uni/services/src';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';

import {
  CombineQueryDetail,
  DetailColumnItem,
  MetricsAggregateItem,
  MetricsGroupItem,
  MetricsMetricItem,
} from './interface';
import { columnSettingColumnsProcessor } from '@/components/columnSettings/utils';
import ColumnsSettingsComponent from '@/components/columnSettings';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { dataTableFilterSorterHandler } from '@/utils';
import { SorterResult } from 'antd/lib/table/interface';
import IconBtn from '@uni/components/src/iconBtn';
import { selectedTableColumnsProcessor } from '@/pages/combine-query/processor';
import { defaultCheckedColumnStateProcessor } from '@/components/queryDetailColumnsSettings/utils';
import DetailColumnsSettingModal from '@/components/queryDetailColumnsSettings/modal';
import { DetailColumnSettingContentConstants } from '@/components/queryDetailColumnsSettings/constants';

const TABLENAME = 'OmniCard';

interface detailProps {
  title?: string;
  selectedTags: any[];
  id: string;
  modalVisible?: boolean;
}
const DetailContainer = (props: detailProps) => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster');

  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  // 后端分页onchange
  const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    if (searchParams && props?.selectedTags?.length > 0) {
      combineQueryDataReq(
        {
          ...searchParams,
          selectedTags: props?.selectedTags,
          code: props?.id,
          outputColumns: selectedTableColumnsProcessor(columnsState),
        },
        pagi.current,
        pagi.pageSize,
      );
    }
  };

  // 这边是明细的 指标的还没搞，直接用combine-query内部的
  // 初始 columns 状态
  const [originColumnsState, setOriginColumnState] = useState<any>(undefined);
  // 实时 columns 状态
  const [columnsState, setColumnState] = useState<any>({});

  // 一切columns，最终展示的部分都在TableReducer的columns内，这边的都只是用来处理的数据

  // 获取获取全部可用于展示的subject (totalColumns)
  const {
    data: retColSubjectList,
    loading: getRetColSubjectLoading,
    run: getRetColSubjectReq,
  } = useRequest(
    () => {
      const data = {
        TableName: TABLENAME,
      };
      return uniCombineQueryService(
        'Api/Analysis/AnaModelDef/GetRetColSubjectList',
        {
          params: data,
        },
      );
    },
    {
      formatResult: (response: RespVO<DetailColumnItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let tableColumns = response?.data?.slice();
          // 额外设置几个参数再返回
          tableColumns?.forEach((item, index) => {
            item['data'] = item?.name;
            item['dataIndex'] = item?.name;
            item['originTitle'] = item?.title;
          });
          return tableColumns;
        } else {
          return [];
        }
      },
    },
  );

  // 获取当前显示 columnsIdList
  const {
    data: currentColumnsTemplate,
    loading: columnsTemplateReqLoading,
    run: columnsTemplateReq,
  } = useRequest(
    () => {
      return uniCombineQueryService(
        'Api/V2/DmrAnalysis/IcdCategoryAnalysis/GetIcdeDetailsTemplate',
        {
          method: 'POST',
        },
      );
    },
    {
      formatResult: (response: RespVO<DetailColumnItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          return response.data;
        } else {
          return [];
        }
      },
    },
  );

  // 获取datasource
  const { loading: combineQueryDataLoading, run: combineQueryDataReq } =
    useRequest(
      (reqData, current, pageSize) => {
        let data = {
          DtParam: {
            Draw: 1,
            Start: (current - 1) * pageSize,
            Length: pageSize,
          },
          icdGrouperCols: [
            {
              isMain: searchParams?.IsMain ?? false,
              groupOption: reqData?.selectedTags?.at(0),
            },
          ],
          basicArgs: {
            hospCode: searchParams?.hospCodes,
            cliDepts: searchParams?.CliDepts,
            sdate: searchParams?.dateRange?.at(0) ?? null,
            edate: searchParams?.dateRange?.at(1) ?? null,
          },
          outputColumns: reqData?.outputColumns,
          icdCode: reqData?.code ?? undefined,
        };
        console.log(current, pageSize);
        return uniCombineQueryService(
          'Api/V2/DmrAnalysis/IcdCategoryAnalysis/GetIcdeDetails',
          // 'Api/DmrAnalysis/IcdCategoryAnalysis/GetDetails',
          {
            method: 'POST',
            requestType: 'json',
            data: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<TableResp<any, any>>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            TableDispatch({
              type: TableAction.dataChange,
              payload: {
                data: response?.data?.data?.map((d) => ({
                  ...d,
                  uuid: uuidv4(),
                })),
              },
            });

            setBackPagination({
              ...backPagination,
              total: response?.data?.recordsTotal || 0,
            });
          } else {
            TableDispatch({
              type: TableAction.dataChange,
              payload: {
                data: [],
              },
            });
            setBackPagination({
              ...backPagination,
              total: 0,
            });
          }
        },
      },
    );

  const getSelectedColumns = () => {
    return selectedTableColumnsProcessor(columnsState);
  };

  // 处理 columnsState [在2个columns都获取完毕后]
  useEffect(() => {
    // currentColumns = 后端保存的columnIdList 跟totalColumns 拼接获得columnState
    if (
      retColSubjectList?.length > 0 &&
      Array.isArray(currentColumnsTemplate)
    ) {
      // defaultCheckedColumnStateProcessor 处理 columnState
      let columnState = defaultCheckedColumnStateProcessor(
        retColSubjectList,
        currentColumnsTemplate,
      );
      setColumnState(columnState);
      setOriginColumnState(_.cloneDeep(columnState));

      // 真正给table使用的columns在这里
      TableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: tableColumnBaseProcessor(
            [],
            retColSubjectList?.map((col) => {
              return {
                ...col,
                title: columnState?.[col.name]?.title ?? col?.title,
              };
            }) as any,
          ),
        },
      });
    }
  }, [currentColumnsTemplate, retColSubjectList]);

  // 根据columnsState 处理 TableReducer.columns
  // useEffect(() => {
  //   if (columnsState) {
  //     let tempColumns = Object.keys(columnsState)
  //       ?.filter((d) => columnsState?.[d]?.show)
  //       ?.map((d) => columnsState?.[d]);
  //     console.log('tempColumns', columnsState, tempColumns);
  //     TableDispatch({
  //       type: TableAction.columnsChange,
  //       payload: {
  //         columns: tableColumnBaseProcessor([], tempColumns),
  //       },
  //     });
  //   }
  // }, [columnsState]);

  // 重新调接口？应该是改变了参数的时候
  useEffect(() => {
    let pagination = {
      ...backPagination,
      current: 1,
      pageSize: 10,
      total: 0,
    };
    if (
      searchParams &&
      props?.selectedTags &&
      columnsState &&
      props?.modalVisible
    ) {
      combineQueryDataReq(
        {
          ...searchParams,
          selectedTags: props?.selectedTags,
          code: props?.id,
          outputColumns: selectedTableColumnsProcessor(columnsState),
        },
        pagination.current,
        pagination.pageSize,
      );
    }

    setBackPagination(pagination);
    // TableDispatch({ type: TableAction.dataChange, payload: { data: [] } });
  }, [
    searchParams,
    props?.selectedTags,
    props?.id,
    TableState?.columns,
    props?.modalVisible,
  ]);

  const getExportCaptionByColumns = (
    columns: any[],
    selectedColumns: any[],
  ) => {
    let exportCaption = selectedColumns?.map((d) => {
      let columnItem = columns?.find((item) => item?.id === d?.Id);
      return {
        ...d,
        ExportTitle: d?.CustomTitle ?? columnItem?.title ?? '',
      };
    });

    return exportCaption;
  };

  // 保存 columnTemplate
  const {
    loading: saveColumnsReqLoading,
    run: saveColumnsReq,
    // data: columnsData,
  } = useRequest(
    (data) => {
      return uniCommonService(
        `Api/V2/DmrAnalysis/IcdCategoryAnalysis/SaveIcdeDetailsTemplate`,
        {
          method: 'POST',
          data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          Emitter.emit(
            `${DetailColumnSettingContentConstants.MODAL_CLOSE}_${'Disease'}`,
          );
          columnsTemplateReq();
        }
      },
    },
  );

  const saveColumnsHandler = (selectedItems) => {
    saveColumnsReq({
      Subjects: selectedItems?.map((item) => {
        return {
          SubjectId: item.id,
          ColumnSort: item?.order,
          CustomTitle: item?.customTitle,
        };
      }),
    });
  };

  // 监听主页面传过来的参数，重新请求数据
  useEffect(() => {
    if (
      columnsState &&
      Object.keys(columnsState).length > 0 &&
      props.modalVisible
    ) {
      // 发送当前选中的列信息和请求参数给主页面
      Emitter.emit('DISEASE_DETAIL_EXPORT_DATA', {
        outputColumns: getExportCaptionByColumns(
          TableState.columns,
          getSelectedColumns(),
        ),
        requestData: {
          icdGrouperCols: [
            {
              isMain: searchParams?.IsMain ?? false,
              groupOption: props?.selectedTags?.at(0),
            },
          ],
          basicArgs: {
            hospCode: searchParams?.hospCodes,
            cliDepts: searchParams?.CliDepts,
            sdate: searchParams?.dateRange?.at(0) ?? null,
            edate: searchParams?.dateRange?.at(1) ?? null,
          },
          icdCode: props?.id ?? undefined,
        },
      });
    }
  }, [
    columnsState,
    props.selectedTags,
    props.id,
    searchParams,
    props.modalVisible,
    TableState.columns,
  ]);

  console.log('newestColumns detailColumns', columnsState);

  return (
    <>
      <Space style={{ float: 'right' }}></Space>
      <UniTable
        id="desease-table-detail"
        rowKey={'id'}
        loading={
          getRetColSubjectLoading ||
          columnsTemplateReqLoading ||
          combineQueryDataLoading
        }
        columnsState={{
          value: columnsState,
        }}
        dictionaryData={dictData}
        className="table-container"
        forceColumnsUpdate={true}
        columns={TableState.columns}
        scroll={{ x: 'max-content' }}
        dataSource={TableState?.data}
        onChange={backTableOnChange}
        pagination={backPagination}
      />
      {/* 明细列配置modal */}
      <DetailColumnsSettingModal
        type={'Disease'}
        detailColumns={TableState.columns}
        columnState={columnsState}
        onOk={saveColumnsHandler}
      />
    </>
  );
};

export default DetailContainer;
