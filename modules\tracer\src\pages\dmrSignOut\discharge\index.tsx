import React, {
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import { useRequest } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { UniTable } from '@uni/components/src';
import PdfPrint from '@uni/components/src/pdf-print';
import { uniCommonService } from '@uni/services/src';
import {
  tableReducer,
  TableAction,
  InitTableState,
} from '@uni/reducers/src/tableReducer';
import {
  Alert,
  Button,
  Card,
  Col,
  Divider,
  InputRef,
  Modal,
  Popconfirm,
  Row,
  Space,
  Tooltip,
  message,
  Tag,
} from 'antd';
import { useSafeState, useKeyPress } from 'ahooks';
import { RespVO } from '@uni/commons/src/interfaces';
import { DmrSignOutRecord } from '../interface';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { PrinterOutlined, UndoOutlined } from '@ant-design/icons';
import PatTimeline from '@/components/PatTimeline';
import { useTimelineReq } from '@/hooks';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { DmrSignOutColumns, DmrSignOutModalColumns } from './constants';
import { DmrSignOutFormItems } from './formItems';
import { isEmptyValues } from '@uni/utils/src/utils';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { QueryParameterConfigurationEditButton } from '@uni/components/src/query-configuration';
import { ProFormInstance } from '@ant-design/pro-components';
import {
  argDefToFormItemTransformer,
  formValuesPostProcessor,
} from '@uni/components/src/query-configuration/processor';
import clsx from 'clsx';
import dayjs from 'dayjs';
import _ from 'lodash';
import './index.less';
import { handleMrActionApi } from '@/utils/widgets';
import { IReducer, ITableState } from '@uni/reducers/src/interface';

const DmrSignOut = () => {
  const {
    globalState: { dictData, userInfo },
  } = useModel('@@qiankunStateFromMaster');

  // 引用
  const proFormRef = useRef<ProFormInstance>();
  const barCodeRef = useRef<InputRef>(null);

  // 节流控制
  const hiddenLoading = useRef(false);
  const newestBarCode = useRef(undefined);

  // timeline请求
  const [timelineItems, { setParams }] = useTimelineReq();
  const [revertRecord, setRevertRecord] = useSafeState(null);

  // 表单配置
  const [customFormItems, setCustomFormItems] = useSafeState([]);
  const defaultFormItems = DmrSignOutFormItems(
    dictData?.Mr?.Employee ?? dictData?.Employee ?? [],
    barCodeRef,
  );

  // 表格数据
  const [tableState, SearchTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);
  const [tableColumns, setTableColumns] = useSafeState<any[]>([]);
  const [clickedItem, setClickedItem] = useSafeState<DmrSignOutRecord | null>(
    null,
  );

  // 弹窗状态
  const [modalVisible, setModalVisible] = useSafeState(false);
  const [modalData, setModalData] = useSafeState<DmrSignOutRecord[]>([]);
  const [modalAlert, setModalAlert] = useSafeState(false);
  const [selectedKeys, setSelectedKeys] = useSafeState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useSafeState<DmrSignOutRecord[]>([]);
  const [modalSpecialData, setModalSpecialData] = useSafeState<any>(undefined);
  const [modalColumns, setModalColumns] = useSafeState<any[]>([]);

  // 获取表格列定义
  const { run: getTableColumns, mutate: setColumnsData } = useRequest(
    () =>
      uniCommonService('Api/Mr/TraceRecord/DmrSignedOutList', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.Columns;
        }
        return [];
      },
      onSuccess: (columnsData) => {
        setTableColumns(
          tableColumnBaseProcessor(DmrSignOutColumns, columnsData),
        );
        setModalColumns(
          tableColumnBaseProcessor(DmrSignOutModalColumns, columnsData),
        );
      },
    },
  );

  // 病案出库请求
  const { loading: signOutLoading, run: signOut } = useRequest(
    (data) =>
      uniCommonService('Api/Mr/Tracing/DmrSignOut', {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => res,
      onSuccess: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 处理API响应结果
          const result = handleMrActionApi(res.data);

          if (result?.isCorrect) {
            message.success('病案出库成功');

            // 将数据添加到表格
            if (result?.data?.length > 0) {
              console.log('病案出库成功', result?.data, tableState.data);
              // 使用 dataUnshiftUniq 进行添加，确保唯一性
              SearchTableDispatch({
                type: TableAction.dataUnshiftUniq,
                payload: {
                  data: {
                    ...(Array.isArray(result.data)
                      ? result.data[0]
                      : result.data),
                    InsertTime: dayjs(),
                  },
                  key: 'BarCode',
                },
              });
            }

            // 关闭弹窗
            setModalVisible(false);
            setSelectedKeys([]);
            setSelectedRows([]);
            setModalAlert(false);

            // 重置表单中的条码字段
            proFormRef.current?.resetFields(['BarCode']);
            // 聚焦回条码输入框
            focusBarCode();
          } else {
            message.error(result?.errMsg?.join('。/n') || '出库失败');
          }
        } else {
          message.error(res?.message || '出库操作失败');
        }

        // 重置节流标识
        hiddenLoading.current = false;
      },
      onError: () => {
        hiddenLoading.current = false;
        message.error('出库请求失败，请重试');
      },
    },
  );

  // 撤销出库
  const { loading: revertSignOutLoading, run: revertSignOut } = useRequest(
    (barCodes) =>
      uniCommonService('Api/Mr/Tracing/DmrRevertSignOut', {
        method: 'POST',
        data: { BarCodes: barCodes },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => res,
      onSuccess: (res, params) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          message.success('撤销出库成功');

          // 从表格中移除撤销的记录
          const barCode = Array.isArray(params[0]) ? params[0][0] : params[0];
          // 使用 dataFilt 过滤掉撤销的记录
          SearchTableDispatch({
            type: TableAction.dataFilt,
            payload: {
              key: 'BarCode',
              value: barCode,
            },
          });

          // 设置撤销记录，用于重置时间轴
          const revertedRecord = tableState.data.find(
            (item) => item.BarCode === barCode,
          );
          if (revertedRecord) {
            setRevertRecord(revertedRecord);
          }
        } else {
          message.error(res?.message || '撤销出库失败');
        }
      },
    },
  );

  // 查询病案
  const { loading: getTraceRecordLoading, run: getTraceRecord } = useRequest(
    (data) =>
      uniCommonService('Api/Mr/TraceRecord/GetListByBarCode', {
        method: 'POST',
        data: {
          BarCode: data?.BarCode,
          SkipCount: 0,
          MaxResultCount: 999999,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => res,
      onSuccess: (res, params) => {
        // 处理查询结果
        handleSearchResult(params[0], res);
      },
      onError: () => {
        hiddenLoading.current = false;
        message.error('查询病案失败，请重试');
      },
    },
  );

  // 按条件查询病案
  const { loading: searchRecordsLoading, run: searchRecords } = useRequest(
    (data) =>
      uniCommonService('Api/Mr/TraceRecord/GetList', {
        method: 'POST',
        data: {
          ...data,
          SkipCount: 0,
          MaxResultCount: 999999,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => res,
      onSuccess: (res, params) => {
        // 处理查询结果
        const formValues = proFormRef.current?.getFieldsValue();
        handleSearchResult(formValues, res);
      },
      onError: () => {
        hiddenLoading.current = false;
        message.error('查询病案失败，请重试');
      },
    },
  );

  // 处理API操作结果
  const handleActionResult = (data) => {
    if (!data) return { isCorrect: false, errMsg: ['无效的响应数据'] };

    if (data.StatusCode?.toString() === '200') {
      return {
        isCorrect: true,
        data: data.Data,
      };
    } else {
      return {
        isCorrect: false,
        errMsg: Array.isArray(data.Message) ? data.Message : [data.Message],
        errType: `${data.StatusCode}`,
      };
    }
  };

  // 处理查询结果
  const handleSearchResult = (params, res) => {
    console.log('handleSearchResult', params, res);
    if (!res || res.code !== 0) {
      hiddenLoading.current = false;
      message.error(res?.message || '查询失败');
      return;
    }

    let resData;
    if (res?.data?.Items) {
      // Api/Mr/TraceRecord/GetList
      resData = res.data.Items;
    } else {
      // Api/Mr/TraceRecord/GetListByBarCode
      resData = res.data;
    }

    if (!resData || resData.length === 0) {
      // 没查到数据
      hiddenLoading.current = false;
      Modal.confirm({
        title: '查无数据',
        content: '请确认病案标识填写正确',
        onOk: () => {
          proFormRef.current?.resetFields(['BarCode']);
          focusBarCode();
        },
        cancelButtonProps: { style: { display: 'none' } },
      });
      return;
    }

    if (resData.length === 1) {
      // 单个，直接处理
      signOut({
        ...params,
        BarCode: resData?.[0].BarCode,
      });
    } else {
      // 多条，弹窗选择
      setModalData(resData);
      setModalSpecialData(params);
      setModalVisible(true);
    }
  };

  // 特殊限流逻辑
  const handleBarCodeDebounce = (barCode) => {
    // 检查是否已经在表格中存在且是近期添加的
    const existingRecord = tableState.data.find((d) => d.BarCode === barCode);

    if (existingRecord) {
      // 如果2秒内已经添加过，则拦截请求
      if (dayjs().diff(existingRecord.InsertTime) < 2000) {
        hiddenLoading.current = false;
        proFormRef?.current?.setFieldValue('BarCode', '');
        return true;
      }
    }

    newestBarCode.current = barCode;
    return false;
  };

  // 重置时间轴
  useEffect(() => {
    if (clickedItem?.BarCode === revertRecord?.BarCode) {
      setClickedItem(null);
      setParams(null);
    }
  }, [revertRecord]);

  // 初始化加载
  useEffect(() => {
    getTableColumns();
  }, []);

  // 提交表单进行出库
  const handleSubmit = () => {
    if (hiddenLoading.current) return;
    hiddenLoading.current = true;

    proFormRef.current
      ?.validateFields()
      .then((values) => {
        // 处理表单值
        const formData: any = formValuesPostProcessor(customFormItems, values);

        // 根据签入类型决定查询方式
        if (formData?.SignType?.value === 'BarCode') {
          // 先做限流检查
          if (handleBarCodeDebounce(formData.BarCode)) {
            return;
          }
          // 按条码精确查询
          getTraceRecord(formData);
        } else {
          // 对于非BarCode类型，创建请求参数对象
          const searchParam = {
            // ..._.omit(values, 'BarCode'),
            [values.SignType.value]: values.BarCode,
          };
          searchRecords(searchParam);
        }
      })
      .catch(() => {
        hiddenLoading.current = false;
        message.error('请确认已填写所有必填信息');
      });
  };

  // 回车提交表单
  useKeyPress(
    'enter',
    () => {
      if (proFormRef.current?.getFieldValue('BarCode')) {
        handleSubmit();
      }
    },
    {
      exactMatch: true,
      target: document.getElementById('dmrSignOutForm'),
    },
  );

  // 聚焦条码输入框
  const focusBarCode = () => {
    setTimeout(() => {
      barCodeRef.current?.focus({ cursor: 'end' });
    }, 20);
  };

  // 表格列处理，添加操作列
  const processedTableColumns = useMemo(() => {
    if (!tableColumns.length) return [];

    const operationColumn = {
      dataIndex: 'option',
      title: '',
      align: 'center',
      width: 60,
      fixed: 'right',
      visible: true,
      render: (_, record) => (
        <Space>
          {/* <Tooltip title="打印">
            <PrinterOutlined
              className="icon_blue-color"
              onClick={(e) => {
                e.stopPropagation();
                // 通过PdfPrint组件打印单个病案
                const printButton = document.getElementById(
                  `print-${record.BarCode}`,
                );
                if (printButton) {
                  printButton.click();
                }
              }}
            />
          </Tooltip> */}
          <Popconfirm
            key="revert"
            title="确定要撤销？"
            onConfirm={(e) => {
              e?.stopPropagation?.();
              revertSignOut([record.BarCode]);
            }}
            onCancel={(e) => e?.stopPropagation?.()}
          >
            <Tooltip title="撤销出库">
              <UndoOutlined
                className="icon_blue-color"
                onClick={(e) => e?.stopPropagation?.()}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    };

    return [...tableColumns, operationColumn];
  }, [tableColumns]);

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col xxl={7} xl={8}>
          <Card
            title={<Space>出库信息</Space>}
            style={{ marginBottom: '15px' }}
            extra={
              <QueryParameterConfigurationEditButton
                type={'LEFT_CONTAINER'}
                currentSearchOptsLength={customFormItems?.length ?? 0}
                setSearchOpts={setCustomFormItems}
                formItems={
                  isEmptyValues(customFormItems)
                    ? defaultFormItems
                    : customFormItems
                }
                dictData={dictData}
                queryInterfaceUrl={'Api/Mr/Tracing/DmrSignOut'}
                onFormItemsSaveSuccess={(argDefs) => {
                  let transformed = argDefToFormItemTransformer(
                    argDefs,
                    dictData,
                  )
                    .concat(
                      (defaultFormItems ?? []).filter(
                        (item) => item?.custom === true,
                      ) as any[],
                    )
                    .sort(
                      (a: any, b: any) =>
                        (a?.ColumnSort ?? 999) - (b?.ColumnSort ?? 999),
                    );

                  setCustomFormItems(
                    transformed?.filter((item) => item?.visible === true),
                  );
                }}
              />
            }
          >
            <ProFormContainer
              spinLoading={
                getTraceRecordLoading || searchRecordsLoading || signOutLoading
              }
              autoFocusFirstInput={false}
              className="dmr-sign-out-form"
              id="dmrSignOutForm"
              formRef={proFormRef}
              grid
              labelCol={{ flex: '100px' }}
              wrapperCol={{ flex: 'auto' }}
              searchOpts={
                isEmptyValues(customFormItems)
                  ? defaultFormItems
                  : customFormItems
              }
              submitter={{
                render: () => [
                  <Button
                    type="primary"
                    style={{
                      width: 'calc(100% - 100px)',
                      float: 'right',
                      marginTop: '8px',
                    }}
                    key="submit"
                    onClick={handleSubmit}
                  >
                    出库(Enter)
                  </Button>,
                ],
              }}
            />
          </Card>
          <PatTimeline
            item={clickedItem}
            loading={false}
            timelineItems={timelineItems}
          />
        </Col>
        <Col xxl={17} xl={16}>
          <Card
            title={
              <Space>
                <span>病案出库列表</span>
                <Tag>总份数：{tableState.data?.length ?? 0}</Tag>
              </Space>
            }
            extra={
              <Space>
                <PdfPrint
                  apiUrl="Api/Mr/TraceRecord/PrintDmrSignedOutList"
                  tooltipTitle="打印出库清单"
                  buttonType="default"
                  buttonSize="middle"
                  paramType="data"
                  disabled={
                    tableState.data.filter((d) => d.isCorrect).length === 0
                  }
                  params={() => {
                    if (
                      tableState.data.filter((d) => d.isCorrect).length === 0
                    ) {
                      message.error('没有可打印的出库记录');
                      return false;
                    }
                    return {
                      BarCodes: tableState.data
                        .filter((d) => d.isCorrect)
                        .map((d) => d.BarCode),
                    };
                  }}
                >
                  打印清单
                </PdfPrint>
                <Divider type="vertical" />
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl: 'Api/Mr/TraceRecord/DmrSignedOutList',
                    onTableRowSaveSuccess: (columns) => {
                      setTableColumns(
                        tableColumnBaseProcessor(DmrSignOutColumns, columns),
                      );
                    },
                  }}
                />
              </Space>
            }
          >
            <UniTable
              id="dmr_sign_out_records"
              rowKey="uuid"
              showSorterTooltip={false}
              loading={
                getTraceRecordLoading ||
                searchRecordsLoading ||
                signOutLoading ||
                revertSignOutLoading
              }
              columns={processedTableColumns}
              dataSource={tableState.data}
              dictionaryData={dictData}
              scroll={{ x: 'max-content' }}
              rowClassName={(record, index) => {
                let classname = [];
                // 互斥
                if (!record?.isCorrect) {
                  classname.push('row-error');
                } else if (index === 0) {
                  return 'row-first';
                }
                if (record?.uuid === clickedItem?.uuid) {
                  classname.push('row-selected');
                }

                return classname.length > 0 ? clsx(classname) : null;
              }}
              pagination={{
                total: tableState.data?.length ?? 0,
              }}
              onRow={(record) => {
                return {
                  onClick: () => {
                    if (clickedItem?.BarCode !== record?.BarCode) {
                      setClickedItem(record);
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 多条记录选择弹窗 */}
      <Modal
        title="确认病案"
        open={modalVisible}
        width={900}
        onOk={() => {
          // 检查是否选择了记录
          if (selectedKeys.length === 0) {
            setModalAlert(true);
            return;
          }

          // 执行出库操作
          signOut({
            ...modalSpecialData,
            BarCode: selectedKeys[0],
          });
        }}
        okButtonProps={{
          loading: signOutLoading,
        }}
        onCancel={() => {
          // 重置节流标识
          hiddenLoading.current = false;
          focusBarCode();

          // 重置弹窗状态
          setModalVisible(false);
          setSelectedKeys([]);
          setSelectedRows([]);
          setModalAlert(false);
        }}
      >
        <UniTable
          id="multi_record_check"
          rowKey="BarCode"
          showSorterTooltip={false}
          loading={signOutLoading}
          columns={modalColumns}
          dataSource={modalData}
          scroll={{ x: 'max-content' }}
          tableAlertRender={() => {
            return modalAlert ? (
              <Alert
                message="请选择一个病案"
                description="请选择一个病案再点击确认，如果没有查询到目标病案请确认信息输入正确"
                type="error"
                closable
                onClose={() => {
                  setModalAlert(false);
                }}
              />
            ) : null;
          }}
          tableAlertOptionRender={false}
          rowSelection={{
            alwaysShowAlert: true,
            type: 'radio',
            selectedRowKeys: selectedKeys,
            onChange: (selectedRowKeys, selectedItems) => {
              setSelectedKeys(selectedRowKeys);
              setSelectedRows(selectedItems);
              setModalAlert(false);
            },
            getCheckboxProps: (record) => ({
              disabled: record?.IsSignedIn,
            }),
          }}
          rowClassName={(record) => {
            if (record?.IsSignedIn) {
              return 'row-first';
            }
          }}
          onRow={(record) => {
            return {
              onClick: () => {
                setSelectedKeys([record.BarCode]);
                setSelectedRows([record]);
              },
            };
          }}
        />
      </Modal>
    </>
  );
};

export default DmrSignOut;
