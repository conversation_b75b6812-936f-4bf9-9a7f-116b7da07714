import React, { useEffect, useState } from 'react';
import { Button, List, Avatar, Modal } from 'antd';
import _ from 'lodash';

import './index.less';
import IconBtn from '@uni/components/src/iconBtn/index';
import { UniTable } from '@uni/components/src/index';

interface SingleListProps {
  data?: [];
  length?: number;
  loading?: boolean;
  columns?: any[];
  listExtraAction?: (record) => void;
}

const SingleList = (props: SingleListProps) => {
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState([]);
  console.log(record);
  return (
    <>
      <div className="drg-single-list-card">
        <List
          className="hqms-list"
          loading={props?.loading ?? false}
          itemLayout="horizontal"
          dataSource={
            props?.length
              ? props.data.filter((d, i) => i < props?.length)
              : props.data
          }
          renderItem={(item: any) => {
            let title;
            switch (item.key) {
              case 1:
                title = (
                  <>
                    <Avatar
                      size={'small'}
                      style={{
                        marginRight: '1rem',
                        backgroundColor: '#eb5757',
                        color: '#fff',
                      }}
                    >
                      {item?.key || ''}
                    </Avatar>
                    <span>{item.name}</span>
                  </>
                );
                break;
              case 2:
                title = (
                  <>
                    <Avatar
                      size={'small'}
                      style={{
                        marginRight: '1rem',
                        backgroundColor: '#f4a741',
                        color: '#fff',
                      }}
                    >
                      {item.key || ''}
                    </Avatar>
                    <span>{item.name}</span>
                  </>
                );
                break;
              case 3:
                title = (
                  <>
                    <Avatar
                      size={'small'}
                      style={{
                        marginRight: '1rem',
                        backgroundColor: '#1464f8',
                        color: '#fff',
                      }}
                    >
                      {item?.key || ''}
                    </Avatar>
                    <span>{item.name}</span>
                  </>
                );
                break;
              default:
                title = (
                  <>
                    <Avatar size={'small'} style={{ marginRight: '1rem' }}>
                      {item?.key || ''}
                    </Avatar>
                    <span>{item.name}</span>
                  </>
                );
            }
            return (
              <List.Item>
                <List.Item.Meta title={<>{title}</>} />
                <div>{item.value}</div>
                {/* <IconBtn
                  type="details"
                  style={{ margin: '0 10px' }}
                  onClick={(e) => {
                    setVisible(true);
                    setRecord(item);
                  }}
                /> */}
                {props?.listExtraAction && (
                  <IconBtn
                    type="details"
                    style={{ margin: '0 10px' }}
                    onClick={(e) => {
                      props?.listExtraAction && props?.listExtraAction(item);
                    }}
                  />
                )}
              </List.Item>
            );
          }}
        />
      </div>
      <Modal
        title="详情"
        open={visible}
        onCancel={() => {
          setVisible(false);
          setRecord(undefined);
        }}
        width={800}
        okButtonProps={{ style: { display: 'none' } }}
        footer={null}
      >
        <UniTable
          rowKey="key"
          id="single-detail-table"
          columns={props?.columns}
          dataSource={record ? [record] : []}
          scroll={{ x: 'max-content' }}
        />
      </Modal>
    </>
  );
};

export default SingleList;
