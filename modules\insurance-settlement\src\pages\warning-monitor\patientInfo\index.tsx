import {
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import {
  Card,
  Col,
  Row,
  Divider,
  Descriptions,
  TableProps,
  Typography,
  Tooltip,
  Spin,
  Space,
  Form,
} from 'antd';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import { useModel } from '@@/plugin-model/useModel';
import {
  IReducer,
  IModalState,
  ITableState,
  IEditableState,
} from '@uni/reducers/src/interface';
import {
  InitTableState,
  InitModalState,
  InitEditableState,
  TableAction,
  modalReducer,
  tableReducer,
  tableEditPropsReducer,
  EditableTableAction,
} from '@uni/reducers/src';
import { useLocation, useRequest, Location } from 'umi';
import './index.less';
import {
  ExportIconBtn,
  UniDragEditOnlyTable,
  UniDragEditTable,
  UniTable,
} from '@uni/components/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { BaseInfoDescriptions, EventConstants } from './constant';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { DiagColumns, OperColumns } from './columns';
import DrgGroupContainer from './drgGroup';
import DrgFeeAnal from './drgFeeAnal';
import DrgFeeAnalContainer from './drgFeeAnal';
import { useUpdateEffect } from 'ahooks';
import FeeTypes from '../components/emrCardInfo/components/feeTypes/index';
import { FeeTypesColumns } from '../components/emrCardInfo/columns';
import { mock } from './mock';

const diag_table = 'diag_table';

const oper_table = 'oper_table';

const PatientChsDetail = ({ hisId }) => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster');

  const { query }: Location = useLocation();

  const [id, setId] = useState(undefined);

  const [baseInfo, setBaseInfo] = useState(undefined);
  const [icdeDscgs, setIcdeDscgs] = useState([]);
  const [opers, setOpers] = useState([]);
  const [originalTotalInfo, setOriginalTotalInfo] = useState(undefined); // 原始数据 先记着

  const [totalInfo, setTotalInfo] = useState(undefined); // 同时要传给入组container 必须保持最新

  // 从 测试预入组 patientDiagnosisTestReq 传过来的值
  const [drgGroupAdjust, setDrgGroupAdjust] = useState(undefined);

  // 诊断 手术
  const [diagForm] = Form.useForm();
  const [operForm] = Form.useForm();

  // 标签className
  const [tagImgClassName, setTagImgClassName] = useState('');

  // 获取患者基本信息
  const { loading: infoReqLoading, run: infoReq } = useRequest(
    (id) => {
      return uniCommonService(`Api/Emr/EmrCardBundle/Get`, {
        method: 'POST',
        data: {
          HisId: id, // id ?? ,
        },
      });
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          setDrgGroupAdjust(undefined); // 清空
          setBaseInfo({
            ...res?.data?.CardFlat,
            MedTeam:
              res?.data?.CardFlat?.MedTeam ??
              res?.data?.CardFlat?.OutMedTeam ??
              res?.data?.CardFlat?.InMedTeam,
            BedNo:
              res?.data?.CardFlat?.BedNo ??
              res?.data?.CardFlat?.OutBedNo ??
              res?.data?.CardFlat?.InBedNo,
            CliDept:
              res?.data?.CardFlat?.CliDept ??
              res?.data?.CardFlat?.OutDept ??
              res?.data?.CardFlat?.InDept,
            Ward:
              res?.data?.CardFlat?.Ward ??
              res?.data?.CardFlat?.OutWard ??
              res?.data?.CardFlat?.InWard,
            InsurType:
              res?.data?.CardFlat?.InsurType ??
              (location.pathname.includes('dip')
                ? res?.data?.MetricDipSettles?.at(0)?.InsurType
                : res?.data?.MetricDrgSettles?.at(0)?.InsurType),
            Fee:
              res?.data?.CardFlat?.Fee ||
              res?.data?.HisChargeTypeAmts?.find(
                (d) => d?.ChargeTypeMode === 'Total',
              )?.TotalFee,
          });
          // 这边再多做一个处理，确保其中的Id都为唯一值
          setIcdeDscgs([
            ..._.sortBy(
              res?.data?.IcdeResult?.IcdeDscgs?.map((d) => ({
                ...d,
                Id: d?.IcdeCode + uuidv4(),
              })),
              'IcdeSort',
            ),
            {
              Id: 'ADD',
            },
          ]);
          setOpers([
            ..._.sortBy(
              res?.data?.Opers?.map((d) => ({
                ...d,
                Id: d?.OperCode + uuidv4(),
              })),
              'OperSort',
            ),
            {
              Id: 'ADD',
            },
          ]);
          setOriginalTotalInfo(res?.data);
          // setTotalInfo(res?.data);
          return res?.data;
        }
        return undefined;
      },
    },
  );

  console.log('baseInfo', hisId);

  useEffect(() => {
    if (query?.id) {
      setId(query?.id);
    } else if (hisId) {
      setId(hisId);
    }
  }, [query, hisId]);

  // fetch & 还原
  useEffect(() => {
    if (id) {
      setTotalInfo(undefined);
      infoReq(id);
    }

    Emitter.on(EventConstants.RESET_DRGS_RESULT, () => {
      setTotalInfo(undefined);
      infoReq(id);
    });

    return () => {
      Emitter.off(EventConstants.RESET_DRGS_RESULT);
    };
  }, [id]);

  // emitter: icde
  useEffect(() => {
    Emitter.on(
      EventConstants.ICDE_TABLE_LIKE_ROW_CLICK,
      ({ rowRecord, record, index }) => {
        console.log(rowRecord);
        if (record?.Id === 'ADD') {
          let temp = [
            ...icdeDscgs?.filter((d) => d?.Id !== 'ADD'),
            {
              Id: uuidv4(),
              IcdeSort: icdeDscgs?.length,
              InsurName: rowRecord?.InsurName,
              InsurCode: rowRecord?.InsurCode,
              IcdeCode: rowRecord?.Code,
              IcdeName: rowRecord?.Name,
            },
          ];
          setIcdeDscgs([..._.cloneDeep(temp), { Id: 'ADD' }]);
        } else {
          let temp = [
            ...icdeDscgs
              ?.filter((d) => d?.Id !== 'ADD')
              ?.map((item, idx) => {
                return idx === index
                  ? {
                      // ...item,
                      Id: uuidv4(),
                      IcdeSort: item?.IcdeSort,
                      InsurCode: rowRecord?.InsurCode,
                      InsurName: rowRecord?.InsurName,
                      IcdeCode: rowRecord?.Code,
                      IcdeName: rowRecord?.Name,
                    }
                  : item;
              }),
          ];
          setIcdeDscgs([..._.cloneDeep(temp), { Id: 'ADD' }]);
        }
      },
    );

    Emitter.on(EventConstants.ICDE_TABLE_LIKE_CLEAR, ({ record, index }) => {
      let temp = icdeDscgs?.slice();
      temp.splice(index, 1);
      setIcdeDscgs(
        temp?.map((d, i) => ({
          ...d,
          IcdeSort: i,
          IsMain: i === 0 ? true : false,
        })),
      );
    });

    return () => {
      Emitter.off(EventConstants.ICDE_TABLE_LIKE_ROW_CLICK);
      Emitter.off(EventConstants.ICDE_TABLE_LIKE_CLEAR);
    };
  }, [icdeDscgs]);

  // emitter: oper
  useEffect(() => {
    Emitter.on(
      EventConstants.OPER_TABLE_LIKE_ROW_CLICK,
      ({ rowRecord, record, index }) => {
        if (record?.Id === 'ADD') {
          let temp = [
            ...opers?.filter((d) => d?.Id !== 'ADD'),
            {
              Id: uuidv4(),
              OperSort: opers?.length,
              InsurName: rowRecord?.InsurName,
              InsurCode: rowRecord?.InsurCode,
              OperCode: rowRecord?.Code,
              OperName: rowRecord?.Name,
            },
          ];
          setOpers([..._.cloneDeep(temp), { Id: 'ADD' }]);
        } else {
          let temp = [
            ...opers
              ?.filter((d) => d?.Id !== 'ADD')
              ?.map((item, idx) => {
                return idx === index
                  ? {
                      OperSort: item?.OperSort,
                      Id: uuidv4(),
                      InsurCode: rowRecord?.InsurCode,
                      InsurName: rowRecord?.InsurName,
                      OperCode: rowRecord?.Code,
                      OperName: rowRecord?.Name,
                    }
                  : item;
              }),
          ];
          setOpers([..._.cloneDeep(temp), { Id: 'ADD' }]);
        }
      },
    );

    Emitter.on(EventConstants.OPER_TABLE_LIKE_CLEAR, ({ record, index }) => {
      let temp = opers?.slice();
      temp.splice(index, 1);
      setOpers(
        temp?.map((d, i) => ({
          ...d,
          OperSort: i,
          IsMain: i === 0 ? true : false,
        })),
      );
    });

    return () => {
      Emitter.off(EventConstants.OPER_TABLE_LIKE_ROW_CLICK);
      Emitter.off(EventConstants.OPER_TABLE_LIKE_CLEAR);
    };
  }, [opers]);

  // 赋值给totalInfo
  useUpdateEffect(() => {
    if (originalTotalInfo && icdeDscgs?.length && opers?.length) {
      setTotalInfo({
        ...originalTotalInfo,
        Opers: opers
          ?.filter((d) => d.Id !== 'ADD')
          ?.map((d) => ({
            ...d,
            Id: _.isNumber(d?.Id) ? d?.Id : undefined,
            InsurIsReported: true,
          })),
        IcdeResult: {
          ...originalTotalInfo?.IcdeResult,
          IcdeDscgs: icdeDscgs
            ?.filter((d) => d.Id !== 'ADD')
            ?.map((d) => ({
              ...d,
              Id: _.isNumber(d?.Id) ? d?.Id : undefined,
              InsurIsReported: true,
            })),
        },
      });
    }
  }, [icdeDscgs, opers]);

  // 打标签
  useEffect(() => {
    Emitter.on(EventConstants.DRGS_RESULT_TO_CARDINFO, (result) => {
      if (result?.ChsMetricsResult?.ChsDrgsResult?.at(0)?.DrgCode === '0000') {
        setTagImgClassName('base_info_0000_bg');
      } else {
        setTagImgClassName(
          totalInfo?.MetricDipSettles?.at(0)?.Applicable
            ? `base_info_is_${
                location.pathname.includes('dip') ? 'dip' : 'drg'
              }_bg`
            : `base_info_not_${
                location.pathname.includes('dip') ? 'dip' : 'drg'
              }_bg`,
        );
      }
    });

    return () => {
      Emitter.off(EventConstants.DRGS_RESULT_TO_CARDINFO);
    };
  }, [totalInfo]);

  const getDictionaryData = (infoDescription, baseInfo) => {
    // 遍历优先级字典模块和组
    for (const { module, group } of infoDescription?.priorityDict || []) {
      const dict = dictData?.[group]?.[module] || dictData?.[module];
      const matchedItem = dict?.find(
        (v) =>
          v.Code === baseInfo?.[infoDescription?.key] ||
          v?.Name === baseInfo?.[infoDescription?.key],
      );
      return matchedItem?.Name ?? baseInfo?.[infoDescription?.key];
    }

    // 如果没有匹配的字典模块和组，返回默认值
    return valueNullOrUndefinedReturnDash(
      baseInfo?.[infoDescription?.key],
      infoDescription?.dataType,
    );
  };

  return (
    <Row gutter={[8, 8]}>
      <Col span={24}>
        <Card className="base_info_container">
          <Spin spinning={infoReqLoading}>
            <Descriptions
              column={{ xxl: 4, xl: 4, lg: 4, md: 3, sm: 3, xs: 2 }}
            >
              {BaseInfoDescriptions?.map((d) => {
                return (
                  <Descriptions.Item key={d.key} label={d.label}>
                    {getDictionaryData(d, baseInfo)}
                  </Descriptions.Item>
                );
              })}
            </Descriptions>
            <div className={tagImgClassName}></div>
          </Spin>
        </Card>
      </Col>
      <Col span={12}>
        <UniDragEditOnlyTable
          form={diagForm}
          key={diag_table}
          id={diag_table}
          tableId={diag_table}
          scroll={{ y: 200 }}
          pagination={false}
          rowKey="Id"
          columns={DiagColumns}
          dataSource={icdeDscgs}
          onTableDataSourceOrderChange={(tableData, oldIndex, newIndex) => {
            setIcdeDscgs(
              tableData?.map((d, i) => ({
                ...d,
                IcdeSort: i,
                IsMain: i === 0 ? true : false,
              })),
            );
          }}
        />
      </Col>
      <Col span={12}>
        <UniDragEditOnlyTable
          form={operForm}
          key={oper_table}
          id={oper_table}
          tableId={oper_table}
          scroll={{ y: 200 }}
          pagination={false}
          rowKey="Id"
          columns={OperColumns}
          dataSource={opers}
          onTableDataSourceOrderChange={(tableData, oldIndex, newIndex) => {
            console.log('tableData', tableData);
            setOpers(
              tableData?.map((d, i) => ({
                ...d,
                OperSort: i,
              })),
            );
          }}
        />
      </Col>
      <DrgGroupContainer checkReqData={totalInfo} />

      {/* <DrgFeeAnalContainer userInfo={totalInfo} /> */}

      <Card bodyStyle={{ padding: '12px 16px' }}>
        <FeeTypes
          hisId={totalInfo?.HisId}
          metricSettlesData={
            location.pathname.includes('dip')
              ? totalInfo?.MetricDipSettles?.length > 0
                ? totalInfo.MetricDipSettles[0]
                : {}
              : totalInfo?.MetricDrgSettles?.length > 0
              ? totalInfo.MetricDrgSettles[0]
              : {}
          }
          loading={infoReqLoading}
          data={_.orderBy(
            totalInfo?.HisChargeTypeAmts ?? [],
            [(d) => d?.TotalFee || ''],
            'desc',
          )}
          columns={FeeTypesColumns}
          // 可以额外传入 不调用接口
          hisChargeDetailStats={
            totalInfo?.HisChargeDetailStats?.length > 0
              ? totalInfo?.HisChargeDetailStats
              : undefined
          }
        />
      </Card>
    </Row>
  );
};

export default PatientChsDetail;
