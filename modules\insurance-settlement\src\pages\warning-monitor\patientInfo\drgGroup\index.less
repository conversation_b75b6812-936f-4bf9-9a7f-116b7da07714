@import '~@uni/commons/src/style/variables.less';

.drg_card {
  .ant-card-body {
    padding: 10px;
    // box-shadow: 0 0 6px #dedede;

    .drg_card_container {
      .drg_card_title {
        display: flex;
        justify-content: space-between;
      }

      .drg_name {
        font-weight: 700;
        font-size: 15px;
        height: 48px;
        margin-bottom: 0.5em;
      }

      .drg_type {
        .ADrgTypeName {
          color: rgb(0, 182, 180);
          background-color: rgb(224, 248, 248);
          display: block;
          width: max-content;
          padding: 3px 6px;
          border-radius: 5px;
          margin-bottom: 5px;
        }
      }

      .drg_icon_fee_container {
        display: flex;
        justify-content: space-between;
      }

      .drg_fee_container {
        div {
          display: flex;
          justify-content: space-between;
        }
      }

      .drg_extra {
        background-color: #f5f7fa;
        padding: 10px;
        display: -webkit-flex;
        display: flex;
        word-break: break-all;
      }
    }

    .fee_group_container {
      background-color: #d2e4e4;
      margin-top: 10px;
      padding: 10px 15px;
      .patient_fee_now_tooltip {
        font-size: 12px;
        position: relative;
        height: 18px;
        &.fee_now_tooltip_Red {
          color: @red-color;
        }
        &.fee_now_tooltip_Yellow {
          color: #ffc300;
        }
        &.fee_now_tooltip_Orange {
          color: #fa8c16;
        }
        > p {
          margin: 0;
          position: absolute;
          right: 0;
          width: max-content;
        }
      }

      .drg_fee_group {
        display: flex;
        position: relative;

        .fee_group_Yellow {
          width: 15%;
          background-color: #ffc300;
          height: 1em;
        }
        .fee_group_Green {
          width: 70%;
          background-color: @green-color;
          height: 1em;
          position: relative;

          .fee_group_total_fee {
            background-color: @green-color;
            width: 1em;
            height: 1em;
            box-sizing: border-box;
            border: 2px solid #fff;
            border-radius: 0.5em;
            position: absolute;
            left: 50%;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
            transform: translate(-50%, 0);
          }
        }
        .fee_group_Red {
          width: 15%;
          background: linear-gradient(
            90deg,
            @red-color 60%,
            rgba(255, 8, 83, 0)
          );
          height: 1em;
        }
        .fee_group_CwValue {
          background-color: @green-color;
          width: 5px;
          height: 100%;
          position: absolute;
          left: 37%;
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
          border-radius: 2.5px;
          top: -2px;
        }
      }

      .drg_fee_header {
        position: relative;
        height: 22px;

        span {
          position: absolute;
          transform: translate(-50%, 0);
          &.low_header {
            left: 15%;
            color: #ffc300;
          }
          &.high_header {
            right: 15%;
            color: @red-color;
            transform: translate(50%, 0);
          }
        }
      }

      .drg_fee_footer {
        position: relative;
        height: 22px;
        span {
          position: absolute;
          transform: translate(-50%, 0);

          &.fee_footer_cwValue {
            color: @green-color;
          }
        }
      }
    }
  }
}

.chs_result_container {
  .ant-collapse-header {
    background-color: #00b6b4;
    color: #fff;
  }
  .ant-collapse-content-box {
    background-color: #e6f3f3;
  }
}

.fee_compute_container {
  font-size: 20px;
  background-color: #d2e4e4;
  padding: 37px 15px;
  .fee_compute {
    color: @blue-color;
    margin-bottom: 30px;
    display: flex;
    flex-wrap: nowrap;
    justify-content: left;
    span {
      display: block;
    }
  }
  .fee_details {
    display: flex;
    flex-wrap: nowrap;
    justify-content: left;
    span {
      display: block;
      width: 10%;
      text-align: right;
    }
  }
}
