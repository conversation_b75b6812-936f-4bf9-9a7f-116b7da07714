import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from 'react';
import { Tabs, Space, Button, Divider, Empty, message, Input } from 'antd';
import { useModel, useRequest } from 'umi';
import { useDebounceFn, useUpdateEffect } from 'ahooks';
import { uniCommonService } from '@uni/services/src/commonService';
import { isRespErr, RespType } from '@/utils/widgets';
import dayjs from 'dayjs';
import { ExportIconBtn, UniTable, UniSelect } from '@uni/components/src';
import { Emitter } from '@uni/utils/src/emitter';
import {
  HierarchyBedAmtItem,
  UpsertBedAmtParams,
  BulkUpdateBedAmtParams,
} from './interface';
import { getBedAmtColumns } from './columns';
import { getDeptHistoryColumns } from './deptHistoryColumns';
import EditModal from './EditModal';
import { HIERARCHY_TYPE, TAB_KEYS, API_PATHS, EVENTS } from './constants';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';
import { PlusOutlined, SearchOutlined, EditOutlined } from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import './index.less';

const HierarchyBedV2: React.FC = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const { searchParams, dictData } = globalState;

  // 状态管理
  const [activeTab, setActiveTab] = useState(TAB_KEYS.BED_LIST);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editRecord, setEditRecord] = useState<HierarchyBedAmtItem | null>(
    null,
  );
  const [tableColumns, setTableColumns] = useState<any[]>([]);
  const [deptHistoryColumns, setDeptHistoryColumns] = useState<any[]>([]);
  // 保存过滤后的科室数组
  const [filteredDepts, setFilteredDepts] = useState<any[]>([]);
  // 获取默认科室
  const defaultDept = useMemo(() => {
    return filteredDepts?.[0]?.Code || dictData?.DynDepts?.[0]?.Code || '';
  }, [filteredDepts, dictData?.DynDepts]);
  // 使用默认科室作为初始值
  const [selectedDept, setSelectedDept] = useState<string>(undefined);
  const actionRef = useRef<any>();
  const deptHistoryActionRef = useRef<any>();

  // 添加搜索相关的状态
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [filteredBedListData, setFilteredBedListData] = useState<any[]>([]);

  // 获取列定义
  const { loading: columnsLoading, run: fetchColumns } = useRequest(
    () =>
      uniCommonService(API_PATHS.CURRENT_LIST, {
        method: 'POST',
        data: {
          HierarchyType: HIERARCHY_TYPE,
        },
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          // 处理列定义
          const processedColumns = tableColumnBaseProcessor(
            getBedAmtColumns,
            res.data?.Columns || [],
          );
          setTableColumns(processedColumns);
          return res.data;
        }
        return null;
      },
      onError: (error) => {
        console.error('获取列定义失败:', error);
      },
    },
  );

  // 获取部门历史列定义
  const { loading: deptHistoryColumnsLoading, run: fetchDeptHistoryColumns } =
    useRequest(
      () =>
        uniCommonService(API_PATHS.DEPT_LIST, {
          method: 'POST',
          data: {
            HierarchyType: HIERARCHY_TYPE,
            DeptCode: 'placeholder', // 提供任意值，只是为了获取列定义
          },
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        }),
      {
        formatResult: (res: RespType<any>) => {
          if (!isRespErr(res)) {
            // 处理列定义 - 使用getDeptHistoryColumns而非getBedAmtColumns
            const processedColumns = tableColumnBaseProcessor(
              getDeptHistoryColumns(), // 这是一个函数，需要调用它
              res.data?.Columns || [],
            );
            setDeptHistoryColumns(processedColumns);
            return res.data;
          }
          return null;
        },
        onError: (error) => {
          console.error('获取部门历史列定义失败:', error);
        },
      },
    );

  // 加载床位数据列表，为每条数据添加uuid
  const {
    data: bedListData,
    loading: bedListLoading,
    run: fetchBedList,
  } = useRequest(
    (HospCode) =>
      uniCommonService(API_PATHS.CURRENT_LIST, {
        method: 'POST',
        data: {
          HierarchyType: HIERARCHY_TYPE,
          HospCode,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          // 为每个数据项添加uuid
          const dataWithUuid = (res.data || []).map((item) => ({
            ...item,
            uuid: uuidv4(),
          }));
          return dataWithUuid;
        }
        return [];
      },
      onError: (error) => {
        console.error('获取床位列表失败:', error);
      },
    },
  );

  // 加载部门历史数据，为每条数据添加uuid
  const {
    data: deptHistoryData,
    loading: deptHistoryLoading,
    run: fetchDeptHistory,
  } = useRequest(
    (deptCode) =>
      uniCommonService(API_PATHS.DEPT_LIST, {
        method: 'POST',
        data: {
          HierarchyType: HIERARCHY_TYPE,
          DeptCode: deptCode,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          // 为每个数据项添加uuid
          const dataWithUuid = (res.data || []).map((item) => ({
            ...item,
            uuid: uuidv4(),
          }));
          return dataWithUuid;
        }
        return [];
      },
      onError: (error) => {
        console.error('获取部门历史数据失败:', error);
      },
    },
  );

  // 新增/编辑床位
  const { loading: upsertLoading, run: upsertBedAmt } = useRequest(
    (params: UpsertBedAmtParams) =>
      uniCommonService(API_PATHS.UPSERT, {
        method: 'POST',
        data: params,
      }),
    {
      manual: true,
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          message.success('操作成功');
          setEditModalVisible(false);

          // 根据当前 tab 决定刷新哪个列表
          if (activeTab === TAB_KEYS.BED_LIST) {
            fetchBedList(searchParams?.hospCodes); // 刷新总床位列表
          } else if (activeTab === TAB_KEYS.DEPT_HISTORY && selectedDept) {
            fetchDeptHistory(selectedDept); // 刷新科室床位历史
          }

          return res;
        } else {
          throw new Error('操作失败');
        }
      },
    },
  );

  // 批量更新床位数
  const { loading: bulkUpdateLoading, run: bulkUpdateBedAmt } = useRequest(
    (params: BulkUpdateBedAmtParams) =>
      uniCommonService(API_PATHS.BULK_UPDATE_BED_AMT, {
        method: 'POST',
        data: params,
      }),
    {
      manual: true,
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          message.success('更新成功');

          // 根据当前 tab 决定刷新哪个列表
          if (activeTab === TAB_KEYS.BED_LIST) {
            fetchBedList(searchParams?.hospCodes); // 刷新总床位列表
          } else if (activeTab === TAB_KEYS.DEPT_HISTORY && selectedDept) {
            fetchDeptHistory(selectedDept); // 刷新科室床位历史
          }

          return res;
        } else {
          throw new Error('更新失败');
        }
      },
    },
  );

  // 处理新增按钮点击
  const handleAdd = () => {
    setEditRecord(null);
    setEditModalVisible(true);
  };

  // 处理EditModal的提交事件，确保返回Promise<void>
  const handleEditSubmit = async (values: UpsertBedAmtParams) => {
    await upsertBedAmt(values);
    // 不返回任何值，以确保返回类型为 Promise<void>
  };

  // 处理科室选择
  const handleDeptChange = (value: string) => {
    console.log('selectedDept', selectedDept);

    setSelectedDept(value);
    if (value) {
      fetchDeptHistory(value);
    }
  };

  // 处理修改床位数按钮点击
  const handleEditBedAmount = () => {
    if (!selectedDept) {
      message.warning('请先选择科室');
      return;
    }

    // 从dictData.DynDepts中找到当前选择的科室信息
    const selectedDeptData = dictData?.DynDepts?.find(
      (dept) => dept.Code === selectedDept,
    );

    if (!selectedDeptData) {
      message.warning('未找到科室信息');
      return;
    }

    // 获取HospCode
    const hospCode = selectedDeptData.ExtraProperties?.HospCode || '';

    // 创建编辑记录对象
    const record: Partial<HierarchyBedAmtItem> = {
      HierarchyCode: selectedDept,
      HierarchyName: selectedDeptData.Name,
      HospCode: hospCode,
      HierarchyType: HIERARCHY_TYPE,
      Id: 0,
      // 其他床位数可以在表单中填写
    };

    // 设置编辑记录并打开弹窗
    setEditRecord(record as HierarchyBedAmtItem);
    setEditModalVisible(true);
  };

  // 搜索函数定义
  const filterBedListData = useCallback(
    (keyword: string) => {
      if (!keyword || !bedListData) {
        setFilteredBedListData(bedListData || []);
        return;
      }

      const filteredData = bedListData.filter((item) => {
        // 搜索科室的code或name
        const hierarchyCode = (item.HierarchyCode || '')
          .toString()
          .toLowerCase();
        const hierarchyName = (item.HierarchyName || '')
          .toString()
          .toLowerCase();
        const lowerKeyword = keyword.toLowerCase();

        // 直接匹配
        const directMatch =
          hierarchyCode.includes(lowerKeyword) ||
          hierarchyName.includes(lowerKeyword);

        // 拼音搜索
        const pinyinMatch = pinyinInitialSearch(hierarchyName, lowerKeyword);

        return directMatch || pinyinMatch;
      });

      setFilteredBedListData(filteredData);
    },
    [bedListData],
  );

  // 使用debounce处理搜索
  const { run: debouncedSearch } = useDebounceFn(
    (value: string) => {
      setSearchKeyword(value);
      filterBedListData(value);
    },
    { wait: 300 },
  );

  // 当bedListData变化时，更新过滤后的数据
  useEffect(() => {
    filterBedListData(searchKeyword);
  }, [bedListData, filterBedListData]);

  // // 设置默认科室并加载初始数据
  // useEffect(() => {
  //   if (defaultDept && !selectedDept && dictData?.DynDepts?.length > 0) {
  //     setSelectedDept(defaultDept);
  //     fetchDeptHistory(defaultDept);
  //   }
  // }, [defaultDept, dictData?.DynDepts]);

  // 过滤科室列表
  const filterDeptsByHospCode = useCallback(
    (hospCodes: string | string[]) => {
      if (!dictData?.DynDepts || !hospCodes) {
        setFilteredDepts(dictData?.DynDepts || []);
        return;
      }

      // 确保hospCodes是数组
      const hospCodesArray = Array.isArray(hospCodes) ? hospCodes : [hospCodes];

      // 根据HospCode过滤科室
      const filtered = dictData.DynDepts.filter((dept) => {
        const deptHospCode = dept.ExtraProperties?.HospCode;
        return deptHospCode && hospCodesArray.includes(deptHospCode);
      });

      setFilteredDepts(filtered.length > 0 ? filtered : dictData.DynDepts);
    },
    [dictData?.DynDepts],
  );

  // 监听
  useEffect(() => {
    if (
      searchParams?.triggerSource === 'btnClick' &&
      activeTab === TAB_KEYS.BED_LIST
    ) {
      console.log('触发搜索 btnClick');
      fetchBedList(searchParams?.hospCodes);
    } else if (
      searchParams?.triggerSource === 'btnClick' &&
      activeTab === TAB_KEYS.DEPT_HISTORY
    ) {
      // 根据选择的医院过滤科室列表
      filterDeptsByHospCode(searchParams?.hospCodes);
      console.log('selectedDept', selectedDept);

      setSelectedDept(filteredDepts[0]?.Code || '');
      if (filteredDepts[0]?.Code) {
        fetchDeptHistory(filteredDepts[0].Code);
      }
    }
  }, [searchParams]);

  // 监听 tab 切换，自动调用相应接口
  useUpdateEffect(() => {
    if (activeTab === TAB_KEYS.BED_LIST) {
      // 切换到总床位数列表时刷新数据
      console.log('触发搜索 tab 切换');
      fetchBedList(searchParams?.hospCodes);
    } else if (activeTab === TAB_KEYS.DEPT_HISTORY) {
      // 切换到科室床位历史时，如果已选择科室，则刷新数据
      // 根据选择的医院过滤科室列表
      // filterDeptsByHospCode(searchParams?.hospCodes);
      //       console.log('selectedDept', selectedDept);
      // setSelectedDept(filteredDepts[0]?.Code || '');
      // if (filteredDepts[0]?.Code) {
      //   fetchDeptHistory(filteredDepts[0].Code);
      // }
    }
  }, [activeTab]);

  // 使用useEffect设置事件监听
  useEffect(() => {
    // 监听编辑按钮点击
    Emitter.on(EVENTS.EDIT_BED, (record: HierarchyBedAmtItem) => {
      setEditRecord(record);
      setEditModalVisible(true);
    });

    // 监听批量修改床位数按钮点击
    Emitter.on(EVENTS.BULK_UPDATE_BED, (record: HierarchyBedAmtItem) => {
      // 如果Id为0或null，不执行操作
      if (record.Id === 0 || record.Id === null) {
        message.warning('选择更新的记录为空，请重新选择');
        return;
      }

      // 处理HospCode和DeptCode，确保它们是有效值的数组
      const hospCode = record.HospCode ? [record.HospCode] : [];
      const deptCode = record.HierarchyCode ? [record.HierarchyCode] : [];

      // 直接构建API参数并调用API
      const params: BulkUpdateBedAmtParams = {
        HospCode: hospCode,
        DeptCode: deptCode,
        Sdate: dayjs(record.Sdate)?.format('YYYY-MM-DD'),
        Edate: dayjs(record.Edate)?.format('YYYY-MM-DD'),
        ApprovedBedsNumber: record.ApprovedBedAmt,
        SuppliedBedsNumber: record.OpenBedAmt,
      };

      bulkUpdateBedAmt(params);
    });

    // 监听查看科室床位数记录按钮点击
    Emitter.on(EVENTS.CHECK_BED_HISTORY, (record: HierarchyBedAmtItem) => {
      // 确保记录中有HierarchyCode
      if (!record?.HierarchyCode) {
        message.warning('科室代码不能为空');
        return;
      }

      // 1. 切换到科室床位历史标签页
      setActiveTab(TAB_KEYS.DEPT_HISTORY);

      // 2. 设置选中的科室为当前记录的科室
      console.log('selectedDept', selectedDept);
      setSelectedDept(record.HierarchyCode);

      // 3. 加载该科室的历史数据
      fetchDeptHistory(record.HierarchyCode);
    });

    // 在组件卸载时移除事件监听
    return () => {
      Emitter.off(EVENTS.EDIT_BED);
      Emitter.off(EVENTS.BULK_UPDATE_BED);
      Emitter.off(EVENTS.CHECK_BED_HISTORY);
    };
  }, []);
  // Tab 1 床位数列表的 extraContent
  const bedListExtraContent = (
    <Space>
      {/* 搜索框代替新增按钮 */}
      <Input.Search
        placeholder="搜索科室编码或名称"
        style={{ width: 220 }}
        onChange={(e) => debouncedSearch(e.target.value)}
        allowClear
      />
      <Divider type="vertical" />
      <ExportIconBtn
        isBackend={true}
        backendObj={{
          url: API_PATHS.EXPORT_CURRENT_LIST,
          method: 'POST',
          data: {
            HierarchyType: HIERARCHY_TYPE,
          },
          fileName: `${dayjs().format('YYYY-MM-DD')}_住院动态床位数`,
        }}
        btnDisabled={!bedListData?.length}
      />
      {/* 添加列编辑按钮 */}
      <TableColumnEditButton
        columnInterfaceUrl={API_PATHS.CURRENT_LIST}
        onTableRowSaveSuccess={(columns) => {
          // 处理列定义
          const processedColumns = tableColumnBaseProcessor(
            getBedAmtColumns, // 这是一个数组，不是函数
            columns,
          );
          setTableColumns(processedColumns);
        }}
      />
    </Space>
  );

  // Tab 2 科室床位历史的 extraContent
  const deptHistoryExtraContent = (
    <Space>
      <UniSelect
        placeholder="请选择科室"
        dataSource={
          filteredDepts.length > 0 ? filteredDepts : dictData?.DynDepts || []
        }
        optionNameKey="Name"
        optionValueKey="Code"
        style={{ width: 180 }}
        value={selectedDept}
        onChange={handleDeptChange}
        showSearch={true}
        allowClear={false}
      />
      <Divider type="vertical" />
      {/* 添加修改床位数按钮 */}
      {/* <Button
        type="default"
        onClick={handleEditBedAmount}
        disabled={!selectedDept}
      >
        <EditOutlined /> 修改床位数
      </Button>
      <Divider type="vertical" /> */}
      <ExportIconBtn
        isBackend={true}
        backendObj={{
          url: API_PATHS.EXPORT_DEPT_LIST,
          method: 'POST',
          data: {
            HierarchyType: HIERARCHY_TYPE,
            DeptCode: selectedDept,
          },
          fileName: `${dayjs().format('YYYY-MM-DD')}_科室床位历史`,
        }}
        btnDisabled={!selectedDept || !deptHistoryData?.length}
      />
      {/* 添加列编辑按钮 */}
      <TableColumnEditButton
        columnInterfaceUrl={API_PATHS.DEPT_LIST}
        onTableRowSaveSuccess={(columns) => {
          // 处理列定义
          const processedColumns = tableColumnBaseProcessor(
            getDeptHistoryColumns(), // 这是一个函数，需要调用它
            columns,
          );
          setDeptHistoryColumns(processedColumns);
        }}
      />
    </Space>
  );

  // 根据当前激活的tab选择不同的extraContent
  const tabBarExtraContent = useMemo(() => {
    return activeTab === TAB_KEYS.BED_LIST
      ? bedListExtraContent
      : deptHistoryExtraContent;
  }, [activeTab, bedListData, selectedDept, deptHistoryData, searchKeyword]);

  // 科室未选择时显示的提示内容
  const DeptSelectPlaceholder = () => (
    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="请先选择科室" />
  );

  // 使用Tabs的新写法，通过items属性配置
  const tabItems = [
    {
      key: TAB_KEYS.BED_LIST,
      label: '总床位数列表',
      children: (
        <UniTable
          id="hierarchyBedAmtsTable"
          rowKey="uuid" // 使用uuid代替Id作为唯一键
          columns={tableColumns}
          dataSource={filteredBedListData} // 使用过滤后的数据而非原始数据
          loading={columnsLoading || bedListLoading}
          bordered
          scroll={{ x: 'max-content' }}
          actionRef={actionRef}
          dictionaryData={dictData}
          // widthDetectAfterDictionary
          forceColumnsUpdate
        />
      ),
    },
    {
      key: TAB_KEYS.DEPT_HISTORY,
      label: '科室床位数',
      children: (
        <>
          {!selectedDept ? (
            <DeptSelectPlaceholder />
          ) : (
            <UniTable
              id="deptHistoryTable"
              rowKey="uuid" // 使用uuid代替Id作为唯一键
              columns={deptHistoryColumns}
              dataSource={deptHistoryData || []}
              loading={deptHistoryLoading || deptHistoryColumnsLoading}
              bordered
              scroll={{ x: 'max-content' }}
              actionRef={deptHistoryActionRef}
              dictionaryData={dictData}
              widthDetectAfterDictionary
              forceColumnsUpdate
              locale={{
                emptyText: '暂无数据',
              }}
            />
          )}
        </>
      ),
    },
  ];

  return (
    <div className="hierarchy-bed-v2">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        className="card-tab-container"
        tabBarExtraContent={tabBarExtraContent}
        items={tabItems}
      />

      {/* 编辑/新增弹窗 */}
      <EditModal
        visible={editModalVisible}
        record={editRecord}
        columnsData={tableColumns}
        dictData={dictData}
        onCancel={() => setEditModalVisible(false)}
        onSubmit={handleEditSubmit}
      />
    </div>
  );
};

export default HierarchyBedV2;
