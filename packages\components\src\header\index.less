@import '~@uni/commons/src/style/variables.less';

.ant-layout-header.site-layout-header {
  height: @layout-header-height;
  background-image: url('../assets/header-bg.png');
  border-bottom: 1px solid @layout-header-border-color;
  padding: 0 !important;

  .site-layout-header-mask {
    background: @layout-header-bg-linear-color;
    height: 100%;
    width: 100%;
    justify-content: space-between;
  }

  .header-menu {
    width: 100%;
    //display: flex;
    //flex: 1;
    //flex-direction: row;
    //align-items: center;

    .ant-menu {
      height: 100%;
      background: transparent;
      border-bottom: 0;

      .ant-menu-submenu {
        top: 0px;

        img {
          width: 20px;
          height: 20px;
          margin-right: 2px;
        }

        .ant-menu-title-content {
          color: @layout-header-text-color;
        }

        &:hover {
          .ant-menu-title-content {
            color: @layout-header-selected-text-color;
          }
        }
      }

      .ant-menu-submenu-selected {
        background-color: darken(@layout-header-bg-color, 5%);

        &::after {
          border-bottom: 0px;
        }

        .ant-menu-title-content {
          color: @layout-header-selected-text-color;
        }
      }
    }

    .header-menu-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      padding: 20px;
      margin: 10px;
    }
  }

  .header-right {
    margin-right: 20px;
    height: 100%;
    display: flex;
    font-size: 24px;
    color: @layout-header-text-color;

    .ant-space-item {
      display: flex;
      flex-direction: row;
      align-items: center;

      .ant-badge {
        svg {
          font-size: 22px;
          color: @layout-header-text-color;
        }
      }

      a {
        color: @layout-header-text-color;
      }
    }

    .icon {
      cursor: pointer;
    }

    .username {
      display: flex;
      flex-direction: row;
      align-items: center;

      span {
        line-height: 1.125;
        display: block;
        font-size: 16px;
        white-space: nowrap;
      }
    }

    .external-logo {
      margin-left: 10px;
      object-fit: contain;
      width: auto;
      height: 50px;
    }
  }
}

.ant-menu-submenu-popup {
  .ant-menu:not(.ant-menu-horizontal) {
    .ant-menu-item-active,
    .ant-menu-item-selected {
      background-color: @light-blue-color;

      .ant-menu-title-content,
      a {
        color: @layout-header-bg-color;
      }
    }
  }
}
