import { ColumnItem } from '@uni/commons/src/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import { Button, Collapse, Form, Switch, Tag } from 'antd';
import Search from 'antd/es/input/Search';
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import _ from 'lodash';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { isEmptyValues } from '@uni/utils/src/utils';
import { UniDragEditTable } from '@uni/components/src/index';
import { DetailColumnSettingContentConstants } from './constants';
import { columnSettingColumns } from './columns';
import './index.less';

const externalStatsAnalysisConfig = (window as any).externalConfig?.[
  'statsAnalysis'
];

const tagBaseStyle = externalStatsAnalysisConfig?.['tagBaseStyle'] ?? {};

const { CheckableTag } = Tag;

interface TreeItemProps {
  itemKey: string;
  title: string;
  selected: any[];
  items: any[];
  keyword?: string;
  onItemClicked: (item, checked) => void;
  onItemsClicked: (items, checked) => void;
}

export const TreeItem = (props: TreeItemProps) => {
  const filteredItems = props?.items
    ?.filter((item) =>
      props?.keyword
        ? item?.title?.indexOf(props?.keyword) > -1 ||
          pinyinInitialSearch(item?.title, props?.keyword)
        : true,
    )
    ?.sort((a, b) => (a?.columnSequence ?? 0) - (b?.columnSequence ?? 0));

  const allItemKeys = filteredItems?.map((item) => item?.key);
  let checkedKeys = props?.selected?.map((item) => item?.key);

  let allChecked =
    allItemKeys.filter((v) => checkedKeys.indexOf(v) == -1)?.length === 0;
  let checkedKeysSize = checkedKeys.filter(
    (v) => allItemKeys.indexOf(v) > -1,
  )?.length;

  const hasDoubleDirectoriesItems = filteredItems?.filter((item) => {
    return item?.directories?.length > 1;
  });

  const doubleDirectoriesGroupedBySubDirectory = _.groupBy(
    hasDoubleDirectoriesItems,
    (item) => {
      return item?.directories?.slice(1)?.at(0);
    },
  );

  return (
    <>
      {filteredItems?.length > 0 && (
        <div id={props?.itemKey} className={'tree-item-container'}>
          <div className={'tree-header-container'}>
            <div className={'label-container'}>
              <span className={'title'}>{props?.title}</span>
              <span style={{ fontSize: 12, marginLeft: 3, marginBottom: 2 }}>
                <span className={'selected-number'}>{checkedKeysSize}</span>/
                {props?.items?.length}
              </span>
            </div>

            <div className={'flex-row-center'}>
              <span style={{ fontSize: 12 }}>全选</span>
              <Switch
                size={'small'}
                checkedChildren={<CheckOutlined />}
                unCheckedChildren={<CloseOutlined />}
                checked={allChecked}
                onChange={(checked) => {
                  props?.onItemsClicked(filteredItems, checked);
                }}
              />
            </div>
          </div>

          <div className={'items-container'}>
            {filteredItems
              ?.filter((item) => {
                if (isEmptyValues(props?.keyword)) {
                  return item?.directories?.length === 1;
                }

                return true;
              })
              ?.filter((item) => !isEmptyValues(item?.title))
              ?.map((tagItem) => {
                return (
                  <CheckableTag
                    style={tagBaseStyle}
                    className={
                      props?.selected?.find(
                        (item) => item?.key === tagItem?.key,
                      )
                        ? 'tag-item-checked'
                        : 'tag-item'
                    }
                    key={tagItem?.key}
                    checked={props?.selected?.find(
                      (item) => item?.key === tagItem?.key,
                    )}
                    onChange={(checked) => {
                      props.onItemClicked(tagItem, checked);
                    }}
                  >
                    <div
                      className={'flex-row-center'}
                      style={{
                        padding: props?.selected?.find(
                          (item) => item?.key === tagItem?.key,
                        )
                          ? 0
                          : '0 8px',
                      }}
                    >
                      {props?.selected?.find(
                        (item) => item?.key === tagItem?.key,
                      ) && <CheckOutlined className={'tick'} />}

                      {tagItem?.title}
                    </div>
                  </CheckableTag>
                );
              })}
          </div>
          {isEmptyValues(props?.keyword) &&
            !isEmptyValues(doubleDirectoriesGroupedBySubDirectory) && (
              <div className={'collapse-container'}>
                <Collapse bordered={false} expandIconPosition={'end'}>
                  {Object.keys(doubleDirectoriesGroupedBySubDirectory)?.map(
                    (key) => {
                      const groupKeys =
                        doubleDirectoriesGroupedBySubDirectory?.[key]?.map(
                          (item) => item?.key,
                        );
                      let currentGroupSelectedKeySize = checkedKeys.filter(
                        (v) => groupKeys.indexOf(v) > -1,
                      )?.length;

                      return (
                        <Collapse.Panel
                          header={
                            <>
                              <span className={'title'}>{key}</span>
                              <span
                                style={{
                                  fontSize: 12,
                                  marginLeft: 3,
                                  marginBottom: 2,
                                }}
                              >
                                <span className={'selected-number'}>
                                  {currentGroupSelectedKeySize}
                                </span>
                                /
                                {
                                  doubleDirectoriesGroupedBySubDirectory?.[key]
                                    ?.length
                                }
                              </span>
                            </>
                          }
                          key={key}
                        >
                          <div
                            className={'panel-item-container items-container'}
                          >
                            {doubleDirectoriesGroupedBySubDirectory?.[key]?.map(
                              (tagItem) => {
                                return (
                                  <CheckableTag
                                    style={tagBaseStyle}
                                    className={
                                      props?.selected?.find(
                                        (item) => item?.key === tagItem?.key,
                                      )
                                        ? 'tag-item-checked'
                                        : 'tag-item'
                                    }
                                    key={tagItem?.key}
                                    checked={props?.selected?.find(
                                      (item) => item?.key === tagItem?.key,
                                    )}
                                    onChange={(checked) => {
                                      props.onItemClicked(tagItem, checked);
                                    }}
                                  >
                                    <div
                                      className={'flex-row-center'}
                                      style={{
                                        padding: props?.selected?.find(
                                          (item) => item?.key === tagItem?.key,
                                        )
                                          ? 0
                                          : '0 8px',
                                      }}
                                    >
                                      {props?.selected?.find(
                                        (item) => item?.key === tagItem?.key,
                                      ) && <CheckOutlined className={'tick'} />}

                                      {tagItem?.title}
                                    </div>
                                  </CheckableTag>
                                );
                              },
                            )}
                          </div>
                        </Collapse.Panel>
                      );
                    },
                  )}
                </Collapse>
              </div>
            )}
        </div>
      )}
    </>
  );
};

interface DetailColumnsSettingTreeProps {
  treeData?: any[];
  checkKeys?: string[];
  extraData?: any;
  columnTreeContainerRef: any;
}

export const DetailColumnsSettingTree = (
  props: DetailColumnsSettingTreeProps,
) => {
  const [keyword, setKeyword] = useState('');

  const [selectedAnchor, setSelectedAnchor] = useState<string>('');

  const [selectedItems, setSelectedItems] = useState<any[]>([]);

  useImperativeHandle(props?.columnTreeContainerRef, () => {
    return {
      getSelectedItems: () => {
        return selectedItems;
      },
    };
  });

  useEffect(() => {
    if (props?.checkKeys?.length > 0) {
      let selectedItems = [];
      props?.treeData?.forEach((item) => {
        item?.children?.forEach((childItem) => {
          if (
            props?.checkKeys?.find(
              (checkKey) => checkKey === childItem?.key,
            ) !== undefined
          ) {
            selectedItems.push(childItem);
          }
        });
      });

      // 初始化获取selectedItems的时候，获取完了先排序好
      setSelectedItems(selectedItems.sort((a, b) => a?.order - b?.order));
    }
  }, [props?.checkKeys]);

  useEffect(() => {
    setSelectedAnchor(props?.treeData?.at(0)?.key);
  }, [props?.treeData]);

  useEffect(() => {
    Emitter.emit(
      DetailColumnSettingContentConstants.COLUMN_SELECT,
      selectedItems,
    );

    Emitter.on(DetailColumnSettingContentConstants.COLUMN_DESELECT, (item) => {
      onItemClicked(item, false);
    });

    return () => {
      // Emitter.off(DetailColumnSettingContentConstants.COLUMN_SELECT);  why?
      Emitter.off(DetailColumnSettingContentConstants.COLUMN_DESELECT);
    };
  }, [selectedItems]);

  const onItemClicked = (item, checked) => {
    if (checked) {
      if (
        selectedItems.find(
          (selectedItem) => selectedItem?.key === item?.key,
        ) === undefined
      ) {
        setSelectedItems([...selectedItems, item]);
      }
    } else {
      let selectedIndex = selectedItems.findIndex(
        (selectedItem) => selectedItem?.key === item?.key,
      );
      if (selectedIndex > -1) {
        selectedItems.splice(selectedIndex, 1);
        setSelectedItems(selectedItems?.slice());
      }
    }
  };

  const onItemsClicked = (items, checked) => {
    let currentItems = selectedItems?.slice();
    if (checked) {
      items?.forEach((item) => {
        if (
          currentItems.find(
            (selectedItem) => selectedItem?.key === item?.key,
          ) === undefined
        ) {
          currentItems?.push(item);
        }
      });
    } else {
      items?.forEach((item) => {
        let selectedIndex = currentItems.findIndex(
          (selectedItem) => selectedItem?.key === item?.key,
        );
        if (selectedIndex > -1) {
          currentItems.splice(selectedIndex, 1);
        }
      });
    }

    setSelectedItems(currentItems);
  };

  const elementIsVisibleInViewport = () => {
    props?.treeData
      ?.map((item) => {
        return document.getElementById(item?.key);
      })
      ?.filter((item) => item)
      ?.forEach((el) => {
        const elementRect = el.getBoundingClientRect();
        const containerRect = document
          .getElementById('detail-tree-container')
          .getBoundingClientRect();
        let visible =
          containerRect.top - (elementRect.top - 10) > 0 &&
          containerRect.top - elementRect.top <= elementRect.height;

        if (visible) {
          setSelectedAnchor(el.id);
        }
      });
  };

  return (
    <div
      className={'detail-columns-tree-container'}
      style={
        props?.extraData?.type === 'DEFAULT_COLUMN_EDIT'
          ? { width: '100%' }
          : {}
      }
    >
      <div className={'flex-row-center'} style={{ margin: '0px 20px' }}>
        <Search
          value={keyword}
          className={'detail-columns-search'}
          style={{ width: '100%' }}
          placeholder="请输入列名称"
          onChange={(event) => {
            setKeyword(event.target.value);
          }}
        />
        {props?.extraData?.type !== 'DEFAULT_COLUMN_EDIT' && (
          <Button
            className={'reset'}
            ghost={true}
            onClick={() => {
              // 重置
              let selectedItems = [];
              props?.treeData?.forEach((item) => {
                item?.children?.forEach((childItem) => {
                  if (
                    props?.checkKeys?.find(
                      (checkKey) => checkKey === childItem?.key,
                    ) !== undefined
                  ) {
                    selectedItems.push(childItem);
                  }
                });
              });
              setSelectedItems(selectedItems);
              Emitter.emit(
                DetailColumnSettingContentConstants.COLUMN_SELECT_RESET,
              );
            }}
          >
            重置
          </Button>
        )}
      </div>

      <div className={'detail-columns-tree-content'}>
        <div className="toc-affix">
          <ul id="demo-toc" className="toc">
            {props?.treeData
              ?.filter((item) => item?.children?.length > 0)
              ?.map((item) => {
                // console.log('anchor', item?.key, selectedAnchor);

                return (
                  <li key={item?.key} title={item?.title}>
                    <a
                      className={item?.key === selectedAnchor ? 'selected' : ''}
                      onClick={() => {
                        setSelectedAnchor(item?.key);
                        document.getElementById(item?.key)?.scrollIntoView({
                          behavior: 'smooth',
                        });
                      }}
                    >
                      {item?.title}
                    </a>
                  </li>
                );
              })}
          </ul>
        </div>

        <div
          id={'detail-tree-container'}
          className={'detail-tree-container'}
          onScroll={(event) => {
            let scrollToBottom =
              document
                .getElementById('detail-tree-container')
                .getBoundingClientRect()?.height +
                document.getElementById('detail-tree-container')?.scrollTop ===
              document.getElementById('detail-tree-container')?.scrollHeight;
            if (scrollToBottom) {
              setSelectedAnchor(
                props?.treeData?.at(props?.treeData?.length - 1)?.key,
              );
              return;
            }
            elementIsVisibleInViewport();
          }}
        >
          {_.cloneDeep(props?.treeData).map((treeItem) => {
            return (
              <TreeItem
                key={treeItem?.key}
                itemKey={treeItem?.key}
                title={treeItem?.title}
                selected={selectedItems}
                items={treeItem?.children ?? []}
                keyword={keyword}
                onItemClicked={onItemClicked}
                onItemsClicked={onItemsClicked}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

interface SelectedColumnTableProps {
  treeData: any[];
}

const SelectedColumnTable = (props: SelectedColumnTableProps) => {
  const [form] = Form.useForm();

  const mainFormInstance = Form.useFormInstance();

  const [selectedItems, setSelectedItems] = useState([]);
  const [selectedItemsOrder, setSelectedItemsOrder] = useState({});
  // now order
  const nowOrder = useRef(1);

  useEffect(() => {
    form.resetFields();

    Emitter.on(DetailColumnSettingContentConstants.COLUMN_SELECT_RESET, () => {
      form.resetFields();
    });

    return () => {
      Emitter.off(DetailColumnSettingContentConstants.COLUMN_SELECT_RESET);
    };
  }, []);

  // 更新最终保存时的columns
  useEffect(() => {
    console.log('mainSet', selectedItems);
    mainFormInstance.setFieldValue('selectedItems', selectedItems);
  }, [selectedItems]);

  useEffect(() => {
    Emitter.on(
      DetailColumnSettingContentConstants.COLUMN_SELECT_TOP,
      (data) => {
        if (data?.index) {
          let currentItem = selectedItems[data?.index];
          let currentSelectedItemOrders = selectedItems?.map(
            (item) => item?.order,
          );

          if (currentItem) {
            selectedItems?.splice(data?.index, 1);

            let currentSelectedItems = [currentItem, ...selectedItems]?.map(
              (item, index) => {
                item['order'] = currentSelectedItemOrders[index];
                return item;
              },
            );
            setSelectedItems(currentSelectedItems);
            console.log(
              'STATS_ANALYSIS_COLUMN_SELECT_TOP.updateSelectedItemOrder',
              currentSelectedItems,
            );
            updateSelectedItemOrder(currentSelectedItems);
          }
        }
      },
    );

    Emitter.on(
      DetailColumnSettingContentConstants.COLUMN_SELECT_BOTTOM,
      (data) => {
        let currentItem = selectedItems[data?.index];
        let currentSelectedItemOrders = selectedItems?.map(
          (item) => item?.order,
        );

        if (currentItem) {
          selectedItems?.splice(data?.index, 1);

          let currentSelectedItems = [...selectedItems, currentItem]?.map(
            (item, index) => {
              item['order'] = currentSelectedItemOrders[index];
              return item;
            },
          );

          setSelectedItems(currentSelectedItems);
          updateSelectedItemOrder(currentSelectedItems);
        }
      },
    );

    Emitter.on(
      DetailColumnSettingContentConstants.COLUMN_SELECT_DELETE,
      (data) => {
        let currentItem = selectedItems[data?.index];
        if (currentItem) {
          Emitter.emit(
            DetailColumnSettingContentConstants.COLUMN_DESELECT,
            currentItem,
          );
        }
      },
    );

    return () => {
      Emitter.offMultiple([
        DetailColumnSettingContentConstants.COLUMN_SELECT, // why?
        DetailColumnSettingContentConstants.COLUMN_SELECT_TOP,
        DetailColumnSettingContentConstants.COLUMN_SELECT_BOTTOM,
        DetailColumnSettingContentConstants.COLUMN_SELECT_DELETE,
      ]);
    };
  }, [selectedItems]);

  useEffect(() => {
    Emitter.on(
      DetailColumnSettingContentConstants.COLUMN_SELECT,
      (selectItems) => {
        let items = _.cloneDeep(selectItems);

        items?.forEach((item) => {
          if (selectedItemsOrder[item?.name]) {
            item['order'] = selectedItemsOrder[item?.name];
          } else {
            // 新增时没有order / 默认进来的items(外面已经排过序，所以order一样)
            // 弃用 columnsSequence
            // console.log('in:', item, nowOrder.current);
            item['order'] = nowOrder.current;
            nowOrder.current += 1;
          }
          item['textOverflowType'] = item?.textOverflowType ?? 'none';
        });

        setSelectedItems(
          items
            ?.slice()
            ?.sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
            ?.slice(),
        );
        updateSelectedItemOrder(items);
      },
    );

    return () => {
      Emitter.offMultiple([DetailColumnSettingContentConstants.COLUMN_SELECT]);
    };
  }, [selectedItemsOrder]);

  const updateSelectedItemOrder = (items: any[]) => {
    let currentSelectedItemsOrder = {};
    items?.forEach((item) => {
      currentSelectedItemsOrder[item?.name] = item?.order;
    });
    setSelectedItemsOrder(currentSelectedItemsOrder);
  };

  return (
    <div className={'selected-table-container'}>
      <UniDragEditTable
        {...props}
        bordered={false}
        form={form}
        key={'column-setting-selected-table'}
        id={'column-setting-selected-table'}
        tableId={'column-setting-selected-table'}
        scroll={{
          y: 400,
        }}
        controlled
        pagination={false}
        className={`table-container`}
        dataSource={selectedItems}
        rowKey={'id'}
        onTableDataSourceOrderChange={(tableData, oldIndex, newIndex) => {
          // 不是交换 而是把order重排 TODO: 如果用重复的order还要处理下？
          let formInstance = mainFormInstance.getFieldValue('selectedItems');

          let currentItems = tableData?.map((d, i) => ({
            ...d,
            order: formInstance?.at(i).order,
            customTitle:
              formInstance?.find((v) => v.id === d.id)?.customTitle ??
              undefined, // 这里要用外部的customTitle替换
          }));

          setSelectedItems(currentItems);
          console.log(
            'onTableDataSourceOrderChange.updateSelectedItemOrder',
            currentItems,
          );
          updateSelectedItemOrder(currentItems);
        }}
        onValuesChange={(recordList) => {
          // 这边set的时候要跟selectedItems合并
          mainFormInstance.setFieldValue('selectedItems', recordList);
        }}
        columns={columnSettingColumns}
      />
    </div>
  );
};

interface QueryDetailColumnSettingsProps {
  nextGeneration: boolean;
  columnsMap?: any;
  columns: ColumnItem[];
  treeData?: any[];
  onColumnSelect?: (columnMap) => void;
  onDefaultColumnSelect?: (selectedKeys: string[]) => void;

  extraData?: any;
  columnTreeContainerRef?: any;
}

const DetailColumnSettingContent = (props: QueryDetailColumnSettingsProps) => {
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  useEffect(() => {
    let checkedKeys = [];
    if (props?.columnsMap) {
      Object.keys(props?.columnsMap)?.forEach((key) => {
        if (props?.columnsMap?.[key]?.show) {
          checkedKeys.push(key);
        }
      });
    }

    setCheckedKeys(checkedKeys);
  }, [props?.columnsMap]);

  return (
    <div className={'detail-columns-setting-info-container'}>
      <DetailColumnsSettingTree
        columnTreeContainerRef={props?.columnTreeContainerRef}
        treeData={props?.treeData}
        checkKeys={checkedKeys}
        extraData={props?.extraData}
      />
      {props?.extraData?.type !== 'DEFAULT_COLUMN_EDIT' && (
        <>
          <div className={'detail-columns-separator'} />
          <SelectedColumnTable treeData={props?.treeData} />
        </>
      )}
    </div>
  );
};

export default DetailColumnSettingContent;
