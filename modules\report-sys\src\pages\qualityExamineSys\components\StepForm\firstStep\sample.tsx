import { checkIsJson } from '@/utils/widgets';
import {
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@uni/components/src/pro-form';
import { AutoComplete, Form } from 'antd';
import { useEffect } from 'react';

const SampleSettings = ({ recordDetail, dictData, isCreate, formRef }) => {
  // 设置默认值
  useEffect(() => {
    // 添加防御性检查，确保 formRef 存在
    if (!formRef?.current) {
      console.log('SampleSettings: formRef 为空');
      return;
    }

    try {
      const currentValues = formRef?.current.getFieldsValue();
      const sampleArgs = currentValues.SampleArgs || {};

      // 设置默认值
      formRef?.current.setFieldsValue({
        SampleArgs: {
          ...sampleArgs,
          EnableAutoAssign:
            sampleArgs.EnableAutoAssign !== undefined
              ? sampleArgs.EnableAutoAssign
              : true,
          EnableAutoPending: true,
          CanRepeat:
            sampleArgs.CanRepeat !== undefined ? sampleArgs.CanRepeat : false,
          CanReviewSelf:
            sampleArgs.CanReviewSelf !== undefined
              ? sampleArgs.CanReviewSelf
              : false,
          AssignMode: sampleArgs.AssignMode || 'ByCnt',
        },
      });
    } catch (err) {
      console.error('SampleSettings 设置默认值时出错:', err);
    }
  }, []);

  return (
    <>
      <ProForm.Group title="SampleSetting">
        <ProFormSelect
          name={['SampleArgs', 'AssignMode']}
          label="分配模式"
          initialValue={recordDetail?.SampleSetting?.AssignMode || 'ByCnt'}
          options={[
            { label: '按数量', value: 'ByCnt' },
            { label: '按比例', value: 'ByRatio', disabled: true },
          ]}
          rules={[{ required: true }]}
          colProps={{
            span: 8,
          }}
          fieldProps={{
            allowClear: false,
            onChange: (value) => {
              formRef.current?.setFieldValue(
                ['SampleArgs', 'EnableAutoPending'],
                value === 'ByCnt' ? true : false,
              );
            },
          }}
        />
        <ProFormDependency name={[['ScheduleArgs', 'ScheduleMode']]}>
          {({ ScheduleArgs }) => {
            console.log(ScheduleArgs, dictData);
            if (ScheduleArgs?.ScheduleMode === 'Periodical') {
              return (
                <>
                  <ProFormSelect
                    name={['SampleArgs', 'ScheduleCrons']}
                    label="采样执行调度"
                    // width="md"
                    initialValue={
                      recordDetail?.SampleSetting?.ScheduleCrons || undefined
                    }
                    fieldProps={{
                      mode: 'tags',
                      maxTagCount: 5,
                    }}
                    tooltip="为空则不自动执行采样"
                    placeholder="请输入采样执行调度"
                    colProps={{
                      span: 6,
                    }}
                  />
                  <ProFormDigit
                    name={['SampleArgs', 'TargetPeriodOffset']}
                    label="目标采样目标周期偏移量（0为本月，-1为上月）"
                    initialValue={
                      recordDetail?.SampleSetting?.TargetPeriodOffset ?? 0
                    }
                    rules={[{ required: true }]}
                    placeholder="请输入"
                    fieldProps={{ precision: 0, min: Number.MIN_SAFE_INTEGER }}
                    colProps={{
                      span: 8,
                    }}
                  />
                </>
              );
            }
          }}
        </ProFormDependency>

        <ProFormTextArea
          name={['SampleArgs', 'BasicArgs']}
          label="基础筛选条件（一般用于限定院区科室等）"
          tooltip="JSON格式"
          initialValue={recordDetail?.SampleSetting?.BasicArgs || undefined}
          placeholder="请输入"
          colProps={{
            span: 8,
          }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value) {
                  return Promise.resolve();
                }
                if (!checkIsJson(value)) {
                  return Promise.reject(new Error('Json格式错误，请检查！'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        />
        <ProFormTextArea
          name={['SampleArgs', 'Expr']}
          label="额外筛选条件"
          tooltip="JSON格式"
          initialValue={recordDetail?.SampleSetting?.Expr || undefined}
          placeholder="请输入"
          colProps={{
            span: 8,
          }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value) {
                  return Promise.resolve();
                }
                if (!checkIsJson(value)) {
                  return Promise.reject(new Error('Json格式错误，请检查！'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        />
        <ProFormText
          name={['SampleArgs', 'DisplayExpr']}
          label="DisplayExpr"
          initialValue={recordDetail?.SampleSetting?.DisplayExpr || undefined}
          placeholder="请输入"
          hidden={true}
          colProps={{
            span: 8,
          }}
        />
        {/* <ProFormText
          name={['SampleArgs', 'ExprProvider']}
          label="ExprProvider"
          initialValue={recordDetail?.SampleSetting?.ExprProvider || undefined}
          placeholder="请输入"
          colProps={{
            span: 8,
          }}
        /> */}

        <ProFormSwitch
          name={['SampleArgs', 'CanRepeat']}
          label="可以重复采样"
          initialValue={recordDetail?.SampleSetting?.CanRepeat ?? false}
          hidden={true}
          colProps={{
            span: 8,
          }}
        />
        <ProFormSwitch
          name={['SampleArgs', 'CanReviewSelf']}
          label="可审核自己的病案"
          initialValue={recordDetail?.SampleSetting?.CanReviewSelf ?? false}
          hidden={true}
          colProps={{
            span: 8,
          }}
        />
        <ProFormDependency name={[['SampleArgs', 'AssignMode']]}>
          {({ SampleArgs }) => {
            if (SampleArgs?.AssignMode === 'ByRatio') {
              return (
                <ProFormDigit
                  name={['SampleArgs', 'MinSampleRatio']}
                  label="全局采样比例"
                  initialValue={
                    recordDetail?.SampleSetting?.MinSampleRatio || undefined
                  }
                  placeholder="请输入"
                  width="sm"
                  colProps={{
                    span: 8,
                  }}
                  rules={[
                    {
                      required: SampleArgs?.AssignMode === 'ByRatio',
                      message: '当分配模式为按比例时，此项必填',
                    },
                  ]}
                  disabled={SampleArgs?.AssignMode !== 'ByRatio'}
                />
              );
            } else if (SampleArgs?.AssignMode === 'ByCnt') {
              return (
                <ProFormDigit
                  name={['SampleArgs', 'MinSampleCnt']}
                  label="全局采样份数"
                  initialValue={
                    recordDetail?.SampleSetting?.MinSampleCnt || undefined
                  }
                  placeholder="请输入"
                  fieldProps={{ precision: 0 }}
                  width="sm"
                  colProps={{
                    span: 8,
                  }}
                  tooltip="最终采样份数可由审核人组的采样份数决定，所以这里可以不填"
                  disabled={SampleArgs?.AssignMode !== 'ByCnt'}
                />
              );
            }
          }}
        </ProFormDependency>
        <ProFormSwitch
          name={['SampleArgs', 'EnableAutoAssign']}
          label="自动抽样评审病案"
          initialValue={recordDetail?.SampleSetting?.EnableAutoAssign ?? true}
          colProps={{
            span: 8,
          }}
        />
        <ProFormSwitch
          name={['SampleArgs', 'EnableAutoPending']}
          label="自动确认分配评审病案"
          initialValue={recordDetail?.SampleSetting?.EnableAutoPending ?? true}
          colProps={{
            span: 8,
          }}
        />
      </ProForm.Group>
    </>
  );
};

export default SampleSettings;
