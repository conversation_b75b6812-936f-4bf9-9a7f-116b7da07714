import { isEmptyValues } from '@uni/utils/src/utils';
import _ from 'lodash';
import { dataItemWithColumnModuleProcessor } from './dictionary-processor';

// 全局 专门用于判断 text width
let sharedCanvas: HTMLCanvasElement | undefined;

// 只能对文本生效 (单个) 用于 column.title
function getTextWidth(text: string, font: string = '14px sans-serif'): number {
  // 创建一个canvas元素或重用已有的canvas元素
  if (!sharedCanvas) {
    sharedCanvas = document.createElement('canvas');
  }
  let context = sharedCanvas.getContext('2d');
  if (!context) {
    throw new Error('无法获取Canvas的2D上下文');
  }
  // 设置字体样式，确保与您要测量的文本的样式相匹配
  context.font = font;
  // 测量文本
  let metrics = context.measureText(text);
  return metrics.width;
}

// 文本检查宽度（array） & 字典翻译结果
function getMaxTextWidthInArr(
  dataSource,
  columnItem,
  dictionaryData = null,
  font: string = '14px sans-serif',
) {
  if (isEmptyValues(dictionaryData)) {
    return { width: 0, longestText: '' };
  }

  if (!sharedCanvas) {
    sharedCanvas = document.createElement('canvas');
  }
  let context = sharedCanvas.getContext('2d');
  if (!context) {
    throw new Error('无法获取Canvas的2D上下文');
  }
  context.font = font;
  let maxWidth = 0;
  let longestText = '';

  for (let i = 0; i < dataSource.length; i++) {
    let dataItem = dataSource[i];
    let dataValue =
      dataItem?.[columnItem?.dataIndex ?? columnItem?.data] || null;
    // 字典翻译
    if (columnItem?.dictionaryModule) {
      let currentDictionaryData = dictionaryData;
      if (columnItem?.dictionaryModuleGroup) {
        currentDictionaryData =
          dictionaryData?.[columnItem?.dictionaryModuleGroup];
      }

      dataValue = dataItemWithColumnModuleProcessor(
        currentDictionaryData,
        columnItem?.dictionaryModule,
        dataValue,
      );
    }

    let metrics = context.measureText(dataValue);
    if (metrics.width > maxWidth) {
      maxWidth = metrics.width;
      longestText = dataValue;
    }
  }

  return { width: maxWidth, longestText };
}

// *** 文本检查时同时还需要字典翻译 不然是检查的是错的 所以在这里同时进行字典翻译
function processColumnsWidthDetect(
  columns,
  dataSource,
  dictionaryData?: any,
  isBackPagination?: boolean,
  parentWidth = [],
) {
  if (isEmptyValues(dictionaryData)) {
    return columns;
  }

  return columns?.map((columnItem, index) => {
    // 已有width && 不是通过该函数设置
    if (columnItem?.width && !columnItem?.isWidthDetect) {
      return columnItem;
    }

    // 没有dataSource
    if (!dataSource || dataSource?.length < 1) {
      let width = getTextWidth(columnItem?.title);
      columnItem.width = width + 25 + 20;
      columnItem.isWidthDetect = true;

      return columnItem;
    }

    // 多层
    if (columnItem.children) {
      columnItem.children = processColumnsWidthDetect(
        columnItem.children,
        dataSource,
      );
      // TODO 如果头比子加起来还大用头
    }

    // 没有width

    // 1. 非string的title，一般是react.node
    // 目前报表就一种情况使用了React.Node → cellClickableProcessor
    if (!_.isString(columnItem?.title)) {
      let width = getTextWidth(columnItem?.name);
      columnItem.width = width + 18 + 50;
      return columnItem;
    }

    // 2. string && normalRenderer [若自定义render了则不会进入width判断]
    if (columnItem?.normalRenderer) {
      // 但凡有一个dataSource长于title好像就不用设？
      let width = getTextWidth(columnItem?.title);

      let maxWidthData = getMaxTextWidthInArr(
        dataSource,
        columnItem,
        dictionaryData,
      );

      columnItem.width = _.max([maxWidthData?.width, width]) + 25 + 20;
      // 一个特殊标识符 表示这个width是通过widthDetect来赋值的
      // 用于 后端分页
      columnItem.isWidthDetect = true;
    }

    return columnItem;
  });
}

// 用于在dictionary翻译之后检查width 通过配置开启 默认不走这个 report默认开启
export const ColumnWidthDetectProcessor = (
  columns,
  dataSource,
  dictionaryData,
  isBackPagination,
) => {
  return processColumnsWidthDetect(
    columns,
    dataSource,
    dictionaryData,
    isBackPagination,
  );
};
