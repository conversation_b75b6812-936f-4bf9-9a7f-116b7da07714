import Datepicker from '@uni/components/src/picker/datepicker';
import dayjs from 'dayjs';

interface ReportFormRangePickerProps {
  picker?: string;

  value?: string[];

  onChange: (values) => void;
}

const ReportFormRangePicker = (props: ReportFormRangePickerProps) => {
  return (
    <Datepicker.RangePicker
      {...props}
      picker={props?.picker || 'date'}
      allowClear={true}
      value={[
        props?.value?.at(0) ? dayjs(props?.value?.at(0)) : undefined,
        props?.value?.at(1) ? dayjs(props?.value?.at(1)) : undefined,
      ]}
      onChange={(dates, dateStrings) => {
        props?.onChange && props?.onChange(dateStrings);
      }}
    />
  );
};

export default ReportFormRangePicker;
