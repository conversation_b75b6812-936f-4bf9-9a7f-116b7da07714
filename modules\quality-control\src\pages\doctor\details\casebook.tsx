import React, { useEffect, useRef, useState } from 'react';
import {
  <PERSON>ton,
  Card,
  Tooltip,
  TableProps,
  Space,
  Divider,
  message,
} from 'antd';
import { useRequest } from 'umi';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { useModel } from '@@/plugin-model/useModel';
import UniTable from '@uni/components/src/table';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './casebook.less';
import { useUpdateEffect } from 'ahooks';
import SearchForm from '@/components/searchForm';
import { OptsProps, SearchItemsOpts } from './formItems';
import { isEmptyObj } from '@/utils/widgets';
import { CasebookColumns } from './columns';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import cloneDeep from 'lodash/cloneDeep';
import { EventConstant } from '@uni/utils/src/emitter';
import ExportIconBtn from '@uni/components/src/backend-export';
import { isEmptyValues } from '@uni/utils/src/utils';
import pick from 'lodash/pick';
import { nextPreviousDmrInfoTableDataSetter } from '@uni/utils/src/next-dmr-utils';
import { dateRangeValueProcessor } from '@uni/components/src/date-range-with-type';
import SummaryItem from './components/SummaryItem';
import { summarys } from './components/data';
import { getSessionStorage } from '@/utils/utils';
import DoctorSvg from './components/DoctorSvg';

const headerSearchForceModified =
  (window as any).externalConfig?.['common']?.headerSearchForceModified ??
  false;

const CasebookDetail = () => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster');

  const [searchedValue, setSearchedValue] = useState(() => {
    let curObj;
    if (isEmptyObj(searchParams)) {
      const obj = getSessionStorage('searchOpts');
      console.log(obj, 'obj123');
      let currentObj = { ...obj };
      let startDate = obj?.['dateRange']?.[0];
      let endDate = obj?.['dateRange']?.[1];
      if (startDate && endDate) {
        let dates = dateRangeValueProcessor(obj, true, true);
        currentObj['Sdate'] = dates?.['Sdate'];
        currentObj['Edate'] = dates?.['Edate'];
        currentObj['dateRange'] = [dates?.['Sdate'], dates?.['Edate']];
      }
      curObj = {
        ..._.omit(currentObj, 'OutType'),
      };
    } else {
      curObj = {
        ..._.omit(searchParams, 'OutType'),
      };
    }
    console.log(curObj, 'curObj66666');
    return curObj;
  });

  const [columns, setColumns] = useState([]);
  const [summarySelectedKey, setSummarySelectedKey] = useState('TotalCount');

  const [selectedHisId, setSelectedHisId] = useState(undefined);

  const cmrInfoViewRef = useRef(null);

  const [DatasourceData, setDatasourceData] = useState([]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  useUpdateEffect(() => {
    if (!isEmptyObj(searchedValue)) {
      setBackPagination({
        ...backPagination,
        current: 1,
      });
      GetDatasourceReq(1, backPagination.pageSize);
      GetStatsReq(searchedValue);
    }
  }, [searchedValue]);

  // stats
  const {
    data: StatsData,
    loading: GetStatsLoading,
    run: GetStatsReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/Emr/EmrQcReport/GetQcCardAndReviewStats', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
      },
    },
  );

  // columns
  const {
    data: ColumnsData,
    loading: GetColumnsLoading,
    run: GetColumnsReq,
  } = useRequest(
    () =>
      uniCommonService('Api/Emr/EmrQcReport/GetQcCardAndReviewResults', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      // manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          setColumns(
            tableColumnBaseProcessor(CasebookColumns, res.data.Columns),
          );
        }
      },
    },
  );

  // datasource
  const { loading: GetDatasourceLoading, run: GetDatasourceReq } = useRequest(
    (current, pageSize) =>
      uniCommonService('Api/Emr/EmrQcReport/GetQcCardAndReviewResults', {
        method: 'POST',
        data: {
          DtParam: {
            Draw: 1,
            Start: (current - 1) * pageSize,
            Length: pageSize,
          },
          ...pick(searchedValue, Object.keys(new OptsProps())),
          ...summarySelectedKeyHandler(),
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          nextPreviousDmrInfoTableDataSetter(
            res?.data,
            setDatasourceData,
            backPagination,
            setBackPagination,
          );
          return res.data.data;
        }
      },
    },
  );

  const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    GetDatasourceReq(pagi.current, pagi.pageSize);
  };

  // handle summary selected
  const summarySelectedKeyHandler = () => {
    if (summarySelectedKey) {
      let summaryItem = summarys?.find(
        (item) => item?.key === summarySelectedKey,
      );
      if (summaryItem) {
        return summaryItem.value;
      }
    }
    return {};
  };

  const onExtraSearchedHisIdLastOne = async () => {
    let current = backPagination.current + 1;
    setBackPagination({
      ...backPagination,
      current: current,
    });

    let nextPageData = await GetDatasourceReq(
      current,
      backPagination?.pageSize,
    );
    setSelectedHisId(undefined);

    return (
      nextPageData
        ?.map((item) => item?.HisId)
        ?.filter((item) => !isEmptyValues(item)) ?? []
    );
  };
  // 病例页
  const onEmrInfoViewClick = (record: any) => {
    (global?.window as any)?.eventEmitter?.emit('DMR_Index_STATUS', {
      status: true, // 弹窗开启状态
      hisId: record.HisId, // 医嘱ID
      extraSearchedHisIds: DatasourceData?.map((item) => item?.HisId)?.filter(
        (item) => !isEmptyValues(item),
      ),
    });
  };

  const isNumberOrNonEmptyString = (value) => {
    return (
      typeof value === 'number' ||
      (typeof value === 'string' && value.trim() !== '')
    );
  };

  React.useImperativeHandle(cmrInfoViewRef, () => {
    return {
      onEmrInfoViewClick: (record) => {
        onEmrInfoViewClick(record);
      },
    };
  });

  return (
    <>
      <Card style={{ marginBottom: '20px' }}>
        <SearchForm
          className="casebook_flex_form"
          grid
          rowProps={{ gutter: [16, 16] }}
          searchOpts={SearchItemsOpts(searchedValue, {
            hospOpts: dictData.Hospital,
            deptOpts: dictData.CliDepts,
            coderOpts: dictData?.['Dmr']?.Coder,
          })}
          submitter={{
            render: (props) => {
              return [
                <Button
                  type="primary"
                  style={{
                    width: '150px',
                    float: 'right',
                  }}
                  key="reset"
                  onClick={() => {
                    props.form?.submit?.();
                  }}
                >
                  查询
                </Button>,
              ];
            },
          }}
          onFinish={async (values) => {
            let dates = dateRangeValueProcessor(values, true, true);
            values['Sdate'] = dates?.['Sdate'];
            values['Edate'] = dates?.['Edate'];
            values['dateRange'] = [dates?.['Sdate'], dates?.['Edate']];

            let searchValues = {
              ...values,
              ...dates,
              UseCodeTimeFilter:
                values.DateType === 'RegisterDate' ? true : false,
            };
            setSearchedValue(
              headerSearchForceModified === false
                ? cloneDeep(searchValues)
                : searchValues,
            );
            (global?.window as any)?.eventEmitter?.emit(
              EventConstant.HEADER_SEARCH_PARAM_SET_PROGRAMMATICALLY,
              {
                ...searchValues,
                searchNow: headerSearchForceModified === false,
              },
            );
            return true;
          }}
        />
      </Card>
      <Card
        title="病案质控明细"
        extra={
          <Space>
            <Divider type="vertical" />
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: 'Api/Emr/EmrQcReport/ExportGetQcCardAndReviewResults',
                method: 'POST',
                data: {
                  ...searchedValue,
                  ...summarySelectedKeyHandler(),
                },
                fileName: `病案质控明细`,
              }}
              btnDisabled={DatasourceData?.length < 1}
            />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl:
                  'Api/Emr/EmrQcReport/GetQcCardAndReviewResults',
                onTableRowSaveSuccess: (columns) => {
                  setColumns(
                    tableColumnBaseProcessor(CasebookColumns, columns),
                  );
                },
              }}
            />
          </Space>
        }
      >
        <Space>
          {summarys?.map((item) => {
            return (
              <SummaryItem
                className={
                  item?.key === summarySelectedKey ? 'card-selected' : ''
                }
                item={item}
                count={StatsData?.[item.key]}
                onClick={() => {
                  setSummarySelectedKey(item.key);
                  setTimeout(() => {
                    if (!isEmptyObj(searchedValue)) {
                      setBackPagination({
                        ...backPagination,
                        current: 1,
                      });
                      if (isNumberOrNonEmptyString(StatsData?.[item.key])) {
                        GetDatasourceReq(1, backPagination.pageSize);
                      }
                    }
                  }, 0);
                }}
              />
            );
          })}
        </Space>
        <UniTable
          id="casebook-table"
          className="casebook_table"
          rowKey="aguid"
          loading={GetDatasourceLoading}
          rowClassName={(record, index) => {
            return !isEmptyValues(record?.HisId) &&
              record?.HisId === selectedHisId
              ? 'table-hisId-row-selected'
              : '';
          }}
          columns={
            (columns.length && [
              {
                title: '',
                colSize: 0.5,
                width: 50,
                fixed: 'left',
                align: 'center',
                visible: true,
                valueType: 'option',
                render: (_, record) => {
                  return [
                    <Tooltip key="watch" title="查看医生明细">
                      <div
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          if (!isEmptyValues(record?.HisId)) {
                            cmrInfoViewRef?.current?.onEmrInfoViewClick(record);
                          } else {
                            message.error('没有可查看的医生明细--HisId为空');
                          }
                        }}
                      >
                        <DoctorSvg />
                      </div>
                    </Tooltip>,
                  ];
                },
              },
              ...columns,
            ]) ||
            []
          }
          dictionaryData={dictData}
          dataSource={DatasourceData ?? []}
          pagination={backPagination}
          onChange={backTableOnChange}
        />
      </Card>
    </>
  );
};

export default CasebookDetail;
