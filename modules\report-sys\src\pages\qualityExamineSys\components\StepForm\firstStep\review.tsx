import {
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@uni/components/src/pro-form';
import { AutoComplete, Form } from 'antd';
import { useEffect } from 'react';

// 预设模式的配置
const PRESET_MODES = {
  RepairMode: {
    name: '返修模式',
    settings: {
      EnableAppeal: true,
      EnableResolve: false,
      EnableIssue: true,
      EnableReject: false,
      EnableAutoReject: true,
      EnableRevieweeResolve: true,
      EnableAutoAccept: true,
    },
  },
  OnlyReviewMode: {
    name: '仅评审模式',
    settings: {
      EnableAppeal: false,
      EnableResolve: false,
      EnableIssue: false,
      EnableReject: false,
      EnableAutoReject: false,
      EnableRevieweeResolve: false,
      EnableAutoAccept: false,
    },
  },
};

const ReviewSettings = ({ recordDetail, dictData, isCreate, formRef }) => {
  // 检查当前开关值是否匹配某个预设模式
  const checkIfMatchesPreset = (values) => {
    // 检查返修模式
    const matchesRepair = Object.entries(
      PRESET_MODES.RepairMode.settings,
    ).every(([key, value]) => values[key] === value);

    // 检查仅评审模式
    const matchesReview = Object.entries(
      PRESET_MODES.OnlyReviewMode.settings,
    ).every(([key, value]) => values[key] === value);

    if (matchesRepair) {
      return 'RepairMode';
    } else if (matchesReview) {
      return 'OnlyReviewMode';
    }

    return null;
  };

  // 当开关值变化时检查预设模式
  const handleSwitchChange = () => {
    if (!formRef) {
      console.log('ReviewSettings: formRef?.current 为空');
      return;
    }

    try {
      const currentValues = formRef?.current?.getFieldsValue();
      const matchedMode = checkIfMatchesPreset(currentValues);

      // 如果开关值与某个预设模式匹配，则选中该模式
      // 否则清除预设模式的选择
      formRef?.current?.setFieldsValue({
        ReviewArgs: {
          ...currentValues.ReviewArgs,
          preset_mode: matchedMode,
        },
      });
    } catch (err) {
      console.error('ReviewSettings handleSwitchChange 错误:', err);
    }
  };

  // 当预设模式变化时更新所有开关
  const handlePresetChange = (value) => {
    console.log('handlePresetChange', value, formRef?.current);
    if (!formRef?.current) {
      console.log('ReviewSettings: formRef?.current 为空');
      return;
    }

    try {
      if (value && PRESET_MODES[value]) {
        const currentValues = formRef?.current?.getFieldsValue();
        const currentReviewArgs = currentValues.ReviewArgs || {};

        // 应用选定预设模式的设置
        formRef?.current?.setFieldsValue({
          ReviewArgs: {
            ...currentReviewArgs,
            ...PRESET_MODES[value].settings,
          },
        });
      }
    } catch (err) {
      console.error('ReviewSettings handlePresetChange 错误:', err);
    }
  };

  // 初始化表单
  useEffect(() => {
    if (!formRef?.current) {
      console.log('ReviewSettings: formRef?.current 为空');
      return;
    }

    try {
      // 创建时默认选择"仅评审模式"
      if (isCreate) {
        const currentValues = formRef?.current.getFieldsValue();
        const currentReviewArgs = currentValues.ReviewArgs || {};

        formRef?.current.setFieldsValue({
          ReviewArgs: {
            ...currentReviewArgs,
            preset_mode: 'OnlyReviewMode',
            ...PRESET_MODES.OnlyReviewMode.settings,
          },
        });
      } else if (recordDetail?.ReviewSetting) {
        // 更新时，根据初始值检查是否匹配预设模式
        const reviewSettings = {
          EnableAppeal: recordDetail.ReviewSetting.EnableAppeal || false,
          EnableResolve: recordDetail.ReviewSetting.EnableResolve || false,
          EnableIssue: recordDetail.ReviewSetting.EnableIssue || false,
          EnableReject: recordDetail.ReviewSetting.EnableReject || false,
          EnableAutoReject:
            recordDetail.ReviewSetting.EnableAutoReject || false,
          EnableRevieweeResolve:
            recordDetail.ReviewSetting.EnableRevieweeResolve || false,
          EnableAutoAccept:
            recordDetail.ReviewSetting.EnableAutoAccept || false,
        };

        // 检查现有设置是否匹配某个预设模式
        let matchedMode = null;

        if (
          Object.entries(PRESET_MODES.RepairMode.settings).every(
            ([key, value]) => reviewSettings[key] === value,
          )
        ) {
          matchedMode = 'RepairMode';
        } else if (
          Object.entries(PRESET_MODES.OnlyReviewMode.settings).every(
            ([key, value]) => reviewSettings[key] === value,
          )
        ) {
          matchedMode = 'OnlyReviewMode';
        }

        // 更新表单值
        const currentValues = formRef?.current.getFieldsValue();
        const currentReviewArgs = currentValues.ReviewArgs || {};

        formRef?.current.setFieldsValue({
          ReviewArgs: {
            ...currentReviewArgs,
            preset_mode: matchedMode,
            ...reviewSettings,
          },
        });
      }
    } catch (err) {
      console.error('ReviewSettings 初始化表单时出错:', err);
    }
  }, [isCreate, recordDetail]);

  return (
    <>
      <ProForm.Group title="ReviewSetting">
        <ProFormSelect
          name={['ReviewArgs', 'ReviewMode']}
          label="审阅模式"
          initialValue={recordDetail?.ReviewSetting?.ReviewMode || undefined}
          placeholder="请选择"
          width="sm"
          options={dictData.ReviewMode}
          fieldProps={{
            fieldNames: {
              label: 'Name',
              value: 'Code',
            },
          }}
          rules={[{ required: true }]}
          colProps={{
            span: 6,
          }}
        />
        <ProFormSelect
          name={['ReviewArgs', 'preset_mode']}
          label="预设模式"
          width="sm"
          options={[
            {
              label: '仅评审模式',
              value: 'OnlyReviewMode',
            },
            {
              label: '返修模式',
              value: 'RepairMode',
            },
          ]}
          colProps={{
            span: 6,
          }}
          fieldProps={{
            allowClear: true,
            onChange: handlePresetChange,
          }}
        />
        <ProFormSwitch
          name={['ReviewArgs', 'EnableResolve']}
          label="EnableResolve"
          fieldProps={{
            onChange: handleSwitchChange,
          }}
          colProps={{
            span: 6,
          }}
        />
        <ProFormSwitch
          name={['ReviewArgs', 'EnableIssue']}
          label="EnableIssue"
          fieldProps={{
            onChange: handleSwitchChange,
          }}
          colProps={{
            span: 6,
          }}
        />
        <ProFormSwitch
          name={['ReviewArgs', 'EnableReject']}
          label="EnableReject"
          fieldProps={{
            onChange: handleSwitchChange,
          }}
          colProps={{
            span: 6,
          }}
        />
        <ProFormSwitch
          name={['ReviewArgs', 'EnableAutoReject']}
          label="EnableAutoReject"
          fieldProps={{
            onChange: handleSwitchChange,
          }}
          colProps={{
            span: 6,
          }}
        />
        <ProFormSwitch
          name={['ReviewArgs', 'EnableAppeal']}
          label="EnableAppeal"
          fieldProps={{
            onChange: handleSwitchChange,
          }}
          colProps={{
            span: 6,
          }}
        />
        <ProFormSwitch
          name={['ReviewArgs', 'EnableRevieweeResolve']}
          label="EnableRevieweeResolve"
          fieldProps={{
            onChange: handleSwitchChange,
          }}
          colProps={{
            span: 6,
          }}
        />
        <ProFormSwitch
          name={['ReviewArgs', 'EnableAutoAccept']}
          label="EnableAutoAccept"
          fieldProps={{
            onChange: handleSwitchChange,
          }}
          colProps={{
            span: 6,
          }}
        />
      </ProForm.Group>
    </>
  );
};

export default ReviewSettings;
