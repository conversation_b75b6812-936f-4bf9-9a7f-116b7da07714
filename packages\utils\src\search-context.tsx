import pick from 'lodash/pick';

export const useHeaderKeys = () => {
  const headerItems = (
    global?.window as any
  )?.headerRef?.current?.getHeaderItems();

  let headerKeys = [];
  headerItems?.forEach((item) => {
    headerKeys.push(
      item?.props?.formKey ?? item?.props?.valueKey ?? item?.props?.dataKey,
    );
  });

  return headerKeys;
};

// TODO 年月日选择 / 一个 form 多个参数情况 / excludeKeys
export const pickOnlyNeedKeys = (
  params,
  auto = false,
  needKeys = [],
  excludeKeys = [],
) => {
  let pickKeys = needKeys;
  if (auto === true) {
    pickKeys.push(...useHeaderKeys());
  }

  if (pickKeys?.length === 0) {
    return params;
  }

  let dateParams = {};
  if (pickKeys?.includes('dateRange')) {
    dateParams['Sdate'] = params?.dateRange?.at(0);
    dateParams['Edate'] = params?.dateRange?.at(1);
  }

  if (pickKeys?.includes('WardSignOutRange')) {
    dateParams['WardSignOutSdate'] = params?.WardSignOutRange?.at(0);
    dateParams['WardSignOutEdate'] = params?.WardSignOutRange?.at(1);
  }

  if (pickKeys?.includes('borrowDateRange')) {
    dateParams['BorrowSdate'] = params?.borrowDateRange?.at(0);
    dateParams['BorrowEdate'] = params?.borrowDateRange?.at(1);
  }

  if (pickKeys?.includes('SignInRange')) {
    dateParams['SignInSdate'] = params?.SignInRange?.at(0);
    dateParams['SignInEdate'] = params?.SignInRange?.at(1);
  }

  if (pickKeys?.includes('ArchivedRange')) {
    dateParams['ArchivedSdate'] = params?.ArchivedRange?.at(0);
    dateParams['ArchivedEdate'] = params?.ArchivedRange?.at(1);
  }

  if (pickKeys?.includes('PrintRange')) {
    dateParams['PrintSdate'] = params?.PrintRange?.at(0);
    dateParams['PrintEdate'] = params?.PrintRange?.at(1);
  }

  if (pickKeys?.includes('dmrSignOutDateRange')) {
    dateParams['dmrSignOutSdate'] = params?.dmrSignOutDateRange?.at(0);
    dateParams['dmrSignOutEdate'] = params?.dmrSignOutDateRange?.at(1);
  }

  // 处理select-text-input类型数据
  let selectTextInputParams = {};
  if (params) {
    // 检查pickKeys是否包含'select-text-input'
    if (
      pickKeys?.includes('select-text-input') &&
      params['select-text-input']
    ) {
      const value = params['select-text-input'];
      // 检查是否为数组且长度为2且两个元素都有值
      if (
        Array.isArray(value) &&
        value.length === 2 &&
        typeof value[0] === 'string' &&
        value[1] !== null &&
        value[1] !== undefined &&
        value[1] !== ''
      ) {
        // 将第一个元素作为key，第二个元素作为value
        selectTextInputParams[value[0]] = value[1];
      }
    }
  }

  return {
    ...dateParams,
    ...pick(params, pickKeys),
    ...selectTextInputParams,
  };
};
