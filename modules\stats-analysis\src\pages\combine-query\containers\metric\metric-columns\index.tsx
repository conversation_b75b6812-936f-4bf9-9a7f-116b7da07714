import './index.less';
import {
  Affix,
  Anchor,
  Button,
  Checkbox,
  Col,
  Modal,
  Row,
  Space,
  Switch,
  TableColumnType,
  Tooltip,
  Tree,
  Tag,
  List,
  Form,
  Tabs,
  Badge,
} from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import Search from 'antd/es/input/Search';
import { ColumnItem } from '@uni/commons/src/interfaces';
import {
  pinyinInitialSearch,
  searchFunctionGetter,
} from '@uni/utils/src/pinyin';
import { cloneDeep } from 'lodash';
import { Emitter } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import {
  CheckOutlined,
  CloseOutlined,
  FlagOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { UniDragEditTable } from '@uni/components/src';
import { arrayMoveImmutable } from '@/pages/combine-query/utils';
import {
  MetricsAggregateItem,
  MetricsGroupItem,
  MetricsMetricItem,
} from '@/pages/combine-query/interfaces';
import { metricsItemsProcessor } from '@/pages/combine-query/processor';
import { ReactComponent as IconDiamond } from '@/assets/icon_diamond.svg';
import Diamond from '@uni/commons/src/icon/Diamond';
import Flag from '@uni/commons/src/icon/Flag';
import { SelectedMetricItem } from '@/pages/combine-query/containers/metric';
import {
  metricColumnsSelectCompleteProcessor,
  metricGroupTreeProcessor,
  metricStateSelectCompleteProcessor,
  rightTableColumnsProcessor,
} from '@/pages/combine-query/containers/metric/metric-columns/processors';
import { useModel } from 'umi';
import { metricColumnSettingColumns } from '@/pages/combine-query/containers/metric/metric-columns/columns';
import { isEmptyValues } from '@uni/utils/src/utils';
import MetricPreviewTable from '@/pages/combine-query/containers/metric/metric-columns/preview';

const { CheckableTag } = Tag;

const TabKeys = {
  STATISTICS: 'STATISTICS',
  BASIC: 'BASIC',
};

const AggregationNames = {
  Avg: '平均值',
  Sum: '合计值',
  Min: '最小值',
  Max: '最大值',
};

const externalStatsAnalysisConfig = (window as any).externalConfig?.[
  'statsAnalysis'
];

const tagBaseStyle = externalStatsAnalysisConfig?.['tagBaseStyle'] ?? {};

interface CombineQueryMetricColumnSettingsProps {
  nextGeneration: boolean;
  tableName: string;
  form: any;

  metricsState?: any;
  aggregateItems?: MetricsAggregateItem[];
  groupItems?: MetricsGroupItem[];
  metricItems?: MetricsMetricItem[];

  tabKey?: string;
}

const CombineQueryMetricColumnSettings = (
  props: CombineQueryMetricColumnSettingsProps,
) => {
  const [form] = Form.useForm();

  const [
    combineQueryMetricColumnSettingModalVisible,
    setCombineQueryMetricColumnSettingModalVisible,
  ] = React.useState(false);

  const [
    combineQueryMetricColumnSettingPreviewModalVisible,
    setCombineQueryMetricColumnSettingPreviewModalVisible,
  ] = React.useState(false);

  const [
    combineQueryMetricColumnSettingExtraData,
    setCombineQueryMetricColumnSettingExtraData,
  ] = React.useState<any>({});

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_CUSTOMIZER_CHANGE,
      (data: any) => {
        setCombineQueryMetricColumnSettingModalVisible(data?.status);
        setCombineQueryMetricColumnSettingExtraData(data?.extra);
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_CUSTOMIZER_CHANGE,
      );
    };
  }, []);

  return (
    <div
      id={'combine-query-metric-column-setting-container'}
      className={'combine-query-metric-column-setting-container'}
    >
      {props?.nextGeneration === false && (
        <Tooltip title={'统计指标设置'}>
          <Button
            key="table_column_setting"
            onClick={() => {
              setCombineQueryMetricColumnSettingModalVisible(
                !combineQueryMetricColumnSettingModalVisible,
              );
            }}
          >
            统计指标设置
          </Button>
        </Tooltip>
      )}

      <Modal
        title={`统计指标设置`}
        width={1200}
        open={combineQueryMetricColumnSettingModalVisible}
        className={'metric-columns-setting-container'}
        destroyOnClose={true}
        closable={true}
        onCancel={() => {
          setCombineQueryMetricColumnSettingModalVisible(false);
          setCombineQueryMetricColumnSettingPreviewModalVisible(false);
        }}
        footer={[
          <div
            className={'flex-row-center'}
            style={{ justifyContent: 'flex-end' }}
          >
            <Button
              style={{ display: 'none' }}
              key="preview"
              onClick={() => {
                setCombineQueryMetricColumnSettingPreviewModalVisible(true);
              }}
            >
              <SearchOutlined />
              结果总览
            </Button>

            <div className={'flex-row-center'}>
              <Button
                key="back"
                onClick={() => {
                  setCombineQueryMetricColumnSettingModalVisible(false);
                  setCombineQueryMetricColumnSettingPreviewModalVisible(false);
                }}
              >
                取消
              </Button>
              <Button
                key="submit"
                type="primary"
                onClick={() => {
                  console.log('selecedItems', form.getFieldsValue(true));

                  // TODO 发送事件丢出去
                  let columns = metricColumnsSelectCompleteProcessor(
                    form.getFieldsValue(true),
                    TabKeys,
                  );

                  let metricsState = metricStateSelectCompleteProcessor(
                    form.getFieldValue('metricsState'),
                    columns,
                  );

                  Emitter.emit(
                    StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_COMPLETE,
                    {
                      columns: columns,
                      metricsState: metricsState,
                    },
                  );
                  setCombineQueryMetricColumnSettingModalVisible(false);

                  combineQueryMetricColumnSettingExtraData?.onOkExtra &&
                    combineQueryMetricColumnSettingExtraData?.onOkExtra();
                }}
              >
                确定
              </Button>
            </div>
          </div>,
        ]}
        onOk={() => {}}
        getContainer={() =>
          document.getElementById(
            // 'combine-query-metric-column-setting-container',
            'combo-table-ng-container',
          )
        }
      >
        <Form form={form} preserve={false}>
          {Object.keys(TabKeys).map((key) => {
            return <Form.Item hidden={true} name={key} />;
          })}

          <Form.Item hidden={true} name={'metricsState'} />

          <MetricColumnSettingContent {...props} />
        </Form>

        <Modal
          title={`结果总览`}
          zIndex={1001}
          width={1200}
          open={combineQueryMetricColumnSettingPreviewModalVisible}
          destroyOnClose={true}
          closable={true}
          onCancel={() => {
            setCombineQueryMetricColumnSettingPreviewModalVisible(false);
          }}
          footer={[
            <Button
              key="know"
              type={'primary'}
              onClick={() => {
                setCombineQueryMetricColumnSettingPreviewModalVisible(false);
              }}
            >
              知道了
            </Button>,
          ]}
        >
          <MetricPreviewTable
            {...props}
            metricsState={form.getFieldValue('metricsState')}
          />
        </Modal>
      </Modal>
    </div>
  );
};

const MetricColumnSettingContent = (
  props: CombineQueryMetricColumnSettingsProps,
) => {
  const activeColor = '#1464f8';

  const mainFormInstance = Form.useFormInstance();

  const [tabKey, setTabKey] = useState<string>(TabKeys.BASIC);

  const [selectedMetricsState, setSelectedMetricsState] = useState<{
    [name: string]: SelectedMetricItem[];
  }>(
    cloneDeep(props?.metricsState) ?? {
      aggregate: [],
      metric: [],
      group: [],
    },
  );

  const { globalState } = useModel('@@qiankunStateFromMaster');

  useEffect(() => {
    console.log('before set', selectedMetricsState);
    mainFormInstance.setFieldValue('metricsState', selectedMetricsState);

    setTimeout(() => {
      Emitter.emit(
        `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT}#${tabKey}`,
        tabKey === TabKeys.BASIC
          ? [
              ...rightTableColumnsProcessor(
                TabKeys.BASIC,
                selectedMetricsState,
                'metric',
                props?.aggregateItems,
                props?.groupItems,
                props?.metricItems,
              ),
              ...rightTableColumnsProcessor(
                TabKeys.BASIC,
                selectedMetricsState,
                'aggregate',
                props?.aggregateItems,
                props?.groupItems,
                props?.metricItems,
              ),
            ]
          : rightTableColumnsProcessor(
              TabKeys.STATISTICS,
              selectedMetricsState,
              'group',
              props?.aggregateItems,
              props?.groupItems,
              props?.metricItems,
              globalState,
            ),
      );
    }, 0);

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
      (items) => {
        items?.forEach((data) => {
          let currentItem: any = selectedMetricsState?.[data.type];
          if (currentItem) {
            if (data?.checked) {
              let selectedItem = {
                id: data?.id,
              };
              if (data?.customTitle) {
                selectedItem['customTitle'] = data?.customTitle;
              }
              if (data?.radio) {
                let actualId = data?.id?.split('#')?.[0];
                let otherRadioItemIndex = currentItem?.findIndex(
                  (item) => item?.id?.indexOf(actualId) > -1,
                );
                if (otherRadioItemIndex > -1) {
                  // 替换
                  currentItem[otherRadioItemIndex] = selectedItem;
                } else {
                  currentItem.push(selectedItem);
                }
              } else {
                currentItem.push(selectedItem);
              }
            } else {
              currentItem = currentItem?.filter(
                (item) => item?.id !== data?.id,
              );
            }
          }

          selectedMetricsState[data.type] = currentItem;
        });

        setSelectedMetricsState(Object.assign({}, selectedMetricsState));
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
      );
    };
  }, [selectedMetricsState, tabKey]);

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_NEED_RESET,
      (tabKey) => {
        if (tabKey === TabKeys.STATISTICS) {
          setSelectedMetricsState({
            ...selectedMetricsState,
            group: props?.metricsState?.['group'],
          });
        }

        if (tabKey === TabKeys.BASIC) {
          setSelectedMetricsState({
            ...props?.metricsState,
            group: selectedMetricsState?.['group'],
          });
        }

        Emitter.emit(
          `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_RESET}#${tabKey}`,
        );
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_NEED_RESET,
      );
    };
  }, []);

  // let statisticTreeData = [
  //   {
  //     children: props?.groupItems,
  //     directory: true,
  //     key: "GROUP",
  //     title: "统计指标",
  //   },
  // ];

  let statisticTreeData = metricGroupTreeProcessor(props?.groupItems);

  let basicTreeData = [];

  const processedMetricItems = metricsItemsProcessor(props?.metricItems);
  if (processedMetricItems?.length > 0) {
    basicTreeData.push({
      children: metricsItemsProcessor(props?.metricItems),
      directory: true,
      itemKey: 'METRIC',
      title: '常用指标',
      key: '常用指标',
    });
  }

  if (props?.aggregateItems?.length > 0) {
    basicTreeData.push({
      children: props?.aggregateItems,
      directory: true,
      itemKey: 'AGGREGATE',
      title: '统计指标',
      key: '统计指标',
    });
  }

  return (
    <Tabs
      className={'metric-columns-tab-container'}
      onChange={(activeKey) => {
        setTabKey(activeKey);
      }}
      destroyInactiveTabPane={false}
    >
      <Tabs.TabPane
        tab={
          <div className={'flex-row-center'}>
            <Flag
              className={'icon'}
              theme="outline"
              size="18"
              fill={`${
                tabKey === TabKeys.BASIC ? activeColor : 'rgba(0, 0, 0, 0.85)'
              }`}
            />
            <span>基本指标</span>
            <Badge
              count={
                (selectedMetricsState?.['aggregate']?.length ?? 0) +
                (selectedMetricsState?.['metric']?.length ?? 0)
              }
              size={'default'}
              style={{
                backgroundColor: '#52C41A',
                marginLeft: 5,
              }}
            />
          </div>
        }
        key={TabKeys.BASIC}
      >
        <div className={'metric-columns-setting-info-container'}>
          <MetricColumnsSettingTree
            tabKey={TabKeys.BASIC}
            treeData={basicTreeData}
            metricsState={selectedMetricsState}
          />
          <div className={'metric-columns-separator'} />

          <MetricSelectedColumnTable tabKey={TabKeys.BASIC} />
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane
        tab={
          <div className={'flex-row-center'}>
            <Diamond
              className={'icon'}
              theme="outline"
              size="18"
              fill={`${
                tabKey === TabKeys.STATISTICS
                  ? activeColor
                  : 'rgba(0, 0, 0, 0.85)'
              }`}
            />

            <span>统计维度</span>
            <Badge
              count={selectedMetricsState?.['group']?.length ?? 0}
              size={'default'}
              style={{
                backgroundColor: '#52C41A',
                marginLeft: 5,
              }}
            />
          </div>
        }
        key={TabKeys.STATISTICS}
      >
        <div className={'metric-columns-setting-info-container'}>
          <MetricColumnsSettingTree
            tabKey={TabKeys.STATISTICS}
            treeData={statisticTreeData}
            metricsState={selectedMetricsState}
          />
          <div className={'metric-columns-separator'} />

          <MetricSelectedColumnTable tabKey={TabKeys.STATISTICS} />
        </div>
      </Tabs.TabPane>
    </Tabs>
  );
};

interface MetricColumnsSettingTreeProps {
  tabKey?: string;
  treeData?: any[];
  metricsState?: any;
}

export const MetricColumnsSettingTree = (
  props: MetricColumnsSettingTreeProps,
) => {
  const [keyword, setKeyword] = useState('');

  const [basicSelectedAnchor, setBasicSelectedAnchor] = useState<string>('');

  const [statisticsSelectedAnchor, setStatisticsSelectedAnchor] =
    useState<string>('');

  useEffect(() => {
    setAnchorKey(props?.treeData[0]?.key, true);
  }, [props?.treeData]);

  const setAnchorKey = (key, initial = false) => {
    if (props?.tabKey === TabKeys.BASIC) {
      if (initial) {
        if (isEmptyValues(basicSelectedAnchor)) {
          setBasicSelectedAnchor(key);
        }
      } else {
        setBasicSelectedAnchor(key);
      }
    }

    if (props?.tabKey === TabKeys.STATISTICS) {
      if (initial) {
        if (isEmptyValues(statisticsSelectedAnchor)) {
          setStatisticsSelectedAnchor(key);
        }
      } else {
        setStatisticsSelectedAnchor(key);
      }
    }
  };

  useEffect(() => {
    // TODO 转换成table数据
  }, [props?.metricsState, props?.treeData]);

  const elementIsVisibleInViewport = () => {
    props?.treeData
      ?.map((item) => {
        return document.getElementById(item?.key);
      })
      ?.filter((item) => item)
      ?.forEach((el) => {
        const elementRect = el.getBoundingClientRect();
        const containerRect = document
          .getElementById(`metric-tree-container-${props?.tabKey}`)
          .getBoundingClientRect();
        let visible =
          containerRect.top - (elementRect.top - 10) > 0 &&
          containerRect.top - elementRect.top <= elementRect.height;

        if (visible) {
          setAnchorKey(el.id);
        }
      });
  };

  return (
    <div className={'metric-columns-tree-container'}>
      <div className={'flex-row-center'} style={{ margin: '0px 20px' }}>
        <Search
          value={keyword}
          className={'metric-columns-search'}
          style={{ width: '100%' }}
          placeholder="请输入关键字"
          onChange={(event) => {
            setKeyword(event.target.value);
          }}
        />
        <Button
          className={'reset'}
          ghost={true}
          onClick={() => {
            // TODO 重置
            Emitter.emit(
              StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_NEED_RESET,
              props?.tabKey,
            );
          }}
        >
          重置
        </Button>
      </div>

      <div className={'metric-columns-tree-content'}>
        <div className="toc-affix">
          <ul id="demo-toc" className="toc">
            {props?.treeData?.map((item) => {
              return (
                <li key={item?.key} title={item?.title}>
                  <a
                    style={{ minWidth: 70 }}
                    className={
                      item?.key === basicSelectedAnchor ||
                      item.key === statisticsSelectedAnchor
                        ? 'selected'
                        : ''
                    }
                    onClick={() => {
                      setAnchorKey(item?.key);
                      document.getElementById(item?.key)?.scrollIntoView({
                        behavior: 'smooth',
                      });
                    }}
                  >
                    {item?.title}
                  </a>
                </li>
              );
            })}
          </ul>
        </div>

        <div
          id={`metric-tree-container-${props?.tabKey}`}
          className={'metric-tree-container'}
          onScroll={(event) => {
            let scrollToBottom =
              document
                .getElementById(`metric-tree-container-${props?.tabKey}`)
                .getBoundingClientRect()?.height +
                document.getElementById(
                  `metric-tree-container-${props?.tabKey}`,
                )?.scrollTop ===
              document.getElementById(`metric-tree-container-${props?.tabKey}`)
                ?.scrollHeight;
            if (scrollToBottom) {
              setAnchorKey(
                props?.treeData?.at(props?.treeData?.length - 1)?.key,
              );
              return;
            }
            elementIsVisibleInViewport();
          }}
        >
          {cloneDeep(props?.treeData)?.map((treeItem) => {
            return (
              <TreeItem
                key={treeItem?.key}
                itemKey={treeItem?.itemKey}
                title={treeItem?.title}
                items={treeItem?.children ?? []}
                keyword={keyword}
                metricsState={props?.metricsState}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

interface TreeItemProps {
  itemKey: string;
  title: string;
  items: any[];
  keyword?: string;
  metricsState: any;
}

interface TreeItemProcessItem {
  selected: (items, metricsState) => number;
  total: (items) => number;
  filterItems: (items, keyword) => any[];
}

const treeItemTypeProcessor: { [key: string]: TreeItemProcessItem } = {
  METRIC: {
    selected: (items, metricsState) => {
      let selected = metricsState?.['metric'];
      if (selected && selected?.length > 0) {
        let total = 0;
        let selectedKeys = selected?.map((item) => item?.id);
        items?.forEach((outerItem) => {
          outerItem?.items?.forEach((item) => {
            if (
              selectedKeys?.find((selectedKey) => selectedKey === item?.id) !==
              undefined
            ) {
              total += 1;
            }
          });
        });

        return total;
      } else {
        return 0;
      }
    },
    total: (items) => {
      let total = 0;
      items?.forEach((item) => {
        total += item?.items?.length ?? 0;
      });

      return total;
    },
    filterItems: (items, keyword) => {
      let treeItems = [];
      let originItems = Object.assign([], items);
      originItems?.forEach((metricItem) => {
        let filteredItems = metricItem?.items?.filter((item) =>
          keyword
            ? item?.title?.[searchFunctionGetter(false)](keyword) ||
              pinyinInitialSearch(item?.title, keyword)
            : true,
        );
        if (filteredItems?.length > 0) {
          metricItem['items'] = filteredItems;
          treeItems.push(metricItem);
        }
      });

      return treeItems;
    },
  },
  AGGREGATE: {
    selected: (items, metricsState) => {
      console.log('selected', items, metricsState);
      return 0;
    },
    total: (items) => items?.length,
    filterItems: (items, keyword) => {
      return items?.filter((item) =>
        keyword
          ? item?.title?.[searchFunctionGetter(false)](keyword) ||
            pinyinInitialSearch(item?.title, keyword)
          : true,
      );
    },
  },
  GROUP: {
    selected: (items, metricsState) => {
      let selected = metricsState?.['group'];
      if (selected && selected?.length > 0) {
        let total = 0;
        let selectedKeys = selected?.map((item) => item?.id);
        items?.forEach((item) => {
          if (
            selectedKeys?.find(
              (selectedKey) => selectedKey?.indexOf(item?.id) > -1,
            ) !== undefined
          ) {
            total += 1;
          }
        });

        return total;
      } else {
        return 0;
      }
    },
    total: (items) => items?.length,
    filterItems: (items, keyword) => {
      return items?.filter((item) =>
        keyword
          ? item?.title?.[searchFunctionGetter(false)](keyword) ||
            pinyinInitialSearch(item?.title, keyword)
          : true,
      );
    },
  },
};

export const TreeItem = (props: TreeItemProps) => {
  const filteredItems = treeItemTypeProcessor[props?.itemKey]?.filterItems(
    props?.items,
    props?.keyword,
  );

  return (
    <>
      {filteredItems?.length > 0 && (
        <div
          id={props?.title ?? props?.itemKey}
          className={'tree-item-container'}
        >
          <div className={'tree-header-container'}>
            <div className={'label-container'}>
              <span className={'title'}>{props?.title}</span>
              <span style={{ fontSize: 12, marginLeft: 3, marginBottom: 2 }}>
                <span className={'selected-number'}>
                  {treeItemTypeProcessor[props?.itemKey]?.selected(
                    props?.items,
                    props?.metricsState,
                  )}
                </span>
                /{treeItemTypeProcessor[props?.itemKey]?.total(filteredItems)}
              </span>
            </div>
          </div>

          <div className={'items-container'}>
            {props?.itemKey === 'GROUP' && (
              <>
                {filteredItems?.filter((item) => {
                  return item?.allowedGroupOptions?.length === 0;
                })?.length > 0 && (
                  <div className={'group-items-container'}>
                    {filteredItems
                      ?.filter((item) => {
                        return item?.allowedGroupOptions?.length === 0;
                      })
                      ?.map((tagItem) => {
                        return (
                          <>
                            <GroupItem
                              key={tagItem?.key}
                              type={'group'}
                              name={tagItem?.name}
                              title={tagItem?.title || tagItem?.name}
                              title2={tagItem?.title2}
                              itemId={tagItem?.id}
                              item={tagItem}
                              selectedItem={props?.metricsState?.['group']}
                            />
                          </>
                        );
                      })}
                  </div>
                )}
                {filteredItems?.filter((item) => {
                  return item?.allowedGroupOptions?.length > 0;
                })?.length > 0 && (
                  <div className={'group-periods-container'}>
                    {filteredItems
                      ?.filter((item) => {
                        return item?.allowedGroupOptions?.length > 0;
                      })
                      ?.map((tagItem) => {
                        return (
                          <>
                            <GroupItemPeriod
                              key={tagItem?.key}
                              type={'group'}
                              name={tagItem?.name}
                              title={tagItem?.title || tagItem?.name}
                              title2={tagItem?.title2}
                              itemId={tagItem?.id}
                              item={tagItem}
                              selectedItem={props?.metricsState?.['group']}
                            />
                          </>
                        );
                      })}
                  </div>
                )}
              </>
            )}
            {(props?.itemKey === 'METRIC' || props?.itemKey === 'AGGREGATE') &&
              filteredItems?.map((tagItem) => {
                return (
                  <>
                    {props?.itemKey === 'METRIC' && (
                      <MetricItem
                        key={tagItem?.key}
                        label={tagItem?.label}
                        items={tagItem?.items}
                        type={'metric'}
                        selectedItem={props?.metricsState?.['metric']}
                      />
                    )}
                    {props?.itemKey === 'AGGREGATE' && (
                      <AggregateItem
                        key={tagItem?.key}
                        type={'aggregate'}
                        name={tagItem?.name}
                        title={tagItem?.title || tagItem?.name}
                        title2={tagItem?.title2}
                        itemId={tagItem?.id}
                        item={tagItem}
                        selectedItem={props?.metricsState?.['aggregate']}
                      />
                    )}
                  </>
                );
              })}
          </div>
        </div>
      )}
    </>
  );
};

interface TagItemProps {
  [key: string]: any;

  type?: 'aggregate' | 'group' | 'metric';
  name?: string;
  customTitle?: string;
  title?: string;
  title2?: string;
  itemId?: string;
  item?: any;

  // 预设指标
  items?: any[];
  label?: string;

  selectedItem: SelectedMetricItem[];
}

export const GroupItem = (props: TagItemProps) => {
  let periodValue = '';

  return (
    <TagItem
      tagItem={props}
      checked={
        !!props?.selectedItem?.find(
          (item) => item?.id === `${props?.itemId}#Default`,
        )
      }
      onItemClicked={(tagItem, checked) => {
        Emitter.emit(
          StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
          [
            {
              type: props?.type,
              checked: checked,
              id: `${props?.itemId}#Default`,
              tabKey: TabKeys.STATISTICS,
              item: {
                ...props?.item,

                customTitle: props?.item?.customTitle,

                // tagTitle: `${props?.item.title}-${
                //   GROUP_DATE?.find((item) => item.value === periodValue)?.name
                // }`,
                tagTitle: props?.item?.title,
                itemType: props?.type,
                itemKey: `${props?.itemId}#Default`,
              },
            },
          ],
        );
      }}
    />
  );
};

export const GroupItemPeriod = (props: TagItemProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const anaGroupOptions = globalState?.dictData?.['AnaGroupOption'];

  return (
    <div className={'group-period-item-container'}>
      <span className={'group-period-item-header'}>{props?.title}</span>
      <div className={'group-period-items-container'}>
        {props?.item?.allowedGroupOptions?.map((tagKey) => {
          let groupPeriodItem = anaGroupOptions?.find(
            (item) => item?.Code === tagKey,
          );
          let groupPeriodItemName = groupPeriodItem?.Name;
          let tagItem = {
            title: groupPeriodItemName,
          };
          return (
            <TagItem
              tagItem={tagItem}
              checked={
                !!props?.selectedItem?.find(
                  (item) => item?.id === `${props?.itemId}#${tagKey}`,
                )
              }
              onItemClicked={(tagItem, checked) => {
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
                  [
                    {
                      type: props?.type,
                      checked: checked,
                      id: `${props?.itemId}#${tagKey}`,
                      tabKey: TabKeys.STATISTICS,
                      radio: true,
                      item: {
                        ...props?.item,

                        title: `${props?.item?.title}-${groupPeriodItemName}`,
                        customTitle: props?.item?.customTitle,

                        // tagTitle: `${props?.item.title}-${
                        //   GROUP_DATE?.find((item) => item.value === periodValue)?.name
                        // }`,
                        tagTitle: props?.item?.title,
                        itemType: props?.type,
                        itemKey: `${props?.itemId}#${tagKey}`,
                      },
                    },
                  ],
                );
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export const MetricItem = (props: TagItemProps) => {
  return (
    <div className={'metric-item-container'}>
      <span className={'metric-item-header'}>{props?.label}</span>
      <div className={'metric-items-container'}>
        {props?.items?.map((tagItem) => {
          return (
            <TagItem
              tagItem={tagItem}
              checked={props?.selectedItem?.find(
                (item) => item?.id === tagItem?.id,
              )}
              onItemClicked={(tagItem, checked) => {
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
                  [
                    {
                      type: props?.type,
                      checked: checked,
                      id: tagItem?.id,
                      tabKey: TabKeys.BASIC,
                      item: {
                        ...tagItem,

                        tagTitle: tagItem?.title,
                        itemType: props?.type,
                        itemKey: tagItem?.id,
                      },
                    },
                  ],
                );
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export const AggregateItem = (props: TagItemProps) => {
  return (
    <div className={'aggregate-item-container'}>
      <span className={'aggregate-item-header'}>{props?.title}</span>
      <div className={'aggregate-items-container'}>
        {props?.item?.allowedAggregations?.map((tagKey) => {
          let aggregateItemName = AggregationNames[tagKey];
          let aggregateItem = props?.item?.aggregationNames[tagKey];
          let tagItem = {
            title: aggregateItemName,
          };
          return (
            <TagItem
              tagItem={tagItem}
              checked={
                !!props?.selectedItem?.find(
                  (item) => item?.id === `${props?.itemId}#${tagKey}`,
                )
              }
              onItemClicked={(tagItem, checked) => {
                Emitter.emit(
                  StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
                  [
                    {
                      type: props?.type,
                      checked: checked,
                      id: `${props?.itemId}#${tagKey}`,
                      tabKey: TabKeys.BASIC,
                      item: {
                        ...props?.item,
                        ...aggregateItem,

                        customTitle: aggregateItem?.customTitle,

                        tagTitle: aggregateItem?.title,
                        itemType: props?.type,
                        itemKey: `${props?.itemId}#${tagKey}`,
                      },
                    },
                  ],
                );
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export const TagItem = (props: any) => {
  return (
    <CheckableTag
      style={tagBaseStyle}
      className={props?.checked ? 'tag-item-checked' : 'tag-item'}
      key={props?.tagItem?.name}
      checked={props?.checked}
      onChange={(checked) => {
        props.onItemClicked(props?.tagItem, checked);
      }}
    >
      <div
        className={'flex-row-center'}
        style={{
          padding: props?.checked ? 0 : '0 6px',
        }}
      >
        {props?.checked && <CheckOutlined className={'tick'} />}

        {props?.tagItem?.title}
      </div>
    </CheckableTag>
  );
};

interface MetricSelectedColumnTableProps {
  tabKey: string;
}

const MetricSelectedColumnTable = (props: MetricSelectedColumnTableProps) => {
  const [form] = Form.useForm();

  const mainFormInstance = Form.useFormInstance();

  const [selectedItems, setSelectedItems] = useState([]);

  const [selectedItemsOrders, setSelectedItemsOrders] = useState<string[]>([]);

  useEffect(() => {
    form.resetFields();
    Emitter.on(
      `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_RESET}#${props?.tabKey}`,
      () => {
        form.resetFields();
      },
    );

    return () => {
      Emitter.offMultiple([
        `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_RESET}#${props?.tabKey}`,
      ]);
    };
  }, []);

  useEffect(() => {
    mainFormInstance.setFieldValue(props?.tabKey, selectedItems);
  }, [selectedItems]);

  useEffect(() => {
    Emitter.on(
      `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT}#${props?.tabKey}`,
      (selectedItems) => {
        let items = cloneDeep(selectedItems)?.map((selectItem, index) => {
          let itemOrder = selectedItemsOrders?.findIndex(
            (item) => item === selectItem?.name,
          );
          if (itemOrder === -1) {
            selectItem['order'] = index;
          } else {
            selectItem['order'] = itemOrder;
          }
          return selectItem;
        });

        let orderedItems = items
          ?.slice()
          ?.sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
          ?.slice();

        setSelectedItems(orderedItems);
        setSelectedItemsOrders(orderedItems?.map((item) => item?.name));
      },
    );

    return () => {
      Emitter.offMultiple([
        `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT}#${props?.tabKey}`,
      ]);
    };
  }, [selectedItemsOrders]);

  useEffect(() => {
    Emitter.on(
      `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_TOP}#${props?.tabKey}`,
      (data) => {
        let currentItem = selectedItems[data?.index];
        if (currentItem) {
          if (currentItem) {
            selectedItems?.splice(data?.index, 1);
            selectedItemsOrders?.splice(data?.index, 1);

            setSelectedItems([currentItem, ...selectedItems]);
            setSelectedItemsOrders([currentItem?.name, ...selectedItemsOrders]);
          }
        }
      },
    );

    Emitter.on(
      `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_BOTTOM}#${props?.tabKey}`,
      (data) => {
        let currentItem = selectedItems[data?.index];
        if (currentItem) {
          selectedItems?.splice(data?.index, 1);
          selectedItemsOrders?.splice(data?.index, 1);

          setSelectedItems([...selectedItems, currentItem]);
          setSelectedItemsOrders([...selectedItemsOrders, currentItem?.name]);
        }
      },
    );

    Emitter.on(
      `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_DELETE}#${props?.tabKey}`,
      (data) => {
        let currentItem = selectedItems[data?.index];
        // if (currentItem) {
        //   selectedItems?.splice(data?.index, 1);
        //   setSelectedItems([...selectedItems]);
        // }
        let itemId = '';
        if (currentItem?.itemType === 'metric') {
          itemId = currentItem?.id;
        }

        if (currentItem?.itemType === 'aggregate') {
          itemId = `${currentItem?.id}#${currentItem?.aggregationOption}`;
        }

        if (currentItem?.itemType === 'group') {
          itemId = `${currentItem?.id}#${currentItem?.groupOption}`;
        }

        if (itemId) {
          Emitter.emit(
            StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS_SELECT,
            [
              {
                type: currentItem?.itemType,
                checked: false,
                id: itemId,
                tabKey: currentItem?.tabKey,
              },
            ],
          );
        }
      },
    );

    return () => {
      Emitter.offMultiple([
        `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_TOP}#${props?.tabKey}`,
        `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_BOTTOM}#${props?.tabKey}`,
        `${StatsAnalysisEventConstant.STATS_ANALYSIS_METRIC_COLUMN_SELECT_DELETE}#${props?.tabKey}`,
      ]);
    };
  }, [selectedItems, selectedItemsOrders]);

  return (
    <div className={'selected-table-container'}>
      <UniDragEditTable
        {...props}
        bordered={false}
        form={form}
        key={'column-setting-selected-table'}
        id={`${props?.tabKey}-column-setting-selected-table`}
        tableId={`${props?.tabKey}-column-setting-selected-table`}
        scroll={{
          y: 400,
        }}
        pagination={false}
        className={`table-container`}
        // dataSource={props?.dataSources}
        dataSource={selectedItems}
        rowKey={'itemKey'}
        onTableDataSourceOrderChange={(tableData, oldIndex, newIndex) => {
          // 不是交换 而是把order重排 TODO: 如果用重复的order还要处理下？
          let formInstance = mainFormInstance.getFieldValue('selectedItems');

          let currentItems = tableData?.map((d, i) => ({
            ...d,
            order: formInstance?.at(i).order,
            customTitle:
              formInstance?.find((v) => v.id === d.id)?.customTitle ??
              undefined, // 这里要用外部的customTitle替换
          }));
          // 交换oldIndex 和 newIndex 的order
          // let currentItems = mainFormInstance.getFieldValue(props?.tabKey);
          // let tempOldOrder = currentItems[oldIndex].order;
          // let tempNewOrder = currentItems[newIndex].order;

          // currentItems[oldIndex].order = tempNewOrder;
          // currentItems[newIndex].order = tempOldOrder;

          // const newData = arrayMoveImmutable(
          //   [...currentItems],
          //   oldIndex,
          //   newIndex,
          // ).filter((el) => !!el);

          setSelectedItems(currentItems);
          setSelectedItemsOrders(currentItems?.map((item) => item?.name));
        }}
        onValuesChange={(recordList) => {
          mainFormInstance.setFieldValue(props?.tabKey, recordList);
        }}
        columns={metricColumnSettingColumns}
      />
    </div>
  );
};
export default CombineQueryMetricColumnSettings;
