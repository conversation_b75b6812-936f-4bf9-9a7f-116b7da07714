import { FC } from 'react';
import { ManagementColumns } from './columns';
import { OutPatientDynamicManagementConstants } from './constants';
import EditByDept from '../outPatientDynamicRegistration/editByDept/index';
import DynamicManagement from '../../components/DynamicManagement';

const OutPatientManagement: FC = () => {
  return (
    <DynamicManagement
      pageTitle="门急动态管理"
      apiBasePath="DeptOutpatientAmtMgmt"
      detailBtnEventKey={OutPatientDynamicManagementConstants.DETAIL_BTN_CLK}
      columns={ManagementColumns}
      id="odm_out_patient_dynamic_management"
      EditByDeptComponent={EditByDept}
    />
  );
};

export default OutPatientManagement;
