import { Col, Row, Tabs } from 'antd';
import { useState } from 'react';
import { useModel } from 'umi';
import _ from 'lodash';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '../../components/drawerCardInfo';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import OperChangeTrend from '../components/operChangeTrend/index';
import DaySurgeryChangeTrend from '../components/daySurgeryChangeTrend/index';
import { OperTotalStatsColumns, TabCommonItems } from '../constants';
import Stats from '../../components/statsWithTrend';
import SingleColumnTable from '../../components/singleColumnTable/index';
import { useDeepCompareEffect } from 'ahooks';
import './index.less';

const HqmsOper = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, insurType } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);

  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
    };
    setTableParams(params);
  }, [dateRange, hospCodes]);

  // tabs
  let tabItems = [
    {
      key: TabCommonItems.statistic.key,
      label: TabCommonItems.statistic.title,
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={24} xl={24}>
            <Stats
              // api={`Api/Hqms/HospHqmsOper/BundledHqmsOper`}
              api={`API/v2/Hqms/HqmsStats/HqmsOperOfHosp`}
              // trendApi={`Api/Hqms/HospHqmsOper/HqmsOperTrend`}
              trendApi={`API/v2/Hqms/HqmsStats/HqmsOperTrend`}
              columns={OperTotalStatsColumns}
              type="col-xl-8"
              tabKey={activeKey}
              chartHeight={320}
              useGlobalState
            />
          </Col>
          <SingleColumnTable
            title="全院手术分布"
            args={{
              // api: 'Api/Hqms/HospHqmsOper/HqmsOperByHosp',
              api: 'API/v2/Hqms/HqmsStats/HqmsOperByHosp',
            }}
            tableParams={tableParams}
            dictData={dictData}
            type="table"
            category="HospName"
            visibleValueKeys={[
              'HospName',
              'OperPatCnt',
              'TotalCnt',
              'OperPatRatio',
            ]}
            colSpan={{
              xs: 24,
              sm: 24,
              md: 24,
              lg: 24,
              xl: 24,
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.HospName + '手术人数',
                args: {
                  ...tableParams,
                  HospCode: record?.HospCode ? [record?.HospCode] : [],
                  IsValidOper: true,
                },
                detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
                dictData: dictData,
              });
            }}
          />
          <OperChangeTrend
            tableParams={tableParams}
            // api="Api/Hqms/HospHqmsOper/HqmsOperTrend"
            api="API/v2/Hqms/HqmsStats/HqmsOperTrend"
          />
          <DaySurgeryChangeTrend
            tableParams={tableParams}
            // api="Api/Hqms/HospHqmsOper/HqmsOperTrend"
            api="API/v2/Hqms/HqmsStats/HqmsOperTrend"
          />
        </Row>
      ),
    },
    {
      key: TabCommonItems.majorPerfDeptAnalysis.key,
      label: TabCommonItems.majorPerfDeptAnalysis.title,
      children: (
        <>
          <Row>
            <SingleColumnTable
              title="科室手术分布"
              args={{
                // api: 'Api/Hqms/CliDeptHqmsOper/HqmsOperByCliDept',
                api: 'API/v2/Hqms/HqmsStats/HqmsOperByCliDept',
              }}
              tableParams={tableParams}
              dictData={dictData}
              category="CliDeptName"
              type="table"
              orderKey="OperPatCnt"
              visibleValueKeys={[
                'CliDeptName',
                'TotalCnt',
                'OperPatCnt',
                'OperPatRatio',
              ]}
              colSpan={{ span: 24 }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.CliDeptName + '手术人数',
                  args: {
                    ...tableParams,
                    CliDepts: record?.CliDept ? [record?.CliDept] : [],
                    IsValidOper: true,
                  },
                  detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
                  dictData: dictData,
                });
              }}
            />
          </Row>
        </>
      ),
    },
    {
      key: TabCommonItems.medTeamAnalysis.key,
      label: TabCommonItems.medTeamAnalysis.title,
      children: (
        <>
          <Row>
            <SingleColumnTable
              title="医疗组手术分布"
              args={{
                // api: 'Api/Hqms/MedTeamHqmsOper/HqmsOperByMedTeam',
                api: 'API/v2/Hqms/HqmsStats/HqmsOperByMedTeam',
              }}
              tableParams={tableParams}
              dictData={dictData}
              category="MedTeamName"
              type="table"
              orderKey="OperPatCnt"
              visibleValueKeys={[
                'MedTeamName',
                'TotalCnt',
                'OperPatCnt',
                'OperPatRatio',
              ]}
              colSpan={{ span: 24 }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.MedTeamName + '手术人数',
                  args: {
                    ...tableParams,
                    MedTeams: record?.MedTeam ? [record?.MedTeam] : [],
                    IsValidOper: true,
                  },
                  detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
                  dictData: dictData,
                });
              }}
            />
          </Row>
        </>
      ),
    },
    {
      key: TabCommonItems.doctorAnalysis.key,
      label: TabCommonItems.doctorAnalysis.title,
      children: (
        <>
          <Row>
            <SingleColumnTable
              title="医生手术分布"
              args={{
                // api: 'Api/Hqms/SurgeonHqmsOper/HqmsOperBySurgeon',
                api: 'API/v2/Hqms/HqmsStats/HqmsOperBySurgeon',
              }}
              tableParams={tableParams}
              dictData={dictData}
              category="SurgeonName"
              type="table"
              orderKey="OperPatCnt"
              visibleValueKeys={[
                'SurgeonName',
                'TotalCnt',
                'OperPatCnt',
                'OperPatRatio',
              ]}
              colSpan={{ span: 24 }}
              select={{
                dataKey: 'SurgeonType',
                valueKey: 'GroupByDoctor',
                allowClear: false,
                defaultSelect: true,
              }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.SurgeonName + '手术人数',
                  args: {
                    ...tableParams,
                    SurgeonCodes: record?.SurgeonCode
                      ? [record?.SurgeonCode]
                      : [],
                    SurgeonType: record?.SurgeonType,
                    IsValidOper: true,
                  },
                  detailsUrl: 'Hqms/Details/HqmsCompositeDetails',
                  dictData: dictData,
                });
              }}
            />
          </Row>
        </>
      ),
    },
  ];
  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      {/* 把modal与drawer的位置提高到这里，统一在这里处理 避免id太多导致匹配混乱 & tab切换后id失效 */}
      {/* TODO DetailTableModal内部的实现方式是destoryOnClose 导致每次打开都要重新获取columns */}
      <DetailTableModal
        dictData={globalState.dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      {/* drawer 同理 这样保持复用 因为drawer 监听的是detailTableModal 或者一些table */}
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default HqmsOper;
