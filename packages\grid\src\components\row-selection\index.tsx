import { FormTableItemBaseProps } from "@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable";
import { Checkbox, Form } from "antd";
import { Emitter } from '@uni/utils/src/emitter';

interface IcdeOperCheckboxProps extends FormTableItemBaseProps {
  id: string;
  recordId: string;
  dataIndex: string;
  onChangeExtra?: (checked: boolean) => void;
  disabled?: boolean;
  dependencyKey?: string;
  dependencyValue?: any;
  form?: any;

  minimumChecked?: number;
}

export const RowSelectionCheckbox = (props: IcdeOperCheckboxProps) => {
  const dependencyFormValue =
    props?.recordId && props?.dependencyKey && props?.form
      ? Form.useWatch([props?.recordId, props?.dependencyKey], props?.form)
      : null;

  const rowSelectionCheckboxValue = Form.useWatch(
    [props?.recordId, props?.dataIndex],
    props?.form,
  );
  console.log('rowSelectionCheckboxValue', rowSelectionCheckboxValue, props);
//   let reachMinimumChecked = false;
//   if (props?.minimumChecked !== undefined && props?.minimumChecked >= 0) {
//     const IsReportedTrueCount = Form.useWatch(
//       'IsReportedTrueCount',
//       props?.form,
//     );

//     if (IsReportedTrueCount <= props?.minimumChecked) {
//       reachMinimumChecked = true;
//     }
//   }

  return (
    <div
      className={'flex-row-center form-content-item-container'}
      style={{ justifyContent: 'center' }}
    >
      <Checkbox
        id={props?.id}
        checked={rowSelectionCheckboxValue}
        onChange={(event) => {
          props?.onChange && props?.onChange(event?.target?.checked);
          props?.onChangeExtra && props?.onChangeExtra(event?.target?.checked);
          
          // 通知表格更新选择状态，使用动态的 tableId
          setTimeout(() => {
            // 从 id 中提取 tableId，格式通常是 formItem#RowSelection#index#TableName
            const idParts = props?.id?.split('#');
            let tableId = 'diagnosisTable'; // 默认值
            
            // 尝试从不同格式的 id 中提取 tableId
            if (idParts?.length >= 4) {
              const lastPart = idParts[3];
              if (lastPart === 'IcdeTable') {
                tableId = 'diagnosisTable';
              } else {
                tableId = lastPart;
              }
            }
            
            Emitter.emit(`DMR_ROW_SELECTION_ITEM_CHANGE_${tableId}`, {
              recordId: props?.recordId,
              checked: event?.target?.checked
            });
          }, 100);
        }}
      />
    </div>
  );
};