import {
  ArgsType,
  ColumnsType,
  ReportType,
  RelationModeType,
} from './interface';

export enum SelfDefinedReportEventType {
  ReportMasterExists = 'ReportMasterExists',
}

export enum ReqActionType {
  GetReportSettingMasters = 'Sys/ReportSys/GetReportSettingMasters',
  UpsertReportSettingMaster = 'Sys/ReportSys/UpsertReportSettingMaster',
  DeleteReportSettingMaster = 'Sys/ReportSys/DeleteReportSettingMaster',

  GetReportColumnSettings = 'Sys/ReportSys/GetReportColumnSettings', // second step
  UpsertReportColumnSetting = 'Sys/ReportSys/UpsertReportColumnSetting', // second step
  DeleteReportColumnSetting = 'Sys/ReportSys/DeleteReportColumnSetting', // second step
  GenerateReportColumnSettings = 'Sys/ReportSys/GenerateReportColumnSettings', // second step
  SetReportColumnSettingSorts = 'Sys/ReportSys/SetReportColumnSettingSorts', // second step

  GetReportArgSettings = 'Sys/ReportSys/GetReportArgSettings', // third step
  UpsertReportArgSetting = 'Sys/ReportSys/UpsertReportArgSetting', // third step
  DeleteReportArgSetting = 'Sys/ReportSys/DeleteReportArgSetting', // third step
  SetReportArgSettingSorts = 'Sys/ReportSys/SetReportArgSettingSorts', // third step

  GetReportLoadDataSetting = 'Sys/ReportSys/GetReportLoadDataSetting',
  UpsertReportLoadDataSetting = 'Sys/ReportSys/UpsertReportLoadDataSetting',

  GetReportValidateSetting = 'Sys/ReportSys/GetReportValidateSetting',
  UpsertReportValidateSetting = 'Sys/ReportSys/UpsertReportValidateSetting',

  GetReportRetrieveMetadataSetting = 'Sys/ReportSys/GetReportRetrieveMetadataSetting',
  UpsertReportRetrieveMetadataSetting = 'Sys/ReportSys/UpsertReportRetrieveMetadataSetting',

  GetReportPersistSetting = 'Sys/ReportSys/GetReportPersistSetting',
  UpsertReportPersistSetting = 'Sys/ReportSys/UpsertReportPersistSetting',

  GetReportExportSetting = 'Sys/ReportSys/GetReportExportSetting',
  UpsertReportExportSetting = 'Sys/ReportSys/UpsertReportExportSetting',

  GetReportArchiveSetting = 'Sys/ReportSys/GetReportArchiveSetting',
  UpsertReportArchiveSetting = 'Sys/ReportSys/UpsertReportArchiveSetting',

  DeleteReportActionSetting = 'Sys/ReportSys/DeleteReportActionSetting',

  //
  GetReport = 'Sys/ReportSys/GetReport', // edit data
  UpsertReport = 'Sys/ReportSys/UpsertReport', // first step → 同时如果是下钻报表组 也是fourth step
}

// hardcode ArchiveType
export const ArchiveType = [
  {
    value: 'Csv',
    label: 'Csv',
  },
  {
    value: 'Docx',
    label: 'Docx',
  },
  {
    value: 'Udf',
    label: 'Udf',
  },
  {
    value: 'Dbf',
    label: 'Dbf',
  },
  {
    value: 'Xlsx',
    label: 'Xlsx',
  },
];

// hardcode importType
export const ImportType = [
  {
    value: 'None',
    label: '默认模板导入',
  },
  {
    value: 'Python',
    label: '基于UDF导入',
  },
];

// hardcode DictModuleGroup
export const DictModuleGroup = [
  {
    value: 'Dmr',
    label: 'Dmr',
  },
  {
    value: 'Insur',
    label: 'Insur',
  },
  {
    value: 'Hqms',
    label: 'Hqms',
  },
  {
    value: 'Wt',
    label: 'Wt',
  },
];

// hardcode ColumnCustomType
export const ColumnCustomTypeByDate = [
  {
    value: 'DateTime',
    label: '显示精确到时分秒',
  },
  {
    value: 'Date',
    label: '显示精确到日',
  },
  {
    value: 'Month',
    label: '显示精确到月',
  },
  {
    value: 'Quarter',
    label: '显示精确到季度',
  },
  {
    value: 'HalfYear',
    label: '显示精确到半年',
  },
  {
    value: 'Year',
    label: '显示精确到年',
  },
];

export const ColumnCustomTypeByDecimal = [
  {
    value: 'Decimal',
    label: '显示为一般数值',
  },
  {
    value: 'Currency',
    label: '显示为金额',
  },
  {
    value: 'Percent',
    label: '显示为百分比',
  },
  {
    value: 'Permillage',
    label: '显示为千分比',
  },
];
// 报表类型
export const ReportTypes: ReportType[] = [
  {
    name: 'StatsReadOnly',
    title: '只读统计报表',
    types: [],
    canSwitchMode: true,
    defaultValues: {
      // 通用配置defaultValue
      CustomTitle: null,
      IsPeriodic: false,
      AllowedDateGranularity: null,
      IsReadonly: true,
      EnableLoadData: true,
      EnablePersist: false,
      EnableValidate: false,
      EnableArchive: false,
      EnableSubmit: false,
      EnableExportBrief: false,
      EnableImport: false,
      // EnableRetrieveMetadata: false,
      IsPublic: true,
      ReportMode: 'StatsReadOnly',
    },
    columnsDefaultValues: {
      // 列配置defaultValue
      IsNullable: true,
      IsSignificant: false,
      IsReadOnly: true,
    },
  },
  {
    name: 'StatsPersist',
    title: '上报统计报表',
    canSwitchMode: true,
    types: [],
    defaultValues: {
      EnableLoadData: true,
      EnablePersist: true,
      EnableSubmit: false,
      EnableExportBrief: false,
      IsPublic: true,
      IsHidden: false,
      ReportMode: 'StatsPersist',
    },
  },
  {
    name: 'DetailsReadOnly',
    title: '只读明细报表',
    types: [],
    canSwitchMode: true,
    defaultValues: {
      CustomTitle: null,
      DataSize: 'Details',
      IsPeriodic: false,
      AllowedDateGranularity: null,
      IsReadonly: true,
      EnableLoadData: true,
      EnablePersist: false,
      EnableValidate: false,
      EnableArchive: false,
      EnableSubmit: false,
      // EnableExport: false,
      EnableExportBrief: false,
      EnableImport: false,
      // EnableRetrieveMetadata: false,
      IsPublic: true,
      ReportMode: 'DetailsReadOnly',
    },
    columnsDefaultValues: {
      IsNullable: true,
      IsSignificant: false,
      IsReadOnly: true,
    },
  },
  {
    name: 'DetailsPersist',
    title: '上报明细报表',
    types: [],
    canSwitchMode: true,
    defaultValues: {
      DataSize: 'Details',
      IsReadonly: true,
      EnableLoadData: true,
      EnablePersist: true,
      EnableSubmit: false,
      // EnableExport: false,
      EnableExportBrief: false,
      EnableImport: false,
      IsPublic: true,
      IsHidden: false,
      ReportMode: 'DetailsPersist',
    },
  },
  {
    name: 'ReportGroupReadOnly',
    title: '下钻报表组',
    types: [],
    canSwitchMode: false,
    defaultValues: {
      // 通用配置defaultValue
      CustomTitle: null,
      IsPeriodic: false,
      AllowedDateGranularity: null,
      IsReadonly: true,
      EnableLoadData: true,
      EnablePersist: false,
      EnableValidate: false,
      EnableArchive: false,
      EnableSubmit: false,
      // EnableRetrieveMetadata: false,
      IsPublic: true,
      IsHidden: false,
      ReportMode: 'ReportGroupReadOnly',
    },
    columnsDefaultValues: {
      // 列配置defaultValue
      IsNullable: true,
      IsSignificant: false,
      IsReadOnly: true,
    },
  },
];

export const ColumnsTypes: ColumnsType[] = [
  {
    name: 'varchar',
    defaultValues: {
      TableName: null,
      ColumnType: 'varchar',
      ColumnCustomType: null,
      ColumnPrecision: null,
      ColumnScale: null,
    },
  },
  {
    name: 'int',
    defaultValues: {
      TableName: null,
      ColumnType: 'int',
      ColumnCustomType: null,
      ColumnPrecision: null,
      ColumnScale: null,
      ColumnLength: null,
      DictModuleGroup: null,
      DictModule: null,
    },
  },
  {
    name: 'decimal',
    defaultValues: {
      TableName: null,
      ColumnType: 'decimal',
      ColumnLength: null,
      DictModuleGroup: null,
      DictModule: null,
    },
  },
  {
    name: 'datetime',
    defaultValues: {
      TableName: null,
      ColumnType: 'datetime',
      ColumnPrecision: null,
      ColumnScale: null,
      ColumnLength: null,
      DictModuleGroup: null,
      DictModule: null,
    },
  },
  {
    name: 'bit',
    defaultValues: {
      TableName: null,
      ColumnType: 'bit',
      ColumnCustomType: null,
      ColumnPrecision: null,
      ColumnScale: null,
      ColumnLength: null,
      DictModuleGroup: null,
      DictModule: null,
    },
  },
];

export const ArgsTypes: ArgsType[] = [
  {
    name: 'varchar',
    code: ['varchar', 'stringlisttype'],
    defaultValues: {
      ColumnType: 'varchar', // ColumnType varchar (若ValuePickMode='MultipleValue'，填 stringlisttype)
      ColumnCustomType: null,
      ColumnPrecision: null,
      ColumnScale: null,
      ColumnLength: null,
      DefaultValueExpr: null,
      MinValueExpr: null,
      MaxValueExpr: null,
    },
  },
  {
    name: 'int',
    code: ['int'],
    defaultValues: {
      ColumnType: 'int',
      ColumnCustomType: null,
      ColumnPrecision: null,
      ColumnScale: null,
      ColumnLength: null,
      DictModuleGroup: null,
      DictModule: null,
      DefaultValueExpr: null,
      MinValueExpr: null,
      MaxValueExpr: null,
    },
  },
  {
    name: 'decimal',
    code: ['decimal'],
    defaultValues: {
      ColumnType: 'decimal',
      ColumnPrecision: null,
      ColumnScale: null,
      ColumnLength: null,
      DictModuleGroup: null,
      DictModule: null,
      DefaultValueExpr: null,
      MinValueExpr: null,
      MaxValueExpr: null,
    },
  },
  {
    name: 'datetime',
    code: ['datetime'],
    defaultValues: {
      ColumnType: 'datetime',
      ColumnPrecision: null,
      ColumnScale: null,
      ColumnLength: null,
      DictModuleGroup: null,
      DictModule: null,
      DefaultValueExpr: null,
      MinValueExpr: null,
      MaxValueExpr: null,
    },
  },
  {
    name: 'bit',
    code: ['bit'],
    defaultValues: {
      ColumnType: 'bit',
      ColumnCustomType: null,
      ColumnPrecision: null,
      ColumnScale: null,
      ColumnLength: null,
      DictModuleGroup: null,
      DictModule: null,
      DefaultValueExpr: null,
      MinValueExpr: null,
      MaxValueExpr: null,
    },
  },
];

export const RelationModes: RelationModeType[] = [
  {
    name: 'ByRow',
    title: '按行选择(ByRow)',
    defaultValues: {
      RelationMode: 'ByRow',
      SrcColumn: null,
      SrcColumnParamValue: null,
      // IsDefaultParam: true
    },
  },
  {
    name: 'ByColumn',
    title: '按列选择(ByColumn)',
    defaultValues: {
      RelationMode: 'ByColumn',
      // IsDefaultParam: true
    },
  },
];
