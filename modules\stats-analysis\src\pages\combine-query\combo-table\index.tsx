import React, { useEffect, useImperativeHandle, useState } from 'react';
import './index.less';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { UniDragEditOnlyTable } from '@uni/components/src';
import { Button, Card, Col, Form, message, Modal, Row } from 'antd';
import {
  comboQueryColumnValue,
  comboQueryTableColumns,
} from '@/pages/combine-query/combo-table/columns';
import { v4 as uuidv4 } from 'uuid';
import { QueryProps } from '@react-awesome-query-builder/ui';
import { Emitter } from '@uni/utils/src/emitter';
import { StatsAnalysisEventConstant } from '@/constants';
import omit from 'lodash/omit';
import { EmptyWrapper, queryOperations } from '@/pages/combine-query';
import {
  buildOptions,
  generateQueryFromTable,
} from '@/pages/combine-query/combo-table/utils';
import { mongodbFormat } from '@/pages/combine-query/custom/mongoDb';
import { toText, toTree } from '@/pages/combine-query/combo-table/parser';
import { Utils } from '@react-awesome-query-builder/antd';
import { isEmptyValues } from '@uni/utils/src/utils';
import { bracketsValidation } from '@/pages/combine-query/combo-table/validate';
import ColumnNameMoreModal from '@/pages/combine-query/combo-table/components/column-more';
import { fieldItemsProcessor } from '@/pages/combine-query/utils';
import { CombineQueryFieldItem } from '@/pages/combine-query/interfaces';
import { RespVO } from '@uni/commons/src/interfaces';
import { history, useModel } from 'umi';
import { useRequest } from 'umi';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import { combineQuerySaveParamProcessor } from '@/pages/combine-query/processor';
const { jsonLogicFormat, loadTree } = Utils;
import { useAntdResizableHeader } from '@uni/components/src/table/resizable-column/header';
import qs from 'qs';
import { toMongo } from '@/pages/combine-query/combo-table/parser/converters';

const expressionEmptySearch = true;

interface ComboTableProps extends QueryProps {
  tableRef?: any;
  nextGeneration?: boolean;
  overrideContainerClassName?: string;

  inFullViewMode?: boolean;
  tableHeight?: number;
}

const tableId = 'combo-query-expression-table';

const ComboTable = (props: ComboTableProps) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  const [form] = Form.useForm();

  const [comboQueryColumns, setComboQueryColumns] = useState<any>([]);

  const [comboQueryDataSource, setComboQueryDataSource] = useState<any>([]);

  const comboTableConfig = localStorage.getItem('combo-table-width');

  const comboTableWidthConfig = JSON.parse(comboTableConfig ?? '{}');

  const comboQueryColumnProcessor = () => {
    setComboQueryColumns(
      comboQueryTableColumns(
        props,
        comboQueryDataSource,
        comboTableWidthConfig,
      ),
    );
  };

  const { components, resizableColumns, tableWidth, resetColumns } =
    useAntdResizableHeader({
      columns: React.useMemo(() => {
        return comboQueryColumns;
      }, [comboQueryColumns]),
      onResizeEnd: (columnItem: any) => {
        // 存到local Storage 里面先
        saveWidthToLocal(columnItem);
      },
    });

  useImperativeHandle(props.tableRef, () => ({
    onOperationClick: async (type: string) => {
      return await onOperationClick(type);
    },
    getTableSize: () => {
      return comboQueryDataSource?.length;
    },
    getExpression: async () => {
      let translationResult = await mongoTranslation();
      console.log(translationResult);
      if (translationResult?.success && translationResult?.mongoExpression) {
        return JSON.stringify(translationResult?.mongoExpression);
      }

      return '';
    },
  }));

  const saveWidthToLocal = (columnItem: any) => {
    if (!isEmptyValues(columnItem?.width)) {
      comboTableWidthConfig[columnItem?.dataIndex] = columnItem?.width;
    } else {
      delete comboTableWidthConfig[columnItem?.dataIndex];
    }

    localStorage.setItem(
      'combo-table-width',
      JSON.stringify(comboTableWidthConfig),
    );
  };

  useEffect(() => {
    setComboQueryDataSource([
      {
        id: uuidv4(),
        groupId: uuidv4(),
      },
    ]);

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_RESET,
      () => {
        // TODO 二次确认框
        setComboQueryDataSource([
          {
            id: uuidv4(),
            groupId: uuidv4(),
          },
        ]);
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
      (payload) => {
        setComboQueryDataSource(
          JSON.parse(payload?.DisPlayExpr)?.sort(
            (a, b) => a?.order ?? 65535 - b?.order ?? 65535,
          ),
        );
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_RESET,
      );
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
      );
    };
  }, []);

  useEffect(() => {
    console.log('comboQueryDataSourceChanged', comboQueryDataSource);
    // 条件组内的item删除
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ITEM_DELETE,
      (payload) => {
        let currentComboQueryDataSources = comboQueryDataSource.slice();
        let waitForDeleteItem = form.getFieldValue(payload?.recordId);

        currentComboQueryDataSources.splice(payload?.index, 1);

        // TODO 同样实现一套新增这个机制
        let groupItem = currentComboQueryDataSources?.find(
          (item) => item?.groupId === payload?.groupId,
        );

        // 表示当前不是一行 至少2行
        if (groupItem && waitForDeleteItem?.rowSpan > 1) {
          // 表示是group的item 需要把除了中间的三个都赋值到当前这个上面
          if (waitForDeleteItem?.rowSpan !== 0) {
            groupItem['rowSpan'] = waitForDeleteItem?.rowSpan - 1;
            form.setFieldValue(
              [groupItem?.id, 'rowSpan'],
              groupItem['rowSpan'],
            );

            let noCopyDataIndex = comboQueryColumnValue(
              undefined,
              undefined,
              undefined,
              [],
            )?.map((item) => item?.dataIndex);

            Object.keys(omit(waitForDeleteItem, noCopyDataIndex))?.forEach(
              (key) => {
                form.setFieldValue(
                  [groupItem?.id, key],
                  waitForDeleteItem[key],
                );
              },
            );
          }
        }

        // 删除了一个组内的需要 rowSpan - 1
        if (waitForDeleteItem?.rowSpan === 0) {
          groupItem['rowSpan'] = groupItem['rowSpan'] - 1;
          form.setFieldValue([groupItem?.id, 'rowSpan'], groupItem['rowSpan']);
        }

        setComboQueryDataSource(
          currentComboQueryDataSources?.map((item) => {
            return {
              ...item,
              ...form.getFieldValue(item?.id),
              rowSpan: item?.rowSpan, // item的才是对的 form的还没更新
            };
          }),
        );
      },
    );
    // 条件组内的item添加
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ITEM_ADD,
      (payload) => {
        let currentComboQueryDataSources = comboQueryDataSource.slice();
        let groupItem = currentComboQueryDataSources?.find(
          (item) => item?.groupId === payload?.groupId,
        );

        let addItem = {
          id: uuidv4(),
          groupId: payload?.groupId,
          rowSpan: 0,
          parentField: form.getFieldValue([payload?.recordId, 'parentField']),
        };
        currentComboQueryDataSources.splice(payload?.index + 1, 0, addItem);
        // 修改之前那个rowSpan
        groupItem['rowSpan'] = currentComboQueryDataSources?.filter(
          (item) => item?.groupId === payload?.groupId,
        )?.length;
        form.setFieldValue([groupItem?.id, 'rowSpan'], groupItem['rowSpan']);

        setComboQueryDataSource(
          currentComboQueryDataSources?.map((item) => {
            return {
              ...item,
              ...form.getFieldValue(item?.id),
            };
          }),
        );
      },
    );

    // 条件 删除
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_DELETE,
      ({ recordId, groupId, index }) => {
        console.log('STATS_ANALYSIS_TABLE_CONDITION_DELETE', groupId, index);
        let currentComboQueryDataSources = comboQueryDataSource.slice();

        setComboQueryDataSource(
          currentComboQueryDataSources?.filter(
            (item) => item?.groupId !== groupId,
          ),
        );
      },
    );

    // 条件 增加
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ADD,
      ({ recordId, groupId, index, offset }) => {
        console.log('STATS_ANALYSIS_TABLE_CONDITION_ADD', recordId, index);
        let currentComboQueryDataSources = comboQueryDataSource.slice();
        let addItem = {
          id: uuidv4(),
          groupId: uuidv4(),
        };
        currentComboQueryDataSources.splice(index + 1 + offset, 0, addItem);

        // 对于上一条数据如果连接符不存在就设定为AND
        if (currentComboQueryDataSources?.length > 1) {
          let previousData =
            currentComboQueryDataSources?.[
              currentComboQueryDataSources?.length - 2
            ];
          let previousLink = form.getFieldValue([previousData?.id, 'link']);
          if (isEmptyValues(previousLink)) {
            form.setFieldValue([previousData?.id, 'link'], 'and');
            previousData['link'] = 'and';
          }
        }

        setComboQueryDataSource(currentComboQueryDataSources);
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE,
      (payload) => {
        onSaveClick(payload);
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_CONDITION_ADD,
      (payload) => {
        let index = payload?.index;
        // 当且仅当是最后一个的时候才 + 1行
        if (index === comboQueryDataSource?.length - 1) {
          onConditionAddClick();
        }
      },
    );

    // 条件 清空
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_CLEAR,
      ({ recordId, groupId, index }) => {
        console.log('STATS_ANALYSIS_TABLE_CONDITION_CLEAR', groupId, index);

        setComboQueryDataSource([
          {
            id: uuidv4(),
            groupId: uuidv4(),
          },
        ]);
      },
    );

    return () => {
      Emitter.offMultiple([
        StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_CLEAR,
        StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_DELETE,
        StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ADD,
        StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ITEM_DELETE,
        StatsAnalysisEventConstant.STATS_ANALYSIS_TABLE_CONDITION_ITEM_ADD,
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE,
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_CONDITION_ADD,
      ]);
    };
  }, [comboQueryDataSource, props?.nextGeneration]);

  useEffect(() => {
    if (!isEmptyValues(props?.fields)) {
      comboQueryColumnProcessor();
    }
  }, [props, comboQueryDataSource]);

  const onSaveClick = async (payload?: any) => {
    let translationResult = await mongoTranslation();

    if (
      translationResult?.success === false ||
      translationResult?.mongoExpression === undefined
    ) {
      return;
    }

    if (translationResult?.success === true) {
      let combineQuerySaveResponse: RespVO<string> = await combineQuerySaveReq(
        payload?.id,
        tableExpressionConvert(),
        translationResult?.mongoExpression,
        payload?.combineQueryTitle,
        payload?.columnsState,
        payload?.selectedMetricsState,
      );
      if (
        combineQuerySaveResponse?.statusCode === 200 &&
        combineQuerySaveResponse?.code === 0
      ) {
        const searchParam = qs.stringify({
          id: combineQuerySaveResponse?.data,
        });
        if (props?.nextGeneration) {
          history.replace(`/combineQuery?${searchParam}`);
        } else {
          history.replace(`/combineQuery?${searchParam}`);
        }
        message.success('保存成功');
        Emitter.emit(
          StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_SAVE_SUCCESS,
          combineQuerySaveResponse?.data,
        );
      } else {
        message.error('保存错误,请稍后重试');
      }
    } else {
      message.error('查询条件不合法，请检查查询条件');
    }
  };

  const { loading: combineQuerySaveLoading, run: combineQuerySaveReq } =
    useRequest(
      (
        id,
        treeExpression,
        expression,
        combineQueryTitle,
        columnsState,
        selectedMetricsState,
      ) => {
        let data = {};
        if (id) {
          data['id'] = id;
        }
        // TODO isPublic 可以为false
        data['isPublic'] = true;
        data['title'] = combineQueryTitle;
        // expr -> mongo expression
        data['expr'] = JSON.stringify(expression);
        // display Expr -> table data
        data['displayExpr'] = JSON.stringify(treeExpression);
        data['DisplayExprStructure'] = 'Row';

        return uniCombineQueryService(
          'Api/DmrAnalysis/ComboQueryTemplate/save',
          {
            method: 'POST',
            requestType: 'json',
            data: combineQuerySaveParamProcessor(
              data,
              columnsState,
              selectedMetricsState,
            ),
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<string>) => {
          return response;
        },
      },
    );

  const mongoTranslation = async () => {
    if (expressionEmptySearch === true) {
      let currentDataSource = comboQueryDataSource?.slice()?.filter((item) => {
        return (
          !isEmptyValues(item?.columnName) &&
          !isEmptyValues(item?.operator) &&
          !isEmptyValues(item?.columnValue)
        );
      });

      if (currentDataSource?.length === 0) {
        return {
          success: true,
          treeExpression: '',
          mongoExpression: '',
        };
      }
    }

    let formValues = form?.getFieldsValue(true);
    let validateResponse = await form.validateFields();
    if (validateResponse?.errorFields?.length > 0) {
      return;
    }
    console.log('mongoTranslation', formValues);

    let bracketsValidationResult = bracketsValidation(formValues);
    if (bracketsValidationResult === false) {
      message.error('请检查括号数量是否匹配');
      return {
        success: false,
        treeExpression: undefined,
        mongoExpression: undefined,
      };
    }
    let tableQuery = generateQueryFromTable(formValues, props);
    console.log('tableQuery', tableQuery);

    const { tree, lexErrors, parseErrors } = toTree(tableQuery);
    const {
      mongo,
      lexErrors: mongoErrors,
      parseErrors: mongoParseErrors,
    } = toMongo(tableQuery);
    console.log('toTree', tree, lexErrors, parseErrors);
    if (lexErrors?.length > 0 || parseErrors?.length > 0) {
      console.log('lexErrors', lexErrors);
      console.log('parseErrors', parseErrors);
      // TODO 埋点 ?
      message.error('转换条件树出现错误，请联系管理员');
      return {
        success: false,
        treeExpression: undefined,
        mongoExpression: undefined,
      };
    }

    if (mongoErrors?.length > 0 || mongoParseErrors?.length > 0) {
      console.log('mongoErrors', mongoErrors);
      console.log('mongoParseErrors', mongoParseErrors);
      // TODO 埋点 ?
      message.error('转换mongo出现错误，请联系管理员');
      return {
        success: false,
        treeExpression: undefined,
        mongoExpression: undefined,
      };
    }
    try {
      const comboQueryTree = loadTree(tree);
      // Emitter.emit(
      //     StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_TO_TREE,
      //     comboQueryTree,
      // );
      const previousMongoQuery = mongodbFormat(comboQueryTree, props);
      const mongoQuery = mongo;
      console.log('mongoQuery', previousMongoQuery, mongoQuery);
      if (mongoQuery) {
        return {
          success: true,
          treeExpression: comboQueryTree,
          mongoExpression: mongoQuery,
        };
      } else {
        const { logic, data, errors } = jsonLogicFormat(tree, props);
        console.error('jsonLogicFormat', logic, data, errors);
        message.error('数据库查询语句 翻译错误，请重试');
        return {
          success: false,
          treeExpression: undefined,
          mongoExpression: undefined,
        };
      }
    } catch (comboQueryTreeError) {
      message.error('转换条件树出现错误，请联系管理员');
      return {
        success: false,
        treeExpression: undefined,
        mongoExpression: undefined,
      };
    }
  };

  const tableExpressionConvert = () => {
    let dataOrder = form.getFieldValue('dataOrder');
    let convertedDataSource = [];

    comboQueryDataSource?.forEach((item) => {
      if (item?.id) {
        let values = form.getFieldValue(item?.id);

        let convertedItem = {
          ...omit(values, ['operatorMeta', 'parentField']),
          order: dataOrder?.[item?.id],
        };
        convertedItem['parentFieldPath'] = values['parentField']?.path;

        convertedDataSource.push(convertedItem);
      }
    });

    return convertedDataSource?.sort(
      (a, b) => a?.order ?? 65535 - b?.order ?? 65535,
    );
  };

  const onOperationClick = async (type) => {
    switch (type) {
      case 'QUERY': {
        let translationResult = await mongoTranslation();
        console.log(translationResult);
        if (translationResult?.success) {
          if (expressionEmptySearch === false) {
            if (isEmptyValues(translationResult?.mongoExpression)) {
              return;
            }
          }
          Emitter.emit(
            [
              StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY,
              StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_METRICS,
            ],
            isEmptyValues(translationResult?.mongoExpression)
              ? ''
              : JSON.stringify(translationResult?.mongoExpression),
          );
        }

        return translationResult;
      }
      case 'RESET': {
        setComboQueryDataSource([
          {
            id: uuidv4(),
            groupId: uuidv4(),
          },
        ]);

        break;
      }
      case 'VIEW_TEXT': {
        let formValues = form?.getFieldsValue(true);
        // let validateResponse = await form.validateFields();
        // if (validateResponse?.errorFields?.length !== 0) {
        //     return;
        // }

        let bracketsValidationResult = bracketsValidation(formValues);
        if (bracketsValidationResult === false) {
          message.error('请检查括号数量是否匹配');
          return;
        }

        console.log('beforeTableQuery', formValues, props);
        let tableQuery = generateQueryFromTable(formValues, props, dictData);
        console.log('tableQuery', tableQuery);

        const { text, lexErrors, parseErrors } = toText(tableQuery);
        console.log('toText', text, lexErrors, parseErrors);
        if (lexErrors?.length > 0 || parseErrors?.length > 0) {
          console.log('lexErrors', lexErrors);
          console.log('parseErrors', parseErrors);
          // TODO 埋点 ?
          message.error('转换条件树出现错误，请联系管理员');
          return;
        }
        Modal.success({
          title: '查询条件的语法',
          content: text,
        });
        break;
      }
      default:
        break;
    }

    return {
      success: true,
    };
  };

  const onConditionAddClick = () => {
    let dataSources = comboQueryDataSource.concat({
      id: uuidv4(),
      groupId: uuidv4(),
    });

    // 对于上一条数据如果连接符不存在就设定为AND
    if (dataSources?.length > 1) {
      let previousData = dataSources?.[dataSources?.length - 2];
      let previousLink = form.getFieldValue([previousData?.id, 'link']);
      if (isEmptyValues(previousLink)) {
        form.setFieldValue([previousData?.id, 'link'], 'and');
        previousData['link'] = 'and';
      }
    }

    setComboQueryDataSource(dataSources);
  };

  const TableOuterContainer = props?.nextGeneration ? EmptyWrapper : Card;

  const tableOuterContainerProps = {
    title: '查询条件',
    extra: props?.nextGeneration ? null : (
      <Button type={'primary'} onClick={onConditionAddClick}>
        新增条件
      </Button>
    ),
  };

  return (
    <div
      id={'combo-query-expression-table-container'}
      className={`${
        props?.overrideContainerClassName ??
        'combo-query-expression-table-container'
      } ${comboQueryDataSource?.length === 0 ? 'table-empty' : ''}`}
    >
      <TableOuterContainer {...tableOuterContainerProps}>
        <UniDragEditOnlyTable
          form={form}
          key={tableId}
          id={tableId}
          tableId={tableId}
          bordered={props?.nextGeneration !== true}
          forceColumnsUpdate={true}
          scroll={{
            y:
              props?.tableHeight - 50 ??
              (props?.inFullViewMode === true ? 'max-content' : 160),
            // x: 'max-content',
          }}
          pagination={false}
          className={`combo-query-table-container`}
          dataSource={comboQueryDataSource}
          rowKey={'id'}
          allowDragging={false}
          onValuesChange={(recordList) => {
            setComboQueryDataSource(recordList);
          }}
          columns={resizableColumns}
          components={components}
        />

        <ColumnNameMoreModal
          mainForm={form}
          fields={fieldItemsProcessor(buildOptions(props, props?.fields), {
            ...props,
            queryType: 'table',
          })}
        />

        {props?.nextGeneration !== true && (
          <div className={'operations-container'}>
            {queryOperations.map((item) => {
              return (
                <Button
                  onClick={() => {
                    onOperationClick(item?.key);
                  }}
                  className={'operation-item'}
                >
                  {item.label}
                </Button>
              );
            })}
          </div>
        )}
      </TableOuterContainer>
    </div>
  );
};

export default ComboTable;
