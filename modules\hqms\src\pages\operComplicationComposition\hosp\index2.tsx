import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { UniSelect } from '@uni/components/src';
import { Col, Row, Tabs } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import _ from 'lodash';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '../../components/drawerCardInfo';
import { TotalStatsColumns } from '../constants';
import { uniCommonService, uniHqmsService } from '@uni/services/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import './index.less';
import DrillDownOperComplicationComposition from '../../components/drillDownOperComplicationComposition/index';
import Stats from '../../components/statsWithTrend';
import SingleColumnTable from '../../components/singleColumnTable/index';
import { RespVO } from '@uni/commons/src/interfaces';

const OperComplicationComposition = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('sdComposition');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedItem, setSelectedItem] = useState(undefined);

  const [selectOpts, setSelectOpts] = useState([]);

  // useEffect(() => {
  //   // 这个页面，先调用table + mdc
  //   MdcCoverageOfHospReq(searchParams);
  //   fetchTableReq(searchParams);
  // }, [searchParams]);

  // // 等table数据出来了，再调用下面的数据显示
  // useUpdateEffect(() => {
  //   if (tableState.clkItem) {
  //     fetchOtherReq({
  //       ...searchParams,
  //       ADrgCode: [tableState?.clkItem?.ADrgCode],
  //     });
  //   }
  // }, [tableState.clkItem]);

  // tab 使用下拉框数据
  const {
    loading: HqmsOperComplicationCompositionLoading,
    run: HqmsOperComplicationCompositionReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        // 'Api/Hqms/HospHqmsOperComplicationComposition/BundledHqmsOperComplicationComposition',
        'API/v2/Hqms/HqmsStats/HqmsOperComplicationCompositionOfHosp',
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          console.log('res?.data12345', res?.data);
          if (res?.data?.length) {
            setSelectOpts(
              res.data?.map((d) => ({
                ...d,
                label: `${d?.OperComplicationName}`,
              })),
            );
            // 默认把第一个设置为selected
            if (!selectedItem) {
              setSelectedItem(
                res?.data?.at(0),
                // _.maxBy(res?.data, function (o) {
                //   return o.PatCnt;
                // }),
              );
            }
          } else {
            setSelectOpts([]);
          }
        }
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
    };
    setTableParams(params);
    HqmsOperComplicationCompositionReq(params);
  }, [dateRange, hospCodes]);

  useEffect(() => {
    Emitter.on(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK, (record) => {
      setSelectedItem(record);
      setActiveKey('statistic');
    });

    return () => {
      Emitter.off(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK);
    };
  }, []);

  let tabItems = [
    {
      key: 'sdComposition',
      label: '择期手术患者并发症',
      children: (
        <DrillDownOperComplicationComposition
          tableParams={tableParams}
          // compositionApi="Api/Hqms/HospHqmsOperComplicationComposition/BundledHqmsOperComplicationComposition"
          compositionApi="API/v2/Hqms/HqmsStats/HqmsOperComplicationCompositionOfHosp"
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.OperComplicationName + '择期手术人数',
              args: {
                ...tableParams,
                OperComplicationCodes: record?.OperComplicationCode
                  ? [record?.OperComplicationCode]
                  : [],
                IsSelectiveSurgeryForComplication: true,
              },
              detailsUrl: 'Hqms/Details/HqmsOperComplicationDetails',
              dictData: dictData,
            });
          }}
        />
      ),
    },
    {
      key: 'statistic',
      label: '全院综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              // api={`Api/Hqms/HospHqmsOperComplicationComposition/BundledHqmsOperComplicationComposition`}
              api={`API/v2/Hqms/HqmsStats/HqmsOperComplicationCompositionOfHosp`}
              trendApi={`API/v2/Hqms/HqmsStats/HqmsOperComplicationCompositionTrend`}
              columns={TotalStatsColumns}
              type="col-xl-24"
              tabKey={activeKey}
              useGlobalState
              level="operComp"
              chartHeight={300}
              selectedTableItem={selectedItem}
            />
          </Col>
          <SingleColumnTable
            title="该并发症全院分布"
            args={{
              // api: 'Api/Hqms/HospHqmsOperComplicationComposition/HqmsOperComplicationCompositionByHosp',
              api: 'API/v2/Hqms/HqmsStats/HqmsOperComplicationCompositionByHosp',
              extraApiArgs: {
                OperComplicationCodes: selectedItem?.OperComplicationCode
                  ? [selectedItem?.OperComplicationCode]
                  : [],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="HospName"
            type="table"
            visibleValueKeys={[
              'HospName',
              'SelectiveSurgeryForComplicationPatCnt',
            ]}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.HospName + '择期手术人数',
                args: {
                  ...tableParams,
                  OperComplicationCodes: selectedItem?.OperComplicationCode
                    ? [selectedItem?.OperComplicationCode]
                    : [],
                  HospCode: record?.HospCode ? [record?.HospCode] : [],
                  IsSelectiveSurgeryForComplication: true,
                },
                detailsUrl: 'Hqms/Details/HqmsOperComplicationDetails',
                dictData: dictData,
              });
            }}
            colSpan={{ span: 24 }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_deptAnalysis',
      label: '科室综合分析',
      children: (
        <Row>
          <SingleColumnTable
            title="该并发症科室分布"
            args={{
              // api: 'Api/Hqms/CliDeptHqmsOperComplicationComposition/HqmsOperComplicationCompositionByCliDept',
              api: 'API/v2/Hqms/HqmsStats/HqmsOperComplicationCompositionByCliDept',
              extraApiArgs: {
                OperComplicationCodes: selectedItem?.OperComplicationCode
                  ? [selectedItem?.OperComplicationCode]
                  : [],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="CliDeptName"
            type="table"
            visibleValueKeys={[
              'CliDeptName',
              'OperComplicationPatCnt',
              'OperComplicationPatRatio',
              'SelectiveSurgeryForComplicationPatCnt',
            ]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.CliDeptName + '择期手术人数',
                args: {
                  ...tableParams,
                  CliDepts: record?.CliDept ? [record?.CliDept] : [],
                  OperComplicationCodes: selectedItem?.OperComplicationCode
                    ? [selectedItem?.OperComplicationCode]
                    : [],
                  IsSelectiveSurgeryForComplication: true,
                },
                detailsUrl: 'Hqms/Details/HqmsOperComplicationDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_medTeamAnalysis',
      label: '医疗组综合分析',
      children: (
        <Row>
          <SingleColumnTable
            title="该并发症医疗组分布"
            args={{
              // api: 'Api/Hqms/MedTeamHqmsOperComplicationComposition/HqmsOperComplicationCompositionByMedTeam',
              api: 'API/v2/Hqms/HqmsStats/HqmsOperComplicationCompositionByMedTeam',
              extraApiArgs: {
                OperComplicationCodes: selectedItem?.OperComplicationCode
                  ? [selectedItem?.OperComplicationCode]
                  : [],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="MedTeamName"
            type="table"
            visibleValueKeys={[
              'MedTeamName',
              'OperComplicationPatCnt',
              'OperComplicationPatRatio',
              'SelectiveSurgeryForComplicationPatCnt',
            ]}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName + '择期手术人数',
                args: {
                  ...tableParams,
                  MedTeams: record?.MedTeam ? [record?.MedTeam] : [],
                  OperComplicationCodes: selectedItem?.OperComplicationCode
                    ? [selectedItem?.OperComplicationCode]
                    : [],
                  IsSelectiveSurgeryForComplication: true,
                },
                detailsUrl: 'Hqms/Details/HqmsOperComplicationDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <Row>
          <SingleColumnTable
            title="该并发症医生分布"
            args={{
              // api: 'Api/Hqms/SurgeonHqmsOperComplicationComposition/HqmsOperComplicationCompositionBySurgeon',
              api: 'API/v2/Hqms/HqmsStats/HqmsOperComplicationCompositionBySurgeon',
              extraApiArgs: {
                OperComplicationCodes: selectedItem?.OperComplicationCode
                  ? [selectedItem?.OperComplicationCode]
                  : [],
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="SurgeonName"
            type="table"
            visibleValueKeys={[
              'SurgeonName',
              'OperComplicationPatCnt',
              'OperComplicationPatRatio',
              'SelectiveSurgeryForComplicationPatCnt',
            ]}
            colSpan={{ span: 24 }}
            select={{
              dataKey: 'SurgeonType',
              valueKey: 'GroupByDoctor',
              allowClear: false,
              defaultSelect: true,
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.SurgeonName + '择期手术人数',
                args: {
                  ...tableParams,
                  SurgeonCodes: record?.SurgeonCode
                    ? [record?.SurgeonCode]
                    : [],
                  SurgeonType: record?.SurgeonType,
                  OperComplicationCodes: selectedItem?.OperComplicationCode
                    ? [selectedItem?.OperComplicationCode]
                    : [],
                  IsSelectiveSurgeryForComplication: true,
                },
                detailsUrl: 'Hqms/Details/HqmsOperComplicationDetails',
                dictData: dictData,
              });
            }}
          />
        </Row>
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
        tabBarExtraContent={{
          right: activeKey !== 'sdComposition' && (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <label>当前并发症：</label>
              <UniSelect
                width={300}
                showSearch
                dataSource={selectOpts}
                value={selectedItem?.OperComplicationCode}
                onChange={(value) => {
                  setSelectedItem(
                    selectOpts?.find((d) => d?.OperComplicationCode === value),
                  );
                }}
                allowClear={false}
                optionNameKey={'label'}
                optionValueKey={'OperComplicationCode'}
                enablePinyinSearch={true}
                fieldNames={{
                  // label: 'ChsDrgName',
                  value: 'OperComplicationCode',
                }}
              />
            </div>
          ),
        }}
      />
      {/* 把modal与drawer的位置提高到这里，统一在这里处理 避免id太多导致匹配混乱 & tab切换后id失效 */}
      {/* TODO DetailTableModal内部的实现方式是destoryOnClose 导致每次打开都要重新获取columns */}
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      {/* drawer 同理 这样保持复用 因为drawer 监听的是detailTableModal 或者一些table */}
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default OperComplicationComposition;
