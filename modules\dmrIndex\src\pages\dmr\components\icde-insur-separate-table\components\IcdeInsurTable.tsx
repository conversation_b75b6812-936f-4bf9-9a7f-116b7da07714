import React, { useEffect, useState, useMemo } from 'react';
import { Form } from 'antd';
import { cloneDeep } from 'lodash';
import { UniDmrDragEditOnlyTable } from '@uni/components/src';
import { icdeColumns } from '../../../columns';
import { triggerFormValueChangeEvent } from '@uni/grid/src/utils';

interface IcdeInsurTableProps {
  form: any;
  id: string;
  parentId: string;
  dataSource: any[];
  onChange: (data: any[]) => void;
  className?: string;
  style?: React.CSSProperties;
  underConfiguration?: boolean;
}

const IcdeInsurTable: React.FC<IcdeInsurTableProps> = (props) => {
  const { form, dataSource, onChange, parentId, id } = props;
  
  // 监听医保表格数据变化（用于触发重新渲染）
  const insurTableWatchData = Form.useWatch('diagnosisInsurTable', form) ?? [];
  const [tableDataSourceSize, setTableDataSourceSize] = useState(0);

  // 获取用户权限
  const getUserRole = () => {
    // TODO: 从全局状态获取用户角色
    // 可以通过 useModel('@@qiankunStateFromMaster') 获取全局状态
    return 'employee'; // 临时返回
  };

  const userRole = getUserRole();
  const isReadonly = userRole === 'employee'; // employee用户对医保表格只读

  // 过滤列：只保留医保相关列和必要的关联列
  const filteredColumns = useMemo(() => {
    return icdeColumns.filter(column => {
      const dataIndex = column.dataIndex;
      // 保留医保编码、医保名称，以及用于显示的诊断编码、诊断名称
      return ['InsurCode', 'InsurName', 'IcdeCode', 'IcdeName'].includes(dataIndex);
    }).map(column => {
      // 根据权限设置列的只读状态
      if (isReadonly && ['InsurCode', 'InsurName'].includes(column.dataIndex)) {
        return {
          ...column,
          readonly: true,
        };
      }
      
      // IcdeCode和IcdeName始终只读（用于显示关联信息）
      if (['IcdeCode', 'IcdeName'].includes(column.dataIndex)) {
        return {
          ...column,
          readonly: true,
        };
      }
      
      return column;
    });
  }, [isReadonly]);

  // 处理表格数据，添加ADD行
  const processedDataSource = useMemo(() => {
    if (!dataSource || !Array.isArray(dataSource)) {
      return [{ id: 'ADD' }];
    }

    return dataSource
      .filter((item) => item?.id !== 'ADD')
      .map((item) => {
        if (!item['id']) {
          item['id'] = Math.round(Date.now() / 1000);
        }
        return item;
      })
      .concat({ id: 'ADD' });
  }, [dataSource]);

  // 处理表格值变化
  const handleValuesChange = (recordList: any[], changedValues: any) => {
    // 如果是employee用户，不允许编辑医保表格
    if (userRole === 'employee') {
      return;
    }
    
    // 过滤掉ADD行
    const filteredRecordList = recordList.filter(item => item?.id !== 'ADD');
    
    // 更新医保表格专用字段（用于触发重新渲染）
    form.setFieldValue('diagnosis-insur-table', filteredRecordList);
    form.setFieldValue('diagnosisInsurTable', cloneDeep(filteredRecordList));
    
    // 触发表单变化事件
    triggerFormValueChangeEvent('diagnosis-insur-table');
    
    // 调用父组件的onChange
    if (onChange) {
      onChange(filteredRecordList);
    }
  };

  // 监听数据变化更新表格大小
  useEffect(() => {
    setTableDataSourceSize(insurTableWatchData?.length || 0);
  }, [insurTableWatchData]);

  return (
    <div className={`icde-insur-table ${isReadonly ? 'readonly' : ''}`}>
      <UniDmrDragEditOnlyTable
        formKey={'diagnosisInsurTable'}
        dataSource={processedDataSource}
        columns={filteredColumns}
        onValuesChange={handleValuesChange}
        form={form}
        id={id}
        parentId={parentId}
        className={props.className}
        style={props.style}
        underConfiguration={props.underConfiguration}
        // 其他原有的props
        tableDataSourceSize={tableDataSourceSize}
        // 根据权限禁用编辑功能
        disabled={isReadonly}
      />
    </div>
  );
};

export default React.memo(IcdeInsurTable);
