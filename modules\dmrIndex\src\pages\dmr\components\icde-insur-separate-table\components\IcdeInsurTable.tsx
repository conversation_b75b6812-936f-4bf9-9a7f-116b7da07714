import React, { useEffect, useState } from 'react';
import { Form } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import { UniDmrDragEditOnlyTable } from '@uni/components/src';
import { icdeColumns } from '@/pages/dmr/columns';
import { triggerFormValueChangeEvent } from '@uni/grid/src/utils';

interface IcdeInsurTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];
  underConfiguration?: boolean;
  onChange?: (value: any) => void;
}

const IcdeInsurTable = (props: IcdeInsurTableProps) => {
  const [form] = Form.useForm();

  // 监听医保表格数据变化（用于触发重新渲染）
  const insurTableWatchData = Form.useWatch('diagnosisInsurTable', props?.form) ?? [];

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    insurTableWatchData?.length,
  );

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  // 获取用户权限
  const getUserRole = () => {
    // TODO: 从全局状态获取用户角色
    return 'employee'; // 临时返回
  };

  const userRole = getUserRole();
  const isReadonly = userRole === 'employee'; // employee用户对医保表格只读

  // 过滤列：只保留医保相关列和必要的关联列
  useEffect(() => {
    const filteredColumns = icdeColumns.filter(column => {
      const dataIndex = column.dataIndex;
      // 保留医保编码、医保名称，以及用于显示的诊断编码、诊断名称
      return ['InsurCode', 'InsurName', 'IcdeCode', 'IcdeName'].includes(dataIndex);
    }).map(column => {
      // 根据权限设置列的只读状态
      if (isReadonly && ['InsurCode', 'InsurName'].includes(column.dataIndex)) {
        return {
          ...column,
          readonly: true,
        };
      }

      // IcdeCode和IcdeName始终只读（用于显示关联信息）
      if (['IcdeCode', 'IcdeName'].includes(column.dataIndex)) {
        return {
          ...column,
          readonly: true,
        };
      }

      return column;
    });
    setTableColumns(filteredColumns);
  }, [isReadonly]);

  useEffect(() => {
    setTableDataSourceSize(insurTableWatchData?.length);
  }, [insurTableWatchData]);

  return (
    <div className={`icde-insur-table ${isReadonly ? 'readonly' : ''}`}>
      <UniDmrDragEditOnlyTable
        {...props}
        form={form}
        key={props?.id}
        id={props?.id}
        tableId={props?.id}
        formKey={'diagnosisInsurTable'}
        forceColumnsUpdate={props?.underConfiguration ?? false}
        scroll={{
          x: 'max-content',
        }}
        pagination={false}
        className={`table-container ${props?.className || ''}`}
        dataSource={(props?.form?.getFieldValue('diagnosis-insur-table') ?? [])
          ?.filter((item) => item?.id !== 'ADD')
          ?.map((item) => {
            if (!item['id']) {
              item['id'] = Math.round(Date.now() / 1000);
            }
            return item;
          })
          ?.concat({
            id: 'ADD',
          })}
        rowKey={'id'}
        onValuesChange={(recordList, changedValues) => {
          // 如果是employee用户，不允许编辑医保表格
          if (userRole === 'employee') {
            return;
          }

          props?.form?.setFieldValue('diagnosis-insur-table', recordList);
          triggerFormValueChangeEvent('diagnosis-insur-table');
        }}
        onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
          if (userRole === 'employee') {
            return;
          }

          props?.form?.setFieldValue('diagnosis-insur-table', cloneDeep(tableData));
          triggerFormValueChangeEvent('diagnosis-insur-table');
        }}
        columns={tableColumns}
        enableRowSelection={true}
      />
    </div>
  );
};

export default React.memo(IcdeInsurTable);
