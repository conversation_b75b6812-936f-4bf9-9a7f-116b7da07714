import { TableColumnType } from 'antd';
import { ReactNode } from 'react';

export interface DynamicManagementProps {
  /**
   * 页面标题
   * @example "门急动态管理" | "住院动态管理"
   */
  pageTitle: string;

  /**
   * API基础路径
   * @example "DeptOutpatientAmtMgmt" | "DeptInpatientAmtMgmt"
   */
  apiBasePath: string;

  /**
   * 详情按钮点击事件的key
   * @example OutPatientDynamicManagementConstants.DETAIL_BTN_CLK
   */
  detailBtnEventKey: string;

  /**
   * 表格列配置
   */
  columns: TableColumnType<any>[];

  /**
   * 表格的唯一标识符
   * @example "odm_out_patient_dynamic_management"
   */
  id: string;

  /**
   * 科室登记组件
   * @example EditByDept from outPatientDynamicRegistration or inHospDynamicRegistration
   */
  EditByDeptComponent: React.ComponentType<{
    propsSearchParams: {
      Sdate: string;
      Edate: string;
      HospCode: string;
      DeptCode: string;
      needFetch: boolean;
    };
  }>;
}

/**
 * 表格数据项的通用接口
 */
export interface IDynamicManagementItem {
  /**
   * 唯一标识符
   */
  uuid: string;

  /**
   * 是否有效
   */
  IsValid: boolean;

  /**
   * 科室
   */
  Dept?: string;

  /**
   * 科室代码
   */
  DeptCode?: string;

  [key: string]: any;
}
