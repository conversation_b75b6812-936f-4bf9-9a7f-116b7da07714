import React, { Component, useEffect } from 'react';
import {
  Breadcrumb,
  Collapse,
  ConfigProvider,
  Layout,
  message,
  Result,
} from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import AutoLogoutLayout from '@/layouts/AutoLogoutLayout';
import './index.less';
import MenuSider from '@uni/components/src/menu-sider/MenuSider';
import { Content, Footer } from 'antd/es/layout/layout';
import SiteHeader from '@uni/components/src/header/header';
import { ErrorBoundary } from 'react-error-boundary';
import { Link, useLocation } from 'umi';
import {
  getBreadcrumbFromMenuData,
  headerMenu,
  menuData,
} from '@/layouts/menuData';
import { useAccess } from '@@/plugin-access/access';
import { useModel } from '@@/plugin-model/useModel';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { metaDataService, uniCommonService } from '@uni/services/src';
import { Dispatch } from '@@/plugin-dva/connect';
import { useDispatch, useSelector } from '@@/plugin-dva/exports';
import { getRoutes } from '@@/core/routes';
import mergeWith from 'lodash/mergeWith';
import merge from 'lodash/merge';
import { BaseLayoutHeaderItem } from '@/layouts/base-layout';
import { useAsyncEffect } from 'ahooks';
import Constants from '@/constants';
import groupBy from 'lodash/groupBy';
import uniq from 'lodash/uniq';
import { doLog, LogLevel } from '@uni/services/src/commonService';
import {
  addGlobalUncaughtErrorHandler,
  removeGlobalUncaughtErrorHandler,
} from '@/utils/utils';
import {
  clearScrollingTitleTimeout,
  getAllLeafMenuItem,
  getHeaderIds,
  isEmptyValues,
} from '@uni/utils/src/utils';
import { history } from 'umi';
import { HierarchyItem, RespVO } from '@uni/commons/src/interfaces';
import { TableColumnEditModal } from '@uni/components/src/table/column-edit';
import { cloneDeep, isEmpty } from 'lodash';
import TableReport from '@uni/commons/src/icon/TableReport';
import ColumnModule from './ColumnModule';
import { QueryParameterConfigurationEditModal } from '@uni/components/src/query-configuration';
import { CameraWithGallery } from '@uni/components/src/camera';
import { tableStripeConfiguration } from '@/layouts/root/processor';
import { DmrAod } from '@/layouts/root/aod';
import BrowserCheck from '@/layouts/root/browser-check';
import { highlightMenuRoutePatch } from '@/layouts/root/report-menu-patch';
import { useRequest } from '@@/plugin-request/request';
import { DynamicMenuItem, dynamicMenuPatch } from '@/layouts/root/dynamic-menu';
import { getAllMenusContainsHidden } from '@uni/components/src/menu-sider/utils';
import { DoctorMedicalRecordIndex } from './dmrindex';

interface ExtraReportAppCodeItem {
  appCode: string;
  routePrefix: string;
  parentRoute: string;
}

const defaultLandingPage = (window as any).externalConfig?.['common']
  ?.defaultLandingPage;

const minChromeVersion = (window as any).externalConfig?.['common']
  ?.minChromeVersion;

const enableDynamicMenuItem =
  (window as any).externalConfig?.['common']?.enableDynamicMenuItem ?? false;

const extraReportAppCodes: ExtraReportAppCodeItem[] =
  (window as any).externalConfig?.['common']?.extraReportAppCodes ?? [];

const enableAutoLogout =
  (window as any).externalConfig?.['common']?.enableAutoLogout ?? false;

const qs = require('qs');

const { Panel } = Collapse;

const fullscreenPathSuffix = '/fullscreen';

const ErrorFallback = ({ error, resetErrorBoundary }) => {
  useEffect(() => {
    let errorMessage = `前端报错：
      路由： ${location?.pathname}
      错误信息： ${error?.message}
      错误堆栈： ${error?.stack}
    `;
    doLog(LogLevel.Fatal, errorMessage);
  }, []);

  return (
    <div className={'error-container'}>
      <Result status="error" title="出错了">
        <Collapse defaultActiveKey={'1'}>
          <Panel header={'展开错误信息'} key="1">
            <span className={'message'}>{error.message}</span>
            <pre>{error.stack}</pre>
          </Panel>
        </Collapse>
      </Result>
    </div>
  );
};

interface RootLayoutProps {
  route?: any;
  routes?: any[];
  systemInfo?: any;
  children?: React.ReactNode;
}

const RootLayout = (props: RootLayoutProps) => {
  const location = useLocation();
  const access = useAccess();

  const dmrAodContainerRef = React.useRef();

  const { initialState }: any = useModel('@@initialState');

  const dvaDictData = useSelector((state: any) => state.uniDict.dictData);

  const dispatch: Dispatch = useDispatch();

  const [patchedMenuData, setPatchedMenuData] = React.useState(menuData);

  const [hierarchiesFetched, setHierarchiesFetched] = React.useState(false);

  const menuPatched = React.useRef(false);

  useAsyncEffect(async () => {
    await patchMenuData();
  }, []);

  useAsyncEffect(async () => {
    if (
      hierarchiesFetched === false &&
      isEmptyValues(dvaDictData?.['Hierarchies'])
    ) {
      await processUniqueData();
    }
  }, [dvaDictData, hierarchiesFetched]);

  let allLeafRoutes = getAllLeafMenuItem(patchedMenuData);

  useAsyncEffect(async () => {
    console.log('onRouteChange RootLayout', location);

    if (menuPatched?.current === false) {
      await patchMenuData();
    }

    // 此处location 不会存在 error / login / main
    // 表示不是 叶子节点 因此做redirect
    let pathname = location.pathname;
    if (!allLeafRoutes?.includes(pathname)) {
      let rootNode = (global['patchedMenuData'] ?? menuData)?.find(
        (item) => item?.route === pathname,
      );
      let redirectPathName = rootNode?.headerMenuItemActualRoute;

      if (access[redirectPathName] !== true) {
        let currentMenuLeafItems = getAllLeafMenuItem(
          global['patchedMenuData'] ?? menuData,
          pathname,
        );
        // 选择第一个 有权限的

        redirectPathName = currentMenuLeafItems?.find((item) => access[item]);
      }

      if (redirectPathName) {
        history.replace(redirectPathName);
      }
    }

    // 动态菜单下 title 问题修复
    let currentSelectRouteItem = getAllMenusContainsHidden(
      global['patchedMenuData'] ?? menuData,
    )?.find((item) => item.route === location.pathname);
    if (currentSelectRouteItem && currentSelectRouteItem?.name) {
      clearScrollingTitleTimeout();
      document.title = `${currentSelectRouteItem?.name}`;
      sessionStorage.setItem('titleByPath', document.title);
    }
  }, [location]);

  const processUniqueData = async () => {
    let dictData = {
      dictData: {},
    };
    let cliDeptWithHospCode = await processCliDeptWithHospCode();
    dictData['dictData']['Hierarchies'] = cliDeptWithHospCode;

    dictData['dictData']['MrDepts'] = cliDeptWithHospCode?.filter(
      (item) => item?.HierarchyType === '1' && item?.IsMr === true,
    );

    dispatch({
      type: 'uniDict/saveDictionaryData',
      data: dictData,
    });

    setHierarchiesFetched(true);
  };

  const masterResponseReq = async (appCode) =>
    await uniCommonService('Api/Report/Report/GetRestrictedReportMasters', {
      params: {
        AppCode: appCode,
      },
    });

  const { loading: dynamicMenuItemLoading, run: dynamicMenuItemsReq } =
    useRequest(
      () => {
        return uniCommonService('Api/Sys/ClientKitSys/GetDynamicMenuItems', {
          method: 'POST',
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<DynamicMenuItem[]>) => {
          console.log('response', response);
          return response?.data ?? [];
        },
      },
    );

  const patchMenuData = async () => {
    if (enableDynamicMenuItem === false) {
      return;
    }

    if (menuPatched?.current === true) {
      return;
    }

    let patchedMenuData = cloneDeep(menuData);

    let dynamicMenuItems: DynamicMenuItem[] = await dynamicMenuItemsReq();

    // patch dynamic Menu Item
    dynamicMenuPatch(dynamicMenuItems, patchedMenuData, access);

    for (let extraAppCodeItem of extraReportAppCodes) {
      // 对 highlight 类型的自定义报表做处理
      let waitForPatchMenuData = [];

      let extraAppCodeResponse: RespVO<any[]> = await masterResponseReq(
        extraAppCodeItem?.appCode,
      );

      if (
        extraAppCodeResponse.statusCode === 200 &&
        extraAppCodeResponse.code === 0
      ) {
        highlightMenuRoutePatch(
          extraAppCodeResponse?.data,
          patchedMenuData,
          extraAppCodeItem,
        );
      }
    }

    console.log('patchedMenuData', patchedMenuData);

    // 设定一个Global
    global['patchedMenuData'] = patchedMenuData;

    setPatchedMenuData(patchedMenuData);

    menuPatched.current = true;
  };

  const processCliDeptWithHospCode = async () => {
    let cliDeptsAndHospitalsResponse: RespVO<HierarchyItem[]> =
      await uniCommonService('Api/Sys/HospHierarchySys/GetHierarchies', {
        method: 'POST',
      });
    if (
      cliDeptsAndHospitalsResponse?.code === 0 &&
      cliDeptsAndHospitalsResponse?.statusCode === 200
    ) {
      return cliDeptsAndHospitalsResponse?.data?.filter(
        (item) => item?.HierarchyType, // 改个判断方式 因为有些医疗组也没有ParentId...
      );
    } else {
      return [];
    }
  };

  const renderBreadcrumb = () => {
    let onlyOneHeaderId =
      getHeaderIds(access, patchedMenuData, headerMenu)?.length <= 1;

    return getBreadcrumbFromMenuData(location.pathname, onlyOneHeaderId).map(
      (crumbItem, index) => {
        return (
          <Breadcrumb.Item key={crumbItem?.url}>
            {crumbItem?.url ? (
              <Link to={crumbItem?.url}>{crumbItem?.name}</Link>
            ) : (
              <span>{crumbItem?.name}</span>
            )}
          </Breadcrumb.Item>
        );
      },
    );
  };

  const tableStripeStyleVariable = tableStripeConfiguration();

  const isFullScreenRoute = location?.pathname?.endsWith(fullscreenPathSuffix);

  const AutoLogoutWrapper =
    enableAutoLogout === true ? AutoLogoutLayout : React.Fragment;

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <ConfigProvider locale={zhCN}>
        <AutoLogoutWrapper>
          <Layout
            id={'root-container'}
            className={'root-container'}
            style={tableStripeStyleVariable}
          >
            {!isFullScreenRoute && (
              <MenuSider
                headerMenu={headerMenu}
                menuData={patchedMenuData}
                access={access}
                menuOrder={
                  initialState?.userInfo?.Preferences?.MenuOrder?.MenuOrder
                }
              />
            )}
            <Layout className={'content-container'}>
              {!isFullScreenRoute && (
                <SiteHeader
                  menuData={patchedMenuData}
                  headerMenu={headerMenu}
                  access={access}
                  homeUrl={
                    initialState?.userInfo?.Preferences?.HomePage?.Default ||
                    defaultLandingPage
                  }
                  menuOrder={
                    initialState?.userInfo?.Preferences?.MenuOrder?.MenuOrder
                  }
                  userInfo={initialState?.userInfo}
                />
              )}
              <Content
                id={'site-layout-content'}
                className="site-layout-content"
              >
                {location.pathname !== '/index' && !isFullScreenRoute && (
                  <Breadcrumb>{renderBreadcrumb()}</Breadcrumb>
                )}

                <div id={'content'} className={'content'}>
                  {props.children}
                </div>
              </Content>
              {!isFullScreenRoute && (
                <Footer className="site-layout-footer">
                  {props.systemInfo?.CopyRight ||
                    '©Copyright 上海联众网络信息股份有限公司'}
                </Footer>
              )}

              {/*表格列修改组件*/}
              <TableColumnEditModal />
              {/*查询参数修改组件*/}
              <QueryParameterConfigurationEditModal />
              {/*拍照*/}
              <CameraWithGallery />

              <DmrAod />
              <DoctorMedicalRecordIndex />
            </Layout>
          </Layout>
        </AutoLogoutWrapper>
      </ConfigProvider>
      <ColumnModule />

      {minChromeVersion !== false && <BrowserCheck />}
    </ErrorBoundary>
  );
};

export default RootLayout;
