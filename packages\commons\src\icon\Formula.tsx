/**
 * @file Formula 公式
 * <AUTHOR> Generated by IconPark
 */

/* tslint:disable: max-line-length */
/* eslint-disable max-len */
import React from 'react';
import { ISvgIconProps, IconWrapper } from './runtime';

export default IconWrapper('formula', true, (props: ISvgIconProps) => (
  <svg width={props.size} height={props.size} viewBox="0 0 48 48" fill="none">
    <path
      d="M40 9L37 6H8L26 24L8 42H37L40 39"
      stroke={props.colors[0]}
      strokeWidth={props.strokeWidth}
      strokeLinecap={props.strokeLinecap}
      strokeLinejoin={props.strokeLinejoin}
    />
  </svg>
));
