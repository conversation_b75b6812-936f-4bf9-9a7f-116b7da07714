@import '~@uni/commons/src/style/variables.less';

.combo-query-override-container {
  /* Override Ant Design's default hidden behavior */
  .ant-tabs-tabpane.ant-tabs-tabpane-hidden {
    display: block !important;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    /* Transform for inactive tabs - positioned to the right */
    transform: translateX(100%);
    opacity: 0;

    /* Ensure it doesn't interfere with interactions */
    pointer-events: none;

    /* Add transition for smooth animation */
    transition: transform 0.3s ease, opacity 0.3s ease;
  }

  /* Active tab pane styling */
  .ant-tabs-tabpane:not(.ant-tabs-tabpane-hidden) {
    transform: translateX(0);
    opacity: 1;
    transition: transform 0.3s ease, opacity 0.3s ease;
  }

  /* Make sure the tabs content container has positioning context */
  .ant-tabs-content-holder {
    position: relative;
    overflow: hidden;
    min-height: 500px; /* Adjust based on your content */
  }

  /* Inactive tab styling */
  .ant-tabs-tab:not(.ant-tabs-tab-active) {
    transform: translateY(0) scale(1);
    opacity: 1;
    transition: all 0.3s;
  }

  /* Active tab styling */
  .ant-tabs-tab.ant-tabs-tab-active {
    transform: translateY(0) scale(1);
    opacity: 1;
    transition: all 0.3s;
  }

  .header-args-container {
    //width: 80%;
    margin-right: 10px;
  }

  .header-operations-container {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .metric-detail-extra-content-container {
    display: flex;
    flex-direction: row;
    //justify-content: flex-end;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    padding: 0px 0px 0px 10px;

    .ant-radio-button-wrapper {
      border-radius: 5px !important;
      margin-right: 10px;
    }

    .normal-radio-item {
    }

    .icde-oper-radio-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-right: 10px;
    }

    .extra-operations-container {
      display: flex;
      flex-direction: row;
      align-items: center;

      .extra-operation-item {
        width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10px;
      }

      .extra-operation-item-icon {
        width: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 5px;
        cursor: pointer;
      }

      .extra-operation-item-disabled {
        cursor: not-allowed !important;
      }
    }

    .export-operation-separator {
      height: 20px;
      width: 1px;
      background: #c9c9c9;
      margin: 0px 10px 0px 20px;
    }
  }

  .detail-data-container,
  .group-data-container {
    display: flex;
    flex-direction: column;
    //height: 100%;
    //max-width: 40%;
    //min-width: 30%;
    padding: 0px 10px 10px 10px;
    background: #ffffff;

    .tanstack-table-container
      .ant-table.ant-table-middle
      .ant-table-tbody
      > tr
      > td,
    .tanstack-table-container
      .ant-table.ant-table-middle
      .ant-table-thead
      > tr
      > th {
      padding: 6px 6px;
      transition: width, min-width 0.5s ease;
    }

    .tanstack-table-container .ant-table-tbody > tr.ant-table-row:hover > td {
      background: #eeeeee;
    }

    #tanstack-table-container {
      height: 100%;

      table {
        height: 100%;
      }

      .no-wrap {
        span {
          white-space: nowrap;
        }
      }
    }
  }
}

.combo-query-tab-container {
  display: flex;
  flex-direction: column;
  border: 1px solid @border-color;
  border-radius: 10px;

  .ant-tabs-nav-list {
    margin: 0px 5px;
  }

  .ant-tabs-tab {
    padding: 16px 16px;
  }

  .expr-preview-separator {
    height: 20px;
    width: 1px;
    min-width: 1px;
    background: @border-color;
    margin: 0px 8px;
  }

  .operation-item {
    min-width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ant-tabs-content,
  .ant-tabs-tabpane {
    height: 100%;
  }
}
