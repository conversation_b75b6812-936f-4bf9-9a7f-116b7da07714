import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { UniSelect } from '@uni/components/src';
import { Col, Row, Tabs } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import '../index.less';
import DrillDownDiseaseStructure from '@/components/DrillDownDiseaseStructure/index';
import Stats from '@/components/stats/index';
import { SdCompositionNormalStat } from '@/constants';
import { RespVO } from '@uni/commons/src/interfaces';
import SingleColumnTable from '@/components/SingleColumnTable/index';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';

const SdComposition = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, MedTeams } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('sdComposition');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedItem, setSelectedItem] = useState(undefined);

  const [selectOpts, setSelectOpts] = useState([]);

  // tab 使用下拉框数据
  const {
    data: SdComposition,
    loading: SdCompositionLoading,
    run: SdCompositionReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        'Api/v2/Drgs/MedTeamSdComposition/BundledSdComposition',
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.length) {
            setSelectOpts(
              _.orderBy(res.data, 'PatCnt', 'desc')?.map((d) => ({
                ...d,
                label: `${d?.SdName}`,
              })),
            );
            // 默认把第一个设置为selected
            if (!selectedItem) {
              setSelectedItem(
                res?.data?.at(0),
                // _.maxBy(res?.data, function (o) {
                //   return o.PatCnt;
                // }),
              );
            }
          } else {
            setSelectOpts([]);
          }
          return res?.data;
        }
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
      MedTeams,
    };
    setTableParams(params);
    SdCompositionReq(params);
  }, [dateRange, hospCodes, MedTeams]);

  useEffect(() => {
    Emitter.on(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK, (record) => {
      setSelectedItem(record);
      setActiveKey('statistic');
    });

    return () => {
      Emitter.off(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK);
    };
  }, []);

  let tabItems = [
    {
      key: 'sdComposition',
      label: '重点监控病种',
      children: (
        <DrillDownDiseaseStructure
          label="重点监控病种"
          tableParams={tableParams}
          filterColumns={[
            'SdName',
            'PatCnt',
            'AvgInPeriod',
            'AvgTotalFee',
            'MedicineFeeRatio',
            'MaterialFeeRatio',
            'AvgPreOperPeriod',
            'AvgPostOperPeriod',
            'DeathRatio',
          ]}
          compositionApi={
            'Api/v2/Drgs/MedTeamSdComposition/BundledSdComposition'
          }
          dataSource={selectOpts}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.SdName,
              args: {
                ...tableParams,
                SdCode: record?.SdCode,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/SdDetails',
              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'statistic',
      label: '科室综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Drgs/MedTeamSdComposition/BundledSdComposition`}
              trendApi={`Api/v2/Drgs/MedTeamSdComposition/SdCompositionTrend`}
              columns={
                selectedItem?.SdType !== '0'
                  ? [
                      ...SdCompositionNormalStat,
                      {
                        contentData: 'AvgPreOperPeriod',
                        clickable: true,
                        footerYoy: true,
                      },
                      {
                        contentData: 'AvgPostOperPeriod',
                        clickable: true,
                        footerYoy: true,
                      },
                    ]
                  : SdCompositionNormalStat
              }
              defaultSelectItem={'PatCnt'}
              type="col-xl-8"
              chartHeight={300}
              tabKey={activeKey}
              useGlobalState
              level="adrg"
              tableParams={tableParams}
              selectedTableItem={selectedItem}
              extraApiArgs={{
                SdCode: selectedItem?.SdCode,
                SdCodes: selectedItem?.SdCode ? [selectedItem?.SdCode] : [],
              }}
            />
          </Col>
          <SingleColumnTable
            title="该病种医疗组分布"
            args={{
              api: `Api/v2/Drgs/MedTeamSdComposition/SdCompositionByMedTeam`,
              extraApiArgs: {
                SdCode: selectedItem?.SdCode,
                SdCodes: selectedItem?.SdCode ? [selectedItem?.SdCode] : [],
              },
            }}
            colSpan={{ span: 24 }}
            type="table"
            tableParams={tableParams}
            dictData={dictData}
            category="MedTeamName"
            visibleValueKeys={['MedTeamName', 'PatCnt']}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName,
                args: {
                  ...tableParams,
                  MedTeams: [record?.MedTeam],
                  SdCode: selectedItem?.SdCode,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/SdDetails',
                dictData: dictData, // 传入
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'medTeam_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <SingleColumnTable
          title="该病种医生分布"
          args={{
            api: 'Api/v2/Drgs/DoctorSdComposition/SdCompositionByDoctor',
            extraApiArgs: {
              SdCode: selectedItem?.SdCode,
            },
          }}
          tableParams={tableParams}
          dictData={dictData}
          type="table"
          category="DoctorName"
          visibleValueKeys={[
            'DoctorName',
            'PatCnt',
            'AvgInPeriod',
            'AvgTotalFee',
            'MedicineFeeRatio',
            'MaterialFeeRatio',
            'AvgPreOperPeriod',
            'AvgPostOperPeriod',
            'DeathRatio',
          ]}
          colSpan={{ span: 24 }}
          select={{
            dataKey: 'DoctorType',
            valueKey: 'DoctorType',
            allowClear: false,
            defaultSelect: true,
          }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.DoctorName,
              args: {
                ...tableParams,
                DoctorCodes: [record?.DoctorCode],
                DoctorType: (record?.DoctorType as string)?.toLocaleUpperCase(),
                SdCode: selectedItem?.SdCode,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/SdDetails',
              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
        tabBarExtraContent={{
          right: activeKey !== 'sdComposition' && (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <label>当前病种：</label>
              <UniSelect
                width={300}
                showSearch
                dataSource={selectOpts}
                value={selectedItem?.SdCode}
                onChange={(value) => {
                  setSelectedItem(selectOpts?.find((d) => d?.SdCode === value));
                }}
                allowClear={false}
                optionNameKey={'label'}
                optionValueKey={'SdCode'}
                enablePinyinSearch={true}
                fieldNames={{
                  // label: 'ChsDrgName',
                  value: 'SdCode',
                }}
              />
            </div>
          ),
        }}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default SdComposition;
