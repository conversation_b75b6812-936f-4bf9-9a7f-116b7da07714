import React, { CSSProperties, ReactNode } from 'react';

export interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

export interface LayoutHeightWidth {
  h?: number;
  w?: number;
  data?: {
    itemClassName?: string;
  };
}

export interface IGridItemData {
  prefix?: string;
  key?: string;
  desc?: string;
  suffix?: string | ReactNode;
  component?: string;
  props?: any;
  dataKey?: string;
  itemClassName?: string;
  itemStyle?: CSSProperties;

  required?: boolean;

  formKeyPrefix?: string[];
}

export interface IGridItem {
  x?: number;
  offsetX?: number;
  h?: number;
  w: number;
  data: IGridItemData;
  lg?: LayoutHeightWidth;
  md?: LayoutHeightWidth;
  sm?: LayoutHeightWidth;
  xs?: LayoutHeightWidth;
  xxs?: LayoutHeightWidth;
  visible?: boolean;
}

export interface SubmitResult {
  DmrCardId?: number;
  Extra?: any;
  MaxCheckErrorType?: string;
  Reviews?: any[];
  ScoreLevel?: string;
  SucceededFlag?: boolean;
  TotalScore?: number;
}

export interface PreCheckRule {
  keys: string[];
  description: string;
  enable: boolean;
  ruleKey: string;
}
