import React, { useEffect, useRef, useState } from 'react';
import { Dropdown, Input, Select, Spin, Form, Tooltip } from 'antd';
import './index.less';
import { v4 as uuidv4 } from 'uuid';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { IcdeOperItem, IcdeOperResp } from '@/pages/chs/network/interfaces';
import ex from 'umi/dist';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { getSelectorDropdownContainerNode } from '@/utils/utils';
import { UniAntdSelect } from '@uni/components/src';
import {
  getArrowUpDownEventKey,
  getDeletePressEventKey,
} from '@uni/grid/src/utils';
import { operationExtraMap } from '@uni/grid/src/components/icde-oper-input/input';
import { operTypeToClassName } from '../oper-table';
import { isEmptyValues } from '@uni/utils/src/utils';
import { FormTableItemBaseProps } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import cloneDeep from 'lodash/cloneDeep';
import { useDebounceFn } from 'ahooks';
import { IcdeOperKeyboardFriendlyDropdown } from '@uni/grid/src/components/dmr-select/keyboard';

const { Option } = Select;

const inputFocusNotSelectAll =
  (window as any).externalConfig?.['chs']?.inputFocusNotSelectAll ?? false;

const tableSelectorDropdownHeight = (window as any).externalConfig?.['chs']
  ?.tableSelectorDropdownHeight;

const enableKeyboardFriendlySelect =
  (window as any).externalConfig?.['chs']?.enableKeyboardFriendlySelect ??
  false;

const numberSelectItem =
  (window as any).externalConfig?.['chs']?.numberSelectItem ?? false;

export interface OperationFormKeysItem {
  [key: string]: string;
}

export interface OperationSelectProps extends FormTableItemBaseProps {
  form?: any;

  recordId: string;

  dataIndex: string;

  parentId?: string;

  formKeys?: OperationFormKeysItem; // 暂定

  componentId: string;

  onOperationSelect?: (data) => void;

  getPopupContainer?: (trigger) => HTMLElement;

  dropdownStyle?: React.CSSProperties;
  listHeight?: number;

  interfaceUrl: string;
  disabled?: boolean;

  // 为了兼容 置灰的手术 / 诊断用于立即搜索
  instantSelect?: boolean;

  rowDataKey?: string;

  dropdownAlign?: any;

  onChangeValueProcessor?: (value: any) => string;

  tableId?: string;

  numberSelectItem?: boolean;
}

export const removeOperationColorClassName = (rowItem: any) => {
  let waitForDeleteClassName = '';
  for (let classItem of rowItem?.classList) {
    if (classItem.startsWith('operation-tr-color')) {
      waitForDeleteClassName = classItem;
      break;
    }
  }
  if (waitForDeleteClassName) {
    rowItem?.classList?.remove(waitForDeleteClassName);
  }
};

const maxResultCount = 100;
const OperationSelect = (props: OperationSelectProps) => {
  const operInputRef = useRef(null);

  const [dataSource, setDataSource] = useState<IcdeOperItem[]>([]);

  const [offset, setOffset] = useState(0);
  const [recordTotal, setRecordTotal] = useState(0);

  const [loading, setLoading] = useState(false);
  const [keyword, setKeyword] = useState(undefined);

  const [hasSearched, setHasSearched] = useState(false);

  const operationCodeValue = Form.useWatch([props?.recordId, props?.dataIndex]);

  const [errorTooltipOpen, setErrorTooltipOpen] = useState(false);

  //  keyboard friendly
  const [optionOpen, setOptionOpen] = useState(false);
  const listRef = React.useRef(null);

  console.log('operationCodeValue', operationCodeValue);

  const setDataSourceOrNot = (keyword: string, searchKeyword: string) => {
    if (props?.instantSelect) {
      return !!searchKeyword;
    } else {
      return keyword && searchKeyword;
    }
  };

  const getOperationDataSource = async (offset, searchKeyword) => {
    if (!searchKeyword) {
      return;
    }

    if (
      offset !== 0 &&
      (dataSource.length >= recordTotal || offset >= recordTotal)
    ) {
      // 表示 全量数据
      return;
    }

    setLoading(true);
    let data = {
      Keyword: searchKeyword?.trim(),
      SkipCount: offset,
      HasInsurCompare: true,
      HasHqmsCompare: true,
      HasDrgsCompare: true,
      MaxResultCount: maxResultCount,
    };

    let operationDataSourceResponse: RespVO<IcdeOperResp> =
      await uniCommonService(props?.interfaceUrl, {
        params: data,
      });

    if (operationDataSourceResponse?.code === 0) {
      if (operationDataSourceResponse?.statusCode === 200) {
        let existDataSource = offset === 0 ? [] : dataSource.slice();
        if (setDataSourceOrNot(keyword, searchKeyword)) {
          setDataSource(
            existDataSource.concat(
              operationDataSourceResponse?.data?.Data?.map((item) => {
                return {
                  ...item,
                  key: item?.Code,
                  value: item?.Code,
                  label: `${item?.Code} ${item?.Name}`,
                  OperCode: item?.Code,
                  OperName: item?.Name,
                };
              }) || [],
            ),
          );
          setOffset(offset + maxResultCount);

          setRecordTotal(operationDataSourceResponse?.data?.RecordsTotal);
        } else {
          setDataSource([]);
          setOffset(0);
          setRecordTotal(0);
        }
      }
    }

    setLoading(false);
  };

  const { run: getOperationDataSourceWithDebounce } = useDebounceFn(
    (offset, keyword) => {
      getOperationDataSource(offset, keyword);
    },
    {
      wait: 200,
    },
  );

  const onOperationPopUpScroll = (event) => {
    let contentElement = event.target;
    let scrollNearlyEnd =
      Math.abs(
        contentElement.scrollHeight -
          contentElement.scrollTop -
          contentElement.clientHeight,
      ) < 100;
    if (scrollNearlyEnd && !loading) {
      getOperationDataSource(offset, keyword);
    }
  };

  const onOperSelectClear = () => {
    let fieldsValue = {};
    if (props?.formKeys) {
      // 第一个是文本 第二个是编码
      Object.keys(props?.formKeys).forEach((key) => {
        fieldsValue[key] = '';
      });
    }
    if (props?.form) {
      if (props?.recordId) {
        let recordFieldValue = cloneDeep(fieldsValue);
        Object.keys(recordFieldValue)?.forEach((key) => {
          props?.form.setFieldValue(
            [props?.recordId, key],
            recordFieldValue[key],
          );
        });
      } else {
        props?.form.setFieldsValue(fieldsValue);
      }
    }

    if (props?.onChangeValueProcessor) {
      fieldsValue = props?.onChangeValueProcessor(fieldsValue);
    }

    props?.onChange && props?.onChange(fieldsValue);

    // 清一下dataSource  keyword offset total
    setDataSource([]);
    setHasSearched(false);
    setKeyword(undefined);
    setOffset(0);
    setRecordTotal(0);

    setOptionOpen(false);

    listRef?.current?.clear();

    props?.onOperationSelect && props?.onOperationSelect(fieldsValue);
  };

  // TODO 当这个页面显示的时候会出问题的  select option 是没有的  需要在做定制

  const onOperSelect = (value: string, externalDataSource?: any[]) => {
    let fieldsValue = {};
    let currentSelected = (externalDataSource ?? dataSource)?.find(
      (item) => item.Code === value,
    );
    if (currentSelected) {
      if (props?.formKeys) {
        // 第一个是文本 第二个是编码
        Object.keys(props?.formKeys).forEach((key) => {
          // 微创手术这个单独做处理
          if (key === 'OperExtra') {
            fieldsValue[key] = Object.keys(operationExtraMap)?.filter((key) => {
              if (key === 'InsurIsObsolete') {
                return currentSelected?.['IsObsolete'] ?? false;
              } else {
                return currentSelected?.[key] ?? false;
              }
            });
          }
          // 首页手术 不处理
          else if (key === 'SrcOperCode' || key === 'SrcOperName') {
            fieldsValue[key] = fieldsValue[key] || undefined;
          } else if (key === 'RowClassName') {
            if (currentSelected?.OperType) {
              let operTypeClassName =
                operTypeToClassName?.[currentSelected?.OperType];
              if (operTypeClassName) {
                fieldsValue[key] = operTypeClassName;

                if (props?.rowDataKey) {
                  let rowItem = document.querySelector(
                    `div#${props?.parentId} tr[data-row-key='${props?.rowDataKey}']`,
                  );
                  if (rowItem) {
                    removeOperationColorClassName(rowItem);
                    rowItem?.classList?.add(operTypeClassName);
                  }
                }
              } else {
                if (props?.rowDataKey) {
                  let rowItem = document.querySelector(
                    `tr[data-row-key='${props?.rowDataKey}']`,
                  );
                  if (rowItem) {
                    removeOperationColorClassName(rowItem);
                  }
                }
              }
            }
          } else {
            fieldsValue[key] = currentSelected?.[props?.formKeys[key]];
          }
        });
      }
      if (props?.form) {
        if (props?.recordId) {
          let recordFieldValue = cloneDeep(fieldsValue);
          Object.keys(recordFieldValue)?.forEach((key) => {
            props?.form.setFieldValue(
              [props?.recordId, key],
              recordFieldValue[key],
            );
          });
        } else {
          props?.form.setFieldsValue(fieldsValue);
        }
      }

      if (props?.onChangeValueProcessor) {
        fieldsValue = props?.onChangeValueProcessor(fieldsValue);
      }

      props?.onChange && props?.onChange(fieldsValue);

      setDataSource([]);
      setErrorTooltipOpen(false);
      setHasSearched(false);
      setOffset(0);
      setRecordTotal(0);
      setKeyword(undefined);

      setTimeout(() => {
        operInputRef?.current?.focus();
      }, 0);
    }
  };

  useEffect(() => {
    setTimeout(() => {
      let dropDownElement = document.getElementById(
        'oper-select-dropdown-container',
      );
      if (dropDownElement) {
        let firstLi = dropDownElement?.querySelector('ul li');
        if (firstLi) {
          (firstLi as any)?.focus();
          setTimeout(() => {
            (firstLi as any)?.scrollIntoView({
              block: 'center',
              inline: 'center',
            });
          }, 100);
        }
      }
    }, 0);
  }, [dataSource]);

  const operKeyboardFriendlyProps = numberSelectItem
    ? {
        dropdownRender: (menu: any) => {
          return (
            <IcdeOperKeyboardFriendlyDropdown
              type={'Oper'}
              enableKeyboardFriendlySelect={enableKeyboardFriendlySelect}
              listRef={listRef}
              interfaceUrl={
                props?.interfaceUrl ?? 'Api/Insur/InsurSearch/OperReorder'
              }
              value={props?.value}
              onSelectChange={(value, item, dataSources) => {
                onOperSelect(value, dataSources);
              }}
              optionOpen={optionOpen}
              setOptionOpen={setOptionOpen}
              searchKeyword={keyword}
              setSearchKeyword={setKeyword}
              instantSelect={props?.instantSelect}
            />
          );
        },
        open: optionOpen && !isEmptyValues(keyword),
        onDropdownVisibleChange: (visible: boolean) => setOptionOpen(visible),
        onKeyboardNumberSelect: (event: any, index: any) => {
          listRef?.current?.onKeyboardNumberSelect(event, index);
        },
        onKeyboardPageFlip: (event: any) => {
          listRef?.current?.onKeyboardPageFlip(event);
        },
        onPopupScroll: () => {},
      }
    : {};

  return (
    <div className={'form-content-item-container'}>
      <Tooltip
        open={errorTooltipOpen}
        color={'rgba(235, 87, 87, 0.85)'}
        title={'手术不存在，请检查后重新选择'}
      >
        <UniAntdSelect
          showArrow={false}
          id={`formItem#${props?.componentId}#OperSelect`}
          className={`select operation-item-container`}
          value={props?.value}
          showSearch
          showAction={props?.instantSelect ? ['focus'] : []}
          allowClear={false}
          disabled={props?.disabled}
          getPopupContainer={(trigger) =>
            (props.getPopupContainer && props?.getPopupContainer(trigger)) ||
            getSelectorDropdownContainerNode()
          }
          onInputKeyDown={(event) => {
            if (hasSearched) {
              if (event.key === 'Enter' && dataSource?.length === 0) {
                setErrorTooltipOpen(true);
                event.preventDefault();
                event.stopPropagation();
              }
            }
          }}
          enterSwitch={true}
          contentEditable={!inputFocusNotSelectAll}
          dropdownStyle={props?.dropdownStyle || {}}
          listHeight={tableSelectorDropdownHeight ?? props?.listHeight}
          placeholder={'请选择手术'}
          onSearch={(searchKeyword) => {
            if (enableKeyboardFriendlySelect === true) {
              setKeyword(searchKeyword);
              return;
            }

            if (numberSelectItem !== true) {
              setHasSearched(true);
              if (searchKeyword) {
                if (searchKeyword !== keyword) {
                  // fetch
                  getOperationDataSourceWithDebounce(0, searchKeyword);
                } else {
                  getOperationDataSourceWithDebounce(offset, searchKeyword);
                }
              } else {
                setDataSource([]);
                setOffset(0);
                setRecordTotal(0);
              }
            }
            setKeyword(searchKeyword);
          }}
          onPopupScroll={onOperationPopUpScroll}
          filterOption={false}
          optionLabelProp={'value'}
          dropdownMatchSelectWidth={false}
          dropdownAlign={props?.dropdownAlign}
          notFoundContent={loading ? <Spin size="small" /> : null}
          onFocus={() => {
            if (props?.instantSelect) {
              if (!isEmptyValues(props?.value)) {
                setKeyword(props?.value);
                if (numberSelectItem !== true) {
                  getOperationDataSource(0, props?.value);
                }
              }
            }

            listRef?.current?.onFocus(null, props?.value);
          }}
          onBlur={(event) => {
            if (numberSelectItem !== true) {
              setTimeout(() => {
                setDataSource([]);
                setHasSearched(false);
                setErrorTooltipOpen(false);
                setKeyword('');
                setOffset(0);
                setRecordTotal(0);
              }, 0);
            }
          }}
          onClear={() => {
            onOperSelectClear();
          }}
          onKeyDown={(event) => {
            console.log('onKeyDown', event?.key);

            // 当且仅当
            if (numberSelectItem === true) {
              listRef?.current?.onKeyDown(event);
            }

            if (
              props?.tableId &&
              (event as any)?.hosted !== true &&
              event?.ctrlKey === false
            ) {
              if (event?.key === 'ArrowUp') {
                Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
                  event: event,
                  type: 'UP',
                  trigger: 'selectkeydown',
                });
              }

              if (event?.key === 'ArrowDown') {
                Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
                  event: event,
                  type: 'DOWN',
                  trigger: 'selectkeydown',
                });
              }
            }
          }}
          onSelect={(value) => {
            onOperSelect(value);
          }}
          options={
            numberSelectItem
              ? [
                  {
                    Code: 'Default',
                    Name: 'Default',
                  },
                ]
              : dataSource
          }
          dumbOnComposition={true}
          mousedownOptionOpen={false}
          doubleClickPopUp={false}
          numberSelectItem={numberSelectItem}
          // keyboard friendly
          {...operKeyboardFriendlyProps}
        />
      </Tooltip>
    </div>
  );
};

export default OperationSelect;
