import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import _, { debounce } from 'lodash';
import {
  Affix,
  Button,
  Card,
  Drawer,
  Form,
  Input,
  message,
  Radio,
  Spin,
  Steps,
  Collapse,
  Row,
  Col,
  List,
  Menu,
  Tag,
  Anchor,
  Tooltip,
  Avatar,
  Badge,
  Modal,
  FormInstance,
} from 'antd';
import { v4 as uuidv4 } from 'uuid';
import './index.less';
import 'react-grid-layout/css/styles.css';
import { dynamicComponentsMap } from '@/utils/dynamicComponents';
import { contentData } from '@/pages/chs/fields/base';
import { headerData } from '@/pages/chs/fields/header';
import RightMenu, { TopMenuItem } from '@/pages/chs/components/right-menu';
import mergeWith from 'lodash/mergeWith';
import { DmrSearch } from '@/pages/chs/components/dmr-search';
import { useRequest } from 'umi';
import {
  DictionaryItems,
  RespVO,
  ShortcutItem,
  TableColumns,
} from '@uni/commons/src/interfaces';
import {
  DoubleLeftOutlined,
  DoubleRightOutlined,
  EditOutlined,
  FullscreenOutlined,
  LoadingOutlined,
  SaveOutlined,
  VerticalLeftOutlined,
} from '@ant-design/icons';
import { useLocation } from 'umi';
import qs from 'qs';
import {
  getCardInfoV2,
  getChsIndexLayout,
  insurBundleThirdPartyCheckReq,
} from '@/pages/chs/network/get';
import { saveCardInfoV2 } from '@/pages/chs/network/save';
import PreCheckResult from '@/pages/chs/components/right-container';
import { useModel } from 'umi';
import {
  deleteSpecialKeysProcessor,
  deleteSpecialKeysProcessorByPrefix,
  formKeyToNavigationId,
  noClickElementsClassName,
} from '@/pages/chs/constants';
import { tableHotKeys } from '@uni/grid/src/common';
import { formValidation } from '@/pages/chs/rules';
import { useHotkeys } from 'react-hotkeys-hook';
import { history } from 'umi';
import ShortcutsHelpModal from '@uni/grid/src/components/help-modal';
import { withResizeDetector } from 'react-resize-detector';
import {
  defaultValueToFormValueBeforeValidation,
  isInElementViewport,
} from '@/pages/chs/utils';
import {
  defaultPageUpDownHandler,
  getDeletePressEventKey,
  getTransformHeightByElementId,
} from '@uni/grid/src/utils';
import { PreCheckIcon } from '@uni/components/src/custom-icon/icon';
import { ModalLoading } from '@uni/components/src';

import {
  Breakpoint,
  ColumnOptions,
  GridStack,
  GridStackEvent,
  GridStackEventHandlerCallback,
  GridStackNode,
} from '@uni/grid/src/core/gridstack';
import '@uni/grid/src/core/gridstack.css';
import '@uni/grid/src/style/index';
import {
  getSeparatorKey,
  onGridColumnChange,
} from '@uni/grid/src/core/custom-utils';
import {
  clearScrollingTitleTimeout,
  isEmptyValues,
  scrollTitle,
} from '@uni/utils/src/utils';
import { generateLayout } from '@/utils/layouts';
import { auditValidation } from '@/pages/chs/rules/audit';
import { GridItem } from '@uni/grid/src/common/item';
import { ChsProcessor } from '@/pages/chs/processors/processors';
import { CHS_LAYOUT_COLUMNS as LAYOUT_COLUMNS } from '@uni/grid/src/common';
import GridItemContext from '@uni/commons/src/grid-context';
import ChsQueryHeader from '@/pages/chs/components/chs-query-header';

const externalChsConfig = (window as any).externalConfig?.['chs'];

const updateLayout = externalChsConfig?.['updateLayout'] ?? false;

const checkOnSave = externalChsConfig?.['checkOnSave'] ?? false;

const inputFocusNotSelectAll =
  externalChsConfig?.['inputFocusNotSelectAll'] ?? false;

const noEditSave = externalChsConfig?.['noEditSave'] ?? false;

const leftRightKeySwitch = externalChsConfig?.['leftRightKeySwitch'] ?? false;

const dmrPageUpDownSwitchModule =
  externalChsConfig?.['dmrPageUpDownSwitchModule'] ?? false;

const timescapeEnterSwitch =
  externalChsConfig?.['timescapeEnterSwitch'] ?? false;

const tableItemFocusTop = externalChsConfig?.['tableItemFocusTop'] ?? false;

const centerInputActivate = externalChsConfig?.['centerInputActivate'] ?? false;

const inEditRemainScrollPosition =
  externalChsConfig?.['inEditRemainScrollPosition'] ?? false;

const customShortcuts =
  (window as any).externalConfig?.['common']?.['customShortcuts'] ?? {};

const forceScrollToCenterDistance =
  externalChsConfig?.['forceScrollToCenterDistance'] ?? 0;

const preCheckContainerPosition =
  externalChsConfig?.['preCheckContainerPosition'] ?? 'right';

const showThirdPartyCheckBtn =
  externalChsConfig?.['showThirdPartyCheckBtn'] ?? false;

const errorKeyClick = true;

export const autoClick = false;

export const forceClickIds = ['department-transfer-container'];

export const ROW_HEIGHT = 42;

export const topMenuKeys: TopMenuItem[] = [
  {
    key: 'PatName',
    title: '基本',
    focusId: 'Input#PatName',
  },
  {
    key: 'DiagnosisTcmItem',
    title: '诊疗',
    focusId: 'formItem#DiagnosisTcmCode#IcdeSelect',
  },
  {
    key: 'diagnosisTable',
    title: '诊断',
    focusId: 'formItem#IcdeCode#0#IcdeSelect',
  },
  {
    key: 'operationTable',
    title: '手术',
    focusId: 'formItem#OperCode#0#OperSelect',
  },
  {
    key: 'icuTable',
    title: '重症',
    focusId: 'formItem#IcuCode#0#Input#rc-select',
  },
  {
    key: 'bloodTable',
    title: '输血',
    focusId: 'formItem#BldCat#0#Input#rc-select',
  },
  {
    key: 'paymentTable',
    title: '收费',
  },
];

// table 用于数据的key
/**
 * 由于form表单中的table 会出现 刷新问题 因此设定一个 form item 用于 收集数据
 */
const hiddenFormItemKeys = [
  'diagnosis-table',
  'diagnosisTable',
  'operation-table',
  'operationTable',
  'icu-table',
  'icuTable',
  'blood-table',
  'bloodTable',
  'fee-charge-table',
  'feeChargeTable',
  'payment-table',
  'paymentTable',
  'formEdited',

  // UniqueIds
  'IcdeOtpsUniqueId',
  'IcdeTcmsUniqueId',
  'IcdeAdmsUniqueId',
  'IcdeDamgsUniqueId',
  'IcdePathosUniqueId',
];

const noCenterElement = ['tr'];

const focusSelectAllClass = [
  'ant-input',
  'compact-date-container',
  'ant-select-selection-search-input',
];

// 1920 * 1080
// 1680 * 1050
// 1440 * 900
// 1366 * 768
// 1280 * 800
// 1024 * 768

export const BREAKPOINTS_LABEL = {
  lg: {
    label: '大屏',
    resolution: '1920*1080',
    width: '100%',
  },
  md: {
    label: '中屏',
    resolution: '1600 * 900 右侧推出',
    width: 918,
  },
  sm: {
    label: '小屏',
    resolution: '1366 * 768',
    width: 790,
  },
};

const noRespondChangeFocusKeys = [];

let focusableElements = [
  'input:not([readonly]):not([disabled])',
  '[tabindex]:not([disabled]):not([tabindex="-1"]):not(tr)',
  "button[class*='ant-switch']",

  // 确保这个在最后
  '#department-transfer-container',
];

const yLayout = {};

export const operations = (
  viewMode: boolean,
  currentHisId: string,
  searchedHisIds: string[],
) => [
  {
    key: 'EDIT',
    title: viewMode ? '编辑' : '取消编辑',
    disabled: !!!currentHisId,
    icon: <EditOutlined />,
  },
  {
    key: 'SAVE',
    title: '保存',
    disabled: !!!currentHisId,
    icon: <SaveOutlined />,
  },
  {
    key: 'CHSGROUP_AUDIT',
    title: '预分组 & 审核',
    disabled: !!!currentHisId,
    icon: <PreCheckIcon />,
    enable: true,
    order: 4,
    color: '#F4AC4D',
  },
  {
    key: 'NEXT',
    title: '下一份',
    disabled: searchedHisIds?.length <= 0,
    icon: <VerticalLeftOutlined />,
  },
  {
    key: 'FULLSCREEN',
    title: '全屏',
    disabled: location?.pathname?.indexOf('/fullscreen') !== -1,
    icon: <FullscreenOutlined />,
  },
  {
    key: 'THIRD_PARTY_CHECK',
    title: '第三方质控',
    disabled: !!!currentHisId,
    enable: true,
    order: 7,
  },
];

export const defaultFieldValue = {
  formEdited: false,
};

let headerGridInstance: GridStack = undefined;
let contentGridInstance: GridStack = undefined;

const CHS = () => {
  const [form] = Form.useForm();

  // location search
  const location = useLocation();

  const [layouts, setLayouts] = useState({});

  const [currentHisId, setCurrentHisId] = useState('');

  const [originChsCardInfo, setOriginChsCardInfo] = useState<any>({});

  const [searchedHisIds, setSearchedHisIds] = useState<string[]>([]);

  const [rightContainerOpen, setRightContainerOpen] = useState(false);
  const [leftContainerOpen, setLeftContainerOpen] = useState(false);

  const [lastChsRecordPageReached, setLastChsRecordPageReached] =
    useState(false);

  const [viewMode, setViewMode] = useState(true);

  const [formChsCardInfo, setFormChsCardInfo] = useState(undefined);

  const [breakpoint, setBreakpoint] = useState('lg');

  const [chsOperatorConfig, setChsOperatorConfig] = useState<any>({});
  const [chsPreCheckModuleConfig, setChsPreCheckModuleConfig] = useState<any>(
    {},
  );

  const [headerLayouts, setHeaderLayouts] = useState({});

  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateFromMaster',
  );

  const [chsProcessorInstance] = useState(() => new ChsProcessor());

  const compositionStatusRef = useRef<boolean>(false);

  const operationClickDebounce = (operationClickFunc: any) => {
    return debounce(operationClickFunc, 3000, {
      leading: true,
      trailing: false,
    });
  };

  const breakpoints = (type: 'CONTENT' | 'HEADER'): Breakpoint[] => [
    {
      // 1920 * 1080
      c: LAYOUT_COLUMNS['lg'],
      w: 1508,
    },
    {
      // 1920 * 1080 右侧推出
      c: LAYOUT_COLUMNS['lg'],
      w: 1238,
    },
    {
      // 1600 * 900
      c: LAYOUT_COLUMNS['lg'],
      w: 1171,
    },
    {
      // 1600 * 900 右侧推出
      // layout: 'list' as any,
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER' ? headerLayouts : layouts,
        );
      },
      c: LAYOUT_COLUMNS['md'],
      w: 918,
    },
    {
      // 1440 * 800
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER' ? headerLayouts : layouts,
        );
      },
      c: LAYOUT_COLUMNS['md'],
      w: 1028,
    },
    {
      // 1440 * 800 推出
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER' ? headerLayouts : layouts,
        );
      },
      c: LAYOUT_COLUMNS['sm'],
      w: 790,
    },
    {
      // 1366 * 768
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER' ? headerLayouts : layouts,
        );
      },
      c: LAYOUT_COLUMNS['sm'],
      w: 954,
    },
  ];

  const shortcuts: ShortcutItem[] = [
    {
      key: 'f1',
      callback: (event) => {
        Emitter.emit(EventConstant.INSURANCE_HELP_MODAL);
      },
      enabled: true,
      description: '帮助',
    },
    {
      key: 'tab,ctrl+right,enter',
      callback: (event) => {
        if (compositionStatusRef.current === false) {
          Emitter.emit(EventConstant.CHS_TABLE_NEXT_KEY, {
            event: event,
            indexOffset: 1,
          });
        }
      },
      enabled: true,
      description: '下一项',
    },
    {
      key: 'right',
      callback: (event) => {
        if (compositionStatusRef.current === false) {
          Emitter.emit(EventConstant.CHS_TABLE_NEXT_KEY, {
            event: event,
            indexOffset: 1,
          });
        }
      },
      enabled: true,
      description: '下一项',
      // options: {
      //   enableOnContentEditable: false,
      // },
    },
    {
      key: 'shift+tab,ctrl+left',
      callback: (event) => {
        if (compositionStatusRef.current === false) {
          Emitter.emit(EventConstant.CHS_TABLE_NEXT_KEY, {
            event: event,
            indexOffset: -1,
          });
        }
      },
      enabled: true,
      description: '上一项',
    },
    {
      key: 'left',
      callback: (event) => {
        Emitter.emit(EventConstant.CHS_TABLE_PREVIOUS_KEY, {
          event: event,
          indexOffset: -1,
        });
      },
      enabled: true,
      description: '上一项',
      // options: {
      //   enableOnContentEditable: false,
      // },
    },
    {
      key: 'ctrl+e',
      callback: operationClickDebounce((event) => {
        if (currentHisId) {
          onOperationItemClick('EDIT');
        }
      }),
      enabled: !!currentHisId,
      description: '开始/取消 编辑',
    },
    {
      key: customShortcuts?.['SAVE'] || 'ctrl+s',
      callback: operationClickDebounce((event) => {
        if (currentHisId) {
          onOperationItemClick('SAVE');
        }
      }),
      enabled: !!currentHisId && !!originChsCardInfo,
      description: '保存',
    },
    {
      key: 'f3',
      callback: operationClickDebounce((event) => {
        if (currentHisId) {
          onOperationItemClick('CHSGROUP_AUDIT');
        }
      }),
      enabled: !!currentHisId && !!originChsCardInfo,
      description: '预分组 & 审核',
    },
    {
      key: 'f4',
      callback: operationClickDebounce((event) => {
        if (currentHisId) {
          onOperationItemClick('NEXT');
        }
      }),
      enabled: !!currentHisId && !!originChsCardInfo,
      description: '下一份',
    },

    {
      key: 'delete',
      callback: (event) => {
        onDeletePress(event);
      },
      enabled: true,
      description: '删除',
    },
    {
      key: 'pagedown',
      callback: (event) => {
        onPageUpDownPress(event, false);
      },
      enabled: true,
      description: '向下滚动',
    },
    {
      key: 'pageup',
      callback: (event) => {
        onPageUpDownPress(event, true);
      },
      enabled: true,
      description: '向上滚动',
    },

    // 菜单跳转
    {
      key: 'ctrl+1...9',
      onlyForHint: true,
      description: '菜单项跳转',
    },

    ...tableHotKeys,
  ];

  // hotkeys start
  // TODO 保存等快捷键
  shortcuts
    ?.filter((item) => item?.onlyForHint !== true)
    ?.map((item) => {
      useHotkeys(item.key, item.callback, {
        enableOnFormTags: true,
        enabled: item?.enabled,
        preventDefault: !(item?.key === 'left' || item?.key === 'right'),
        enableOnContentEditable: true,
        ...(item?.options ?? {}),
      });
    });

  // 菜单hotkeys
  topMenuKeys?.forEach((item, index) => {
    let key = `ctrl+${index + 1}`;
    useHotkeys(
      key,
      () => {
        Emitter.emit(EventConstant.CHS_LEFT_MENU_CLICK, index);
      },
      {
        enableOnFormTags: true,
        enabled: true,
        preventDefault: true,
        enableOnContentEditable: true,
      },
    );
  });
  // hotkeys end

  // 滚动title
  useEffect(() => {
    if (formChsCardInfo) {
      let scrollingTitle = buildScrollTitle();
      if (scrollingTitle) {
        clearScrollingTitleTimeout();
        scrollTitle(scrollingTitle);
      }
    }
  }, [formChsCardInfo]);

  useEffect(() => {
    if (viewMode === true) {
      // 清空所有的 border-active
      unBorderActiveElements();
    }
  }, [viewMode]);

  useEffect(() => {
    // setLoading(true);
    Emitter.emit('DMR_LAYOUT_LOADING', true);
    //header Layout

    let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');

    chsLayoutsGetReq(userInfo?.CliDepts?.at(0), userInfo?.HospCodes?.at(0), [
      'ChsOperatorsConfig',
      'ChsPreCheckModules',
    ]);

    // tab focus event
    Emitter.onMultiple(
      [EventConstant.CHS_TABLE_NEXT_KEY, EventConstant.CHS_TABLE_PREVIOUS_KEY],
      (data) => {
        let event = data?.event;

        console.log('NEXT PREVIOUS EVENT', event);
        // TODO modal确认 不吃事件
        if ((event?.target as any)?.tagName?.toLowerCase() === 'button') {
          if (event?.target?.parentElement?.className?.includes('modal')) {
            event?.target?.click && event?.target?.click();
          }
          return;
        }

        // 当且仅当 左右按键键按下的时候
        if (inputFocusNotSelectAll === false && event?.ctrlKey === false) {
          if (event?.target?.tagName?.toLowerCase() === 'input') {
            let uniqueHosted = false;
            // checkbox的话
            if (event?.target?.type === 'checkbox') {
              uniqueHosted = true;
              if (
                (event.key === 'ArrowRight' || event.key === 'ArrowLeft') &&
                leftRightKeySwitch === false
              ) {
                return;
              }
            }

            // 如果是select 无条件跳转
            if (event?.target?.className?.indexOf('ant-select') > -1) {
              // 如果是icde oper select 不能强制跳转
              let icdeOperSelect =
                event?.target?.id?.toLowerCase()?.indexOf('icdeselect') > -1 ||
                event?.target?.id?.toLowerCase()?.indexOf('operselect') > -1;
              let peopleSelect =
                event?.target?.id?.toLowerCase()?.indexOf('employee') > -1;
              if (!icdeOperSelect && !peopleSelect) {
                uniqueHosted = true;
                if (
                  (event.key === 'ArrowRight' || event.key === 'ArrowLeft') &&
                  leftRightKeySwitch === false
                ) {
                  return;
                }
              }
            }

            // 如果是timescape 日期控件 单独做设定
            if (event?.target?.id?.toLowerCase()?.indexOf('timescape') > -1) {
              uniqueHosted = true;

              if (event?.key === 'Enter' && timescapeEnterSwitch === true) {
                // 当在timescape里面并且是Enter的时候 不能吃了Enter 需要跳格子
                uniqueHosted = false;
              }

              // 表示当前 还在日期控件内 需要日期控件接管
              if (
                event?.['timeScapeNextIndex'] !== undefined &&
                leftRightKeySwitch === false
              ) {
                return;
              }
            }

            if (uniqueHosted === false) {
              let selectionStart = event?.target?.selectionStart;
              let selectionEnd = event?.target?.selectionEnd;
              let selectionLength = event?.target?.value?.substring(
                selectionStart,
                selectionEnd,
              )?.length;

              if (event?.key === 'ArrowRight') {
                // 还没到最后了
                if (leftRightKeySwitch === false) {
                  return;
                }

                // 如果都被选中了 同时格子内容长度不能为0 就不能换格子
                if (
                  event?.target?.value?.length !== 0 &&
                  selectionLength === event?.target?.value?.length
                ) {
                  return;
                }

                if (selectionEnd !== event?.target?.value?.length) {
                  return;
                }
              }

              if (event?.key === 'ArrowLeft') {
                if (leftRightKeySwitch === false) {
                  return;
                }
                // 如果都被选中了 同时格子内容长度不能为0 就不能换格子
                if (
                  event?.target?.value?.length !== 0 &&
                  selectionLength === event?.target?.value?.length
                ) {
                  return;
                }

                // 还没到最前了
                if (selectionStart !== 0) {
                  return;
                }
              }
            }
          }
        }

        event?.stopPropagation && event?.stopPropagation();
        event?.preventDefault && event?.preventDefault();

        let componentId = (event.target as any)?.id;

        if (componentId) {
          // 不触发
          if (
            noRespondChangeFocusKeys.find(
              (item) => componentId.split('#')?.at(0) === item,
            )
          ) {
            return;
          }

          let focusableSelector = focusableElements.join(',');
          if (data?.indexOffset === -1) {
            focusableSelector = focusableElements
              ?.slice(0, focusableElements?.length - 1)
              .join(',');
          }

          let formInputElements = document
            .getElementById('dmr-form-container')
            // ?.getElementsByTagName("input");
            ?.querySelectorAll(focusableSelector);

          if (formInputElements && formInputElements?.length > 0) {
            let formInputs = [].slice.call(formInputElements);
            let currentComponentIndex = formInputs.findIndex(
              (item) => item.id === componentId,
            );

            if (currentComponentIndex >= 0) {
              if (data?.indexOffset) {
                let waitForFocusElement = formInputs?.at(
                  currentComponentIndex + data?.indexOffset,
                );

                // 如果是时间控件 需要找到下一个不是时间控件的控件 并且只有enter下
                if (
                  componentId?.includes('TimeScape') &&
                  event?.key === 'Enter' &&
                  timescapeEnterSwitch === true
                ) {
                  waitForFocusElement = formInputs
                    ?.slice(currentComponentIndex)
                    ?.find((item) => !item?.id?.includes('TimeScape'));
                }

                // 弹窗内部不响应
                if (
                  waitForFocusElement?.parentElement?.className?.includes(
                    'ant-modal',
                  )
                ) {
                  return;
                }

                if (waitForFocusElement) {
                  formInputs?.at(currentComponentIndex)?.blur();

                  if (forceClickIds?.includes(waitForFocusElement?.id)) {
                    waitForFocusElement?.click();
                    return;
                  } else {
                    if (
                      autoClick &&
                      waitForFocusElement?.click &&
                      Array.from(waitForFocusElement?.classList ?? [])?.filter(
                        (classItem: string) =>
                          noClickElementsClassName?.includes(classItem),
                      ).length <= 0
                    ) {
                      waitForFocusElement?.click();
                      return;
                    }
                  }
                  waitForFocusElement?.focus();
                }
              }
            }
          }
        }
      },
    );

    Emitter.on(EventConstant.INSURANCE_SEARCH_TABLE_HISIDS, (hisIds) => {
      setSearchedHisIds(hisIds);
    });

    Emitter.on(EventConstant.DMR_DRAWER_CHANGE, (status) => {
      setLeftContainerOpen(status);
    });

    Emitter.on(EventConstant.DMR_RECORD_LAST_PAGE, (status) => {
      setLastChsRecordPageReached(status);
    });

    return () => {
      Emitter.offMultiple([
        EventConstant.CHS_TABLE_NEXT_KEY,
        EventConstant.CHS_TABLE_PREVIOUS_KEY,
      ]);

      document.removeEventListener('onkeydown', () => {});

      Emitter.off(EventConstant.INSURANCE_SEARCH_TABLE_HISIDS);
      Emitter.off(EventConstant.DMR_DRAWER_CHANGE);

      // make grid instance undefined
      headerGridInstance = undefined;
      contentGridInstance = undefined;
    };
  }, []);

  useEffect(() => {
    window.addEventListener('beforeunload', function (e) {
      // Cancel the event
      if (form.getFieldValue('formEdited') && currentHisId) {
        e.preventDefault();
        let returnValue = '存在尚未保存的信息，确定要离开？';
        e.returnValue = returnValue;

        return returnValue;
      }

      return null;
    });

    return () => {
      // 移除提示
      window.removeEventListener('beforeunload', () => {});
    };
  }, [currentHisId]);

  useEffect(() => {
    if (isEmptyValues(location?.search) || isEmptyValues(layouts)) {
      return;
    }

    let urlParam = qs.parse(location.search, {
      ignoreQueryPrefix: true,
    });

    if (urlParam.hisId && urlParam?.hisId !== currentHisId) {
      getChsCardInfo(
        decodeURIComponent(urlParam.hisId),
        urlParam?.instantAudit,
      );

      // 跳回最开始
      document.getElementById('dmr-content-container')?.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [location?.search, layouts]);

  useEffect(() => {
    // initialize yLayout
    console.log('initialize yLayout');

    if (!isEmptyValues(layouts)) {
      Object.keys(LAYOUT_COLUMNS)?.forEach((key) => {
        yLayout[key] = 0;
      });
      // TODO layouts改动 ？ 还是 breakpoint改动？

      chsProcessorInstance.setLayouts(layouts);

      let gridOpts = {
        column: LAYOUT_COLUMNS['lg'],
        cellHeight: ROW_HEIGHT,
        sizeToContent: true,
        disableResize: true,
        disableDrag: true,
        marginTop: 0,
        marginLeft: 0,
        marginRight: 0,
        marginBottom: 0,
        staticGrid: true,
      };

      if (contentGridInstance === undefined) {
        contentGridInstance = GridStack.init(
          {
            columnOpts: {
              columnMax: LAYOUT_COLUMNS['lg'],
              layout: 'move' as any,
              breakpoints: breakpoints('CONTENT')?.sort((a, b) => a.w - b.w),
            },
            ...gridOpts,
          },
          document.getElementById('dmr-content-grid-layout'),
        );

        contentGridInstance?.on('loaded', (name, callback) => {
          setTimeout(() => {
            Emitter.emit(EventConstant.DMR_MENU_POSITION_RECALCULATE);
            Emitter.emit(EventConstant.TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR);
            Emitter.emit(EventConstant.TABLE_LAYOUT_RESIZE_SEPARATOR);
          }, 500);
        });
        global['contentGridInstance'] = contentGridInstance;
      }

      if (headerGridInstance === undefined) {
        global['headerGridInstance'] = headerGridInstance = GridStack.init(
          {
            columnOpts: {
              columnMax: LAYOUT_COLUMNS['lg'],
              layout: 'move' as any,
              breakpoints: breakpoints('HEADER')?.sort((a, b) => a.w - b.w),
            },
            ...gridOpts,
          },
          document.getElementById('dmr-header-grid-layout'),
        );
      }
    }
  }, [layouts]);

  useEffect(() => {
    if (contentGridInstance) {
      if (!isEmptyValues(layouts['lg'])) {
        contentGridInstance.batchUpdate();
        contentGridInstance.removeAll(false);
        let stackLayouts = [];
        layouts['lg']?.forEach((item) => {
          contentGridInstance.makeWidget(`#${item?.i}`);
          stackLayouts.push({
            id: item?.i,
            x: item?.x,
            y: item?.y,
            // sizeToContent: true,
            w: item?.w,
          });
        });

        contentGridInstance.load(stackLayouts);
        contentGridInstance.batchUpdate(false);
        Emitter.emit('DMR_LAYOUT_LOADING', false);
      }
    }
  }, [layouts, contentGridInstance]);

  useEffect(() => {
    if (headerGridInstance) {
      if (!isEmptyValues(headerLayouts['lg'])) {
        headerGridInstance.batchUpdate();
        headerGridInstance.removeAll(false);
        let stackLayouts = [];
        headerLayouts['lg']?.forEach((item) => {
          headerGridInstance.makeWidget(`#${item?.i}`);
          stackLayouts.push({
            id: item?.i,
            x: item?.x,
            y: item?.y,
            // sizeToContent: true,
            w: item?.w,
          });
        });

        headerGridInstance.load(stackLayouts);
        headerGridInstance.batchUpdate(false);
      }
    }
  }, [headerLayouts, headerGridInstance]);

  useEffect(() => {
    // 监听focus in
    document
      ?.getElementById('dmr-form-container')
      ?.addEventListener('focusin', (event) => {
        // Input 并且 readonly = true 不响应
        if (
          (event?.target as any)?.tagName?.toString()?.toLowerCase() === 'input'
        ) {
          if ((event?.target as any)?.readOnly === true) {
            let excludeIdKeywords = ['TimeScape'];
            let shouldSkip = true;

            for (let excludeIdKeyword of excludeIdKeywords) {
              if ((event?.target as any)?.id?.includes(excludeIdKeyword)) {
                shouldSkip = false;
              }
            }

            if (shouldSkip) {
              return;
            }
          }
        }

        console.log('focus changed', event, document.activeElement);
        let hasScrolled = false;
        if (centerInputActivate) {
          if (
            !noCenterElement?.includes(
              (event.target as any)?.tagName?.toString()?.toLowerCase(),
            ) &&
            (event.target as any).closest("td[class*='ant-table-cell']") !==
              null
          ) {
            scrollFocusedElementToCenter(document?.activeElement);
          }
        }

        if (tableItemFocusTop) {
          if (
            (event.target as any).closest("td[class*='ant-table-cell']") !==
            null
          ) {
            // 表示在表格内
            let containerScrollTop = document.getElementById(
              'dmr-content-container',
            )?.scrollTop;
            let tableContainerOffsetTop = (event.target as any).closest(
              "div[class~='grid-stack-item']",
            )?.offsetTop;
            if (containerScrollTop < tableContainerOffsetTop) {
              document.getElementById('dmr-content-container')?.scrollTo({
                top: tableContainerOffsetTop,
                behavior: 'smooth',
              });
              hasScrolled = true;
            }
          }
        }

        // 当前这个item 距离底部多少的时候强制 滚动到屏幕正中间 防止看不见 一般来说是100 可设置0， 0 的话不会自动滚动
        if (forceScrollToCenterDistance !== 0) {
          const inViewPort = isInElementViewport(
            event?.target as any,
            document.getElementById('dmr-content-container'),
            forceScrollToCenterDistance,
          );
          if (!inViewPort && !hasScrolled) {
            (event?.target as any)?.scrollIntoView({
              block: 'center',
              inline: 'center',
              behavior: 'smooth',
            });
          }
        }

        borderActiveInputs();

        if (inputFocusNotSelectAll === false) {
          let classList = (event.target as any)?.classList;
          let selectAll = false;
          for (let classItem of classList) {
            if (focusSelectAllClass?.includes(classItem)) {
              selectAll = true;
              break;
            }
          }

          if (selectAll) {
            (event.target as any)?.select();
          }
        }
      });

    document
      ?.getElementById('dmr-form-container')
      ?.addEventListener('drop', (event) => {
        // 如果是 输入框 禁止文案拖放操作
        if ((event?.target as any)?.tagName.toLowerCase() === 'input') {
          event.dataTransfer.dropEffect = 'none';
          event.stopPropagation();
          event.preventDefault();
        }
      });

    return () => {
      document
        ?.getElementById('dmr-form-container')
        ?.removeEventListener('focusin', () => {});

      document
        ?.getElementById('dmr-form-container')
        ?.removeEventListener('drop', () => {});
    };
  }, [document?.getElementById('dmr-form-container')]);

  // 构建scrollTitle
  const buildScrollTitle = () => {
    let scrollingTitles = [];

    if (formChsCardInfo?.PatName) {
      scrollingTitles.push(`姓名：${formChsCardInfo?.PatName}`);
    }

    if (formChsCardInfo?.PatAdmNo) {
      scrollingTitles.push(`住院号：${formChsCardInfo?.PatAdmNo}`);
    }

    if (formChsCardInfo?.PatNo) {
      scrollingTitles.push(`病案号：${formChsCardInfo?.PatNo}`);
    }

    return scrollingTitles.join('，') + '    ';
  };

  // 接口start

  const getChsCardInfo = async (hisId: string, instantAudit?: boolean) => {
    if (hisId) {
      // setDmrCardInfoLoading(true);
      Emitter.emit('MODAL_LOADING_STATUS', true);

      let { formFieldValue, cardBundleInfo } = await getCardInfoV2(
        hisId,
        chsProcessorInstance,
      );

      let formCardInfo = mergeWith(
        {},
        defaultFieldValue,
        formFieldValue,
        (objValue, srcValue) => {
          if (srcValue !== null && srcValue !== undefined) {
            return srcValue;
          }

          return objValue;
        },
      );
      // 重置viewMode
      // TODO ??? 确定是这个？？？
      let viewModeByRegisterStatus =
        cardBundleInfo?.CardFlat?.RegisterStatus === '1' ? false : true;
      setViewMode(viewModeByRegisterStatus);
      if (viewModeByRegisterStatus === false) {
        setTimeout(() => {
          // 第一个可以编辑的项目
          const firstEditableInput = document
            .getElementById('dmr-content-container')
            ?.querySelector('input:not([disabled])');
          if (firstEditableInput) {
            (firstEditableInput as any)?.focus();
            (firstEditableInput as any)?.click();
          }
        }, 300);
      }

      // form.resetFields();
      // reset form table fields
      resetTableData();

      form.setFieldsValue(formCardInfo);
      setFormChsCardInfo(formCardInfo);
      setCurrentHisId(hisId);
      setOriginChsCardInfo(cardBundleInfo);
      // 状态 TODO
      // setRegisterStatusName(formFieldValue['RegisterStatusName']);

      if (instantAudit?.toString() === 'true') {
        setTimeout(() => {
          Emitter.emit(EventConstant.CHS_CHSGROUP_AUDIT, {
            hisId: hisId,
            originChsCardInfo: cardBundleInfo,
            formFieldsValue: formCardInfo,
          });
        }, 0);
        setRightContainerOpen(true);
      } else {
        // 清除右侧
        Emitter.emit(EventConstant.CHS_CARD_DATA_RESET);
      }

      // setDmrCardInfoLoading(false);
      Emitter.emit('MODAL_LOADING_STATUS', false);

      // 定位到第一个能填写的地方
      // document?.getElementById('formItem#InTimes#RestrictInputNumber')?.focus();
      // document?.getElementById('formItem#InTimes#RestrictInputNumber')?.click();
    } else {
      message.error('HisId缺失，请检查');
    }
  };

  // 获取layouts TODO 可能需要改掉 loading / 其他的
  const { loading: chsLayoutsGetLoading, run: chsLayoutsGetReq } = useRequest(
    (cliDeptCode, hospCode, modules) => {
      return getChsIndexLayout(cliDeptCode, hospCode, modules);
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setHeaderLayouts(
            generateLayout(
              response?.data?.ChsHeaderLayout || {},
              updateLayout,
              headerData,
              'ChsHeaderLayout',
            ),
          );

          setLayouts(
            generateLayout(
              response?.data?.ChsLayout || {},
              updateLayout,
              contentData,
              'ChsLayout',
            ),
          );
          // Emitter.emit("DMR_LOADING", false);

          // TODO config
          // setDmrOperatorConfig(response?.data?.DmrOperatorsConfig ?? {});
          // setDmrPreCheckModuleConfig(response?.data?.DmrPreCheckModules ?? {});
        } else {
          setLayouts({});
          setHeaderLayouts({});
          // TODO config
          // setDmrOperatorConfig({});
          // setDmrPreCheckModuleConfig({});
        }
      },
    },
  );

  // TODO config
  // const { loading: dmrOperatorConfigLoading, run: dmrOperaotrConfigReq } =
  //   useRequest(
  //     () => {
  //       return getDmrOperatorConfig();
  //     },
  //     {
  //       manual: true,
  //       formatResult: async (response: RespVO<any>) => {
  //         if (response?.code === 0 && response?.statusCode === 200) {
  //           setDmrOperatorConfig(response?.data?.DmrOperatorsConfig ?? {});
  //         } else {
  //           setDmrOperatorConfig({});
  //         }
  //       },
  //     },
  //   );
  //
  // const {
  //   loading: dmrPreCheckModuleConfigLoading,
  //   run: dmrPreCheckModuleConfigReq,
  // } = useRequest(
  //   () => {
  //     return getDmrPreCheckModuleConfig();
  //   },
  //   {
  //     manual: true,
  //     formatResult: async (response: RespVO<any>) => {
  //       if (response?.code === 0 && response?.statusCode === 200) {
  //         setDmrPreCheckModuleConfig(response?.data?.DmrPreCheckModules ?? {});
  //       } else {
  //         setDmrPreCheckModuleConfig({});
  //       }
  //     },
  //   },
  // );

  // 接口 end

  // 用于判定编辑模式下 是否存在未保存的东西 并且被跳转了
  const onChsCardChangeInterceptor = (label?: string) => {
    return new Promise<boolean>((resolve, reject) => {
      if (!viewMode) {
        // 有操作过的时候
        if (form.getFieldValue('formEdited')) {
          Modal.confirm({
            title: `有未保存的更改，确定要${label ?? '切换病案'}？`,
            content: `${label ?? '切换病案'}会舍弃当前病案修改的内容`,
            onOk: () => {
              resolve(false);
            },
            onCancel: () => {
              reject();
            },
          });
        } else {
          resolve(false);
        }
      } else {
        resolve(false);
      }
    });
  };

  const onFinish = (values: any) => {
    console.log('Success:', values);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  const borderErrorInputs = (errorKeys: string[], navigate?: boolean) => {
    // reset border error
    let allErrorElements = document
      .getElementById('dmr-form-container')
      ?.getElementsByClassName('border-error');
    let errorElements = [].slice.call(allErrorElements);
    errorElements?.forEach((errorElement) => {
      errorElement?.classList?.remove('border-error');
    });

    if (errorKeys?.length === 0) {
      return;
    }

    errorKeys?.forEach((key, index) => {
      if (key.includes('Table')) {
        let keys = key.split('~');
        let errorTableCell = document?.querySelector(
          `tr[data-index='${keys?.at(2)}'] td[rowindex='${keys?.at(
            2,
          )}'][dataindex='${keys?.at(1)}']`,
        );
        if (!isEmptyValues(errorTableCell)) {
          errorElementsClick(errorTableCell);
        }
      } else {
        if (key === 'RH') {
          // RH 特殊处理
          let errorElement = document?.getElementById(`formItem#Rh`);
          if (errorElement) {
            errorElement
              ?.closest('.form-content-item-container')
              ?.classList?.remove('border-active');
            errorElement
              ?.closest('.form-content-item-container')
              ?.classList?.add('border-error');

            if (index === 0) {
              errorElementsClick(errorElement);
            }
          }
        } else {
          let errorElement = document?.getElementById(`formItem#${key}`);
          if (errorElement) {
            errorElement
              ?.closest('.form-content-item-container')
              ?.classList?.remove('border-active');
            errorElement
              ?.closest('.form-content-item-container')
              ?.classList?.add('border-error');

            if (index === 0) {
              errorElementsClick(errorElement);
            }
          }
        }
      }
    });

    if (navigate) {
      let firstErrorKey = errorKeys?.at(0);
      let errorKeyNavigateId = firstErrorKey;

      let errorKeyReflectKey = Object.keys(formKeyToNavigationId)?.find((key) =>
        new RegExp(key).test(firstErrorKey),
      );
      if (errorKeyReflectKey) {
        errorKeyNavigateId = formKeyToNavigationId[errorKeyReflectKey];
      }

      if (errorKeyNavigateId) {
        let transformHeight = getTransformHeightByElementId(errorKeyNavigateId);
        if (transformHeight) {
          document.getElementById('dmr-content-container')?.scrollTo({
            top: transformHeight,
            behavior: 'smooth',
          });
        }
      }
    }
  };

  const borderErrorInputsInTable = (
    tableErrorKeyItem: any,
    navigate?: boolean,
  ) => {
    if (isEmptyValues(tableErrorKeyItem)) {
      return;
    }

    let keys = tableErrorKeyItem?.key.split('~');
    let errorTableCell = document?.querySelector(
      `tr[data-index='${keys?.at(2)}'] td[rowindex='${keys?.at(
        2,
      )}'][dataindex='${keys?.at(1)}']`,
    );
    if (!isEmptyValues(errorTableCell)) {
      errorElementsClick(errorTableCell);
    }

    // 标记行 根据 OriginalInputValue 标记 限定rulecode
    if (tableErrorKeyItem?.RuleCode == '8-CUST-92') {
      if (tableErrorKeyItem?.key?.includes('diagnosisTable')) {
        let tableData = form?.getFieldValue('diagnosis-table');
        const icdeTableItem = document.getElementById('diagnosisTable');
        tableData?.forEach((item, index) => {
          if (item?.IcdeCode === tableErrorKeyItem?.OriginalInputValue) {
            // 标记 行
            let waitForHighlightLineItem = icdeTableItem?.querySelector(
              `tbody > tr:nth-child(${index + 1})`,
            );
            if (!isEmptyValues(waitForHighlightLineItem)) {
              waitForHighlightLineItem?.classList?.add(
                'table-duplicate-highlight-item',
              );
            }
          }
        });
      }

      if (tableErrorKeyItem?.key?.includes('operationTable')) {
        let tableData = form?.getFieldValue('operation-table');
        const operationTableItem = document.getElementById('operationTable');
        tableData?.forEach((item, index) => {
          if (item?.OperCode === tableErrorKeyItem?.OriginalInputValue) {
            // 标记 行
            let waitForHighlightLineItem = operationTableItem?.querySelector(
              `tbody > tr:nth-child(${index + 1})`,
            );
            if (!isEmptyValues(waitForHighlightLineItem)) {
              waitForHighlightLineItem?.classList?.add(
                'table-duplicate-highlight-item',
              );
            }
          }
        });
      }
    }

    if (navigate) {
      const errorKeyNavigateId = keys?.at(0);
      if (errorKeyNavigateId) {
        let transformHeight = getTransformHeightByElementId(errorKeyNavigateId);
        if (transformHeight) {
          document.getElementById('dmr-content-container')?.scrollTo({
            top: transformHeight,
            behavior: 'smooth',
          });
        }
      }
    }
  };

  const errorElementsClick = (element: any) => {
    if (errorKeyClick) {
      element?.getElementsByTagName('input')?.[0]?.focus();
    }
  };

  const borderActiveInputs = () => {
    // reset border active
    // unBorderActiveElements();
    unBorderErrorElements(document?.activeElement);
    // borderActiveElements(document?.activeElement, true);
  };

  const scrollFocusedElementToCenter = (element: Element) => {
    // TODO 临时解决方案  checkbox switch不做 居中
    if (!(element.id.includes('IsMain') || element.id.includes('IsReported'))) {
      element?.scrollIntoView({
        block: 'center',
        inline: 'center',
        // behavior: 'smooth'
      });
    }
  };

  const borderActiveElements = (element: any, border?: boolean) => {
    let closestElement = element?.closest('.form-content-item-container');
    if (closestElement) {
      if (border) {
        closestElement?.classList?.add('border-active');

        // active table line item
        // 有且仅有一个能border
        let closestTBodyElement = element?.closest('tbody');
        if (closestTBodyElement) {
          // 删除所有的 tr-border-active
          let allTrElementsUnderTbody = closestTBodyElement?.querySelectorAll(
            'tr[class*=ant-table-row]',
          );
          let trElementsList = [].slice.call(allTrElementsUnderTbody);
          if (trElementsList?.length > 0) {
            trElementsList?.forEach((trElement) => {
              trElement?.classList?.remove('tr-border-active');
            });
          }
        }

        let closestTrElement = element?.closest('tr[class*=ant-table-row]');
        if (closestTrElement) {
          closestTrElement?.classList?.add('tr-border-active');
        }
      } else {
        closestElement?.classList?.remove('border-active');

        // 有且仅有一个能border
        let closestTrElement = element?.closest('tr[class*=ant-table-row]');
        if (closestTrElement) {
          closestTrElement?.classList?.remove('tr-border-active');
        }
      }
    }
  };

  const unBorderActiveElements = () => {
    console.log('unBorderActiveElements');
    let activeElements = document.querySelectorAll('.border-active');
    if (activeElements && activeElements?.length > 0) {
      let activeElementsList = [].slice.call(activeElements);
      activeElementsList?.forEach((elementItem) => {
        elementItem?.classList?.remove('border-active');

        let closestTrElement = elementItem?.closest('tr[class*=ant-table-row]');
        if (closestTrElement) {
          closestTrElement?.classList?.remove('tr-border-active');
        }
      });
    }
  };

  const unBorderErrorElements = (focusElement) => {
    let errorClassNameKey = 'div[class*="border-error"]';
    let errorElement = focusElement?.closest(errorClassNameKey);
    if (errorElement) {
      errorElement?.classList?.remove('border-error');
    }
  };

  const mostTopInScreenInput = () => {
    let scrollTop = document.getElementById('dmr-content-container').scrollTop;

    let contentItemElements = document.querySelectorAll(
      '#dmr-content-grid-layout > div',
    );
    if (contentItemElements?.length > 0) {
      let contentItems = [].slice.call(contentItemElements);

      for (let contentItem of contentItems) {
        let offsetScrollPosition = scrollTop - (contentItem?.offsetTop ?? 0);
        if (offsetScrollPosition <= ROW_HEIGHT / 2) {
          const firstEditableInput = contentItem?.querySelector(
            'input:not([disabled])',
          );
          if (firstEditableInput) {
            setTimeout(() => {
              (firstEditableInput as any)?.focus();
              (firstEditableInput as any)?.click();
            }, 0);
            break;
          }
        }
      }
    }
  };

  const onOperationItemClick = async (operationKey) => {
    switch (operationKey) {
      case 'EDIT':
        let currentViewMode = !viewMode;
        if (!currentViewMode) {
          // edit -> true
          if (inEditRemainScrollPosition) {
            // 计算离得最近的 input格子
            mostTopInScreenInput();
          } else {
            setTimeout(() => {
              // 第一个可以编辑的项目
              const firstEditableInput = document
                .getElementById('dmr-content-container')
                ?.querySelector('input:not([disabled])');
              if (firstEditableInput) {
                (firstEditableInput as any)?.focus();
                (firstEditableInput as any)?.click();
              }
            }, 0);
          }
        } else {
          let result = await onChsCardChangeInterceptor('取消编辑');
          if (result) {
            return;
          }
          // 刷新
          if (form.getFieldValue('formEdited')) {
            getChsCardInfo(currentHisId, false);
          }
        }
        setViewMode(currentViewMode);
        break;
      case 'SAVE':
        // TODO 需要删除
        // let errorKeys = formValidation(form);
        // borderErrorInputs(errorKeys);
        if (noEditSave === false) {
          if (!form.getFieldValue('formEdited')) {
            console.log('未改动');
            return;
          }
        }
        if (currentHisId) {
          // 给定默认值 诸如 身份证不填给 - 这种 使其跳过 validation
          defaultValueToFormValueBeforeValidation(form);
          let errorKeys = await formValidation(form);
          borderErrorInputs(errorKeys, true);
          if (errorKeys?.length === 0) {
            message.success('保存中，请稍后');
            // setDmrCardInfoLoading(true);
            Emitter.emit('MODAL_LOADING_STATUS', true);

            let saveResponse = await saveCardInfoV2(
              originChsCardInfo,
              form.getFieldsValue(),
              currentHisId,
              globalState?.dictData?.['Insur'],
              chsProcessorInstance,
            );

            // setDmrCardInfoLoading(false);
            Emitter.emit('MODAL_LOADING_STATUS', false);
            form.setFieldValue('formEdited', false);
            // 更新一下 table的数据
            // TODO tableData需要修改
            updateTableData();

            if (saveResponse?.code === 0 && saveResponse?.statusCode === 200) {
              if (saveResponse?.data?.PassCheckFlag) {
                message.success('保存成功');
                // 保存成功的话光标跳到 搜索框上
                Emitter.emit(EventConstant.CHS_HEADER_FOCUS);
                setRightContainerOpen(false);
                Emitter.emit(EventConstant.CHS_CARD_SAVE_REVIEWS, {});
              } else {
                message.warn('保存成功，质控审核不通过，请查看');
                setRightContainerOpen(true);
                Emitter.emit(
                  EventConstant.CHS_CARD_SAVE_REVIEWS,
                  saveResponse?.data,
                );
              }

              // 登记状态 TODO
              if (saveResponse?.data?.Extra?.['RegisterStatusName']) {
                // setRegisterStatusName(
                //   saveResponse?.data?.Extra?.['RegisterStatusName'],
                // );
              }
              // 合并keys 到 form value里面
              Object.keys(saveResponse?.data?.Extra ?? {})?.forEach((key) => {
                form.setFieldValue(key, saveResponse?.data?.Extra?.[key]);
              });

              if (checkOnSave) {
                setTimeout(() => {
                  Emitter.emit(EventConstant.CHS_CHSGROUP_AUDIT, {
                    hisId: currentHisId,
                    originCardInfo: originChsCardInfo,
                    formFieldsValue: form.getFieldsValue(true),
                  });
                }, 0);
              }
            } else {
              if (saveResponse?.statusCode !== 400) {
                message.error('保存出现问题');
                if (location.search) {
                  let urlParam = qs.parse(location.search, {
                    ignoreQueryPrefix: true,
                  });
                  getChsCardInfo(
                    decodeURIComponent(urlParam.hisId),
                    urlParam?.instantAudit,
                  );
                }
              }
            }
          }
        } else {
          message.error('HisId不存在，请检查');
        }
        break;
      case 'CHSGROUP_AUDIT':
        // TODO 预留validation方法
        let errorKeys = auditValidation(form.getFieldsValue());
        if (errorKeys?.length > 0) {
          borderErrorInputs(errorKeys);
          return;
        }
        Emitter.emit(EventConstant.CHS_CHSGROUP_AUDIT, {
          hisId: currentHisId,
          originChsCardInfo: originChsCardInfo,
          formFieldsValue: form?.getFieldsValue(),
        });
        if (preCheckContainerPosition === 'bottom') {
          document.getElementById('content').style.overflowY = 'hidden';
        }
        setRightContainerOpen(true);
        break;
      case 'NEXT':
        if (searchedHisIds?.length > 0 && currentHisId) {
          let currentHisIdIndex = searchedHisIds?.findIndex(
            (item) => item === currentHisId,
          );
          if (currentHisIdIndex !== searchedHisIds?.length - 1) {
            let result = await onChsCardChangeInterceptor();
            if (result) {
              return;
            }
            let chsParam = {
              hisId: searchedHisIds?.[currentHisIdIndex + 1],
            };
            Emitter.emit(
              EventConstant.CHS_NEXT,
              searchedHisIds?.[currentHisIdIndex + 1],
            );
            history.replace(`${location?.pathname}?${qs.stringify(chsParam)}`);
          } else {
            if (lastChsRecordPageReached) {
              message.success('这是最后一份病案');
            } else {
              Emitter.emit(EventConstant.DMR_NEXT_PAGE);
            }
          }
        }
        break;
      case 'FULLSCREEN':
        let baseUrl = '/chs/main/index/fullscreen';
        window.open(`${baseUrl}${location.search}`);
        break;
      case 'CENTER':
        let urlParam = {
          center: !centerInputActivate,
        };
        if (location.search) {
          urlParam = {
            ...qs.parse(location.search, {
              ignoreQueryPrefix: true,
            }),
            ...urlParam,
          };
        }
        history.replace(`${location?.pathname}?${qs.stringify(urlParam)}`);
        break;
      case 'THIRD_PARTY_CHECK':
        insurBundleThirdPartyCheckReq(
          currentHisId,
          originChsCardInfo,
          form?.getFieldsValue(),
          chsProcessorInstance,
          globalState,
        );
        break;
      default:
        break;
    }
  };

  const onChsClear = () => {
    form.resetFields();
    form.setFieldValue('formEdited', false);
    setViewMode(true);
    setFormChsCardInfo(undefined);
    setCurrentHisId('');
    setOriginChsCardInfo(null);
    // 状态 TODO
    // setRegisterStatusName('');
    history.replace(`${location?.pathname}`);
  };

  const onDeletePress = (event) => {
    if (document?.activeElement) {
      let formKey = document?.activeElement?.id?.split('#')?.at(1);

      // 当没有formKey的时候 return 比如 顶上的输入病案号这个框
      if (isEmptyValues(formKey)) {
        return;
      }

      // 单独适配
      if (
        deleteSpecialKeysProcessorByPrefix(
          formKey,
          document?.activeElement?.id,
        ) ||
        deleteSpecialKeysProcessor[formKey]
      ) {
        formKey =
          deleteSpecialKeysProcessorByPrefix(
            formKey,
            document?.activeElement?.id,
          ) ?? deleteSpecialKeysProcessor[formKey](document?.activeElement?.id);
      }

      console.log('onDeletePress', formKey, document?.activeElement?.id);

      Emitter.emit(
        getDeletePressEventKey(formKey),
        document?.activeElement?.id,
      );

      if (!formKey?.toLowerCase()?.includes('table')) {
        form.setFieldValue(formKey, '');
      }
    }
  };

  const onPageUpDownPress = (event, up: boolean) => {
    if (dmrPageUpDownSwitchModule === true) {
      if (up === true) {
        Emitter.emit(EventConstant.DMR_LEFT_MENU_CLICK_OFFSET, -1);
      } else {
        Emitter.emit(EventConstant.DMR_LEFT_MENU_CLICK_OFFSET, 1);
      }
    } else {
      defaultPageUpDownHandler(up);
    }
  };

  const onIcdeOperTransferTableAddClick = (eventName) => {
    if (eventName) {
      Emitter.emit(eventName);
    }
  };

  const updateTableData = () => {
    // 'diagnosis-table',
    //   'diagnosisTable',
    //   'operation-table',
    //   'operationTable',
    //   'formEdited',
    //   'pathological-diagnosis-table',
    //   'pathologicalDiagnosisTable',
    //   'icu-table',
    //   'icuTable',

    console.log('form value', form.getFieldsValue(true));

    form.setFieldValue(
      'diagnosisTable',
      form.getFieldValue('diagnosis-table') ?? [],
    );
    form.setFieldValue(
      'operationTable',
      form.getFieldValue('operation-table') ?? [],
    );
    form.setFieldValue('icuTable', form.getFieldValue('icu-table') ?? []);

    form.setFieldValue('bloodTable', form.getFieldValue('blood-table') ?? []);

    form.setFieldValue(
      'feeChargeTable',
      form.getFieldValue('fee-charge-table') ?? [],
    );

    form.setFieldValue(
      'paymentTable',
      form.getFieldValue('payment-table') ?? [],
    );

    // TODO blood table fee table
    console.log('formValues updateTableData', form.getFieldsValue());
  };

  const resetTableData = () => {
    hiddenFormItemKeys?.forEach((key) => {
      if (!key?.includes('-')) {
        form.setFieldValue(key, []);
      }
    });
  };

  const OperationHeader = ({ form, operatorConfig }) => {
    const [saveEnable, setSaveEnable] = useState(
      form.getFieldValue('formEdited') ?? false,
    );

    useEffect(() => {
      Emitter.on(EventConstant.CHS_FORM_VALUE_CHANGE, () => {
        form.setFieldValue('formEdited', true);
        setSaveEnable(true);
      });

      return () => {
        Emitter.off(EventConstant.CHS_FORM_VALUE_CHANGE);
      };
    }, []);

    return (
      <div
        id={'dmr-root-operation-header'}
        className={'dmr-root-operation-header'}
      >
        <ChsQueryHeader
          onChsCardChangeInterceptor={onChsCardChangeInterceptor}
        />

        <div
          style={{ flexFlow: 'row wrap', rowGap: 4, marginLeft: 'auto' }}
          className={'flex-row-center'}
        >
          {operations(viewMode, currentHisId, searchedHisIds)
            ?.filter((item) => {
              if (!isEmptyValues(operatorConfig)) {
                return operatorConfig?.[item.key]?.enable ?? true;
              }

              if (
                !showThirdPartyCheckBtn &&
                item?.key === 'THIRD_PARTY_CHECK'
              ) {
                console.log('aaasdsadsad', showThirdPartyCheckBtn, item);
                return false;
              }

              return item?.enable ?? true;
            })
            ?.map((item) => {
              if (!isEmptyValues(operatorConfig)) {
                item['order'] = operatorConfig?.[item.key]?.order ?? 0;
              }

              return item;
            })
            ?.sort((a, b) => a?.order - b?.order)
            .map((item) => {
              return (
                <Button
                  className={'operation-item'}
                  // type="primary"
                  // ghost
                  size={'small'}
                  disabled={
                    item?.key !== 'SAVE'
                      ? item?.disabled
                      : noEditSave
                      ? item?.disabled
                      : !saveEnable || item?.disabled
                  }
                  onClick={operationClickDebounce(() =>
                    onOperationItemClick(item.key),
                  )}
                >
                  {item?.icon
                    ? React.cloneElement(item?.icon, {
                        style: {
                          color: item?.color ?? 'unset',
                          '--customStrokeColor': item?.color,
                        },
                        className: `${
                          item?.color ? 'operation-icon-custom-stroke' : ''
                        }`,
                      })
                    : null}
                  {item.title}
                </Button>
              );
            })}
        </div>
      </div>
    );
  };

  const GridItemContainer = (props) => {
    const sectionStyle = props?.item?.data?.component?.includes('Section')
      ? { background: '#ffffff !important' }
      : {};

    return (
      <div
        id={props?.id}
        key={props?.key}
        style={{ ...props?.style, ...sectionStyle }}
        className={`grid-stack-item ${props?.className ?? ''} `}
        gs-id={props?.id}
        gs-x={props?.item?.x}
        gs-y={props?.item?.y}
        gs-w={props?.item?.w}
      >
        <GridItem
          containerStyle={props?.style}
          containerClassName={props?.className}
          form={form}
          index={props?.index}
          componentId={props?.item.data.key}
          key={uuidv4()}
          data={props?.item.data}
        />
      </div>
    );
  };

  const chsChildren = React.useMemo(() => {
    return layouts[breakpoint]?.map((item, index) => {
      return (
        <GridItemContext.Provider
          value={{
            dynamicComponentsMap: dynamicComponentsMap,
            externalConfig: externalChsConfig,
            eventNames: {
              HELP_MODAL: EventConstant.INSURANCE_HELP_MODAL,
              TABLE_NEXT_KEY: EventConstant.CHS_TABLE_NEXT_KEY,
            },
            modelGroup: 'Insur',
          }}
        >
          <GridItemContainer
            id={item.data.key}
            key={item.data.key}
            item={item}
            index={index}
          />
        </GridItemContext.Provider>
      );
    });
  }, [layouts]);

  console.log('isEdited', form.isFieldsTouched(true));

  let defaultWidth =
    document.getElementById('dmr-form-container')?.getBoundingClientRect()
      ?.width - 19;
  console.log('defaultWidth', defaultWidth);

  // @ts-ignore
  return (
    <>
      <div className="ant-card" style={{ height: 'calc(100% - 10px)' }}>
        <ShortcutsHelpModal shortcuts={shortcuts} />

        <div
          id={'dmr-root-container'}
          className={'dmr-root-container'}
          style={{
            height: '100%',
            // height:
            //   props?.extra?.rootContainerHeight ??
            //   document.getElementById('site-layout-content')?.offsetHeight - 50,
          }}
        >
          {/*left container*/}
          <div className={'dmr-left-container'}>
            <Drawer
              placement="left"
              mask={true}
              maskClosable={true}
              width={800}
              open={leftContainerOpen}
              className={'dmr-search-drawer-container'}
              onClose={() => {
                setLeftContainerOpen(false);
              }}
              title={`病案查询`}
              getContainer={() => {
                return document.getElementById('dmr-root-container');
              }}
            >
              <DmrSearch
                visible={leftContainerOpen}
                onChsCardChangeInterceptor={onChsCardChangeInterceptor}
                viewMode={viewMode}
                onRightArrowClick={(status) => setLeftContainerOpen(status)}
              />
            </Drawer>

            <div className={'dmr-left-trigger-container'}>
              <div
                className={'result-collapsed'}
                onClick={() => {
                  setLeftContainerOpen(!leftContainerOpen);
                }}
              >
                {leftContainerOpen ? (
                  <DoubleLeftOutlined />
                ) : (
                  <DoubleRightOutlined />
                )}
                查询
              </div>
            </div>
          </div>

          <div id={'dmr-container'} className={'dmr-container'}>
            {/*operation header*/}
            <div id={'dmr-main-container'} className={'dmr-main-container'}>
              <RightMenu
                defaultTopMenuKeys={topMenuKeys}
                initialLayout={layouts[breakpoint]}
              />

              <OperationHeader
                form={form}
                operatorConfig={chsOperatorConfig ?? {}}
              />

              <ModalLoading
                eventName={'MODAL_LOADING_STATUS'}
                spinWrapperClassName={'dmr-loading'}
                backgroundColor={'rgba(255,255,255, 0.3)'}
                parentContainerId={'dmr-root-container'}
              />

              <fieldset
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  height: `calc(100% - ${
                    document.getElementById('dmr-root-operation-header')
                      ?.offsetHeight + 5
                  }px)`,
                }}
                className={`${viewMode ? 'view-mode' : ''}`}
                disabled={viewMode}
                onCompositionStart={() => {
                  compositionStatusRef.current = true;
                }}
                onCompositionEnd={() => {
                  compositionStatusRef.current = false;
                }}
              >
                <Form
                  id={'dmr-form-container'}
                  className={'dmr-form-container'}
                  style={{
                    height: '100%',
                  }}
                  form={form}
                  name="basic"
                  onFinish={onFinish}
                  onFinishFailed={onFinishFailed}
                  autoComplete="off"
                  wrapperCol={{ flex: 1 }}
                  onValuesChange={(changedValues, allValues) => {
                    console.log('onValuesChange', changedValues, allValues);
                  }}
                  onFieldsChange={(changedFields, allFields) => {
                    console.log('onFieldsChange', changedFields, allFields);
                    Emitter.emit(EventConstant.CHS_FORM_VALUE_CHANGE);
                  }}
                >
                  <ModalLoading
                    eventName={'DMR_LAYOUT_LOADING'}
                    spinWrapperClassName={'dmr-loading'}
                    backgroundColor={'rgba(255,255,255,1)'}
                    parentContainerId={'dmr-form-container'}
                  />
                  <>
                    <div
                      id={'dmr-header-container'}
                      className={'dmr-header-container'}
                    >
                      <DmrHeader
                        form={form}
                        viewMode={viewMode}
                        // TODO
                        // registerStatusName={registerStatusName}
                        registerStatusName={''}
                        layoutSizeKey={breakpoint}
                        headerLayouts={headerLayouts}
                        itemWrapper={GridItemContainer}
                        headerContentData={headerLayouts[breakpoint]}
                      />
                    </div>

                    <div
                      id={'dmr-content-container'}
                      className={'dmr-content-container'}
                      style={{
                        flex: 1,
                        minWidth: 324,
                        overflowX: 'hidden',
                        padding: '0px 5px',
                      }}
                    >
                      <>
                        {[...hiddenFormItemKeys]?.map((key) => {
                          return <Form.Item name={key} hidden={true} />;
                        })}
                      </>

                      <Separators
                        width={defaultWidth}
                        initialLayout={layouts[breakpoint]}
                      />

                      <SectionSeparators
                        width={defaultWidth}
                        initialLayout={layouts[breakpoint]}
                      />

                      <div
                        id={'dmr-content-grid-layout'}
                        className="grid-stack"
                      >
                        {chsChildren}
                      </div>
                    </div>
                  </>
                </Form>
              </fieldset>
            </div>
          </div>

          <div
            className={'dmr-right-container'}
            style={{
              width: `${rightContainerOpen ? 300 : 30}px`,
              minWidth: `${rightContainerOpen ? 300 : 30}px`,
            }}
          >
            <div className={'dmr-right-trigger-container'}>
              <div
                className={'result-collapsed'}
                onClick={() => {
                  setRightContainerOpen(!rightContainerOpen);
                }}
              >
                <PreCheckIcon />
              </div>
            </div>
            <div
              className={`right-content ${
                rightContainerOpen ? 'content-open' : 'content-close'
              }`}
            >
              <PreCheckResult
                borderErrorInputs={borderErrorInputs}
                chsProcessorInstance={chsProcessorInstance}
                // preCheckModuleConfig={dmrPreCheckModuleConfig}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export const Separators = ({ width, initialLayout }) => {
  const [tableWidth, setTableWidth] = useState(0);

  const [layout, setLayout] = useState(initialLayout);

  const [resizeCount, setResizeCount] = useState(0);

  useEffect(() => {
    Emitter.on(EventConstant.DMR_TABLE_LAYOUT_CHANGE_SEPARATOR, (layout) => {
      setLayout(layout);
    });

    Emitter.on(EventConstant.TABLE_LAYOUT_CHANGE_RESET_SEPARATOR, () => {
      setLayout(undefined);
    });

    return () => {
      Emitter.off(EventConstant.DMR_TABLE_LAYOUT_CHANGE_SEPARATOR);
      Emitter.off(EventConstant.TABLE_LAYOUT_CHANGE_RESET_SEPARATOR);
    };
  }, []);

  useEffect(() => {
    Emitter.on(EventConstant.CHS_TABLE_LAYOUT_RESIZE_SEPARATOR, (layout) => {
      let newResizeCount = resizeCount + 1;
      setResizeCount(newResizeCount);
    });

    return () => {
      Emitter.off(EventConstant.CHS_TABLE_LAYOUT_RESIZE_SEPARATOR);
    };
  }, [resizeCount]);

  const yListWithKey = {};
  (!isEmptyValues(layout) ? layout : initialLayout)?.forEach((item) => {
    if (yListWithKey[item?.y]) {
      yListWithKey[item?.y] = [
        ...yListWithKey[item?.y],
        {
          i: item?.i,
          component: item?.data?.component,
        },
      ];
    } else {
      yListWithKey[item?.y] = [
        {
          i: item?.i,
          component: item?.data?.component,
        },
      ];
    }
  });

  const yListKeys = Object.keys(yListWithKey);

  return (
    <>
      {yListKeys.map((key, index) => {
        let rowKeysItem = yListWithKey[key]?.at(0);

        let previousRowKeysItem =
          index > 0 ? yListWithKey[yListKeys[index - 1]]?.at(0) : undefined;

        let separatorItemKey = getSeparatorKey(rowKeysItem?.i);
        let currentRowItemElement = document.getElementById(rowKeysItem?.i);

        let transformHeight = currentRowItemElement?.offsetTop;

        // 第一行也不用
        if (index === 0) {
          return null;
        }

        // 当前行 是section 就返回空
        if (rowKeysItem?.component?.includes('Section')) {
          return null;
        }

        // 上一行是 section 也返回空
        if (previousRowKeysItem?.component?.includes('Section')) {
          return null;
        }

        return (
          <div
            id={separatorItemKey}
            key={separatorItemKey}
            className={'separator-container'}
            style={{
              top: transformHeight - 2,
            }}
          >
            <div className={'separator'} />
          </div>
        );
      })}
    </>
  );
};

export const SectionSeparators = ({ width, initialLayout }) => {
  const [layout, setLayout] = useState(initialLayout);

  const [resizeCount, setResizeCount] = useState(0);

  useEffect(() => {
    Emitter.on(
      EventConstant.CHS_TABLE_LAYOUT_CHANGE_SECTION_SEPARATOR,
      (layout) => {
        setLayout(layout);
      },
    );

    Emitter.on(
      EventConstant.CHS_TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR,
      () => {
        setLayout(undefined);
      },
    );

    return () => {
      Emitter.off(EventConstant.CHS_TABLE_LAYOUT_CHANGE_SECTION_SEPARATOR);
      Emitter.off(
        EventConstant.CHS_TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR,
      );
    };
  }, []);

  useEffect(() => {
    Emitter.on(
      EventConstant.CHS_TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR,
      (layout) => {
        let newResizeCount = resizeCount + 1;
        setResizeCount(newResizeCount);
      },
    );

    return () => {
      Emitter.off(EventConstant.CHS_TABLE_LAYOUT_RESIZE_SECTION_SEPARATOR);
    };
  }, [resizeCount]);

  let sectionSeparators = [];

  let sectionSeparatorItem: any = {};
  (!isEmptyValues(layout) ? layout : initialLayout)
    ?.filter((item) => {
      return (
        item?.data?.component === 'SectionHeader' ||
        item?.data?.component === 'SectionBottom'
      );
    })
    ?.forEach((item) => {
      if (item?.data?.component === 'SectionHeader') {
        let headerItem = document.getElementById(item?.i);
        // 不存在 item
        if (isEmptyValues(sectionSeparatorItem)) {
          sectionSeparatorItem = {
            top: headerItem?.offsetTop + headerItem?.offsetHeight,
            backgroundColor: item?.data?.props?.sectionHeaderBorderColor,
            id: item?.i,
          };
        } else {
          // 存在item那就直接返回上一行
          sectionSeparatorItem['height'] =
            headerItem?.offsetTop - sectionSeparatorItem?.['top'];
          sectionSeparators.push(sectionSeparatorItem);
          sectionSeparatorItem = {};
          // 因为现在是header bottom一体
          // 从第二个开始 也要这样 先重置 然后再追加一个 以表示下一个开始
          sectionSeparatorItem = {
            top: headerItem?.offsetTop + headerItem?.offsetHeight,
            backgroundColor: item?.data?.props?.sectionHeaderBorderColor,
            id: item?.i,
          };
        }
      }

      if (item?.data?.component === 'SectionBottom') {
        let bottomItem = document.getElementById(item?.i);
        if (!isEmptyValues(sectionSeparatorItem)) {
          sectionSeparatorItem['height'] =
            bottomItem?.offsetTop - sectionSeparatorItem?.['top'];
          sectionSeparators.push(sectionSeparatorItem);
          sectionSeparatorItem = {};
        }
      }
    });

  // 可能最下面没有 但是建议 一对 TODO
  if (!isEmptyValues(sectionSeparatorItem)) {
  }

  console.log('sectionSeparators', sectionSeparators);

  return (
    <>
      {sectionSeparators
        ?.filter((item) => {
          return item?.height > 0 && item?.top >= 0;
        })
        ?.map((item) => {
          return (
            <>
              <div
                id={`${item?.id}-LEFT`}
                className={'section-separator'}
                style={{
                  left: 5,
                  height: item?.height,
                  top: item?.top,
                  backgroundColor: item?.backgroundColor,
                }}
              />
              <div
                id={`${item?.id}-RIGHT`}
                className={'section-separator'}
                style={{
                  right: 5,
                  height: item?.height,
                  top: item?.top,
                  backgroundColor: item?.backgroundColor,
                }}
              />
            </>
          );
        })}
    </>
  );
};

interface DmrHeaderProps {
  form?: FormInstance;
  viewMode: boolean;
  registerStatusName: string;
  layoutSizeKey?: string;
  headerLayouts: any;
  headerContentData: any;

  // configuration
  isDraggable?: boolean;
  isResizable?: boolean;
  onLayoutChange?: (layout, layouts) => void;
  onDragStop?: (
    newLayout,
    oldDragItem,
    newDragItem,
    placeHlder,
    e,
    node,
  ) => void;
  itemWrapper: React.FC;

  layoutForm?: any;
}

export const DmrHeader = (props: DmrHeaderProps) => {
  const dmrHeaderChildren = React.useMemo(() => {
    return props?.headerContentData?.map((item, index) => {
      let ItemWrapper = props?.itemWrapper;
      let itemWrapperProps = {
        id: item.data.key,
        key: item.data.key,
        item: item,
        index: index,
        type: 'HEADER',
        layoutForm: props?.layoutForm,
      };
      return (
        <GridItemContext.Provider
          value={{
            dynamicComponentsMap: dynamicComponentsMap,
            externalConfig: externalChsConfig,
            eventNames: {
              HELP_MODAL: EventConstant.INSURANCE_HELP_MODAL,
              TABLE_NEXT_KEY: EventConstant.CHS_TABLE_NEXT_KEY,
            },
            modelGroup: 'Insur',
          }}
        >
          <ItemWrapper {...itemWrapperProps} />
        </GridItemContext.Provider>
      );
    });
  }, [props?.headerContentData]);

  let defaultWidth =
    document.getElementById('dmr-form-container')?.getBoundingClientRect()
      ?.width - 19;
  console.log('defaultWidth', defaultWidth);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const hospitalName = globalState?.dictData?.['Hospital']?.find((item) => {
    return item?.Code === props?.form.getFieldValue('HospCode');
  })?.Name;

  return (
    <div className={'dmr-header'}>
      <div className={'header-title'}>
        <span className={'header-label'}>医疗保障基金结算清单</span>
        <div>
          {props?.registerStatusName === '已登记' && (
            <Tag color="green">{props?.registerStatusName}</Tag>
          )}
          {props?.registerStatusName === '未登记' && (
            <Tag color="orange">{props?.registerStatusName}</Tag>
          )}
          {props?.viewMode ? (
            <Tag color="geekblue">只读模式</Tag>
          ) : (
            <Tag color="magenta">编辑模式</Tag>
          )}

          {hospitalName && <Tag>{hospitalName}</Tag>}
        </div>
      </div>

      <div
        id={'dmr-header-grid-layout'}
        className="grid-stack"
        style={{ margin: '0px 5px' }}
      >
        {dmrHeaderChildren}
      </div>
    </div>
  );
};

export default CHS;
