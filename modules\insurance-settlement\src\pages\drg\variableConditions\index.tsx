import CardEchart from '@uni/components/src/cardEchart';
import { uniCommonService } from '@uni/services/src';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { SettleCompStatsByGrpLineBarOption } from '../chart.opts';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  Space,
  Select,
  Col,
  Row,
  InputNumber,
  Divider,
  Alert,
  TableProps,
  Card,
  Tooltip,
  Spin,
} from 'antd';
import CardWithBtns from '@uni/components/src/cardWithBtns';
import UniTable from '@uni/components/src/table';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import IconBtn from '@uni/components/src/iconBtn';
import ExportIconBtn from '@uni/components/src/backend-export';
import { CloseOutlined, RedoOutlined } from '@ant-design/icons';
import {
  FeeChargeDistributioByMedChrgitmTypeRadarOption,
  SettleCompStatsByCliDeptLineBarOption,
} from '@/pages/drg/chart.opts';
import UniEcharts from '@uni/components/src/echarts';
import {
  ColumnsSelectOptions,
  LengthSelectOptions,
  NumFilterTypeSelectOptions,
  FilterTypeSelectOptions,
} from './optsConstants';
import _ from 'lodash';
import './index.less';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';

const { Option } = Select;

const DrgHighVariableConditions = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, insurType } = globalState?.searchParams;
  const [settleCompStatsByGrpData, setSettleCompStatsByGrpData] = useState<any>(
    [],
  );
  const [settleCompStatsByGrpColumnsData, setSettleCompStatsByGrpColumnsData] =
    useState<any>([]);

  const [settleCompStatsByCliDeptData, setSettleCompStatsByCliDeptData] =
    useState<any>([]);
  const [
    settleCompStatsByCliDeptColumnsData,
    setSettleCompStatsByCliDeptColumnsData,
  ] = useState<any>([]);

  const [detailsColumns, setDetailsColumns] = useState([]);
  const [detailsDataSource, setDetailsDataSource] = useState([]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });
  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorter,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    detailsReq(tableParams, {
      current: pagi?.current,
      pageSize: pagi?.pageSize,
    });
  };

  const [tableParams, setTableParams] = useState<any>({});
  const [selectItemTableParams, setSelectItemTableParams] = useState<any>({});

  const [selectedTableItem, setSelectedTableItem] = useState<any>({});
  // table click
  useEffect(() => {
    Emitter.on(EventConstant.TABLE_ROW_CLICK, ({ record, index }) => {
      setSelectedTableItem(record);
    });
    return () => {
      Emitter.off(EventConstant.TABLE_ROW_CLICK);
    };
  }, [selectedTableItem]);

  // 费用组成
  const {
    data: feeChargeDistributionData,
    loading: getFeeChargeDistributionLoading,
    run: getFeeChargeDistributionReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestDrgSettleStats/FeeChargeDistribution`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.Stats?.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 费用组成 Columns
  const {
    data: feeChargeDistributionColumnsData,
    run: getFeeChargeDistributionColumnsReq,
    mutate: setFeeChargeDistributionColumnsData,
  } = useRequest(
    () =>
      uniCommonService(
        `Api/FundSupervise/LatestDrgSettleStats/FeeChargeDistribution`,
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  // 病种入组
  const {
    data: settleCompStatsByGrpOriginData,
    loading: getSettleCompStatsByGrpLoading,
    run: getSettleCompStatsByGrpReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByGrp`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.Stats.length) {
            setSettleCompStatsByGrpData(res.data.Stats);
            return res.data.Stats;
          } else return [];
        }
      },
    },
  );
  // 病种入组 Columns
  const { run: getSettleCompStatsByGrpColumnsReq } = useRequest(
    () =>
      uniCommonService(
        `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByGrp`,
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.Columns;
        }
      },
      onSuccess(data, params) {
        setSettleCompStatsByGrpColumnsData(tableColumnBaseProcessor([], data));
      },
    },
  );

  // 科室统计
  const {
    loading: getSettleCompStatsByCliDeptLoading,
    run: getSettleCompStatsByCliDeptReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByCliDept`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.Stats.length) {
            setSettleCompStatsByCliDeptData(res.data.Stats);
            return res.data.Stats;
          } else return [];
        }
      },
    },
  );
  // 科室统计 Columns
  const {
    run: getSettleCompStatsByCliDeptColumnsReq,
    mutate: mutateSettleCompStatsByCliDeptColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(
        `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByCliDept`,
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        console.log('SettleCompStatsByCliDept:', res);
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.Columns;
        }
      },
      onSuccess(data, params) {
        setSettleCompStatsByCliDeptColumnsData(
          tableColumnBaseProcessor([], data),
        );
      },
    },
  );
  // 病例数据
  const { loading: getDetailsLoading, run: detailsReq } = useRequest(
    (params, dtParams) =>
      uniCommonService(`Api/FundSupervise/LatestDrgSettleStats/SettleDetails`, {
        method: 'POST',
        data: {
          ...params,
          DtParam: {
            Draw: 1,
            Start: (dtParams.current - 1) * dtParams.pageSize,
            Length: dtParams.pageSize,
          },
        },
      }),
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setDetailsDataSource(response?.data?.data);
          setBackPagination({
            ...backPagination,
            total: response?.data?.recordsTotal || 0,
          });
        } else {
          setDetailsDataSource([]);
        }
      },
    },
  );
  const { loading: columnsLoading, run: columnsReq } = useRequest(
    () => {
      return uniCommonService(
        `Api/FundSupervise/LatestDrgSettleStats/SettleDetails`,
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let data = response?.data?.Columns;
          setDetailsColumns(
            tableColumnBaseProcessor(
              [
                {
                  dataIndex: 'operation',
                  visible: true,
                  width: 40,
                  align: 'center',
                  title: '',
                  fixed: 'left',
                  render: (node, record, index) => {
                    return (
                      <IconBtn
                        title="查看病案首页"
                        type="details2"
                        className="operation-btn"
                        onClick={(e) => {
                          window.open(
                            `/chs/analysis/cardInfo?hisId=${encodeURIComponent(
                              record.HisId,
                            )}&type=drg`,
                          );
                        }}
                      />
                    );
                  },
                },
              ],
              data,
            ),
          );
        } else {
          setDetailsColumns([]);
        }
      },
    },
  );

  /* 按条件筛选 */
  const [selectedColumn, setSelectedColumn] = useState('Cv');
  const [selectedOrder, setSelectedOrder] = useState('desc');
  const [selectedLength, setSelectedLength] = useState(500);
  const [selectedNumFilterType, setSelectedNumFilterType] = useState(1);
  const [selectedFilterType, setSelectedFilterType] = useState(1);
  const [selectedFilterNum, setSelectedFilterNum] = useState(1);
  // const [changeValue, setChangeValue] = useState(1);

  useEffect(() => {
    if (settleCompStatsByGrpOriginData?.length) {
      let data = settleCompStatsByGrpOriginData || [];
      data = _.orderBy(data, [selectedColumn], [selectedOrder]);
      if (selectedFilterType === 1) {
        data = data.filter((item) => {
          if (
            selectedNumFilterType === 1 &&
            item[selectedColumn] >= selectedFilterNum
          )
            return item;
          if (
            selectedNumFilterType === 2 &&
            item[selectedColumn] < selectedFilterNum
          )
            return item;
        });
      }
      if (selectedFilterType === 2) {
        data = data?.slice(0, selectedLength);
      }
      setSettleCompStatsByGrpData(data);
    }
  }, [
    selectedColumn,
    selectedOrder,
    selectedLength,
    selectedFilterType,
    selectedNumFilterType,
    selectedFilterNum,
    settleCompStatsByGrpOriginData,
  ]);

  useEffect(() => {
    if (
      settleCompStatsByGrpData?.length &&
      settleCompStatsByGrpData[0]?.VersionedChsDrgCode
    )
      setSelectedTableItem(settleCompStatsByGrpData[0]);
  }, [settleCompStatsByGrpData]);
  /* */

  useEffect(() => {
    getSettleCompStatsByGrpColumnsReq();
    getFeeChargeDistributionColumnsReq();
    getSettleCompStatsByCliDeptColumnsReq();
    columnsReq();
  }, []);

  useEffect(() => {
    if (dateRange?.length) {
      let params = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        insurType,
      };
      setTableParams(params);
    }
  }, [dateRange, hospCodes, insurType]);

  useEffect(() => {
    if (Object.keys(tableParams)?.length) {
      getSettleCompStatsByGrpReq(tableParams);
    }
  }, [tableParams]);

  useEffect(() => {
    if (
      Object.keys(tableParams)?.length &&
      Object.keys(selectedTableItem)?.length
    ) {
      let params = {
        ...tableParams,
        VersionedChsDrgCodes: [selectedTableItem?.VersionedChsDrgCode],
      };
      setSelectItemTableParams(params);
    }
  }, [selectedTableItem, tableParams]);

  useEffect(() => {
    if (Object.keys(selectItemTableParams)?.length) {
      getFeeChargeDistributionReq(selectItemTableParams);
      getSettleCompStatsByCliDeptReq(selectItemTableParams);
      detailsReq(selectItemTableParams, { current: 1, pageSize: 10 });
    }
  }, [selectItemTableParams]);

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div className="grp-filter-div">
            <Space size={10}>
              <div>高差异度病组监控</div>
              <Select
                style={{ width: 100 }}
                placeholder="请选择列"
                disabled={getSettleCompStatsByGrpLoading}
                allowClear={false}
                defaultValue={selectedColumn}
                options={ColumnsSelectOptions}
                onSelect={(value) => setSelectedColumn(value)}
              />
              <Divider type="vertical" style={{ height: '32px' }} />
              <Select
                placeholder="请选择排序方式"
                allowClear={false}
                disabled={getSettleCompStatsByGrpLoading}
                defaultValue={selectedOrder}
                onSelect={(value) => setSelectedOrder(value)}
                options={[
                  { value: 'desc', label: '降序' },
                  { value: 'asc', label: '升序' },
                ]}
              />
              <Space.Compact>
                <Select
                  placeholder="请选择筛选方式"
                  allowClear={false}
                  disabled={getSettleCompStatsByGrpLoading}
                  defaultValue={selectedFilterType}
                  options={FilterTypeSelectOptions}
                  onSelect={(value) => setSelectedFilterType(value)}
                />
                {selectedFilterType === 1 && (
                  <>
                    <Select
                      placeholder="请选择符号"
                      allowClear={false}
                      disabled={getSettleCompStatsByGrpLoading}
                      defaultValue={selectedNumFilterType}
                      options={NumFilterTypeSelectOptions}
                      onSelect={(value) => setSelectedNumFilterType(value)}
                    />
                    <InputNumber
                      style={{ width: 100 }}
                      value={selectedFilterNum}
                      precision={1}
                      autoFocus={true}
                      disabled={getSettleCompStatsByGrpLoading}
                      placeholder="输入数值"
                      onChange={(v) => setSelectedFilterNum(v)}
                    />
                  </>
                )}
                {selectedFilterType === 2 && (
                  <>
                    <Select
                      placeholder="请选择前X位"
                      allowClear={false}
                      disabled={getSettleCompStatsByGrpLoading}
                      defaultValue={selectedLength}
                      options={LengthSelectOptions}
                      onSelect={(value) => setSelectedLength(value)}
                    />
                  </>
                )}
              </Space.Compact>
            </Space>
          </div>
        </Col>

        <Col xs={24} sm={24} md={24} lg={24} xl={24}>
          <CardWithBtns
            content={
              <>
                <UniTable
                  onRow={(record, index) => ({
                    onClick: (event) => {
                      Emitter.emit(EventConstant.TABLE_ROW_CLICK, {
                        record,
                        index,
                      });
                    },
                  })}
                  loading={getSettleCompStatsByGrpLoading}
                  id={'variable-conditions-table'}
                  className={`settle-comp-stats-by-adrg-table clickable`}
                  rowKey={'id'}
                  scroll={{
                    x: 'max-content',
                  }}
                  rowClassName={(record, index) => {
                    if (record?.VersionedChsDrgCode)
                      return record.VersionedChsDrgCode ===
                        selectedTableItem?.VersionedChsDrgCode
                        ? 'row-selected'
                        : '';
                  }}
                  pagination={{
                    pageSize: 5,
                    pageSizeOptions: ['5', '10', '20'],
                  }}
                  columns={settleCompStatsByGrpColumnsData || []}
                  dataSource={settleCompStatsByGrpData || []}
                />
              </>
            }
            needExport={true}
            exportTitle={'高差异病组列表'}
            exportData={settleCompStatsByGrpData}
            exportColumns={settleCompStatsByGrpColumnsData}
            columnsEditableUrl="Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByGrp"
            onColumnChange={(newColumns) => {
              setSettleCompStatsByGrpColumnsData(
                tableColumnBaseProcessor([], newColumns),
              );
            }}
            needModalDetails={true}
            onRefresh={() => {
              getSettleCompStatsByGrpReq(tableParams);
            }}
          />
        </Col>
        <Col span={24}>
          {Object.keys(selectedTableItem)?.length ? (
            <h2>{`${selectedTableItem?.ChsDrgCode} ${selectedTableItem?.ChsDrgName}`}</h2>
          ) : (
            <Alert message="请点击表格行" type="warning" />
          )}
        </Col>
        {Object.keys(selectedTableItem)?.length && (
          <>
            <Col xs={24} sm={24} md={12} lg={8} xl={7}>
              <CardWithBtns
                title="费用构成分析"
                content={
                  <UniEcharts
                    elementId={'radar'}
                    height={250}
                    loading={getFeeChargeDistributionLoading || false}
                    options={
                      (feeChargeDistributionData !== 'apiErr' &&
                        feeChargeDistributionData &&
                        FeeChargeDistributioByMedChrgitmTypeRadarOption(
                          feeChargeDistributionData,
                          'MedChrgitmTypeName',
                        )) ||
                      {}
                    }
                  />
                }
                needExport={true}
                exportTitle={'费用类型盈亏分析'}
                exportData={feeChargeDistributionData?.Stats ?? []}
                exportColumns={feeChargeDistributionColumnsData}
                needModalDetails={true}
                onRefresh={() => {
                  getFeeChargeDistributionReq(selectItemTableParams);
                }}
                columnsEditableUrl={
                  'Api/FundSupervise/LatestDrgSettleStats/FeeChargeDistribution'
                }
                onColumnChange={(columns) => {
                  setFeeChargeDistributionColumnsData(
                    tableColumnBaseProcessor([], columns),
                  );
                }}
              />
            </Col>
            <Col xs={24} sm={24} md={12} lg={16} xl={17}>
              <CardWithBtns
                title="科室费用统计"
                content={
                  <UniEcharts
                    elementId={'radar'}
                    height={250}
                    loading={getSettleCompStatsByCliDeptLoading || false}
                    options={
                      (settleCompStatsByCliDeptData !== 'apiErr' &&
                        settleCompStatsByCliDeptData &&
                        SettleCompStatsByCliDeptLineBarOption(
                          settleCompStatsByCliDeptData,
                          'CliDeptName',
                        )) ||
                      {}
                    }
                  />
                }
                needExport={true}
                exportTitle={'科室费用统计'}
                exportData={settleCompStatsByCliDeptData ?? []}
                exportColumns={settleCompStatsByCliDeptColumnsData}
                needModalDetails={true}
                onRefresh={() => {
                  getSettleCompStatsByCliDeptReq(selectItemTableParams);
                }}
                columnsEditableUrl={
                  'Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByCliDept'
                }
                onColumnChange={(columns) => {
                  setSettleCompStatsByCliDeptColumnsData(
                    tableColumnBaseProcessor([], columns),
                  );
                }}
              />
            </Col>
            <Col span={24}>
              <Card
                title={`${selectedTableItem?.ChsDrgCode} 病例数据`}
                className={`chart-card`}
                extra={
                  <Space className="btn_space">
                    <Divider type="vertical" />
                    <Tooltip title="刷新">
                      <RedoOutlined
                        style={{
                          width: '32px',
                          height: '32px',
                          lineHeight: '32px',
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setBackPagination({
                            ...backPagination,
                            current: 1,
                          });
                          detailsReq(selectItemTableParams, {
                            current: 1,
                            pageSize: backPagination?.pageSize,
                          });
                        }}
                      />
                    </Tooltip>
                    <ExportIconBtn
                      isBackend={true}
                      backendObj={{
                        url: `Api/FundSupervise/LatestDrgSettleStats/ExportSettleDetails`,
                        method: 'POST',
                        data: selectItemTableParams,
                        fileName: `病例数据`,
                      }}
                      btnDisabled={
                        detailsDataSource?.length < 1 ||
                        !detailsDataSource ||
                        (detailsDataSource?.length === 1 &&
                          !detailsDataSource?.at(0))
                      }
                    />
                    <TableColumnEditButton
                      {...{
                        columnInterfaceUrl:
                          'Api/FundSupervise/LatestDrgSettleStats/SettleDetails',
                        onTableRowSaveSuccess: (columns) => {
                          setDetailsColumns(
                            tableColumnBaseProcessor(
                              [
                                {
                                  dataIndex: 'operation',
                                  visible: true,
                                  width: 40,
                                  align: 'center',
                                  title: '',
                                  fixed: 'left',
                                  render: (node, record, index) => {
                                    return (
                                      <IconBtn
                                        title="查看病案首页"
                                        type="details2"
                                        className="operation-btn"
                                        onClick={(e) => {
                                          window.open(
                                            `/chs/analysis/cardInfo?hisId=${encodeURIComponent(
                                              record.HisId,
                                            )}&type=drg`,
                                          );
                                        }}
                                      />
                                    );
                                  },
                                },
                              ],
                              columns,
                            ),
                          );
                        },
                      }}
                    />
                  </Space>
                }
              >
                <UniTable
                  id={'detail-table'}
                  rowKey={'CardId'}
                  dictionaryData={globalState?.dictData}
                  scroll={{ x: 'max-content' }}
                  loading={getDetailsLoading || columnsLoading}
                  columns={detailsColumns}
                  dataSource={detailsDataSource}
                  pagination={backPagination}
                  onChange={backTableOnChange}
                  toolBarRender={null}
                  isBackPagination
                />
              </Card>
            </Col>
          </>
        )}
      </Row>
    </>
  );
};

export default DrgHighVariableConditions;
