import { LinkOutlined, HistoryOutlined } from '@ant-design/icons';
import IconBtn from '@uni/components/src/iconBtn/index';
import { Emitter } from '@uni/utils/src/emitter';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { Space } from 'antd';
import { EVENTS } from './constants';
import { HierarchyBedAmtItem } from './interface';

// 创建部门历史列定义，不包括操作列，根据IsValid字段设置文本颜色
export const getDeptHistoryColumns = () => [
  {
    title: '',
    key: 'options',
    fixed: 'left' as 'left', // 指定TypeScript类型
    visible: true,
    width: 30,
    render: (_, record: HierarchyBedAmtItem) => (
      <Space size="small">
        <IconBtn
          type="details" // 使用系统内置的details图标
          customIcon={<LinkOutlined />}
          openPop
          popOnConfirm={() => Emitter.emit(EVENTS.BULK_UPDATE_BED, record)}
          title="更新住院动态登记床位数"
        />
      </Space>
    ),
  },
  {
    dataIndex: 'HospName',
    key: 'HospName',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
  {
    dataIndex: 'HierarchyName',
    key: 'HierarchyName',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
  {
    dataIndex: 'HierarchyCode',
    key: 'HierarchyCode',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
  {
    dataIndex: 'Sdate',
    key: 'Sdate',
    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {console.log(
          'Sdate text',
          record,
          text,
          valueNullOrUndefinedReturnDash(text, 'DateByDash'),
        )}
        {valueNullOrUndefinedReturnDash(record?.Sdate, 'DateByDash')}
      </span>
    ),
  },
  {
    dataIndex: 'Edate',
    key: 'Edate',
    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {valueNullOrUndefinedReturnDash(record?.Edate, 'DateByDash')}
      </span>
    ),
  },
  {
    dataIndex: 'ApprovedBedAmt',
    key: 'ApprovedBedAmt',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
  {
    dataIndex: 'OpenBedAmt',
    key: 'OpenBedAmt',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
  {
    dataIndex: 'Reserve1Amt',
    key: 'Reserve1Amt',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
  {
    dataIndex: 'Reserve2Amt',
    key: 'Reserve2Amt',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
  {
    dataIndex: 'Reserve3Amt',
    key: 'Reserve3Amt',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
  {
    dataIndex: 'Reserve4Amt',
    key: 'Reserve4Amt',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
  {
    dataIndex: 'Reserve5Amt',
    key: 'Reserve5Amt',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
  {
    dataIndex: 'StatusName',
    key: 'StatusName',

    render: (text, record) => (
      <span style={{ color: record.IsValid ? 'inherit' : '#999999' }}>
        {text}
      </span>
    ),
  },
];
