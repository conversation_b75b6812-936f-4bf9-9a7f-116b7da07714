import {
  ProForm,
  ProFormDependency,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@uni/components/src/pro-form';
import { AutoComplete, Form } from 'antd';
const ScheduleSettings = ({ recordDetail, dictData }) => {
  return (
    <>
      <ProForm.Group title="ScheduleSetting">
        <ProFormSelect
          name={['ScheduleArgs', 'ScheduleMode']}
          label="排期模式"
          initialValue={
            recordDetail?.ScheduleSetting?.ScheduleMode || undefined
          }
          placeholder="请选择"
          width="sm"
          options={dictData.ScheduleMode}
          fieldProps={{
            fieldNames: {
              label: 'Name',
              value: 'Code',
            },
          }}
          rules={[{ required: true }]}
          colProps={{
            span: 6,
          }}
        />
        <ProFormText
          name={['ScheduleArgs', 'BatchNameFormatter']}
          label="批次名称表达式"
          // width="md"
          initialValue={
            recordDetail?.ScheduleSetting?.BatchNameFormatter || undefined
          }
          placeholder="请输入批次名称表达式"
          colProps={{
            span: 6,
          }}
        />
        <ProFormDependency name={[['ScheduleArgs', 'ScheduleMode']]}>
          {({ ScheduleArgs }) => {
            console.log(ScheduleArgs, dictData);
            if (ScheduleArgs?.ScheduleMode === 'Periodical') {
              return (
                <>
                  <ProFormSelect
                    name={['ScheduleArgs', 'PeriodicalCycle']}
                    label="任务周期"
                    initialValue={
                      recordDetail?.ScheduleSetting?.PeriodicalCycle ||
                      'ByMonth'
                    }
                    placeholder="请选择"
                    width="sm"
                    options={dictData.DateGranularity}
                    fieldProps={{
                      fieldNames: {
                        label: 'Name',
                        value: 'Code',
                      },
                    }}
                    rules={[{ required: true }]}
                    colProps={{
                      span: 6,
                    }}
                  />
                  {/* <ProFormSelect
                    name={['ScheduleArgs', 'ScheduleCrons']}
                    label="采样执行调度"
                    // width="md"
                    initialValue={
                      recordDetail?.ScheduleSetting?.ScheduleCrons || undefined
                    }
                    fieldProps={{
                      mode: 'tags',
                      maxTagCount: 5,
                    }}
                    placeholder="请输入"
                    colProps={{
                      span: 6,
                    }}
                  /> */}
                </>
              );
            }
          }}
        </ProFormDependency>
      </ProForm.Group>
    </>
  );
};

export default ScheduleSettings;
