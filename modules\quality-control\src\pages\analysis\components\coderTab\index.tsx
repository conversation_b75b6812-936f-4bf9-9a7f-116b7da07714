import CardWithBtns from '@uni/components/src/cardWithBtns/index';
import { Col, Row } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src/commonService';
import { RespVO } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { UniTable } from '@uni/components/src/index';
import _ from 'lodash';
import PieTreeTrend from '@/pages/analysis/components/pieTreeTrend/index';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { isEmptyValues } from '@uni/utils/src/utils';
import IconBtn from '@uni/components/src/iconBtn/index';

const CoderTab = ({ detailAction, flagRef, flagRefPlus }) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, errorLevel, coders, qualityMonitorLevel } =
    globalState.searchParams;
  const [tableParams, setTableParams] = useState(undefined);
  const [pieTrendParams, setPieTrendParams] = useState(undefined);
  const [clkedRecord, setClkedRecord] = useState(undefined);
  // const flagRef = useRef(1);

  useEffect(() => {
    // if (dateRange?.length) {
    let params = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: isEmptyValues(hospCodes) ? undefined : hospCodes,
      ErrorLevel: errorLevel,
      Coders: coders,
      MonitorLevel: qualityMonitorLevel,
    };

    if (flagRef === 1) {
      // setTimeout(() => {
      //   if (flagRef.current === 1) {
      //     flagRefPlus();
      //   }
      // }, 1000);
    } else {
      if (globalState?.searchParams?.triggerSource !== 'btnClick') {
      } else {
        setTableParams(params);
      }
    }
    // }
  }, [dateRange, hospCodes, errorLevel, coders, qualityMonitorLevel]);

  useEffect(() => {
    if (clkedRecord?.Coder) {
      setPieTrendParams({
        ...tableParams,
        Coders: [clkedRecord?.Coder],
      });
    } else {
      setPieTrendParams({
        ...tableParams,
        Coders: [],
      });
    }
  }, [clkedRecord, tableParams]);

  useEffect(() => {
    if (tableParams) {
      getQcStatsOfCoderReq(tableParams);
    }
  }, [tableParams]);

  useEffect(() => {
    if (!QcStatsOfCoderColumnsData?.length) {
      getQcStatsOfCoderColumnsReq();
    }
  }, []);

  const {
    data: qcStatsOfCoderData,
    loading: getQcStatsOfCoderLoading,
    run: getQcStatsOfCoderReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/Dmr/DmrQcStats/QcStatsOfCoder', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.Data?.length) {
            let data = _.orderBy(res.data.Data, ['ErrorCount'], 'desc');
            setClkedRecord(data[0]);
            return data;
          } else {
            return [];
          }
        } else return [];
      },
    },
  );

  // Columns
  const {
    data: QcStatsOfCoderColumnsData,
    loading: getQcStatsOfCoderColumnsLoading,
    mutate: mutateQcStatsOfCoderColumns,
    run: getQcStatsOfCoderColumnsReq,
  } = useRequest(
    () =>
      uniCommonService('Api/Dmr/DmrQcStats/QcStatsOfCoder', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <CardWithBtns
            title={'编码员问题统计'}
            content={
              <UniTable
                id={'dmr-data-coder-table'}
                rowKey={'Coder'}
                columns={[
                  {
                    dataIndex: 'options',
                    width: 40,
                    visible: true,
                    fixed: 'left',
                    render: (text, record) => (
                      <IconBtn
                        type="details"
                        style={{ margin: '0 10px' }}
                        onClick={(e) => {
                          detailAction && detailAction(record);
                        }}
                      />
                    ),
                  },
                ].concat(QcStatsOfCoderColumnsData)}
                dataSource={qcStatsOfCoderData}
                loading={getQcStatsOfCoderLoading}
                clickable={true}
                // pagination={false}
                dictionaryData={globalState?.dictData}
                onRow={(record) => {
                  return {
                    onClick: (event) => {
                      setClkedRecord(record);
                    },
                  };
                }}
                rowClassName={(record, index) => {
                  if (record?.Coder)
                    return record.Coder === clkedRecord?.Coder
                      ? 'row-selected'
                      : '';
                }}
              />
            }
            needExport={true}
            exportTitle={'编码员问题统计'}
            exportData={qcStatsOfCoderData}
            exportColumns={QcStatsOfCoderColumnsData}
            needModalDetails={true}
            onRefresh={() => {
              getQcStatsOfCoderReq(tableParams);
            }}
          />
        </Col>
        <Col span={24}>
          <h3>
            <b>
              {clkedRecord?.Coder
                ? `${clkedRecord?.CoderName} 统计分析`
                : '请选择编码员'}
            </b>
          </h3>
        </Col>
        <Col span={24}>
          <PieTreeTrend
            tableParams={pieTrendParams}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: '',
                args: {
                  ...pieTrendParams,
                  ...record?.args,
                },
                detailsUrl: 'Dmr/DmrQcStats/GetQcCards',
                dictData: dictData,
              });
            }}
          />
        </Col>
      </Row>
    </>
  );
};
export default CoderTab;
