import { Key, Reducer, useEffect, useMemo, useReducer, useState } from 'react';
import {
  <PERSON>ton,
  Card,
  Col,
  Divider,
  Modal,
  Progress,
  Row,
  Select,
  Space,
  Spin,
  Tooltip,
  Tree,
  message,
  Checkbox,
} from 'antd';
import ProgressModal from '@/components/data-flow-progress-modal';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { ReqActionType } from '../../constants';
import { Dispatch, useDispatch, useRequest, useSelector } from 'umi';
import {
  columnsHandler,
  formatDuration,
  isRespErr,
  loop,
  RespType,
} from '@/utils/widgets';
import _ from 'lodash';
import {
  IReducer,
  IModalState,
  ITableState,
} from '@uni/reducers/src/interface';
import { ModalAction } from '@uni/reducers/src/modalReducer';
import {
  InitTableState,
  TableAction,
  modalReducer,
  tableReducer,
} from '@uni/reducers/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { useSafeState } from 'ahooks';
import { CloseOutlined, PushpinOutlined } from '@ant-design/icons';
import { UniTable } from '@uni/components/src';
import dayjs from 'dayjs';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { uniCommonService } from '@uni/services/src';
import { RecordDetailColumns } from './columns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';

export interface IIsurDataFlowProps {
  searchParams: any;
}

const PreDataDataFlow = ({ searchParams }: IIsurDataFlowProps) => {
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  // summarys data
  const [SummaryTableState, SummaryTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, {
    ...InitTableState,
  });

  // 审核 modal
  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer>
  >(modalReducer, {
    visible: false,
    record: undefined,
    dataSource: [],
  });
  // 审核 modal 新增一个 DeriveFromEmr
  const [deriveFromEmr, setDeriveFromEmr] = useState(false);

  // record detail modal
  const [RecordDetailModalState, RecordDetailModalStateDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer>
  >(modalReducer, {
    visible: false,
    record: undefined,
    dataSource: [],
  });

  // progress visible
  const [progressOpen, setProgressOpen] = useSafeState(false);
  // progress extra select value
  const [progressExtraAction, setProgressExtraAction] = useState(undefined);

  const [totalActions, setTotalActions] = useSafeState([]);

  useEffect(() => {
    if (
      columnsList?.['PreDataSummary'] &&
      SummaryTableState.columns.length < 1
    ) {
      SummaryTableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: tableColumnBaseProcessor([], columnsList['PreDataSummary']),
        },
      });
    }
  }, [columnsList, SummaryTableState.columns]);

  const columnsSolver = useMemo(() => {
    return SummaryTableState.columns.length > 0
      ? columnsHandler(SummaryTableState.columns, {})
      : [];
  }, [SummaryTableState.columns, searchParams]);

  // summary fetch
  const anyReq = async (data, reqType, method) => {
    // if (!data) return;
    let res: any = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: reqType,
        requestParams: {
          requestType: 'json',
          url: reqType,
          method,
          data,
        },
      },
    });
    if (!isRespErr(res)) {
      if (reqType.includes('Api/Maint/PreDataMgmt/GetSummary')) {
        SummaryTableDispatch({
          type: TableAction.dataChange,
          payload: {
            data: res.data ?? [],
          },
        });
      } else if (reqType.includes('Api/Maint/PreDataMgmt/GetActions')) {
        setTotalActions(res.data);
      } else if (reqType.includes('Api/Maint/PreDataMgmt/Apply')) {
        message.success('申请成功');
        ModalStateDispatch({
          type: ModalAction.init,
        });
        setDeriveFromEmr(false);
      }
    }
  };

  useEffect(() => {
    if (searchParams.dateRange) {
      let requestObj = {
        Sdate: dayjs(searchParams?.dateRange?.at(0))
          .startOf('M')
          .format('YYYY-MM-DD'),
        Edate: dayjs(searchParams?.dateRange?.at(1))
          .endOf('M')
          .format('YYYY-MM-DD'),
        HospCode:
          searchParams?.hospCodes?.length ?? 0 > 0
            ? searchParams?.hospCodes
            : [],
      };
      anyReq(requestObj, 'Api/Maint/PreDataMgmt/GetSummary', 'POST');
      anyReq(undefined, 'Api/Maint/PreDataMgmt/GetActions', 'POST');
      // tableReq(requestObj);
    }
  }, [searchParams]);

  // record columns
  const { data: recordTotalColumns } = useRequest(
    async (apiObj, cb = null) => {
      const res: RespType<any> = await uniCommonService(
        'Api/Maint/PreDataMgmt/GetRecords',
        {
          method: apiObj?.method ?? 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
      return res;
    },
    {
      // manual: true,
      formatResult: (res: RespType<any>) => {
        if (res?.code === 0) {
          return {
            record: tableColumnBaseProcessor([], res?.data?.Columns),
            currentProgress: tableColumnBaseProcessor(
              [],
              _.values(res?.data?.DictColumns?.CurrentProgress),
            ),
            details: tableColumnBaseProcessor(
              RecordDetailColumns,
              res?.data?.Columns,
            ),
          };
        }
        return null;
      },
    },
  );

  // process task 内部action按钮处理的api req
  const { loading: actionsLoading, run: actionsReq } = useRequest(
    async (apiObj, cb = null) => {
      const res: RespType<any> = await uniCommonService(
        apiObj?.params
          ? `${apiObj?.url}/${apiObj?.params?.['Id']}`
          : apiObj?.url,
        {
          method: apiObj?.method ?? 'POST',
          params: apiObj?.params,
          data: apiObj?.data,
        },
      );
      return res;
    },
    {
      manual: true,
      formatResult: (res: RespType<any>) => res,
      onSuccess(res, params) {
        if (res.code === 0) {
          if (params?.at(1) && params?.at(0)?.url === '') {
            params?.at(1)();
          }
        }
      },
    },
  );

  // 处理 process task 监听 进度内操作
  useEffect(() => {
    // cb: check modalShow 需要传入title，res
    // 其实好像应该在外面处理
    Emitter.on(EventConstant.PROGRESS_CHECK_DETAIL, (item) => {
      RecordDetailModalStateDispatch({
        type: ModalAction.change,
        payload: {
          visible: true,
          record: `创建时间：
              ${valueNullOrUndefinedReturnDash(
                item?.CreationTime,
                'dateTime',
              )}`,
          dataSource: [item],
        },
      });
    });

    Emitter.on(EventConstant.PROGRESS_RESTART, ({ item, cb }) => {
      actionsReq(
        {
          url: 'Api/Maint/PreDataMgmt/Restart',
          params: { Id: item?.Id },
        },
        cb,
      );
    });

    Emitter.on(EventConstant.PROGRESS_DELETE, ({ item, cb }) => {
      actionsReq(
        {
          url: 'Api/Maint/PreDataMgmt/CancelJob',
          params: { Id: item?.Id },
        },
        cb,
      );
    });

    return () => {
      Emitter.off(EventConstant.PROGRESS_CHECK_DETAIL);
      Emitter.off(EventConstant.PROGRESS_RESTART);
      Emitter.off(EventConstant.PROGRESS_DELETE);
    };
  }, []);

  console.log('deriveFromEmr', deriveFromEmr);

  return (
    <>
      <Row wrap={false} gutter={8} style={{ overflow: 'hidden' }}>
        <Col flex="auto">
          <Card
            title="编码前首页数据管理"
            extra={
              <Space>
                <Divider type="vertical" />
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl: 'Api/Maint/PreDataMgmt/GetSummary',
                    onTableRowSaveSuccess: (columns) => {
                      // 这个columns 存到dva
                      dispatch({
                        type: 'global/saveColumns',
                        payload: {
                          name: 'PreDataSummary',
                          value: columns,
                        },
                      });
                      SummaryTableDispatch({
                        type: TableAction.columnsChange,
                        payload: {
                          columns: tableColumnBaseProcessor([], columns),
                        },
                      });
                    },
                  }}
                />
                <Tooltip title={progressOpen ? '关闭进度' : '查看进度'}>
                  <Button
                    type="text"
                    shape="circle"
                    icon={<PushpinOutlined className="infinity_shake_bottom" />}
                    onClick={(e) => {
                      setProgressOpen(!progressOpen);
                    }}
                  />
                  {/* <PushpinOutlined
                    onClick={(e) => {
                      setProgressOpen(!progressOpen);
                    }}
                  /> */}
                </Tooltip>
              </Space>
            }
          >
            <UniTable
              id="data_flow"
              rowKey="Key"
              forceColumnsUpdate
              loading={
                loadings['InsurFlowGetRecords'] ||
                loadings?.['Api/Maint/PreDataMgmt/GetSummary'] ||
                loadings?.['Api/Maint/PreDataMgmt/GetActions']
              }
              columns={columnsSolver}
              dataSource={SummaryTableState.data}
              // pagination={backPagination}
              // onChange={backTableOnChange}
              tableAlertRender={() => (
                <Space size={24}>
                  {SummaryTableState.selectedKeys.length !== 0 && (
                    <span>
                      已选 {SummaryTableState.selectedKeys.length} 项
                      <Space size={16}>
                        <Spin
                          spinning={false}
                          style={{ display: 'inlineBlock' }}
                        >
                          <a
                            style={{ marginLeft: 8 }}
                            onClick={(e) => {
                              ModalStateDispatch({
                                type: ModalAction.change,
                                payload: {
                                  ...ModalState,
                                  visible: true,
                                },
                              });
                            }}
                          >
                            审核
                          </a>
                        </Spin>
                      </Space>
                    </span>
                  )}
                </Space>
              )}
              rowSelection={{
                fixed: true,
                selectedRowKeys: SummaryTableState.selectedKeys,
                onChange: (selectedRowKeys, selectedRows) => {
                  SummaryTableDispatch({
                    type: TableAction.selectionChange,
                    payload: {
                      selectedKeys: selectedRowKeys,
                      selectedRecords: selectedRows,
                    },
                  });
                },
                selections: [
                  {
                    key: 'selectAll',
                    text: '全选所有',
                    onSelect: async () => {
                      SummaryTableDispatch({
                        type: TableAction.selectionChange,
                        payload: {
                          selectedKeys: SummaryTableState.data?.map(
                            (d) => d?.Key,
                          ),
                          selectedRecords: SummaryTableState.data,
                        },
                      });
                    },
                  },
                  {
                    key: 'clearAll',
                    text: '清空所有',
                    onSelect: async () => {
                      SummaryTableDispatch({
                        type: TableAction.selectionChange,
                        payload: {
                          selectedKeys: [],
                          selectedRecords: [],
                        },
                      });
                    },
                  },
                ],
              }}
              scroll={{ x: 'max-content' }}
            />
            <Modal
              open={ModalState.visible}
              title={'选择要进行的操作'}
              onCancel={(e) => {
                ModalStateDispatch({
                  type: ModalAction.init,
                });
                setDeriveFromEmr(false);
              }}
              onOk={async (e) => {
                let res = await anyReq(
                  {
                    Actions: ModalState.record,
                    DeriveFromEmr: deriveFromEmr,
                    HospMonths: SummaryTableState.selectedRecords?.map((d) => ({
                      HospCode: d.HospCode,
                      ExactMonth: d.ExactMonth,
                    })),
                  },
                  'Api/Maint/PreDataMgmt/Apply',
                  'POST',
                );
              }}
              confirmLoading={loadings?.['Api/Maint/PreDataMgmt/Apply']}
            >
              <Tree
                checkable
                checkStrictly
                defaultExpandAll
                onCheck={(checkedKeys: {
                  checked: Key[];
                  halfChecked: Key[];
                }) => {
                  let temp = _.cloneDeep(totalActions);
                  checkedKeys?.checked?.map((d: string) => {
                    loop(temp, d, 'Code', (item, index, arr) => {
                      arr.splice(index, 1, { ...item, Selected: true });
                    });
                  });

                  ModalStateDispatch({
                    type: ModalAction.change,
                    payload: {
                      ...ModalState,
                      dataSource: checkedKeys,
                      record: temp, // 最终actions
                    },
                  });
                }}
                checkedKeys={ModalState.dataSource}
                selectable={false}
                treeData={totalActions}
                fieldNames={{
                  title: 'Name',
                  key: 'Code',
                  children: 'Children',
                }}
              />
              <Divider style={{ margin: '12px 0' }} />
              <Checkbox
                style={{ marginLeft: '24px' }}
                checked={deriveFromEmr}
                onChange={(e) => setDeriveFromEmr(e.target.checked)}
              >
                <strong>是否要将上述操作通过医生端导入</strong>
              </Checkbox>
            </Modal>
          </Card>
        </Col>

        {/* progress */}
        <ProgressModal
          title="申请进度"
          dataApiObj={{
            url: 'Api/Maint/PreDataMgmt/GetRecords',
            method: 'POST',
            backendType: 'dyn',
            data: { actionCode: progressExtraAction },
          }}
          listItemRender={(item) => (
            <div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '4px',
                }}
              >
                <span style={{ width: '150px', marginRight: '5px' }}>
                  {item?.HospName}&nbsp;&nbsp;&nbsp;
                  {valueNullOrUndefinedReturnDash(item?.ExactMonth, 'Month')}
                </span>
                <span style={{ marginRight: '8px' }}>
                  耗时：
                  {item?.ExecutionEnd
                    ? formatDuration(item?.ExecutionStart, item?.ExecutionEnd)
                    : '至今'}
                </span>
              </div>
              <div style={{ display: 'flex' }}>
                <span
                  style={{
                    color:
                      item?.JobResultStatus === '已完成'
                        ? item?.Progress === 100
                          ? '#18ba56'
                          : '#eb5757'
                        : '#1464f8',
                    marginRight: '8px',
                  }}
                >
                  {item.JobResultStatus}
                </span>
                <span>{item?.ActionName}</span>
              </div>
              <div style={{ display: 'flex' }}>
                <Progress
                  style={{ width: '175px' }}
                  strokeColor={'#597ef7'}
                  percent={item?.Progress}
                />
                <div
                  style={{
                    fontSize: '1em',
                    marginLeft: '8px',
                    paddingTop: '1px',
                    display: 'inline-block',
                  }}
                >{`${item?.Count} / ${item?.Total}`}</div>
              </div>
            </div>
          )}
          isBackend
          open={progressOpen}
          actionsApiObj={[
            {
              type: 'cancel',
              url: 'Api/Maint/PreDataMgmt/CancelJob',
              params: 'JobId',
              cb: 'reloadData',
            },
            {
              type: 'restart',
              url: 'Api/Maint/PreDataMgmt/Restart',
              params: 'Id',
              cb: 'reloadData',
            },
          ]}
          pageSize={6}
          extra={
            <Select
              style={{ width: '150px' }}
              allowClear
              placeholder="请选择Action"
              options={totalActions}
              onChange={(value) => {
                setProgressExtraAction(value);
              }}
              fieldNames={{ value: 'Code', label: 'Name' }}
            ></Select>
          }
        />
      </Row>
      <Modal
        open={RecordDetailModalState.visible}
        title={RecordDetailModalState.record}
        width={800}
        onCancel={(e) => {
          RecordDetailModalStateDispatch({
            type: ModalAction.init,
          });
        }}
        okButtonProps={{ style: { display: 'none' } }}
        cancelButtonProps={{ style: { display: 'none' } }}
      >
        <UniTable
          id="progress_detail_table"
          rowKey="Id"
          columns={recordTotalColumns?.details ?? []}
          dataSource={RecordDetailModalState.dataSource}
          scroll={{ x: 'max-content' }}
          pagination={{ pageSize: 10 }}
        />
      </Modal>
    </>
  );
};

export default PreDataDataFlow;
