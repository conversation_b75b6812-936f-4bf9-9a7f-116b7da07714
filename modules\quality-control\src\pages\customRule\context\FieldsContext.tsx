import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { loadSubjects } from '@uni/combo-condition-parser';
import { fieldsProcessorByDirectories } from '../components/customizedJsonLogicModal/components/json-expr/processor';
import { CombineQueryFieldItem } from '../components/customizedJsonLogicModal/components/json-expr/interfaces';
import { ReqActionType } from '../constants';
import { Fields } from '@react-awesome-query-builder/core';

// 定义上下文数据类型
interface FieldsContextType {
  queryFields: Fields;
  keySort: any;
  loading: boolean;
  refresh: () => void;
}

// 创建上下文
const FieldsContext = createContext<FieldsContextType | undefined>(undefined);

// 创建Provider组件
export const FieldsProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [queryFields, setQueryFields] = useState<Fields>({});
  const [keySort, setKeySort] = useState<any>({});

  // 获取字段数据的接口
  const { loading, run: fetchFields } = useRequest(
    () => {
      return uniCommonService(`Api/${ReqActionType?.GetQueryableList}`, {
        method: 'POST',
        data: {
          moduleGroup: 'Dmr',
          tableName: 'OmniCard',
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<CombineQueryFieldItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          console.log('字段数据获取成功:', response?.data);

          // 在这里进行数据处理
          loadSubjects(response?.data);
          let fieldKeySort = fieldsProcessorByDirectories(response?.data);

          setQueryFields(fieldKeySort?.fieldTree);
          setKeySort(fieldKeySort?.keySort);
          return response.data;
        } else {
          console.error('字段数据获取失败:', response);
          setQueryFields({});
          setKeySort({});
          return [];
        }
      },
    },
  );

  // 组件挂载时自动获取数据
  useEffect(() => {
    fetchFields();
  }, []);

  // 提供上下文值
  const contextValue: FieldsContextType = {
    queryFields,
    keySort,
    loading,
    refresh: fetchFields,
  };

  return (
    <FieldsContext.Provider value={contextValue}>
      {children}
    </FieldsContext.Provider>
  );
};

// 创建自定义Hook便于使用上下文
export const useFields = (): FieldsContextType => {
  const context = useContext(FieldsContext);
  if (context === undefined) {
    throw new Error('useFields must be used within a FieldsProvider');
  }
  return context;
};
