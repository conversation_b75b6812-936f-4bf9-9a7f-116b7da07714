import { useRef } from 'react';
import {
  ProForm,
  ProFormInstance,
  ProFormText,
  ProFormSwitch,
  StepsForm,
  ProFormDependency,
  ProFormSelect,
  ProFormTextArea,
} from '@uni/components/src/pro-form';
import CommonFormItems from '../commonFormItems';
import LoadDataFormItems from '../formItems/loadData';
import PersistFormItems from '../formItems/pertsist';
import ValidateFormItems from '../formItems/validate';
import ArchiveFormItems from '../formItems/archive';
import RetrieveMetadataFormItems from '../formItems/retrieveMetadata';
import MenuFormItems from '../formItems/menu';
import { Divider } from 'antd';
import './index.less';
import ExportFormItems from '../formItems/export';
import ExportBriefFormItems from '../formItems/exportBreif';
import ImportFormItems from '../formItems/import';
import { checkIsJson } from '@/utils/widgets';

const FirstStep = ({
  editValueObj = undefined,
  dictData,
  hideItems,
  defaultValues,
  stepType,
  appcodeOpts,
}) => {
  // const formStep1Ref = useRef<ProFormInstance>();

  return (
    // <StepsForm.StepForm
    //   name="common"
    //   title={stepType ?? 'StatsReadOnly'}
    //   stepProps={{ description: '通用的规则设置部分' }}
    //   onFinish={async (values) => {
    //     console.log(values);
    //     await waitTime(2000);
    //     return true;
    //   }}
    // >
    <>
      <CommonFormItems
        editValueObj={editValueObj}
        dictData={dictData}
        hideItems={hideItems}
        stepType={stepType}
        defaultValues={defaultValues}
        appcodeOpts={appcodeOpts}
      />
      <ProForm.Group>
        {((defaultValues && defaultValues['EnableLoadData']) ||
          hideItems.indexOf('EnableLoadData') === -1) && (
          <LoadDataFormItems
            editValueObj={editValueObj}
            dictData={dictData}
            defaultValues={defaultValues}
            stepType={stepType}
          />
        )}
        {defaultValues && defaultValues['EnablePersist'] && (
          <PersistFormItems
            editValueObj={editValueObj}
            dictData={dictData}
            defaultValues={defaultValues}
          />
        )}
        {/* {((defaultValues && defaultValues['EnableLoadData']) ||
          hideItems.indexOf('EnableLoadData') === -1) && (
          <Divider className="first-step-divider" />
        )} */}
        {hideItems.indexOf('EnableValidate') === -1 && (
          <ValidateFormItems editValueObj={editValueObj} dictData={dictData} />
        )}
        {hideItems.indexOf('EnableRetrieveMetadata') === -1 && (
          <RetrieveMetadataFormItems
            editValueObj={editValueObj}
            dictData={dictData}
          />
        )}
        {/* {hideItems.indexOf('EnableRetrieveMetadata') === -1 && (
          <Divider className="first-step-divider" />
        )} */}
        {hideItems.indexOf('EnableArchive') === -1 && (
          <ArchiveFormItems editValueObj={editValueObj} dictData={dictData} />
        )}
        {hideItems.indexOf('EnableExport') === -1 && (
          <ExportFormItems editValueObj={editValueObj} />
        )}
        {hideItems.indexOf('EnableExportBrief') === -1 && (
          <ExportBriefFormItems editValueObj={editValueObj} />
        )}
        {hideItems.indexOf('EnableImport') === -1 && (
          <ImportFormItems editValueObj={editValueObj} />
        )}
        <Divider className="first-step-divider" />
      </ProForm.Group>
      <MenuFormItems editValueObj={editValueObj} dictData={dictData} />
      <Divider className="first-step-divider" />
      <ProFormTextArea
        name={['MasterArgs', 'ExtraConfig']}
        label="额外配置"
        // width="md"
        initialValue={editValueObj?.Master?.ExtraConfig || undefined}
        placeholder="请输入额外配置"
        colProps={{
          span: 10,
        }}
        fieldProps={{
          autoSize: {
            minRows: 4,
            maxRows: 8,
          },
        }}
        rules={[
          ({ getFieldValue }) => ({
            validator(_, value) {
              console.log(value);
              if (!value) {
                return Promise.resolve();
              }
              if (!checkIsJson(value)) {
                return Promise.reject(new Error('Json格式错误，请检查！'));
              }
              return Promise.resolve();
            },
          }),
        ]}
      />
      <ProFormTextArea
        name={['MasterArgs', 'Remark']}
        label="备注"
        // width="md"
        initialValue={editValueObj?.Master?.Remark || undefined}
        placeholder="请输入备注"
        fieldProps={{
          autoSize: {
            minRows: 4,
            maxRows: 8,
          },
        }}
        colProps={{
          offset: 1,
          span: 10,
        }}
      />
    </>
    // </StepsForm.StepForm>
  );
};

export default FirstStep;
