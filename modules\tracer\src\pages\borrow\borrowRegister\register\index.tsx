import React, {
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import { Dispatch, useDispatch, useSelector } from 'umi';
import PdfPrint from '@uni/components/src/pdf-print';
import { useModel } from '@@/plugin-model/useModel';
import { UniTable } from '@uni/components/src';
import {
  Alert,
  Button,
  Card,
  Col,
  Divider,
  InputRef,
  Modal,
  Popconfirm,
  Row,
  Space,
  Tooltip,
  message,
} from 'antd';
import { useSafeState, useKeyPress } from 'ahooks';
import {
  IModalState,
  IReducer,
  ITableState,
} from '@uni/reducers/src/Interface';
import { SwagBorrowRecordItem } from '../../interface';
import { columnsHandler, handleMrActionApi, isRespErr } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType } from '@/Constants';
import {
  ModalAction,
  TableAction,
  modalReducer,
  tableReducer,
  InitTableState,
  InitModalState,
} from '@uni/reducers/src';
import { BorrowFormItems } from '../formItems';
import { ProFormInstance } from '@ant-design/pro-components';
import { FileExcelOutlined, PrinterOutlined } from '@ant-design/icons';
import PatTimeline from '@/components/PatTimeline';
import { useTimelineReq } from '@/hooks';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { UseDispostionEnum, downloadFile } from '@uni/utils/src/download';
import './index.less';
import { ActionRecordItem } from '@/pages/mrRoom/interface';
import { BorrowColumns } from '../constants';
import clsx from 'clsx';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { QueryParameterConfigurationEditButton } from '@uni/components/src/query-configuration';
import { useReactToPrint } from 'react-to-print';
import BorrowPagePrint from '@/components/PrintBorrowPage';
import dayjs from 'dayjs';
import {
  argDefToFormItemTransformer,
  formItemRequiredTransformer,
  formValuesPostProcessor,
} from '@uni/components/src/query-configuration/processor';
import { isEmptyValues } from '@uni/utils/src/utils';

const BorrowRegister = (props) => {
  const {
    globalState: { dictData, userInfo },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);
  // form args
  const proFormRef = useRef<ProFormInstance>();

  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();
  const [revertRecord, setRevertRecord] = useSafeState(null);

  // 列表
  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagBorrowRecordItem & ActionRecordItem>, IReducer>
  >(tableReducer, InitTableState);

  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<SwagBorrowRecordItem[]>, IReducer>
  >(modalReducer, {
    ...InitModalState,
    specialData: undefined,
  });

  // modal selected table key
  const [selectedRecordKey, setSelectedRecordKey] = useSafeState([]);
  const [selectedRecordRows, setSelectedRecordRows] = useSafeState(undefined);
  // modal columns key
  const [modalColumns, setModalColumns] = useSafeState([]);
  // modal alert
  const [modalAlert, setModalAlert] = useSafeState(false);

  const barCodeRef = useRef<InputRef>(null);
  // 节流隐式标识
  const hiddenLoading = useRef(false);
  // 设计思路是先判断是不是相同的 不是就没事 是就进debounce判断
  const newestBarCode = useRef(undefined);

  const [lendSearchOpts, setLendSearchOpts] = useSafeState([]);

  const defaultSearchOpts = BorrowFormItems(
    dictData?.Mr?.Employee ?? dictData?.Employee ?? [],
    dictData?.Mr?.BorrowerOrg ?? dictData?.BorrowerOrg ?? [],
    dictData?.Mr?.BorrowPurpose ?? dictData?.BorrowPurpose ?? [],
    barCodeRef,
  );

  // 查询结果处理
  const searchResultHandler = (params, res, needDataPush) => {
    if (!isRespErr(res)) {
      let resData;
      if (res?.data?.Items) {
        // Api/Mr/TraceRecord/GetList
        resData = res?.data?.Items?.slice();
      } else {
        // Api/Mr/TraceRecord/GetListByBarCode
        resData = res?.data?.slice();
      }
      if (!needDataPush) {
        if (resData?.length === 1) {
          // 单个，直接处理
          reqActionReq(
            { ...params, BarCode: resData?.at(0)?.BarCode },
            null,
            ReqActionType.lend,
          );
        } else if (resData?.length > 1) {
          // 多条，modal提示处理
          ModalStateDispatch({
            type: ModalAction.change,
            payload: {
              visible: true,
              record: resData,
              specialData: res?.data?.Items ? params : params?.AutoBarCode,
              actionType: undefined,
            },
          });
        } else {
          // 没查到数据
          // 重置节流标识
          hiddenLoading.current = false;

          Modal.confirm({
            title: `查无数据`,
            content: '请确认病案标识填写正确',
            onOk: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            onCancel: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            cancelButtonProps: { style: { display: 'none' } },
          });
          // message.error('没有查询到有效数据，请确认参数是否正确');
        }
      } else {
        // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
        // 查询成功后，把数据插入已记录列表
        SearchTableDispatch({
          type: TableAction.dataUnshift,
          payload: {
            data: Array.isArray(resData) ? resData : [resData],
            key: 'BarCode',
          },
        });
        // 重置节流标识
        hiddenLoading.current = false;
        // focus input
        focusBarCode();
      }
    } else {
      // 重置节流标识
      hiddenLoading.current = false;
    }
  };
  //批量查询，查询→操作
  const searchOneReq = async (data: any, needDataPush = false) => {
    if (!data) return;

    // 直接使用传入的参数对象，添加分页参数
    const requestData = {
      ...data,
      SkipCount: 0,
      MaxResultCount: 999999,
    };

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetList`,
          method: 'POST',
          data: requestData,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    searchResultHandler(data, res, needDataPush);
  };

  // 特殊debounce 扫码枪才需要
  const handleBarCodeDebounce = (barCode) => {
    if (SearchTable?.data?.findIndex((d) => d?.BarCode === barCode) > -1) {
      // 判断时间 存在与表内 长于 特定时间，比如 2s
      if (
        dayjs().diff(
          SearchTable?.data?.find((d) => d?.BarCode === barCode)?.InsertTime,
        ) < 2000
      ) {
        // 拦截
        hiddenLoading.current = false;
        proFormRef?.current.setFieldValue('BarCode', '');
        return true;
      }
    }

    newestBarCode.current = barCode;
    return false;
  };

  // 扫码枪条码，走这里
  const searchByBarCodeReq = async (params: any, needDataPush = false) => {
    if (!params) return;
    // 扫之前，先做判断 判断条码号与上一次扫的 & 表格内的 是否相符 如果能匹配出来则特殊报错
    if (handleBarCodeDebounce(params?.BarCode)) {
      // 拦截
      return;
    }
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetListByBarCode`,
          method: 'POST',
          data: {
            BarCode: params?.BarCode,
            SkipCount: 0,
            MaxResultCount: 999999,
          },
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    searchResultHandler(params, res, needDataPush);
  };

  // 现在变成了操作记录数据的table用
  // 扫码枪输入的是一个barCode，然后点签收 这一个操作, 当操作成功时，在调Get接口单独获取这个病案信息，再在右边显示出来
  // 正常输入则是用于查询，查询的数据
  // **现在的逻辑顺序：
  /**
   * 第一先拿BarCode去查询Get，获取一个list（searchOneReq）
   * 如果只有一条那么就正常继续走借阅登记（reqActionReq）
   * 最后将那条数据插入table内
   */
  const reqActionReq = async (data: any, item, reqType: ReqActionType) => {
    if (!data || !reqType) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `Borrowing/${reqType}`,
        requestParams: {
          url: `Api/Mr/Borrowing/${reqType}`,
          method: 'POST',
          data: data,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    // 不管结果 重置节流标识
    hiddenLoading.current = false;

    if (!isRespErr(res)) {
      if (reqType === ReqActionType.lend) {
        // 把modal关闭
        ModalStateDispatch({
          type: ModalAction.init,
        });
        setSelectedRecordKey([]);
        setSelectedRecordRows(undefined);
        setModalAlert(false);

        // StatusCode 黄神的单独处理
        let result = handleMrActionApi(res.data);
        result?.isCorrect
          ? message.success('借阅成功')
          : message.error(result?.errMsg?.join('。/n'));

        if (result?.data?.length > 0) {
          // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
          // 把数据插入已记录列表
          SearchTableDispatch({
            type: TableAction.dataUnshiftUniq,
            payload: {
              data: {
                ...(Array.isArray(result?.data)
                  ? result?.data?.at(0)
                  : result?.data),
                InsertTime: dayjs(),
              },
              key: 'BarCode',
              overWriteBy: {
                key: 'isCorrect',
                value: true,
              },
            },
          });
        }

        if (result?.errType === '404') {
          // 404
          Modal.error({
            title: result?.errMsg?.join('。/n'),
            onOk: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            onCancel: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
          });
        } else {
          // barCode自动清除：成功才清除，如果失败则提示并且不清除？
          proFormRef.current.resetFields(['BarCode']);
          // focus input
          focusBarCode();
        }

        // 然后调用searchOneReq查询该病案详情?
        // searchOneReq(data.BarCodes?.at(0), true);
      } else if (reqType === ReqActionType.return) {
        // revert
        SearchTableDispatch({
          type: TableAction.dataFilt,
          payload: {
            key: 'BarCode',
            value: item.BarCode,
          },
        });
        setRevertRecord(item);
        message.success('撤销成功');
      }
    }
  };

  // 处理 借阅导出（院方需要特殊格式，而且数据需要保证最新）
  const exportBackendReq = async (params) => {
    if (!params) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: 'ExportTraceRecord',
        requestParams: {
          url: `Api/Mr/BorrowRecord/${ReqActionType.borrowRecordPrint}`,
          method: 'POST',
          data: params,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      let exportName = `病案借阅记录`;
      downloadFile(exportName, res?.response, UseDispostionEnum.nouse);
    }
  };

  // 处理撤销后的timeLine 重置
  useEffect(() => {
    // 时间轴如果匹配则值空
    if (SearchTable?.clkItem?.BarCode === revertRecord?.BarCode) {
      SearchTableDispatch({
        type: TableAction.clkChange,
        payload: {
          clkItem: null,
        },
      });
      setParams(null);
    }
  }, [revertRecord]);

  // columns 处理，主要用于处理options
  const SearchedTableColumnsSolver = useMemo(() => {
    return SearchTable.columns ? columnsHandler(SearchTable.columns) : [];
  }, [SearchTable.columns]);

  // columns处理
  if (
    columnsList?.['BorrowRecord/GetLendList'] &&
    SearchTable.columns.length < 1
  ) {
    SearchTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(
          BorrowColumns,
          columnsList['BorrowRecord/GetLendList'],
        ),
      },
    });
    setModalColumns(
      tableColumnBaseProcessor([], columnsList['BorrowRecord/GetLendList']),
    );
  }

  // 点击操作
  const fetchReqActionReq = () => {
    if (hiddenLoading.current) return;
    hiddenLoading.current = true;
    console.log(proFormRef.current.getFieldsValue());
    proFormRef.current
      .validateFields()
      .then((values) => {
        console.log(values);

        values = formValuesPostProcessor(lendSearchOpts, values);

        props.setBorrower(values?.Borrower);
        if (values.SignType.value === 'BarCode') {
          searchByBarCodeReq(values, false);
        } else {
          // 对于非BarCode类型，创建请求参数对象
          const searchParam = {
            ..._.omit(values, 'BarCode'),
            [values.SignType.value]: values.BarCode,
          };
          searchOneReq(searchParam, false);
        }
      })
      .catch((err) => {
        hiddenLoading.current = false;

        message.error('请确认已填写所有借阅人信息！');
      });
  };

  useKeyPress(
    'enter',
    () => {
      console.log('press enter only');
      if (proFormRef.current.getFieldValue('BarCode')) {
        proFormRef.current.validateFields().then((values) => {
          fetchReqActionReq();
        });
      }
    },
    {
      exactMatch: true,
      target: document.getElementById('borrowRegisterForm'),
    },
  );

  const focusBarCode = () => {
    // 定位
    setTimeout(() => {
      barCodeRef.current?.focus({ cursor: 'end' });
    }, 20);
  };

  // 打印 part
  // print component
  const componentRef = useRef<any>();
  // store the resolve Promise being used in `onBeforeGetContent`
  const promiseResolveRef = useRef(null);
  const [isPrinting, setIsPrinting] = useState(false);

  // We watch for the state to change here, and for the Promise resolve to be available
  useEffect(() => {
    if (isPrinting && promiseResolveRef.current) {
      // Resolves the Promise, letting `react-to-print` know that the DOM updates are completed
      promiseResolveRef.current();
    }
  }, [isPrinting]);

  const handlePrint = useReactToPrint({
    bodyClass: 'print-body',
    pageStyle: '#print_example { display: block !important; }',
    content: () => componentRef.current,
    onBeforeGetContent: () => {
      return new Promise((resolve) => {
        // 打印前的额外操作
        promiseResolveRef.current = resolve;
        setIsPrinting(true);
      });
    },
    onAfterPrint: () => {
      // 打印后的复原操作
      // Reset the Promise resolve so we can print again
      promiseResolveRef.current = null;
      setIsPrinting(false);
    },
  });

  useEffect(() => {
    console.log('lendSearchOpts', lendSearchOpts);
  }, [lendSearchOpts]);

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col xxl={7} xl={8}>
          <Card
            title={<Space>借阅信息</Space>}
            style={{ marginBottom: '15px' }}
            extra={
              <QueryParameterConfigurationEditButton
                type={'LEFT_CONTAINER'}
                currentSearchOptsLength={lendSearchOpts?.length ?? 0}
                setSearchOpts={setLendSearchOpts}
                formItems={
                  isEmptyValues(lendSearchOpts)
                    ? defaultSearchOpts
                    : lendSearchOpts
                }
                dictData={dictData}
                queryInterfaceUrl={'Api/Mr/Borrowing/Lend'}
                onFormItemsSaveSuccess={(argDefs) => {
                  let transformed = argDefToFormItemTransformer(
                    argDefs,
                    dictData,
                  )
                    .concat(
                      (defaultSearchOpts ?? [])?.filter(
                        (item) => item?.custom === true,
                      ) as any[],
                    )
                    .sort(
                      (a: any, b: any) =>
                        (a?.ColumnSort ?? 999) - (b?.ColumnSort ?? 999),
                    );

                  setLendSearchOpts(
                    transformed?.filter((item) => item?.visible === true),
                  );
                }}
              />
            }
          >
            <ProFormContainer
              spinLoading={
                loadings['TraceRecord/GetList'] ||
                loadings[`Borrowing/${ReqActionType.lend}`] ||
                false
              }
              autoFocusFirstInput={false}
              className="borrow_register_form"
              id="borrowRegisterForm"
              formRef={proFormRef}
              grid
              labelCol={{ flex: '100px' }}
              wrapperCol={{ flex: 'auto' }}
              searchOpts={lendSearchOpts}
              submitter={{
                render: (props, doms) => {
                  return [
                    <Button
                      type="primary"
                      style={{
                        width: 'calc(100% - 100px)',
                        float: 'right',
                        marginTop: '8px',
                      }}
                      key="submit"
                      onClick={() => {
                        fetchReqActionReq();
                      }}
                    >
                      借阅(Enter)
                    </Button>,
                  ];
                },
              }}
            />
          </Card>
          <PatTimeline
            item={SearchTable?.clkItem}
            loading={loadings['TraceRecord/GetActions']}
            timelineItems={timelineItems}
          />
        </Col>
        <Col xxl={17} xl={16}>
          <Card
            title="病案借阅列表"
            extra={
              <Space>
                <PdfPrint
                  apiUrl="Api/Mr/BorrowRecord/BorrowRecordPrintPdf"
                  tooltipTitle="打印借阅记录"
                  buttonType="default"
                  buttonSize="middle"
                  paramType="data"
                  params={() => {
                    const borrower =
                      proFormRef.current.getFieldValue('Borrower');
                    if (!borrower) {
                      message.error('请先填写借阅人');
                      return false; // 阻止请求发送
                    }

                    const borrowerName = (
                      dictData?.Mr?.Employee || dictData?.Employee
                    )?.find((d) => d.Code === borrower)?.Name;

                    if (!borrowerName) {
                      message.error('未找到借阅人信息');
                      return false;
                    }

                    const filteredData = SearchTable.data?.filter(
                      (d) => d.isCorrect,
                    );

                    if (!filteredData || filteredData.length < 1) {
                      message.error('没有可打印的借阅记录');
                      return false;
                    }

                    return {
                      BorrowerName: borrowerName,
                      BorrowRecords: filteredData.map((d, i) => ({
                        ...d,
                        Sort: i + 1,
                        Purpose: (
                          dictData?.Mr?.BorrowPurpose ?? dictData?.BorrowPurpose
                        )?.find(
                          (d) =>
                            d?.Code ===
                            proFormRef.current.getFieldValue('Purpose'),
                        )?.Name,
                      })),
                    };
                  }}
                >
                  打印
                </PdfPrint>
                <Divider type="vertical" />
                <Popconfirm
                  title="导出时会将错误的记录过滤掉"
                  onConfirm={(e) => {
                    let borrower = proFormRef.current.getFieldValue('Borrower');
                    console.log(dictData?.Mr?.Employee);
                    if (
                      borrower &&
                      (dictData?.Mr?.Employee || dictData?.Employee)?.find(
                        (d) => d.Code === borrower,
                      )?.Name
                    ) {
                      exportBackendReq({
                        BorrowerName: (
                          dictData?.Mr?.Employee || dictData?.Employee
                        )?.find((d) => d.Code === borrower)?.Name,
                        BorrowRecords: SearchTable.data
                          ?.filter((d) => d.isCorrect)
                          ?.map((d, i) => ({
                            ...d,
                            Sort: i + 1,
                            Purpose: (
                              dictData?.Mr?.BorrowPurpose ??
                              dictData?.BorrowPurpose
                            )?.find(
                              (d) =>
                                d?.Code ===
                                proFormRef.current.getFieldValue('Purpose'),
                            )?.Name,
                          })),
                      });
                    } else {
                      message.error('请先填写借阅人');
                    }
                  }}
                  disabled={
                    SearchTable.data?.filter((d) => d.isCorrect)?.length < 1
                  }
                >
                  <Tooltip title="导出Docx">
                    <Button
                      type="text"
                      shape="circle"
                      disabled={
                        SearchTable.data?.filter((d) => d.isCorrect)?.length < 1
                      }
                      // @ts-ignore - 忽略TypeScript错误，项目中其他组件也是这样使用图标的
                      icon={<FileExcelOutlined />}
                    />
                  </Tooltip>
                </Popconfirm>
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl: 'Api/Mr/BorrowRecord/GetLendList',
                    onTableRowSaveSuccess: (columns) => {
                      // 这个columns 存到dva
                      dispatch({
                        type: 'global/saveColumns',
                        payload: {
                          name: 'BorrowRecord/GetLendList',
                          value: columns,
                        },
                      });
                      SearchTableDispatch({
                        type: TableAction.columnsChange,
                        payload: {
                          columns: tableColumnBaseProcessor(
                            BorrowColumns,
                            columns,
                          ),
                        },
                      });
                    },
                  }}
                />
              </Space>
            }
          >
            <UniTable
              id="borrow_record"
              rowKey="uuid"
              showSorterTooltip={false}
              loading={
                loadings['BorrowRecord/GetLendList'] ||
                loadings['TraceRecord/GetList'] ||
                loadings[`Borrowing/${ReqActionType.lend}`] ||
                false
              }
              columns={SearchedTableColumnsSolver} // columnsHandler
              dataSource={SearchTable.data}
              dictionaryData={dictData}
              scroll={{ x: 'max-content' }}
              rowClassName={(record, index) => {
                let classname = [];
                // 互斥
                if (!record?.isCorrect) {
                  classname.push('row-error');
                } else if (index === 0) {
                  return 'row-first';
                }
                if (record?.uuid === SearchTable.clkItem?.uuid) {
                  classname.push('row-selected');
                }

                return classname.length > 0 ? clsx(classname) : null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (SearchTable.clkItem?.BarCode !== record?.BarCode) {
                      SearchTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
            />
          </Card>
        </Col>
      </Row>

      <Modal
        title="确认病案"
        open={ModalState.visible}
        width={900}
        onOk={(e) => {
          console.log(
            ModalState.record,
            selectedRecordKey,
            ModalState.record.findIndex(
              (d) => d.BarCode === selectedRecordKey?.at(0),
            ),
          );
          if (
            ModalState.record.findIndex(
              (d) => d.BarCode === selectedRecordKey?.at(0),
            ) > -1
          ) {
            reqActionReq(
              {
                ...ModalState.specialData,
                BarCode: selectedRecordKey?.at(0),
              },
              selectedRecordRows,
              ReqActionType.lend,
            );
          } else {
            // 没选
            setModalAlert(true);
          }
        }}
        okButtonProps={{
          loading: loadings[`Tracing/${ReqActionType.lend}`],
        }}
        onCancel={(e) => {
          // 重置节流标识
          hiddenLoading.current = false;
          focusBarCode();

          ModalStateDispatch({
            type: ModalAction.init,
          });
          setSelectedRecordKey([]);
          setSelectedRecordRows(undefined);
          setModalAlert(false);
        }}
      >
        <UniTable
          id="multi_record_check"
          rowKey="BarCode"
          showSorterTooltip={false}
          loading={
            loadings['BorrowRecord/GetLendList'] ||
            loadings['TraceRecord/GetList'] ||
            loadings[`Tracing/${ReqActionType.lend}`] ||
            false
          }
          columns={modalColumns} // columnsHandler
          dataSource={ModalState.record}
          scroll={{ x: 'max-content' }}
          tableAlertRender={() => {
            return modalAlert ? (
              <Alert
                message="请选择一个病案"
                description="请选择一个病案再点击确认，如果没有查询到目标病案请确认信息输入正确"
                type="error"
                closable
                onClose={() => {
                  setModalAlert(false);
                }}
              />
            ) : (
              false
            );
          }}
          tableAlertOptionRender={false}
          rowSelection={{
            alwaysShowAlert: true,
            type: 'radio',
            selectedRowKeys: selectedRecordKey,
            onChange: (
              selectedRowKeys: React.Key[],
              selectedRows: SwagBorrowRecordItem[],
            ) => {
              setSelectedRecordKey(selectedRowKeys);
              setSelectedRecordRows(selectedRows?.at(0));
              setModalAlert(false);
            },
          }}
          onRow={(record) => {
            return {
              onClick: (event) => {
                setSelectedRecordKey([record?.BarCode]);
                setSelectedRecordRows(record);
              },
            };
          }}
        />
      </Modal>

      <BorrowPagePrint
        userInfo={{
          Operator: userInfo?.Name,
          Borrower: (dictData?.Mr?.Employee || dictData?.Employee)?.find(
            (d) => d.Code === proFormRef.current?.getFieldValue('Borrower'),
          )?.Name,
        }}
        tableData={SearchTable.data
          ?.filter((d) => d.isCorrect)
          ?.map((d, i) => ({
            ...d,
            index: i + 1,
            Purpose: (
              dictData?.Mr?.BorrowPurpose ?? dictData?.BorrowPurpose
            )?.find(
              (d) => d?.Code === proFormRef.current.getFieldValue('Purpose'),
            )?.Name,
          }))}
        ref={componentRef}
      />
    </>
  );
};

export default BorrowRegister;
