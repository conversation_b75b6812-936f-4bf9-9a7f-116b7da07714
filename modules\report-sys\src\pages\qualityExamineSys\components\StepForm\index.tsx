import {
  ProFormInstance,
  StepsForm,
  ProForm,
} from '@uni/components/src/pro-form';
import { Modal, Button, notification, Space, Select, message, Row } from 'antd';
import _ from 'lodash';
import { useEffect, useRef, useState } from 'react';
import FirstStep from './firstStep';
// import SecondStep from '../secondStep';
import { isRespErr, RespType } from '@/utils/widgets';
// import ThirdStep from '../thirdStep';
// import FourthStep from '../fourthStep';
import { useEventEmitter } from 'ahooks';
import { IQuanlityExamineSysItemDetail } from '../../interface';
import { Emitter } from '@uni/utils/src/emitter';
import { QualityExamineEventConstants } from '../../constants';
import QualityExamineSecondStep from './secondStep';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';

interface IQualityModalFormProps {
  visible: boolean;
  recordDetail?: IQuanlityExamineSysItemDetail;
  dictData: any;
  templateData: any[];
  appCodeOpts: any[];
}

const QualityModalForm = ({
  visible,
  recordDetail,
  dictData,
  templateData,
  appCodeOpts,
}: IQualityModalFormProps) => {
  // 使用 ProForm.useForm 直接创建表单实例
  const formRef = useRef<ProFormInstance>(null);
  // const [firstForm] = ProForm.useForm();
  const [stepCurrent, setStepCurrent] = useState(0);
  const [masterId, setMasterId] = useState(undefined);
  const [revieweeType, setRevieweeType] = useState(undefined);
  const [isCreate, setIsCreate] = useState(false);

  // first step save
  const { loading: upsertQualityMasterLoading, run: upsertQualityMasterReq } =
    useRequest(
      (data) =>
        uniCommonService('Api/Sys/QualityExamineSys/UpsertQualityExamineSys', {
          method: 'POST',
          data,
        }),
      {
        manual: true,
        formatResult: (res: RespType<any>) => {
          if (!isRespErr(res)) {
            setStepCurrent(1);
            setMasterId(res?.data);
            return res?.data;
          } else {
            return null;
          }
        },
      },
    );
  // 新增/编辑接口处理
  //   const upsertMasterData = async (data) => {
  //     console.log(data);
  //     return await actionReq(data, ReqActionType.UpsertReport);
  //   };
  useEffect(() => {
    if (recordDetail) {
      setMasterId(recordDetail?.Master?.MasterId);
      setRevieweeType(recordDetail?.Master?.RevieweeType);
      setIsCreate(false);
    } else {
      setIsCreate(true);
    }
  }, [recordDetail]);

  useEffect(() => {
    Emitter.on(QualityExamineEventConstants.TEMPLATEID_CHANGE, (record) => {
      formRef?.current?.setFieldValue(
        ['MasterArgs', 'TemplateIds'],
        [record?.TemplateId],
      );
    });

    return () => {
      Emitter.off(QualityExamineEventConstants.TEMPLATEID_CHANGE);
    };
  }, []);

  // 调试日志
  console.log('firstForm', formRef);

  return (
    <StepsForm
      current={stepCurrent}
      onCurrentChange={(cur) => {
        setStepCurrent(cur);
      }}
      stepsFormRender={(dom, submitter) => {
        return (
          <Modal
            title={
              <Space size={15}>
                <span>
                  {_.isEmpty(recordDetail) ? '新建' : '编辑'}：
                  {recordDetail?.Master?.Title}
                </span>
              </Space>
            }
            width={1200}
            onCancel={() => {
              setStepCurrent(0);
              Emitter.emit(QualityExamineEventConstants.MODAL_FORM_CLOES);
            }}
            open={visible}
            footer={submitter}
            destroyOnClose={true}
            maskClosable={false}
            keyboard={false}
          >
            {dom}
          </Modal>
        );
      }}
      onFinish={async (values) => {
        setStepCurrent(0);
        Emitter.emit(QualityExamineEventConstants.MODAL_FORM_CLOES);
      }}
      submitter={{
        render: (props, dom) => {
          if (props.step === 0) {
            return (
              <Space>
                <Button
                  onClick={(e) => {
                    if (recordDetail?.Master?.MasterId) {
                      setStepCurrent(1);
                    } else {
                      message.error('请先完成主记录与行为配置');
                    }
                  }}
                >
                  跳过
                </Button>
                <Button
                  type="primary"
                  loading={upsertQualityMasterLoading}
                  onClick={() => props.onSubmit?.()}
                >
                  保存并下一步 {'>'}
                </Button>
              </Space>
            );
          }
          return dom;
        },
      }}
    >
      <StepsForm.StepForm
        name="first"
        stepProps={{
          title: '主记录与行为配置',
          onClick: (e) => {
            setStepCurrent(0);
          },
        }}
        grid={true}
        formRef={formRef}
        preserve={false}
        rowProps={{
          gutter: [8, 8],
        }}
        onFinish={async (values) => {
          console.log('onFinish', values);
          setRevieweeType(values?.MasterArgs?.RevieweeType);
          if (recordDetail) {
            upsertQualityMasterReq({
              MasterArgs: {
                ...recordDetail.Master,
                ...values?.MasterArgs,
              },
              ScheduleArgs: {
                ...recordDetail.ScheduleSetting,
                ...values.ScheduleArgs,
              },
              SampleArgs: {
                ...recordDetail.SampleSetting,
                ...values.SampleArgs,
              },
              ReviewArgs: {
                ...recordDetail.ReviewSetting,
                ...values.ReviewArgs,
              },
            });
          } else {
            upsertQualityMasterReq(values);
          }
          // const res = await api.qualityExamineSys.addMaster(values);
          // if (isRespErr(res)) {
          //   return;
          // }
          // setMasterId(res.data);
          // setStepCurrent(1);
        }}
      >
        <FirstStep
          recordDetail={recordDetail}
          dictData={dictData}
          appcodeOpts={appCodeOpts}
          templateData={templateData?.filter((temp) => temp.IsValid)}
          isCreate={isCreate}
          formRef={formRef}
        />
      </StepsForm.StepForm>
      <StepsForm.StepForm
        name="second"
        stepProps={{
          title: '审核人与被审核人配置',
          onClick: (e) => {
            if (recordDetail?.Master?.MasterId) {
              setStepCurrent(1);
            } else {
              message.error('请先完成主记录与行为配置');
            }
          },
        }}
      >
        <QualityExamineSecondStep
          masterId={masterId}
          revieweeType={revieweeType}
          dictData={dictData}
        />
      </StepsForm.StepForm>
    </StepsForm>
  );
};

export default QualityModalForm;
