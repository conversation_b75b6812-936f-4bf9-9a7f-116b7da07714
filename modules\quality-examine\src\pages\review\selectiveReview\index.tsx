import React, { useRef, useState } from 'react';
import './index.less';
import ReviewTable from '@/pages/review/components/review-table';
import { Button, Card, Col, Input, Popover, Row } from 'antd';
import dayjs from 'dayjs';
import { dmrAuditeeColumns } from '@/pages/review/columns';
import ScoreCommentDrawerContainer from '@/pages/review/components/score-comment';
import { useModel } from 'umi';
import { BatchItem, TaskItem } from '@/pages/review/interface';
import { useRouteProps } from '@uni/commons/src/route-context';
import Datepicker from '@uni/components/src/picker/datepicker';
import locale from 'antd/es/date-picker/locale/zh_CN';
import { UniSelect } from '@uni/components/src';
import { noDisablePermissionAsAdminCoderManagementExtra } from '@/pages/review/utils';
import { cloneDeep } from 'lodash';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  CarryOutOutlined,
  ExportOutlined,
  FileTextOutlined,
  MonitorOutlined,
  UndoOutlined,
} from '@ant-design/icons';
import IconBtn from '@uni/components/src/iconBtn';
import { QualityExamineStatus } from '@/pages/review/components/score-comment/score/constant';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
const DmrReviewSelective = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const taskTableRef = useRef(null);

  const dmrPreviewContainerRef = useRef(null);

  const [batchArgs, setBatchArgs] = useState<any>({});

  const { examineMasterId } = useRouteProps();

  const overrideOperationColumns = [
    {
      dataIndex: 'operation',
      visible: true,
      width: 60,
      align: 'center',
      title: '操作',
      render: (node, record, index) => {
        return (
          <div
            className={'flex-row-center'}
            style={{ justifyContent: 'center' }}
          >
            <IconBtn
              title="审核"
              type={'selective-icon'}
              customIcon={<MonitorOutlined />}
              className="operation-btn"
              style={{
                height: 28,
                alignItems: 'center',
                display: 'flex',
                flex: 1,
                justifyContent: 'center',
              }}
              onClick={async (e) => {
                // 先行start selective review
                await startSelectiveReview(record?.MasterId, record?.HisId);
                // 更新当前TaskId
                let newTaskItem =
                  await taskTableRef?.current?.updateCurrentTaskId(
                    record?.TaskId,
                  );
                dmrPreviewContainerRef?.current?.showDrawer({
                  taskItem: newTaskItem,
                  status: true,
                });
              }}
            />
          </div>
        );
      },
    },
  ];

  const { run: startSelectiveReview } = useRequest(
    (masterId: number, hisId: string) => {
      return uniCommonService(
        'Api/Dmr/DmrCardQualityExamine/StartSelectiveReview',
        {
          method: 'POST',
          data: {
            MasterId: masterId,
            HisId: hisId,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: any) => {
        return response;
      },
    },
  );

  return (
    <div id={'selective-container'} className={'selective-container'}>
      <div className={'batch-range-container'}>
        <Row gutter={[0, 16]} style={{ width: '100%' }}>
          <Col span={18}>
            <Row className={'flex-row-center'} gutter={[16, 16]}>
              <Col span={8}>
                <div className={'flex-row-center'}>
                  <span className={'batch-label'}>病案标识：</span>
                  <Input
                    placeholder={'请输入病案标识'}
                    value={batchArgs?.Keyword}
                    onChange={(event) => {
                      setBatchArgs({
                        ...batchArgs,
                        Keyword: event?.target?.value,
                      });
                    }}
                  />
                </div>
              </Col>
              <Col span={8}>
                <div className={'flex-row-center'}>
                  <span className={'batch-label'}>时间：</span>
                  <Datepicker.RangePicker
                    style={{ flex: 1 }}
                    locale={locale}
                    picker={'date'}
                    allowClear={false}
                    value={[
                      batchArgs?.['Sdate']
                        ? dayjs(batchArgs?.['Sdate'])
                        : undefined,
                      batchArgs?.['Edate']
                        ? dayjs(batchArgs?.['Edate'])
                        : undefined,
                    ]}
                    showTime={false}
                    format={'YYYY-MM-DD'}
                    onChange={(dates, dateStrings) => {
                      setBatchArgs({
                        ...batchArgs,
                        Sdate: dateStrings?.at(0),
                        Edate: dateStrings?.at(1),
                      });
                    }}
                  />
                </div>
              </Col>
            </Row>
          </Col>

          <Col span={6} style={{ justifyContent: 'flex-end', display: 'flex' }}>
            <Button
              className={'search-btn'}
              type={'primary'}
              onClick={() => {
                taskTableRef?.current?.freshQueryTable();
              }}
            >
              查询
            </Button>
          </Col>
        </Row>
      </div>

      <Card style={{ marginTop: 20 }} title={'二次评审'}>
        <ReviewTable
          id={'dmr-selective-progress-table'}
          className={'selective-progress-table'}
          fastSelectShow={false}
          noBatchIdInData={true}
          masterId={examineMasterId}
          noMasterIdInData={isEmptyValues(examineMasterId)}
          taskTableRef={taskTableRef}
          dmrPreviewContainerRef={dmrPreviewContainerRef}
          scroll={{
            x: 'max-content',
            y: 620,
          }}
          overrideBasicColumns={overrideOperationColumns}
          taskExtraParams={() => {
            return {
              Sdate: batchArgs?.['Sdate']
                ? dayjs(batchArgs?.['Sdate'])?.format('YYYY-MM-DD')
                : undefined,
              Edate: batchArgs?.['Edate']
                ? dayjs(batchArgs?.['Edate'])?.format('YYYY-MM-DD')
                : undefined,
              Keyword: batchArgs?.Keyword,
            };
          }}
        />
      </Card>

      <ScoreCommentDrawerContainer
        tableReadonly={false}
        dmrReadonly={false}
        drawerContainerRef={dmrPreviewContainerRef}
        onScoreCommentReviewEnd={(taskId: number) => {
          // taskTableRef?.current?.freshQueryTable();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        onScoreCommentClose={(taskId: number) => {
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        onScoreCommentTableRefresh={(taskId: number) => {
          // taskTableRef?.current?.queryTasksCurrent();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        getContainer={() => {
          return document.getElementById('selective-container');
        }}
      />
    </div>
  );
};

export default DmrReviewSelective;
