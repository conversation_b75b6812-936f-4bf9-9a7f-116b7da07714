import { ExportIconBtn, UniTable } from '@uni/components/src';
import React, {
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  CombineQueryDetail,
  DetailColumnItem,
} from '@/pages/combine-query/interfaces';
import { useModel, useRequest } from 'umi';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import { RespVO, TableResp } from '@uni/commons/src/interfaces';
import { Emitter } from '@uni/utils/src/emitter';
import { Button, Card, Divider, message, Space, TableProps, Tag } from 'antd';
import { StatsAnalysisEventConstant } from '@/constants';
import {
  activeColumnsWithIndexProcessor,
  columnDataCustomTitleStateProcessor,
  columnSettingColumnsProcessor,
  defaultCheckedColumnStateProcessor,
  selectedTableColumnsProcessor,
  tableToEchartsTableColumnsProcessor,
} from '@/pages/combine-query/processor';
import ColumnSetting, { Container } from '@uni/components/src/column-setting';
import { downloadFile } from '@uni/utils/src/download';
import { isEmptyValues } from '@uni/utils/src/utils';
import ReactDragColumnView from '@/pages/combine-query/components/drag-listview/ReactDragColumnView';
import { cloneDeep } from 'lodash';
import './index.less';
import { columns } from '@/pages/combine-query/containers/table/columns';
import CombineQueryDetailColumnSettings from '@/pages/combine-query/containers/table/detail-columns';
// import CombineQueryDetailColumnSettings from '@/pages/combine-query/containers/table/columns-setting';
import ColumnTemplateTitleAdd from '@/pages/combine-query/containers/column-save';
import { useDeepCompareEffect } from 'ahooks';
import { EmptyWrapper } from '@/pages/combine-query';
import { CombineQueryContext } from '@/pages/combine-query/combo-table-ng';
import { dateRangeValueProcessor } from '@uni/components/src/date-range-with-type';
import UniTableNG from '@uni/components/src/tanstack-table';
import md5 from 'crypto-js/md5';
import isEqual from 'lodash/isEqual';
import { v4 as uuidv4 } from 'uuid';
import { onDetailTableColumnChange } from '@/pages/combine-query/plugins/column-change';
import ComboDetailSummary from '@/pages/combine-query/containers/table/summary';
import { autoSumAndAvg } from '@/pages/combine-query/plugins/sum-avg';

const detailPageSize =
  (window as any).externalConfig?.['statsAnalysis']?.detailPageSize ?? 1000;

const infiniteScroll =
  (window as any).externalConfig?.['statsAnalysis']?.infiniteScroll ?? false;

const expressionEmptySearch = true;

interface CombineQueryTableProps {
  nextGeneration?: boolean;
  tableRef?: any;

  tableName: string;

  needCardContainer?: boolean;

  tableHeight?: number;

  templateSelectorContainerRef?: any;

  tabContainer?: any;
  metricContainerRef?: any;

  metricDetailExtraContentContainerRef?: any;
}

const pageSize = detailPageSize;

const CombineQueryTable = (props: CombineQueryTableProps) => {
  const fetchNextPageFlagRef = useRef(false);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const columnSizingContentRef = React.useRef(null);

  const combineQueryContext = useContext(CombineQueryContext);

  const [detailColumns, setDetailColumns] = useState<DetailColumnItem[]>([]);

  const [defaultDetailColumns, setDefaultDetailColumns] = useState<
    DetailColumnItem[]
  >([]);

  const [detailTableDataSource, setDetailTableDataSource] = useState([]);

  const [columnsState, setColumnState] = useState<any>({});

  const [defaultColumnsState, setDefaultColumnState] = useState<any>({});

  // comboSignificant 默认列
  const [comboSignificantColumnsState, setComboSignificantColumnsState] =
    useState<any>({});

  const [query, setQuery] = useState(undefined);

  const [combineQueryDetail, setCombineQueryDetail] =
    useState<CombineQueryDetail>({});

  const [combineQueryTitle, setCombineQueryTitle] = useState('');

  const [sorterFilter, setSorterFilter] = useState({});

  const [selectedRowId, setSelectedRowId] = useState(undefined);

  const [
    combineQueryDetailColumnTemplateTitle,
    setCombineQueryDetailColumnTemplateTitle,
  ] = useState('');

  const [
    combineQueryDetailColumnTemplateId,
    setCombineQueryDetailColumnTemplateId,
  ] = useState(undefined);

  const [summaryData, setSummaryData] = useState([]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: pageSize,
    defaultPageSize: pageSize,
    pageSizeOptions: ['1000', '2000', '5000'],
    hideOnSinglePage: false,
  });

  useImperativeHandle(props?.tableRef, () => ({
    getTableDataSize: () => {
      return detailTableDataSource?.length;
    },

    getColumnSizingData: () => {
      return columnSizingContentRef?.current;
    },

    getComboSignificantColumnsState: () => {
      return comboSignificantColumnsState;
    },

    getAutoSumAndAvgProps: () => {
      let data = {
        columns: detailColumns,
        columnsState: columnsState,
      };

      data['expr'] = isEmptyValues(query) ? undefined : query;

      // 当且仅当有context然后form中能拿到值的时候才追加
      if (!isEmptyValues(combineQueryContext)) {
        let extraArgs = combineQueryContext?.basicArgForm?.getFieldsValue();
        if (!isEmptyValues(extraArgs)) {
          let processedExtraArgs = dateRangeValueProcessor(
            extraArgs,
            props?.nextGeneration === true,
          );
          if (!isEmptyValues(processedExtraArgs)) {
            data['BasicArgs'] = processedExtraArgs;
          }
        }
      }

      return data;
    },

    selectQueryTemplateQueryDetail: (data: CombineQueryDetail) => {
      setQuery(undefined);
      setTimeout(() => {
        setQuery(data?.Expr);
      }, 0);
      setCombineQueryDetail(data);
    },

    openColumnCustomizer: () => {
      Emitter.emit(
        StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_COLUMN_CUSTOMIZER_CHANGE,
        {
          status: true,
        },
      );
    },
    saveColumnTemplate: () => {
      Emitter.emit(
        StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_ADD,
        {
          originTitle: combineQueryDetailColumnTemplateTitle,
          type: 'save',
        },
      );
    },
    saveAsColumnTemplate: () => {
      Emitter.emit(
        StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TEMPLATE_SAVE_AS,
        {
          originTitle: combineQueryDetailColumnTemplateTitle,
          type: 'saveAs',
        },
      );
    },
    getExportDataConfig: (exportType?: string) => {
      let data = {
        outputColumns: getExportCaptionByColumns(
          detailColumns,
          getSelectedColumns(),
        ), // getSelectedColumns(),
        expr: isEmptyValues(query) ? undefined : query,
        // exportCaption: ,

        ...(props?.metricDetailExtraContentContainerRef?.current?.getExtraContentParams() ??
          {}),
      };

      // 当且仅当有context然后form中能拿到值的时候才追加
      if (!isEmptyValues(combineQueryContext)) {
        let extraArgs = combineQueryContext?.basicArgForm?.getFieldsValue();
        if (!isEmptyValues(extraArgs)) {
          let processedExtraArgs = dateRangeValueProcessor(
            extraArgs,
            props?.nextGeneration === true,
          );
          if (!isEmptyValues(processedExtraArgs)) {
            data['BasicArgs'] = processedExtraArgs;
          }
        }
      }

      let exportInterfaceUrl = 'Api/DmrAnalysis/ComboQuery/ExportGetDetails';
      if (!isEmptyValues(exportType)) {
        if (exportType === 'ICDE_EXPORT') {
          exportInterfaceUrl = 'Api/DmrAnalysis/ComboQuery/ExportIcdeDetails';
        }

        if (exportType === 'OPER_EXPORT') {
          exportInterfaceUrl = 'Api/DmrAnalysis/ComboQuery/ExportOperDetails';
        }

        if (exportType === 'OPER_GROUP_EXPORT') {
          exportInterfaceUrl =
            'Api/DmrAnalysis/ComboQuery/ExportOperGroupDetails';
        }
      }

      return {
        isBackend: true,
        backendObj: {
          url: exportInterfaceUrl,
          method: 'POST',
          data: data,
          fileName: `组合查询明细-${
            combineQueryTitle || combineQueryDetail?.Title || '未命名'
          }`,
          // customClkFunc: () => exportDetailTable()
        },
      };
    },
  }));

  useEffect(() => {
    combineQueryDataColumnReq();

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
      (data: CombineQueryDetail) => {
        setQuery(undefined);
        setTimeout(() => {
          setQuery(data?.Expr);
        }, 0);
        setCombineQueryDetail(data);
        setDetailTableDataSource([]);
        Emitter.emit(
          StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TAB_COUNT,
          {
            total: 0,
          },
        );
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TITLE,
      (title: string) => {
        setCombineQueryTitle(title);
      },
    );

    // 查询模板点击后，明细模板的点击要清空
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_CLICK,
      (item) => {
        setCombineQueryDetailColumnTemplateTitle(undefined);
      },
    );

    Emitter.on(StatsAnalysisEventConstant.STATS_ANALYSIS_RESET, () => {
      // 清除数据
      let pagination = {
        ...backPagination,
        current: 1,
        pageSize: pageSize,
        total: 0,
      };
      setQuery(undefined);
      setCombineQueryDetail({});
      setCombineQueryTitle('');
      setBackPagination(pagination);
      setDetailTableDataSource([]);
    });

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_DETAIL,
      );

      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TITLE,
      );

      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_TEMPLATE_CLICK);

      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_RESET);
    };
  }, []);

  // 李惠利定制 DMRDEV-871
  useEffect(() => {
    if (!isEmptyValues(columnsState) && detailColumns?.length > 0) {
      onDetailTableColumnChange(
        props?.metricDetailExtraContentContainerRef,
        detailColumns,
        columnsState,
      );
    }
  }, [columnsState, detailColumns]);

  useEffect(() => {
    // 通知tab title 更新数量
    Emitter.emit(StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TAB_COUNT, {
      total: backPagination?.total ?? 0,
    });
  }, [backPagination?.total]);

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_CLICK,
      (item) => {
        if (item?.Id) {
          if (item?.Id === 'DEFAULT') {
            let currentDetail = Object.assign({}, combineQueryDetail);
            const defaultVisibleColumnStateKeys = Object.keys(
              defaultColumnsState,
            )?.filter((key) => defaultColumnsState[key]?.show === true);
            currentDetail['SubjectOutputColumns'] = defaultDetailColumns
              ?.filter((item) =>
                defaultVisibleColumnStateKeys?.includes(item?.name),
              )
              ?.map((item, index) => {
                return {
                  ColumnSort: item?.columnSort ?? index,
                  CustomTitle: null,
                  Id: item?.id,
                };
              });

            setCombineQueryDetail(currentDetail);
            setCombineQueryDetailColumnTemplateId(undefined);
            setCombineQueryDetailColumnTemplateTitle('常用列');
          } else if (item?.Id === 'COMBO_TEMPLATE') {
            // 模板自带
            let currentDetail = Object.assign({}, combineQueryDetail);
            currentDetail['SubjectOutputColumns'] = item?.columns;
            setCombineQueryDetail(currentDetail);
            setCombineQueryDetailColumnTemplateId(undefined);
            setCombineQueryDetailColumnTemplateTitle(item?.label);
          } else {
            detailColumnsTemplatedGetReq(item?.Id);
          }
          setCombineQueryDetailColumnTemplateId(item?.Id);
          setCombineQueryDetailColumnTemplateTitle(item?.Title);
          setSorterFilter({});
        }
      },
    );

    return () => {
      Emitter.off(StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_CLICK);
    };
  }, [combineQueryDetail, defaultDetailColumns]);

  useEffect(() => {
    // 这边的监听是针对：左侧collapse那边修改名称后传过来的名称修改
    // 保存与另存为的名称修改 不在这边
    Emitter.on(
      `${StatsAnalysisEventConstant.STATS_ANALYSIS_TITLE_UPDATE}-detail`,
      (item) => {
        if (item?.Id === combineQueryDetailColumnTemplateId) {
          setCombineQueryDetailColumnTemplateTitle(item?.Title);
        }
      },
    );

    Emitter.on(
      StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_DELETE_SUCCESS,
      (id) => {
        if (id === combineQueryDetailColumnTemplateId) {
          setCombineQueryDetailColumnTemplateId(undefined);
          // setCombineQueryDetailColumnTemplateTitle('');
        }
      },
    );

    return () => {
      Emitter.off(
        `${StatsAnalysisEventConstant.STATS_ANALYSIS_TITLE_UPDATE}-detail`,
      );
      Emitter.off(
        StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_DELETE_SUCCESS,
      );
    };
  }, [combineQueryDetailColumnTemplateId]);

  useEffect(() => {
    console.log('columnsStateChanged', columnsState.PatNo);
    Emitter.emit(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_COLUMNS_STATE,
      columnsState,
    );
  }, [columnsState]);

  useEffect(() => {
    Emitter.on('COLUMN_SETTING_TITLE_EDIT', (data) => {
      if (data?.columnKey && data?.title) {
        let columnItem = detailColumns?.find(
          (item) => item.name === data?.columnKey,
        );
        if (columnItem) {
          // columnItem['originTitle'] = columnItem['title'];
          columnItem['title'] = data?.title;
          columnItem['customTitle'] = data?.title;
        }

        setDetailColumns([...detailColumns]);
      }
    });

    return () => {
      Emitter.off('COLUMN_SETTING_TITLE_EDIT');
    };
  }, [detailColumns]);

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY,
      (query) => {
        setQuery(undefined);
        setTimeout(() => {
          setQuery(query);
          let activeKey = props?.tabContainer?.current?.getActiveKey();
          if (activeKey === 'COMBO_DETAIL') {
            queryDetail(query, columnsState);
          }
        }, 0);
      },
    );

    return () => {
      Emitter.off(StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY);
    };
  }, [columnsState]);

  useEffect(() => {
    // TODO detailColumns 有问题
    if (!isEmptyValues(combineQueryDetail)) {
      let columnState = defaultCheckedColumnStateProcessor(
        detailColumns,
        combineQueryDetail?.SubjectOutputColumns,
        columnSizingContentRef,
      );

      setColumnState(columnState);
      setDefaultColumnState(cloneDeep(columnState));
    }
  }, [detailColumns, combineQueryDetail]);

  useEffect(() => {
    if (!isEmptyValues(combineQueryDetail)) {
      let columnData = columnDataCustomTitleStateProcessor(
        detailColumns,
        combineQueryDetail?.SubjectOutputColumns,
      );

      setDetailColumns(columnData?.slice());
    }
  }, [combineQueryDetail]);

  const queryDetail = (query: string, columnsState: any) => {
    let pagination = {
      ...backPagination,
      current: 1,
      pageSize: pageSize,
      total: 0,
    };

    let outputColumns = getSelectedColumns();
    if (outputColumns?.length === 0) {
      if (!isEmptyValues(columnsState)) {
        message.error('请选择需要展示的列');
      }
      setDetailTableDataSource([]);
      return;
    }

    combineQueryDataReq(
      query,
      pagination.current,
      pagination.pageSize,
      outputColumns,
      0,
    );

    autoSumAndAvg(
      query,
      props?.tableRef,
      props?.metricContainerRef,
      setSummaryData,
    );

    setBackPagination(pagination);
  };

  // useEffect(() => {
  //   if (expressionEmptySearch === false && isEmptyValues(query)) {
  //     return;
  //   }
  //
  //   if (query !== undefined) {
  //     let pagination = {
  //       ...backPagination,
  //       current: 1,
  //       pageSize: pageSize,
  //       total: 0,
  //     };
  //
  //     let outputColumns = getSelectedColumns();
  //     if (outputColumns?.length === 0) {
  //       if (!isEmptyValues(columnsState)) {
  //         message.error('请选择需要展示的列');
  //       }
  //       setDetailTableDataSource([]);
  //       return;
  //     }
  //
  //     combineQueryDataReq(
  //       query,
  //       pagination.current,
  //       pagination.pageSize,
  //       outputColumns,
  //       0,
  //     );
  //     setBackPagination(pagination);
  //   }
  // }, [columnsState, query]);

  useEffect(() => {
    Emitter.on(
      StatsAnalysisEventConstant.STATS_ANALYSIS_TAB_SWITCH_DETAIL_QUERY,
      (data: any) => {
        let instantQuery = true;
        if (data?.queryWhenUpdate === true) {
          if (data?.query) {
            if (isEqual(data?.query, query)) {
              instantQuery = false;
            }
          }
        }

        if (data?.query !== query) {
          setQuery(data?.query);
          return;
        }

        if (instantQuery === false) {
          return;
        }

        let pagination = {
          ...backPagination,
          current: 1,
          pageSize: pageSize,
          total: 0,
        };

        let outputColumns = getSelectedColumns();
        if (outputColumns?.length === 0) {
          if (!isEmptyValues(columnsState)) {
            message.error('请选择需要展示的列');
          }
          setDetailTableDataSource([]);
          return;
        }

        setSorterFilter({});
        setBackPagination(pagination);

        setTimeout(() => {
          combineQueryDataReq(
            data?.query,
            pagination.current,
            pagination.pageSize,
            outputColumns,
            0,
          );

          // GetStats
        }, 0);
      },
    );

    return () => {
      Emitter.off(
        StatsAnalysisEventConstant.STATS_ANALYSIS_TAB_SWITCH_DETAIL_QUERY,
      );
    };
  }, [columnsState, query]);

  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filter,
    sorter: any,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    let outputColumns = getSelectedColumns();
    if (outputColumns?.length === 0) {
      message.error('请选择需要展示的列');
      setDetailTableDataSource([]);
      return;
    }

    let sorterFilter = {
      order: [],
      columns: [],
    };

    if (!isEmptyValues(sorter)) {
      if (sorter?.order) {
        let orderIndex = outputColumns?.findIndex(
          (item) => item.Name === (sorter?.columnKey || sorter?.field),
        );

        if (!isEmptyValues(orderIndex)) {
          sorterFilter?.order.push({
            column: orderIndex,
            dir: sorter?.order === 'ascend' ? 'asc' : 'desc',
          });
        }
      }
    }

    setSorterFilter(sorterFilter);

    setTimeout(() => {
      combineQueryDataReq(
        query,
        pagi.current,
        pagi.pageSize,
        outputColumns,
        -1,
      );
    }, 0);
  };

  const paginationType = infiniteScroll === true ? 'INFINITE' : 'PAGINATION';

  // 分页 / 无限滚动
  const paginationInfiniteScrollItemMap = {
    PAGINATION: {
      yAxisOffset: 64,
      pagination: backPagination,
      onTableChange: backTableOnChange,
      setData: (currentData, detailData, recordsTotal) => {
        setDetailTableDataSource(detailData);
        document.getElementById('tanstack-table-container').scrollTo({
          behavior: 'instant',
          top: 0,
        });
        setBackPagination({
          ...backPagination,
          total: recordsTotal || 0,
        });
      },
      setDataWhenError: () => {
        setDetailTableDataSource([]);

        setBackPagination({
          ...backPagination,
          total: 0,
        });
      },
      loadingExtra: true,
    },
    INFINITE: {
      yAxisOffset: 0,
      pagination: null,
      onTableChange: null,
      setData: (currentData, detailData, recordsTotal) => {
        setDetailTableDataSource(currentData?.concat(detailData));

        setBackPagination({
          ...backPagination,
          current: backPagination?.current + 1,
          total: recordsTotal || 0,
        });
      },
      setDataWhenError: () => {
        setDetailTableDataSource([]);

        setBackPagination({
          ...backPagination,
          current: 1,
          pageSize: pageSize,
          total: 0,
        });
      },
      loadingExtra: fetchNextPageFlagRef.current === false,
    },
  };

  const { loading: combineQueryDataLoading, run: combineQueryDataReq } =
    useRequest(
      (query, current, pageSize, columns, currentDataSize) => {
        if (current === 1) {
          fetchNextPageFlagRef.current = false;
        }

        let data = {
          DtParam: {
            Draw: 1,
            Start:
              currentDataSize === -1
                ? (current - 1) * pageSize
                : currentDataSize,
            Length: pageSize,
          },
          outputColumns: columns,
        };

        if (!isEmptyValues(query)) {
          data['expr'] = query;
        }

        // 当且仅当有context然后form中能拿到值的时候才追加
        if (!isEmptyValues(combineQueryContext)) {
          let extraArgs = combineQueryContext?.basicArgForm?.getFieldsValue();
          if (!isEmptyValues(extraArgs)) {
            let processedExtraArgs = dateRangeValueProcessor(
              extraArgs,
              props?.nextGeneration === true,
            );
            if (!isEmptyValues(processedExtraArgs)) {
              data['BasicArgs'] = processedExtraArgs;
            }
          }
        }

        if (!isEmptyValues(sorterFilter)) {
          delete global['tableParameters'];
          data['DtParam'] = {
            ...data['DtParam'],
            ...sorterFilter,
          };
        }

        let getDetailUrl = 'Api/DmrAnalysis/ComboQuery/GetDetails';
        let currentSelectedDetailTypeItem =
          props?.metricDetailExtraContentContainerRef?.current?.getCurrentSelectedDetailTypeItem();

        data = {
          ...data,
          ...(props?.metricDetailExtraContentContainerRef?.current?.getExtraContentParams() ??
            {}),
        };

        return uniCombineQueryService(
          currentSelectedDetailTypeItem?.queryUrl ?? getDetailUrl,
          {
            method: 'POST',
            requestType: 'json',
            data: data,
            // headers: {
            //   // 增加traceId 用于 防止 多次相同请求
            //   traceId: md5(JSON.stringify(data))?.toString(),
            // },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<TableResp<any, any>>) => {
          return response;
        },
        onSuccess: (response: RespVO<TableResp<any, any>>, params) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            if (response?.cancelled === true) {
              return;
            }

            let detailData = response?.data?.data?.map((item) => {
              item['id'] = uuidv4();
              return item;
            });

            if (isEmptyValues(detailData)) {
              // 跳出 不设定值
              if (params[1] === 1) {
                setDetailTableDataSource([]);
                setSelectedRowId(undefined);
              }
              return;
            }

            let currentData = detailTableDataSource?.slice();
            if (params[1] === 1) {
              currentData = [];
              setSelectedRowId(undefined);
              document.getElementById('tanstack-table-container').scrollTo({
                behavior: 'instant',
                top: 0,
              });
            }
            paginationInfiniteScrollItemMap[paginationType]?.setData(
              currentData,
              detailData,
              response?.data?.recordsTotal,
            );
          } else {
            paginationInfiniteScrollItemMap[paginationType]?.setDataWhenError();
          }

          return response;
        },
      },
    );

  const {
    loading: combineQueryDataColumnLoading,
    run: combineQueryDataColumnReq,
  } = useRequest(
    () => {
      let data = {
        TableName: props?.tableName,
      };

      return uniCombineQueryService(
        'Api/Analysis/AnaModelDef/GetRetColSubjectList',
        {
          params: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<DetailColumnItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let tableColumns = response?.data?.slice();
          tableColumns?.forEach((item, index) => {
            // item['width'] = 200;
            item['dataIndex'] = item?.name;
            item['originTitle'] = item?.title;
          });
          let columnState = defaultCheckedColumnStateProcessor(
            tableColumns,
            [],
            columnSizingContentRef,
          );

          setColumnState(columnState);
          setDefaultColumnState(cloneDeep(columnState));
          setComboSignificantColumnsState(cloneDeep(columnState));
          setDetailColumns(tableColumns);
          setDefaultDetailColumns(cloneDeep(tableColumns));
        } else {
          setDetailColumns([]);
          setDefaultDetailColumns([]);
          setColumnState({});
          setDefaultColumnState({});
          setComboSignificantColumnsState({});
        }
      },
    },
  );

  const {
    loading: detailColumnsTemplateGetLoading,
    run: detailColumnsTemplatedGetReq,
  } = useRequest(
    (id) => {
      return uniCombineQueryService('Api/DmrAnalysis/ComboQueryTemplate/Get', {
        params: {
          id: id,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<CombineQueryDetail>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          if (!isEmptyValues(response?.data?.SubjectOutputColumns)) {
            let currentDetail = Object.assign({}, combineQueryDetail);
            currentDetail['SubjectOutputColumns'] =
              response?.data?.SubjectOutputColumns;

            setCombineQueryDetail(currentDetail);
            setCombineQueryDetailColumnTemplateTitle(response?.data?.Title);
          } else {
            message.warn('当前模板不存在列');
          }
        }
      },
    },
  );

  const {
    loading: detailColumnsTemplateSaveLoading,
    run: detailColumnsTemplatedSaveReq,
  } = useRequest(
    (data) => {
      return uniCombineQueryService(
        'Api/DmrAnalysis/ComboQueryTemplate/SaveDetailOutputTemplate',
        {
          method: 'POST',
          requestType: 'json',
          data: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<string>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('保存成功');
          setCombineQueryDetailColumnTemplateId(response?.data);
          Emitter.emit(
            StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_SAVE_SUCCESS,
            response?.data,
          );
          Emitter.emit(
            StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_EXPAND,
          );

          return 'success';
        } else {
          message.error('保存失败');

          return 'fail';
        }
      },
      onSuccess: (data, params) => {
        // 保存 另存为 在这边更新title
        if (data === 'success') {
          setCombineQueryDetailColumnTemplateTitle(params?.at(0)?.Title);
        }
      },
    },
  );

  const { run: detailDefaultColumnsSaveReq } = useRequest(
    (selectedKeys: string[]) => {
      return uniCombineQueryService(
        'Api/DmrAnalysis/ComboQueryTemplate/SaveCommonSubjects',
        {
          data: selectedKeys,
          method: 'POST',
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('常用列保存成功');
        } else {
          message.warn('常用列保存失败');
        }
      },
    },
  );

  const getSelectedColumns = (value = null) => {
    return selectedTableColumnsProcessor(
      value ?? columnsState,
      columnSizingContentRef,
    );
  };

  // 导出修改
  const getExportCaptionByColumns = (
    columns: any[],
    selectedColumns: any[],
  ) => {
    let exportCaption = selectedColumns?.map((d) => {
      let columnItem = columns?.find((item) => item?.id === d?.Id);

      return {
        ...d,
        columnScale: columnItem?.scale,
        columnPrecision: columnItem?.precision,
        ExportTitle: d?.CustomTitle ?? columnItem?.title ?? '',
      };
    });

    return exportCaption;
  };

  // 导出数据
  const exportDetailTable = async () => {
    message.success('明细导出中....');
    let outputColumns = getSelectedColumns();
    let data = {
      outputColumns: outputColumns,
      expr: isEmptyValues(query) ? undefined : JSON.stringify(query),
      exportCaption: getExportCaptionByColumns(detailColumns, outputColumns),
    };

    let detailExportResponse = await uniCombineQueryService(
      'Api/DmrAnalysis/ComboQuery/ExportDetails',
      {
        method: 'POST',
        requestType: 'json',
        data: data,
      },
    );

    if (detailExportResponse?.response) {
      message.success('明细导出成功');
      downloadFile(
        `组合查询明细-${
          combineQueryTitle || combineQueryDetail?.Title || '未命名'
        }`,
        detailExportResponse?.response,
      );
    } else {
      message.success('明细导出失败，请联系管理员');
    }
  };

  // 这个是保存
  const saveDetailColumnsTemplate = async (id, title) => {
    message.success('列模板保存中....');
    let outputColumns = getSelectedColumns();
    let data = {
      subjectOutputColumns: outputColumns,
    };

    if (id) {
      data['id'] = id;
    }

    data['Title'] = title ?? combineQueryDetailColumnTemplateTitle;
    // todo ？？？不能为空
    data['Expr'] = '1';
    data['DisPlayExpr'] = '1';

    detailColumnsTemplatedSaveReq(data);
  };

  // 这个是另存为
  const saveAsDetailColumnsTemplate = async (title) => {
    message.success('列模板另存为中....');
    let outputColumns = getSelectedColumns();
    let data = {
      subjectOutputColumns: outputColumns,
    };
    data['Title'] = title ?? combineQueryDetailColumnTemplateTitle;
    // todo ？？？不能为空
    data['Expr'] = '1';
    data['DisPlayExpr'] = '1';

    detailColumnsTemplatedSaveReq(data);
  };

  const onDetailColumnsTemplateOpen = () => {
    Emitter.emit(StatsAnalysisEventConstant.DETAIL_COLUMN_TEMPLATE_OPEN, true);
  };

  const onDragEnd = (fromIndex, toIndex) => {
    let currentVisibleColumns = activeColumnsWithIndexProcessor(
      detailColumns,
      columnsState,
    );

    console.log(
      'currentVisibleColumns',
      currentVisibleColumns,
      fromIndex - columns([], undefined, undefined)?.length,
      toIndex - columns([], undefined, undefined)?.length,
    );

    // from Index <---->  to Index 的index 对换
    let fromIndexItemActualIndex =
      currentVisibleColumns[
        fromIndex - columns([], undefined, undefined)?.length
      ]?.order;
    currentVisibleColumns[
      fromIndex - columns([], undefined, undefined)?.length
    ]['order'] =
      currentVisibleColumns[
        toIndex - columns([], undefined, undefined)?.length
      ]?.order;
    currentVisibleColumns[toIndex - columns([], undefined, undefined)?.length][
      'order'
    ] = fromIndexItemActualIndex;

    // 再把这个order放回去
    let currentColumnsState = Object.assign({}, columnsState);
    currentVisibleColumns?.forEach((item) => {
      columnsState[item?.dataIndex]['order'] = item['order'];
    });

    console.log('columnsState', currentColumnsState);

    setColumnState(currentColumnsState);
  };

  const currentDataHisIds = detailTableDataSource
    ?.map((item) => item?.HisId)
    ?.filter((item) => !isEmptyValues(item));

  const onExtraSearchedHisIdLastOne = async () => {
    // 直接获取下一页 然后吐回去
    if (detailTableDataSource?.length >= backPagination?.total) {
      return;
    }

    if (!combineQueryDataLoading) {
      let outputColumns = getSelectedColumns();
      if (outputColumns?.length === 0) {
        message.error('请选择需要展示的列');
        setDetailTableDataSource([]);
        return;
      }

      let tableResponse = await combineQueryDataReq(
        query,
        backPagination?.current,
        pageSize,
        outputColumns,
        detailTableDataSource?.length,
      );

      if (tableResponse?.code === 0 && tableResponse?.statusCode === 200) {
        let tableData = tableResponse?.data?.data;
        return tableData
          ?.map((item) => item?.HisId)
          ?.filter((item) => !isEmptyValues(item));
      } else {
        return [];
      }
    }

    return [];
  };

  const onExtraSearchedHisIdChange = (hisId: string) => {
    // 通过hisId 反查  recordId
    let record = detailTableDataSource?.find((item) => item?.HisId === hisId);

    setSelectedRowId(record?.id);
  };

  const onRowClick = (rowId: string) => {
    setSelectedRowId(rowId);
  };

  const containerWidth =
    document.getElementById('combo-query-v3-tab-container')?.offsetWidth -
    20 -
    2;

  const tableProps = {
    containerWidth: containerWidth,
    id: 'combine-query-table',
    rowKey: 'id',
    scroll: {
      x: 'max-content',
      y:
        props?.nextGeneration === true
          ? props?.tableHeight -
              paginationInfiniteScrollItemMap?.[paginationType]?.yAxisOffset ??
            0 ??
            500
          : 200,
    },
    virtualized: true,
    estimateSize: 50,
    virtualizeOverscan: 15,
    enableColumnResizing: true,
    enableMultipleTranslate: true,
    columns: [
      ...columns(
        currentDataHisIds,
        onExtraSearchedHisIdLastOne,
        onRowClick,
        onExtraSearchedHisIdChange,
      ),
      ...detailColumns,
    ] as any[],
    dataSource: detailTableDataSource,
    loading:
      paginationInfiniteScrollItemMap?.[paginationType]?.loadingExtra &&
      (combineQueryDataLoading ||
        combineQueryDataColumnLoading ||
        detailColumnsTemplateGetLoading),
    clickable: true,
    pagination: paginationInfiniteScrollItemMap?.[paginationType]?.pagination,
    // TODO dict data
    dictionaryData: globalState?.dictData,
    onTableChange:
      paginationInfiniteScrollItemMap?.[paginationType]?.onTableChange,
    bordered: true,
    columnsState: {
      value: columnsState,
    },
    onRowClick: (record, rowIndex) => {
      setSelectedRowId(record?.id);
    },
    rowClassName: (record, index) => {
      return !isEmptyValues(record?.id) && record?.id === selectedRowId
        ? 'table-hisId-row-selected'
        : '';
    },
    forceColumnsUpdate: true,
    infiniteScroll: infiniteScroll ?? false,
    infiniteScrollPageSize: pageSize,
    bottomReachMargin: 500,
    emptyDataYHeight: true,
    enableHeaderNoWrap: false,
    enableHeaderEllipse: true,
    enableContentEllipse: true,
    enableMaxCharNumberEllipses: false,
    noMetaProperty: true,
    enableHeaderDragging: false,
    onColumnSizingEnd: (columnSizing: any) => {
      console.log('ColumnSizing', columnSizing);
      columnSizingContentRef.current = columnSizing;
    },
    onCustomAggregableClick: (metaData: any) => {
      // TODO 直接切换统计 然后调用 GetStats 用来 Group By
      props?.metricContainerRef?.current?.fastAggregable(metaData);

      // 先弹窗再group
      props?.metricContainerRef?.current?.openColumnCustomizer({
        onOkExtra: () => {
          setTimeout(() => {
            props?.tabContainer?.current?.changeActiveKey('COMBO_METRIC');
          }, 0);
        },
      });
    },
    infiniteScrollSorterFilter: (sorter: any, filter: any) => {
      let sorterFilter = {
        order: [],
        columns: [],
      };

      if (!combineQueryDataLoading) {
        let outputColumns = getSelectedColumns();
        if (outputColumns?.length === 0) {
          message.error('请选择需要展示的列');
          setDetailTableDataSource([]);
          return;
        }

        if (!isEmptyValues(sorter)) {
          if (sorter?.order) {
            let orderIndex = outputColumns?.findIndex(
              (item) => item.Name === (sorter?.columnKey || sorter?.field),
            );

            if (!isEmptyValues(orderIndex)) {
              sorterFilter?.order.push({
                column: orderIndex,
                dir: sorter?.order === 'ascend' ? 'asc' : 'desc',
              });
            }
          }
        }

        if (!isEmptyValues(filter)) {
        }

        let pagination = {
          ...backPagination,
          current: 1,
          pageSize: pageSize,
          total: 0,
        };

        setSorterFilter(sorterFilter);
        setBackPagination(pagination);

        setTimeout(() => {
          combineQueryDataReq(
            query,
            pagination?.current,
            pageSize,
            outputColumns,
            0,
          );
          // GetStats
        }, 0);
      }
    },
    fetchNextPage: async (pageIndex, pageSize, callback) => {
      if (detailTableDataSource?.length >= backPagination?.total) {
        return;
      }

      if (!combineQueryDataLoading) {
        let outputColumns = getSelectedColumns();
        if (outputColumns?.length === 0) {
          message.error('请选择需要展示的列');
          setDetailTableDataSource([]);
          return;
        }

        fetchNextPageFlagRef.current = true;

        await combineQueryDataReq(
          query,
          backPagination?.current,
          pageSize,
          outputColumns,
          detailTableDataSource?.length,
        );

        callback && callback();
      }

      return [];
    },
  };

  console.log('tableProps?.columns', tableProps?.columns, columnsState);

  const TableWrapperContainer =
    props?.needCardContainer === false ? EmptyWrapper : Card;

  const TableComponent = props?.nextGeneration === true ? UniTableNG : UniTable;

  return (
    <Container initValue={tableProps}>
      <TableWrapperContainer
        className={'combine-query-detail-table-container'}
        title={
          <Space>
            <span>明细</span>
            <Tag>
              {combineQueryDetailColumnTemplateTitle
                ? `模板：${combineQueryDetailColumnTemplateTitle}`
                : '无模板'}
            </Tag>
          </Space>
        }
        extra={
          <Space>
            {props?.nextGeneration !== true && (
              <>
                <Button
                  key="button"
                  loading={detailColumnsTemplateSaveLoading}
                  onClick={() =>
                    Emitter.emit(
                      StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TEMPLATE_SAVE_AS,
                      {
                        originTitle: combineQueryDetailColumnTemplateTitle,
                        type: 'saveAs',
                      },
                    )
                  }
                >
                  另存为模板
                </Button>
                <Button
                  key="button"
                  loading={detailColumnsTemplateSaveLoading}
                  onClick={() =>
                    Emitter.emit(
                      StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_ADD,
                      {
                        originTitle: combineQueryDetailColumnTemplateTitle,
                        type: 'save',
                      },
                    )
                  }
                >
                  保存列模板
                </Button>
              </>
            )}
            <CombineQueryDetailColumnSettings
              nextGeneration={props?.nextGeneration ?? false}
              columns={columnSettingColumnsProcessor(
                detailColumns,
                columnsState,
              )}
              columnsMap={columnsState}
              onDefaultColumnSelect={(keys: string[]) => {
                const currentSelectedTemplateItemId =
                  props?.templateSelectorContainerRef?.current?.getCurrentSelectedItemId();
                if (currentSelectedTemplateItemId === 'DEFAULT') {
                  // 更新数据
                  detailDefaultColumnsSaveReq(keys);
                }
              }}
              onColumnSelect={(value) => {
                const currentSelectedTemplateItemId =
                  props?.templateSelectorContainerRef?.current?.getCurrentSelectedItemId();

                if (currentSelectedTemplateItemId === 'DEFAULT') {
                  setComboSignificantColumnsState(value);
                }

                onDetailTableColumnChange(
                  props?.metricDetailExtraContentContainerRef,
                  detailColumns,
                  value,
                );

                setTimeout(() => {
                  // null 表示重置
                  if (value) {
                    setColumnState(value);
                    if (detailTableDataSource?.length > 0) {
                      queryDetail(query, value);
                    }
                  } else {
                    setColumnState(defaultColumnsState);
                    if (detailTableDataSource?.length > 0) {
                      queryDetail(query, defaultColumnsState);
                    }
                  }
                }, 0);
              }}
            />
            {props?.nextGeneration !== true && (
              <>
                <Divider type="vertical" />
                <ExportIconBtn
                  isBackend={true}
                  backendObj={{
                    url: 'Api/DmrAnalysis/ComboQuery/ExportGetDetails',
                    method: 'POST',
                    data: {
                      outputColumns: getExportCaptionByColumns(
                        detailColumns,
                        getSelectedColumns(),
                      ), // getSelectedColumns(),
                      expr: isEmptyValues(query) ? undefined : query,
                      // exportCaption: ,
                    },
                    fileName: `组合查询明细-${
                      combineQueryTitle || combineQueryDetail?.Title || '未命名'
                    }`,
                    // customClkFunc: () => exportDetailTable()
                  }}
                  btnDisabled={detailTableDataSource?.length < 1}
                />
              </>
            )}
          </Space>
        }
      >
        {/*<ReactDragColumnView*/}
        {/*  onDragEnd={onDragEnd}*/}
        {/*  nodeSelector="th"*/}
        {/*  scrollElement={document.querySelector(*/}
        {/*    "div#combine-query-table div[class='ant-table-body']",*/}
        {/*  )}*/}
        {/*>*/}
        <TableComponent
          {...tableProps}
          renderSummary={(tableColumns: any[]) => {
            return (
              <ComboDetailSummary
                tableContainerRef={props?.tableRef}
                tableColumns={tableColumns}
                summaryData={summaryData}
              />
            );
          }}
        />
        {/*</ReactDragColumnView>*/}
      </TableWrapperContainer>

      <ColumnTemplateTitleAdd
        eventName={[
          StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TEMPLATE_TITLE_ADD,
          StatsAnalysisEventConstant.STATS_ANALYSIS_DETAIL_TEMPLATE_SAVE_AS,
        ]}
        onTemplateTitleFillIn={(title, type) => {
          if (type === 'save') {
            saveDetailColumnsTemplate(
              combineQueryDetailColumnTemplateId,
              title,
            );
          } else if (type === 'saveAs') {
            saveAsDetailColumnsTemplate(title);
          }
        }}
      />
    </Container>
  );
};

export default CombineQueryTable;
