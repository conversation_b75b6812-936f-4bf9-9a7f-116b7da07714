import { useState } from 'react';
import { useRequest, useModel } from 'umi';
import { Col, Row, Tabs } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import _ from 'lodash';
import { OperBmChartSelectOptions } from './constants';
import { OperTrendsBar } from '@/echarts/oper.chart.opts';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import BmTable from '@/components/BmTable/index';
import { RespVO } from '@uni/commons/src/interfaces';
import SingleColumnTable from '@/components/SingleColumnTable/index';
import { uniCommonService } from '@uni/services/src/commonService';
import Stats from '@/components/stats/index';
import { OperNormalStat } from '@/constants';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import DrawerCardInfo from '@/components/drawerCardInfo/index';

const HospOperRate = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // 这边调bundleData
  const {
    data: BundledData,
    loading: getBundledDataLoading,
    run: getBundledDataReq,
  } = useRequest(
    (data) =>
      uniCommonService('Api/v2/Drgs/HospOper/BundledOperRate', {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
    };
    setTableParams(params);
    getBundledDataReq(params);
  }, [dateRange, hospCodes]);

  let tabItems = [
    {
      key: 'statistic',
      label: '全院综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Drgs/HospOper/BundledOperRate`}
              trendApi={`Api/v2/Drgs/HospOper/OperRateTrend`}
              columns={OperNormalStat}
              defaultSelectItem={'OperPatCnt'}
              type="col-xl-8"
              chartHeight={310}
              tabKey={activeKey}
              useGlobalState
              tableParams={tableParams}
            />
          </Col>
          <SingleColumnTable
            title="全院外科能力"
            args={{
              api: `Api/v2/Drgs/HospOper/OperRateByHosp`,
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="HospName"
            type="table"
            colSpan={{ span: 24 }}
            orderKey="OperPatCnt"
            visibleValueKeys={['HospName', 'TotalCnt', 'OperPatCnt']}
            chart={{
              api: `Api/v2/Drgs/HospOper/OperRateTrend`,
              title: '各级手术变化趋势',
              options: (data) => {
                return data
                  ? OperTrendsBar(
                      data?.map((d) => ({
                        ...d,
                        MonthDate: valueNullOrUndefinedReturnDash(
                          d?.MonthDate,
                          'Month',
                        ),
                      })),
                      'MonthDate',
                    )
                  : {};
              },
              colSpan: { span: 24 },
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.HospName + '手术人次',
                args: {
                  Sdate: dateRange?.at(0),
                  Edate: dateRange?.at(1),
                  HospCode: [record?.HospCode],
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/OperDetails',

                dictData: dictData, // 传入
              });
            }}
          />
          {/* TODO 各级别术种数量 */}
          <Col></Col>
        </Row>
      ),
    },
    {
      key: 'hosp_deptAnalysis',
      label: '科室综合分析',
      children: (
        <Row gutter={[16, 16]}>
          {/* <SingleColumnTable
            title="绩效科室外科能力"
            args={{
              api: undefined,
            }}
            tableParams={tableParams}
            dictData={dictData}
            type="table"
            visibleValueKeys={['HospName', 'TotalCnt', 'OperPatCnt']}
          /> */}
          <SingleColumnTable
            title="临床科室外科能力"
            args={{
              api: 'Api/v2/Drgs/CliDeptOper/OperRateByCliDept',
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="CliDeptName"
            type="table"
            orderKey="OperPatCnt"
            visibleValueKeys={[
              'CliDeptName',
              'TotalCnt',
              'OperPatCnt',
              'OperPatRatio',
              'Grade3Or4OperPatCnt',
              'Grade3Or4OperPatRatio',
              'AvgPreOperPeriod',
              'AvgPostOperPeriod',
            ]}
            colSpan={{ span: 24 }}
            // chart={{
            //   api: 'Api/Drgs/CliDeptHighRw/HighRwADrgComposition',
            //   type: 'bar',
            //   valueKeys: ['PatCnt'],
            //   category: 'ADrgName',
            //   yAxis: 'TotalCnt',
            // }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.CliDeptName + '手术人次',
                args: {
                  ...tableParams,
                  CliDepts: [record?.CliDept],
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/OperDetails',

                dictData: dictData, // 传入
              });
            }}
          />
        </Row>
      ),
    },
    {
      key: 'hosp_medTeamAnalysis',
      label: '医疗组综合分析',
      children: (
        <SingleColumnTable
          title="医疗组外科能力"
          args={{
            api: 'Api/v2/Drgs/MedTeamOper/OperRateByMedTeam',
          }}
          tableParams={tableParams}
          dictData={dictData}
          category="MedTeamName"
          type="table"
          orderKey="OperPatCnt"
          visibleValueKeys={[
            'MedTeamName',
            'TotalCnt',
            'OperPatCnt',
            'OperPatRatio',
            'Grade3Or4OperPatCnt',
            'Grade3Or4OperPatRatio',
            'AvgPreOperPeriod',
            'AvgPostOperPeriod',
          ]}
          colSpan={{ span: 24 }}
          //   chart={{
          //     api: 'Api/Drgs/MedTeamHighRw/HighRwADrgComposition',
          //     type: 'bar',
          //     valueKeys: ['PatCnt'],
          //     category: 'ADrgName',
          //     yAxis: 'TotalCnt',
          //   }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.MedTeamName + '手术人次',
              args: {
                ...tableParams,
                MedTeams: [record?.MedTeam],
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/OperDetails',

              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <SingleColumnTable
          title="医生外科能力"
          args={{
            api: 'Api/v2/Drgs/SurgeonOper/OperRateBySurgeon',
          }}
          tableParams={tableParams}
          dictData={dictData}
          type="table"
          orderKey="OperPatCnt"
          visibleValueKeys={[
            'SurgeonName',
            // 'TotalCnt',
            'OperPatCnt',
            // 'OperPatRatio',
            'Grade3Or4OperPatCnt',
            'Grade3Or4OperPatRatio',
            'AvgPreOperPeriod',
            'AvgPostOperPeriod',
          ]}
          colSpan={{ span: 24 }}
          select={{
            dataKey: 'SurgeonType',
            valueKey: 'SurgeonType',
            allowClear: false,
            defaultSelect: true,
          }}
          //   chart={{
          //     api: 'Api/Drgs/MedTeamHighRw/HighRwADrgComposition',
          //     type: 'bar',
          //     valueKeys: ['PatCnt'],
          //     category: 'ADrgName',
          //     yAxis: 'TotalCnt',
          //   }}
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.SurgeonName + '手术人次',
              args: {
                ...tableParams,
                SurgeonType: record?.SurgeonType?.toLocaleUpperCase(),
                SurgeonCodes: [record?.SurgeonCode],
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/OperDetails',

              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'hosp_BmAnalysis',
      label: '标杆值',
      children: (
        <BmTable
          tableParams={tableParams}
          api="Api/v2/Drgs/HospOper/OperRateHospBm"
          bundleData={BundledData}
          BmChartSelectOptions={OperBmChartSelectOptions}
        />
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
    // <Row gutter={[16, 16]}>
    //   <Col span={24}>
    //     <Row gutter={[16, 16]}>
    //       {(statisticState.data.length > 0
    //         ? statisticState.data
    //         : HospOperRateStat
    //       ).map((d) => {
    //         return (
    //           <Col key={d?.dataIndex} span={6}>
    //             <SingleStat
    //               {...d}
    //               loading={loadings['HospOperRate'] ?? false}
    //               args={{
    //                 ...d?.args,
    //                 DateMode: 2,
    //                 Sdate: searchParams?.dateRange[0],
    //                 Edate: searchParams?.dateRange[1],
    //                 HospCode:
    //                   searchParams?.hospCodes?.length ?? 0 > 0
    //                     ? searchParams?.hospCodes
    //                     : '%',
    //               }}
    //             ></SingleStat>
    //           </Col>
    //         );
    //       })}
    //     </Row>
    //   </Col>
    //   <Col span={24}>
    //     <Row gutter={[16, 16]}>
    //       <Col span={16}>
    //         <Card title={'各级手术变化趋势'}>
    //           <UniEcharts
    //             height={350}
    //             elementId="Line"
    //             loading={loadings['HospOperRate'] ?? false}
    //             options={
    //               (trend.length > 0 &&
    //                 OperTrendsBar(
    //                   trend?.map((d) => ({
    //                     ...d,
    //                     MonthDate: valueNullOrUndefinedReturnDash(
    //                       d?.MonthDate,
    //                       'Month',
    //                     ),
    //                   })),
    //                   'MonthDate',
    //                 )) ||
    //               {}
    //             }
    //           />
    //         </Card>
    //       </Col>
    //       <Col span={8}>
    //         <Row gutter={[16, 16]}>
    //           <Col span={24}>
    //             <Card title={'医院手术人次分布'}>
    //               <SingleList
    //                 data={_.map(
    //                   _.orderBy(
    //                     ListResult.hospOperList,
    //                     ['OperPatCnt'],
    //                     ['desc'],
    //                   ),
    //                   (data, i) => {
    //                     return {
    //                       key: i + 1,
    //                       name: data?.HospName,
    //                       value: data?.OperPatCnt,
    //                     };
    //                   },
    //                 )}
    //                 loading={loadings['HospOperRate'] ?? false}
    //               ></SingleList>
    //             </Card>
    //           </Col>
    //           <Col span={24}>
    //             <Card title={'科室手术人次分布(TOP10)'}>
    //               <SingleList
    //                 data={_.map(
    //                   _.orderBy(
    //                     ListResult.deptOperList,
    //                     ['OperPatCnt'],
    //                     ['desc'],
    //                   ),
    //                   (data, i) => {
    //                     return {
    //                       key: i + 1,
    //                       name: data?.CliDeptName,
    //                       value: data?.OperPatCnt,
    //                     };
    //                   },
    //                 )}
    //                 loading={loadings['HospOperRate'] ?? false}
    //                 length={10}
    //               ></SingleList>
    //             </Card>
    //           </Col>
    //         </Row>
    //       </Col>
    //     </Row>
    //   </Col>
    //   {/* <Col span={8}>
    //     <Card title={'医生外科能力(TOP10)'}>
    //       <SingleList
    //         data={_.map(ListResult.doctorOperList, (data) => {
    //           return {
    //             name: data?.DoctorName,
    //             value: data?.OperPatCnt,
    //           };
    //         })}
    //       ></SingleList>
    //     </Card>
    //   </Col> */}
    // </Row>
  );
};

export default HospOperRate;
