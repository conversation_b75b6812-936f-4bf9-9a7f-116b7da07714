import dayjs from 'dayjs';
import { ProFormDependency, ProFormSelect } from '@uni/components/src/pro-form';

export const SearchFormItems = (hospList, deptList) => [
  {
    name: 'dateRange',
    title: '时间',
    dataType: 'dateRange',
    colProps: { span: 12 },
    transform: (values) => {
      return {
        Sdate: values ? dayjs(values[0]).format('YYYY-MM-DD') : undefined,
        Edate: values ? dayjs(values[1]).format('YYYY-MM-DD') : undefined,
      };
    },
    fieldProps: {
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
    },
  },
  {
    name: 'HospCode',
    title: '院区',
    dataType: 'select',
    // rules: [{ required: true }],
    fieldProps: {
      mode: 'multiple',
    },
    visible: true,
    opts: hospList,
    colProps: { span: 12 },
  },
  {
    name: 'Cli<PERSON>epts',
    title: '科室(基于院区)',
    dataType: 'Custom',
    visible: true,
    render: (
      <ProFormDependency name={['HospCode']}>
        {({ HospCode }) => {
          let options = deptList?.filter(
            (d) =>
              (HospCode || [])?.includes(d.HospCode) && d.HierarchyType === '1',
          );

          return (
            <ProFormSelect
              {...{
                key: 'CliDepts',
                name: 'CliDepts',
                label: '科室(基于院区)',
                // initialValue: dateRange,
                colProps: { span: 12 },
                fieldProps: {
                  placeholder: '请选择',
                  options: options,
                  mode: 'multiple',
                  maxTagCount: 'responsive',
                  fieldNames: {
                    label: 'Name',
                    value: 'Code',
                  },
                  // labelInValue: true,
                },
              }}
            />
          );
        }}
      </ProFormDependency>
    ),
  },
];
