export const mockDiagOptions = [
  {
    name: '主要诊断',
    value: 'MainIcde',
  },
  {
    name: '其他诊断',
    value: 'SecondaryIcdes',
  },
  {
    name: '全诊断',
    value: 'IcdeDscgs',
  },
];

export const mockOperOptions = [
  {
    name: '主要手术',
    value: 'MainOper',
  },
  {
    name: '其他手术',
    value: 'SecondaryOpers',
  },
  {
    name: '全手术',
    value: 'Opers',
  },
];

export const mockConditionTemplate = [
  {
    name: '诊断类',
    children: [
      {
        name: '诊断编码',
        value: 'mainDiagnosis',
        ruleCodeSuffix: '10-0001-',
        contents: [
          {
            name: '',
            nameSelectable: true,
            nameOpts: mockDiagOptions,
            defaultNameValue: 'MainIcde',
            contentType: 'single',
            type: 'diag',
            operator: 'startsWithIn',
            jsonLogic: (title: string, value: any[]) => {
              let conditions: any[] = [
                { startsWithIn: [{ var: 'IcdeCode' }, [...value]] },
              ];
              if (!title?.startsWith('Icde')) {
                conditions.push({
                  '==': [{ var: 'IsMain' }, title?.startsWith('Main')],
                });
              }
              return {
                some: [
                  { var: 'IcdeDscgs' },
                  {
                    and: conditions,
                  },
                ],
              };
            },
          },
        ],
      },
      {
        name: '诊断逻辑冲突',
        value: 'diagnosisConflict',
        ruleCodeSuffix: '10-0005-',
        contents: [
          {
            name: '出院诊断',
            contentType: 'multi',
            type: 'diag',
            listLinkType: 'and',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let valueJsonLogic = value
                ?.filter?.((d) => d?.value)
                ?.map((d) => {
                  return {
                    some: [
                      { var: 'IcdeDscgs' },
                      { startsWithIn: [{ var: 'IcdeCode' }, d?.value] },
                    ],
                  };
                });
              return {
                and: valueJsonLogic,
              };
            },
          },
          {
            name: '逻辑冲突',
            contentType: 'none',
            type: 'diag',
          },
        ],
      },
      {
        name: '诊断附加编码遗漏',
        value: 'diagnosisMissing',
        ruleCodeSuffix: '10-0030-',
        contents: [
          {
            name: '',
            contentType: 'single',
            nameSelectable: true,
            nameOpts: mockDiagOptions,
            defaultNameValue: 'IcdeDscgs',
            type: 'diag',
            operator: 'select_any_in',
            jsonLogic: (title: string, value: any[]) => {
              let conditions: any[] = [
                { startsWithIn: [{ var: 'IcdeCode' }, [...value]] },
              ];
              if (!title?.startsWith('Icde')) {
                conditions.push({
                  '==': [{ var: 'IsMain' }, title?.startsWith('Main')],
                });
              }
              return {
                some: [
                  { var: 'IcdeDscgs' },
                  {
                    and: conditions,
                  },
                ],
              };
            },
          },
          {
            name: '应补充附加编码',
            contentType: 'multi',
            type: 'diag',
            listLinkType: 'and',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let valueJsonLogic = value
                ?.filter?.((d) => d?.value)
                ?.map((d) => {
                  return {
                    '!': {
                      some: [
                        { var: 'IcdeDscgs' },
                        { startsWithIn: [{ var: 'IcdeCode' }, d?.value] },
                      ],
                    },
                  };
                });
              return {
                or: valueJsonLogic,
              };
            },
          },
        ],
      },
      {
        name: '诊断合并',
        value: 'diagnosisMerge',
        ruleCodeSuffix: '10-0006-',
        contents: [
          {
            name: '出院诊断',
            contentType: 'multi',
            type: 'diag',
            listLinkType: 'and',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let valueJsonLogic = value
                ?.filter?.((d) => d?.value)
                ?.map((d) => {
                  return {
                    some: [
                      { var: 'IcdeDscgs' },
                      { startsWithIn: [{ var: 'IcdeCode' }, d?.value] },
                    ],
                  };
                });
              console.log('valueJsonLogic', valueJsonLogic);
              return {
                and: valueJsonLogic,
              };
            },
          },
          // {
          //   name: '应合并编码',
          //   contentType: 'none',
          //   type: 'diag',
          //   // contentType: 'single',
          //   // type: 'diag',
          //   // operator: 'select_any_in',
          // },
        ],
      },
      {
        name: '诊断入院病情为“无”',
        value: 'mainDiagnosisNoAdmissionCondition',
        ruleCodeSuffix: '10-0056-',
        contents: [
          {
            name: '',
            nameSelectable: true,
            nameOpts: mockDiagOptions,
            defaultNameValue: 'IcdeDscgs',
            contentType: 'single',
            type: 'diag',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let conditions: any[] = [
                { startsWithIn: [{ var: 'IcdeCode' }, [...value]] },
              ];
              if (!title?.startsWith('Icde')) {
                conditions.push({
                  '==': [{ var: 'IsMain' }, title?.startsWith('Main')],
                });
              }
              conditions.push({
                in: [{ var: 'IcdeCond' }, ['4']],
              });
              return {
                some: [
                  { var: 'IcdeDscgs' },
                  {
                    and: conditions,
                  },
                ],
              };
            },
          },
        ],
      },
    ],
  },
  {
    name: '手术类',
    children: [
      {
        name: '手术及操作编码',
        value: 'MainOper',
        ruleCodeSuffix: '10-0002-',
        contents: [
          {
            name: '',
            nameSelectable: true,
            nameOpts: mockOperOptions,
            defaultNameValue: 'MainOper',
            contentType: 'single',
            type: 'oper',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let conditions: any[] = [
                { startsWithIn: [{ var: 'OperCode' }, [...value]] },
              ];
              if (!title?.startsWith('Oper')) {
                conditions.push({
                  '==': [{ var: 'IsMain' }, title?.startsWith('Main')],
                });
              }
              return {
                some: [
                  { var: 'Opers' },
                  {
                    and: conditions,
                  },
                ],
              };
            },
          },
        ],
      },
      {
        name: '手术附加编码遗漏',
        value: 'operationMissing',
        ruleCodeSuffix: '10-0024-',
        contents: [
          {
            name: '存在',
            nameSelectable: true,
            nameOpts: mockOperOptions,
            defaultNameValue: 'Opers',
            contentType: 'single',
            type: 'oper',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let conditions: any[] = [
                { startsWithIn: [{ var: 'OperCode' }, [...value]] },
              ];
              if (!title?.startsWith('Oper')) {
                conditions.push({
                  '==': [{ var: 'IsMain' }, title?.startsWith('Main')],
                });
              }
              return {
                some: [
                  { var: 'Opers' },
                  {
                    and: conditions,
                  },
                ],
              };
            },
          },
          {
            name: '遗漏手术',
            contentType: 'multi',
            type: 'oper',
            listLinkType: 'and',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let valueJsonLogic = value
                ?.filter?.((d) => d?.value)
                ?.map((d) => {
                  return {
                    '!': {
                      some: [
                        { var: 'Opers' },
                        { startsWithIn: [{ var: 'OperCode' }, d?.value] },
                      ],
                    },
                  };
                });
              return {
                or: valueJsonLogic,
              };
            },
          },
        ],
      },
      {
        name: '手术合并编码',
        value: 'operationMerge',
        ruleCodeSuffix: '10-0040-',
        contents: [
          {
            name: '手术',
            contentType: 'multi',
            type: 'oper',
            listLinkType: 'and',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let valueJsonLogic = value
                ?.filter?.((d) => d?.value)
                ?.map((d) => {
                  return {
                    some: [
                      { var: 'Opers' },
                      { startsWithIn: [{ var: 'OperCode' }, d?.value] },
                    ],
                  };
                });
              return {
                and: valueJsonLogic,
              };
            },
          },
          // {
          //   name: '应合并编码至',
          //   contentType: 'single',
          //   type: 'oper',
          // },
        ],
      },
      {
        name: '手术逻辑冲突',
        value: 'operationConflict',
        ruleCodeSuffix: '10-0022-',
        contents: [
          {
            name: '手术',
            contentType: 'multi',
            type: 'oper',
            listLinkType: 'and',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let valueJsonLogic = value
                ?.filter?.((d) => d?.value)
                ?.map((d) => {
                  return {
                    some: [
                      { var: 'Opers' },
                      { startsWithIn: [{ var: 'OperCode' }, d?.value] },
                    ],
                  };
                });
              return {
                and: valueJsonLogic,
              };
            },
          },
          {
            name: '逻辑冲突',
            contentType: 'none',
            type: 'oper',
          },
        ],
      },
    ],
  },
  {
    name: '联合类',
    children: [
      {
        name: '手术缺少对应诊断',
        value: 'operationMissingDiagnosis',
        ruleCodeSuffix: '10-0034-',
        contents: [
          {
            name: '有',
            nameSelectable: true,
            nameOpts: mockOperOptions,
            defaultNameValue: 'Opers',
            contentType: 'single',
            type: 'oper',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let conditions: any[] = [
                { startsWithIn: [{ var: 'OperCode' }, [...value]] },
              ];
              if (!title?.startsWith('Oper')) {
                conditions.push({
                  '==': [{ var: 'IsMain' }, title?.startsWith('Main')],
                });
              }
              return {
                some: [
                  { var: 'Opers' },
                  {
                    and: conditions,
                  },
                ],
              };
            },
          },
          {
            name: '缺少',
            nameSelectable: true,
            nameOpts: mockDiagOptions,
            defaultNameValue: 'IcdeDscgs',
            contentType: 'single',
            type: 'diag',
            operator: 'select_any_in',
            // jsonLogic: (title: string, value: any[]) => {
            //   return { '!': { startsWithIn: [{ var: title }, [...value]] } };
            // },
            jsonLogic: (title: string, value: any[]) => {
              let conditions: any[] = [
                { startsWithIn: [{ var: 'IcdeCode' }, [...value]] },
              ];
              if (!title?.startsWith('Icde')) {
                conditions.push({
                  '==': [{ var: 'IsMain' }, title?.startsWith('Main')],
                });
              }
              return {
                '!': {
                  some: [
                    { var: 'IcdeDscgs' },
                    {
                      and: conditions,
                    },
                  ],
                },
              };
            },
          },
        ],
      },
      {
        name: '诊断缺少对应手术',
        value: 'diagnosisMissingOperation',
        ruleCodeSuffix: '10-0049-',
        contents: [
          {
            name: '有',
            nameSelectable: true,
            nameOpts: mockDiagOptions,
            defaultNameValue: 'IcdeDscgs',
            contentType: 'single',
            type: 'diag',
            operator: 'select_any_in',
            jsonLogic: (title: string, value: any[]) => {
              let conditions: any[] = [
                { startsWithIn: [{ var: 'IcdeCode' }, [...value]] },
              ];
              if (!title?.startsWith('Icde')) {
                conditions.push({
                  '==': [{ var: 'IsMain' }, title?.startsWith('Main')],
                });
              }
              return {
                some: [
                  { var: 'IcdeDscgs' },
                  {
                    and: conditions,
                  },
                ],
              };
            },
          },
          {
            name: '缺少',
            nameSelectable: true,
            nameOpts: mockOperOptions,
            defaultNameValue: 'Opers',
            contentType: 'single',
            type: 'oper',
            operator: 'select_any_in',
            jsonLogic: (title, value) => {
              let conditions: any[] = [
                { startsWithIn: [{ var: 'OperCode' }, [...value]] },
              ];
              if (!title?.startsWith('Oper')) {
                conditions.push({
                  '==': [{ var: 'IsMain' }, title?.startsWith('Main')],
                });
              }
              return {
                '!': {
                  some: [
                    { var: 'Opers' },
                    {
                      and: conditions,
                    },
                  ],
                },
              };
            },
          },
        ],
      },
    ],
  },
  {
    name: '病理类',
    children: [
      {
        name: '肿瘤编码与病理诊断编码不对应',
        value: 'tumorCodeNotMatchPathologyDiagnosisCode',
        ruleCodeSuffix: '10-0028-',
        contents: [
          {
            name: '当病理诊断为',
            contentType: 'single',
            type: 'diagPathos',
            defaultNameValue: 'IcdePathos.IcdeCode',
            operator: 'select_any_in',
            jsonLogic: (title: string, value: any[]) => {
              return {
                some: [
                  { var: 'IcdePathos' },
                  {
                    and: [{ startsWithIn: [{ var: 'IcdeCode' }, value] }],
                  },
                ],
              };
            },
          },
          {
            name: '应为',
            nameSelectable: true,
            nameOpts: mockDiagOptions,
            defaultNameValue: 'MainIcde',
            contentType: 'single',
            type: 'diag',
            operator: 'select_any_in',
            jsonLogic: (title: string, value: any[]) => {
              let conditions: any[] = [
                { startsWithIn: [{ var: 'IcdeCode' }, value] },
              ];
              if (!title?.startsWith('Icde')) {
                conditions.push({
                  '==': [{ var: 'IsMain' }, title?.startsWith('Main')],
                });
              }
              return {
                '!': {
                  some: [
                    { var: 'IcdeDscgs' },
                    {
                      and: conditions,
                    },
                  ],
                },
              };
            },
          },
        ],
      },
    ],
  },
  {
    name: '其他类',
    children: [
      {
        name: '自定义条件',
        value: 'customized',
        ruleCodeSuffix: '10-90',
      },
    ],
  },
];

export const mockExtraConditions = [
  {
    label: '性别',
    value: 'gender',
  },
  {
    label: '年龄',
    value: 'age',
  },
  {
    label: '（年龄不足1周岁）年龄',
    value: 'babyAge',
  },
  {
    label: '离院方式',
    value: 'dischargeType',
  },
  {
    label: '住院天数',
    value: 'inHospitalDays',
  },
  {
    label: '出院科室',
    value: 'cliDept',
  },
];
