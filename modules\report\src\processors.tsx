import {
  ReportArgItem,
  ReportComponentItem,
  ReportMasterItem,
} from '@/interfaces';
import dayjs from 'dayjs';
import {
  PeriodicPickerModes,
  PickerModes,
  RangePickerModes,
} from '@/constants';
import isEmpty from 'lodash/isEmpty';
import isNil from 'lodash/isNil';
import { valueFormatter } from '@uni/utils/src/utils';
import {
  specialDateTypeChecker,
  specialDateTypeProcessor,
} from '@uni/utils/src/special-date';

const DATE_TYPES = ['Date', 'Month', 'Quarter', 'Year'];

export const reportAddArgComponentProcessor = (
  reportItem: ReportArgItem,
  masterItem: ReportMasterItem,
) => {
  let component: ReportComponentItem = {};

  // string input
  if (
    reportItem?.ColumnType.toLowerCase() === 'string' &&
    !reportItem?.DictModule
  ) {
    component.name = 'UniInput';
    component.props = {
      maxLength: reportItem?.ColumnLength || -1,
    };
  }

  // int input
  if (reportItem?.ColumnType.toLowerCase() === 'int32') {
    component.name = 'UniRestrictInputNumber';
    component.props = {
      precious: reportItem?.ColumnPrecision,
      scale: reportItem?.ColumnScale,
    };
  }

  // decimal input
  if (reportItem?.ColumnType.toLowerCase() === 'decimal') {
    component.name = 'UniRestrictInputNumber';
    component.props = {
      precious: reportItem?.ColumnPrecision,
      scale: reportItem?.ColumnScale,
    };
  }

  // select
  if (
    reportItem?.ColumnType.toLowerCase() === 'string' &&
    reportItem?.DictModule
  ) {
    component.name = 'UniReportSelect';
    component.props = {
      modelDataKey: reportItem?.DictModule,
      moduleGroupKey: reportItem?.DictModuleGroup,
      placeholder: `请选择${reportItem?.ColumnTitle}`,
      optionNameKey: 'Name',
      optionValueKey: 'Code',
    };

    if (reportItem?.ValuePickMode === 'MultipleValue') {
      component.props['mode'] = 'multiple';
    }
  }

  //  boolean switch
  if (reportItem?.ColumnType.toLowerCase() === 'boolean') {
    component.name = 'UniReportSwitch';
  }

  if (
    reportItem?.ColumnType.toLowerCase() === 'string' &&
    !reportItem?.DictModule
  ) {
    component.name = 'UniInput';
    component.props = {
      maxLength: reportItem?.ColumnLength || -1,
    };
  }

  //  date time  range picker

  if (
    masterItem?.IsPeriodic &&
    masterItem?.AllowedDateGranularity?.length > 0
  ) {
    if (
      reportItem?.ColumnType.toLowerCase() === 'datetime' &&
      reportItem?.ValuePickMode?.toLowerCase() === 'range'
    ) {
      component.name = 'UniReportPeriodic';
      // date | week | month | quarter | year
      component.props = {
        fastSelectDataSource: pickerModesProcessor(
          masterItem?.AllowedDateGranularity,
        ),
      };

      component.props['fastSelectDefaultValue'] =
        component?.props?.fastSelectDataSource?.at(0)?.value;
    }
  } else {
    if (reportItem?.ColumnType.toLowerCase() === 'datetime') {
      if (reportItem?.ValuePickMode?.toLowerCase() === 'range') {
        component.name = 'UniReportFormRangePicker';
        component.props = {
          picker:
            RangePickerModes[
              reportItem?.ColumnCustomType?.toLowerCase() || 'date'
            ]?.value,
          showTime: reportItem?.ColumnCustomType === 'DateTime',
        };
      } else {
        component.name = 'UniDatePicker';
        component.props = {
          picker:
            PickerModes[reportItem?.ColumnCustomType?.toLowerCase() || 'date']
              ?.value,
          showTime: reportItem?.ColumnCustomType === 'DateTime',
        };
      }
    } else if (DATE_TYPES.includes(reportItem?.ColumnType.toLowerCase())) {
      component.name = 'UniDatePicker';
      component.props = {
        picker:
          PickerModes[reportItem?.ColumnCustomType?.toLowerCase() || 'date']
            ?.value,
        showTime: reportItem?.ColumnCustomType === 'DateTime',
      };
    }
  }

  if (component?.name) {
    reportItem['component'] = component;
  }

  return reportItem;
};

export const reportAddArgFormValueProcessor = (
  columns: ReportArgItem[],
  values: any,
  masterItem: ReportMasterItem,
) => {
  let reportArgs = {};

  for (let columnItem of columns) {
    if (!values?.[columnItem?.ColumnName]) {
      continue;
    }

    // datetime && range picker
    if (
      columnItem?.ColumnType.toLowerCase() === 'datetime' &&
      columnItem?.ValuePickMode?.toLowerCase() === 'range'
    ) {
      if (
        masterItem?.IsPeriodic &&
        masterItem?.AllowedDateGranularity?.length > 0
      ) {
        // 月 / 年 季度 / 第一天 & 最后一天
        reportArgs[columnItem?.RangeColumnName0] = dayjs(
          values[columnItem?.ColumnName],
        )
          .startOf(values['periodic'])
          .format('YYYY-MM-DD');
        reportArgs[columnItem?.RangeColumnName1] = dayjs(
          values[columnItem?.ColumnName],
        )
          .endOf(values['periodic'])
          .format('YYYY-MM-DD');
      } else {
        let dateModes =
          RangePickerModes[
            columnItem?.ColumnCustomType?.toLowerCase() || 'date'
          ];
        if (dateModes?.dateProcessor) {
          dateModes
            ?.dateProcessor(values?.[columnItem?.ColumnName])
            ?.forEach((item, index) => {
              if (columnItem[`RangeColumnName${index}`]) {
                reportArgs[columnItem[`RangeColumnName${index}`]] = item;
              }
            });
        }
      }
    } else if (DATE_TYPES.includes(columnItem?.ColumnType?.toLowerCase())) {
      let dateProcessor =
        PickerModes[columnItem?.ColumnCustomType?.toLowerCase() || 'Date'];
      if (dateProcessor) {
        reportArgs[columnItem?.ColumnName] = dateProcessor(
          values?.[columnItem?.ColumnName],
        );
      }
    } else if (columnItem?.ColumnName) {
      // 其他格式数据
      if (columnItem?.ValuePickMode === 'MultipleValue') {
        if (Array.isArray(values?.[columnItem?.ColumnName])) {
          reportArgs[columnItem?.ColumnName] = values?.[columnItem?.ColumnName];
        } else {
          reportArgs[columnItem?.ColumnName] = [
            values?.[columnItem?.ColumnName],
          ];
        }
      } else {
        reportArgs[columnItem?.ColumnName] = values?.[columnItem?.ColumnName];
      }
    }
  }

  return reportArgs;
};

const pickerModesProcessor = (modes: string[]) => {
  let pickerModes = [];
  modes.forEach((item) => {
    if (PeriodicPickerModes[item]) {
      pickerModes.push(PeriodicPickerModes[item]);
    }
  });

  return pickerModes;
};

export const reportAddCustomTitleValueProcessor = (
  masterItem: ReportMasterItem,
  formValues: any,
  args: ReportArgItem[],
  dictionaryData?: any,
) => {
  return masterItem?.CustomTitle?.replace(
    /{([_&\-a-zA-Z]+)}/g,
    function (match, param) {
      // check if the argument is present
      let argItem = args?.find((item) => {
        return item?.ColumnName === param;
      });

      let valueWithTypeProcess = '';
      if (formValues[param]) {
        valueWithTypeProcess = formValues[param];

        /**
         * TODO 这个是根据columnType 做了个格式化 会出现 时分秒 后期再议
         * if(
         *           masterItem?.IsPeriodic &&
         *           masterItem?.AllowedDateGranularity?.length > 0
         *         ) {
         *           valueWithTypeProcess = valueFormatter(formValues[param], argItem?.ColumnType.toLowerCase());
         *         } else {
         *           // range
         *           if (
         *             argItem?.ColumnType.toLowerCase() === 'datetime' &&
         *             argItem?.ValuePickMode?.toLowerCase() === 'range'
         *           ) {
         *             if (Array.isArray(valueWithTypeProcess)) {
         *               valueWithTypeProcess = formValues[param].join('~');
         *             }
         *           }
         *         }
         */

        // range
        if (
          argItem?.ColumnType.toLowerCase() === 'datetime' &&
          argItem?.ValuePickMode?.toLowerCase() === 'range' &&
          !(
            masterItem?.IsPeriodic &&
            masterItem?.AllowedDateGranularity?.length > 0
          )
        ) {
          if (Array.isArray(valueWithTypeProcess)) {
            valueWithTypeProcess = formValues[param].join('~');
          }
        }

        // dictModule
        if (
          argItem?.ColumnType.toLowerCase() === 'string' &&
          argItem?.DictModule
        ) {
          // 多个值
          if (argItem?.ValuePickMode === 'MultipleValue') {
            if (Array.isArray(valueWithTypeProcess)) {
              let selectedNameItems = (
                argItem?.DictModuleGroup
                  ? dictionaryData?.[argItem?.DictModuleGroup]
                  : dictionaryData
              )?.[argItem?.DictModule]?.filter((item) => {
                return formValues[param]?.includes(item?.Code);
              });

              if (selectedNameItems?.length > 0) {
                valueWithTypeProcess = selectedNameItems
                  ?.map((item) => item?.Name)
                  ?.join(',');
              }
            }
          } else {
            valueWithTypeProcess = (
              argItem?.DictModuleGroup
                ? dictionaryData?.[argItem?.DictModuleGroup]
                : dictionaryData
            )?.[argItem?.DictModule]?.find((item) => {
              return item?.Code === formValues[param];
            })?.Name;
          }
        }
      }

      return isEmpty(formValues[param]) || isNil(formValues[param])
        ? `【${argItem?.ColumnTitle}】`
        : valueWithTypeProcess;
    },
  );
};

export const reportTitleParamProcessor = (
  hasCustomTitle: boolean,
  title: string,
) => {
  if (hasCustomTitle) {
    return title?.replaceAll(/【(.*?)】/g, '');
  }

  return title;
};

const datetimeProcessor = (value: string) => {
  if (specialDateTypeChecker(value)) {
    return specialDateTypeProcessor(value);
  }

  return value;
};

// 默认值设置 目前只设置 时间 与 院区
export const reportAddArgFormInitialValueProcessor = (
  columns: ReportArgItem[],
  searchParams: any,
  type: string,
) => {
  if (columns?.length > 0) {
    let result = {};
    columns.forEach((d) => {
      // 时间
      if (d?.ColumnType.toLowerCase() === 'datetime') {
        if (d?.ValuePickMode?.toLowerCase() === 'range') {
          result[d.ColumnName] = datetimeProcessor(
            searchParams?.[type === 'GLOBAL' ? 'dateRange' : d.ColumnName],
          );
        } else {
          result[d.ColumnName] = datetimeProcessor(
            type === 'GLOBAL'
              ? searchParams?.singleDate ||
                  searchParams?.singlesearchParams?.dateRange?.at(0)
              : searchParams?.[d.ColumnName],
          )?.at(0);
        }
      }
      // 院区
      if (
        d?.ColumnType?.toLowerCase() === 'string' &&
        d?.DictModule?.toLowerCase() === 'hospital'
      ) {
        if (d?.ValuePickMode?.toLowerCase() === 'multiplevalue') {
          result[d.ColumnName] =
            searchParams?.[type === 'GLOBAL' ? 'hospCodes' : d.ColumnName];
        } else {
          result[d.ColumnName] =
            type === 'GLOBAL'
              ? searchParams?.hospCode || searchParams?.hospCodes?.at(0)
              : searchParams?.[d.ColumnName];
        }
      }
    });
    return result;
  } else {
    return {};
  }
};
