import IcdeSelect from '@/pages/dmr/components/icde-select';
import IcdeDragTable from '@/pages/dmr/components/icde-table';
import OperationDragTable from '@/pages/dmr/components/oper-table';
import IcuDragTable from '@/pages/dmr/components/icu-table';
import PathologyIcdeDragTable from '@/pages/dmr/components/pathology-table';
import { baseDynamicComponentsMap } from '@uni/grid/src/common/dynamicComponents';
import TcmIcdeDragTable from '@/pages/dmr/components/tcm-icde-table';
import BabyIcdeDragTable from '@/pages/dmr/components/icde-table/baby';
import IcdeInsurSeparateTable from '@/pages/dmr/components/icde-insur-separate-table';

export const dynamicComponentsMap = {
  ...baseDynamicComponentsMap,
  UniIcdeDragTable: IcdeDragTable,
  UniOperationDragTable: OperationDragTable,
  UniIcuDragTable: IcuDragTable,
  UniPathologyIcdeDragTable: PathologyIcdeDragTable,
  UniTcmIcdeDragTable: TcmIcdeDragTable,
  // 诊断 select
  UniIcdeSelect: IcdeSelect,

  // 新生儿诊断表格
  UniBabyIcdeDragTable: BabyIcdeDragTable,

  // 诊断医保分离表格
  UniIcdeInsurSeparateTable: IcdeInsurSeparateTable,
};
