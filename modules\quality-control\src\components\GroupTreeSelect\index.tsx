import React from 'react';
import { TreeSelect, Divider, Space, Button, Tag } from 'antd';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { TreeSelectProps } from 'antd/lib/tree-select';
import './index.less';

// 标签自定义渲染属性
export interface CustomTagProps {
  label: React.ReactNode;
  value: any;
  disabled: boolean;
  onClose: (event?: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  closable: boolean;
}

export interface GroupTreeSelectProps {
  /**
   * 是否延迟触发onChange，仅在下拉框关闭时才触发外部onChange
   * @default false
   */
  delayedChange?: boolean;
  /**
   * 树形数据源
   */
  treeData: any[];
  /**
   * 已选中的值
   */
  value?: string[];
  /**
   * 值变化时的回调
   */
  onChange?: (value: string[]) => void;
  /**
   * 下拉框样式
   */
  style?: React.CSSProperties;
  /**
   * 下拉菜单样式
   */
  dropdownStyle?: React.CSSProperties;
  /**
   * 是否显示清除按钮
   */
  allowClear?: boolean;
  /**
   * 占位符
   */
  placeholder?: string;
  /**
   * 加载状态
   */
  loading?: boolean;
  /**
   * 是否显示搜索框
   */
  showSearch?: boolean;
  /**
   * 自定义过滤函数
   */
  filterTreeNode?: (inputValue: string, treeNode: any) => boolean;
  /**
   * 自定义标签渲染
   */
  tagRender?: (props: CustomTagProps) => React.ReactElement;
  /**
   * 最多显示多少个标签
   */
  maxTagCount?: number | 'responsive';
  /**
   * 是否显示底部快速选择按钮
   */
  showBottomButtons?: boolean;
  /**
   * 是否显示全选按钮
   */
  showAllSelectButton?: boolean;
  /**
   * 底部自定义按钮配置
   */
  quickSelectButtons?: {
    /** 按钮文字 */
    text: string;
    /** 过滤条件，针对value的过滤函数 */
    filter: (value: string) => boolean;
  }[];
  /**
   * 值和标签的分隔符
   */
  separator?: string;
  /**
   * 是否禁用
   */
  disabled?: boolean;
}

/**
 * 分组树形选择组件
 *
 * 特性：
 * 1. 永久展开所有节点
 * 2. 父节点作为分组名称
 * 3. 选中项右侧显示勾选图标
 * 4. 支持拼音搜索
 * 5. 支持"全部提示"和"全部强制"快捷按钮
 */
const GroupTreeSelect: React.FC<GroupTreeSelectProps> = ({
  treeData = [],
  value = [],
  onChange,
  style = { width: '220px' },
  dropdownStyle = { maxHeight: 400, overflow: 'auto' },
  allowClear = true,
  placeholder = '请选择',
  loading = false,
  showSearch = true,
  filterTreeNode,
  tagRender,
  showBottomButtons = true,
  showAllSelectButton = true,
  quickSelectButtons,
  separator = '|',
  disabled = false,
  maxTagCount = 'responsive',
  delayedChange = false,
}) => {
  // 默认拼音搜索过滤函数
  const defaultFilterTreeNode = (inputValue: string, treeNode: any) => {
    const title = String(treeNode?.title || '');
    return (
      title.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0 ||
      pinyinInitialSearch(title.toLowerCase(), inputValue.toLowerCase())
    );
  };

  // 用于跟踪下拉框的可见状态
  const [dropdownVisible, setDropdownVisible] = React.useState(false);

  // 用于存储内部值，实现延迟触发onChange
  const [internalValue, setInternalValue] = React.useState<string[]>(value);

  // 当外部value变化时，更新内部值
  React.useEffect(() => {
    setInternalValue(value);
  }, [value]);

  // 处理下拉框显示状态变化
  const handleDropdownVisibleChange = (open: boolean) => {
    setDropdownVisible(open);

    // 当下拉框关闭且启用了延迟触发时，调用外部onChange
    if (!open && delayedChange && onChange) {
      onChange(internalValue);
    }
  };

  // 真正处理值变化的函数（可能触发外部onChange或仅更新内部状态）
  const handleValueChange = (newValues: string[]) => {
    if (delayedChange) {
      // 延迟触发模式：仅更新内部状态
      setInternalValue(newValues);
    } else {
      // 直接触发模式：调用外部onChange
      onChange && onChange(newValues);
    }
  };

  // 处理父节点选择：将父节点转换为其所有子节点
  const handleChange = (newValue: string[] | undefined) => {
    // 处理undefined或null情况
    if (!newValue || newValue.length === 0) {
      // 当是清空操作且启用了延迟触发时，直接调用外部onChange
      if (delayedChange && internalValue.length > 0) {
        // 用户点击了清除按钮，直接调用外部onChange，不等待下拉框关闭
        onChange && onChange([]);
        setInternalValue([]);
      } else {
        // 非延迟模式或其他情况
        handleValueChange([]);
      }
      return;
    }

    const expandedValues: string[] = [];

    newValue.forEach((val) => {
      if (!val) return; // 跳过无效值

      // 检查是否是父节点值（不含分隔符）
      if (!val.includes(separator)) {
        // 是父节点，查找其所有子节点并添加
        const parent =
          Array.isArray(treeData) &&
          treeData.find((item) => item && item.value === val);
        if (parent && Array.isArray(parent.children)) {
          parent.children.forEach((child: any) => {
            if (child && child.value) {
              expandedValues.push(child.value);
            }
          });
        }
      } else {
        // 是子节点，直接添加
        expandedValues.push(val);
      }
    });

    // 去重
    const uniqueValues = Array.from(new Set(expandedValues));
    handleValueChange(uniqueValues);
  };

  // 处理树节点点击 - 专门用于父节点的全选/反选功能
  const handleTreeNodeSelect = (selectedValue: React.Key, info: any) => {
    // 直接使用info作为节点数据
    if (
      !info ||
      !info.children ||
      !Array.isArray(info.children) ||
      info.children.length === 0
    ) {
      return; // 不是父节点或没有子节点，直接返回
    }

    // 父节点值
    const nodeValue = info.value;

    // 获取所有子节点的值
    const childValues = info.children
      .map((child: any) => child.value)
      .filter(Boolean);

    if (childValues.length === 0) return;

    // 使用当前有效的选中值（延迟模式下使用internalValue，否则使用value）
    const currentSelectedValues = delayedChange ? internalValue : value;

    // 检查是否所有子节点都已选中
    const allSelected = childValues.every((childValue) =>
      currentSelectedValues.includes(childValue),
    );

    let newValues: string[];

    if (allSelected) {
      // 全选状态下点击父节点，执行反选操作
      newValues = currentSelectedValues.filter((v) => !childValues.includes(v));
    } else {
      // 非全选状态下点击父节点，执行全选操作
      newValues = Array.from(
        new Set([...currentSelectedValues, ...childValues]),
      );
    }

    handleValueChange(newValues);
  };

  // 自定义下拉菜单渲染
  const customDropdownRender = (menu: React.ReactNode) => {
    return (
      <div className="group-tree-select-dropdown">
        {menu}

        {showBottomButtons && (
          <>
            <Divider style={{ margin: '8px 0' }} />
            <Space style={{ padding: '0 8px' }}>
              {showAllSelectButton && (
                <Button
                  type="default"
                  size="small"
                  onClick={() => {
                    // 全选所有子项目
                    const allValues: string[] = [];
                    if (Array.isArray(treeData)) {
                      treeData.forEach((group) => {
                        if (
                          group &&
                          Array.isArray(group.children) &&
                          group.children.length > 0
                        ) {
                          group.children.forEach((item: any) => {
                            if (item && item.value) {
                              allValues.push(item.value);
                            }
                          });
                        }
                      });
                    }
                    handleValueChange(allValues);
                  }}
                >
                  全选
                </Button>
              )}

              {quickSelectButtons?.map((button, index) => (
                <Button
                  key={index}
                  type="default"
                  size="small"
                  onClick={() => {
                    // 获取当前有效的选中值（延迟模式下使用internalValue，否则使用value）
                    const currentSelectedValues = delayedChange
                      ? internalValue
                      : value;

                    // 根据自定义过滤函数筛选项目
                    const selectedValues: string[] = [];

                    if (
                      Array.isArray(treeData) &&
                      typeof button?.filter === 'function'
                    ) {
                      treeData.forEach((group) => {
                        if (
                          group &&
                          Array.isArray(group.children) &&
                          group.children.length > 0
                        ) {
                          group.children.forEach((item: any) => {
                            if (
                              item &&
                              item.value &&
                              button.filter(item.value)
                            ) {
                              selectedValues.push(item.value);
                            }
                          });
                        }
                      });
                    }

                    handleValueChange(selectedValues);
                  }}
                >
                  {button.text}
                </Button>
              ))}
            </Space>
          </>
        )}
      </div>
    );
  };

  // 默认标签渲染 - 符合antd标准tag样式
  const defaultTagRender = (props: CustomTagProps): React.ReactElement => {
    const { label, value, closable, onClose } = props || {};
    // 安全地处理值
    if (value === undefined || value === null) {
      return (
        <Tag closable={false} className="ant-select-selection-item">
          Invalid
        </Tag>
      );
    }

    // 从树形数据中找到对应的选项
    let itemLabel = label;
    const stringValue = String(value);
    const parts = stringValue.split(separator || '|');

    // 父节点已经在onChange中展开为子节点，这里只需处理子节点
    if (parts.length === 2) {
      const templateCode = parts[0];
      const type = parts[1];

      // 安全地查找，避免undefined错误
      const group =
        Array.isArray(treeData) &&
        treeData.find((g) => g && g.value === templateCode);
      const item =
        group &&
        group.children &&
        Array.isArray(group.children) &&
        group.children.find((c: any) => c && c.value === stringValue);

      if (item && item.parentDescription) {
        itemLabel = `${item.parentDescription}-${type}`;
      }
    }

    // 确保始终有显示内容
    const displayLabel = itemLabel || String(value);

    return (
      <Tag
        closable={!!closable}
        onClose={onClose}
        className="ant-select-selection-item"
      >
        {displayLabel}
      </Tag>
    );
  };

  return (
    <TreeSelect
      className="group-tree-select"
      style={style}
      dropdownStyle={dropdownStyle}
      treeData={treeData}
      placeholder={placeholder}
      allowClear={allowClear}
      multiple
      showSearch={showSearch}
      treeNodeFilterProp="title"
      filterTreeNode={filterTreeNode || defaultFilterTreeNode}
      showCheckedStrategy={TreeSelect.SHOW_CHILD}
      value={delayedChange ? internalValue : value}
      onChange={handleChange}
      onSelect={handleTreeNodeSelect as any}
      loading={loading}
      onDropdownVisibleChange={handleDropdownVisibleChange}
      tagRender={tagRender || defaultTagRender}
      dropdownRender={customDropdownRender}
      disabled={disabled}
      getPopupContainer={(triggerNode) => triggerNode.parentNode as HTMLElement}
      treeDefaultExpandAll
      treeExpandedKeys={treeData.map((item) => item.value)}
      treeIcon={false}
      treeLine={false}
      maxTagCount={maxTagCount}
    />
  );
};

export default GroupTreeSelect;
