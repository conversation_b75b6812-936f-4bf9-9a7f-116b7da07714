.selective-container {
  display: flex;
  flex-direction: column;

  .batch-label {
    font-size: 14px;
    color: #7d87b3;
    white-space: nowrap;
  }

  .header-content-container {
  }

  .batch-range-container {
    display: flex;
    flex-direction: row;
    background: #ffffff;
    padding: 24px;
    margin-bottom: 10px;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #e9e9e9;
  }

  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
    .ant-select-selector {
    background-color: #fff;
    color: rgba(0, 0, 0, 0.85);
  }

  .search-btn {
    width: 120px;
  }

  .selective-progress-table {
    #tanstack-table-container {
      height: 620px;
    }

    table {
      height: 100%;
    }

    .empty-container {
      height: 100%;
      justify-content: center;
    }
  }
}
