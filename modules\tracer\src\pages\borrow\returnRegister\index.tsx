import { Reducer, useEffect, useReducer, useRef } from 'react';
import { Dispatch, useDispatch, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { UniTable } from '@uni/components/src';
import {
  <PERSON>ert,
  Button,
  Card,
  Col,
  Divider,
  InputRef,
  Modal,
  Popconfirm,
  Row,
  Space,
  Tooltip,
  message,
} from 'antd';
import { useSafeState, useKeyPress } from 'ahooks';
import {
  IModalState,
  IReducer,
  ITableState,
} from '@uni/reducers/src/Interface';
import { SwagBorrowRecordItem } from '../interface';
import { columnsHandler, handleMrActionApi, isRespErr } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType } from '@/Constants';
import {
  ModalAction,
  TableAction,
  modalReducer,
  tableReducer,
  InitTableState,
  InitModalState,
} from '@uni/reducers/src';
import {
  ReturnRegisterSearchOpts,
  ReturnRegisterUnReturnColumns,
  ReturnRegisteredColumns,
} from './constants';
import PatTimeline from '@/components/PatTimeline';
import { useTimelineReq } from '@/hooks';
import { exportExcel } from '@uni/utils/src/excel-export';
import {
  FileExcelOutlined,
  DeliveredProcedureOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { exportExcelDictionaryModuleProcessor } from '@uni/components/src/table/processor/data/export';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { ProFormInstance } from '@uni/components/src/pro-form';
import './index.less';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { ActionRecordItem } from '@/pages/mrRoom/interface';
import clsx from 'clsx';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { ITableReq } from '@/Interface';

const BorrowReturnRegister = () => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  // form args
  const proFormRef = useRef<ProFormInstance>();
  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();
  const [revertRecord, setRevertRecord] = useSafeState(null);

  // 操作记录table
  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagBorrowRecordItem & ActionRecordItem>, IReducer>
  >(tableReducer, InitTableState);

  // 当前借阅人所有未归还的table 当切换借阅人时整个表也会切换
  const [UnReturnTableState, UnReturnTableDispatch] = useReducer<
    Reducer<ITableState<SwagBorrowRecordItem & ActionRecordItem>, IReducer>
  >(tableReducer, {
    ...InitTableState,
    nowBorrower: undefined,
  });

  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<SwagBorrowRecordItem[]>, IReducer>
  >(modalReducer, InitModalState);
  // modal selected table key
  const [selectedRecordKey, setSelectedRecordKey] = useSafeState([]);
  const [selectedRecordRows, setSelectedRecordRows] = useSafeState(undefined);
  // modal columns key
  const [modalColumns, setModalColumns] = useSafeState([]);
  // modal alert
  const [modalAlert, setModalAlert] = useSafeState(false);
  // 节流隐式标识
  const hiddenLoading = useRef(false);
  const barCodeRef = useRef<InputRef>(null);

  useKeyPress(
    'enter',
    () => {
      console.log('press enter only');
      if (proFormRef.current.getFieldValue('BarCode')) {
        proFormRef.current.validateFields().then((values) => {
          fetchReqActionReq();
        });
      }
    },
    {
      exactMatch: true,
      target: document.getElementById('returnRegisterForm'),
    },
  );

  // 批量查询，先查，再归还
  const searchOneReq = async (searchParam: any, needDataPush = false) => {
    if (!searchParam) return;

    // 构建请求数据 - 直接使用传入的参数对象，添加分页参数和归还状态
    const requestData = {
      ...searchParam,
      IsReturned: false,
      SkipCount: 0,
      MaxResultCount: 999999,
    };

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetList`,
          method: 'POST',
          data: requestData,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    if (!isRespErr(res)) {
      let { Items } = res?.data;
      if (!needDataPush) {
        if (res?.data?.TotalCount === 1) {
          // 单个，直接处理
          reqActionReq(
            Items?.at(0)?.BarCode,
            Items?.at(0),
            ReqActionType.return,
          );
        } else if (res?.data?.TotalCount > 1) {
          // 多条，modal提示处理
          ModalStateDispatch({
            type: ModalAction.change,
            payload: {
              visible: true,
              record: Items,
              specialData:
                typeof searchParam === 'string'
                  ? searchParam
                  : Object.values(searchParam)[0],
              actionType: undefined,
            },
          });
        } else {
          // 没查到数据
          // 重置节流标识
          hiddenLoading.current = false;
          Modal.confirm({
            title: `查无数据`,
            content: '请确认病案标识填写正确',
            onCancel: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            onOk: () => {
              proFormRef.current.getFieldsValue();
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            cancelButtonProps: { style: { display: 'none' } },
          });
        }
      } else {
        // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
        // 查询成功后，把数据插入已记录列表
        SearchTableDispatch({
          type: TableAction.dataUnshift,
          payload: {
            data: Array.isArray(Items) ? Items : [Items],
            key: 'BarCode',
          },
        });
        // 重置节流标识
        hiddenLoading.current = false;
        // focus input
        focusBarCode();
      }
    }
  };
  // 扫码枪条码，走这里
  const searchByBarCodeReq = async (params: any, needDataPush = false) => {
    if (!params) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetListByBarCode`,
          method: 'POST',
          data: {
            BarCode: params?.BarCode,
            SkipCount: 0,
            MaxResultCount: 999999,
          },
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    if (!isRespErr(res)) {
      let { data } = res;
      if (!needDataPush) {
        if (data?.length === 1) {
          // 单个，直接处理
          reqActionReq(data?.at(0)?.BarCode, data?.at(0), ReqActionType.return);
        } else if (data?.length > 1) {
          // 多条，modal提示处理
          ModalStateDispatch({
            type: ModalAction.change,
            payload: {
              visible: true,
              record: data,
              specialData: params?.BarCode,
              actionType: undefined,
            },
          });
        } else {
          // 没查到数据
          // 重置节流标识
          hiddenLoading.current = false;

          Modal.confirm({
            title: `查无数据`,
            content: '请确认病案标识填写正确',
            onOk: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            onCancel: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            cancelButtonProps: { style: { display: 'none' } },
          });
        }
      } else {
        // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
        // 根据免打标签 自动打印
        // 查询成功后，把数据插入已记录列表
        SearchTableDispatch({
          type: TableAction.dataUnshift,
          payload: {
            data: Array.isArray(data) ? data : [data],
            key: 'BarCode',
          },
        });
        // 重置节流标识
        hiddenLoading.current = false;
        // focus input
        focusBarCode();
      }
    }
  };

  // 归还
  const reqActionReq = async (
    barCode: string,
    record,
    reqType: ReqActionType,
  ) => {
    if (!barCode || !reqType) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `Borrowing/${reqType}`,
        requestParams: {
          url: `Api/Mr/Borrowing/${reqType}`,
          method: 'POST',
          data: { BarCode: barCode },
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    // 不管结果 重置节流标识
    hiddenLoading.current = false;

    if (!isRespErr(res)) {
      if (reqType === ReqActionType.return) {
        // 把modal关闭
        ModalStateDispatch({
          type: ModalAction.init,
        });
        setSelectedRecordKey([]);
        setSelectedRecordRows(undefined);
        setModalAlert(false);

        // StatusCode 黄神的单独处理
        let result = handleMrActionApi(res.data);
        result?.isCorrect
          ? message.success('归还成功')
          : message.error(result?.errMsg?.join('。/n'));

        if (result?.data?.length > 0) {
          // 获取未归还list
          getUnReturnListReq(result?.data?.at(0));

          // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
          // 把数据插入已记录列表
          SearchTableDispatch({
            type: TableAction.dataUnshiftUniq,
            payload: {
              data: result?.data?.at(0),
              key: 'BarCode',
              overWriteBy: {
                key: 'isCorrect',
                value: true,
              },
            },
          });
        }

        if (result?.errType === '404') {
          // 404 ERROR
          Modal.error({
            title: result?.errMsg?.join('。/n'),
            onOk: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
            onCancel: () => {
              proFormRef.current.resetFields(['BarCode']);
              focusBarCode();
            },
          });
        } else {
          // 其他的 barCode自动清除
          proFormRef.current.resetFields(['BarCode']);
          // focus input
          focusBarCode();
        }
      } else {
        // 撤销预留 目前并没提供撤销按钮
        // revert
        SearchTableDispatch({
          type: TableAction.dataFilt,
          payload: {
            key: 'BarCode',
            value: record.BarCode,
          },
        });
        setRevertRecord(record);

        message.success('撤销成功');
      }
    }
  };

  // 新增一个未归还table 在归还的同时（确保唯一）去获取该借阅人的全部列表
  const getUnReturnListReq = async (record) => {
    let res: ITableReq | string = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: 'BorrowRecord',
        requestParams: [
          {
            url: 'Api/Mr/BorrowRecord/GetReturnList',
            method: 'POST',
            data: {
              IsReturned: false,
              Borrower: record?.Borrower,
              SkipCount: 0,
              MaxResultCount: 99999,
            },
            dataType: 'mr',
          },
        ],
      },
    });
    if (typeof res !== 'string') {
      UnReturnTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res?.datas?.at(0)?.data ?? [],
        },
      });
      UnReturnTableDispatch({
        type: TableAction.summaryChange,
        payload: {
          summary: record?.Borrower, // summary 做借阅人标识符，懒得做修改
        },
      });
    }
  };

  // columns 处理，主要用于处理options
  // const SearchedTableColumnsSolver = useMemo(() => {
  //   return SearchTable.columns ? columnsHandler(SearchTable.columns) : [];
  // }, [SearchTable.columns]);

  // 处理撤销后的操作(因为useMemo的缘故，得effect处理...蛮蠢的，但是又懒得改useMemo的写法)
  useEffect(() => {
    // 时间轴如果匹配则值空
    if (SearchTable?.clkItem?.BarCode === revertRecord?.BarCode) {
      SearchTableDispatch({
        type: TableAction.clkChange,
        payload: {
          clkItem: null,
        },
      });
      setParams(null);
    }
  }, [revertRecord]);

  // columns处理
  useEffect(() => {
    if (columnsList?.['BorrowRecord/GetReturnList']) {
      SearchTableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: columnsHandler(
            tableColumnBaseProcessor(
              ReturnRegisteredColumns,
              columnsList['BorrowRecord/GetReturnList'],
            ),
          ),
        },
      });

      UnReturnTableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: columnsHandler(
            tableColumnBaseProcessor(
              ReturnRegisterUnReturnColumns,
              columnsList['BorrowRecord/GetReturnList'],
            ),
            {
              dataIndex: 'option',
              title: '操作',
              visible: true,
              width: 60,
              align: 'center',
              fixed: 'right',
              render: (text, record: SwagBorrowRecordItem) => [
                <Tooltip title="归还" key="btn_return">
                  <DeliveredProcedureOutlined
                    className="icon_blue-color"
                    onClick={(e) => {
                      e.stopPropagation();
                      reqActionReq(
                        record.BarCode,
                        record,
                        ReqActionType.return,
                      );
                    }}
                  />
                </Tooltip>,
              ],
            },
          ),
        },
      });

      setModalColumns(
        tableColumnBaseProcessor(
          ReturnRegisteredColumns,
          columnsList['BorrowRecord/GetReturnList'],
        ),
      );
    }
  }, [columnsList?.['BorrowRecord/GetReturnList']]);

  // 点击操作
  const fetchReqActionReq = () => {
    if (hiddenLoading.current) return;
    hiddenLoading.current = true;

    proFormRef.current
      .validateFields()
      .then((values) => {
        if (values.SignType.value === 'BarCode') {
          searchByBarCodeReq(values, false);
        } else {
          // 对于非BarCode类型，创建一个以SignType.value为键，BarCode为值的对象
          const searchParam = {
            [values.SignType.value]: values.BarCode,
          };
          searchOneReq(searchParam, false);
        }
      })
      .catch((err) => {
        hiddenLoading.current = false;

        message.error('请确认已填写归还信息！');
      });
  };

  const focusBarCode = () => {
    // 定位
    setTimeout(() => {
      barCodeRef.current?.focus({ cursor: 'end' });
    }, 20);
  };

  return (
    <>
      <Row gutter={[8, 8]}>
        <Col xxl={7} xl={8}>
          <Card
            title={<Space>归还信息</Space>}
            style={{ marginBottom: '15px' }}
          >
            <ProFormContainer
              formRef={proFormRef}
              labelCol={{ flex: '100px' }}
              wrapperCol={{ flex: 'auto' }}
              searchOpts={ReturnRegisterSearchOpts(barCodeRef)}
              id="returnRegisterForm"
              className="return_register_form"
              submitter={{
                render: (props, doms) => {
                  return [
                    <Button
                      type="primary"
                      style={{
                        width: 'calc(100% - 100px)',
                        float: 'right',
                        marginTop: '8px',
                      }}
                      key="submit"
                      onClick={() => {
                        fetchReqActionReq();
                      }}
                    >
                      归还(Enter)
                    </Button>,
                  ];
                },
              }}
            />
          </Card>
          <PatTimeline
            item={SearchTable?.clkItem}
            loading={loadings['TraceRecord/GetActions']}
            timelineItems={timelineItems}
          />
        </Col>
        <Col xxl={17} xl={16}>
          <Card
            style={{ marginBottom: '8px' }}
            title="病案归还登记"
            extra={
              <Space>
                <Divider type="vertical" />
                <Popconfirm
                  title="导出时会将错误的记录过滤掉"
                  onConfirm={(e) => {
                    let exportColumns = SearchTable.columns?.filter(
                      (columnItem: any) =>
                        columnItem?.className?.indexOf('exportable') > -1 &&
                        columnItem?.valueType !== 'option' &&
                        columnItem?.dataIndex !== 'operation',
                    );
                    exportExcel(
                      exportColumns,
                      exportExcelDictionaryModuleProcessor(
                        exportColumns,
                        _.cloneDeep(
                          SearchTable.data?.filter((d) => d.isCorrect),
                        ),
                      ),
                      `病案单份签收列表__${dayjs().format('YYYY-MM-DD')}`,
                      [],
                    );
                  }}
                  disabled={
                    SearchTable.data?.filter((d) => d.isCorrect)?.length < 1
                  }
                >
                  <Tooltip title="导出Excel">
                    <Button
                      type="text"
                      shape="circle"
                      disabled={
                        SearchTable.data?.filter((d) => d.isCorrect)?.length < 1
                      }
                      icon={<FileExcelOutlined />}
                    />
                  </Tooltip>
                </Popconfirm>
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl: 'Api/Mr/BorrowRecord/GetReturnList',
                    onTableRowSaveSuccess: (columns) => {
                      // 这个columns 存到dva
                      dispatch({
                        type: 'global/saveColumns',
                        payload: {
                          name: 'BorrowRecord/GetReturnList',
                          value: columns,
                        },
                      });
                    },
                  }}
                />
              </Space>
            }
          >
            <UniTable
              id="borrow_return_record_searched"
              rowKey="Id"
              showSorterTooltip={false}
              loading={
                loadings['BorrowRecord/GetReturnList'] ||
                loadings['TraceRecord/GetList'] ||
                loadings[`Borrowing/${ReqActionType.return}`] ||
                false
              }
              columns={SearchTable.columns} // columnsHandler
              dataSource={SearchTable.data}
              dictionaryData={dictData}
              scroll={{ x: 'max-content' }}
              rowClassName={(record, index) => {
                let classname = [];
                // 互斥
                if (!record?.isCorrect) {
                  classname.push('row-error');
                } else if (index === 0) {
                  return 'row-first';
                }
                if (record?.Id === SearchTable.clkItem?.Id) {
                  classname.push('row-selected');
                }

                return classname.length > 0 ? clsx(classname) : null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (SearchTable.clkItem?.BarCode !== record?.BarCode) {
                      SearchTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
            />
          </Card>
          <Card title="未归还列表">
            <UniTable
              id="borrower_unreturned_list"
              rowKey="Id"
              showSorterTooltip={false}
              loading={
                loadings['BorrowRecord/GetReturnList'] ||
                loadings['TraceRecord/GetList'] ||
                loadings[`Borrowing/${ReqActionType.return}`] ||
                false
              }
              columns={UnReturnTableState.columns} // columnsHandler
              dataSource={UnReturnTableState.data}
              dictionaryData={dictData}
              rowClassName={(record, index) => {
                let classname = [];
                // *与上面的table共用一个timeline
                if (record?.Id === SearchTable.clkItem?.Id) {
                  classname.push('row-selected');
                }

                return classname.length > 0 ? clsx(classname) : null;
              }}
              onRow={(record) => {
                return {
                  onClick: (event) => {
                    if (SearchTable.clkItem?.BarCode !== record?.BarCode) {
                      SearchTableDispatch({
                        type: TableAction.clkChange,
                        payload: {
                          clkItem: record,
                        },
                      });
                      setParams({ barCode: record.BarCode });
                    }
                  },
                };
              }}
              pagination={false}
              scroll={{ x: 'max-content' }}
            />
          </Card>
        </Col>
      </Row>
      <Modal
        title="确认病案"
        open={ModalState.visible}
        width={900}
        onOk={(e) => {
          console.log(
            ModalState.record,
            selectedRecordKey,
            ModalState.record.findIndex(
              (d) => d.BarCode === selectedRecordKey?.at(0),
            ),
          );
          if (
            ModalState.record.findIndex(
              (d) => d.BarCode === selectedRecordKey?.at(0),
            ) > -1
          ) {
            reqActionReq(
              selectedRecordKey?.at(0),
              selectedRecordRows,
              ReqActionType.return,
            );
          } else {
            // 没选
            setModalAlert(true);
          }
        }}
        okButtonProps={{
          loading: loadings[`Tracing/${ReqActionType.return}`],
        }}
        onCancel={(e) => {
          // 重置节流标识
          hiddenLoading.current = false;
          focusBarCode();

          ModalStateDispatch({
            type: ModalAction.init,
          });
          setSelectedRecordKey([]);
          setSelectedRecordRows(undefined);
          setModalAlert(false);
        }}
      >
        <UniTable
          id="multi_record_check"
          rowKey="BarCode"
          showSorterTooltip={false}
          loading={
            loadings['BorrowRecord/GetReturnList'] ||
            loadings['TraceRecord/GetList'] ||
            loadings[`Borrowing/${ReqActionType.return}`] ||
            false
          }
          columns={modalColumns} // columnsHandler
          dataSource={ModalState.record}
          scroll={{ x: 'max-content' }}
          tableAlertRender={() => {
            return modalAlert ? (
              <Alert
                message="请选择一个病案"
                description="请选择一个病案再点击确认，如果没有查询到目标病案请确认信息输入正确"
                type="error"
                closable
                onClose={() => {
                  setModalAlert(false);
                }}
              />
            ) : (
              false
            );
          }}
          tableAlertOptionRender={false}
          rowSelection={{
            alwaysShowAlert: true,
            type: 'radio',
            selectedRowKeys: selectedRecordKey,
            onChange: (
              selectedRowKeys: React.Key[],
              selectedRows: SwagBorrowRecordItem[],
            ) => {
              setSelectedRecordKey(selectedRowKeys);
              setSelectedRecordRows(selectedRows?.at(0));
              setModalAlert(false);
            },
          }}
          onRow={(record) => {
            return {
              onClick: (event) => {
                setSelectedRecordKey([record?.BarCode]);
                setSelectedRecordRows(record);
              },
            };
          }}
        />
      </Modal>
    </>
  );
};

export default BorrowReturnRegister;
