import { Badge, Empty, message, Spin, Tabs } from 'antd';
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import './index.less';
import { DoubleLeftOutlined } from '@ant-design/icons';
import DetailComment, {
  DetailCommentProps,
} from '@/pages/review/components/score-comment/comment/detail-comment';
import { useRequest } from 'umi';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import { RespVO } from '@uni/commons/src/interfaces';
import {
  CommentCategoryItem,
  CommentDetailItem,
  RuleItem,
} from '@/pages/review/components/score-comment/score/interfaces';
import {
  scoreDataSourceProcessor,
  scoreDataSourceTreeProcessor,
  scoreLabelGenerator,
} from '@/pages/review/components/score-comment/score/processor';
import { isEmptyValues } from '@uni/utils/src/utils';
import { uniCommonService } from '@uni/services/src';
import GroupComment from '@/pages/review/components/score-comment/comment/group-comment';
import {
  addDmrHasCommentStyle,
  canCommentAdd,
  deleteDmrHasCommentStyle,
} from '@/pages/review/components/score-comment/comment/utils';
import { Emitter } from '@uni/utils/src/emitter';
import { useAsyncEffect, useUpdateEffect } from 'ahooks';
import {
  BatchMasterReviewSetting,
  BatchMasterSysItem,
} from '@/pages/review/interface';
import RuleScoreComment from '@/pages/review/components/score-comment/comment/rule-score-comment';
import useStateCallback from '@uni/utils/src/useStateCallback';

interface RightItemRuleCommentContainerProps {
  sysMasterItem?: BatchMasterSysItem;

  extraDmrContainer?: any;

  taskItemRef: any;

  drawerStatus?: boolean;

  commentTemplateId?: string;

  dmrGridContainerRef: any;
  detailCommentRef?: any;
  svgLinesContainerRef: any;
  taskId?: string;
  taskStatus?: string;
  tableReadonly?: boolean;

  commentCategories?: CommentCategoryItem[];
  commentDetails?: CommentDetailItem[];

  onIssueUpdate?: () => void;
}

const RightItemRuleCommentContainer = (
  props: RightItemRuleCommentContainerProps,
) => {
  const [containerHeight, setContainerHeight] = useState(0);

  const [commentCategories, setCommentCategories] = useState<
    CommentCategoryItem[]
  >([]);

  const [commentDetails, setCommentDetails] = useState<CommentDetailItem[]>([]);

  const [detailedCommentDetails, setDetailedCommentDetails] = useState<
    CommentDetailItem[]
  >([]);
  const [groupedCommentDetails, setGroupedCommentDetails] = useState<
    CommentDetailItem[]
  >([]);

  const [activeKey, setActiveKey] = useStateCallback('COMMENT');

  const [ruleDetails, setRuleDetails] = useState<RuleItem[]>([]);

  const detailedCommentRef = useRef(null);
  const groupedCommentRef = useRef(null);

  const detailedCommentLabelRef = useRef(null);

  const commentRefMap = {
    COMMENT: detailedCommentRef,
    GROUP_COMMENT: groupedCommentRef,
  };

  useEffect(() => {
    scoreTemplateInfoReq();
  }, []);

  const { loading: scoreTemplateInfoLoading, run: scoreTemplateInfoReq } =
    useRequest(
      () => {
        let data = {
          templateId: props?.commentTemplateId,
        };

        return uniCommonService(
          'Api/Dmr/DmrCardQualityExamine/GetRuleWithScores',
          {
            method: 'POST',
            requestType: 'json',
            data: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<RuleItem[]>) => {
          console.log('GetRuleWithScores', response);
          setRuleDetails(
            response?.data?.map((item) => {
              item['scoreLabel'] = scoreLabelGenerator(item)?.toString();

              return item;
            }),
          );
        },
      },
    );

  React.useImperativeHandle(props?.detailCommentRef, () => {
    return {
      getComments: () => {
        return commentRefMap?.[activeKey]?.current?.getComments();
      },
      scrollAlongWithDmr: (closestElement) => {
        commentRefMap?.[activeKey]?.current?.scrollAlongWithDmr(closestElement);
      },
      addComment: (data: any) => {
        setActiveKey('COMMENT');
        props?.svgLinesContainerRef?.current?.updatePolyLine();
        commentRefMap?.['COMMENT']?.current?.addComment(data);
      },
      switchTab: (activeKey: string) => {
        setActiveKey(activeKey);
      },
    };
  });

  useAsyncEffect(async () => {
    if (!isEmptyValues(props?.commentTemplateId)) {
      await initializeTableData();
    }
  }, [props?.commentTemplateId, props?.taskId]);

  useUpdateEffect(() => {
    props?.svgLinesContainerRef?.current?.updatePolyLine();
  }, [activeKey]);

  const initializeTableData = async () => {
    await commentCategoryInfoReq();
    await commentDetailReq();
  };

  useEffect(() => {
    (global?.window as any)?.eventEmitter?.on(
      'DMR_COMMENT_NOTIFY_HEIGHT',
      () => {
        setContainerHeight(
          props?.dmrGridContainerRef?.current?.clientHeight + 60,
        );
      },
    );

    return () => {
      (global?.window as any)?.eventEmitter?.off('DMR_COMMENT_NOTIFY_HEIGHT');
    };
  }, []);

  const { loading: commentCategoryInfoLoading, run: commentCategoryInfoReq } =
    useRequest(
      () => {
        let data = {
          templateId: props?.commentTemplateId,
        };

        return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetCategorys', {
          method: 'POST',
          requestType: 'json',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<CommentCategoryItem[]>) => {
          setCommentCategories(response?.data);
        },
      },
    );

  const { loading: commentDetailLoading, run: commentDetailReq } = useRequest(
    () => {
      let data = {
        taskId: props?.taskId,
      };

      return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetDetails', {
        method: 'POST',
        requestType: 'json',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<CommentDetailItem[]>) => {
        setCommentDetails(response?.data ?? []);
        setDetailedCommentDetails(
          response?.data?.filter(
            (commentDetailItem) =>
              !isEmptyValues(commentDetailItem?.ColumnName),
          ) ?? [],
        );
        setGroupedCommentDetails(
          response?.data?.filter((commentDetailItem) =>
            isEmptyValues(commentDetailItem?.ColumnName),
          ) ?? [],
        );
      },
    },
  );

  const onTabKeyChange = (key: string) => {
    if (key === 'COMMENT') {
      // 标记
      commentDetails
        ?.filter(
          (commentDetailItem) => !isEmptyValues(commentDetailItem?.ColumnName),
        )
        ?.forEach((commentItem: any) => {
          addDmrHasCommentStyle({ formKey: commentItem?.ColumnName });
        });

      setTimeout(() => {
        props?.svgLinesContainerRef?.current?.updatePolyLine();
      }, 600);
    }

    if (key === 'GROUP_COMMENT') {
      // 反标记
      commentDetails
        ?.filter(
          (commentDetailItem) => !isEmptyValues(commentDetailItem?.ColumnName),
        )
        ?.forEach((commentItem: any) => {
          deleteDmrHasCommentStyle(commentItem?.ColumnName);
        });
    }
  };

  return (
    <div className={'right-item-comment-container'}>
      <Tabs
        activeKey={activeKey}
        onChange={(activeKey) => {
          setActiveKey(activeKey);
          setTimeout(() => {
            Emitter.emit('DMR_LINE_STATUS', activeKey === 'COMMENT');
          }, 200);
          onTabKeyChange(activeKey);
        }}
      >
        <Tabs.TabPane
          tab={
            <DetailedCommentTabLabel containerRef={detailedCommentLabelRef} />
          }
          key="COMMENT"
        >
          <div
            id={'separator-comment-container'}
            className={'separator-comment-container'}
          >
            <RuleScoreComment
              taskId={props?.taskId}
              taskItemRef={props?.taskItemRef}
              sysMasterItem={props?.sysMasterItem}
              detailCommentRef={detailedCommentRef}
              dmrGridContainerRef={props?.dmrGridContainerRef}
              svgLinesContainerRef={props?.svgLinesContainerRef}
              commentRuleDetails={ruleDetails}
              commentDetails={detailedCommentDetails}
              onCommentsUpdate={(comments: any[]) => {
                detailedCommentLabelRef?.current?.setCommentCount(
                  comments.length,
                );
              }}
              tableReadonly={props?.tableReadonly}
              taskStatus={props?.taskStatus}
              onDmrContainerReady={() => {
                // let dmrContainerHeight = document?.getElementById('dmr-content-grid-layout')?.clientHeight ?? 0;
                // setContainerHeight(dmrContainerHeight + 60);
              }}
              onIssueUpdate={props?.onIssueUpdate}
            />
          </div>
        </Tabs.TabPane>

        <Tabs.TabPane
          tab={'分组质控'}
          key="DMR_RIGHT_CONTAINER"
          forceRender={true}
        >
          <div id={'dmr-examine-check-container'}></div>
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
};

interface DetailedCommentTabLabelProps {
  containerRef: any;
}

const DetailedCommentTabLabel = (props: DetailedCommentTabLabelProps) => {
  const [commentCount, setCommentCount] = useState(0);

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      setCommentCount: (count) => {
        setCommentCount(count);
      },
    };
  });

  return (
    <div className={'flex-row-center'}>
      <span>明细批注</span>
      <Badge
        count={commentCount}
        size={'default'}
        style={{
          backgroundColor: '#52C41A',
          marginLeft: 5,
        }}
      />
    </div>
  );
};

export default RightItemRuleCommentContainer;
