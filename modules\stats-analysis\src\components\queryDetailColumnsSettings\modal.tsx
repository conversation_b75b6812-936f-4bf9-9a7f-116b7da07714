import DetailColumnSettingContent from './index';
import { columnSettingColumnsProcessor, columnsTreeProcessor } from './utils';
import { Modal, Form } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { Emitter } from '@uni/utils/src/emitter';
import { DetailColumnSettingContentConstants } from './constants';

export interface IDetailColumnsModalProps {
  type: string;
  detailColumns: any[]; // columns 默认值
  columnState: any; // columnState
  record?: any;
  modalTitle?: string;
  onClose?: () => void;
  onOk?: (selectedItems: any[]) => void;
  extraData?: any;
  columnTreeContainerRef?: any;
}

const DetailColumnsSettingModal = ({
  type,
  detailColumns,
  columnState,
  record,
  modalTitle,
  onOk,
  onClose,
}: IDetailColumnsModalProps) => {
  const [
    QueryDetailColumnSettingModalVisible,
    setQueryDetailColumnSettingModalVisible,
  ] = useState(false);

  const [
    QueryDetailColumnSettingModalExtraData,
    setQueryDetailColumnSettingModalExtraData,
  ] = useState<any>({});

  const columnTreeContainerRef = useRef(null);

  const [form] = Form.useForm();

  const columnMapProcessor = (selectedItems, type) => {
    let currentColumnMap = Object.assign({}, columnState);
    Object.keys(currentColumnMap)?.forEach((item) => {
      currentColumnMap[item]['show'] = false;
    });
    selectedItems?.forEach((item) => {
      if (item?.key) {
        if (currentColumnMap?.[item?.key]) {
          currentColumnMap[item?.key]['show'] = true;
          currentColumnMap[item?.key]['order'] = item?.order ?? 0;
          currentColumnMap[item?.key]['textOverflowType'] =
            item?.textOverflowType ?? 'none';
        }
      }

      if (item?.customTitle) {
        if (item?.title !== item?.customTitle) {
          Emitter.emit(
            `${DetailColumnSettingContentConstants.COLUMN_SETTING_TITLE_EDIT}_${type}`,
            {
              columnKey: item?.key,
              title: item?.customTitle,
            },
          );
        }
      }
    });

    //  回调给外部
    onOk && onOk(selectedItems);
  };

  useEffect(() => {
    Emitter.on(
      `${DetailColumnSettingContentConstants.MODAL_OPEN}_${type}`,
      (data) => {
        setQueryDetailColumnSettingModalVisible(data?.status);
        setQueryDetailColumnSettingModalExtraData(data?.extraData);
      },
    );

    Emitter.on(
      `${DetailColumnSettingContentConstants.MODAL_CLOSE}_${type}`,
      () => {
        setQueryDetailColumnSettingModalVisible(false);
      },
    );

    return () => {
      Emitter.off(`${DetailColumnSettingContentConstants.MODAL_OPEN}_${type}`);
      Emitter.off(`${DetailColumnSettingContentConstants.MODAL_CLOSE}_${type}`);
    };
  }, [type]);

  return (
    <Modal
      title="明细列设置"
      width={1600}
      open={QueryDetailColumnSettingModalVisible}
      className="detail-columns-setting-container"
      destroyOnClose={true}
      closable={true}
      onOk={() => {
        columnMapProcessor(
          form.getFieldValue('selectedItems') ??
            columnTreeContainerRef?.current?.getSelectedItems(),
          type,
        );
      }}
      onCancel={() => {
        setQueryDetailColumnSettingModalVisible(false);
      }}
    >
      <Form form={form} preserve={false}>
        <Form.Item hidden={true} name="selectItems" />
        <Form.Item hidden={true} name="columnMap" />
        <DetailColumnSettingContent
          nextGeneration={false}
          columns={columnSettingColumnsProcessor(detailColumns, columnState)}
          columnsMap={columnState}
          columnTreeContainerRef={columnTreeContainerRef}
          extraData={QueryDetailColumnSettingModalExtraData}
          treeData={columnsTreeProcessor(
            columnSettingColumnsProcessor(detailColumns, columnState),
            columnState,
          )}
        />
      </Form>
    </Modal>
  );
};

export default DetailColumnsSettingModal;
