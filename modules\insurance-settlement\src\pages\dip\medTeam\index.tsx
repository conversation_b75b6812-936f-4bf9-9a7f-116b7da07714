import { <PERSON>, Col, Row, Divider, But<PERSON>, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import CardEchart from '@uni/components/src/cardEchart';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  GrpDefaultOpts,
  GrpQuadrantAxisOpts,
  AdrgQuadrantAxisOpts,
  AdrgDefaultOpts,
} from '@/pages/dip/optsConstants';
import { SettleCompStatsSelectedTrendsLineOption } from '@/pages/dip/chart.opts';
import {
  DeptTotalStatsColumns,
  SettleCompStatsByMedTeamColumns,
  TabCommonItems,
} from '@/pages/dip/constants';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import Stats from '@/components/stats';
import FeeCompositionAndAnalysis from '@/components/feeCompositionAndAnalysis';
import GradientChartAndTable from '@/components/gradientChartAndTable';
import GradientChartAndTableAndPie from '@/components/gradientChartAndTableAndPie';
import IconBtn from '@uni/components/src/iconBtn';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '@/pages/dip/components/drawerCardInfo';
import { SettleCompStatsByGrpColumns } from '@/pages/dip/constants';

const DipMedTeamAnalysis = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, MedTeams, insurType } = globalState?.searchParams;
  const [requestParams, setRequestParams] = useState<any>({});
  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // 统计指标 选中项
  const [selectedStatItem, setSelectedStatItem] = useState({
    contentData: 'Profit',
    dataType: 'Currency',
    title: '盈亏',
  });

  // fetch 趋势 Columns
  const {
    data: settleCompStatsTrendColumnsData,
    loading: getSettleCompStatsTrendColumnsLoading,
    run: getSettleCompStatsTrendColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(
        `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsTrend`,
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      ),
    {
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  // fetch 趋势 data
  const {
    data: settleCompStatsTrendData,
    loading: getSettleCompStatsTrendLoading,
    run: getSettleCompStatsTrendReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsTrend`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  useEffect(() => {
    getSettleCompStatsTrendColumnsReq();
  }, []);

  // setRequestParams 调接口前入参数据处理
  useEffect(() => {
    if (
      (globalState?.searchParams &&
        globalState?.searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(requestParams) &&
        globalState?.searchParams?.dateRange?.length)
    ) {
      if (dateRange?.length) {
        let tableParams = {
          Sdate: dateRange?.at(0),
          Edate: dateRange?.at(1),
          HospCode: hospCodes,
          MedTeams,
          insurType,
        };
        setRequestParams(tableParams);
      }
    }
  }, [globalState?.searchParams]);

  // 调接口
  useEffect(() => {
    if (
      Object.keys(requestParams)?.length &&
      activeKey === TabCommonItems.statistic.key
    ) {
      getSettleCompStatsTrendReq(requestParams);
    }
  }, [requestParams, activeKey]);

  // stat click 由Component Stats传入
  useEffect(() => {
    Emitter.on(EventConstant.STAT_ON_CLICK_EMITTER, (record) => {
      setSelectedStatItem(record);
    });
    return () => {
      Emitter.off(EventConstant.STAT_ON_CLICK_EMITTER);
    };
  }, [selectedStatItem]);

  let tabItems = [
    {
      key: TabCommonItems.statistic.key,
      label: TabCommonItems.statistic.title,
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={12} xl={11}>
            <Row gutter={[16, 16]}>
              <Stats
                level="medTeam"
                api={`Api/FundSupervise/LatestDipSettleStats/SettleCompStatsOfMedTeam`}
                columns={DeptTotalStatsColumns} // 医疗组要展示的统计同科室
                type="col-xl-6"
                tabKey={activeKey}
                outerTableParams={requestParams}
                onClickEmitter={EventConstant.STAT_ON_CLICK_EMITTER}
              />
            </Row>
          </Col>

          <Col xs={24} sm={24} md={24} lg={12} xl={13}>
            <CardEchart
              title={<>医疗组月度变化趋势</>}
              height={430}
              dictData={globalState.dictData}
              elementId="Trend"
              loading={getSettleCompStatsTrendLoading}
              options={
                (settleCompStatsTrendData !== 'apiErr' &&
                  settleCompStatsTrendData &&
                  SettleCompStatsSelectedTrendsLineOption(
                    settleCompStatsTrendData,
                    'MonthDate',
                    selectedStatItem,
                  )) ||
                {}
              }
              needExport={true}
              exportTitle={'医疗组月度变化趋势'}
              exportData={settleCompStatsTrendData}
              exportColumns={settleCompStatsTrendColumnsData}
              needModalDetails={true}
              onRefresh={() => {
                getSettleCompStatsTrendReq(requestParams);
              }}
            ></CardEchart>
          </Col>
        </Row>
      ),
    },
    {
      key: TabCommonItems.feeAnalysis.key,
      label: TabCommonItems.feeAnalysis.title,
      children: (
        <FeeCompositionAndAnalysis
          requestParams={requestParams}
          api={`Api/FundSupervise/LatestDipSettleStats/FeeChargeDistribution`}
          tabKey={activeKey}
        />
      ),
    },
    {
      key: TabCommonItems.drgAnalysis.key,
      label: TabCommonItems.drgAnalysis.title,
      children: (
        <Row gutter={[16, 16]}>
          <GradientChartAndTableAndPie
            args={{
              level: 'medTeam',
              type: 'grp',
              title: '病组效率',
              category: 'ChsDrgName',
              columns: [
                {
                  dataIndex: 'operation',
                  visible: true,
                  width: 40,
                  align: 'center',
                  order: 1,
                  fixed: 'left',
                  title: '',
                  render: (node, record, index) => {
                    return (
                      <IconBtn
                        type="details"
                        onClick={(e) => {
                          e.stopPropagation();
                          Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                            id: 'dip-clidept-settle-stats-by-drp', // 要匹配到对应的DetailTableModal id
                            title: `${record?.ChsDrgName}`,
                            args: {
                              ...requestParams,
                              VersionedChsDrgCodes: record?.VersionedChsDrgCode
                                ? [record?.VersionedChsDrgCode || undefined]
                                : [],
                            },
                            type: 'dip',
                            detailsUrl:
                              'FundSupervise/LatestDipSettleStats/SettleDetails',
                            dictData: globalState?.dictData, // 传入
                          });
                        }}
                      />
                    );
                  },
                },
                ...SettleCompStatsByGrpColumns,
              ],
              clickable: true,
              detailsTitle: '病组分布',
              axisOpts: GrpQuadrantAxisOpts,
              defaultAxisOpt: GrpDefaultOpts,
              api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByGrp`,
            }}
            requestParams={requestParams}
            tabKey={activeKey}
          />
        </Row>
      ),
    },
    {
      key: TabCommonItems.deptAnalysis.key,
      label: '医疗组对比分析',
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTable
                args={{
                  clickable: false,
                  cols: 'col-xl-24',
                  title: '医疗组对比分析',
                  category: 'MedTeamName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      order: 1,
                      fixed: 'left',
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                id: 'drg-med-team-settle-stats-by-med-team', // 要匹配到对应的DetailTableModal id
                                title: record?.MedTeamName,
                                args: {
                                  ...requestParams,
                                  MedTeams: record?.MedTeam
                                    ? [record?.MedTeam]
                                    : [''],
                                },
                                type: 'dip',
                                detailsUrl:
                                  'FundSupervise/LatestDipSettleStats/SettleDetails',
                                dictData: globalState?.dictData, // 传入
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByMedTeamColumns,
                  ],
                  api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByMedTeam`,
                  level: 'medTeam',
                }}
                noChart={true}
                requestParams={requestParams}
              />
            </Col>
          </Row>
        </>
      ),
    },
  ];

  return (
    <Card>
      <Tabs
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={globalState.dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'dip',
          });
        }}
      />
      <DrawerCardInfo
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </Card>
  );
};

export default DipMedTeamAnalysis;
