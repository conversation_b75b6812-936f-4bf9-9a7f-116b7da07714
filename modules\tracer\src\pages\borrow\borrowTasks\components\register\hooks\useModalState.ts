import { useReducer, Reducer } from 'react';
import { IModalState, IReducer } from '@uni/reducers/src/Interface';
import { modalReducer, InitModalState, ModalAction } from '@uni/reducers/src';
import { useSafeState } from 'ahooks';

/**
 * 处理弹窗状态和选中记录的自定义Hook
 */
export const useModalState = <T>() => {
  // 弹窗状态管理
  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<T[]>, IReducer>
  >(modalReducer, {
    ...InitModalState,
    specialData: undefined,
  });

  // modal selected table key
  const [selectedRecordKey, setSelectedRecordKey] = useSafeState([]);
  const [selectedRecordRows, setSelectedRecordRows] = useSafeState(undefined);
  // modal alert
  const [modalAlert, setModalAlert] = useSafeState(false);

  /**
   * 重置模态框状态
   */
  const resetModalState = () => {
    ModalStateDispatch({
      type: ModalAction.init,
    });
    setSelectedRecordKey([]);
    setSelectedRecordRows(undefined);
    setModalAlert(false);
  };

  /**
   * 显示模态框并设置数据
   */
  const showModal = (record: T[], specialData: any) => {
    ModalStateDispatch({
      type: ModalAction.change,
      payload: {
        visible: true,
        record,
        specialData,
        actionType: undefined,
      },
    });
  };

  /**
   * 选择记录行
   */
  const selectRecord = (recordKey: string, record: T) => {
    setSelectedRecordKey([recordKey]);
    setSelectedRecordRows(record);
    setModalAlert(false);
  };

  return {
    ModalState,
    ModalStateDispatch,
    selectedRecordKey,
    setSelectedRecordKey,
    selectedRecordRows,
    setSelectedRecordRows,
    modalAlert,
    setModalAlert,
    resetModalState,
    showModal,
    selectRecord,
  };
};
