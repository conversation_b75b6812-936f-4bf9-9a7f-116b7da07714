import React from 'react';
// @ts-ignore - 忽略TypeScript错误，项目中其他组件也是这样使用的
import { useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { Button, message, Tooltip } from 'antd';
import { PrinterOutlined } from '@ant-design/icons';
import type { ButtonProps } from 'antd';

/**
 * 处理PDF打印的核心逻辑，可在组件外部使用
 * @param response API响应数据
 */
export const handlePdfPrint = (response: any) => {
  if (response?.code === 0 && response?.statusCode === 200) {
    console.log(`API响应成功`, response);
    let { response: res } = response;

    // 获取Content-Type
    const contentType = res.headers.get('Content-Type');

    // 获取PDF数据流并转换为Blob
    res
      .blob()
      .then((blobStream) => {
        // 创建Blob对象
        const blob = new Blob([blobStream], {
          type: contentType || 'application/pdf',
        });

        // 创建URL对象
        const blobUrl = window.URL.createObjectURL(blob);

        // 创建隐藏的iframe元素
        const printFrame = document.createElement('iframe');
        printFrame.style.display = 'none';
        document.body.appendChild(printFrame);

        // 当iframe加载完成后调用打印
        printFrame.onload = function () {
          try {
            // 等待一小段时间确保PDF渲染完成
            setTimeout(() => {
              printFrame.contentWindow.print();
            }, 0);
          } catch (error) {
            console.error('打印过程出错:', error);
            message.error('打印过程中出错');
            document.body.removeChild(printFrame);
            window.URL.revokeObjectURL(blobUrl);
          }
        };

        // 设置iframe的src来加载PDF
        printFrame.src = blobUrl;
      })
      .catch((error) => {
        console.error('PDF处理出错:', error);
        message.error('处理PDF数据时出错');
      });
  } else {
    message.error('获取打印数据失败');
  }
};

interface PdfPrintProps {
  /**
   * 接口URL，用于获取PDF数据
   */
  apiUrl: string;

  /**
   * 请求参数
   * 可以是静态对象，也可以是返回参数对象的函数
   * 如果函数返回false，则不会发送请求
   */
  params?: any | (() => any | false);

  /**
   * 请求方法，默认为POST
   */
  method?: 'GET' | 'POST';

  /**
   * 参数类型，默认为params
   * - params: 作为URL参数传递
   * - data: 作为请求体传递
   */
  paramType?: 'params' | 'data';

  /**
   * 按钮图标，默认为PrinterOutlined
   */
  icon?: React.ReactNode;

  /**
   * 提示文本
   */
  tooltipTitle?: string;

  /**
   * 按钮类型
   */
  buttonType?: 'default' | 'primary' | 'ghost' | 'dashed' | 'link' | 'text';

  /**
   * 按钮大小
   */
  buttonSize?: 'large' | 'middle' | 'small';

  /**
   * 按钮样式
   */
  style?: React.CSSProperties;

  /**
   * 自定义类名
   */
  className?: string;

  /**
   * 禁用状态
   */
  disabled?: boolean;

  /**
   * 按钮文字内容
   */
  children?: React.ReactNode;
}

/**
 * PDF打印组件
 *
 * 用于通过API获取PDF数据并自动打印
 */
const PdfPrint: React.FC<PdfPrintProps> = ({
  apiUrl,
  params = {},
  method = 'POST',
  paramType = 'params',
  // @ts-ignore
  icon = <PrinterOutlined />,
  tooltipTitle = '打印',
  buttonType = 'default',
  buttonSize = 'middle',
  style,
  className,
  disabled = false,
  children,
}) => {
  // 请求配置
  const requestConfig = {
    method,
    [paramType]: params,
  };

  // PDF打印请求
  const { loading, run: printPdf } = useRequest(
    (currentParams) => {
      const mergedParams = { ...params, ...currentParams };
      const config = {
        method,
        [paramType]: mergedParams,
      };

      return uniCommonService(apiUrl, config);
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response) => {
        // 使用提取的核心打印逻辑
        handlePdfPrint(response);
      },
    },
  );

  // 处理按钮点击事件
  const handleClick = () => {
    // 检查params是否为函数
    if (typeof params === 'function') {
      const dynamicParams = params();
      // 如果函数返回false，则不发送请求
      if (dynamicParams === false) {
        return;
      }
      printPdf(dynamicParams);
    } else {
      // 原始行为：传递静态params
      printPdf(params || {});
    }
  };

  return (
    <Tooltip title={tooltipTitle}>
      <Button
        type={buttonType}
        icon={icon}
        onClick={handleClick}
        loading={loading}
        size={buttonSize}
        style={style}
        className={className}
        disabled={disabled}
      >
        {children}
      </Button>
    </Tooltip>
  );
};

export default PdfPrint;
