import { Card, Col, Row, Form, Divider, Space, Tabs } from 'antd';
import { useEffect, useState, useMemo } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import CardEchart from '@uni/components/src/cardEchart';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';
import { SettleCompStatsSelectedTrendsLineOption } from '../chart.opts';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import {
  TabCommonItems,
  HospTotalStatsColumns,
  SettleCompStatsByMedTeamColumns,
  HospGainLossProfitStatsColumns,
  SettleCompStatsByCliDeptColumns,
  SettleCompStatsByGrpColumns,
} from '../constants';
import Stats from '@/components/stats';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import DetailsBtn from '@uni/components/src/details-btn';
import GradientChartAndTableAndPie from '@/components/gradientChartAndTableAndPie';
import FeeCompositionAndAnalysis from '@/components/feeCompositionAndAnalysis';
import {
  CliDeptDefaultOpts,
  GrpDefaultOpts,
  GrpQuadrantAxisOpts,
  CliDeptQuadrantAxisOpts,
} from '@/pages/dip/optsConstants';
import IconBtn from '@uni/components/src/iconBtn';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import { StatsDetailColumns } from '../components/stats/constants';
import DrawerCardInfo from '@/pages/dip/components/drawerCardInfo';
import { mergeTableClickParams } from '@/utils/utils';

const DipHospAnalysis = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, insurType } = globalState?.searchParams;
  const [selectedStatItem, setSelectedStatItem] = useState({
    contentData: 'Profit',
    dataType: 'Currency',
    title: '盈亏',
  });
  const [requestParams, setRequestParams] = useState<any>({});
  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  useEffect(() => {
    getSettleCompStatsTrendColumnsReq();
  }, []);

  useEffect(() => {
    console.log('globalState?.searchParams', globalState?.searchParams);
    if (
      (globalState?.searchParams &&
        globalState?.searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(requestParams) &&
        globalState?.searchParams?.dateRange?.length)
    ) {
      if (dateRange?.length) {
        let tableParams = {
          Sdate: dateRange?.at(0),
          Edate: dateRange?.at(1),
          HospCode: hospCodes,
          insurType,
        };
        setRequestParams(tableParams);
      }
    }
  }, [globalState?.searchParams]);

  useEffect(() => {
    if (
      Object.keys(requestParams)?.length &&
      activeKey === TabCommonItems.statistic.key
    ) {
      getSettleCompStatsTrendReq(requestParams);
    }
  }, [requestParams, activeKey]);

  // 趋势
  const {
    data: settleCompStatsTrendData,
    loading: getSettleCompStatsTrendLoading,
    run: getSettleCompStatsTrendReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsTrend`,
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 趋势 Columns
  const {
    data: settleCompStatsTrendColumnsData,
    loading: getSettleCompStatsTrendColumnsLoading,
    run: getSettleCompStatsTrendColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(
        `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsTrend`,
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  // stat click 由Component Stats传入
  useEffect(() => {
    Emitter.on(EventConstant.STAT_ON_CLICK_EMITTER, (record) => {
      setSelectedStatItem(record);
    });
    return () => {
      Emitter.off(EventConstant.STAT_ON_CLICK_EMITTER);
    };
  }, []);

  const SettleCompStatsSelectedTrendsLineOptionResult = useMemo(() => {
    if (settleCompStatsTrendData && settleCompStatsTrendData?.length > 0) {
      return SettleCompStatsSelectedTrendsLineOption(
        settleCompStatsTrendData,
        'MonthDate',
        selectedStatItem,
      );
    }
    return {};
  }, [selectedStatItem, settleCompStatsTrendData]);

  // tabs
  let tabItems = [
    {
      key: TabCommonItems.statistic.key,
      label: TabCommonItems.statistic.title,
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={12} xl={11}>
            <Row gutter={[16, 16]}>
              <Stats
                api={`Api/FundSupervise/LatestDipSettleStats/SettleCompStatsOfHosp`}
                columns={HospTotalStatsColumns}
                type="col-xl-6"
                tabKey={activeKey}
                outerTableParams={requestParams}
                onClickEmitter={EventConstant.STAT_ON_CLICK_EMITTER}
              />
            </Row>
          </Col>
          <Col xs={24} sm={24} md={24} lg={12} xl={13}>
            <CardEchart
              title={<>全院月度变化趋势</>}
              height={430}
              dictData={globalState.dictData}
              elementId="Trend"
              loading={getSettleCompStatsTrendLoading}
              options={SettleCompStatsSelectedTrendsLineOptionResult}
              needExport={true}
              exportTitle={'全院月度变化趋势'}
              exportData={settleCompStatsTrendData}
              exportColumns={settleCompStatsTrendColumnsData}
              needModalDetails={true}
              onRefresh={() => {
                getSettleCompStatsTrendReq(requestParams);
              }}
            ></CardEchart>
          </Col>
        </Row>
      ),
    },
    {
      key: TabCommonItems.feeAnalysis.key,
      label: TabCommonItems.feeAnalysis.title,
      children: (
        <FeeCompositionAndAnalysis
          requestParams={requestParams}
          api={`Api/FundSupervise/LatestDipSettleStats/FeeChargeDistribution`}
          tabKey={activeKey}
        />
      ),
    },
    {
      key: TabCommonItems.drgAnalysis.key,
      label: TabCommonItems.drgAnalysis.title,
      children: (
        <Row gutter={[16, 16]}>
          <GradientChartAndTableAndPie
            args={{
              level: 'hosp',
              type: 'grp',
              title: '病组效率',
              category: 'ChsDrgName',
              columns: [
                {
                  dataIndex: 'operation',
                  visible: true,
                  width: 40,
                  align: 'center',
                  order: 1,
                  title: '',
                  render: (node, record, index) => {
                    return (
                      <IconBtn
                        type="details"
                        onClick={(e) => {
                          e.stopPropagation();
                          Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                            id: 'dip-hosp-settle-stats-by-drp', // 要匹配到对应的DetailTableModal id
                            title: `${record?.ChsDrgName}`,
                            args: {
                              ...requestParams,
                              VersionedChsDrgCodes: [
                                record?.VersionedChsDrgCode || undefined,
                              ],
                            },
                            type: 'dip',
                            detailsUrl:
                              'FundSupervise/LatestDipSettleStats/SettleDetails',
                            dictData: globalState?.dictData, // 传入
                          });
                        }}
                      />
                    );
                  },
                },
                ...SettleCompStatsByGrpColumns,
              ],
              clickable: true,
              detailsTitle: '病组分布',
              axisOpts: GrpQuadrantAxisOpts,
              defaultAxisOpt: GrpDefaultOpts,
              api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByGrp`,
            }}
            requestParams={requestParams}
          />
        </Row>
      ),
    },
    // 学科
    // {
    //   key: TabCommonItems.majorPerfDeptAnalysis.key,
    //   label: TabCommonItems.majorPerfDeptAnalysis.title,
    //   children: (
    //     <>
    //       <Row gutter={[16, 16]}>
    //         <Col span={24}>
    //           <GradientChartAndTableAndPie
    //             args={{
    //               clickable: true,
    //               emitter: EventConstant.MAJOR_PERF_DEPT_TABLE_ROW_CLICK,
    //               type: 'majorPerfDept',
    //               title: '学科效率',
    //               category: 'MajorPerfDeptName',
    //               columns: [
    //                 {
    //                   dataIndex: 'operation',
    //                   visible: true,
    //                   width: 40,
    //                   align: 'center',
    //                   fixed: 'left',
    //                   order: 1,
    //                   title: '',
    //                   render: (node, record, index) => {
    //                     return (
    //                       <IconBtn
    //                         type="details"
    //                         title="查看数据"
    //                         onClick={(e) => {
    //                           e.stopPropagation();
    //                           Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
    //                             title: `${record?.MajorPerfDeptName}`,
    //                             args: {
    //                               // 学科 特殊处理 需要按照record里面与requestParams合并传参
    //                               ...mergeTableClickParams(
    //                                 requestParams,
    //                                 record,
    //                                 ['HospCode'],
    //                               ),
    //                               MajorPerfDepts: record?.MajorPerfDept
    //                                 ? [record?.MajorPerfDept]
    //                                 : ['%'],
    //                             },
    //                             type: 'dip',
    //                             detailsUrl:
    //                               'FundSupervise/LatestDipSettleStats/SettleDetails',
    //                             dictData: globalState?.dictData, // 传入
    //                           });
    //                         }}
    //                       />
    //                     );
    //                   },
    //                 },
    //                 ...SettleCompStatsByCliDeptColumns,
    //               ],
    //               detailsTitle: '学科分布',
    //               defaultAxisOpt: CliDeptDefaultOpts,
    //               axisOpts: CliDeptQuadrantAxisOpts,
    //               api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByMajorPerfDept`,
    //             }}
    //             requestParams={requestParams}
    //           />
    //         </Col>
    //       </Row>
    //     </>
    //   ),
    // },
    {
      key: TabCommonItems.deptAnalysis.key,
      label: TabCommonItems.deptAnalysis.title,
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTableAndPie
                args={{
                  clickable: true,
                  emitter: EventConstant.DEPT_TABLE_ROW_CLICK,
                  // cols: 'col-xl-24-8',
                  type: 'dept',
                  title: '科室效率',
                  category: 'CliDeptName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      fixed: 'left',
                      order: 1,
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            title="查看数据"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                title: `${record?.CliDeptName}`,
                                args: {
                                  ...requestParams,
                                  CliDepts: record?.CliDept
                                    ? [record?.CliDept]
                                    : ['%'],
                                },
                                type: 'dept',
                                detailsUrl:
                                  'FundSupervise/LatestDipSettleStats/SettleDetails',
                                dictData: globalState?.dictData, // 传入
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByCliDeptColumns,
                  ],
                  detailsTitle: '科室分布',
                  defaultAxisOpt: CliDeptDefaultOpts,
                  axisOpts: CliDeptQuadrantAxisOpts,
                  api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByCliDept`,
                }}
                requestParams={requestParams}
              />
            </Col>
          </Row>
        </>
      ),
    },
    // 医疗组
    {
      key: TabCommonItems.medTeamAnalysis.key,
      label: TabCommonItems.medTeamAnalysis.title,
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTableAndPie
                args={{
                  type: 'medTeam',
                  clickable: true,
                  emitter: EventConstant.MED_TEAM_TABLE_ROW_CLICK,
                  title: '医疗组效率',
                  category: 'MedTeamName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      order: 1,
                      fixed: 'left',
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            title="查看数据"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                title: `${record?.MedTeamName}`,
                                args: {
                                  ...requestParams,
                                  MedTeams: record?.MedTeam
                                    ? [record?.MedTeam]
                                    : ['%'],
                                },
                                type: 'medteam',
                                detailsUrl:
                                  'FundSupervise/LatestDipSettleStats/SettleDetails',
                                dictData: globalState?.dictData, // 传入
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByMedTeamColumns,
                  ],
                  detailsTitle: '医疗组分布',
                  defaultAxisOpt: CliDeptDefaultOpts,
                  axisOpts: CliDeptQuadrantAxisOpts,
                  api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByMedTeam`,
                }}
                requestParams={requestParams}
              />
            </Col>
          </Row>
        </>
      ),
    },
  ];

  return (
    <>
      <Card>
        <Tabs
          items={tabItems}
          activeKey={activeKey}
          onChange={(key) => setActiveKey(key)}
        />
        <DetailTableModal
          dictData={globalState.dictData}
          detailAction={(record) => {
            // 这里替代内部 操作 onClick
            setDrawerVisible({
              hisId: record?.HisId,
              type: 'dip',
            });
          }}
        />
        {/* drawer 同理 这样保持复用 因为drawer 监听的是detailTableModal 或者一些table */}
        <DrawerCardInfo
          visible={drawerVisible}
          onClose={() => setDrawerVisible(undefined)}
        />
      </Card>
    </>
  );
};

export default DipHospAnalysis;
