import Dmr<PERSON><PERSON>yHeader from '../dmr-query-header';
import { RespVO } from '@uni/commons/src/interfaces';
import { UniTable } from '@uni/components/src';
import {
  ProForm,
  ProFormDateRangePicker,
  ProFormSelect,
} from '@uni/components/src/pro-form';
import { uniCommonService } from '@uni/services/src';
import { Emitter } from '@uni/utils/src/emitter';
import { Button, Card, Alert, Empty, Spin } from 'antd';
import Modal from 'antd/lib/modal/Modal';
import { useEffect, useState, useReducer, Reducer } from 'react';
import { useRequest } from 'umi';
import { qualityControlRuleEventConstants } from '../../constants';
import { TestColumns } from './columns';
import { IReducer, IModalState } from '@uni/reducers/src/interface';
import { modalReducer, ModalAction } from '@uni/reducers/src';
import jsonLogic from './jsonLogic';
import _ from 'lodash';
import DescriptionPart from '../patient-info';
import { recurrenceFindOne } from '@/utils/widgets';
import { notification } from 'antd';
import { mockConditionTemplate } from '../conditionForm/constants';
import md5 from 'crypto-js/md5';

const NOTIFICATION_KEY = 'JsonLogic_notification';

const TestModal = ({ formRef, ruleCode }) => {
  const [ModalState, ModalDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer<IModalState<any>>>
  >(modalReducer, {
    visible: false,
    record: undefined,
    actionType: 'Create',
  });

  const [HisId, setHisId] = useState(undefined);

  const [jsonLogicTestResult, setjsonLogicTestResult] = useState({
    message: '',
    description: '',
    type: '',
    showIcon: true,
  });

  const checkJsonLogic = async (src, ruleJsonLogic) => {
    // const startsWithIn = (target, prefixes) => {
    //   console.log('startsWithIn', target, prefixes);
    //   if (!_.isString(target)) {
    //     return false;
    //   }
    //   return _.some(prefixes, (p) => target.startsWith(p));
    // };
    // jsonLogic.add_operation('startsWithIn', startsWithIn);

    try {
      let matchResult = jsonLogic.apply(ruleJsonLogic, src);
      console.log('matchResults', src, ruleJsonLogic, matchResult);
      // src = JSON.stringify(jsonLogicSrcObjectData)
      if (_.isBoolean(matchResult) && matchResult) {
        setjsonLogicTestResult({
          message: '测试成功',
          description: '规则条件语法正确，且满足质控条件',
          type: 'success',
          showIcon: true,
        });
      } else {
        setjsonLogicTestResult({
          message: '测试成功',
          description: (
            <>
              规则条件语法正确，但<b style={{ color: 'red' }}>不满足</b>质控条件
            </>
          ) as any,
          type: 'info',
          showIcon: true,
        });
      }

      return matchResult;
    } catch (err) {
      console.error('ERRORRRR', err, jsonLogic);
      setjsonLogicTestResult({
        message: '测试失败',
        description: '规则条件语法错误，请检查',
        type: 'error',
        showIcon: true,
      });
      // message.info(
      //   <span style={{ color: 'red' }}>规则条件语法错误，请检查</span>,
      // );
      // setCheckedStr('<span style="color:red">规则条件语法错误，请检查</span>');
      //   message.error('Expr语法错误，请检查');
    }
  };

  // 根据HisId返回JsonLogic所用的Src Json
  const {
    data: jsonLogicSrcObjectData, // JsonSrc切换数据
    mutate: jsonLogicSrcObjectDataMutate,
    loading: getJsonLogicSrcObjectReqLoading,
    run: getJsonLogicSrcObjectReq,
  } = useRequest(
    (hisId) => {
      return uniCommonService(
        `Api/Dmr/DmrQualityControl/GetJsonLogicSrcObject`,
        {
          method: 'POST',
          requestType: 'json',
          data: {
            hisId,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0) {
          console.log(response);
          return response?.data;
        } else {
          return {};
        }
      },
    },
  );

  // 测试 JsonLogic
  const { loading: testJsonLogicReqLoading, run: testJsonLogicReq } =
    useRequest(
      (data) => {
        return uniCommonService(`Api/Sys/QualitySys/TestJsonLogicRule`, {
          method: 'POST',
          requestType: 'json',
          data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<any>) => {
          return response;
        },
        onSuccess: (res, params) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            let { IsGrammarCorrect, IsTriggered, OutPut } = res.data;
            if (!IsGrammarCorrect || (IsGrammarCorrect && !IsTriggered)) {
              notification.warning({
                key: NOTIFICATION_KEY,
                message: IsGrammarCorrect ? '规则未触发' : 'Expr语法错误',
                description: OutPut || (
                  <>
                    Expr语法正确，但<b style={{ color: 'red' }}>不满足</b>
                    质控条件
                  </>
                ),
                placement: 'top',
                duration: 10,
              });
            } else {
              notification.success({
                key: NOTIFICATION_KEY,
                message: '测试Expr成功',
                description: OutPut || 'Expr语法正常，且满足质控条件',
                placement: 'top',
              });
            }
          }
        },
      },
    );

  useEffect(() => {
    console.log('ddsasadsa');
    Emitter.on(qualityControlRuleEventConstants.OPEN_TEST_MODAL, (expr) => {
      console.log('jsonLogic', expr);
      ModalDispatch({
        type: ModalAction.change,
        payload: {
          visible: true,
          record: expr,
        },
      });
    });

    return () => {
      Emitter.off(qualityControlRuleEventConstants.OPEN_TEST_MODAL);
    };
  }, []);

  useEffect(() => {
    if (jsonLogicSrcObjectData && ModalState?.record) {
      // 自动测试
      console.log('jsonLogicSrcObjectData', ModalState?.record);
      // checkJsonLogic(jsonLogicSrcObjectData, ModalState?.record);
      let node = recurrenceFindOne(
        formRef?.current?.at(0)?.current?.getFieldsValue()?.conditionTemplate,
        'value',
        mockConditionTemplate,
      );
      let ruleCodeSuffix = '';
      if (node?.result?.contents?.length > 0) {
        ruleCodeSuffix = node?.result?.ruleCodeSuffix;
      }
      testJsonLogicReq({
        RuleArgs: {
          ...formRef?.current?.at(0)?.current?.getFieldsValue(),
          RuleCode:
            ruleCode ||
            `${ruleCodeSuffix}${md5(JSON.stringify(ModalState?.record))}`,
          Expr: JSON.stringify(ModalState?.record),
          ExprProvider: 'JsonLogic',
        },
        CardJson: JSON.stringify(jsonLogicSrcObjectData),
      });
    }
  }, [jsonLogicSrcObjectData, ModalState?.record, ruleCode]);

  return (
    <Modal
      title="规则测试"
      width={1120}
      style={{ top: '20px' }}
      open={ModalState.visible}
      onCancel={() => {
        ModalDispatch({
          type: ModalAction.init,
        });
        setHisId(undefined);
        setjsonLogicTestResult({
          message: '',
          description: '',
          type: 'success',
          showIcon: true,
        });
        jsonLogicSrcObjectDataMutate(undefined);
      }}
      maskClosable={false}
      destroyOnClose
      cancelText="取消"
      okButtonProps={{
        style: {
          display: 'none',
        },
      }}
    >
      {/* 把原本的先拿过来 */}
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div className="ant-form-item-label expr-form-item">
          <label>唯一号</label>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          // justifyContent: 'space-between',
          alignItems: 'baseline',
        }}
      >
        <DmrQueryHeader
          getSelectedValue={(value) => {
            getJsonLogicSrcObjectReq(value);
            setHisId(value);
          }}
        />
        {/* {jsonLogicSrcObjectData && (
          <Button
            type="primary"
            style={{ margin: '0 10px' }}
            onClick={(e) => {
              Emitter.emit(
                StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE,
              );
              console.log('请先填写规则条件后再测试', expr.current);
            }}
          >
            测试
          </Button>
        )} */}
      </div>
      {jsonLogicTestResult?.description && (
        <>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div className="ant-form-item-label expr-form-item">
              <label>测试结果</label>
            </div>
          </div>
          <Alert {...(jsonLogicTestResult as any)} />
        </>
      )}
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div className="ant-form-item-label expr-form-item">
          <label>患者基本信息</label>
        </div>
      </div>
      <Spin spinning={getJsonLogicSrcObjectReqLoading}>
        {jsonLogicSrcObjectData ? (
          <DescriptionPart hisId={HisId} />
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>暂无数据，请通过关键字搜索查询</span>}
          />
        )}
      </Spin>

      {/* <ProForm
        layout="inline"
        grid={false}
        onFinish={async (values) => {
          console.log(values);
        }}
        submitter={{
          render: (props, doms) => {
            return (
              <Button type="primary" onClick={() => props.submit()}>
                测试
              </Button>
            );
          },
        }}
      >
        <ProFormDateRangePicker name="dateRange" label="时间" />
        <ProFormSelect name="hospCode" label="院区" width={'sm'} />
        <ProForm.Item label="唯一号">
          <DmrQueryHeader
            needValueShow
            getSelectedValue={(value) => {
              getJsonLogicSrcObjectReq(value);
              setHisId(value);
            }}
          />
        </ProForm.Item>
      </ProForm>
      <Card title="测试结果">
        <UniTable
          rowKey="id"
          columns={TestColumns}
          dataSource={[]}
          id="tested_datasource"
        />
      </Card> */}
    </Modal>
  );
};

export default TestModal;
