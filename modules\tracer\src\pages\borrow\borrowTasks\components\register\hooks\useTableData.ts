import { useReducer, useMemo, Reducer, useCallback } from 'react';
import { ITableState, IReducer } from '@uni/reducers/src/Interface';
import { TableAction, tableReducer, InitTableState } from '@uni/reducers/src';
import { columnsHandler } from '@/utils/widgets';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';

/**
 * 处理表格数据和列的自定义Hook
 */
export const useTableData = <T extends Record<string, any>>(
  baseColumns: any[],
) => {
  // 列表数据状态管理
  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<T>, IReducer>
  >(tableReducer, {
    ...InitTableState,
  });

  // columns 处理，主要用于处理options
  const SearchedTableColumnsSolver = useMemo(() => {
    return SearchTable.columns?.length > 0
      ? columnsHandler(SearchTable.columns)
      : [];
  }, [SearchTable.columns]);

  // 初始化表格列
  const initTableColumns = useCallback(
    (remoteColumns = []) => {
      if (remoteColumns.length > 0) {
        console.log('remoteColumns', baseColumns, remoteColumns);
        SearchTableDispatch({
          type: TableAction.columnsChange,
          payload: {
            columns: tableColumnBaseProcessor(baseColumns, remoteColumns),
          },
        });
      }
      return tableColumnBaseProcessor([], remoteColumns);
    },
    [baseColumns],
  );

  // 保存列配置并更新表格
  const saveTableColumns = (columns) => {
    SearchTableDispatch({
      type: TableAction.columnsChange,
      payload: {
        columns: tableColumnBaseProcessor(baseColumns, columns),
      },
    });
  };

  return {
    SearchTable,
    SearchTableDispatch,
    SearchedTableColumnsSolver,
    initTableColumns,
    saveTableColumns,
  };
};
