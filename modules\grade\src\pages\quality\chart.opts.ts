import _ from 'lodash';
import theme from '@uni/components/src/echarts/themes/themeBlueYellow';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';

const hqmsQualityOfEntityRadarOption = (data, category = '') => {
  const res = data?.[0] || {};
  const {
    UnexpectedBackInRatio,
    SurgeryDeathRatio,
    NeonateDeathRatio,
    GradeLowRiskDeathRatio,
    DeathRatio,
  } = res;
  let color = theme.color;
  let option = {
    tooltip: {
      confine: true,
    },
    legend: {
      show: false,
      icon: 'circle',
    },
    xAxis: false,
    yAxis: false,
    radar: {
      shape: 'circle',
      radius: '60%',
      center: ['50%', '50%'],
      axisLabel: {
        interval: 3,
      },
      indicator: [
        { name: '31天非预期再住院率' },
        { name: '手术死亡率' },
        { name: '新生儿死亡率' },
        { name: '死亡率' },
        { name: 'ICD低风险死亡率' },
        // { name: '31天非预期再住院率', max: 0.045 },
        // { name: '手术死亡率', max: 0.0002 },
        // { name: '新生儿死亡率', max: 0.00007 },
        // { name: '死亡率', max: 0.0009 },
        // { name: 'ICD低风险死亡率', max: 0.015 },
        // { name: '低风险死亡率', max: 0.0004 },
      ],
    },
    series: [
      {
        type: 'radar',
        tooltip: {
          valueFormatter: (value) => {
            return valueNullOrUndefinedReturnDash(value, 'Percent');
          },
        },
        data: [
          {
            value: [
              UnexpectedBackInRatio ?? 0,
              SurgeryDeathRatio ?? 0,
              NeonateDeathRatio ?? 0,
              DeathRatio ?? 0,
              GradeLowRiskDeathRatio ?? 0,
              // data?.LowRiskDeathRatio ?? 0,
            ],
            name: '全部',
            areaStyle: {
              normal: {
                opacity: 0.6,
                color: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: color[3], // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: color[0], // 100% 处的颜色
                    },
                  ],
                  globalCoord: false, // 缺省为 false
                },
              },
            },
          },
        ],
      },
    ],
  };
  return option;
};

export { hqmsQualityOfEntityRadarOption };
