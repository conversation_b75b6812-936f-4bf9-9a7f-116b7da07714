import { useDispatch, useSelector } from 'umi';
import { Dispatch } from 'umi';
import { ReqActionType } from '@/Constants';
import { handleMrActionApi, isRespErr } from '@/utils/widgets';
import { message } from 'antd';
import { TableAction } from '@uni/reducers/src';
import { UseDispostionEnum, downloadFile } from '@uni/utils/src/download';
import dayjs from 'dayjs';

interface UseApiRequestsProps {
  SearchTableDispatch: any;
  UnLendTableDispatch: any;
  resetModalState: () => void;
  hiddenLoading: React.MutableRefObject<boolean>;
  focusBarCode: () => void;
  proFormRef: React.MutableRefObject<any>;
  setRevertRecord?: (record: any) => void;
}

/**
 * 处理所有API请求的自定义Hook
 */
export const useApiRequests = ({
  SearchTableDispatch,
  UnLendTableDispatch,
  resetModalState,
  hiddenLoading,
  focusBarCode,
  proFormRef,
  setRevertRecord,
}: UseApiRequestsProps) => {
  const dispatch: Dispatch = useDispatch();
  const loadings = useSelector((state) => state.global.loadings);

  /**
   * 查询结果处理
   */
  const searchResultHandler = (params, res, needDataPush, reqActionReq) => {
    if (!isRespErr(res)) {
      let resData;
      if (res?.data?.Items) {
        // Api/Mr/TraceRecord/GetList
        resData = res?.data?.Items?.slice();
      } else {
        // Api/Mr/TraceRecord/GetListByBarCode
        resData = res?.data?.slice();
      }
      if (!needDataPush) {
        if (resData?.length === 1) {
          // 单条，直接操作
          if (resData?.at(0)?.CanLend) {
            SearchTableDispatch({
              type: TableAction.dataUnshiftUniqSimple,
              payload: {
                data: {
                  ...resData?.at(0),
                  InsertTime: dayjs(),
                },
                key: 'BarCode',
              },
            });
          } else {
            message.error('该病案不可借阅');
            UnLendTableDispatch({
              type: TableAction.dataUnshiftUniqSimple,
              payload: {
                data: {
                  ...resData?.at(0),
                  InsertTime: dayjs(),
                },
                key: 'BarCode',
              },
            });
          }
          // 重置节流标识
          hiddenLoading.current = false;
          // barCode自动清除 不阻碍扫码枪继续扫码
          proFormRef.current.resetFields(['BarCode']);
          // focus input
          focusBarCode();
        } else if (resData?.length > 1) {
          // 多条，返回数据以供显示在模态框中
          return {
            multipleRecords: true,
            records: resData,
            specialData: params,
          };
        } else {
          // 没查到数据
          // 重置节流标识
          hiddenLoading.current = false;
          return { noData: true };
        }
      } else {
        // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
        // 查询成功后，把数据插入已记录列表
        if ((Array.isArray(resData) ? resData : [resData])?.at(0)?.CanLend) {
          SearchTableDispatch({
            type: TableAction.dataUnshiftUniqSimple,
            payload: {
              data: {
                ...(Array.isArray(resData) ? resData : [resData])?.at(0),
                InsertTime: dayjs(),
              },
              key: 'BarCode',
            },
          });
        } else {
          message.error('该病案不可借阅');
          UnLendTableDispatch({
            type: TableAction.dataUnshiftUniqSimple,
            payload: {
              data: {
                ...(Array.isArray(resData) ? resData : [resData])?.at(0),
                InsertTime: dayjs(),
              },
              key: 'BarCode',
            },
          });
        }
        // 重置节流标识
        hiddenLoading.current = false;
        // barCode自动清除 不阻碍扫码枪继续扫码
        proFormRef.current.resetFields(['BarCode']);
        // focus input
        focusBarCode();
      }
    } else {
      // 重置节流标识
      hiddenLoading.current = false;
    }
  };

  /**
   * 批量查询，查询→操作
   */
  const searchOneReq = async (data: any, needDataPush = false) => {
    if (!data) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetList`,
          method: 'POST',
          data: {
            ...data,
            SkipCount: 0,
            MaxResultCount: 999999,
          },
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    return searchResultHandler(data, res, needDataPush, reqActionReq);
  };

  /**
   * 扫码枪条码查询
   */
  const searchByBarCodeReq = async (params: any, needDataPush = false) => {
    if (!params) return;

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetListByBarCode`,
          method: 'POST',
          data: {
            BarCode: params?.BarCode,
            SkipCount: 0,
            MaxResultCount: 999999,
          },
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    return searchResultHandler(params, res, needDataPush, reqActionReq);
  };

  /**
   * 执行借阅/撤销操作
   * 如果传入了createReq，会调用外部传入的创建函数
   */
  const reqActionReq = async (data: any, item, reqType: ReqActionType) => {
    if (!data || !reqType) return;

    // 常规情况使用默认API
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `Borrowing/${reqType}`,
        requestParams: {
          url: `Api/Mr/Borrowing/${reqType}`,
          method: 'POST',
          data: data,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    // 不管结果 重置节流标识
    hiddenLoading.current = false;

    if (!isRespErr(res)) {
      if (reqType === ReqActionType.lend) {
        // 把modal关闭
        resetModalState();

        // StatusCode 黄神的单独处理
        let result = handleMrActionApi(res.data);
        result?.isCorrect
          ? message.success('借阅成功')
          : message.error(result?.errMsg?.join('。/n'));

        if (result?.data?.length > 0) {
          // 需要datapush,此时已经确定了操作的条目，所以只能是单个，不做判断
          // 把数据插入已记录列表
          SearchTableDispatch({
            type: TableAction.dataUnshiftUniqSimple,
            payload: {
              data: {
                ...(Array.isArray(result?.data)
                  ? result?.data?.at(0)
                  : result?.data),
                InsertTime: dayjs(),
              },
              key: 'BarCode',
            },
          });
        }

        if (result?.errType === '404') {
          // 404
          return { error404: true, errMsg: result?.errMsg };
        } else {
          // barCode自动清除：成功才清除，如果失败则提示并且不清除？
          proFormRef.current.resetFields(['BarCode']);
          // focus input
          focusBarCode();
        }
      } else if (reqType === ReqActionType.return) {
        // revert
        SearchTableDispatch({
          type: TableAction.dataFilt,
          payload: {
            key: 'BarCode',
            value: item.BarCode,
          },
        });
        setRevertRecord && setRevertRecord(item);
        message.success('撤销成功');
      }
    }
  };

  /**
   * 处理借阅导出
   */
  const exportBackendReq = async (params) => {
    if (!params) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: 'ExportTraceRecord',
        requestParams: {
          url: `Api/Mr/BorrowRecord/${ReqActionType.borrowRecordPrint}`,
          method: 'POST',
          data: params,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      let exportName = `病案借阅记录`;
      downloadFile(exportName, res?.response, UseDispostionEnum.nouse);
    }
  };

  return {
    loadings,
    searchOneReq,
    searchByBarCodeReq,
    reqActionReq,
    exportBackendReq,
  };
};
