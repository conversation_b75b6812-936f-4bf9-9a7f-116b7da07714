import { uniCommonService } from '@uni/services/src/index';
import { isEmptyValues } from '@uni/utils/src/utils';
import _ from 'lodash';
import { isRespErr } from './widgets';

export const getCalculationKeys = async (module) => {
  return uniCommonService('Api/Dyn-ddr/DynCommon/GetCalculation', {
    method: 'POST',
    data: { module },
  });
};

export const calculateRequest = async (type, data) => {
  return uniCommonService(`Api/Dyn-ddr/${type}/Calculate`, {
    method: 'POST',
    data,
  });
};

export const autoCalculateHandler = async (
  calculateKeys, // 默认情况下需要自动计算的keys list[]
  record, // 数据
  formRef, // formRef
  type, // 接口 所处类型（DailyInpatientAmt ....）
  isAutoComputInputs, // flag 用于判断用户有没有手动输入过的字段的合集
) => {
  // 先判断isAutoComputInputs 在为空或者全为false的情况下不做处理直接返回
  if (
    !isAutoComputInputs ||
    (_.isObject(isAutoComputInputs) &&
      Object.values(isAutoComputInputs)?.every((value) => !value))
  )
    return null;

  // 对数据做一层额外处理
  let transformedRecord = transformAutoCalculateParams(record);
  // 如果没有特定的值则直接return
  if (!hasAllKeys(transformedRecord, ['DeptCode', 'ExactDate'])) return null;

  try {
    const res = await calculateRequest(type, { ...transformedRecord });
    if (!isRespErr(res)) {
      let { data } = res;
      for (const key of calculateKeys) {
        // 如果isAutoComputInputs 内对应的key为false则跳过
        if (!isAutoComputInputs?.[key]) continue;
        // 因为只有一条
        // 单格修改
        formRef.current.setFieldValue(
          [`${Object.keys(formRef.current?.getFieldsValue())?.at(0)}`, key],
          data?.[key],
        );
      }
      return data; // 返回接口响应数据
    }
    return null;
  } catch (error) {
    console.error(`AutoCalculate 失败。具体报错:`, error);
    return null;
  }
};

export const autoComputeRef = (keys) => {
  if (keys?.length > 0) {
    return keys.reduce((acc, curr) => {
      acc[curr] = true; // 设置每个字符串为键，值为true
      return acc;
    }, {});
  } else {
    return undefined;
  }
};

// 对create时的数据做一层额外处理 以支持autoCalculate
export const transformAutoCalculateParams = (
  data,
  translateKeys = undefined,
) => {
  const result = {};

  for (const key in data) {
    if (!data.hasOwnProperty(key)) continue;
    const value = data[key];
    let newKey = key;
    let shouldProcess = false;

    // 判断是否需要处理当前键
    if (translateKeys) {
      shouldProcess = translateKeys.includes(key);
    } else {
      shouldProcess = key.endsWith('Name');
    }

    if (shouldProcess) {
      // 替换键名：以 Name 结尾的键改为 Code
      if (key.endsWith('Name')) {
        newKey = key.replace(/Name$/, 'Code');
      }

      // 提取 value 属性（如果存在）
      if (value !== null && typeof value === 'object' && 'value' in value) {
        result[newKey] = value.value;
      } else {
        result[newKey] = value; // 非对象或無 value 时保留原值
      }
    } else {
      result[newKey] = value; // 不需要处理，直接保留
    }
  }

  return result;
};

export const hasAllKeys = (obj, keyList) => {
  return keyList.every(
    (key) => obj.hasOwnProperty(key) && !isEmptyValues(obj[key]),
  );
};
