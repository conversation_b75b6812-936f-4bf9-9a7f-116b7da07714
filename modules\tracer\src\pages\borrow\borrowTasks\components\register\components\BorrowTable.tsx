import React, { useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Divider,
  Popconfirm,
  Tooltip,
  message,
} from 'antd';
import { UniTable } from '@uni/components/src';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import clsx from 'clsx';
import { BorrowColumns } from '../../constants';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { PrinterOutlined, FileExcelOutlined } from '@ant-design/icons';

interface BorrowTableProps {
  id: string;
  columns: any[];
  data: any[];
  loadings: any;
  dictData: any;
  clkItem: any;
  onRowClick: (record: any) => void;
  // onTableRowSaveSuccess: (columns: any) => void;
  onExport?: () => void;
  handlePrint?: () => void;
  excelDisabled?: boolean;
}

/**
 * 借阅表格组件
 */
const BorrowTable: React.FC<BorrowTableProps> = ({
  id,
  columns,
  data,
  loadings,
  dictData,
  clkItem,
  onRowClick,
  // onTableRowSaveSuccess,
  onExport,
  handlePrint,
  excelDisabled,
}) => {
  // 拦截columns点击导致的sorter变化
  useEffect(() => {
    if (!columns?.length || !data?.length) return;

    let tableHeaders = null;
    const handleHeaderClick = (e) => {
      e.stopPropagation();
      e.preventDefault();
      return false;
    };

    // 给个容错时间 内部的rendercolumns处理时间
    setTimeout(() => {
      tableHeaders =
        document.querySelectorAll('#cant-lend-table .ant-table-thead th') || [];

      console.log('tableHeaders', tableHeaders);

      tableHeaders?.forEach((header) => {
        header.addEventListener('click', handleHeaderClick, true); // 使用捕获阶段
      });
    }, 500);

    return () => {
      tableHeaders?.forEach((header) => {
        header.removeEventListener('click', handleHeaderClick, true);
      });
    };
  }, [columns, data]);

  return (
    <UniTable
      id={id}
      rowKey="uuid"
      showSorterTooltip={false}
      loading={
        loadings['BorrowRecord/GetLendList'] ||
        loadings['TraceRecord/GetList'] ||
        loadings['Borrowing/lend'] ||
        false
      }
      columns={columns?.map((d) => ({
        ...d,
        defaultSortOrder:
          d?.orderable && d?.orderPriority !== 0 && d?.orderMode
            ? d?.orderMode
            : null,
        sorter: false,
        orderable: false,
        orderPriority: 0,
        orderMode: null,
        showSorterTooltip: false,
      }))}
      dataSource={data}
      dictionaryData={dictData}
      scroll={{ x: 'max-content' }}
      rowClassName={(record, index) => {
        let classname = [];
        // 互斥
        // if (!record?.isCorrect) {
        //   classname.push('row-error');
        // } else if (index === 0) {
        //   return 'row-first';
        // }
        if (record?.uuid === clkItem?.uuid) {
          classname.push('row-selected');
        }

        return classname.length > 0 ? clsx(classname) : null;
      }}
      onRow={(record) => {
        return {
          onClick: () => {
            onRowClick(record);
          },
        };
      }}
    />
    // <Card
    //   title="病案借阅列表"
    //   extra={
    //     <Space>
    //       {handlePrint && (
    //         <Button icon={<PrinterOutlined />} onClick={handlePrint}>
    //           打印
    //         </Button>
    //       )}
    //       <Divider type="vertical" />
    //       {onExport && (
    //         <Popconfirm
    //           title="导出时会将错误的记录过滤掉"
    //           onConfirm={() => onExport()}
    //           disabled={excelDisabled}
    //         >
    //           <Tooltip title="导出Docx">
    //             <Button
    //               type="text"
    //               shape="circle"
    //               disabled={excelDisabled}
    //               icon={<FileExcelOutlined />}
    //             />
    //           </Tooltip>
    //         </Popconfirm>
    //       )}
    //       {/* <TableColumnEditButton
    //         {...{
    //           columnInterfaceUrl: 'Api/Mr/BorrowRecord/GetLendList',
    //           onTableRowSaveSuccess: onTableRowSaveSuccess,
    //         }}
    //       /> */}
    //     </Space>
    //   }
    // >

    // </Card>
  );
};

export default BorrowTable;
