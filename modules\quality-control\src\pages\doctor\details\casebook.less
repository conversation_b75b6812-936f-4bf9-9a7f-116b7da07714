@import '~@uni/commons/src/style/variables.less';

.casebook_flex_form {
  display: flex;
  justify-content: space-between;
  align-items: center;

  > .ant-row {
    width: 80%;
  }

  .ant-form-item {
    margin-bottom: 0px;
  }
}

.summary-item-container {
  margin: 0px 10px 10px;
  padding: 2px 35px;
  line-height: 25px;
  border-radius: 20px;
  background: @tag-bg-color;
  border: 1px solid @border-color;

  display: flex;
  flex-direction: row;
  // height: 60px;
  align-items: center;
  justify-content: center;
  // border-radius: 4px;
  // border: 1px solid @border-color;
  cursor: pointer;

  .label {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
  }

  .value {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }
}
.card-selected {
  color: white;
  border-color: darken(@red-color, 10%);
  background-color: @red-color;
  span {
    color: #ffffff !important;
  }
}
