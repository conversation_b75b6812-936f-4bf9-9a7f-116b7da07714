import { ReactNode, RefObject, useEffect, useRef, useState } from 'react';
import { Statistic, Divider, Spin } from 'antd';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import _ from 'lodash';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { useDebounceFn } from 'ahooks';
import { useResizeObserver } from '@uni/hooks/src';
import IconBtn from '../iconBtn';
interface SingleStatProps {
  loading?: boolean;
  title: string | ReactNode;
  value?: any;
  noValue?: boolean;
  dataType?: string;
  suffix?: string | ReactNode;
  prefix?: string | ReactNode;
  footerNode?: ReactNode;
  footerTitle?: string | ReactNode;
  rightFooterTitle?: string | ReactNode;
  footerValue?: any;
  rightFootValue?: any;
  footerDataType?: string;
  rightFooterDataType?: string;
  args?: any;
  detailsUrl?: string;
  type?: string;
  detailType?: string;
  headerTitle?: string;
  needRenderHeaderTitle?: boolean;
  size?: string;
  // 是否能点击
  clickable?: boolean;
  className?: string;
  id?: string;
  hidden?: boolean; // 用hidden改动小
}

const SingleStat = (props: SingleStatProps) => {
  const [value, setValue] = useState(undefined);
  const [footValue, setFootValue] = useState(undefined);
  const [rightFootValue, setRightFootValue] = useState(undefined);
  const defaultContainer: RefObject<HTMLDivElement> = useRef();

  useEffect(() => {
    setValue(valueNullOrUndefinedReturnDash(props?.value, props?.dataType));
    setFootValue(
      props?.footerValue
        ? valueNullOrUndefinedReturnDash(
            props?.footerValue,
            props?.footerDataType,
          )
        : undefined,
    );
    setRightFootValue(
      valueNullOrUndefinedReturnDash(
        props?.rightFootValue,
        props?.rightFooterDataType,
      ),
    );
  }, [props]);

  const adjustFontSize = (parentWidth, domElement: HTMLElement) => {
    const maxFontSize = 24;
    let fontSize = maxFontSize;

    let spanValue: any = domElement.getElementsByClassName(
      'ant-statistic-content-value',
    )?.[0];

    let spanPrefix: any = domElement.getElementsByClassName(
      'ant-statistic-content-prefix',
    )?.[0];

    let spanSuffix: any = domElement.getElementsByClassName(
      'ant-statistic-content-suffix',
    )?.[0];

    spanValue.style.fontSize = `${fontSize}px`;

    while (
      (spanValue?.offsetWidth ?? 0) +
        (spanPrefix?.offsetWidth ?? 0) +
        (spanSuffix?.offsetWidth ?? 0) >
        parentWidth &&
      fontSize > 0
    ) {
      --fontSize; // 字体大小递减
      spanValue.style.fontSize = `${fontSize}px`;
    }
  };

  // 监听
  const { run: onDocumentResize } = useDebounceFn(
    (target) => {
      if (defaultContainer?.current) {
        const elements = defaultContainer?.current?.querySelectorAll(
          '.ant-statistic-content',
        );
        adjustFontSize(target.width, elements?.[0] as HTMLElement);
      }
    },
    {
      wait: 20,
    },
  );

  useResizeObserver<HTMLElement>({
    ref: defaultContainer.current,
    onResize: onDocumentResize,
  });

  if (props?.hidden) return <></>;

  return (
    <Spin spinning={props?.loading ?? false}>
      <div
        className={`single-stat-card
        ${props?.size === 'small' ? 'single-stat-card-sm' : ''}
        ${props?.className}
        ${props?.clickable ? 'clickable' : ''}
        `}
        id={props?.id}
        onClick={() => {
          if (props?.clickable) {
            Emitter.emit(EventConstant.STAT_CLICK, props);
          }
        }}
        ref={defaultContainer}
        title={typeof props?.title === 'string' ? props?.title : ''}
      >
        <Statistic
          title={props.title}
          value={props?.noValue ? ' ' : value ?? '-'}
          suffix={props?.suffix}
          prefix={
            <>
              {props?.prefix}
              {props.detailsUrl && value !== '-' && (
                <IconBtn
                  type="details"
                  onClick={(e) => {
                    e.stopPropagation();
                    Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                      id: props.args?.id,
                      title: props.title,
                      args: props.args,
                      type: props?.type ? props?.type : 'dmr',
                      detailType: props?.detailType ?? '',
                      detailsUrl: props.detailsUrl,
                    });
                  }}
                />
              )}
            </>
          }
        />
        {_.isNumber(footValue) ||
        (footValue && footValue !== '-') ||
        props?.footerNode ||
        (rightFootValue && rightFootValue !== '-') ? (
          <>
            <Divider style={{ margin: '5px 0 5px 0' }} />
            <div className="single-stat-card-footer">
              {props?.footerNode ??
                (footValue ? (
                  <span>
                    {props.footerTitle}：{footValue ?? '-'}
                  </span>
                ) : null)}
              {!isEmptyValues(props?.rightFootValue) && (
                <span>
                  {props?.rightFooterTitle}：{rightFootValue}
                </span>
              )}
            </div>
          </>
        ) : null}
      </div>
    </Spin>
  );
};

export default SingleStat;
