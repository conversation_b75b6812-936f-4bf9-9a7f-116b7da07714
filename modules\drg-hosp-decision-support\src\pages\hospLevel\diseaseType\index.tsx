import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { UniSelect } from '@uni/components/src';
import { Col, Space, Row, Tabs, Select } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import SingleColumnTable from '@/components/SingleColumnTable/index';
import _ from 'lodash';
import { DiseaseBmChartSelectOptions, NormalStat } from './constants';
import { uniCommonService } from '@uni/services/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import '../index.less';
import DrillDownDiseaseStructure from '@/components/DrillDownDiseaseStructure/index';
import Stats from '@/components/stats/index';
import BCGMatrixAndTable from '@/components/BCGMatrixAndTable/index';
import {
  DiseaseTypeDefaultOpts,
  DiseaseTypeCliDeptAxisOpts,
  DiseaseTypeDoctorAxisOpts,
  DiseaseTypeMedTeamAxisOpts,
} from '@/constants';
import BmTable from '@/components/BmTable/index';
import { RespVO } from '@uni/commons/src/interfaces';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';

const DiseaseType = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('diseaseStructure');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedItem, setSelectedItem] = useState(undefined);

  const [selectOpts, setSelectOpts] = useState([]);

  const [selectedDoctorValue, setSelectedDoctorValue] = useState(undefined);

  useEffect(() => {
    setSelectedDoctorValue(dictData?.['DoctorType']?.at(0)?.Code);
  }, [dictData?.['DoctorType']]);

  // tab 使用下拉框数据
  const {
    data: BundledData,
    loading: ADrgCompositionLoading,
    run: ADrgCompositionReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        'Api/v2/Drgs/HospADrgComposition/BundledADrgComposition',
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.length) {
            setSelectOpts(
              res.data?.map((d) => ({
                ...d,
                label: `${d?.ADrgName}`,
              })),
            );
            // 默认把第一个设置为selected
            if (!selectedItem) {
              setSelectedItem(
                res?.data?.at(0),
                // _.maxBy(res?.data, function (o) {
                //   return o.PatCnt;
                // }),
              );
            }
          }
          return res?.data;
        }
        setSelectOpts([]);
        return [];
      },
    },
  );

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
    };
    setTableParams(params);
    ADrgCompositionReq(params);
  }, [dateRange, hospCodes]);

  useEffect(() => {
    Emitter.on(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK, (record) => {
      setSelectedItem(record);
      setActiveKey('statistic');
    });

    return () => {
      Emitter.off(EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK);
    };
  }, []);

  let tabItems = [
    {
      key: 'diseaseStructure',
      label: '病种结构',
      children: (
        <DrillDownDiseaseStructure
          tableParams={tableParams}
          filterColumns={[
            'ADrgName',
            'PatCnt',
            'PatRatio',
            'AvgRw',
            'AvgInPeriod',
            'AvgTotalFee',
          ]}
          compositionApi="Api/v2/Drgs/HospADrgComposition/BundledADrgComposition"
          detailAction={(record) => {
            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
              title: record?.ADrgName,
              args: {
                ...tableParams,
                VersionedADrgCode: record?.VersionedADrgCode,
              },
              detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

              dictData: dictData, // 传入
            });
          }}
        />
      ),
    },
    {
      key: 'statistic',
      label: '全院综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              api={`Api/v2/Drgs/HospADrgComposition/BundledADrgComposition`}
              trendApi={`Api/v2/Drgs/HospADrgComposition/ADrgCompositionTrend`}
              columns={NormalStat}
              defaultSelectItem={'PatCnt'}
              type="col-xl-8"
              tabKey={activeKey}
              chartHeight={300}
              useGlobalState
              level="adrg"
              selectedTableItem={selectedItem}
            />
          </Col>
          <SingleColumnTable
            title="全院分布"
            args={{
              api: 'Api/v2/Drgs/HospADrgComposition/ADrgCompositionByHosp',
              extraApiArgs: {
                VersionedADrgCode: selectedItem?.VersionedADrgCode,
              },
            }}
            tableParams={tableParams}
            dictData={dictData}
            category="HospName"
            type="table"
            visibleValueKeys={['HospName', 'PatCnt']}
            colSpan={{ span: 24 }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.HospName,
                args: {
                  Sdate: dateRange?.at(0),
                  Edate: dateRange?.at(1),
                  HospCode: [record?.HospCode],
                  VersionedADrgCode: selectedItem?.VersionedADrgCode,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                dictData: dictData, // 传入
              });
            }}
          />
          {/* <Col span={6}>
            <BCGMatrixAndTable
              title="全院效率"
              tableTitle="全院分布"
              type="hosp"
              emitter={EventConstant.HOSP_TABLE_ROW_CLICK}
              category="HospName"
              listValueKey="PatCnt"
              axisOpts={HospAxisOpts}
              defaultAxisOpt={DiseaseTypeDefaultOpts}
              args={{
                api: 'Api/Drgs/HospADrgComposition/ADrgCompositionByHosp',
                columns: [],
                extraApiArgs: {
                  VersionedADrgCodes: selectedItem?.VersionedADrgCode ? [selectedItem?.VersionedADrgCode] : [],
                },
              }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.HospName,
                  args: {
                    Sdate: dateRange?.at(0),
                    Edate: dateRange?.at(1),
                    HospCode: [record?.HospCode],
                    VersionedADrgCodes: selectedItem?.VersionedADrgCode ? [selectedItem?.VersionedADrgCode] : [],
                  },
                  
                  dictData: dictData, // 传入
                });
              }}
            />
          </Col> */}
        </Row>
      ),
    },
    {
      key: 'hosp_deptAnalysis',
      label: '科室综合分析',
      children: (
        <Row gutter={[16, 16]}>
          {/* <Col span={24}>
            <BCGMatrixAndTable
              title="绩效科室效率"
              tableTitle="绩效科室分布"
              type="MajorPerfDept"
              emitter={EventConstant.MAJOR_PERF_DEPT_TABLE_ROW_CLICK}
              category="MajorPerfDeptName"
              axisOpts={HospAxisOpts}
              defaultAxisOpt={DiseaseTypeDefaultOpts}
              args={{
                // TODO
                api: undefined,
                columns: [],
                extraApiArgs: {
                  VersionedADrgCodes: selectedItem?.VersionedADrgCode ? [selectedItem?.VersionedADrgCode] : [],
                },
              }}
            />
          </Col> */}
          <Col span={24}>
            <BCGMatrixAndTable
              title="临床科室效率"
              tableTitle="临床科室分布"
              // type="hosp"
              emitter={EventConstant.DEPT_TABLE_ROW_CLICK}
              category="CliDeptName"
              listValueKey="PatCnt"
              axisOpts={DiseaseTypeCliDeptAxisOpts}
              defaultAxisOpt={DiseaseTypeDefaultOpts}
              args={{
                api: 'Api/v2/Drgs/CliDeptADrgComposition/ADrgCompositionByCliDept',
                extraApiArgs: {
                  VersionedADrgCodes: selectedItem?.VersionedADrgCode
                    ? [selectedItem?.VersionedADrgCode]
                    : [],
                },
                columns: [],
              }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.CliDeptName,
                  args: {
                    ...tableParams,
                    CliDepts: [record?.CliDept],
                    VersionedADrgCode: selectedItem?.VersionedADrgCode,
                  },
                  detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                  dictData: dictData, // 传入
                });
              }}
            />
          </Col>
        </Row>
      ),
    },
    {
      key: 'hosp_medTeamAnalysis',
      label: '医疗组综合分析',
      children: (
        <Col span={24}>
          <BCGMatrixAndTable
            title="医疗组效率"
            tableTitle="医疗组分布"
            type="hosp"
            emitter={EventConstant.MED_TEAM_TABLE_ROW_CLICK}
            category="MedTeamName"
            listValueKey="PatCnt"
            axisOpts={DiseaseTypeMedTeamAxisOpts}
            defaultAxisOpt={DiseaseTypeDefaultOpts}
            args={{
              api: 'Api/v2/Drgs/MedTeamADrgComposition/ADrgCompositionByMedTeam',
              extraApiArgs: {
                VersionedADrgCode: selectedItem?.VersionedADrgCode,
              },
              columns: [],
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.MedTeamName,
                args: {
                  ...tableParams,
                  MedTeams: record?.MedTeam ? [record?.MedTeam] : [],
                  VersionedADrgCode: selectedItem?.VersionedADrgCode,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                dictData: dictData, // 传入
              });
            }}
          />
        </Col>
      ),
    },
    {
      key: 'hosp_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <Col span={24}>
          <BCGMatrixAndTable
            title="医生效率"
            tableTitle="医生分布"
            type="hosp"
            emitter={EventConstant.DOCTOR_TABLE_ROW_CLICK}
            category="DoctorName"
            listValueKey="PatCnt"
            axisOpts={DiseaseTypeDoctorAxisOpts}
            defaultAxisOpt={DiseaseTypeDefaultOpts}
            args={{
              api: 'Api/v2/Drgs/DoctorADrgComposition/ADrgCompositionByDoctor',
              extraApiArgs: {
                VersionedADrgCode: selectedItem?.VersionedADrgCode,
                DoctorType: (
                  selectedDoctorValue as string
                )?.toLocaleUpperCase(),
              },
              columns: [],
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.DoctorName,
                args: {
                  ...tableParams,
                  DoctorCodes: [record?.DoctorCode],
                  DoctorType: (
                    record?.DoctorType as string
                  )?.toLocaleUpperCase(),
                  VersionedADrgCode: selectedItem?.VersionedADrgCode,
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                dictData: dictData, // 传入
              });
            }}
          />
        </Col>
      ),
    },
    {
      key: 'hosp_BmAnalysis',
      label: '标杆值',
      children: (
        <Col span={24}>
          <BmTable
            tableParams={tableParams}
            bundleData={
              selectedItem
                ? [
                    BundledData?.find(
                      (data) =>
                        data?.VersionedADrgCode ===
                        selectedItem?.VersionedADrgCode,
                    ),
                  ]
                : []
            }
            extraApiArgs={{
              VersionedADrgCode: selectedItem?.VersionedADrgCode,
            }}
            api="Api/v2/Drgs/HospADrgComposition/ADrgCompositionHospBm"
            BmChartSelectOptions={DiseaseBmChartSelectOptions}
          />
        </Col>
      ),
    },
  ];

  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
        tabBarExtraContent={{
          right: (
            <>
              <Space>
                {activeKey !== 'diseaseStructure' && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <label>当前病种：</label>
                    <UniSelect
                      width={300}
                      showSearch
                      dataSource={selectOpts}
                      value={selectedItem?.VersionedADrgCode}
                      onChange={(value) => {
                        setSelectedItem(
                          selectOpts?.find(
                            (d) => d?.VersionedADrgCode === value,
                          ),
                        );
                      }}
                      allowClear={false}
                      optionNameKey={'label'}
                      optionValueKey={'VersionedADrgCode'}
                      enablePinyinSearch={true}
                      fieldNames={{
                        label: 'ADrgName',
                        value: 'VersionedADrgCode',
                      }}
                    />
                  </div>
                )}
                {activeKey === 'hosp_doctorAnalysis' && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <label>医生类型：</label>
                    <Select
                      options={dictData?.['DoctorType']}
                      style={{ width: '200px' }}
                      fieldNames={{ value: 'Code', label: 'Name' }}
                      placeholder="请选择"
                      allowClear={false}
                      value={selectedDoctorValue}
                      onChange={(value) => {
                        setSelectedDoctorValue(value);
                      }}
                    />
                  </div>
                )}
              </Space>
            </>
          ),
        }}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default DiseaseType;
