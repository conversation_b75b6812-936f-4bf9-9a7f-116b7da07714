import { isEmptyValues } from '@uni/utils/src/utils';
import { metricParamsProcessor } from '@/pages/combine-query/processor';
import { dateRangeValueProcessor } from '@uni/components/src/date-range-with-type';
import { uniCombineQueryService } from '@uni/services/src/combineQueryService';
import { RespVO } from '@uni/commons/src/interfaces';

export const autoSumAndAvg = async (
  query: string,
  detailContainerRef: any,
  metricContainerRef: any,
  setSummaryData: any,
) => {
  // Avg__InPeriod: 7.418656049402779
  // PatCnt: 467194
  // Proportion: "1"
  // Sum__InPeriod: 913014

  let sumAvgProps = detailContainerRef.current?.getAutoSumAndAvgProps();

  let detailTableColumns = sumAvgProps?.columns?.filter((item) => {
    return sumAvgProps?.columnsState?.[item?.name]?.show === true;
  });

  console.log('autoSumAndAvg', detailTableColumns);

  let aggregationColumns = metricContainerRef?.current?.getAggregationColumns();

  let canAggregationTableColumnIds = detailTableColumns
    ?.filter((item) => {
      return item?.aggregable === true;
    })
    ?.map((item) => {
      return item?.id;
    });

  // AggregationOperator: "Max"
  // Id: "f0a6fe6c-365a-45ba-a9c4-108a353b7f79"

  let outputColumns = [];

  aggregationColumns
    ?.filter((item) => canAggregationTableColumnIds?.includes(item.id))
    ?.forEach((item) => {
      if (item?.allowedAggregations?.includes('Avg')) {
        if (
          outputColumns?.filter(
            (item) =>
              item?.AggregationOperator === 'Avg' && item?.Id === item?.id,
          ).length === 0
        ) {
          outputColumns.push({
            Id: item?.id,
            AggregationOperator: 'Avg',
          });
        }
      }

      if (item?.allowedAggregations?.includes('Sum')) {
        if (
          outputColumns?.filter(
            (item) =>
              item?.AggregationOperator === 'Sum' && item?.Id === item?.id,
          ).length === 0
        ) {
          outputColumns.push({
            Id: item?.id,
            AggregationOperator: 'Sum',
          });
        }
      }
    });

  // 请求GetStats
  let data: any = {
    BasicArgs: sumAvgProps?.BasicArgs,
  };

  if (!isEmptyValues(query)) {
    data['expr'] = query;
  }

  data = {
    ...data,
    outputColumns: outputColumns,
  };

  if (isEmptyValues(outputColumns)) {
    setSummaryData([]);
    return;
  }

  let autoSumAvgResponse: RespVO<any> = await uniCombineQueryService(
    'Api/DmrAnalysis/ComboQuery/GetStats',
    {
      method: 'POST',
      requestType: 'json',
      data: data,
    },
  );

  if (
    autoSumAvgResponse?.code === 0 &&
    autoSumAvgResponse?.statusCode === 200
  ) {
    setSummaryData(autoSumAvgResponse?.data?.data);
  } else {
    setSummaryData([]);
  }
};
