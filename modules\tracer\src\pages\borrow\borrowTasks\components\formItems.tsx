import { SigninType } from '@/Constants';
import {
  ProFormDependency,
  ProFormGroup,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Col } from 'antd';

export const BorrowFormItems = (barCodeRef) => [
  {
    name: '<PERSON>rrow<PERSON>',
    title: '借阅者',
    dataType: 'select',
    rules: [{ required: true }],
    // opts: borrowerOpts,
    dictModuleGroup: 'Mr',
    dictModule: 'Employee',
    visible: true,
  },
  {
    name: 'Purpose',
    title: '借阅目的',
    dataType: 'select',
    rules: [{ required: true }],
    // opts: purposeOpts,
    dictModuleGroup: 'Mr',
    dictModule: 'BorrowPurpose',
    visible: true,
  },
  {
    name: 'BorrowerOrg',
    title: '借阅机构',
    dataType: 'select',
    rules: [{ required: true }],
    // opts: organizationOpts,
    dictModuleGroup: 'Mr',
    dictModule: '<PERSON>rrowerOrg',
    visible: true,
  },
  // {
  //   name: 'Lender',
  //   title: '操作人员',
  //   dataType: 'select',
  //   rules: [{ required: true }],
  //   opts: lenderOpts,
  //   visible: true,
  // },
  {
    name: 'ContactInfo',
    title: (
      <span style={{ display: 'inline-block', marginLeft: '11px' }}>
        联络方式
      </span>
    ),
    dataType: 'text',
    // rules: [{ required: true }],
    visible: true,
  },
  {
    name: 'Remark',
    title: (
      <span style={{ display: 'inline-block', marginLeft: '11px' }}>备注</span>
    ),
    dataType: 'textarea',
    visible: true,
  },
  // {
  //   title: '病案标识',
  //   dataType: 'text',
  //   name: 'BarCode',
  //   placeholder: '病案号/住院号/条码号(扫码)',
  //   rules: [{ required: true }],
  //   fieldProps: {
  //     ref: barCodeRef,
  //   },
  //   visible: true,
  // },
  {
    custom: true,
    dataType: 'FormGroup',
    visible: true,
    render: (
      // <Col span={24} className="form_group_test">
      <ProFormGroup grid>
        <Col flex={'92px'}>
          <ProFormSelect
            name="SignType"
            // colProps={{ flex: '100px' }}
            allowClear={false}
            initialValue={{
              label: SigninType[0].title,
              value: SigninType[0].value,
            }}
            fieldProps={{
              labelInValue: true,
              fieldNames: {
                label: 'title',
                value: 'value',
              },
              style: { width: '92px' },
            }}
            rules={[{ required: true }]}
            options={SigninType as any[]}
          />
        </Col>
        <Col flex={'auto'}>
          <ProFormDependency name={['SignType']}>
            {({ SignType }) => {
              return (
                <ProFormText
                  // colProps={{ flex: 'auto' }}
                  name="BarCode"
                  placeholder={
                    SignType?.value === 'BarCode'
                      ? '条码号(扫码)'
                      : `请输入${SignType?.label}`
                  }
                  fieldProps={{
                    ref: barCodeRef,
                  }}
                  rules={[{ required: true }]}
                />
              );
            }}
          </ProFormDependency>
        </Col>
      </ProFormGroup>
      // </Col>
    ),
  },
];
