import { Col, message, Modal, Row, Space } from 'antd';
import _ from 'lodash';
import { useReducer, useState, Reducer, useRef, useEffect } from 'react';
import {
  IReducer,
  IModalState,
  ITableState,
} from '@uni/reducers/src/interface';
import {
  InitTableState,
  TableAction,
  modalReducer,
  tableReducer,
  ModalAction,
} from '@uni/reducers/src';
import {
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
  StepsForm,
} from '@uni/components/src/pro-form';
import { Emitter } from '@uni/utils/src/emitter';
import {
  defaultDatasourcePicks,
  qualityControlRuleEventConstants,
  StatsAnalysisEventConstant,
} from '../../constants';
import { useRequest, useModel } from 'umi';
import { uniCommonService, insurMetaDataService } from '@uni/services/src';
import { DictionaryItem, RespVO } from '@uni/commons/src/interfaces';
import { recurrenceFindOne } from '../../utils';
import { v4 as uuidv4 } from 'uuid';
import ConditionForm from '../conditionForm';
import UniEditableTable from '@uni/components/src/table/edittable';
import { SceneColumns } from './columns';
import TestModal from '../testModal';
import { generateExpression } from '../../utils';
import { mockConditionTemplate } from '../conditionForm/constants';
import md5 from 'crypto-js/md5';
import SameFormPart from '../sameFormPart/index';
import CustomizedJsonLogic from '../customizedJsonLogicModal/index';
import { useAsyncEffect } from 'ahooks';
export interface IFormModalProps {
  editValueObj?: any;
  editableTemplates?: any[];
  treeData?: any[];
}

const FormModal = ({
  editValueObj,
  editableTemplates,
  treeData,
}: IFormModalProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [ModalState, ModalDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer<IModalState<any>>>
  >(modalReducer, {
    visible: false,
    record: undefined,
    actionType: 'Create',
  });

  const [ErrorLevelTableState, ErrorLevelTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, {
    ...InitTableState,
  });

  const formMapRef = useRef<
    React.MutableRefObject<ProFormInstance<any> | undefined>[]
  >([]);
  const [stepCurrent, setStepCurrent] = useState(0);
  // 判断value有没有变过?
  const hasFirstStepChanged = useRef(false);
  const ruleCode = useRef(undefined);

  const conditionTabActiveKey = useRef('default');

  // 新增/更新 Expr规则列表
  const { loading: upsertExprRuleReqLoading, run: upsertExprRuleReq } =
    useRequest(
      (data) => {
        return uniCommonService(`Api/Sys/QualitySys/UpsertExprRule`, {
          method: 'POST',
          requestType: 'json',
          data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<any>) => {
          return response;
        },
        onSuccess: (res, params) => {
          // params?.at(0).RuleCode === ruleCode
          ruleCode.current = params?.at(0).RuleCode;
        },
      },
    );

  useEffect(() => {
    getSelectDataSource('CheckErrorType');

    // add
    Emitter.on(qualityControlRuleEventConstants.ADD_BY_JSONLOGIC, () => {
      ModalDispatch({
        type: ModalAction.change,
        payload: {
          visible: true,
          actionType: 'Create',
        },
      });
    });

    // edit
    Emitter.on(qualityControlRuleEventConstants.EDIT_BTN_CLK, (record) => {
      ModalDispatch({
        type: ModalAction.change,
        payload: {
          visible: true,
          actionType: 'Edit',
          record,
        },
      });
    });

    return () => {
      Emitter.off(qualityControlRuleEventConstants.ADD_BY_JSONLOGIC);
      Emitter.off(qualityControlRuleEventConstants.EDIT_BTN_CLK);
    };
  }, []);

  useEffect(() => {
    if (ModalState.record && ModalState.actionType === 'Edit') {
      // 根据DisplayStructure
      // 赋值给form
      console.log(
        'formMapRef',
        ModalState?.record?.DisplayExprStructure === 'customized',
      );
      setTimeout(() => {
        if (ModalState?.record?.DisplayExprStructure === 'customized') {
          console.log(
            'formMapRef',
            formMapRef,
            ModalState.record,
            JSON.parse(ModalState?.record?.ExtraConfig),
            {
              ...ModalState.record,
              ...JSON.parse(ModalState?.record?.ExtraConfig),
            },
          );
          formMapRef?.current?.at(0)?.current?.setFieldsValue({
            ...ModalState.record,
            ...JSON.parse(ModalState?.record?.ExtraConfig),
          });
          conditionTabActiveKey.current = 'customized';
        } else {
          let extraData = JSON.parse(ModalState.record?.DisplayExpr);
          console.log(
            'formMapRef extra',
            formMapRef,
            ModalState.record,
            extraData,
            {
              ...ModalState.record,
              ...extraData,
            },
          );
          formMapRef?.current
            ?.at(0)
            ?.current?.setFieldsValue({ ...ModalState.record, ...extraData });
        }
      }, 0);

      // 第二步的list 列表数据 仅对编辑生效
      if (ModalState.record?.Picks?.length > 0) {
        // setEditableTemplateList(ModalState.record?.Picks);
        ErrorLevelTableDispatch({
          type: TableAction.dataChange,
          payload: {
            columns: [],
            data: ModalState.record?.Picks?.slice(),
          },
        });
      }

      // 第三步 赋值ruleCode
      ruleCode.current = ModalState.record?.RuleCode;
    }
  }, [ModalState.record]);

  // 第二步使用的内容: start
  // 规则错误select Opts
  const [selectErrorLevelDataSource, setSelectErrorLevelDataSource] = useState(
    [],
  );

  // 第二步的list 列表数据 仅对新建生效
  useEffect(() => {
    if (editableTemplates?.length > 0 && ModalState.actionType === 'Create') {
      ErrorLevelTableDispatch({
        type: TableAction.dataChange,
        payload: {
          columns: [],
          data: editableTemplates.map((item) => {
            return {
              ...item,
              ErrorLevel: '2',
              IsValid: true,
            };
          }),
        },
      });
      // setEditableTemplateList();
    }
  }, [editableTemplates, ModalState]);

  // 规则错误 select opts 数据处理
  const dataSourceProcessor = (dataSource: any[]) => {
    let selectErrorLevelDataSource = [];
    dataSource.slice().forEach((item) => {
      let selectDataItem = {
        ...item,
      };

      selectDataItem['title'] = `${selectDataItem['Name']}`;

      selectErrorLevelDataSource.push(selectDataItem);
    });

    return selectErrorLevelDataSource;
  };

  const getSelectDataSource = async (module) => {
    let response: RespVO<DictionaryItem[]> = await insurMetaDataService(module);

    if (response?.code === 0) {
      if (response?.statusCode === 200) {
        setSelectErrorLevelDataSource(dataSourceProcessor([...response?.data]));
        return;
      }
    }

    setSelectErrorLevelDataSource([]);
  };

  const {
    data: updateQualityCheckPickData,
    loading: getUpdateQualityCheckPickLoading,
    run: getUpdateQualityCheckPickReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Sys/QualitySys/UpdateQualityCheckPick`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      onSuccess: (res, params) => {
        ErrorLevelTableDispatch({
          type: TableAction.singleDataChange,
          payload: {
            key: 'RuleTemplate',
            value: params?.at(0),
          },
        });
      },
      onError: (res: RespVO<any>) => {
        message.error('编辑失败，请联系管理员');
      },
    },
  );

  useEffect(
    () => {
      Emitter.on(
        qualityControlRuleEventConstants.UPDATE_QUALITY_CHECK_PICK,
        (record) => {
          console.log('UPDATE_QUALITY_CHECK_PICK', record);
          getUpdateQualityCheckPickReq({
            // rulecode + picks
            // ...obj.Picks[index],
            ...record,
            RuleCode: ruleCode.current,
          });
        },
      );

      Emitter.on(qualityControlRuleEventConstants.ACTIVE_TAB_CHANGE, (key) => {
        conditionTabActiveKey.current = key;
      });

      return () => {
        Emitter.off(qualityControlRuleEventConstants.UPDATE_QUALITY_CHECK_PICK);
        Emitter.off(qualityControlRuleEventConstants.ACTIVE_TAB_CHANGE);
      };
    },
    [
      //TODO
    ],
  );

  useEffect(() => {
    Emitter.on(qualityControlRuleEventConstants.RETURN_EXPR, (record) => {
      handleSaveOnCustomized(record);
    });

    return () => {
      Emitter.off(qualityControlRuleEventConstants.RETURN_EXPR);
    };
  }, [ModalState]);

  const handleSaveOnCustomized = (record) => {
    console.log('return expr', record);
    // 处理提交表单
    formMapRef?.current
      ?.at(0)
      ?.current?.validateFields()
      .then(async (values) => {
        // 根据是否新建判断 ruleCode是否要 哈希处理 && ruleCodeSuffix
        let node = recurrenceFindOne(
          values?.conditionTemplate,
          'value',
          mockConditionTemplate,
        );
        let ruleCodeSuffix = '';
        if (node?.result?.contents?.length > 0) {
          ruleCodeSuffix = node?.result?.ruleCodeSuffix;
        }
        const res = await upsertExprRuleReq({
          RuleId:
            ModalState?.actionType === 'Edit'
              ? ModalState?.record?.RuleId
              : undefined,
          ...values,
          DisplayExpr: JSON.stringify(record?.DisplayExpr),
          DisplayExprStructure: 'customized',
          Expr: JSON.stringify(record?.Expr),
          RuleCode:
            ModalState?.record?.RuleCode ||
            `${ruleCodeSuffix}${md5(JSON.stringify(record?.Expr))}`,
          ExprProvider: 'JsonLogic',
          ForHqmsCode: values?.ForHqmsCode ?? false,
          ForInsurCode: values?.ForInsurCode ?? false,
          checkCategory: '9', // 编码问题
          ExtraConfig: JSON.stringify({
            conditionTemplate: values?.conditionTemplate,
          }), // 存一个模板类型
        });

        setStepCurrent(1);
      });
  };

  return (
    <>
      <StepsForm
        formMapRef={formMapRef}
        current={stepCurrent}
        onCurrentChange={(cur) => {
          setStepCurrent(cur);
        }}
        onFinish={async (values) => {
          message.success('提交成功');
          ModalDispatch({
            type: ModalAction.init,
          });
          // form 重置
          formMapRef?.current?.at(0)?.current?.resetFields();
          setStepCurrent(0);

          ruleCode.current = undefined;
          conditionTabActiveKey.current = 'default';
          Emitter.emit(qualityControlRuleEventConstants.RELOAD_BY_JSONLOGIC);
        }}
        stepsFormRender={(dom, submitter) => {
          return (
            <Modal
              width={'1356px'}
              style={{ top: 20 }}
              bodyStyle={{ height: 'calc(100vh - 160px)', overflow: 'auto' }}
              className="json_expr_rule_edit_modal"
              title={
                ModalState?.actionType === 'Create'
                  ? '新增自定义质控规则'
                  : ModalState?.actionType === 'Edit'
                  ? '编辑自定义质控规则'
                  : '测试'
              }
              open={ModalState?.visible}
              okButtonProps={{
                style: {
                  display:
                    ModalState?.actionType === 'Test' ? 'none' : 'inline-block',
                },
              }}
              footer={submitter}
              destroyOnClose
              maskClosable={false}
              onCancel={(e) => {
                ModalDispatch({
                  type: ModalAction.init,
                });
                // form 重置
                formMapRef?.current?.at(0)?.current?.resetFields();
                ruleCode.current = undefined;
                conditionTabActiveKey.current = 'default';
                setStepCurrent(0);
                Emitter.emit(
                  qualityControlRuleEventConstants.RELOAD_BY_JSONLOGIC,
                );
              }}
            >
              {dom}
              {/* 测试Modal */}
              <TestModal
                formRef={formMapRef}
                ruleCode={ModalState?.record?.RuleCode}
              />
            </Modal>
          );
        }}
      >
        <StepsForm.StepForm // 得写在外面....
          name="firstStep"
          stepProps={{
            title: '规则配置',
            onClick: () => {
              setStepCurrent(0);
            },
          }}
          grid={ModalState?.actionType === 'Test' ? false : true}
          rowProps={{
            gutter: 8,
          }}
          layout={'vertical'}
          preserve={false}
          autoFocusFirstInput
          onValuesChange={(changeValues, allValues) => {
            // hack 简单处理
            if (!hasFirstStepChanged.current) {
              hasFirstStepChanged.current = true;
            }
          }}
          onFinish={async (values) => {
            console.log('onFinish', values, conditionTabActiveKey.current);
            // 根据是否新建判断 ruleCode是否要 哈希处理 && ruleCodeSuffix
            let node = recurrenceFindOne(
              values?.conditionTemplate,
              'value',
              mockConditionTemplate,
            );
            let ruleCodeSuffix = '';
            if (node?.result?.contents?.length > 0) {
              ruleCodeSuffix = node?.result?.ruleCodeSuffix;
            }
            // 使用模板的情况
            if (conditionTabActiveKey.current === 'default') {
              // displayExpr;
              let displayExpr = {
                conditionTemplate: values?.conditionTemplate,
                [values?.conditionTemplate]:
                  values?.[values?.conditionTemplate],
                extra: values?.extra,
                extraConditionOpts: values?.extraConditionOpts,
              };

              // expr
              let key = formMapRef?.current
                ?.at(0)
                ?.current.getFieldsValue()?.conditionTemplate;
              console.log('key', key);
              let diagOperData = formMapRef?.current
                ?.at(0)
                ?.current.getFieldsValue()?.[key];
              let extraData = formMapRef?.current
                ?.at(0)
                ?.current.getFieldsValue()?.extra;

              let jsonLogicExpr = generateExpression(
                key,
                diagOperData,
                extraData,
              );

              if (!jsonLogicExpr) {
                message.error('请检查条件配置');
                return false;
              }

              let data = {
                RuleId:
                  ModalState?.actionType === 'Edit'
                    ? ModalState?.record?.RuleId
                    : undefined,
                ...values,
                DisplayExpr: JSON.stringify(displayExpr),
                DisplayExprStructure: 'default',
                Expr: JSON.stringify(jsonLogicExpr),
                RuleCode:
                  ModalState?.record?.RuleCode ||
                  `${ruleCodeSuffix}${md5(JSON.stringify(jsonLogicExpr))}`,
                ExprProvider: 'JsonLogic',
                ForHqmsCode: values?.ForHqmsCode ?? false,
                ForInsurCode: values?.ForInsurCode ?? false,
                checkCategory: '9', // 编码问题
                ExtraConfig: JSON.stringify({
                  conditionTemplate: values?.conditionTemplate,
                }), // 存一个模板类型
              };
              const res = await upsertExprRuleReq(data);
              return true;
            }
            // 使用自定义的情况 此时不判断是否有变化 因为最终逻辑要丢给外部 RETURN_EXPR
            else if (conditionTabActiveKey.current === 'customized') {
              // customzie
              // 交给外部emitter处理
              Emitter.emit(
                StatsAnalysisEventConstant.STATS_ANALYSIS_COMBINE_QUERY_TABLE_SAVE,
              );
            }
          }}
        >
          {/* 第一部分，一些新增与修改的 */}
          <SameFormPart showConditionTemplate treeData={treeData} />
          <ConditionForm formMapRef={formMapRef} modalState={ModalState} />
          {/* <CustomizedJsonLogic ModalState={ModalState} /> */}
        </StepsForm.StepForm>

        <StepsForm.StepForm
          name="secondStep"
          stepProps={{
            title: '规则应用配置',
            onClick: () => {
              if (ModalState.actionType === 'Create') {
                message.info('请先完成规则配置的填写！');
                return;
              }
              if (!hasFirstStepChanged.current) {
                setStepCurrent(1);
              }
              // if (_.isEmpty(editValueObj)) return;
              // if (hasFirstStepChanged.current) {
              //   // openNotification(1);
              // } else {
              //   setStepCurrent(1);
              // }
              // setStepCurrent(1);
            },
          }}
        >
          <label className="required_label">场景配置</label>
          <UniEditableTable
            id="scene_setting"
            className="scene_setting_table"
            forceColumnsUpdate
            rowKey="RuleTemplate"
            columns={SceneColumns(selectErrorLevelDataSource)}
            dataSource={ErrorLevelTableState.data}
            recordCreatorProps={false}
          />
        </StepsForm.StepForm>
      </StepsForm>
    </>
  );
};

export default FormModal;
