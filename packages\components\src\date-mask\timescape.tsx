import { Input } from 'antd';
import {
  Options as ITimscapeOptions,
  useTimescape,
  useTimescapeRange,
} from './timescape/integrations/react';
import './index.less';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { SwapRightOutlined } from '@ant-design/icons';
import { useEffect } from 'react';
import classNames from 'classnames';

interface TimescapeProps {
  container?: string;

  className?: string | string[];

  formKey: string;

  format: string;

  value?: string;

  onValueChange?: (value: string) => void;

  showHours?: boolean;

  showMinutes?: boolean;

  showSeconds?: boolean;

  dateDisabled?: boolean;

  [name: string]: any;

  dateSeparator?: string;

  updateObj?: any;

  timescapeOptions?: ITimscapeOptions;
}

const maxWidthMap = {
  years: 34.5,
  months: 17.25,
  days: 17.25,
  hours: 17.25,
  minutes: 17.25,
  seconds: 17.25,
};

const TimeScape = (props: TimescapeProps) => {
  const { getRootProps, getInputProps, options, update } = useTimescape({
    minDate: dayjs('1900-01-01').toDate(),
    maxDate: dayjs('2060-12-31').toDate(),
    defaultDate: dayjs('2000-01-01').toDate(),
    onChangeDate: (nextDate) => {
      if (nextDate) {
        props?.onValueChange &&
          props?.onValueChange(dayjs(nextDate).format(props?.format));
      }
    },
    date:
      typeof props?.value === 'string'
        ? dayjs(props?.value)?.isValid()
          ? dayjs(props?.value).toDate()
          : undefined
        : undefined,
    container: props?.container ?? 'COMMON',
    ...(props?.timescapeOptions || {}),
  });

  useEffect(() => {
    let currentDate =
      typeof props?.value === 'string'
        ? dayjs(props?.value)?.isValid()
          ? dayjs(props?.value).toDate()
          : undefined
        : undefined;

    if (props?.updateObj) {
      // 有额外的初始内容或者需要更新的Obj
      if (options?.date !== currentDate) {
        update({
          ...options,
          ...props?.updateObj,
          date: currentDate,
        });
      } else {
        update({
          ...options,
          ...props?.updateObj,
        });
      }
    } else {
      if (options?.date !== currentDate) {
        update({
          ...options,
          date: currentDate,
        });
      }
    }
  }, [props?.updateObj, props?.value]);

  return (
    <div
      className={classNames('timescape', props?.className)}
      {...getRootProps()}
    >
      <input
        id={`formItem#${props?.formKey}#Years#TimeScape`}
        style={{
          maxWidth: `${maxWidthMap['years']}px`,
        }}
        disabled={props.dateDisabled ?? false}
        {...getInputProps('years')}
      />
      <span>{props?.dateSeparator || '-'}</span>
      <input
        id={`formItem#${props?.formKey}#Months#TimeScape`}
        style={{ maxWidth: `${maxWidthMap['months']}px` }}
        disabled={props.dateDisabled ?? false}
        {...(getInputProps('months') as any)}
      />
      <span>{props?.dateSeparator || '-'}</span>
      <input
        id={`formItem#${props?.formKey}#Days#TimeScape`}
        style={{ maxWidth: `${maxWidthMap['days']}px` }}
        disabled={props.dateDisabled ?? false}
        {...(getInputProps('days') as any)}
      />
      {props?.format?.indexOf('HH') > -1 && (
        <>
          <div className={'timescape_date_time_gap'} />
          <input
            id={`formItem#${props?.formKey}#hours#TimeScape`}
            style={{ maxWidth: `${maxWidthMap['hours']}px` }}
            disabled={props.dateDisabled ?? false}
            {...(getInputProps('hours') as any)}
          />
          {props?.showMinutes && (
            <>
              <span>:</span>
              <input
                id={`formItem#${props?.formKey}#minutes#TimeScape`}
                style={{ maxWidth: `${maxWidthMap['minutes']}px` }}
                disabled={props.dateDisabled ?? false}
                {...(getInputProps('minutes') as any)}
              />
            </>
          )}
          {props?.showSeconds && (
            <>
              <span>:</span>
              <input
                id={`formItem#${props?.formKey}#seconds#TimeScape`}
                style={{ maxWidth: `${maxWidthMap['seconds']}px` }}
                disabled={props.dateDisabled ?? false}
                {...(getInputProps('seconds') as any)}
              />
            </>
          )}
        </>
      )}
    </div>
  );
};

interface TimescapeRangeProps {
  className?: string;
  formKey?: string;
  format?: string;
  value?: [string, string];
  onValueChange?: (
    value: string | string[],
    type: 'from' | 'to' | 'both',
  ) => void;
  wrapAround?: boolean;
  showHours?: boolean;
  showMinutes?: boolean;
  showSeconds?: boolean;
  canOutRange?: boolean;
  [name: string]: any;
}

const TimeScapeRange = (props: TimescapeRangeProps) => {
  console.log('TimeScapeRange', props?.value);
  const { getRootProps, from, to } = useTimescapeRange(
    {
      from: {
        date: dayjs(props?.value?.at(0)).toDate(),
        minDate: dayjs('1900-01-01').toDate(),
        maxDate: dayjs('2060-12-31').toDate(),
        wrapAround: props?.wrapAround ?? true,
        onChangeDate: (nextFormDate) => {
          onDateChange(nextFormDate, 'from');
        },
      },
      to: {
        date: dayjs(props?.value?.at(1)).toDate(),
        minDate: dayjs('1900-01-01').toDate(),
        maxDate: dayjs('2060-12-31').toDate(),
        wrapAround: props?.wrapAround ?? true,
        onChangeDate: (nextToDate) => {
          onDateChange(nextToDate, 'to');
        },
      },
    },
    props?.canOutRange,
  );

  const onDateChange = (nextDate: Date, type: 'from' | 'to') => {
    if (nextDate) {
      let values = [];
      if (type === 'from') {
        values.push(dayjs(nextDate).format(props?.format));
        values.push(dayjs(to._manager.date).format(props?.format));
      }

      if (type === 'to') {
        values.push(dayjs(from._manager.date).format(props?.format));
        values.push(dayjs(nextDate).format(props?.format));
      }
      props?.onValueChange && props?.onValueChange(values, 'both');
    }
  };

  useEffect(() => {
    if (!props?.value || props?.value?.length !== 2) {
      // from.update({
      //   ...from.options,
      //   date: dayjs().toDate(),
      // });
      // to.update({ ...to.options, date: dayjs().toDate() });
      props?.onValueChange &&
        props?.onValueChange(
          [dayjs().format(props?.format), dayjs().format(props?.format)],
          'both',
        );
    } else if (
      props?.value?.length === 2 &&
      props?.value?.filter(Boolean)?.length < 2
    ) {
      // from.update({
      //   ...from.options,
      //   date: dayjs(props?.value?.at(0)).toDate(),
      // });
      // to.update({ ...to.options, date: dayjs(props?.value?.at(1)).toDate() });
      props?.onValueChange &&
        props?.onValueChange(
          [
            dayjs(props?.value?.at(0)).format(props?.format),
            dayjs(props?.value?.at(1)).format(props?.format),
          ],
          'both',
        );
    }
  }, [props?.value]);

  return (
    <div className="timescape_range" {...getRootProps()}>
      <div>
        <input
          id={`formItem#${props?.formKey}#Years#TimeScapeRangeFrom`}
          style={{ maxWidth: `${maxWidthMap['years']}px` }}
          {...from.getInputProps('years')}
        />
        <span>-</span>
        <input
          id={`formItem#${props?.formKey}#Months#TimeScapeRangeFrom`}
          style={{ maxWidth: `${maxWidthMap['months']}px` }}
          {...from.getInputProps('months')}
        />
        <span>-</span>
        <input
          id={`formItem#${props?.formKey}#Days#TimeScapeRangeFrom`}
          style={{ maxWidth: `${maxWidthMap['days']}px` }}
          {...from.getInputProps('days')}
        />
      </div>

      {/* <span className="separator">→</span> */}
      <SwapRightOutlined className="separator" />
      <div>
        <input
          id={`formItem#${props?.formKey}#Years#TimeScapeRangeTo`}
          style={{ maxWidth: `${maxWidthMap['years']}px` }}
          {...to.getInputProps('years')}
        />
        <span>-</span>
        <input
          id={`formItem#${props?.formKey}#Months#TimeScapeRangeTo`}
          style={{ maxWidth: `${maxWidthMap['months']}px` }}
          {...to.getInputProps('months')}
        />
        <span>-</span>
        <input
          id={`formItem#${props?.formKey}#Days#TimeScapeRangeTo`}
          style={{ maxWidth: `${maxWidthMap['days']}px` }}
          {...to.getInputProps('days')}
        />
      </div>
    </div>
  );
};

export { TimeScape, TimeScapeRange };
