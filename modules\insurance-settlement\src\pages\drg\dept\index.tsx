import { Card, Col, Row, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { useModel } from 'umi';
import {
  DeptTotalStatsColumns,
  SettleCompStatsByGrpColumns,
  TabCommonItems,
} from '../constants';
import {
  GrpDefaultOpts,
  GrpQuadrantAxisOpts,
  AdrgQuadrantAxisOpts,
  AdrgDefaultOpts,
} from '../optsConstants';
import { isEmptyValues } from '@uni/utils/src/utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import FeeCompositionAndAnalysis from '@/components/feeCompositionAndAnalysis';
import GradientChartAndTable from '@/components/gradientChartAndTable';
import IconBtn from '@uni/components/src/iconBtn';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import DrawerCardInfo from '@/pages/drg/components/drawerCardInfo';
import GradientChartAndTableAndPie from '@/components/gradientChartAndTableAndPie';
import TrendAnalysis from '@/components/trendAnalysis';
import Stats from '@/components/stats';

const DrgDeptAnalysis = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, CliDepts, wards, insurType } =
    globalState?.searchParams;

  const [activeKey, setActiveKey] = useState('statistic');
  const [requestParams, setRequestParams] = useState<any>({});
  const [drawerVisible, setDrawerVisible] = useState(undefined);
  const [selectedStatItem, setSelectedStatItem] = useState({
    contentData: 'Profit',
    dataType: 'Currency',
    title: '盈亏',
  });
  const [statsHeight, setStatsHeight] = useState<number>(295);

  useEffect(() => {
    const updateHeight = () => {
      const element = document.getElementById('dept-stats-list');
      if (element) {
        const height = element.getBoundingClientRect().height;
        if (height > 0) {
          setStatsHeight(height - 50 - 24);
        }
      }
    };

    // 初始更新
    updateHeight();

    // 创建一个观察器实例
    const observer = new MutationObserver(updateHeight);

    // 配置观察选项
    const config = { attributes: true, childList: true, subtree: true };

    // 开始观察目标节点
    const targetNode = document.getElementById('dept-stats-list');
    if (targetNode) {
      observer.observe(targetNode, config);
    }

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    if (
      (globalState?.searchParams &&
        globalState?.searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(requestParams) &&
        globalState?.searchParams?.dateRange?.length &&
        globalState?.searchParams?.CliDepts?.length)
    ) {
      let tableParams = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        CliDepts,
        Wards: wards,
        insurType,
      };
      setRequestParams(tableParams);
    }
  }, [globalState?.searchParams]);

  // stat click 由Component Stats传入
  useEffect(() => {
    Emitter.on(EventConstant.STAT_ON_CLICK_EMITTER, (record) => {
      setSelectedStatItem(record);
    });
    return () => {
      Emitter.off(EventConstant.STAT_ON_CLICK_EMITTER);
    };
  }, []);

  let tabItems = [
    {
      key: TabCommonItems.statistic.key,
      label: TabCommonItems.statistic.title,
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={12} xl={11}>
            <Row gutter={[16, 16]} id="dept-stats-list">
              <Stats
                level="dept"
                api={`Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsOfCliDept`}
                columns={DeptTotalStatsColumns}
                type="col-xl-6"
                outerTableParams={requestParams}
                onClickEmitter={EventConstant.STAT_ON_CLICK_EMITTER}
              />
            </Row>
          </Col>

          <Col xs={24} sm={24} md={24} lg={12} xl={13}>
            <TrendAnalysis
              title="科室月度变化趋势"
              height={statsHeight}
              selectedStatItem={selectedStatItem}
              requestParams={requestParams}
              dictData={globalState.dictData}
            />
          </Col>
        </Row>
      ),
    },
    {
      key: TabCommonItems.feeAnalysis.key,
      label: TabCommonItems.feeAnalysis.title,
      children: (
        <FeeCompositionAndAnalysis
          requestParams={requestParams}
          level={['dept', 'ward']}
          api={`Api/FundSupervise/LatestDrgSettleStats/FeeChargeDistribution`}
          tabKey={activeKey}
        />
      ),
    },
    {
      key: TabCommonItems.drgAnalysis.key,
      label: TabCommonItems.drgAnalysis.title,
      children: (
        <GradientChartAndTableAndPie
          args={{
            level: 'dept',
            type: 'drg',
            title: '病组效率',
            category: 'ChsDrgName',
            columns: [
              {
                dataIndex: 'operation',
                visible: true,
                width: 40,
                align: 'center',
                order: 1,
                title: '',
                render: (node, record, index) => {
                  return (
                    <IconBtn
                      type="details"
                      onClick={(e) => {
                        e.stopPropagation();
                        Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                          id: 'drg-dept-settle-stats-by-chsdrg',
                          title: record?.ChsDrgName,
                          args: {
                            ...requestParams,
                            VersionedChsDrgCodes: record?.VersionedChsDrgCode
                              ? [record?.VersionedChsDrgCode]
                              : [],
                          },
                          type: 'drg',
                          detailsUrl:
                            'FundSupervise/LatestDrgSettleStats/SettleDetails',
                          dictData: globalState?.dictData,
                        });
                      }}
                    />
                  );
                },
              },
              ...SettleCompStatsByGrpColumns,
            ],
            clickable: true,
            emitter: EventConstant.DRG_TABLE_ROW_CLICK,
            detailsTitle: '病组分布',
            axisOpts: GrpQuadrantAxisOpts,
            defaultAxisOpt: GrpDefaultOpts,
            api: 'Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByGrp',
          }}
          requestParams={requestParams}
        />
      ),
    },
    {
      key: TabCommonItems.deptAnalysis.key,
      label: '科室对比分析',
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTable
                args={{
                  clickable: false,
                  cols: 'col-xl-24',
                  title: '科室对比分析',
                  category: 'CliDeptName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      fixed: 'left',
                      order: 1,
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                id: 'drg-hosp-settle-stats-by-dept',
                                title: `${record?.CliDeptName}`,
                                args: {
                                  ...requestParams,
                                  CliDepts: record?.CliDept
                                    ? [record?.CliDept]
                                    : ['%'],
                                },
                                type: 'drg',
                                detailsUrl:
                                  'FundSupervise/LatestDrgSettleStats/SettleDetails',
                                dictData: globalState?.dictData,
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByGrpColumns,
                  ],
                  api: `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByCliDept`,
                  level: 'dept',
                }}
                noChart={true}
                requestParams={requestParams}
              />
            </Col>
          </Row>
        </>
      ),
    },
  ];

  return (
    <Card>
      <Tabs
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
      <DetailTableModal
        dictData={globalState.dictData}
        detailAction={(record) => {
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </Card>
  );
};

export default DrgDeptAnalysis;
