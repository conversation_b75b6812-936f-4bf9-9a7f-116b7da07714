import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'chevrotain';

import {
  Operator,
  And,
  Or,
  Conjunction,
  Not,
  Identifier,
  Dot,
  Comma,
  Some,
  StringLiteral,
  NumberLiteral,
  BooleanLiteral,
  LSquare,
  RSquare,
  LParenthesis,
  RParenthesis,
  allTokens,
} from './tokens.js';

class ComboConditionParser extends CstParser {
  constructor() {
    super(allTokens);

    const $ = this;

    $.RULE('subject', () => {
      $.CONSUME(Identifier);
      $.MANY(() => {
        $.CONSUME(Dot);
        $.CONSUME2(Identifier);
      });
    });

    $.RULE('array', () => {
      $.CONSUME(LSquare);
      $.OPTION(() => {
        $.SUBRULE($.value);
        $.MANY(() => {
          $.CONSUME(Comma);
          $.SUBRULE2($.value);
        });
      });
      $.CONSUME(RSquare);
    });

    $.RULE('value', () => {
      $.OR([
        { ALT: () => $.SUBRULE($.array) },
        { ALT: () => $.CONSUME(StringLiteral) },
        { ALT: () => $.CONSUME(NumberLiteral) },
        { ALT: () => $.CONSUME(BooleanLiteral) },
      ]);
    });

    $.RULE('atomRule', () => {
      $.SUBRULE($.subject);
      $.CONSUME(Operator);
      $.OPTION(() => {
        $.SUBRULE($.value);
      });
    });

    $.RULE('notRule', () => {
      $.CONSUME(Not);
      $.OR([
        { ALT: () => $.SUBRULE($.atomRule) },
        { ALT: () => $.SUBRULE($.someRule) },
      ]);
    });

    $.RULE('someRule', () => {
      $.CONSUME(Some);
      $.SUBRULE($.subject);
      $.CONSUME(LParenthesis);
      $.SUBRULE($.atomRule);
      $.MANY(() => {
        $.CONSUME(Conjunction);
        $.SUBRULE2($.atomRule);
      });
      $.CONSUME(RParenthesis);
    });

    $.RULE('parenthesisRule', () => {
      $.CONSUME(LParenthesis);
      $.SUBRULE($.rule);
      $.CONSUME(RParenthesis);
    });

    $.RULE('unaryRule', () => {
      $.OR([
        { ALT: () => $.SUBRULE($.atomRule) },
        { ALT: () => $.SUBRULE($.notRule) },
        { ALT: () => $.SUBRULE($.someRule) },
        { ALT: () => $.SUBRULE($.parenthesisRule) },
      ]);
    });

    $.RULE('andRule', () => {
      $.SUBRULE($.unaryRule);
      $.MANY(() => {
        $.CONSUME(And);
        $.SUBRULE2($.unaryRule);
      });
    });

    $.RULE('orRule', () => {
      $.SUBRULE($.andRule);
      $.MANY(() => {
        $.CONSUME(Or);
        $.SUBRULE2($.andRule);
      });
    });

    $.RULE('rule', () => {
      $.SUBRULE($.orRule);
    });

    this.performSelfAnalysis();
  }
}

export default ComboConditionParser;
