import React, { useEffect, useRef, useState } from 'react';
import { useModel } from '@@/plugin-model/useModel';
import { Row, Steps, Col, Form, Button } from 'antd';
import { ProFormInstance, StepsForm } from '@ant-design/pro-components';
// import { StepsForm } from '@ant-design/pro-components';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import BarCodeForm from '../BarCodeForm';
import BaseInfoForm from '../BaseInfoForm';

const BorrowTasksCreate = (props) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');
  const [currentStep, setCurrentStep] = useState(0);
  const formMapRef = useRef<
    React.MutableRefObject<ProFormInstance<any> | undefined>[]
  >([]);
  const formRef2 = useRef<ProFormInstance>();
  const [form1] = Form.useForm();
  const [baseInfoFormData, setBaseInfoFormData] = useState({});

  useEffect(() => {
    Emitter.on('BaseInfoForm', (values) => {
      setBaseInfoFormData(values);
    });
    return () => {
      Emitter.off('BaseInfoForm');
    };
  }, []);

  const handleSubmit = () => {
    // 获取两个表单的数据
    form1.validateFields().then(() => {
      formRef2.current.validateFields().then(() => {
        if (formRef2?.current?.getFieldValue('BarCodes')?.length) {
          props.createReq({
            ...baseInfoFormData,
            BarCodes: formRef2.current
              .getFieldValue('BarCodes')
              .map((obj) => obj?.barCode),
          });
        }
        props.setModalVisible(false);
      });
    });
  };

  const next = () => {
    // 根据当前步骤进行表单校验
    if (currentStep === 0) {
      form1.validateFields().then(() => {
        Emitter.emit('BaseInfoForm', form1.getFieldsValue());
        setCurrentStep(currentStep + 1);
      });
    }
  };

  const prev = () => {
    setCurrentStep(currentStep - 1);
  };

  return (
    <StepsForm
      stepsProps={{
        direction: 'vertical',
      }}
      formMapRef={formMapRef}
      stepsFormRender={(dom, submitter) => (
        <Row gutter={16}>
          <Col span={6}>
            <Steps direction="vertical" size="small" current={currentStep}>
              <Steps.Step title="基本信息" />
              <Steps.Step title="病案信息" />
            </Steps>
          </Col>
          <Col span={18}>
            {currentStep === 0 && (
              <Form name="step1" title="Step 1">
                <BaseInfoForm form1={form1} />
                <Button
                  style={{ float: 'right' }}
                  type="primary"
                  onClick={next}
                >
                  下一步
                </Button>
              </Form>
            )}
            {currentStep === 1 && (
              <Form name="step2" title="Step 2">
                <BarCodeForm formRef2={formRef2} />
                <Button
                  style={{ float: 'right' }}
                  type="primary"
                  onClick={handleSubmit}
                >
                  确认申请
                </Button>
                <Button
                  style={{ float: 'right' }}
                  className="mr-1"
                  onClick={prev}
                >
                  上一步
                </Button>
              </Form>
            )}
          </Col>
        </Row>
      )}
    >
      <></>
    </StepsForm>
  );
};

export default BorrowTasksCreate;
