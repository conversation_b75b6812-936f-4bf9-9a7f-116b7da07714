import React from 'react';
import { Modal, Alert } from 'antd';
import { UniTable } from '@uni/components/src';
import { ReqActionType } from '@/Constants';

interface ConfirmModalProps {
  visible: boolean;
  record: any[];
  columns: any[];
  selectedRowKeys: React.Key[];
  loading: boolean;
  modalAlert: boolean;
  onOk: () => void;
  onCancel: () => void;
  onRowSelect: (selectedRowKeys: React.Key[], selectedRows: any[]) => void;
  onRowClick: (record: any) => void;
  onAlertClose: () => void;
}

// 特定的columns 用于给用户看的更加清楚 保留的部分
export const ConfirmColumns = [
  'BarCode',
  'PatNo',
  'TrackNo',
  'PatName',
  'OutDate',
  'OutDept',
  'CliDept',
  'Doctor',
  'Nurse',
  'CanLend',
];

/**
 * 病案选择确认模态框
 */
const ConfirmModal: React.FC<ConfirmModalProps> = ({
  visible,
  record,
  columns,
  selectedRowKeys,
  loading,
  modalAlert,
  onOk,
  onCancel,
  onRowSelect,
  onRowClick,
  onAlertClose,
}) => {
  console.log('ConfirmModal', columns);
  return (
    <Modal
      title="确认病案"
      open={visible}
      width={900}
      onOk={onOk}
      okButtonProps={{
        loading: loading,
      }}
      onCancel={onCancel}
    >
      <UniTable
        id="multi_record_check"
        rowKey="BarCode"
        showSorterTooltip={false}
        loading={loading}
        columns={[
          ...columns,
          {
            dataIndex: 'CanLend',
            title: '可借阅',
            fixed: 'right',
            width: 80,
            orderable: true,
            visible: true,
            dataType: 'boolean',
          },
        ]?.map((d) => ({
          ...d,
          defaultSortOrder:
            d?.orderable && d?.orderPriority !== 0 && d?.orderMode
              ? d?.orderMode
              : null,
          sorter: false,
          orderable: false,
          orderPriority: 0,
          orderMode: null,
          showSorterTooltip: false,
        }))}
        dataSource={record}
        scroll={{ x: 'max-content' }}
        rowClassName={(record, index) => {
          return record?.CanLend ? '' : 'row-first';
        }}
        tableAlertRender={() => {
          return modalAlert ? (
            <Alert
              message="请选择一个病案"
              description="请选择一个病案再点击确认，如果没有查询到目标病案请确认信息输入正确"
              type="error"
              closable
              onClose={onAlertClose}
            />
          ) : (
            false
          );
        }}
        tableAlertOptionRender={false}
        rowSelection={{
          alwaysShowAlert: true,
          type: 'radio',
          selectedRowKeys: selectedRowKeys,
          onChange: onRowSelect,
          getCheckboxProps: (record) => ({
            disabled: !record?.CanLend,
          }),
        }}
        onRow={(record) => {
          return {
            onClick: () => {
              if (record?.CanLend) {
                onRowClick(record);
              }
            },
          };
        }}
      />
    </Modal>
  );
};

export default ConfirmModal;
