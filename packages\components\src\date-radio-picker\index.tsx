import { useSessionStorageState } from 'ahooks';
import { DatePicker, Form, Radio, Space } from 'antd';
import { RangePickerDateProps } from 'antd/lib/date-picker/generatePicker';
import dayjs from 'dayjs';
import { FC, useEffect } from 'react';
import _ from 'lodash';
import './index.less';
import Datepicker from '../picker/datepicker';

interface UniDateRadioPickerFormProps {
  form: any;
  extraFormKey: string;
  postProcess?: (currentValue, selectedValue, extra) => any;
}

export interface IUniDateRadioPickerProps
  extends Omit<RangePickerDateProps<dayjs.Dayjs>, 'value'>,
    UniDateRadioPickerFormProps {
  enabledRadioKeys?: string[];
  extra?: any;

  value: ['year' | 'quarter' | 'month' | 'date' | 'tenDays', dayjs.Dayjs[]];
  requiredStatus?: boolean;
  onBlurValueChange?: boolean;
  onPressEnterValueChange?: boolean;
  postValueProcessor?: (value: any) => string;
  onChange?: (value: any, key?: any) => void;
  onChangeValueKeys?: (values: any) => void;
  onRadioChange?: (value: any) => void;
}

const radioKeysToLabel = {
  year: '年',
  quarter: '季',
  month: '月',
  date: '日',
  tenDays: '旬',
};

function getDayjsStartOfByType(pickerType: string, dayjsInstance: any) {
  switch (pickerType) {
    case 'year':
      return dayjsInstance.startOf('year');
    case 'quarter':
      return dayjsInstance.startOf('quarter');
    case 'month':
      return dayjsInstance.startOf('month');
    case 'tenDays':
      return dayjsInstance;
  }

  return dayjsInstance;
}

function getDayjsEndOfByType(pickerType: string, dayjsInstance: any) {
  switch (pickerType) {
    case 'year':
      return dayjsInstance.endOf('year');
    case 'quarter':
      return dayjsInstance.endOf('quarter');
    case 'month':
      return dayjsInstance.endOf('month');
    case 'tenDays': {
      const currentDayInMonth = dayjsInstance.date();
      if (currentDayInMonth === 1) {
        return dayjsInstance.add(9, 'day');
      } else if (currentDayInMonth === 11) {
        return dayjsInstance.add(9, 'day');
      } else {
        return dayjsInstance.endOf('month');
      }
    }
  }

  return dayjsInstance;
}

const DateRadioPicker: FC<IUniDateRadioPickerProps> = ({
  value,
  status,
  requiredStatus,
  onChange,
  onChangeValueKeys,
  onRadioChange,

  enabledRadioKeys,
  extra,
}) => {
  let radioKeys =
    enabledRadioKeys && enabledRadioKeys?.length > 0
      ? enabledRadioKeys
      : Object.keys(radioKeysToLabel);

  let radioValue =
    extra?.form && extra?.radioFormKey
      ? Form.useWatch(extra?.radioFormKey, extra?.form)
      : value?.at(0);
  let dateValues = extra?.form
    ? [
        typeof value?.at(0) === 'string'
          ? dayjs(value?.at(0) as string)
          : (value?.at(0) as any),
        typeof value?.at(1) === 'string'
          ? dayjs(value?.at(1) as string)
          : (value?.at(1) as any),
      ]
    : [value?.at(1)?.at(0) as any, value?.at(1)?.at(1) as any];

  let RadioGroupWrapper = (props: any) => <>{props.children}</>;

  if (extra?.form) {
    RadioGroupWrapper = Form.Item;
  }

  const changeHandler = (dateTangeValues, dateType) => {
    console.log('changeHandler', dateTangeValues, radioValue);
    return dateTangeValues?.map((valueItem, index) => {
      if (valueItem) {
        if (index === 0) {
          return getDayjsStartOfByType(
            dateType?.toString(),
            dayjs(valueItem),
          ).format('YYYY-MM-DD');
        }
        if (index === 1) {
          return getDayjsEndOfByType(
            dateType?.toString(),
            dayjs(valueItem),
          ).format('YYYY-MM-DD');
        }
      }
      return valueItem;
    });
  };

  return (
    <div className="date-radio-picker d-flex">
      {extra?.label && (
        <label>
          {extra?.required && <span className={'require-mark'}>*</span>}
          {extra?.label}：
        </label>
      )}

      <RadioGroupWrapper name={extra?.radioFormKey}>
        <Radio.Group
          // size="small"
          value={radioValue}
          onChange={(e) => {
            if (extra?.form && extra?.radioFormKey) {
              extra?.form.setFieldValue(extra?.radioFormKey, e.target.value);
              onChange(changeHandler(value, e.target.value));
            } else {
              onChangeValueKeys({
                dateType: e.target.value,
                dateRange: changeHandler(value, e.target.value),
              });
            }
          }}
        >
          {radioKeys
            ?.filter((key) => {
              return radioKeysToLabel[key] !== undefined;
            })
            ?.map((key) => {
              return (
                <Radio.Button value={key}>{radioKeysToLabel[key]}</Radio.Button>
              );
            })}
        </Radio.Group>
      </RadioGroupWrapper>

      <Datepicker.RangePicker
        picker={radioValue}
        value={dateValues}
        onChange={(v) => {
          console.log(
            'Datepicker.RangePicker',
            v,
            radioValue,
            value,
            extra?.form,
          );
          if (extra?.form !== undefined) {
            onChange(changeHandler(v, radioValue));
          } else {
            onChangeValueKeys({
              dateType: value?.at(0),
              dateRange: v,
            });
          }
        }}
        status={
          status ??
          (requiredStatus
            ? _.isArray(value?.at(1))
              ? value?.at(1)?.length > 0
                ? ''
                : 'error'
              : value?.at(1)
              ? ''
              : 'error'
            : '')
        }
      />
    </div>
  );
};

export default DateRadioPicker;
