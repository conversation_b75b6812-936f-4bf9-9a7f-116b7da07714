import { useEffect, useState } from 'react';
import { Col, Row, Tabs } from 'antd';
import './index.less';
import { BasePageProps } from '@uni/commons/src/interfaces';
import _ from 'lodash';
import { useModel } from '@@/plugin-model/useModel';
import Stats from '@/components/statsWithTrend';
import { TotalStatsColumns } from '../constants';
import SingleColumnTable from '@/components/singleColumnTable';
import PieTreeTrend from './components/pieTreeTrend/index';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';

interface DmrManagementProps extends BasePageProps {}

const CheckResultAnalysis = (props: DmrManagementProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, errorLevel, CliDepts } = globalState.searchParams;
  const [tableParams, setTableParams] = useState(undefined);

  const [clickedRecord, setClickedRecord] = useState(undefined);
  const [pieTrendParams, setPieTrendParams] = useState(undefined);

  useEffect(() => {
    if (dateRange?.length && hospCodes?.length && CliDepts?.length) {
      let params = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        ErrorLevel: errorLevel,
        HospCode: hospCodes,
        CliDepts: CliDepts,
      };
      setTableParams(params);
    }
  }, [dateRange, hospCodes, errorLevel, CliDepts]);

  useEffect(() => {
    if (clickedRecord) {
      setPieTrendParams({
        ...tableParams,
        doctorTypes: clickedRecord?.DoctorType
          ? [clickedRecord?.DoctorType]
          : undefined,
        doctorCodes: clickedRecord?.Doctor
          ? [clickedRecord?.Doctor]
          : undefined,
        CliDepts: clickedRecord?.CliDept ? [clickedRecord?.CliDept] : undefined,
      });
    }
  }, [clickedRecord]);

  // tab
  const [activeKey, setActiveKey] = useState('Dept');
  let tabItems = [
    {
      key: 'Dept',
      label: '科室统计分析',
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <Stats
                api={`Api/Emr/QcResultStats/GetOverviewBundle`}
                trendApi={`Api/Emr/QcResultStats/GetOverviewTrends`}
                columns={TotalStatsColumns}
                type="col-xl-12"
                level={'dept'}
                tabKey={activeKey}
                chartHeight={320}
                useGlobalState
                defaultSelectItem="RecordCnt"
              />
            </Col>
            <Col
              xs={24}
              sm={24}
              md={24}
              lg={24}
              xl={24}
              style={{ padding: '0' }}
            >
              <SingleColumnTable
                title="科室质控结果"
                args={{
                  api: 'Api/Emr/QcResultStats/QcStatsOfCliDept',
                }}
                isShowDetails
                detailAction={(record) => {
                  Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                    title: '',
                    args: {
                      ...tableParams,
                      cliDepts: [record?.CliDept],
                      ...record?.args,
                    },
                    detailsUrl: 'Emr/QcResultStats/GetQcCards',
                    dictData: globalState?.dictData,
                  });
                }}
                tableParams={tableParams}
                dictData={globalState?.dictData}
                category="CliDeptName"
                type="table"
                visibleValueKeys={[
                  'CliDeptName',
                  'RecordCnt',
                  'QcRecordCnt',
                  'RecordCheckCount',
                  'RecordCheckRate',
                  'CodeCheckCount',
                  'CodeCheckRate',
                  'ErrorCount',
                  'ErrorRate',
                  'ForceErrorCount',
                  'ForceErrorRate',
                  'IndicativeErrorCount',
                  'IndicativeErrorRate',
                ]}
                colSpan={{ span: 24 }}
                clickedRecord={clickedRecord}
                setClickedRecord={setClickedRecord}
                isCliDepts
              />
            </Col>
            <Col span={24}>
              <h3>
                <b>
                  {clickedRecord?.CliDept
                    ? `${clickedRecord?.CliDeptName} 统计分析`
                    : '请选择科室'}
                </b>
              </h3>
            </Col>
            <Col xs={24} sm={24} md={24} lg={24} xl={24}>
              <PieTreeTrend
                tableParams={pieTrendParams}
                detailAction={(record) => {
                  Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                    title: '',
                    args: {
                      ...pieTrendParams,
                      ...record?.args,
                    },
                    detailsUrl: 'Emr/QcResultStats/GetQcCards',
                    dictData: globalState?.dictData,
                  });
                }}
              />
            </Col>
          </Row>
        </>
      ),
    },
    {
      key: 'Doctor',
      label: '医生统计分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <SingleColumnTable
              title="医生质控结果"
              args={{
                api: 'Api/Emr/QcResultStats/QcStatsOfDoctor',
              }}
              tableParams={tableParams}
              dictData={globalState?.dictData}
              category="Doctor"
              type="table"
              isShowDetails
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: '',
                  args: {
                    ...tableParams,
                    doctorTypes: record?.DoctorType
                      ? [record?.DoctorType]
                      : undefined,
                    doctorCodes: record?.Doctor ? [record?.Doctor] : undefined,
                    ...record?.args,
                  },
                  detailsUrl: 'Emr/QcResultStats/GetQcCards',
                  dictData: globalState?.dictData,
                });
              }}
              visibleValueKeys={[
                'DoctorName',
                'RecordCnt',
                'QcRecordCnt',
                'RecordCheckCount',
                'RecordCheckRate',
                'CodeCheckCount',
                'CodeCheckRate',
                'ErrorCount',
                'ErrorRate',
                'ForceErrorCount',
                'ForceErrorRate',
                'IndicativeErrorCount',
                'IndicativeErrorRate',
              ]}
              colSpan={{ span: 24 }}
              select={{
                dataKey: 'DoctorType',
                valueKey: 'DoctorType',
                allowClear: false,
                defaultSelect: true,
              }}
              clickedRecord={clickedRecord}
              setClickedRecord={setClickedRecord}
            />
          </Col>
          <Col span={24}>
            <h3>
              <b>
                {clickedRecord?.Doctor
                  ? `${clickedRecord?.DoctorName} 统计分析`
                  : '请选择医生'}
              </b>
            </h3>
          </Col>
          <Col span={24}>
            <PieTreeTrend
              tableParams={pieTrendParams}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: '',
                  args: {
                    ...pieTrendParams,
                    ...record?.args,
                  },
                  detailsUrl: 'Emr/QcResultStats/GetQcCards',
                  dictData: globalState?.dictData,
                });
              }}
            />
          </Col>
        </Row>
      ),
    },
  ];

  return (
    <>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
    </>
  );
};

export default CheckResultAnalysis;
