import { useEffect, useState } from 'react';
import { useModel } from 'umi';
import { Col, Row, Tabs, Select } from 'antd';
import { useDeepCompareEffect } from 'ahooks';
import _ from 'lodash';
import { MedTeamAxisOpts, CmiStat, DefaultOpts } from './constants';
import Stats from '@/components/stats/index';
import BCGMatrixAndTable from '@/components/BCGMatrixAndTable/index';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import DiseaseStructure from '@/pages/hospLevel/hospCmi/components/diseaseStructure/index';
import RwWeights from '@/pages/hospLevel/hospCmi/components/rwWeights/index';
import { DoctorAxisOpts } from '@/constants';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import DrawerCardInfo from '@/components/drawerCardInfo/index';

const MedTeamCmi = () => {
  const {
    globalState: { dictData, searchParams },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  let { dateRange, hospCodes, MedTeams } = searchParams;

  const [tableParams, setTableParams] = useState(undefined);
  // tab
  const [activeKey, setActiveKey] = useState('statistic');
  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedDoctorValue, setSelectedDoctorValue] = useState(undefined);

  useEffect(() => {
    setSelectedDoctorValue(dictData?.['DoctorType']?.at(0)?.Code);
  }, [dictData?.['DoctorType']]);

  // 处理tableParams
  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes,
      MedTeams,
    };
    setTableParams(params);
  }, [dateRange, hospCodes, MedTeams]);

  let tabItems = [
    {
      key: 'statistic',
      label: '综合分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Stats
              level="medTeam"
              api={`Api/v2/Drgs/MedTeamCmi/BundledCmi`}
              trendApi={`Api/v2/Drgs/MedTeamCmi/CmiTrend`}
              columns={CmiStat}
              defaultSelectItem={'Cmi'}
              type="col-xl-6"
              tabKey={activeKey}
              useGlobalState
            />
          </Col>
          <Col span={24}>
            <BCGMatrixAndTable
              title="医疗组效率"
              tableTitle="医疗组CMI"
              type="medTeam"
              emitter={EventConstant.MED_TEAM_TABLE_ROW_CLICK}
              category="MedTeamName"
              axisOpts={MedTeamAxisOpts}
              defaultAxisOpt={DefaultOpts}
              args={{
                api: 'Api/v2/Drgs/MedTeamCmi/CmiByMedTeam',
                columns: [],
                extraApiArgs: {
                  // MedTeams: [],
                },
              }}
              detailAction={(record) => {
                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                  title: record?.MedTeamName,
                  args: {
                    ...tableParams,
                    MedTeams: [record?.MedTeam],
                  },
                  detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                  dictData: dictData, // 传入
                });
              }}
            />
          </Col>
          <RwWeights
            tableParams={tableParams}
            rwDistributionApi="Api/v2/Drgs/MedTeamCmi/RwDistribution"
            rwDistributionTrendApi="Api/v2/Drgs/MedTeamCmi/RwDistributionTrend"
          />
          <DiseaseStructure
            tableParams={tableParams}
            api="Api/v2/Drgs/MedTeamADrgComposition/BundledADrgComposition"
          />
          {/* <CmiChangeTrend
            tableParams={tableParams}
            api="Api/Drgs/MedTeamCmi/CmiTrend"
          /> */}
        </Row>
      ),
    },
    {
      key: 'medTeam_doctorAnalysis',
      label: '医生综合分析',
      children: (
        <Col span={24}>
          <BCGMatrixAndTable
            title="医生效率"
            tableTitle="医生CMI"
            type="medTeam"
            emitter={EventConstant.DOCTOR_TABLE_ROW_CLICK}
            category="DoctorName"
            axisOpts={DoctorAxisOpts}
            defaultAxisOpt={DefaultOpts}
            args={{
              api: 'Api/v2/Drgs/DoctorCmi/CmiByDoctor',
              extraApiArgs: {
                DoctorCodes: [],
                DoctorType: (
                  selectedDoctorValue as string
                )?.toLocaleUpperCase(),
              },
              columns: [],
            }}
            detailAction={(record) => {
              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                title: record?.DoctorName,
                args: {
                  ...tableParams,
                  DoctorCodes: [record?.DoctorCode],
                  DoctorType: (
                    record?.DoctorType as string
                  )?.toLocaleUpperCase(),
                },
                detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

                dictData: dictData, // 传入
              });
            }}
          />
        </Col>
      ),
    },
  ];
  return (
    <div>
      <Tabs
        size="small"
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
        tabBarExtraContent={{
          right: (
            <>
              {activeKey === 'medTeam_doctorAnalysis' && (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <label>医生类型：</label>
                  <Select
                    options={dictData?.['DoctorType']}
                    style={{ width: '200px' }}
                    fieldNames={{ value: 'Code', label: 'Name' }}
                    placeholder="请选择"
                    allowClear={false}
                    value={selectedDoctorValue}
                    onChange={(value) => {
                      setSelectedDoctorValue(value);
                    }}
                  />
                </div>
              )}
            </>
          ),
        }}
      />
      <DetailTableModal
        dictData={dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'drg',
          });
        }}
      />
      <DrawerCardInfo
        type={'DrgAndHqms'}
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </div>
  );
};

export default MedTeamCmi;
