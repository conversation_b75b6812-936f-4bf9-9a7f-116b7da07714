type SummaryItemProps = {
  className?: string;
  onClick: Function;
  item: {
    label: string;
    key: string;
  };
  count?: number;
};

const SummaryItem = (props: SummaryItemProps) => {
  const { count, onClick, item, className } = props || {};
  return (
    <div
      className={`summary-item-container ${className}`}
      onClick={() => {
        onClick && onClick();
      }}
      key={item?.key}
    >
      <span className={'label'}>{item?.label}：</span>
      <span className={'value'}>{count ?? '-'}</span>
    </div>
  );
};

export default SummaryItem;
