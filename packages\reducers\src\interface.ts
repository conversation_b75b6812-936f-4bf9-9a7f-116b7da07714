import { PaginationProps, TableColumnsType } from 'antd';
import { PaginationConfig } from 'antd/lib/pagination';
import { SorterResult, TablePaginationConfig } from 'antd/lib/table/interface';
import { ReactNode } from 'react';

interface IColumnItem {
  data: string;
  title: string;
  name: string;
  visible: boolean;
  orderable: boolean;
  orderMode?: string;
  orderPriority?: number;
  aggregable?: false;
  responsivePriority?: number;
  className?: string;
  dataType?: string;
  scale?: string | number | boolean;
  dictionaryModule?: string;
  groupName?: string;
  shortTitle?: string;
  shortTitleDescription?: string;
  sorter: boolean;
  dataIndex: string;
  defaultRenderByType?: boolean;
}

// table data type
export interface ITableState<DataType> extends IEditableState<DataType> {
  columns: (TableColumnsType & IColumnItem)[];
  data: DataType[];
  multiData?: { [key: string]: DataType[] };
  title?: string | ReactNode;
  clkItem?: DataType;
  summary?: any;
  total?: number;
  // table sorter
  sorter?: SorterResult<DataType>;
  // table rowSelection
  selectedKeys?: string[] | React.Key[];
  selectedRecords?: DataType[];
  // table expandable
  expandedRecords?: DataType[];
  expandedTypes?: string[];
  // backend pagination
  backPagination?: TablePaginationConfig;
  [key: string]: any;
}

// table editable part
export interface IEditableState<DataType> {
  value?: DataType[];
  editableKeys?: React.Key[] | string[];
  key?: React.Key | string | number;
}

// useReducer里面的reducer
export type IReducer<T = any> = {
  type: string;
  payload?: T;
};

// modal data type
export type IModalState<DataType> = {
  visible?: boolean;
  record?: DataType;
  actionType?:
    | 'Create'
    | 'Edit'
    | 'Check'
    | 'Remove'
    | 'Noti'
    | 'Batch'
    | 'PreAction'
    | 'Test'
    | 'Download';
  state?: any;
  dataSource?: any[];
  [key: string]: any;
};

//cmi stat data type
export interface IStatState<DataType> extends ITableState<DataType> {
  compareStatRes?: {
    Loms?: DataType[];
    Loys?: DataType[];
    Moms?: DataType[];
    Yoys?: DataType[];
  };
}
